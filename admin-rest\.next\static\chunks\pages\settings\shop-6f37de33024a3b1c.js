(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3778,2007,2036],{76521:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/shop",function(){return r(57210)}])},92072:function(e,t,r){"use strict";var o=r(85893),l=r(93967),n=r.n(l),i=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,o.jsx)("div",{className:(0,i.m6)(n()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},62003:function(e,t,r){"use strict";r.d(t,{s:function(){return ChevronLeft}});var o=r(85893);let ChevronLeft=e=>(0,o.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},86779:function(e,t,r){"use strict";r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var o=r(85893);let InfoIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,o.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},12032:function(e,t,r){"use strict";r.d(t,{N:function(){return SaveIcon}});var o=r(85893);let SaveIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,o.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},25774:function(e,t,r){"use strict";r.r(t),r.d(t,{FacebookIcon:function(){return FacebookIcon},InstagramIcon:function(){return InstagramIcon},TwitterIcon:function(){return TwitterIcon},YouTubeIcon:function(){return YouTubeIcon}});var o=r(85893);let FacebookIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 12",...e,children:(0,o.jsx)("path",{"data-name":"_ionicons_svg_logo-facebook (6)",d:"M11.338 0H.662A.663.663 0 000 .663v10.674a.663.663 0 00.662.662H6V7.25H4.566V5.5H6V4.206a2.28 2.28 0 012.459-2.394c.662 0 1.375.05 1.541.072V3.5H8.9c-.753 0-.9.356-.9.881V5.5h1.794L9.56 7.25H8V12h3.338a.663.663 0 00.662-.663V.662A.663.663 0 0011.338 0z",fill:"currentColor"})}),InstagramIcon=e=>(0,o.jsxs)("svg",{"data-name":"Group 96",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 12",...e,children:[(0,o.jsx)("path",{"data-name":"Path 1",d:"M8.5 1A2.507 2.507 0 0111 3.5v5A2.507 2.507 0 018.5 11h-5A2.507 2.507 0 011 8.5v-5A2.507 2.507 0 013.5 1h5m0-1h-5A3.51 3.51 0 000 3.5v5A3.51 3.51 0 003.5 12h5A3.51 3.51 0 0012 8.5v-5A3.51 3.51 0 008.5 0z",fill:"currentColor"}),(0,o.jsx)("path",{"data-name":"Path 2",d:"M9.25 3.5a.75.75 0 11.75-.75.748.748 0 01-.75.75zM6 4a2 2 0 11-2 2 2 2 0 012-2m0-1a3 3 0 103 3 3 3 0 00-3-3z",fill:"currentColor"})]}),TwitterIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14.747 12",...e,children:(0,o.jsx)("path",{"data-name":"_ionicons_svg_logo-twitter (5)",d:"M14.747 1.422a6.117 6.117 0 01-1.737.478A3.036 3.036 0 0014.341.225a6.012 6.012 0 01-1.922.734 3.025 3.025 0 00-5.234 2.069 2.962 2.962 0 00.078.691A8.574 8.574 0 011.026.553a3.032 3.032 0 00.941 4.044 2.955 2.955 0 01-1.375-.378v.037A3.028 3.028 0 003.02 7.225a3.046 3.046 0 01-.8.106 2.854 2.854 0 01-.569-.056 3.03 3.03 0 002.828 2.1 6.066 6.066 0 01-3.759 1.3 6.135 6.135 0 01-.722-.044A8.457 8.457 0 004.631 12a8.557 8.557 0 008.616-8.619c0-.131 0-.262-.009-.391a6.159 6.159 0 001.509-1.568z",fill:"currentColor"})}),YouTubeIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15.997 12",...e,children:(0,o.jsx)("path",{d:"M15.893 2.65A2.429 2.429 0 0013.581.113c-1.731-.081-3.5-.112-5.3-.112h-.563c-1.8 0-3.569.031-5.3.112A2.434 2.434 0 00.106 2.656C.028 3.768-.006 4.881-.003 5.993s.031 2.225.106 3.34a2.437 2.437 0 002.309 2.547c1.822.085 3.688.12 5.584.12s3.759-.031 5.581-.119a2.438 2.438 0 002.312-2.547c.075-1.116.109-2.228.106-3.344s-.027-2.225-.102-3.34zM6.468 9.059v-6.14l4.531 3.069z",fill:"currentColor"})})},97670:function(e,t,r){"use strict";r.r(t);var o=r(85893),l=r(78985),n=r(79362),i=r(8144),s=r(74673),a=r(99494),c=r(5233),d=r(1631),m=r(11163),f=r(48583),p=r(93967),b=r.n(p),x=r(30824),v=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,c.$G)(),[l,i]=(0,f.KO)(n.Hf),{childMenu:s}=t,{width:a}=(0,v.Z)();return(0,o.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:i,icon:s,childMenu:c}=e;return(0,o.jsx)(d.Z,{href:t,label:r(i),icon:s,childMenu:c,miniSidebar:l&&a>=n.h2},i)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[r,l]=(0,f.KO)(n.Hf),i=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(i),{width:d}=(0,v.Z)();return(0,o.jsx)(o.Fragment,{children:null==s?void 0:s.map((e,l)=>{var s;return(0,o.jsxs)("div",{className:b()("flex flex-col px-5",r&&d>=n.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,o.jsx)("div",{className:b()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&d>=n.h2?"hidden":""),children:t(null===(s=i[e])||void 0===s?void 0:s.label)}),(0,o.jsx)(SidebarItemMap,{menuItems:i[e]})]},l)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,m.useRouter)(),[a,c]=(0,f.KO)(n.Hf),[d]=(0,f.KO)(n.GH),[p]=(0,f.KO)(n.W4),{width:h}=(0,v.Z)();return(0,o.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,o.jsx)(l.Z,{}),(0,o.jsx)(s.Z,{children:(0,o.jsx)(SideBarGroup,{})}),(0,o.jsxs)("div",{className:"flex flex-1",children:[(0,o.jsx)("aside",{className:b()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=n.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-20",a&&h>=n.h2?"lg:w-24":"lg:w-76"),children:(0,o.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,o.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,o.jsx)(SideBarGroup,{})})})}),(0,o.jsxs)("main",{className:b()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=n.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&h>=n.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,o.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,o.jsx)(i.Z,{})]})]})]})}},59122:function(e,t,r){"use strict";r.d(t,{Z:function(){return SettingsPageHeader}});var o=r(85893),l=r(11163),n=r(99494),i=r(8152),s=r(93967),a=r.n(s),c=r(5233),d=r(67294),m=r(86998),f=r(62003);function SettingsPageHeader(e){var t,r,s,p;let{pageTitle:b}=e,{t:x}=(0,c.$G)(),v=(0,l.useRouter)(),{sliderEl:h,sliderPrevBtn:g,sliderNextBtn:w,scrollToTheRight:y,scrollToTheLeft:j}=function(){let e=(0,d.useRef)(null),t=(0,d.useRef)(null),r=(0,d.useRef)(null);return(0,d.useEffect)(()=>{let o=e.current,l=t.current,n=r.current,i=o.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=o.offsetWidth;o.scrollWidth>e?(null==n||n.classList.remove("opacity-0","invisible"),i&&(null==o||o.classList.add("!-mb-[43px]"))):(null==n||n.classList.add("opacity-0","invisible"),i&&(null==o||o.classList.remove("!-mb-[43px]"))),null==l||l.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==o?void 0:o.scrollLeft,t=null==o?void 0:o.offsetWidth;(null==o?void 0:o.scrollWidth)-e==t?(null==n||n.classList.add("opacity-0","invisible"),null==l||l.classList.remove("opacity-0","invisible")):null==n||n.classList.remove("opacity-0","invisible"),0===e?(null==l||l.classList.add("opacity-0","invisible"),null==n||n.classList.remove("opacity-0","invisible")):null==l||l.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),o.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),o.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:r,scrollToTheRight:function(){let r=e.current.offsetWidth;e.current.scrollLeft+=r/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,r.current.classList.remove("opacity-0","invisible")}}}(),N=null===n.siteSettings||void 0===n.siteSettings?void 0:null===(p=n.siteSettings.sidebarLinks)||void 0===p?void 0:null===(s=p.admin)||void 0===s?void 0:null===(r=s.settings)||void 0===r?void 0:null===(t=r.childMenu[0])||void 0===t?void 0:t.childMenu,S=v.asPath.split("#")[0].split("?")[0];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,o.jsx)("h1",{className:"text-lg font-semibold text-heading",children:x(b)})}),(0,o.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,o.jsx)("button",{title:"Prev",ref:g,onClick:()=>j(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,o.jsx)(f.s,{className:"h-[18px] w-[18px]"})}),(0,o.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,o.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:h,children:null==N?void 0:N.map((e,t)=>(0,o.jsx)(i.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:a()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",S===e.href?"text-heading before:w-full":null),children:x(e.label)},t))})}),(0,o.jsx)("button",{title:"Next",ref:w,onClick:()=>y(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,o.jsx)(m._,{className:"h-[18px] w-[18px]"})})]})]})}},80602:function(e,t,r){"use strict";var o=r(85893);t.Z=e=>{let{title:t,details:r,className:l,...n}=e;return(0,o.jsxs)("div",{className:l,...n,children:[t&&(0,o.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),r&&(0,o.jsx)("p",{className:"text-sm text-body",children:r})]})}},66271:function(e,t,r){"use strict";var o=r(85893),l=r(93967),n=r.n(l),i=r(98388);t.Z=e=>{let{message:t,className:r}=e;return(0,o.jsx)("p",{className:(0,i.m6)(n()("my-2 text-xs text-start text-red-500",r)),children:t})}},33e3:function(e,t,r){"use strict";var o=r(85893),l=r(71611),n=r(93967),i=r.n(n),s=r(67294),a=r(98388);let c={small:"text-sm h-10",medium:"h-12",big:"h-14"},d=s.forwardRef((e,t)=>{let{className:r,label:n,note:s,name:d,error:m,children:f,variant:p="normal",dimension:b="medium",shadow:x=!1,type:v="text",inputClassName:h,disabled:g,showLabel:w=!0,required:y,toolTipText:j,labelClassName:N,...S}=e,C=i()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===p,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===p,"border border-border-base focus:border-accent":"outline"===p},{"focus:shadow":x},c[b],h),T="number"===v&&g?"number-disable":"";return(0,o.jsxs)("div",{className:(0,a.m6)(r),children:[w||n?(0,o.jsx)(l.Z,{htmlFor:d,toolTipText:j,label:n,required:y,className:N}):"",(0,o.jsx)("input",{id:d,name:d,type:v,ref:t,className:(0,a.m6)(i()(g?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(T," select-none"):"",C)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:g,"aria-invalid":m?"true":"false",...S}),s&&(0,o.jsx)("p",{className:"mt-2 text-xs text-body",children:s}),m&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:m})]})});d.displayName="Input",t.Z=d},23091:function(e,t,r){"use strict";var o=r(85893),l=r(93967),n=r.n(l),i=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,o.jsx)("label",{className:(0,i.m6)(n()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},28454:function(e,t,r){"use strict";var o=r(85893),l=r(79828),n=r(71611),i=r(87536);t.Z=e=>{let{control:t,options:r,name:s,rules:a,getOptionLabel:c,getOptionValue:d,disabled:m,isMulti:f,isClearable:p,isLoading:b,placeholder:x,label:v,required:h,toolTipText:g,error:w,...y}=e;return(0,o.jsxs)(o.Fragment,{children:[v?(0,o.jsx)(n.Z,{htmlFor:s,toolTipText:g,label:v,required:h}):"",(0,o.jsx)(i.Qr,{control:t,name:s,rules:a,...y,render:e=>{let{field:t}=e;return(0,o.jsx)(l.Z,{...t,getOptionLabel:c,getOptionValue:d,placeholder:x,isMulti:f,isClearable:p,isLoading:b,options:r,isDisabled:m})}}),w&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:w})]})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return l},X:function(){return o}});let o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},l={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var o=r(85893),l=r(76518),n=r(67294),i=r(23157),s=r(87077);let a=n.forwardRef((e,t)=>{let{isRTL:r}=(0,l.S)();return(0,o.jsx)(i.ZP,{ref:t,styles:s.X,isRtl:r,...e})});a.displayName="Select",t.Z=a},22220:function(e,t,r){"use strict";var o=r(85893),l=r(93967),n=r.n(l),i=r(98388);t.Z=e=>{let{children:t,className:r,...l}=e;return(0,o.jsx)("div",{className:(0,i.m6)(n()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...l,children:t})}},77180:function(e,t,r){"use strict";var o=r(85893),l=r(66271),n=r(71611),i=r(77768),s=r(93967),a=r.n(s),c=r(5233),d=r(87536),m=r(98388);t.Z=e=>{let{control:t,label:r,name:s,error:f,disabled:p,required:b,toolTipText:x,className:v,labelClassName:h,...g}=e,{t:w}=(0,c.$G)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:(0,m.m6)(a()("flex items-center gap-x-4",v)),children:[(0,o.jsx)(d.Qr,{name:s,control:t,...g,render:e=>{let{field:{onChange:t,value:l}}=e;return(0,o.jsxs)(i.r,{checked:l,onChange:t,disabled:p,className:"".concat(l?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(p?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,o.jsxs)("span",{className:"sr-only",children:["Enable ",r]}),(0,o.jsx)("span",{className:"".concat(l?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),r?(0,o.jsx)(n.Z,{htmlFor:s,className:a()("mb-0",h),toolTipText:x,label:r,required:b}):""]}),f?(0,o.jsx)(l.Z,{message:f}):""]})}},95414:function(e,t,r){"use strict";var o=r(85893),l=r(71611),n=r(93967),i=r.n(n),s=r(67294),a=r(98388);let c=s.forwardRef((e,t)=>{let{className:r,label:n,toolTipText:s,name:c,error:d,variant:m="normal",shadow:f=!1,inputClassName:p,disabled:b,required:x,...v}=e,h=i()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===m,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===m,"border border-border-base focus:border-accent":"outline"===m},{"focus:shadow":f},p);return(0,o.jsxs)("div",{className:(0,a.m6)(i()(r)),children:[n&&(0,o.jsx)(l.Z,{htmlFor:c,toolTipText:s,label:n,required:x}),(0,o.jsx)("textarea",{id:c,name:c,className:(0,a.m6)(i()(h,b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:b,...v}),d&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:d})]})});c.displayName="TextArea",t.Z=c},71611:function(e,t,r){"use strict";var o=r(85893),l=r(86779),n=r(71943),i=r(23091),s=r(98388);t.Z=e=>{let{className:t,required:r,label:a,toolTipText:c,htmlFor:d}=e;return(0,o.jsxs)(i.Z,{className:(0,s.m6)(t),htmlFor:d,children:[a,r?(0,o.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",c?(0,o.jsx)(n.u,{content:c,children:(0,o.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,o.jsx)(l.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){"use strict";r.d(t,{u:function(){return Tooltip}});var o=r(85893),l=r(67294),n=r(93075),i=r(82364),s=r(24750),a=r(93967),c=r.n(a),d=r(67421),m=r(98388);let f={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:a=8,animation:b="zoomIn",placement:x="top",size:v="md",rounded:h="DEFAULT",shadow:g="md",color:w="default",className:y,arrowClassName:j,showArrow:N=!0}=e,[S,C]=(0,l.useState)(!1),T=(0,l.useRef)(null),{t:k}=(0,d.$G)(),{x:E,y:D,refs:F,strategy:Z,context:L}=(0,n.YF)({placement:x,open:S,onOpenChange:C,middleware:[(0,i.x7)({element:T}),(0,i.cv)(a),(0,i.RR)(),(0,i.uY)({padding:8})],whileElementsMounted:s.Me}),{getReferenceProps:R,getFloatingProps:_}=(0,n.NI)([(0,n.XI)(L),(0,n.KK)(L),(0,n.qs)(L,{role:"tooltip"}),(0,n.bQ)(L)]),{isMounted:P,styles:O}=(0,n.Y_)(L,{duration:{open:150,close:150},...p[b]});return(0,o.jsxs)(o.Fragment,{children:[(0,l.cloneElement)(t,R({ref:F.setReference,...t.props})),(P||S)&&(0,o.jsx)(n.ll,{children:(0,o.jsxs)("div",{role:"tooltip",ref:F.setFloating,className:(0,m.m6)(c()(f.base,f.size[v],f.rounded[h],f.variant.solid.base,f.variant.solid.color[w],f.shadow[g],y)),style:{position:Z,top:null!=D?D:0,left:null!=E?E:0,...O},..._(),children:[k("".concat(r)),N&&(0,o.jsx)(n.Y$,{ref:T,context:L,className:c()(f.arrow.color[w],j),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},57210:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return _},default:function(){return ShopSettings}});var o=r(85893),l=r(97670),n=r(59122),i=r(92072),s=r(12032),a=r(25774),c=r(16310);let d=c.Ry().shape({deliveryTime:c.IX().min(1,"form:error-add-at-least-one-delivery-time").of(c.Ry().shape({title:c.Z_().required("form:error-title-required")}))});var m=r(21587),f=r(60802),p=r(80602),b=r(33e3),x=r(23091),v=r(22220),h=r(77180),g=r(95414),w=r(90573),y=r(99494),j=r(3986),N=r(47559),S=r(47533),C=r(5233),T=r(11163),k=r(67294),E=r(87536),D=r(28454);let F=y.e.map(e=>(e.label=(0,o.jsxs)("div",{className:"flex items-center text-body space-s-4",children:[(0,o.jsx)("span",{className:"flex items-center justify-center w-4 h-4",children:(0,N.q)({iconList:a,iconName:e.value,className:"w-4 h-4"})}),(0,o.jsx)("span",{children:e.label})]}),e));function SettingsForm(e){var t,r,l,n,a;let{settings:c}=e,{t:y}=(0,C.$G)(),N=(0,T.useRouter)(),{locale:Z}=N,[L,R]=(0,k.useState)(!1),{mutate:_,isLoading:P}=(0,w.B)(),{language:O,options:I}=null!=c?c:{},{register:A,handleSubmit:z,control:B,watch:M,reset:V,formState:{errors:G,isDirty:Y}}=(0,E.cI)({shouldUnregister:!0,resolver:(0,S.X)(d),defaultValues:{...I,contactDetails:{...null==I?void 0:I.contactDetails,socials:(null==I?void 0:null===(t=I.contactDetails)||void 0===t?void 0:t.socials)?null==I?void 0:null===(r=I.contactDetails)||void 0===r?void 0:r.socials.map(e=>({icon:null==F?void 0:F.find(t=>(null==t?void 0:t.value)===(null==e?void 0:e.icon)),url:null==e?void 0:e.url})):[]},deliveryTime:(null==I?void 0:I.deliveryTime)?null==I?void 0:I.deliveryTime:[],reviewSystem:(null==I?void 0:I.reviewSystem)?null==I?void 0:I.reviewSystem:"review_single_time"}}),{fields:K,append:q,remove:$}=(0,E.Dq)({control:B,name:"deliveryTime"}),W=M("useGoogleMap");async function onSubmit(e){_({language:Z,options:{...e,...I,deliveryTime:null==e?void 0:e.deliveryTime,maxShopDistance:Number(e.maxShopDistance),useGoogleMap:null==e?void 0:e.useGoogleMap,enableTerms:null==e?void 0:e.enableTerms,enableCoupons:null==e?void 0:e.enableCoupons,isProductReview:null==e?void 0:e.isProductReview,enableEmailForDigitalProduct:null==e?void 0:e.enableEmailForDigitalProduct,enableReviewPopup:null==e?void 0:e.enableReviewPopup,reviewSystem:null==e?void 0:e.reviewSystem}}),V(e,{keepValues:!0})}return(0,j.H)({isDirty:Y}),(0,o.jsxs)("form",{onSubmit:z(onSubmit),children:[(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-gray-300 border-dashed sm:my-8",children:[(0,o.jsx)(p.Z,{title:y("form:text-delivery-schedule"),details:y("form:delivery-schedule-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pr-4 md:w-1/3 md:pr-5"}),(0,o.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)("div",{children:K.map((e,t)=>{var r,l,n;return(0,o.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:col-span-4",children:[(0,o.jsx)(b.Z,{label:y("form:input-delivery-time-title"),toolTipText:y("form:input-tooltip-shop-title"),variant:"outline",...A("deliveryTime.".concat(t,".title")),defaultValue:null==e?void 0:e.title,error:y(null==G?void 0:null===(n=G.deliveryTime)||void 0===n?void 0:null===(l=n[t])||void 0===l?void 0:null===(r=l.title)||void 0===r?void 0:r.message)}),(0,o.jsx)(g.Z,{label:y("form:input-delivery-time-description"),toolTipText:y("form:input-tooltip-shop-description"),variant:"outline",...A("deliveryTime.".concat(t,".description")),defaultValue:e.description})]}),(0,o.jsx)("button",{onClick:()=>{$(t)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none sm:col-span-1 sm:mt-4",children:y("form:button-label-remove")})]})},e.id)})}),(0,o.jsx)(f.Z,{type:"button",onClick:()=>q({title:"",description:""}),className:"w-full sm:w-auto",children:y("form:button-label-add-delivery-time")}),(null==G?void 0:null===(l=G.deliveryTime)||void 0===l?void 0:l.message)?(0,o.jsx)(m.Z,{message:y(null==G?void 0:null===(n=G.deliveryTime)||void 0===n?void 0:n.message),variant:"error",className:"mt-5"}):null]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-gray-300 border-dashed sm:mt-8 sm:mb-3",children:[(0,o.jsx)(p.Z,{title:y("form:shop-settings"),details:y("form:shop-settings-helper-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsx)(h.Z,{name:"isProductReview",control:B,label:y("form:input-label-product-for-review"),toolTipText:y("form:input-tooltip-shop-product-review")})}),(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsx)(h.Z,{name:"useGoogleMap",control:B,label:y("form:input-label-use-google-map-service"),toolTipText:y("form:input-tooltip-shop-enable-google-map")})}),W?(0,o.jsx)(b.Z,{label:y("text-max-search-location-distance"),...A("maxShopDistance"),type:"number",error:y(null===(a=G.maxShopDistance)||void 0===a?void 0:a.message),variant:"outline",className:"my-5"}):"",(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsx)(h.Z,{name:"enableTerms",control:B,label:y("form:input-label-terms-conditions-vendors"),toolTipText:y("form:input-tooltip-shop-enable-terms")})}),(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsx)(h.Z,{name:"enableCoupons",control:B,label:y("form:input-label-coupons-vendors"),toolTipText:y("form:input-tooltip-shop-enable-coupons")})}),(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsx)(h.Z,{name:"enableReviewPopup",control:B,label:y("form:text-enable-review-popup"),toolTipText:y("form:input-tooltip-enable-review-popup")})}),(0,o.jsx)("div",{className:"mb-5 mt-6",children:(0,o.jsx)(D.Z,{name:"reviewSystem",control:B,defaultValue:null==I?void 0:I.reviewSystem,getOptionLabel:e=>e.name,getOptionValue:e=>e.value,options:[{name:y("form:text-conventional-review-system"),value:"review_single_time"},{name:y("form:text-order-basis-review-system"),value:"review_multiple_time"}],label:y("form:text-review-system"),toolTipText:y("form:input-tooltip-review-system")})}),(0,o.jsx)("div",{className:"mt-6 mb-5",children:(0,o.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,o.jsx)(h.Z,{name:"enableEmailForDigitalProduct",control:B}),(0,o.jsx)(x.Z,{className:"!mb-0.5",children:"Send email to purchased customer of any digital product, when that digital product get update."})]})})]})]}),(0,o.jsx)(v.Z,{className:"z-0",children:(0,o.jsxs)(f.Z,{loading:P,disabled:P||!Y,className:"text-sm md:text-base",children:[(0,o.jsx)(s.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),y("form:button-label-save-settings")]})})]})}var Z=r(45957),L=r(55846),R=r(16203),_=!0;function ShopSettings(){let{t:e}=(0,C.$G)(),{locale:t}=(0,T.useRouter)(),{settings:r,loading:l,error:i}=(0,w.n)({language:t});return l?(0,o.jsx)(L.Z,{text:e("common:text-loading")}):i?(0,o.jsx)(Z.Z,{message:i.message}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.Z,{pageTitle:"form:form-title-settings"}),(0,o.jsx)(SettingsForm,{settings:r})]})}ShopSettings.authenticate={permissions:R.M$},ShopSettings.Layout=l.default},3986:function(e,t,r){"use strict";r.d(t,{H:function(){return useConfirmRedirectIfDirty}});var o=r(67294),l=r(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:r="You have unsaved changes - are you sure you wish to leave this page?"}=e,n=(0,l.useRouter)(),i=(0,o.useRef)(t),s=(0,o.useRef)(r);(0,o.useEffect)(()=>{i.current=t},[t]),(0,o.useEffect)(()=>{s.current=r},[r]);let a=(0,o.useCallback)(e=>{if(i.current)return e.preventDefault(),e.returnValue=s.current},[]),c=(0,o.useCallback)(()=>{if(i.current&&!window.confirm(s.current))throw n.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,o.useEffect)(()=>(window.addEventListener("beforeunload",a),n.events.on("routeChangeStart",c),()=>{window.removeEventListener("beforeunload",a),n.events.off("routeChangeStart",c)}),[a,c])}},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return s}});var o=r(65342),l=r(87462),n=r(67294),i=r(76416);r(48711),r(73935),r(73469);var s=(0,n.forwardRef)(function(e,t){var r=(0,o.u)(e);return n.createElement(i.S,(0,l.Z)({ref:t},r))})},97326:function(e,t,r){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,r){"use strict";r.d(t,{Z:function(){return _createClass}});var o=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var l=t[r];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(e,(0,o.Z)(l.key),l)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var o=r(71002),l=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,n=_getPrototypeOf(e);if(t){var i=_getPrototypeOf(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"==(0,o.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,l.Z)(e)}(this,r)}}},60136:function(e,t,r){"use strict";r.d(t,{Z:function(){return _inherits}});var o=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.Z)(e,t)}},1413:function(e,t,r){"use strict";r.d(t,{Z:function(){return _objectSpread2}});var o=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}},95389:function(e,t,r){"use strict";r.d(t,{_:function(){return d},b:function(){return H}});var o=r(67294),l=r(19946),n=r(12351),i=r(16723),s=r(23784),a=r(73781);let c=(0,o.createContext)(null);function H(){let[e,t]=(0,o.useState)([]);return[e.length>0?e.join(" "):void 0,(0,o.useMemo)(()=>function(e){let r=(0,a.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),o=r.indexOf(e);return -1!==o&&r.splice(o,1),r}))),l=(0,o.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props}),[r,e.slot,e.name,e.props]);return o.createElement(c.Provider,{value:l},e.children)},[t])]}let d=Object.assign((0,n.yV)(function(e,t){let r=(0,l.M)(),{id:a=`headlessui-label-${r}`,passive:d=!1,...m}=e,f=function u(){let e=(0,o.useContext)(c);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),p=(0,s.T)(t);(0,i.e)(()=>f.register(a),[a,f.register]);let b={ref:p,...f.props,id:a};return d&&("onClick"in b&&(delete b.htmlFor,delete b.onClick),"onClick"in m&&delete m.onClick),(0,n.sY)({ourProps:b,theirProps:m,slot:f.slot||{},defaultTag:"label",name:f.name||"Label"})}),{})},77768:function(e,t,r){"use strict";r.d(t,{r:function(){return w}});var o=r(67294),l=r(12351),n=r(19946),i=r(61363),s=r(64103),a=r(95389),c=r(39516),d=r(14157),m=r(23784),f=r(46045),p=r(18689),b=r(73781),x=r(31147),v=r(94192);let h=(0,o.createContext)(null);h.displayName="GroupContext";let g=o.Fragment,w=Object.assign((0,l.yV)(function(e,t){let r=(0,n.M)(),{id:a=`headlessui-switch-${r}`,checked:c,defaultChecked:g=!1,onChange:w,name:y,value:j,form:N,...S}=e,C=(0,o.useContext)(h),T=(0,o.useRef)(null),k=(0,m.T)(T,t,null===C?null:C.setSwitch),[E,D]=(0,x.q)(c,w,g),F=(0,b.z)(()=>null==D?void 0:D(!E)),Z=(0,b.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),F()}),L=(0,b.z)(e=>{e.key===i.R.Space?(e.preventDefault(),F()):e.key===i.R.Enter&&(0,p.g)(e.currentTarget)}),R=(0,b.z)(e=>e.preventDefault()),_=(0,o.useMemo)(()=>({checked:E}),[E]),P={id:a,ref:k,role:"switch",type:(0,d.f)(e,T),tabIndex:0,"aria-checked":E,"aria-labelledby":null==C?void 0:C.labelledby,"aria-describedby":null==C?void 0:C.describedby,onClick:Z,onKeyUp:L,onKeyPress:R},O=(0,v.G)();return(0,o.useEffect)(()=>{var e;let t=null==(e=T.current)?void 0:e.closest("form");t&&void 0!==g&&O.addEventListener(t,"reset",()=>{D(g)})},[T,D]),o.createElement(o.Fragment,null,null!=y&&E&&o.createElement(f._,{features:f.A.Hidden,...(0,l.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:N,checked:E,name:y,value:j})}),(0,l.sY)({ourProps:P,theirProps:S,slot:_,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[r,n]=(0,o.useState)(null),[i,s]=(0,a.b)(),[d,m]=(0,c.f)(),f=(0,o.useMemo)(()=>({switch:r,setSwitch:n,labelledby:i,describedby:d}),[r,n,i,d]);return o.createElement(m,{name:"Switch.Description"},o.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(t=f.switch)?void 0:t.id,onClick(e){r&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),r.click(),r.focus({preventScroll:!0}))}}},o.createElement(h.Provider,{value:f},(0,l.sY)({ourProps:{},theirProps:e,defaultTag:g,name:"Switch.Group"}))))},Label:a._,Description:c.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=76521)}),_N_E=e.O()}]);