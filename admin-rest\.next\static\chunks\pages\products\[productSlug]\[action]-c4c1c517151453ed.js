(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9903,2007,2036],{44643:function(e,l,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products/[productSlug]/[action]",function(){return t(69600)}])},97670:function(e,l,t){"use strict";t.r(l);var r=t(85893),i=t(78985),s=t(79362),a=t(8144),n=t(74673),d=t(99494),o=t(5233),u=t(1631),c=t(11163),p=t(48583),x=t(93967),h=t.n(x),f=t(30824),g=t(62964);let SidebarItemMap=e=>{let{menuItems:l}=e,{t}=(0,o.$G)(),[i,a]=(0,p.KO)(s.Hf),{childMenu:n}=l,{width:d}=(0,g.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:l,label:a,icon:n,childMenu:o}=e;return(0,r.jsx)(u.Z,{href:l,label:t(a),icon:n,childMenu:o,miniSidebar:i&&d>=s.h2},a)})})},SideBarGroup=()=>{var e;let{t:l}=(0,o.$G)(),[t,i]=(0,p.KO)(s.Hf),a=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(a),{width:u}=(0,g.Z)();return(0,r.jsx)(r.Fragment,{children:null==n?void 0:n.map((e,i)=>{var n;return(0,r.jsxs)("div",{className:h()("flex flex-col px-5",t&&u>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:h()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",t&&u>=s.h2?"hidden":""),children:l(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},i)})})};l.default=e=>{let{children:l}=e,{locale:t}=(0,c.useRouter)(),[d,o]=(0,p.KO)(s.Hf),[u]=(0,p.KO)(s.GH),[x]=(0,p.KO)(s.W4),{width:m}=(0,g.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===t||"he"===t?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(n.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:h()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",m>=s.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-20",d&&m>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(f.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:h()("relative flex w-full flex-col justify-start transition-[padding] duration-300",m>=s.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&m>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,r.jsx)(a.Z,{})]})]})]})}},69600:function(e,l,t){"use strict";t.r(l),t.d(l,{__N_SSP:function(){return p},default:function(){return UpdateProductPage}});var r=t(85893),i=t(97670),s=t(81696),a=t(45957),n=t(55846),d=t(93242),o=t(11163),u=t(5233),c=t(93345),p=!0;function UpdateProductPage(){let{query:e,locale:l}=(0,o.useRouter)(),{t}=(0,u.$G)(),{product:i,isLoading:p,error:x}=(0,d.FA)({slug:e.productSlug,language:"edit"===e.action.toString()?l:c.Config.defaultLanguage});return p?(0,r.jsx)(n.Z,{text:t("common:text-loading")}):x?(0,r.jsx)(a.Z,{message:null==x?void 0:x.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex items-center gap-5 border-b border-dashed border-border-base py-5 sm:py-8",children:(0,r.jsx)("h4",{className:"text-lg font-semibold text-heading",children:t("form:form-title-edit-product")})}),(0,r.jsx)(s.Z,{initialValues:i})]})}UpdateProductPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,7755,939,7557,3316,9494,5535,8186,1285,1631,5468,6256,1696,9774,2888,179],function(){return e(e.s=44643)}),_N_E=e.O()}]);