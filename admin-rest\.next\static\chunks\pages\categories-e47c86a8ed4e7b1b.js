(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3161],{20829:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories",function(){return a(80439)}])},47504:function(e,t,a){"use strict";a.d(t,{Ei:function(){return useCategoriesQuery},Im:function(){return useCategoryQuery},m7:function(){return useCreateCategoryMutation},l8:function(){return useDeleteCategoryMutation},pi:function(){return useUpdateCategoryMutation}});var s=a(11163),l=a.n(s),n=a(88767),i=a(22920),r=a(5233),o=a(97514),c=a(47869),u=a(28597),d=a(55191),m=a(3737);let g={...(0,d.h)(c.P.CATEGORIES),paginated:e=>{let{type:t,name:a,self:s,...l}=e;return m.eN.get(c.P.CATEGORIES,{searchJoin:"and",self:s,...l,search:m.eN.formatSearchParams({type:t,name:a})})}};var x=a(93345);let useCreateCategoryMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)();return(0,n.useMutation)(g.create,{onSuccess:()=>{l().push(o.Z.category.list,void 0,{locale:x.Config.defaultLanguage}),i.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.CATEGORIES)}})},useDeleteCategoryMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)();return(0,n.useMutation)(g.delete,{onSuccess:()=>{i.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.CATEGORIES)}})},useUpdateCategoryMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,s.useRouter)(),a=(0,n.useQueryClient)();return(0,n.useMutation)(g.update,{onSuccess:async a=>{let s=t.query.shop?"/".concat(t.query.shop).concat(o.Z.category.list):o.Z.category.list;await t.push("".concat(s,"/").concat(null==a?void 0:a.slug,"/edit"),void 0,{locale:x.Config.defaultLanguage}),i.Am.success(e("common:successfully-updated"))},onSettled:()=>{a.invalidateQueries(c.P.CATEGORIES)}})},useCategoryQuery=e=>{let{slug:t,language:a}=e,{data:s,error:l,isLoading:i}=(0,n.useQuery)([c.P.CATEGORIES,{slug:t,language:a}],()=>g.get({slug:t,language:a}));return{category:s,error:l,isLoading:i}},useCategoriesQuery=e=>{var t;let{data:a,error:s,isLoading:l}=(0,n.useQuery)([c.P.CATEGORIES,e],e=>{let{queryKey:t,pageParam:a}=e;return g.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{categories:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(a),error:s,loading:l}}},80439:function(e,t,a){"use strict";a.r(t),a.d(t,{__N_SSG:function(){return I},default:function(){return Categories}});var s=a(85893),l=a(18230),n=a(27899),i=a(47559),r=a(4232),o=a(10265),c=a(25675),u=a.n(c),d=a(5233),m=a(76518),g=a(67294),x=a(78998),h=a(97514),f=a(34927),p=a(77556),y=a(99494),category_list=e=>{let{categories:t,paginatorInfo:a,onPagination:c,onSort:b,onOrder:v}=e,{t:C}=(0,d.$G)(),{alignLeft:w,alignRight:j}=(0,m.S)(),[N,A]=(0,g.useState)({sort:o.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{b(e=>e===o.As.Desc?o.As.Asc:o.As.Desc),v(e),A({sort:N.sort===o.As.Desc?o.As.Asc:o.As.Desc,column:e})}}),S=[{title:C("table:table-item-id"),dataIndex:"id",key:"id",align:w,width:120,render:e=>"#".concat(C("table:table-item-id"),": ").concat(e)},{title:(0,s.jsx)(x.Z,{title:C("table:table-item-title"),ascending:N.sort===o.As.Asc&&"name"===N.column,isActive:"name"===N.column}),className:"cursor-pointer",dataIndex:"name",key:"name",align:w,width:180,onHeaderCell:()=>onHeaderClick("name"),render:(e,t)=>{var a;let{image:l}=t;return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative aspect-square h-10 w-10 shrink-0 overflow-hidden rounded border border-border-200/80 bg-gray-100 me-2.5",children:(0,s.jsx)(u(),{src:null!==(a=null==l?void 0:l.thumbnail)&&void 0!==a?a:y.siteSettings.product.placeholder,alt:e,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw"})}),(0,s.jsx)("span",{className:"truncate font-medium",children:e})]})}},{title:C("table:table-item-details"),dataIndex:"details",key:"details",ellipsis:!0,align:w,width:200},{title:C("table:table-item-icon"),dataIndex:"icon",key:"icon",align:"center",width:120,render:e=>e?(0,s.jsx)("span",{className:"flex items-center justify-center",children:(0,i.q)({iconList:r,iconName:e,className:"w-5 h-5 max-h-full max-w-full"})}):null},{title:(0,s.jsx)(x.Z,{title:C("table:table-item-slug"),ascending:N.sort===o.As.Asc&&"slug"===N.column,isActive:"slug"===N.column}),className:"cursor-pointer",dataIndex:"slug",key:"slug",align:w,width:150,onHeaderCell:()=>onHeaderClick("slug")},{title:C("table:table-item-group"),dataIndex:"type",key:"type",align:"center",width:120,render:e=>(0,s.jsx)("div",{className:"overflow-hidden truncate whitespace-nowrap",title:null==e?void 0:e.name,children:null==e?void 0:e.name})},{title:C("table:table-item-actions"),dataIndex:"slug",key:"actions",align:j,width:120,render:(e,t)=>(0,s.jsx)(f.Z,{slug:e,record:t,deleteModalView:"DELETE_CATEGORY",routes:null===h.Z||void 0===h.Z?void 0:h.Z.category})}];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,s.jsx)(n.i,{columns:S,emptyText:()=>(0,s.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,s.jsx)(p.m,{className:"w-52"}),(0,s.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:C("table:empty-table-data")}),(0,s.jsx)("p",{className:"text-[13px]",children:C("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3},expandable:{expandedRowRender:()=>" ",rowExpandable:e=>{var t;return null===(t=e.children)||void 0===t?void 0:t.length}}})}),!!(null==a?void 0:a.total)&&(0,s.jsx)("div",{className:"flex items-center justify-end",children:(0,s.jsx)(l.Z,{total:a.total,current:a.currentPage,pageSize:a.perPage,onChange:c})})]})},b=a(92072),v=a(97670),C=a(37912),w=a(61616),j=a(45957),N=a(55846),A=a(33656),S=a(16203),E=a(47504),k=a(11163),_=a(93345),Z=a(35484),I=!0;function Categories(){let{locale:e}=(0,k.useRouter)(),[t,a]=(0,g.useState)(""),[l,n]=(0,g.useState)(""),[i,r]=(0,g.useState)(1),{t:c}=(0,d.$G)(),[u,m]=(0,g.useState)("created_at"),[x,f]=(0,g.useState)(o.As.Desc),{categories:p,paginatorInfo:y,loading:v,error:S}=(0,E.Ei)({limit:20,page:i,type:l,name:t,orderBy:u,sortedBy:x,parent:null,language:e});return v?(0,s.jsx)(N.Z,{text:c("common:text-loading")}):S?(0,s.jsx)(j.Z,{message:S.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.Z,{className:"mb-8 flex flex-col",children:(0,s.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(Z.Z,{title:c("form:input-label-categories")})}),(0,s.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-3/4",children:[(0,s.jsx)(C.Z,{onSearch:function(e){let{searchText:t}=e;a(t),r(1)},placeholderText:c("form:input-placeholder-search-name")}),(0,s.jsx)(A.Z,{className:"md:ms-6",onTypeFilter:e=>{n(null==e?void 0:e.slug),r(1)}}),e===_.Config.defaultLanguage&&(0,s.jsxs)(w.Z,{href:"".concat(h.Z.category.create),className:"h-12 w-full md:w-auto md:ms-6",children:[(0,s.jsxs)("span",{className:"block md:hidden xl:block",children:["+ ",c("form:button-label-add-categories")]}),(0,s.jsxs)("span",{className:"hidden md:block xl:hidden",children:["+ ",c("form:button-label-add")]})]})]})]})}),(0,s.jsx)(category_list,{categories:p,paginatorInfo:y,onPagination:function(e){r(e)},onOrder:m,onSort:f})]})}Categories.authenticate={permissions:S.M$},Categories.Layout=v.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1737,2452,9774,2888,179],function(){return e(e.s=20829)}),_N_E=e.O()}]);