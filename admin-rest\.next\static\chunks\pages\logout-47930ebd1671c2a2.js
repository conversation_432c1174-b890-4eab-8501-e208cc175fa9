(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[765,9930],{31527:function(e,t,u){(window.__NEXT_P=window.__NEXT_P||[]).push(["/logout",function(){return u(46206)}])},44498:function(e,t,u){"use strict";u.d(t,{B:function(){return r}});var s=u(47869),n=u(3737);let r={me:()=>n.eN.get(s.P.ME),login:e=>n.eN.post(s.P.TOKEN,e),logout:()=>n.eN.post(s.P.LOGOUT,{}),register:e=>n.eN.post(s.P.REGISTER,e),update:e=>{let{id:t,input:u}=e;return n.eN.put("".concat(s.P.USERS,"/").concat(t),u)},changePassword:e=>n.eN.post(s.P.CHANGE_PASSWORD,e),forgetPassword:e=>n.eN.post(s.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>n.eN.post(s.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>n.eN.post(s.P.RESET_PASSWORD,e),makeAdmin:e=>n.eN.post(s.P.MAKE_ADMIN,e),block:e=>n.eN.post(s.P.BLOCK_USER,e),unblock:e=>n.eN.post(s.P.UNBLOCK_USER,e),addWalletPoints:e=>n.eN.post(s.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>n.eN.post(s.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...u}=e;return n.eN.get(s.P.USERS,{searchJoin:"and",with:"wallet",...u,search:n.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return n.eN.get(s.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return n.eN.get("".concat(s.P.USERS,"/").concat(t))},resendVerificationEmail:()=>n.eN.post(s.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return n.eN.post(s.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...u}=e;return n.eN.get(s.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...u})},fetchCustomers:e=>{let{...t}=e;return n.eN.get(s.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:u,name:r,...o}=e;return n.eN.get(s.P.MY_STAFFS,{searchJoin:"and",shop_id:u,...o,search:n.eN.formatSearchParams({name:r,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:u,...r}=e;return n.eN.get(s.P.ALL_STAFFS,{searchJoin:"and",...r,search:n.eN.formatSearchParams({name:u,is_active:t})})}}},99930:function(e,t,u){"use strict";u.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var s=u(79362),n=u(97514),r=u(31955),o=u(5233),i=u(11163),a=u(88767),l=u(22920),c=u(47869),d=u(44498),S=u(28597),f=u(87066),P=u(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,i.useRouter)();return(0,a.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===n.Z.verifyLicense&&t.replace(n.Z.dashboard),t.pathname===n.Z.verifyEmail&&((0,P.Fu)(!0),t.replace(n.Z.dashboard))},onError:u=>{if(f.Z.isAxiosError(u)){var s,r;if((null===(s=u.response)||void 0===s?void 0:s.status)===417){t.replace(n.Z.verifyLicense);return}if((null===(r=u.response)||void 0===r?void 0:r.status)===409){(0,P.Fu)(!1),t.replace(n.Z.verifyEmail);return}e.clear(),t.replace(n.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,i.useRouter)(),{t}=(0,o.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{r.Z.remove(s.E$),e.replace(n.Z.login),l.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,o.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{l.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};l.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,o.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{l.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,l.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,o.$G)();(0,a.useQueryClient)();let t=(0,i.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{l.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{l.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,o.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{l.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,o.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{l.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,o.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{l.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}},useAdminsQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}},useVendorsQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}},useCustomersQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}},useMyStaffsQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}},useAllStaffsQuery=e=>{var t;let{data:u,isLoading:s,error:n}=(0,a.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(u),loading:s,error:n}}},46206:function(e,t,u){"use strict";u.r(t),u.d(t,{__N_SSG:function(){return a}});var s=u(85893),n=u(67294),r=u(55846),o=u(99930),i=u(5233),a=!0;t.default=function(){let{t:e}=(0,i.$G)(),{mutate:t}=(0,o._y)();return(0,n.useEffect)(()=>{t()},[]),(0,s.jsx)(r.Z,{text:e("common:signing-out-text")})}}},function(e){e.O(0,[9774,2888,179],function(){return e(e.s=31527)}),_N_E=e.O()}]);