"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_layouts_menu_authorized-menu_tsx"],{

/***/ "./src/components/icons/user-outlined.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/user-outlined.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserOutlinedIcon: function() { return /* binding */ UserOutlinedIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst UserOutlinedIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 15.6 19.6\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            id: \"Path_11\",\n            \"data-name\": \"Path 11\",\n            d: \"M16,7a4,4,0,1,1-4-4A4,4,0,0,1,16,7Zm-4,7a7,7,0,0,0-7,7H19a7,7,0,0,0-7-7Z\",\n            transform: \"translate(-4.2 -2.2)\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"1.6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\user-outlined.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\user-outlined.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n_c = UserOutlinedIcon;\nvar _c;\n$RefreshReg$(_c, \"UserOutlinedIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy91c2VyLW91dGxpbmVkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsbUJBQXNELENBQUNDLHNCQUNsRSw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsU0FBUTtRQUFpQixHQUFHSCxLQUFLO2tCQUN2RSw0RUFBQ0k7WUFDQ0MsSUFBRztZQUNIQyxhQUFVO1lBQ1ZDLEdBQUU7WUFDRkMsV0FBVTtZQUNWQyxNQUFLO1lBQ0xDLFFBQU87WUFDUEMsZUFBYztZQUNkQyxnQkFBZTtZQUNmQyxhQUFZOzs7Ozs7Ozs7O2tCQUdoQjtLQWRXZCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9pY29ucy91c2VyLW91dGxpbmVkLnRzeD8xZmI3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBVc2VyT3V0bGluZWRJY29uOiBSZWFjdC5GQzxSZWFjdC5TVkdBdHRyaWJ1dGVzPHt9Pj4gPSAocHJvcHMpID0+IChcclxuICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB2aWV3Qm94PVwiMCAwIDE1LjYgMTkuNlwiIHsuLi5wcm9wc30+XHJcbiAgICA8cGF0aFxyXG4gICAgICBpZD1cIlBhdGhfMTFcIlxyXG4gICAgICBkYXRhLW5hbWU9XCJQYXRoIDExXCJcclxuICAgICAgZD1cIk0xNiw3YTQsNCwwLDEsMS00LTRBNCw0LDAsMCwxLDE2LDdabS00LDdhNyw3LDAsMCwwLTcsN0gxOWE3LDcsMCwwLDAtNy03WlwiXHJcbiAgICAgIHRyYW5zZm9ybT1cInRyYW5zbGF0ZSgtNC4yIC0yLjIpXCJcclxuICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgc3Ryb2tlV2lkdGg9XCIxLjZcIlxyXG4gICAgLz5cclxuICA8L3N2Zz5cclxuKTtcclxuIl0sIm5hbWVzIjpbIlVzZXJPdXRsaW5lZEljb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwidmlld0JveCIsInBhdGgiLCJpZCIsImRhdGEtbmFtZSIsImQiLCJ0cmFuc2Zvcm0iLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/user-outlined.tsx\n"));

/***/ }),

/***/ "./src/components/layouts/menu/authorized-menu.tsx":
/*!*********************************************************!*\
  !*** ./src/components/layouts/menu/authorized-menu.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _components_icons_user_outlined__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/user-outlined */ \"./src/components/icons/user-outlined.tsx\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst AuthorizedMenu = (param)=>{\n    let { minimal } = param;\n    var _me_profile_avatar, _me_profile, _me_wallet;\n    _s();\n    const { mutate: logout } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_9__.useLogout)();\n    const { me } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    function handleClick(path) {\n        router.push(path);\n    }\n    var _me_profile_avatar_thumbnail, _me_wallet_available_points;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n        as: \"div\",\n        className: \"relative inline-block ltr:text-left rtl:text-right\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu.Button, {\n                className: \"flex items-center focus:outline-0\",\n                children: [\n                    minimal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_user_outlined__WEBPACK_IMPORTED_MODULE_8__.UserOutlinedIcon, {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: (_me_profile_avatar_thumbnail = me === null || me === void 0 ? void 0 : (_me_profile = me.profile) === null || _me_profile === void 0 ? void 0 : (_me_profile_avatar = _me_profile.avatar) === null || _me_profile_avatar === void 0 ? void 0 : _me_profile_avatar.thumbnail) !== null && _me_profile_avatar_thumbnail !== void 0 ? _me_profile_avatar_thumbnail : _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__.avatarPlaceholder,\n                        title: \"user name\",\n                        className: \"h-[38px] w-[38px] border-border-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"user-avatar\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu.Items, {\n                    as: \"ul\",\n                    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"absolute mt-5 w-48 rounded bg-white pb-4 shadow-700 focus:outline-none ltr:right-0 ltr:origin-top-right rtl:left-0 rtl:origin-top-left\", {\n                        \"!mt-2\": minimal\n                    }),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu.Item, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex w-full items-center justify-between bg-accent-500 px-6 py-4 text-xs font-semibold capitalize text-light focus:outline-none ltr:text-left rtl:text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t(\"text-points\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (_me_wallet_available_points = me === null || me === void 0 ? void 0 : (_me_wallet = me.wallet) === null || _me_wallet === void 0 ? void 0 : _me_wallet.available_points) !== null && _me_wallet_available_points !== void 0 ? _me_wallet_available_points : 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        _config_site__WEBPACK_IMPORTED_MODULE_2__.siteSettings.authorizedLinks.map((param)=>/*#__PURE__*/ {\n                            let { href, label } = param;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu.Item, {\n                                children: (param)=>/*#__PURE__*/ {\n                                    let { active } = param;\n                                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleClick(href),\n                                            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"block w-full py-2.5 px-6 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent focus:outline-0 ltr:text-left rtl:text-right\", active ? \"text-accent\" : \"text-heading\"),\n                                            children: t(label)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, undefined);\n                                }\n                            }, \"\".concat(href).concat(label), false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_10__.Menu.Item, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>logout(),\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"block w-full py-2.5 px-6 text-sm font-semibold capitalize text-heading transition duration-200 hover:text-accent focus:outline-0 ltr:text-left rtl:text-right\"),\n                                    children: t(\"auth-menu-logout\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\authorized-menu.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthorizedMenu, \"IPDM+Iwm1UNr9Gzy558I/z/Dn0c=\", false, function() {\n    return [\n        _framework_user__WEBPACK_IMPORTED_MODULE_9__.useLogout,\n        _framework_user__WEBPACK_IMPORTED_MODULE_9__.useUser,\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = AuthorizedMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AuthorizedMenu);\nvar _c;\n$RefreshReg$(_c, \"AuthorizedMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layouts/menu/authorized-menu.tsx\n"));

/***/ }),

/***/ "./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n\n\n\nconst Avatar = (param)=>{\n    let { src, className, title, ...rest } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"relative cursor-pointer overflow-hidden rounded-full border border-border-100\", className),\n        ...rest,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_2__.Image, {\n            alt: title,\n            src: src,\n            fill: true,\n            sizes: \"(max-width: 768px) 100vw\",\n            priority: true\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Avatar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Avatar);\nvar _c;\n$RefreshReg$(_c, \"Avatar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9hdmF0YXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEI7QUFDa0I7QUFTOUMsTUFBTUUsU0FBZ0M7UUFBQyxFQUFFQyxHQUFHLEVBQUVDLFNBQVMsRUFBRUMsS0FBSyxFQUFFLEdBQUdDLE1BQU07SUFDdkUscUJBQ0UsOERBQUNDO1FBQ0NILFdBQVdKLGlEQUFFQSxDQUNYLGlGQUNBSTtRQUVELEdBQUdFLElBQUk7a0JBRVIsNEVBQUNMLHVEQUFLQTtZQUNKTyxLQUFLSDtZQUNMRixLQUFLQTtZQUNMTSxJQUFJO1lBQ0pDLE9BQU07WUFDTkMsVUFBVTs7Ozs7Ozs7Ozs7QUFJbEI7S0FsQk1UO0FBb0JOLCtEQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3VpL2F2YXRhci50c3g/MWQwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY24gZnJvbSAnY2xhc3NuYW1lcyc7XHJcbmltcG9ydCB7IEltYWdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2ltYWdlJztcclxuXHJcbnR5cGUgQXZhdGFyUHJvcHMgPSB7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIHNyYzogc3RyaW5nO1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgW2tleTogc3RyaW5nXTogdW5rbm93bjtcclxufTtcclxuXHJcbmNvbnN0IEF2YXRhcjogUmVhY3QuRkM8QXZhdGFyUHJvcHM+ID0gKHsgc3JjLCBjbGFzc05hbWUsIHRpdGxlLCAuLi5yZXN0IH0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICdyZWxhdGl2ZSBjdXJzb3ItcG9pbnRlciBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItYm9yZGVyLTEwMCcsXHJcbiAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICl9XHJcbiAgICAgIHsuLi5yZXN0fVxyXG4gICAgPlxyXG4gICAgICA8SW1hZ2VcclxuICAgICAgICBhbHQ9e3RpdGxlfVxyXG4gICAgICAgIHNyYz17c3JjfVxyXG4gICAgICAgIGZpbGxcclxuICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDc2OHB4KSAxMDB2d1wiXHJcbiAgICAgICAgcHJpb3JpdHk9e3RydWV9XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQXZhdGFyO1xyXG4iXSwibmFtZXMiOlsiY24iLCJJbWFnZSIsIkF2YXRhciIsInNyYyIsImNsYXNzTmFtZSIsInRpdGxlIiwicmVzdCIsImRpdiIsImFsdCIsImZpbGwiLCJzaXplcyIsInByaW9yaXR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/ui/avatar.tsx\n"));

/***/ })

}]);