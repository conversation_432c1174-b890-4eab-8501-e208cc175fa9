(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3165],{5485:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/tags",function(){return r(53694)}])},97670:function(e,t,r){"use strict";r.r(t);var a=r(85893),l=r(78985),o=r(79362),n=r(8144),s=r(74673),i=r(99494),d=r(5233),c=r(1631),u=r(11163),f=r(48583),g=r(93967),p=r.n(g),m=r(30824),x=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,d.$G)(),[l,n]=(0,f.KO)(o.Hf),{childMenu:s}=t,{width:i}=(0,x.Z)();return(0,a.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:n,icon:s,childMenu:d}=e;return(0,a.jsx)(c.Z,{href:t,label:r(n),icon:s,childMenu:d,miniSidebar:l&&i>=o.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[r,l]=(0,f.KO)(o.Hf),n=null===i.siteSettings||void 0===i.siteSettings?void 0:null===(e=i.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(n),{width:c}=(0,x.Z)();return(0,a.jsx)(a.Fragment,{children:null==s?void 0:s.map((e,l)=>{var s;return(0,a.jsxs)("div",{className:p()("flex flex-col px-5",r&&c>=o.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,a.jsx)("div",{className:p()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&c>=o.h2?"hidden":""),children:t(null===(s=n[e])||void 0===s?void 0:s.label)}),(0,a.jsx)(SidebarItemMap,{menuItems:n[e]})]},l)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,u.useRouter)(),[i,d]=(0,f.KO)(o.Hf),[c]=(0,f.KO)(o.GH),[g]=(0,f.KO)(o.W4),{width:h}=(0,x.Z)();return(0,a.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,a.jsx)(l.Z,{}),(0,a.jsx)(s.Z,{children:(0,a.jsx)(SideBarGroup,{})}),(0,a.jsxs)("div",{className:"flex flex-1",children:[(0,a.jsx)("aside",{className:p()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=o.h2&&(c||g)?"lg:pt-[8.75rem]":"pt-20",i&&h>=o.h2?"lg:w-24":"lg:w-76"),children:(0,a.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,a.jsx)(m.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,a.jsx)(SideBarGroup,{})})})}),(0,a.jsxs)("main",{className:p()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=o.h2&&(c||g)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",i&&h>=o.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,a.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,a.jsx)(n.Z,{})]})]})]})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return l},X:function(){return a}});let a={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},l={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var a=r(85893),l=r(76518),o=r(67294),n=r(23157),s=r(87077);let i=o.forwardRef((e,t)=>{let{isRTL:r}=(0,l.S)();return(0,a.jsx)(n.ZP,{ref:t,styles:s.X,isRtl:r,...e})});i.displayName="Select",t.Z=i},15824:function(e,t,r){"use strict";r.d(t,{be:function(){return useCreateTagMutation},BW:function(){return useDeleteTagMutation},wt:function(){return useTagQuery},Qd:function(){return useTagsQuery},go:function(){return useUpdateTagMutation}});var a=r(11163),l=r.n(a),o=r(88767),n=r(22920),s=r(5233),i=r(28597),d=r(47869),c=r(97514),u=r(55191),f=r(3737);let g={...(0,u.h)(d.P.TAGS),paginated:e=>{let{type:t,name:r,...a}=e;return f.eN.get(d.P.TAGS,{searchJoin:"and",...a,search:f.eN.formatSearchParams({type:t,name:r})})}};var p=r(93345);let useCreateTagMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,s.$G)();return(0,o.useMutation)(g.create,{onSuccess:()=>{l().push(c.Z.tag.list,void 0,{locale:p.Config.defaultLanguage}),n.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.TAGS)}})},useDeleteTagMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,s.$G)();return(0,o.useMutation)(g.delete,{onSuccess:()=>{n.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.TAGS)}})},useUpdateTagMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,a.useRouter)(),r=(0,o.useQueryClient)();return(0,o.useMutation)(g.update,{onSuccess:async r=>{let a=t.query.shop?"/".concat(t.query.shop).concat(c.Z.tag.list):c.Z.tag.list;await t.push("".concat(a,"/").concat(null==r?void 0:r.slug,"/edit"),void 0,{locale:p.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(d.P.TAGS)}})},useTagQuery=e=>{let{slug:t,language:r}=e,{data:a,error:l,isLoading:n}=(0,o.useQuery)([d.P.TYPES,{slug:t,language:r}],()=>g.get({slug:t,language:r}));return{tag:a,error:l,loading:n}},useTagsQuery=e=>{var t;let{data:r,error:a,isLoading:l}=(0,o.useQuery)([d.P.TAGS,e],e=>{let{queryKey:t,pageParam:r}=e;return g.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0});return{tags:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:a,loading:l}}},16720:function(e,t,r){"use strict";r.d(t,{jT:function(){return useCreateTypeMutation},e7:function(){return useDeleteTypeMutation},F2:function(){return useTypeQuery},qs:function(){return useTypesQuery},oy:function(){return useUpdateTypeMutation}});var a=r(11163),l=r.n(a),o=r(88767),n=r(22920),s=r(5233),i=r(97514),d=r(47869),c=r(55191),u=r(3737);let f={...(0,c.h)(d.P.TYPES),all:e=>{let{name:t,...r}=e;return u.eN.get(d.P.TYPES,{searchJoin:"and",...r,search:u.eN.formatSearchParams({name:t})})}};var g=r(93345);let useCreateTypeMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(f.create,{onSuccess:()=>{l().push(i.Z.type.list,void 0,{locale:g.Config.defaultLanguage}),n.Am.success(e("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(d.P.TYPES)}})},useDeleteTypeMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,s.$G)();return(0,o.useMutation)(f.delete,{onSuccess:()=>{n.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.TYPES)}})},useUpdateTypeMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,a.useRouter)(),r=(0,o.useQueryClient)();return(0,o.useMutation)(f.update,{onSuccess:async r=>{let a=t.query.shop?"/".concat(t.query.shop).concat(i.Z.type.list):i.Z.type.list;await t.push("".concat(a,"/").concat(null==r?void 0:r.slug,"/edit"),void 0,{locale:g.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(d.P.TYPES)}})},useTypeQuery=e=>{let{slug:t,language:r}=e;return(0,o.useQuery)([d.P.TYPES,{slug:t,language:r}],()=>f.get({slug:t,language:r}))},useTypesQuery=e=>{let{data:t,isLoading:r,error:a}=(0,o.useQuery)([d.P.TYPES,e],e=>{let{queryKey:t,pageParam:r}=e;return f.all(Object.assign({},t[1],r))},{keepPreviousData:!0});return{types:null!=t?t:[],loading:r,error:a}}},53694:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return C},default:function(){return Tags}});var a=r(85893),l=r(92072),o=r(97670),n=r(37912),s=r(61616),i=r(67294),d=r(45957),c=r(55846),u=r(5233),f=r(18230),g=r(27899),p=r(76518),m=r(10265),x=r(78998),h=r(77556),b=r(97514),v=r(34927),tag_list=e=>{let{tags:t,onPagination:r,onSort:l,onOrder:o,paginatorInfo:n}=e,{t:s}=(0,u.$G)(),{alignLeft:d,alignRight:c}=(0,p.S)(),[y,S]=(0,i.useState)({sort:m.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{l(e=>e===m.As.Desc?m.As.Asc:m.As.Desc),o(e),S({sort:y.sort===m.As.Desc?m.As.Asc:m.As.Desc,column:e})}}),j=[{title:(0,a.jsx)(x.Z,{title:s("table:table-item-id"),ascending:y.sort===m.As.Asc&&"id"===y.column,isActive:"id"===y.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:d,width:180,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(s("table:table-item-id"),": ").concat(e)},{title:(0,a.jsx)(x.Z,{title:s("table:table-item-title"),ascending:y.sort===m.As.Asc&&"name"===y.column,isActive:"name"===y.column}),className:"cursor-pointer",dataIndex:"name",key:"name",align:d,width:220,onHeaderCell:()=>onHeaderClick("name")},{title:s("table:table-item-slug"),dataIndex:"slug",key:"slug",align:"center",width:250,ellipsis:!0},{title:s("table:table-item-group"),dataIndex:"type",key:"type",align:d,width:250,render:e=>(0,a.jsx)("div",{className:"overflow-hidden truncate whitespace-nowrap",title:null==e?void 0:e.name,children:null==e?void 0:e.name})},{title:s("table:table-item-actions"),dataIndex:"slug",key:"actions",align:c,width:120,render:(e,t)=>(0,a.jsx)(v.Z,{slug:e,record:t,deleteModalView:"DELETE_TAG",routes:null===b.Z||void 0===b.Z?void 0:b.Z.tag})}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,a.jsx)(g.i,{columns:j,emptyText:()=>(0,a.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,a.jsx)(h.m,{className:"w-52"}),(0,a.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:s("table:empty-table-data")}),(0,a.jsx)("p",{className:"text-[13px]",children:s("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==n?void 0:n.total)&&(0,a.jsx)("div",{className:"flex items-center justify-end",children:(0,a.jsx)(f.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:r})})]})},y=r(16203),S=r(15824),j=r(11163),w=r(93345),T=r(35484),A=r(33656),C=!0;function Tags(){let{t:e}=(0,u.$G)(),{locale:t}=(0,j.useRouter)(),[r,o]=(0,i.useState)(""),[f,g]=(0,i.useState)(""),[p,x]=(0,i.useState)(1),[h,v]=(0,i.useState)("created_at"),[y,C]=(0,i.useState)(m.As.Desc),{tags:D,loading:F,paginatorInfo:E,error:N}=(0,S.Qd)({limit:10,orderBy:h,sortedBy:y,name:r,page:p,language:t,type:f});return F?(0,a.jsx)(c.Z,{text:e("common:text-loading")}):N?(0,a.jsx)(d.Z,{message:N.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,a.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,a.jsx)(T.Z,{title:e("common:sidebar-nav-item-tags")})}),(0,a.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:w-1/2 md:flex-row md:space-y-0",children:[(0,a.jsx)(n.Z,{onSearch:function(e){let{searchText:t}=e;o(t)},placeholderText:e("form:input-placeholder-search-name")}),(0,a.jsx)(A.Z,{className:"md:ms-6",onTypeFilter:e=>{g(null==e?void 0:e.slug),x(1)}}),t===w.Config.defaultLanguage&&(0,a.jsxs)(s.Z,{href:"".concat(b.Z.tag.create),className:"h-12 w-full md:w-auto md:ms-6",children:[(0,a.jsxs)("span",{className:"block md:hidden xl:block",children:["+ ",e("form:button-label-add-tag")]}),(0,a.jsxs)("span",{className:"hidden md:block xl:hidden",children:["+ ",e("form:button-label-add")]})]})]})]}),(0,a.jsx)(tag_list,{tags:D,onPagination:function(e){x(e)},onOrder:v,onSort:C,paginatorInfo:E})]})}Tags.authenticate={permissions:y.M$},Tags.Layout=o.default},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return s}});var a=r(65342),l=r(87462),o=r(67294),n=r(76416);r(48711),r(73935),r(73469);var s=(0,o.forwardRef)(function(e,t){var r=(0,a.u)(e);return o.createElement(n.S,(0,l.Z)({ref:t},r))})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,2452,9774,2888,179],function(){return e(e.s=5485)}),_N_E=e.O()}]);