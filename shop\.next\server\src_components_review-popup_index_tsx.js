"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_review-popup_index_tsx";
exports.ids = ["src_components_review-popup_index_tsx"];
exports.modules = {

/***/ "./src/components/icons/hand-sign.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/hand-sign.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandSign: () => (/* binding */ HandSign)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HandSign = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        stroke: \"currentColor\",\n        fill: \"none\",\n        strokeWidth: 2,\n        viewBox: \"0 0 24 24\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        height: \"1em\",\n        width: \"1em\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M0 0h24v24H0z\",\n                stroke: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\hand-sign.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 13V4.5a1.5 1.5 0 013 0V12M11 11.5v-2a1.5 1.5 0 013 0V12M14 10.5a1.5 1.5 0 013 0V12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\hand-sign.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17 11.5a1.5 1.5 0 013 0V16a6 6 0 01-6 6h-2 .208a6 6 0 01-5.012-2.7L7 19c-.312-.479-1.407-2.388-3.286-5.728a1.5 1.5 0 01.536-2.022 1.867 1.867 0 012.28.28L8 13M5 3L4 2M4 7H3M14 3l1-1M15 6h1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\hand-sign.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\hand-sign.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9oYW5kLXNpZ24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxXQUE4QyxDQUFDQztJQUMxRCxxQkFDRSw4REFBQ0M7UUFDQ0MsUUFBTztRQUNQQyxNQUFLO1FBQ0xDLGFBQWE7UUFDYkMsU0FBUTtRQUNSQyxlQUFjO1FBQ2RDLGdCQUFlO1FBQ2ZDLFFBQU87UUFDUEMsT0FBTTtRQUNOQyxPQUFNO1FBQ0wsR0FBR1YsS0FBSzs7MEJBRVQsOERBQUNXO2dCQUFLQyxHQUFFO2dCQUFnQlYsUUFBTzs7Ozs7OzBCQUMvQiw4REFBQ1M7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7OztBQUdkLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbXBvbmVudHMvaWNvbnMvaGFuZC1zaWduLnRzeD83OGQ0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBIYW5kU2lnbjogUmVhY3QuRkM8UmVhY3QuU1ZHQXR0cmlidXRlczx7fT4+ID0gKHByb3BzKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxzdmdcclxuICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICBzdHJva2VXaWR0aD17Mn1cclxuICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICBoZWlnaHQ9XCIxZW1cIlxyXG4gICAgICB3aWR0aD1cIjFlbVwiXHJcbiAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICB7Li4ucHJvcHN9XHJcbiAgICA+XHJcbiAgICAgIDxwYXRoIGQ9XCJNMCAwaDI0djI0SDB6XCIgc3Ryb2tlPVwibm9uZVwiIC8+XHJcbiAgICAgIDxwYXRoIGQ9XCJNOCAxM1Y0LjVhMS41IDEuNSAwIDAxMyAwVjEyTTExIDExLjV2LTJhMS41IDEuNSAwIDAxMyAwVjEyTTE0IDEwLjVhMS41IDEuNSAwIDAxMyAwVjEyXCIgLz5cclxuICAgICAgPHBhdGggZD1cIk0xNyAxMS41YTEuNSAxLjUgMCAwMTMgMFYxNmE2IDYgMCAwMS02IDZoLTIgLjIwOGE2IDYgMCAwMS01LjAxMi0yLjdMNyAxOWMtLjMxMi0uNDc5LTEuNDA3LTIuMzg4LTMuMjg2LTUuNzI4YTEuNSAxLjUgMCAwMS41MzYtMi4wMjIgMS44NjcgMS44NjcgMCAwMTIuMjguMjhMOCAxM001IDNMNCAyTTQgN0gzTTE0IDNsMS0xTTE1IDZoMVwiIC8+XHJcbiAgICA8L3N2Zz5cclxuICApO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiSGFuZFNpZ24iLCJwcm9wcyIsInN2ZyIsInN0cm9rZSIsImZpbGwiLCJzdHJva2VXaWR0aCIsInZpZXdCb3giLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJoZWlnaHQiLCJ3aWR0aCIsInhtbG5zIiwicGF0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/hand-sign.tsx\n");

/***/ }),

/***/ "./src/components/icons/star-icon.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/star-icon.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StarIcon: () => (/* binding */ StarIcon),\n/* harmony export */   StarIconNew: () => (/* binding */ StarIconNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst StarIcon = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 25.056 24\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            \"data-name\": \"Group 36413\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                id: \"Path_22667\",\n                \"data-name\": \"Path 22667\",\n                d: \"M19.474,34.679l-6.946-4.346L5.583,34.679a.734.734,0,0,1-1.1-.8L6.469,25.93.263,20.668a.735.735,0,0,1,.421-1.3l8.1-.566,3.064-7.6a.765.765,0,0,1,1.362,0l3.064,7.6,8.1.566a.735.735,0,0,1,.421,1.3L18.588,25.93l1.987,7.949a.734.734,0,0,1-1.1.8Z\",\n                transform: \"translate(0 -10.792)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\nconst StarIconNew = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 25.056 24\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            \"data-name\": \"Group 36413\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                id: \"Path_22667\",\n                \"data-name\": \"Path 22667\",\n                d: \"M19.474,34.679l-6.946-4.346L5.583,34.679a.734.734,0,0,1-1.1-.8L6.469,25.93.263,20.668a.735.735,0,0,1,.421-1.3l8.1-.566,3.064-7.6a.765.765,0,0,1,1.362,0l3.064,7.6,8.1.566a.735.735,0,0,1,.421,1.3L18.588,25.93l1.987,7.949a.734.734,0,0,1-1.1.8Z\",\n                transform: \"translate(0 -10.792)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\star-icon.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/star-icon.tsx\n");

/***/ }),

/***/ "./src/components/review-popup/index.tsx":
/*!***********************************************!*\
  !*** ./src/components/review-popup/index.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_close_icon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/close-icon */ \"./src/components/icons/close-icon.tsx\");\n/* harmony import */ var _components_icons_hand_sign__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/hand-sign */ \"./src/components/icons/hand-sign.tsx\");\n/* harmony import */ var _components_icons_star_icon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/star-icon */ \"./src/components/icons/star-icon.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/constants */ \"./src/lib/constants/index.ts\");\n/* harmony import */ var _lib_range_map__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/range-map */ \"./src/lib/range-map.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_constants__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_8__, js_cookie__WEBPACK_IMPORTED_MODULE_9__]);\n([_lib_constants__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_8__, js_cookie__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReviewModal = ()=>{\n    const { isOpen, data: { tracking_number } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    const closeModalAction = (0,react__WEBPACK_IMPORTED_MODULE_11__.useCallback)(()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(_lib_constants__WEBPACK_IMPORTED_MODULE_6__.REVIEW_POPUP_MODAL_KEY, \"true\", {\n            expires: 1\n        });\n        closeModal();\n    }, []);\n    return isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed lg:bottom-4 bottom-16 right-2 shadow-400 lg:right-4 rounded-xl lg:bg-white bg-slate-50 max-w-full z-50 sm:max-w-md sm:w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between relative px-4 py-5 border-b border-b-slate-100 lg:text-xl sm:text-lg text-base\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: [\n                            \"You last order \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-accent\",\n                                children: tracking_number\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 26\n                            }, undefined),\n                            \" \",\n                            \"is completed successfully!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: closeModalAction,\n                        \"aria-label\": \"Close panel\",\n                        className: \"inline-block outline-none focus:outline-0 px-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_close_icon__WEBPACK_IMPORTED_MODULE_1__.CloseIconNew, {}, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.order(tracking_number),\n                        className: \"hover:text-accent transition-colors lg:text-2xl text-xl mb-3 flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Rate your experience here.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 relative top-[1.875rem] left-[1.1875rem]\",\n                                        children: (0,_lib_range_map__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(4, (i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 1\n                                                },\n                                                animate: {\n                                                    opacity: [\n                                                        0,\n                                                        1,\n                                                        0\n                                                    ],\n                                                    scale: 1.5\n                                                },\n                                                exit: {\n                                                    opacity: 0.3,\n                                                    scale: 1.7\n                                                },\n                                                className: \"absolute rounded-full border border-accent\",\n                                                style: {\n                                                    background: \"linear-gradient(90deg, rgba(0, 159, 127, 0.2) 0%, rgba(1, 147, 118, 0.2) 100%)\",\n                                                    width: `${100 + i * 50}%`,\n                                                    height: `${100 + i * 50}%`,\n                                                    zIndex: 4 - i,\n                                                    top: `-${20 + i * 4}px`,\n                                                    left: `-${20 + i * 4}px`\n                                                },\n                                                transition: {\n                                                    duration: 3.4 + i,\n                                                    repeat: Infinity,\n                                                    delay: i * 1,\n                                                    // repeatDelay: 4 - i,\n                                                    ease: \"easeOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_hand_sign__WEBPACK_IMPORTED_MODULE_2__.HandSign, {\n                                        className: \"text-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: (0,_lib_range_map__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(5, (i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    scale: 1,\n                                    opacity: 0.2\n                                },\n                                animate: {\n                                    scale: 1.2,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeatType: \"reverse\",\n                                    repeat: Infinity,\n                                    ease: \"linear\",\n                                    delay: i * 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_star_icon__WEBPACK_IMPORTED_MODULE_3__.StarIconNew, {\n                                    className: \"text-[#FFE03A] lg:text-xl text-base\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, i, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\review-popup\\\\index.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined) : \"\";\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/review-popup/index.tsx\n");

/***/ }),

/***/ "./src/lib/range-map.ts":
/*!******************************!*\
  !*** ./src/lib/range-map.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rangeMap)\n/* harmony export */ });\nfunction rangeMap(n, fn) {\n    const arr = [];\n    while(n > arr.length){\n        arr.push(fn(arr.length));\n    }\n    return arr;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL3JhbmdlLW1hcC50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0MsQ0FBUyxFQUFFQyxFQUFzQjtJQUNoRSxNQUFNQyxNQUFNLEVBQUU7SUFDZCxNQUFPRixJQUFJRSxJQUFJQyxNQUFNLENBQUU7UUFDckJELElBQUlFLElBQUksQ0FBQ0gsR0FBR0MsSUFBSUMsTUFBTTtJQUN4QjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2xpYi9yYW5nZS1tYXAudHM/MWYwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByYW5nZU1hcChuOiBudW1iZXIsIGZuOiAoaTogbnVtYmVyKSA9PiBhbnkpIHtcclxuICBjb25zdCBhcnIgPSBbXTtcclxuICB3aGlsZSAobiA+IGFyci5sZW5ndGgpIHtcclxuICAgIGFyci5wdXNoKGZuKGFyci5sZW5ndGgpKTtcclxuICB9XHJcbiAgcmV0dXJuIGFycjtcclxufVxyXG4iXSwibmFtZXMiOlsicmFuZ2VNYXAiLCJuIiwiZm4iLCJhcnIiLCJsZW5ndGgiLCJwdXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/lib/range-map.ts\n");

/***/ })

};
;