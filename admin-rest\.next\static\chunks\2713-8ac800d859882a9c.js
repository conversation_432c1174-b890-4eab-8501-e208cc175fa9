"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2713],{92072:function(e,l,t){var n=t(85893),r=t(93967),i=t.n(r),o=t(98388);l.Z=e=>{let{className:l,...t}=e;return(0,n.jsx)("div",{className:(0,o.m6)(i()("rounded bg-light p-5 shadow md:p-8",l)),...t})}},85031:function(e,l,t){t.d(l,{p:function(){return PlusIcon}});var n=t(85893);let PlusIcon=e=>(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})},34927:function(e,l,t){t.d(l,{Z:function(){return action_LanguageSwitcher}});var n=t(85893),r=t(804),i=t(93345),o=t(67294),s=t(5233),a=t(11163),d=t(76518),u=t(93075),c=t(82364),h=t(24750),x=t(28368);let ChevronDown=e=>{let{color:l="currentColor",width:t="14px",height:r="10px",...i}=e;return(0,n.jsx)("svg",{width:t,height:r,...i,fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13.4 1.845a.91.91 0 0 0-1.613-.6L7.015 6.817l-4.771-5.57A.91.91 0 1 0 .866 2.428l5.457 6.378a.91.91 0 0 0 1.385 0l5.462-6.378a.91.91 0 0 0 .23-.583Z",fill:l})})};var v=t(8152),g=t(85031),p=t(71261),lang_list_box=e=>{let{title:l,items:t,translate:r,slug:i,id:o,routes:d}=e,{t:u}=(0,s.$G)("common"),{locale:c,query:{shop:h}}=(0,a.useRouter)(),f=c?null==t?void 0:t.find(e=>(null==e?void 0:e.value)===c):t[2];return(0,n.jsx)(x.p,{children:e=>{let{open:o}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(x.p.Button,{className:"flex w-full items-center justify-between border-b border-solid border-[#E5E5EB] bg-white p-4 text-left font-medium text-black",children:[l,(0,n.jsx)("span",{className:"text-[#8A8F9C]",children:(0,n.jsx)(ChevronDown,{className:"".concat(o?"origin-center rotate-180 transform":""," h-4 w-4")})})]}),(0,n.jsx)(x.p.Panel,{className:"py-2",children:null==t?void 0:t.map((e,l)=>(0,n.jsxs)("span",{className:"relative flex cursor-pointer items-center px-4 py-2 transition-all hover:bg-white ".concat((null==f?void 0:f.id)===(null==e?void 0:e.id)?"bg-white":""),children:[(0,n.jsx)("span",{className:"relative overflow-hidden rounded-full",children:null==e?void 0:e.icon}),(0,n.jsx)("span",{className:"ltr:ml-3 rtl:mr-3",children:u(null==e?void 0:e.name)}),"true"===r?(0,n.jsxs)("span",{className:"cursor-pointer text-base transition duration-200 hover:text-heading ltr:ml-auto rtl:mr-auto",children:[(0,n.jsx)(v.Z,{href:d.edit(i,null==e?void 0:e.id,h),locale:!1,className:"absolute top-0 left-0 h-full w-full"},null==e?void 0:e.id),(0,n.jsx)(p.dY,{width:16})]}):(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("span",{className:"cursor-pointer text-base transition duration-200 hover:text-heading ltr:ml-auto rtl:mr-auto",children:[(0,n.jsx)(v.Z,{href:d.translate(i,null==e?void 0:e.id,h),locale:!1,className:"absolute top-0 left-0 h-full w-full"},null==e?void 0:e.id),(0,n.jsx)(g.p,{width:24})]})})]},"language-".concat(l)))})]})}})},f=t(86215),w=t(93967),m=t.n(w);let ToggleIcon=e=>(0,n.jsx)("svg",{...e,viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M19.268 8.232a2.5 2.5 0 1 1-3.536 3.536 2.5 2.5 0 0 1 3.536-3.536ZM11.768 8.232a2.5 2.5 0 1 1-3.536 3.536 2.5 2.5 0 0 1 3.536-3.536ZM4.268 8.232a2.5 2.5 0 1 1-3.536 3.536 2.5 2.5 0 0 1 3.536-3.536Z",fill:"currentColor"})}),ToggleIconVertical=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",...e,fill:"none",children:(0,n.jsx)("path",{fill:"currentColor",d:"M3 4.5a2.257 2.257 0 0 0 2.25-2.25A2.257 2.257 0 0 0 3 0 2.257 2.257 0 0 0 .75 2.25 2.257 2.257 0 0 0 3 4.5Zm0 2.25A2.257 2.257 0 0 0 .75 9 2.257 2.257 0 0 0 3 11.25 2.257 2.257 0 0 0 5.25 9 2.257 2.257 0 0 0 3 6.75Zm0 6.75a2.257 2.257 0 0 0-2.25 2.25A2.257 2.257 0 0 0 3 18a2.257 2.257 0 0 0 2.25-2.25A2.257 2.257 0 0 0 3 13.5Z"})});var ui_popover=e=>{let{children:l,popOverButtonClass:t,popOverPanelClass:r,iconStyle:i="horizontal"}=e,{x:s,y:a,strategy:d,update:x,refs:v}=(0,u.YF)({strategy:"fixed",placement:"bottom",middleware:[(0,c.cv)(0),(0,c.RR)(),(0,c.uY)()]});return(0,o.useEffect)(()=>{if(v.reference.current&&v.floating.current)return(0,h.Me)(v.reference.current,v.floating.current,x)},[v.reference,v.floating,x]),(0,n.jsxs)(f.J,{className:"relative inline-block",children:[(0,n.jsx)(f.J.Button,{className:m()("p-2 text-base opacity-80 transition duration-200 hover:text-heading",t),ref:v.setReference,children:"horizontal"===i?(0,n.jsx)(ToggleIcon,{width:20}):(0,n.jsx)(ToggleIconVertical,{height:18,width:6})}),(0,n.jsx)("div",{ref:v.setFloating,style:{position:d,top:null!=a?a:"",left:null!=s?s:"",zIndex:1},children:(0,n.jsx)(f.J.Panel,{className:m()("w-[18rem] max-w-[20rem] overflow-hidden rounded bg-[#F7F8F9] px-4 shadow-translatePanel sm:px-0",r),children:l})})]})},language_switcher=e=>{let{record:l,slug:t,deleteModalView:x,routes:v,className:g="",enablePreviewMode:p,isShop:f,shopSlug:w,isCouponApprove:m,couponApproveButton:j}=e,b=(0,a.useRouter)(),{t:N}=(0,s.$G)("common"),{locales:_,locale:A}=b,C=[...[...d.X].filter(e=>null==_?void 0:_.includes(null==e?void 0:e.id))].filter(e=>{var t;return!(null==l?void 0:null===(t=l.translated_languages)||void 0===t?void 0:t.find(l=>l===(null==e?void 0:e.value)))}),M=[...d.X].filter(e=>{var t;return null==l?void 0:null===(t=l.translated_languages)||void 0===t?void 0:t.includes(null==e?void 0:e.id)}).filter(e=>!(null==A?void 0:A.includes(null==e?void 0:e.id))),{x:Z,y,strategy:k,update:B,refs:F}=(0,u.YF)({strategy:"fixed",placement:"bottom",middleware:[(0,c.cv)(20),(0,c.RR)(),(0,c.uY)()]});(0,o.useEffect)(()=>{if(F.reference.current&&F.floating.current)return(0,h.Me)(F.reference.current,F.floating.current,B)},[F.reference,F.floating,B]);let L="".concat("http://localhost:3003","/products/preview/").concat(t);return(0,n.jsxs)("div",{className:"flex w-full items-center justify-end gap-3 ".concat(g),children:[(0,n.jsx)(r.Z,{id:null==l?void 0:l.id,editUrl:f?v.editWithoutLang(t,w):v.editWithoutLang(t),previewUrl:L,deleteModalView:x,enablePreviewMode:p,couponApproveButton:j,isCouponApprove:m}),i.Config.defaultLanguage===b.locale&&(0,n.jsxs)(ui_popover,{children:[(null==C?void 0:C.length)?(0,n.jsx)(lang_list_box,{title:N("text-non-translated-title"),items:C,translate:"false",slug:t,id:null==l?void 0:l.id,routes:v}):null,(null==M?void 0:M.length)?(0,n.jsx)(lang_list_box,{title:N("text-translated-title"),items:M,translate:"true",slug:t,id:null==l?void 0:l.id,routes:v}):null]})]})};function action_LanguageSwitcher(e){let{record:l,slug:t,deleteModalView:o,routes:s,className:d,enablePreviewMode:u,isShop:c,shopSlug:h,couponApproveButton:x,isCouponApprove:v}=e,{enableMultiLang:g}=i.Config,{query:{shop:p}}=(0,a.useRouter)(),f="".concat("http://localhost:3003","/products/preview/").concat(t);return(0,n.jsx)(n.Fragment,{children:g?(0,n.jsx)(language_switcher,{slug:t,record:l,deleteModalView:o,routes:s,className:d,enablePreviewMode:u,isShop:c,shopSlug:h,couponApproveButton:x,isCouponApprove:v}):(0,n.jsx)(r.Z,{id:null==l?void 0:l.id,editUrl:s.editWithoutLang(t,p),previewUrl:f,enablePreviewMode:u,deleteModalView:o,couponApproveButton:x,isCouponApprove:v})})}},78998:function(e,l,t){t.d(l,{Z:function(){return title_with_sort}});var n=t(85893),r=t(93967),i=t.n(r);t(67294);let TriangleArrowDown=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,n.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,n.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:l,ascending:t,isActive:r=!0,className:o}=e;return(0,n.jsxs)("span",{className:i()("inline-flex items-center",o),children:[(0,n.jsx)("span",{title:"Sort by ".concat(l),children:l}),t?(0,n.jsx)(TriangleArrowUp,{width:"9",className:i()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":r})}):(0,n.jsx)(TriangleArrowDown,{width:"9",className:i()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":r})})]})}}}]);