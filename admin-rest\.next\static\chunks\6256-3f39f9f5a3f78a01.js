"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6256],{71261:function(e,t,r){r.d(t,{Iy:function(){return EditFillIcon},Iz:function(){return EditGhostIcon},dK:function(){return ComposeEditIcon},dY:function(){return EditIcon}});var o=r(85893);let EditIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20.547 20.299",fill:"currentColor",...e,children:(0,o.jsxs)("g",{stroke:"currentColor",strokeWidth:".4",children:[(0,o.jsx)("path",{"data-name":"Path 78",d:"M18.659 12.688a.5.5 0 00-.5.5v4.423a1.5 1.5 0 01-1.494 1.494H2.691A1.5 1.5 0 011.2 17.609V4.629a1.5 1.5 0 011.494-1.494h4.419a.5.5 0 100-1H2.691A2.493 2.493 0 00.2 4.629v12.98A2.493 2.493 0 002.691 20.1h13.976a2.493 2.493 0 002.491-2.491v-4.423a.5.5 0 00-.5-.5zm0 0"}),(0,o.jsx)("path",{"data-name":"Path 79",d:"M18.96.856a2.241 2.241 0 00-3.17 0L6.899 9.739a.5.5 0 00-.128.219l-1.169 4.219a.5.5 0 00.613.613l4.219-1.169a.5.5 0 00.219-.128l8.886-8.887a2.244 2.244 0 000-3.17zm-10.971 9.21l7.273-7.273 2.346 2.346-7.273 7.273zm-.469.94l1.879 1.875-2.592.718zm11.32-7.1l-.528.528-2.346-2.345.528-.528a1.245 1.245 0 011.761 0l.585.584a1.247 1.247 0 010 1.761zm0 0"})]})}),EditFillIcon=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("path",{d:"M4.671 7.87l4.546-4.546 1.459 1.459-4.547 4.546h0a2.563 2.563 0 01-1.08.645s0 0 0 0l-1.456.433.434-1.455c.121-.409.343-.78.644-1.081h0zm-1.189 2.57s0 0 0 0h0zm8.112-9.065a1.031 1.031 0 01.729 1.76l-.321.322-1.459-1.459.322-.32a1.03 1.03 0 01.729-.303z",fill:"currentColor",stroke:"currentColor"}),(0,o.jsx)("path",{d:"M3.063 3.063a1.75 1.75 0 00-1.75 1.75v6.125a1.75 1.75 0 001.75 1.75h6.124a1.75 1.75 0 001.75-1.75V7.874a.438.438 0 00-.874 0v3.063a.875.875 0 01-.876.874H3.064a.875.875 0 01-.876-.874V4.811a.875.875 0 01.876-.875h3.062a.437.437 0 100-.874H3.062z",fill:"currentColor"})]}),EditGhostIcon=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h2.793a.992.992 0 00.707-.293l5.23-5.229.217.869-2.3 2.3a.5.5 0 00.707.707l2.5-2.5a.5.5 0 00.132-.475l-.432-1.726L14.207 6a.999.999 0 000-1.414zM3 13v-1.793L4.793 13H3zm3-.207L3.207 10 8.5 4.707 11.293 7.5 6 12.793zm6-6L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]}),ComposeEditIcon=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h10.5a.5.5 0 000-1H7.208l7-7a.999.999 0 000-1.414zM3 10.206l5.5-5.5L11.293 7.5l-5.5 5.5H3v-2.793zm9-3.413L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]})},88762:function(e,t,r){r.d(t,{Z:function(){return OpenAIButton}});var o=r(85893),a=r(93967),n=r.n(a);function OpenAIButton(e){let{className:t,onClick:r,title:a,...u}=e;return(0,o.jsx)("div",{onClick:r,className:n()("absolute right-0 -top-1 z-10 cursor-pointer text-sm font-medium text-accent hover:text-accent-hover",t),...u,children:a})}},25898:function(e,t,r){var o=r(85893),a=r(28454),n=r(23091),u=r(66271),s=r(5233),l=r(16720),i=r(11163);t.Z=e=>{let{control:t,error:r}=e,{t:c}=(0,s.$G)(),{locale:d}=(0,i.useRouter)(),{types:f,loading:p}=(0,l.qs)({limit:200,language:d});return(0,o.jsxs)("div",{className:"mb-5",children:[(0,o.jsxs)(n.Z,{children:[c("form:input-label-group"),"*"]}),(0,o.jsx)(a.Z,{name:"type",control:t,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:f,isLoading:p}),(0,o.jsx)(u.Z,{message:c(r)})]})}},28454:function(e,t,r){var o=r(85893),a=r(79828),n=r(71611),u=r(87536);t.Z=e=>{let{control:t,options:r,name:s,rules:l,getOptionLabel:i,getOptionValue:c,disabled:d,isMulti:f,isClearable:p,isLoading:g,placeholder:m,label:h,required:x,toolTipText:b,error:v,...y}=e;return(0,o.jsxs)(o.Fragment,{children:[h?(0,o.jsx)(n.Z,{htmlFor:s,toolTipText:b,label:h,required:x}):"",(0,o.jsx)(u.Qr,{control:t,name:s,rules:l,...y,render:e=>{let{field:t}=e;return(0,o.jsx)(a.Z,{...t,getOptionLabel:i,getOptionValue:c,placeholder:m,isMulti:f,isClearable:p,isLoading:g,options:r,isDisabled:d})}}),v&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:v})]})}},87077:function(e,t,r){r.d(t,{W:function(){return a},X:function(){return o}});let o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},a={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){var o=r(85893),a=r(76518),n=r(67294),u=r(23157),s=r(87077);let l=n.forwardRef((e,t)=>{let{isRTL:r}=(0,a.S)();return(0,o.jsx)(u.ZP,{ref:t,styles:s.X,isRtl:r,...e})});l.displayName="Select",t.Z=l},22220:function(e,t,r){var o=r(85893),a=r(93967),n=r.n(a),u=r(98388);t.Z=e=>{let{children:t,className:r,...a}=e;return(0,o.jsx)("div",{className:(0,u.m6)(n()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...a,children:t})}},95414:function(e,t,r){var o=r(85893),a=r(71611),n=r(93967),u=r.n(n),s=r(67294),l=r(98388);let i=s.forwardRef((e,t)=>{let{className:r,label:n,toolTipText:s,name:i,error:c,variant:d="normal",shadow:f=!1,inputClassName:p,disabled:g,required:m,...h}=e,x=u()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===d,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===d,"border border-border-base focus:border-accent":"outline"===d},{"focus:shadow":f},p);return(0,o.jsxs)("div",{className:(0,l.m6)(u()(r)),children:[n&&(0,o.jsx)(a.Z,{htmlFor:i,toolTipText:s,label:n,required:m}),(0,o.jsx)("textarea",{id:i,name:i,className:(0,l.m6)(u()(x,g?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:g,...h}),c&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});i.displayName="TextArea",t.Z=i},61571:function(e,t,r){r.d(t,{M5:function(){return useCreateManufacturerMutation},ty:function(){return useDeleteManufacturerMutation},a7:function(){return useManufacturerQuery},ML:function(){return useManufacturersQuery},pN:function(){return useUpdateManufacturerMutation},TN:function(){return useUpdateManufacturerMutationInList}});var o=r(11163),a=r.n(o),n=r(88767),u=r(22920),s=r(5233),l=r(97514),i=r(47869),c=r(28597),d=r(55191),f=r(3737);let p={...(0,d.h)(i.P.MANUFACTURERS),paginated:e=>{let{name:t,shop_id:r,...o}=e;return f.eN.get(i.P.MANUFACTURERS,{searchJoin:"and",...o,search:f.eN.formatSearchParams({name:t,shop_id:r})})}};var g=r(93345);let useCreateManufacturerMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,s.$G)(),r=(0,o.useRouter)();return(0,n.useMutation)(p.create,{onSuccess:async()=>{let e=r.query.shop?"/".concat(r.query.shop).concat(l.Z.manufacturer.list):l.Z.manufacturer.list;await a().push(e,void 0,{locale:g.Config.defaultLanguage}),u.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useDeleteManufacturerMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,s.$G)();return(0,n.useMutation)(p.delete,{onSuccess:()=>{u.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useRouter)(),r=(0,n.useQueryClient)();return(0,n.useMutation)(p.update,{onSuccess:async r=>{let o=t.query.shop?"/".concat(t.query.shop).concat(l.Z.manufacturer.list):l.Z.manufacturer.list;await t.push("".concat(o,"/").concat(null==r?void 0:r.slug,"/edit"),void 0,{locale:g.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutationInList=()=>{let{t:e}=(0,s.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(p.update,{onSuccess:async()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.MANUFACTURERS)}})},useManufacturerQuery=e=>{let{slug:t,language:r}=e,{data:o,error:a,isLoading:u}=(0,n.useQuery)([i.P.MANUFACTURERS,{slug:t,language:r}],()=>p.get({slug:t,language:r}));return{manufacturer:o,error:a,loading:u}},useManufacturersQuery=e=>{var t;let{data:r,error:o,isLoading:a}=(0,n.useQuery)([i.P.MANUFACTURERS,e],e=>{let{queryKey:t,pageParam:r}=e;return p.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0});return{manufacturers:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(r),error:o,loading:a}}},16720:function(e,t,r){r.d(t,{jT:function(){return useCreateTypeMutation},e7:function(){return useDeleteTypeMutation},F2:function(){return useTypeQuery},qs:function(){return useTypesQuery},oy:function(){return useUpdateTypeMutation}});var o=r(11163),a=r.n(o),n=r(88767),u=r(22920),s=r(5233),l=r(97514),i=r(47869),c=r(55191),d=r(3737);let f={...(0,c.h)(i.P.TYPES),all:e=>{let{name:t,...r}=e;return d.eN.get(i.P.TYPES,{searchJoin:"and",...r,search:d.eN.formatSearchParams({name:t})})}};var p=r(93345);let useCreateTypeMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(f.create,{onSuccess:()=>{a().push(l.Z.type.list,void 0,{locale:p.Config.defaultLanguage}),u.Am.success(e("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(i.P.TYPES)}})},useDeleteTypeMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,s.$G)();return(0,n.useMutation)(f.delete,{onSuccess:()=>{u.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.TYPES)}})},useUpdateTypeMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useRouter)(),r=(0,n.useQueryClient)();return(0,n.useMutation)(f.update,{onSuccess:async r=>{let o=t.query.shop?"/".concat(t.query.shop).concat(l.Z.type.list):l.Z.type.list;await t.push("".concat(o,"/").concat(null==r?void 0:r.slug,"/edit"),void 0,{locale:p.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(i.P.TYPES)}})},useTypeQuery=e=>{let{slug:t,language:r}=e;return(0,n.useQuery)([i.P.TYPES,{slug:t,language:r}],()=>f.get({slug:t,language:r}))},useTypesQuery=e=>{let{data:t,isLoading:r,error:o}=(0,n.useQuery)([i.P.TYPES,e],e=>{let{queryKey:t,pageParam:r}=e;return f.all(Object.assign({},t[1],r))},{keepPreviousData:!0});return{types:null!=t?t:[],loading:r,error:o}}},9140:function(e,t,r){r.d(t,{e:function(){return getErrorMessage}});var o=r(11163),a=r.n(o),n=r(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let r of e.graphQLErrors){if(r.extensions&&"validation"===r.extensions.category)return t.message=r.message,t.validation=r.extensions.validation,t;r.extensions&&"authorization"===r.extensions.category&&(n.Z.remove("auth_token"),n.Z.remove("auth_permissions"),a().push("/"))}return t.message=e.message,t}},24504:function(e,t,r){r.d(t,{g:function(){return formatSlug}});function formatSlug(e){if(!e)return"";let t=e.replace(/\s+/g,"-").toLowerCase(),r=t.replace(/-+$/,"");return r}}}]);