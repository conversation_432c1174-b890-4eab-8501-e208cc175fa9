(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1872],{28839:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/flash-sale/vendor-request/[id]",function(){return s(77102)}])},52201:function(e,t,s){"use strict";var l=s(85893),a=s(5233),n=s(27484),u=s.n(n),r=s(93967),o=s.n(r),i=s(98388);t.Z=e=>{let{data:t,className:s,...n}=e,r=null==t?void 0:t.flash_sale,{t:d}=(0,a.$G)();return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:(0,i.m6)(o()(s)),...n,children:[(0,l.jsx)("h2",{className:"mb-8 border-b border-b-[#E5E5E5] pb-6 text-2xl font-semibold text-muted-black",children:"Flash sale request."}),(0,l.jsx)("div",{className:"relative overflow-hidden rounded-tl-[1.25rem] rounded-tr-[1.25rem] bg-white mb-5",children:(0,l.jsxs)("div",{className:"p-10",children:[(null==t?void 0:t.title)?(0,l.jsx)("h3",{className:"mb-4 text-xl font-semibold text-muted-black",children:null==t?void 0:t.title}):"",(null==r?void 0:r.description)?(0,l.jsx)("p",{className:"mb-8 text-base leading-[150%] text-[#666] lg:text-lg",children:null==r?void 0:r.description}):"",(0,l.jsxs)("ul",{className:"space-y-4 text-sm lg:text-base [&>li>p]:text-base-dark [&>li>span]:font-semibold [&>li>span]:text-muted-black [&>li]:flex [&>li]:items-center [&>li]:gap-1",children:[(null==r?void 0:r.sale_status)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-campaign-status")," : "]}),(0,l.jsx)("p",{children:(null==r?void 0:r.sale_status)?"On going":"On hold"})]}):"",(null==r?void 0:r.start_date)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("notice-active-date"),": "]}),(0,l.jsx)("p",{children:u()(null==r?void 0:r.start_date).format("DD MMM YYYY")})]}):"",(null==r?void 0:r.end_date)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("notice-expire-date"),": "]}),(0,l.jsx)("p",{children:u()(null==r?void 0:r.end_date).format("DD MMM YYYY")})]}):"",(null==r?void 0:r.type)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-campaign-type-on")," : "]}),(0,l.jsx)("p",{children:d(null==r?void 0:r.type)})]}):"",(null==r?void 0:r.rate)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-deals-rate")," : "]}),(0,l.jsxs)("p",{children:[null==r?void 0:r.rate,(null==r?void 0:r.type)==="percentage"?"% off.":"",(null==r?void 0:r.type)==="wallet_point_gift"?" point.":"",(null==r?void 0:r.type)==="free_shipping"?" N/A":""]})]}):""]})]})}),(0,l.jsx)("div",{className:"relative overflow-hidden bg-white mb-5",children:(0,l.jsxs)("div",{className:"p-10",children:[(0,l.jsx)("h3",{className:"mb-4 text-xl font-semibold text-muted-black",children:"Request note."}),(null==t?void 0:t.note)?(0,l.jsx)("p",{className:"mb-8 text-base leading-[150%] text-[#666] lg:text-lg",children:null==t?void 0:t.note}):""]})})]})})}},60385:function(e,t,s){"use strict";s.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var l=s(11163),a=s.n(l),n=s(88767),u=s(22920),r=s(5233),o=s(28597),i=s(97514),d=s(47869),c=s(93345),S=s(55191),h=s(3737);let p={...(0,S.h)(d.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h.eN.get(d.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...s,search:h.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:s,shop_id:l}=e;return h.eN.get("".concat(d.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:s,shop_id:l,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:s,...l}=e;return h.eN.get(d.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:s,...l,search:h.eN.formatSearchParams({title:t,shop_id:s})})},approve:e=>h.eN.post(d.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>h.eN.post(d.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...s}=e;return h.eN.get(d.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...s,search:h.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:s,error:l,isLoading:a}=(0,n.useQuery)([d.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,o.Q)(s),error:l,loading:a}},useRequestedListForFlashSale=e=>{let{id:t,language:s,shop_id:l}=e,{data:a,error:u,isLoading:r}=(0,n.useQuery)([d.P.FLASH_SALE,{id:t,language:s,shop_id:l}],()=>p.get({id:t,language:s,shop_id:l}));return{flashSaleRequest:a,error:u,loading:r}},useRequestedProductsForFlashSale=e=>{var t;let{data:s,error:l,isLoading:a}=(0,n.useQuery)([d.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.requestedProducts(Object.assign({},t[1],s))},{keepPreviousData:!0});return{products:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,o.Q)(s),error:l,loading:a}},useCreateFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),t=(0,l.useRouter)(),{t:s}=(0,r.$G)();return(0,n.useMutation)(p.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await a().push(e,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;u.Am.error(s("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)(),s=(0,l.useRouter)();return(0,n.useMutation)(p.update,{onSuccess:async t=>{let l=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await s.push(l,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var s;u.Am.error(e("common:".concat(null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)();return(0,n.useMutation)(p.delete,{onSuccess:()=>{u.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var s;u.Am.error(t("common:".concat(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)(),s=(0,l.useRouter)();return(0,n.useMutation)(p.approve,{onSuccess:async()=>{let t=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await a().push(t,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(p.disapprove,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.FLASH_SALE)}})}},77102:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return f}});var l=s(85893),a=s(52201),n=s(97670),u=s(45957),r=s(55846),o=s(60385),i=s(16203),d=s(27484),c=s.n(d),S=s(84110),h=s.n(S),p=s(29387),v=s.n(p),_=s(70178),m=s.n(_),F=s(5233),g=s(11163),x=s(34739),R=s(67294);c().extend(h()),c().extend(m()),c().extend(v());let VendorRequestFlashSaleSinglePage=()=>{var e,t;let{query:s,locale:n}=(0,g.useRouter)(),{t:d}=(0,F.$G)(),{permissions:c}=(0,i.WA)(),[S,h]=(0,R.useState)(""),[p,v]=(0,R.useState)(1),{flashSaleRequest:_,loading:m,error:f}=(0,o.n1)({id:null==s?void 0:s.id,language:n}),{products:E,loading:A,paginatorInfo:L,error:q}=(0,o.j4)({limit:5,vendor_request_id:null==s?void 0:s.id,page:p,name:S}),P=(0,R.useCallback)(e=>{let{searchText:t}=e;h(t),v(1)},[h,v]),j=(0,R.useCallback)(e=>{v(e)},[v]);return m?(0,l.jsx)(r.Z,{text:d("common:text-loading")}):f?(0,l.jsx)(u.Z,{message:f.message}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.Z,{data:_}),(0,l.jsx)("div",{className:"relative overflow-hidden bg-white mb-5",children:(0,l.jsx)(x.Z,{products:E,paginatorInfo:L,onPagination:j,handleSearch:P,type:null==_?void 0:null===(e=_.flash_sale)||void 0===e?void 0:e.type,rate:null==_?void 0:null===(t=_.flash_sale)||void 0===t?void 0:t.rate})})]})};VendorRequestFlashSaleSinglePage.authenticate={permissions:i.M$},VendorRequestFlashSaleSinglePage.Layout=n.default;var f=!0;t.default=VendorRequestFlashSaleSinglePage}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,4928,9774,2888,179],function(){return e(e.s=28839)}),_N_E=e.O()}]);