Write-Host "🚀 Starting E-commerce Platform with Docker Compose..." -ForegroundColor Green

# Create necessary directories
if (!(Test-Path "letsencrypt")) {
    New-Item -ItemType Directory -Path "letsencrypt" | Out-Null
}

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

# Build and start all services
Write-Host "🔨 Building and starting all services..." -ForegroundColor Blue
docker-compose up --build -d

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Run database migrations and seeding
Write-Host "🗄️ Setting up database..." -ForegroundColor Magenta
docker-compose exec api-rest npm run db:migrate
docker-compose exec api-rest node simple-seed.js

Write-Host "✅ Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access your services:" -ForegroundColor White
Write-Host "   • Shop Frontend: http://shop.localhost" -ForegroundColor Cyan
Write-Host "   • Admin Panel: http://admin.localhost" -ForegroundColor Cyan
Write-Host "   • API Documentation: http://api.localhost/docs" -ForegroundColor Cyan
Write-Host "   • MinIO Console: http://minio-console.localhost" -ForegroundColor Cyan
Write-Host "   • MinIO API: http://minio.localhost" -ForegroundColor Cyan
Write-Host "   • Traefik Dashboard: http://traefik.localhost:8080" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 Default credentials:" -ForegroundColor White
Write-Host "   • Admin: <EMAIL> / password" -ForegroundColor Yellow
Write-Host "   • MinIO: minioadmin / minioadmin123" -ForegroundColor Yellow
Write-Host ""
Write-Host "💡 If services are not accessible, add these entries to your hosts file:" -ForegroundColor White
Write-Host "   127.0.0.1 shop.localhost" -ForegroundColor Gray
Write-Host "   127.0.0.1 admin.localhost" -ForegroundColor Gray
Write-Host "   127.0.0.1 api.localhost" -ForegroundColor Gray
Write-Host "   127.0.0.1 minio.localhost" -ForegroundColor Gray
Write-Host "   127.0.0.1 minio-console.localhost" -ForegroundColor Gray
Write-Host "   127.0.0.1 traefik.localhost" -ForegroundColor Gray

Read-Host "Press Enter to continue"
