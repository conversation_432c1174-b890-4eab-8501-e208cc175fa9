services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      - --configFile=/etc/traefik/traefik.yml
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik/dynamic.yml:/etc/traefik/dynamic.yml:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.dashboard.service=api@internal"
    networks:
      - ecommerce-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ecommerce
      POSTGRES_USER: ecommerce_owner
      POSTGRES_PASSWORD: npg_aI0Dn8AMfbWj
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - ecommerce-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`minio.localhost`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.routers.minio.service=minio"
      - "traefik.http.services.minio.loadbalancer.server.port=9000"
      - "traefik.http.routers.minio-console.rule=Host(`minio-console.localhost`)"
      - "traefik.http.routers.minio-console.entrypoints=web"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"
    networks:
      - ecommerce-network

  # API REST Service
  api-rest:
    build:
      context: ./api-rest
      dockerfile: Dockerfile
    container_name: api-rest
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce
      DB_USERNAME: ecommerce_owner
      DB_PASSWORD: npg_aI0Dn8AMfbWj
      DB_DIALECT: postgres
    depends_on:
      - postgres
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.localhost`)"
      - "traefik.http.routers.api.entrypoints=web"
      - "traefik.http.services.api.loadbalancer.server.port=5000"
    networks:
      - ecommerce-network

  # Admin REST Service
  admin-rest:
    build:
      context: ./admin-rest
      dockerfile: Dockerfile
    container_name: admin-rest
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3002
      NEXT_PUBLIC_REST_API_ENDPOINT: http://api.localhost/api
      NEXT_PUBLIC_DEFAULT_LANGUAGE: en
      NEXT_PUBLIC_ENABLE_MULTI_LANG: "false"
      NEXT_PUBLIC_AUTH_TOKEN_KEY: authToken
    depends_on:
      - api-rest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin.rule=Host(`admin.localhost`)"
      - "traefik.http.routers.admin.entrypoints=web"
      - "traefik.http.services.admin.loadbalancer.server.port=3002"
    networks:
      - ecommerce-network

  # Shop Service
  shop:
    build:
      context: ./shop
      dockerfile: Dockerfile
    container_name: shop
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3003
      NEXT_PUBLIC_REST_API_ENDPOINT: http://api.localhost/api
      NEXT_PUBLIC_DEFAULT_LANGUAGE: en
      NEXT_PUBLIC_ENABLE_MULTI_LANG: "false"
    depends_on:
      - api-rest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shop.rule=Host(`shop.localhost`)"
      - "traefik.http.routers.shop.entrypoints=web"
      - "traefik.http.services.shop.loadbalancer.server.port=3003"
    networks:
      - ecommerce-network

networks:
  ecommerce-network:
    driver: bridge

volumes:
  postgres_data:
  minio_data:
