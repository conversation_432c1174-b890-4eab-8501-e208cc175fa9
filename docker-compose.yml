services:

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ecommerce
      POSTGRES_USER: ecommerce_owner
      POSTGRES_PASSWORD: npg_aI0Dn8AMfbWj
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - ecommerce-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - ecommerce-network

  # API REST Service
  api-rest:
    build:
      context: ./api-rest
      dockerfile: Dockerfile
    container_name: api-rest
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce
      DB_USERNAME: ecommerce_owner
      DB_PASSWORD: npg_aI0Dn8AMfbWj
      DB_DIALECT: postgres
    depends_on:
      - postgres
      - minio
    ports:
      - "5000:5000"
    networks:
      - ecommerce-network



networks:
  ecommerce-network:
    driver: bridge

volumes:
  postgres_data:
  minio_data:


# # 1. Stop all running containers
# docker stop $(docker ps -aq)

# # 2. Remove all containers
# docker rm $(docker ps -aq)

# # 3. Remove all volumes
# docker volume rm $(docker volume ls -q)

# # 4. Remove all networks except the default ones (bridge, host, none)
# docker network rm $(docker network ls -q | grep -vE "bridge|host|none")
