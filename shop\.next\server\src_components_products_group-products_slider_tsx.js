"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_group-products_slider_tsx";
exports.ids = ["src_components_products_group-products_slider_tsx"];
exports.modules = {

/***/ "./src/components/icons/check-icon.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/check-icon.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CheckIcon = ({ width = 24, height = 24, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: width,\n        height: height,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M20 6L9 17L4 12\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CheckIcon);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGVjay1pY29uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUEsTUFBTUEsWUFBK0MsQ0FBQyxFQUNwREMsUUFBUSxFQUFFLEVBQ1ZDLFNBQVMsRUFBRSxFQUNYLEdBQUdDLE9BQ0o7SUFDQyxxQkFDRSw4REFBQ0M7UUFDQ0gsT0FBT0E7UUFDUEMsUUFBUUE7UUFDUkcsU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLFFBQU87UUFDTixHQUFHSixLQUFLO2tCQUVULDRFQUFDSztZQUNDQyxHQUFFO1lBQ0ZDLGFBQVk7WUFDWkMsZUFBYztZQUNkQyxnQkFBZTs7Ozs7Ozs7Ozs7QUFJdkI7QUFFQSxpRUFBZVosU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGVjay1pY29uLnRzeD9kNjRkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IENoZWNrSWNvbjogUmVhY3QuRkM8UmVhY3QuU1ZHQXR0cmlidXRlczx7fT4+ID0gKHtcclxuICB3aWR0aCA9IDI0LFxyXG4gIGhlaWdodCA9IDI0LFxyXG4gIC4uLnByb3BzXHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPHN2Z1xyXG4gICAgICB3aWR0aD17d2lkdGh9XHJcbiAgICAgIGhlaWdodD17aGVpZ2h0fVxyXG4gICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICB7Li4ucHJvcHN9XHJcbiAgICA+XHJcbiAgICAgIDxwYXRoXHJcbiAgICAgICAgZD1cIk0yMCA2TDkgMTdMNCAxMlwiXHJcbiAgICAgICAgc3Ryb2tlV2lkdGg9XCIyXCJcclxuICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICAvPlxyXG4gICAgPC9zdmc+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IENoZWNrSWNvbjtcclxuIl0sIm5hbWVzIjpbIkNoZWNrSWNvbiIsIndpZHRoIiwiaGVpZ2h0IiwicHJvcHMiLCJzdmciLCJ2aWV3Qm94IiwiZmlsbCIsInN0cm9rZSIsInBhdGgiLCJkIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/check-icon.tsx\n");

/***/ }),

/***/ "./src/components/icons/index.ts":
/*!***************************************!*\
  !*** ./src/components/icons/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowNext: () => (/* reexport safe */ _arrow_next__WEBPACK_IMPORTED_MODULE_1__.ArrowNextIcon),\n/* harmony export */   ArrowPrev: () => (/* reexport safe */ _arrow_prev__WEBPACK_IMPORTED_MODULE_2__.ArrowPrevIcon),\n/* harmony export */   Check: () => (/* reexport safe */ _check_icon__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _check_icon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-icon */ \"./src/components/icons/check-icon.tsx\");\n/* harmony import */ var _arrow_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./arrow-next */ \"./src/components/icons/arrow-next.tsx\");\n/* harmony import */ var _arrow_prev__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./arrow-prev */ \"./src/components/icons/arrow-prev.tsx\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0Q7QUFDVTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2ljb25zL2luZGV4LnRzPzdmNjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVjayB9IGZyb20gXCIuL2NoZWNrLWljb25cIjtcclxuZXhwb3J0IHsgQXJyb3dOZXh0SWNvbiBhcyBBcnJvd05leHQgfSBmcm9tIFwiLi9hcnJvdy1uZXh0XCI7XHJcbmV4cG9ydCB7IEFycm93UHJldkljb24gYXMgQXJyb3dQcmV2IH0gZnJvbSBcIi4vYXJyb3ctcHJldlwiO1xyXG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNoZWNrIiwiQXJyb3dOZXh0SWNvbiIsIkFycm93TmV4dCIsIkFycm93UHJldkljb24iLCJBcnJvd1ByZXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/index.ts\n");

/***/ }),

/***/ "./src/components/products/group-products/slider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/products/group-products/slider.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/slider */ \"./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons */ \"./src/components/icons/index.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products/cards/card */ \"./src/components/products/cards/card.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__]);\n_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst offerSliderBreakpoints = {\n    320: {\n        slidesPerView: 1,\n        spaceBetween: 0\n    },\n    580: {\n        slidesPerView: 2,\n        spaceBetween: 16\n    },\n    1024: {\n        slidesPerView: 3,\n        spaceBetween: 16\n    },\n    1920: {\n        slidesPerView: 5,\n        spaceBetween: 24\n    }\n};\nconst ProductsSlider = ({ products })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Swiper, {\n                id: \"handPicked_products\",\n                breakpoints: offerSliderBreakpoints,\n                modules: [\n                    _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Navigation\n                ],\n                navigation: {\n                    nextEl: \".next\",\n                    prevEl: \".prev\"\n                },\n                children: products?.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.SwiperSlide, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            product: product\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product?.id, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prev absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-left-4 rtl:-right-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-left-5 rtl:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-previous\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowPrev, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"next absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-right-4 rtl:-left-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-next\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowNext, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductsSlider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/group-products/slider.tsx\n");

/***/ })

};
;