"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5217],{92072:function(e,t,l){var r=l(85893),s=l(93967),a=l.n(s),n=l(98388);t.Z=e=>{let{className:t,...l}=e;return(0,r.jsx)("div",{className:(0,n.m6)(a()("rounded bg-light p-5 shadow md:p-8",t)),...l})}},35484:function(e,t,l){var r=l(85893),s=l(93967),a=l.n(s),n=l(98388);t.Z=e=>{let{title:t,className:l,...s}=e;return(0,r.jsx)("h2",{className:(0,n.m6)(a()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",l)),...s,children:t})}},37912:function(e,t,l){var r=l(85893),s=l(5114),a=l(80287),n=l(93967),i=l.n(n),o=l(67294),c=l(87536),d=l(5233),x=l(98388);t.Z=e=>{let{className:t,onSearch:l,variant:n="outline",shadow:u=!1,inputClassName:h,placeholderText:m,...p}=e,{register:f,handleSubmit:b,watch:g,reset:w,formState:{errors:v}}=(0,c.cI)({defaultValues:{searchText:""}}),j=g("searchText"),{t:N}=(0,d.$G)();(0,o.useEffect)(()=>{j||l({searchText:""})},[j]);let y=i()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===n,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===n,"border border-border-base focus:border-accent":"outline"===n},{"focus:shadow":u},h);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,x.m6)(i()("relative flex w-full items-center",t)),onSubmit:b(l),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:N("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(a.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...f("searchText"),className:(0,x.m6)(y),placeholder:null!=m?m:N("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...p}),v.searchText&&(0,r.jsx)("p",{children:v.searchText.message}),!!j&&(0,r.jsx)("button",{type:"button",onClick:function(){w(),l({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(s.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,l){l.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=l(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},97670:function(e,t,l){l.r(t);var r=l(85893),s=l(78985),a=l(79362),n=l(8144),i=l(74673),o=l(99494),c=l(5233),d=l(1631),x=l(11163),u=l(48583),h=l(93967),m=l.n(h),p=l(30824),f=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,c.$G)(),[s,n]=(0,u.KO)(a.Hf),{childMenu:i}=t,{width:o}=(0,f.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==i?void 0:i.map(e=>{let{href:t,label:n,icon:i,childMenu:c}=e;return(0,r.jsx)(d.Z,{href:t,label:l(n),icon:i,childMenu:c,miniSidebar:s&&o>=a.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[l,s]=(0,u.KO)(a.Hf),n=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,i=Object.keys(n),{width:d}=(0,f.Z)();return(0,r.jsx)(r.Fragment,{children:null==i?void 0:i.map((e,s)=>{var i;return(0,r.jsxs)("div",{className:m()("flex flex-col px-5",l&&d>=a.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&d>=a.h2?"hidden":""),children:t(null===(i=n[e])||void 0===i?void 0:i.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:n[e]})]},s)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,x.useRouter)(),[o,c]=(0,u.KO)(a.Hf),[d]=(0,u.KO)(a.GH),[h]=(0,u.KO)(a.W4),{width:b}=(0,f.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,r.jsx)(s.Z,{}),(0,r.jsx)(i.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",b>=a.h2&&(d||h)?"lg:pt-[8.75rem]":"pt-20",o&&b>=a.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",b>=a.h2&&(d||h)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",o&&b>=a.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(n.Z,{})]})]})]})}},8953:function(e,t,l){var r=l(85893),s=l(93967),a=l.n(s),n=l(5233),i=l(98388);t.Z=e=>{let{t}=(0,n.$G)(),{className:l,color:s,textColor:o,text:c,textKey:d,animate:x=!1}=e,u={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("span",{className:(0,i.m6)(a()("inline-block",u.root,{[u.default]:!s,[u.text]:!o,[u.animate]:x},s,o,l)),children:d?t(d):c})})}},18230:function(e,t,l){l.d(t,{Z:function(){return pagination}});var r=l(85893),s=l(55891),a=l(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};l(5871);var pagination=e=>(0,r.jsx)(s.Z,{nextIcon:(0,r.jsx)(a.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},78998:function(e,t,l){l.d(t,{Z:function(){return title_with_sort}});var r=l(85893),s=l(93967),a=l.n(s);l(67294);let TriangleArrowDown=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,r.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,r.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:t,ascending:l,isActive:s=!0,className:n}=e;return(0,r.jsxs)("span",{className:a()("inline-flex items-center",n),children:[(0,r.jsx)("span",{title:"Sort by ".concat(t),children:t}),l?(0,r.jsx)(TriangleArrowUp,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":s})}):(0,r.jsx)(TriangleArrowDown,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":s})})]})}},39816:function(e,t,l){var r=l(85893),s=l(18230),a=l(27899),n=l(804),i=l(10265),o=l(99930),c=l(5233),d=l(76518),x=l(67294),u=l(78998),h=l(77556),m=l(71816),p=l(8953);t.Z=e=>{let{customers:t,paginatorInfo:l,onPagination:f,onSort:b,onOrder:g}=e,{t:w}=(0,c.$G)(),{alignLeft:v}=(0,d.S)(),[j,N]=(0,x.useState)({sort:i.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{b(e=>e===i.As.Desc?i.As.Asc:i.As.Desc),g(e),N({sort:j.sort===i.As.Desc?i.As.Asc:i.As.Desc,column:e})}}),y=[{title:(0,r.jsx)(u.Z,{title:w("table:table-item-id"),ascending:j.sort===i.As.Asc&&"id"===j.column,isActive:"id"===j.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:v,width:150,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(w("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(u.Z,{title:w("table:table-item-title"),ascending:j.sort===i.As.Asc&&"id"===j.column,isActive:"id"===j.column}),className:"cursor-pointer",dataIndex:"name",key:"name",align:v,width:250,ellipsis:!0,onHeaderCell:()=>onHeaderClick("name"),render:(e,t)=>{var l;let{profile:s,email:a}=t;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.Z,{name:e,src:null==s?void 0:null===(l=s.avatar)||void 0===l?void 0:l.thumbnail}),(0,r.jsxs)("div",{className:"flex flex-col whitespace-nowrap font-medium ms-2",children:[e,(0,r.jsx)("span",{className:"text-[13px] font-normal text-gray-500/80",children:a})]})]})}},{title:w("table:table-item-permissions"),dataIndex:"permissions",key:"permissions",align:v,width:300,render:e=>(0,r.jsx)("div",{className:"flex flex-wrap gap-1.5 whitespace-nowrap",children:null==e?void 0:e.map(e=>{let{name:t,index:l}=e;return(0,r.jsx)("span",{className:"rounded bg-gray-200/50 px-2.5 py-1",children:t},l)})})},{title:w("table:table-item-available_wallet_points"),dataIndex:["wallet","available_points"],key:"available_wallet_points",align:"center",width:150},{title:(0,r.jsx)(u.Z,{title:w("table:table-item-status"),ascending:j.sort===i.As.Asc&&"is_active"===j.column,isActive:"is_active"===j.column}),width:150,className:"cursor-pointer",dataIndex:"is_active",key:"is_active",align:"center",onHeaderCell:()=>onHeaderClick("is_active"),render:e=>(0,r.jsx)(p.Z,{textKey:e?"common:text-active":"common:text-inactive",color:e?"bg-accent/10 !text-accent":"bg-status-failed/10 text-status-failed"})},{title:w("table:table-item-actions"),dataIndex:"id",key:"actions",align:"right",width:120,render:function(e,t){let{is_active:l}=t,{data:s}=(0,o.UE)();return(0,r.jsx)(r.Fragment,{children:(null==s?void 0:s.id)!=e&&(0,r.jsx)(n.Z,{id:e,userStatus:!0,isUserActive:l,showAddWalletPoints:!0,showMakeAdminButton:!0})})}}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,r.jsx)(a.i,{columns:y,emptyText:()=>(0,r.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,r.jsx)(h.m,{className:"w-52"}),(0,r.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:w("table:empty-table-data")}),(0,r.jsx)("p",{className:"text-[13px]",children:w("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==l?void 0:l.total)&&(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(s.Z,{total:l.total,current:l.currentPage,pageSize:l.perPage,onChange:f})})]})}}}]);