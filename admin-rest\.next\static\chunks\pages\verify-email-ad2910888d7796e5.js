(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9070,9930],{13541:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/verify-email",function(){return s(65706)}])},29160:function(e,t,s){"use strict";s.d(t,{Z:function(){return AuthPageLayout}});var n=s(85893),u=s(51237);function AuthPageLayout(e){let{children:t}=e;return(0,n.jsx)("div",{className:"flex h-screen items-center justify-center bg-light sm:bg-gray-100",children:(0,n.jsxs)("div",{className:"m-auto w-full max-w-[420px] rounded bg-light p-5 sm:p-8 sm:shadow",children:[(0,n.jsx)("div",{className:"mb-2 flex justify-center",children:(0,n.jsx)(u.Z,{})}),t]})})}s(67294)},51237:function(e,t,s){"use strict";var n=s(85893),u=s(8152),o=s(93967),i=s.n(o),r=s(99494),a=s(48583),l=s(79362),c=s(62964),d=s(25675),f=s.n(d),S=s(11163),m=s(90573);t.Z=e=>{var t,s,o,d,g,v,P,E,h,p,y;let{className:A,...M}=e,{locale:N}=(0,S.useRouter)(),{settings:Q}=(0,m.n)({language:N}),[_,R]=(0,a.KO)(l.Hf),{width:L}=(0,c.Z)();return(0,n.jsx)(u.Z,{href:null===r.siteSettings||void 0===r.siteSettings?void 0:null===(t=r.siteSettings.logo)||void 0===t?void 0:t.href,className:i()("inline-flex items-center gap-3",A),children:_&&L>=l.h2?(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:r.siteSettings.collapseLogo.width,height:r.siteSettings.collapseLogo.height},children:(0,n.jsx)(f(),{src:null!==(E=null==Q?void 0:null===(o=Q.options)||void 0===o?void 0:null===(s=o.collapseLogo)||void 0===s?void 0:s.original)&&void 0!==E?E:r.siteSettings.collapseLogo.url,alt:null!==(h=null==Q?void 0:null===(d=Q.options)||void 0===d?void 0:d.siteTitle)&&void 0!==h?h:r.siteSettings.collapseLogo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})}):(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:r.siteSettings.logo.width,height:r.siteSettings.logo.height},children:(0,n.jsx)(f(),{src:null!==(p=null==Q?void 0:null===(v=Q.options)||void 0===v?void 0:null===(g=v.logo)||void 0===g?void 0:g.original)&&void 0!==p?p:r.siteSettings.logo.url,alt:null!==(y=null==Q?void 0:null===(P=Q.options)||void 0===P?void 0:P.siteTitle)&&void 0!==y?y:r.siteSettings.logo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})})})}},44498:function(e,t,s){"use strict";s.d(t,{B:function(){return o}});var n=s(47869),u=s(3737);let o={me:()=>u.eN.get(n.P.ME),login:e=>u.eN.post(n.P.TOKEN,e),logout:()=>u.eN.post(n.P.LOGOUT,{}),register:e=>u.eN.post(n.P.REGISTER,e),update:e=>{let{id:t,input:s}=e;return u.eN.put("".concat(n.P.USERS,"/").concat(t),s)},changePassword:e=>u.eN.post(n.P.CHANGE_PASSWORD,e),forgetPassword:e=>u.eN.post(n.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>u.eN.post(n.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>u.eN.post(n.P.RESET_PASSWORD,e),makeAdmin:e=>u.eN.post(n.P.MAKE_ADMIN,e),block:e=>u.eN.post(n.P.BLOCK_USER,e),unblock:e=>u.eN.post(n.P.UNBLOCK_USER,e),addWalletPoints:e=>u.eN.post(n.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>u.eN.post(n.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...s}=e;return u.eN.get(n.P.USERS,{searchJoin:"and",with:"wallet",...s,search:u.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return u.eN.get(n.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return u.eN.get("".concat(n.P.USERS,"/").concat(t))},resendVerificationEmail:()=>u.eN.post(n.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return u.eN.post(n.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...s}=e;return u.eN.get(n.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...s})},fetchCustomers:e=>{let{...t}=e;return u.eN.get(n.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:s,name:o,...i}=e;return u.eN.get(n.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...i,search:u.eN.formatSearchParams({name:o,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:s,...o}=e;return u.eN.get(n.P.ALL_STAFFS,{searchJoin:"and",...o,search:u.eN.formatSearchParams({name:s,is_active:t})})}}},99930:function(e,t,s){"use strict";s.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var n=s(79362),u=s(97514),o=s(31955),i=s(5233),r=s(11163),a=s(88767),l=s(22920),c=s(47869),d=s(44498),f=s(28597),S=s(87066),m=s(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,r.useRouter)();return(0,a.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===u.Z.verifyLicense&&t.replace(u.Z.dashboard),t.pathname===u.Z.verifyEmail&&((0,m.Fu)(!0),t.replace(u.Z.dashboard))},onError:s=>{if(S.Z.isAxiosError(s)){var n,o;if((null===(n=s.response)||void 0===n?void 0:n.status)===417){t.replace(u.Z.verifyLicense);return}if((null===(o=s.response)||void 0===o?void 0:o.status)===409){(0,m.Fu)(!1),t.replace(u.Z.verifyEmail);return}e.clear(),t.replace(u.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,r.useRouter)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{o.Z.remove(n.E$),e.replace(u.Z.login),l.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{l.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};l.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,i.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{l.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,l.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,i.$G)();(0,a.useQueryClient)();let t=(0,r.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{l.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{l.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{l.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{l.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{l.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}},useAdminsQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}},useVendorsQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}},useCustomersQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}},useMyStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}},useAllStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:u}=(0,a.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:u}}},65706:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSG:function(){return d},default:function(){return VerifyEmailActions}});var n=s(85893),u=s(60802),o=s(99930),i=s(97514),r=s(11163),a=s(29160),l=s(5233),c=s(16203),d=!0;function VerifyEmailActions(){let{t:e}=(0,l.$G)("common");(0,o.UE)();let{mutate:t,isLoading:s}=(0,o._y)(),{mutate:d,isLoading:f}=(0,o.y6)(),S=(0,r.useRouter)(),{emailVerified:m}=(0,c.T9)();return m&&S.push(i.Z.dashboard),(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(a.Z,{children:[(0,n.jsx)("h3",{className:"mt-4 mb-6 text-center text-base italic text-red-500 text-body",children:e("common:email-not-verified")}),(0,n.jsxs)("div",{className:"w-full space-y-3",children:[(0,n.jsx)(u.Z,{onClick:()=>d(),disabled:f,className:"w-full",children:e("common:text-resend-verification-email")}),(0,n.jsx)(u.Z,{type:"button",disabled:s,className:"w-full",onClick:()=>t(),children:e("common:authorized-nav-item-logout")})]})]})})}}},function(e){e.O(0,[2964,9494,9774,2888,179],function(){return e(e.s=13541)}),_N_E=e.O()}]);