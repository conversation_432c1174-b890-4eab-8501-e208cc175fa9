"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_dashboard_widgets_box_widget-order-by-status_tsx"],{

/***/ "./src/components/dashboard/widgets/box/widget-order-by-status.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/dashboard/widgets/box/widget-order-by-status.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/widgets/sticker-card */ \"./src/components/widgets/sticker-card.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/summary/order-processed */ \"./src/components/icons/summary/order-processed.tsx\");\n/* harmony import */ var _components_icons_summary_customers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/summary/customers */ \"./src/components/icons/summary/customers.tsx\");\n/* harmony import */ var _components_icons_summary_checklist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/icons/summary/checklist */ \"./src/components/icons/summary/checklist.tsx\");\n/* harmony import */ var _components_icons_summary_earning__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/icons/summary/earning */ \"./src/components/icons/summary/earning.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst WidgetOrderByStatus = (param)=>{\n    let { order, timeFrame = 1, allowedStatus } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    let tempContent = [];\n    const widgetContents = [\n        {\n            key: \"pending\",\n            title: t(\"text-pending-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_checklist__WEBPACK_IMPORTED_MODULE_6__.ChecklistIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, undefined),\n            color: \"#0094FF\",\n            data: order === null || order === void 0 ? void 0 : order.pending\n        },\n        {\n            key: \"processing\",\n            title: t(\"text-processing-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_customers__WEBPACK_IMPORTED_MODULE_5__.CustomersIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, undefined),\n            color: \"#28B7FF\",\n            data: order === null || order === void 0 ? void 0 : order.processing\n        },\n        {\n            key: \"complete\",\n            title: t(\"text-completed-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__.OrderProcessedIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, undefined),\n            color: \"#FF8D29\",\n            data: order === null || order === void 0 ? void 0 : order.complete\n        },\n        {\n            key: \"cancel\",\n            title: t(\"text-cancelled-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_earning__WEBPACK_IMPORTED_MODULE_7__.EaringIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, undefined),\n            color: \"#D7E679\",\n            data: order === null || order === void 0 ? void 0 : order.cancelled\n        },\n        {\n            key: \"refund\",\n            title: t(\"text-refunded-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__.OrderProcessedIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, undefined),\n            color: \"#A7F3D0\",\n            data: order === null || order === void 0 ? void 0 : order.refunded\n        },\n        {\n            key: \"fail\",\n            title: t(\"text-failed-order\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__.OrderProcessedIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, undefined),\n            color: \"#A7F3D0\",\n            data: order === null || order === void 0 ? void 0 : order.failed\n        },\n        {\n            key: \"local-facility\",\n            title: t(\"text-order-local-facility\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__.OrderProcessedIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 77,\n                columnNumber: 13\n            }, undefined),\n            color: \"#A7F3D0\",\n            data: order === null || order === void 0 ? void 0 : order.localFacility\n        },\n        {\n            key: \"out-for-delivery\",\n            title: t(\"text-order-out-delivery\"),\n            subtitle: \"sticker-card-subtitle-last-\".concat(timeFrame, \"-days\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_summary_order_processed__WEBPACK_IMPORTED_MODULE_4__.OrderProcessedIcon, {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                lineNumber: 85,\n                columnNumber: 13\n            }, undefined),\n            color: \"#A7F3D0\",\n            data: order === null || order === void 0 ? void 0 : order.outForDelivery\n        }\n    ];\n    for(let index = 0; index < allowedStatus.length; index++){\n        const element = allowedStatus[index];\n        const items = widgetContents.find((item)=>item.key === element);\n        tempContent.push(items);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-5 grid w-full grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-4\",\n            children: tempContent && tempContent.length > 0 ? tempContent.map((content)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_widgets_sticker_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        titleTransKey: content === null || content === void 0 ? void 0 : content.title,\n                        subtitleTransKey: content === null || content === void 0 ? void 0 : content.subtitle,\n                        icon: content === null || content === void 0 ? void 0 : content.icon,\n                        color: content === null || content === void 0 ? void 0 : content.color,\n                        price: content === null || content === void 0 ? void 0 : content.data\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 19\n                    }, undefined)\n                }, content === null || content === void 0 ? void 0 : content.key, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 17\n                }, undefined);\n            }) : \"\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\box\\\\widget-order-by-status.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WidgetOrderByStatus, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = WidgetOrderByStatus;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WidgetOrderByStatus);\nvar _c;\n$RefreshReg$(_c, \"WidgetOrderByStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/widgets/box/widget-order-by-status.tsx\n"));

/***/ }),

/***/ "./src/components/icons/summary/customers.tsx":
/*!****************************************************!*\
  !*** ./src/components/icons/summary/customers.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomersIcon: function() { return /* binding */ CustomersIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CustomersIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#FCB36B\",\n                d: \"M6.008 16.937C2.689 16.937 0 19.635 0 22.963v1.769c0 .477.385.863.86.863h10.294c.476 0 .861-.386.861-.863v-1.769c0-3.328-2.69-6.026-6.007-6.026ZM9.31 11.882a3.308 3.308 0 0 0-3.303-3.313 3.307 3.307 0 0 0-3.302 3.313 3.307 3.307 0 0 0 3.302 3.312 3.308 3.308 0 0 0 3.303-3.312ZM29.294 11.348a3.307 3.307 0 0 0-3.302-3.313 3.307 3.307 0 0 0-3.303 3.313 3.307 3.307 0 0 0 3.303 3.312 3.307 3.307 0 0 0 3.302-3.313ZM25.994 16.528c-3.318 0-6.008 2.698-6.008 6.026v1.769c0 .476.385.863.861.863H31.14c.476 0 .861-.387.861-.863v-1.77c0-3.327-2.69-6.025-6.007-6.025Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\customers.tsx\",\n                lineNumber: 3,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#723535\",\n                d: \"M16 15.178c-4.666 0-8.45 3.795-8.45 8.475v2.488c0 .67.543 1.214 1.211 1.214H23.24c.668 0 1.21-.544 1.21-1.214v-2.488c0-4.68-3.783-8.475-8.449-8.475ZM20.21 9.055a4.403 4.403 0 0 0-4.396-4.41 4.403 4.403 0 0 0-4.396 4.41 4.403 4.403 0 0 0 4.396 4.41 4.403 4.403 0 0 0 4.396-4.41Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\customers.tsx\",\n                lineNumber: 7,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\customers.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n_c = CustomersIcon;\nvar _c;\n$RefreshReg$(_c, \"CustomersIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/summary/customers.tsx\n"));

/***/ }),

/***/ "./src/components/icons/summary/order-processed.tsx":
/*!**********************************************************!*\
  !*** ./src/components/icons/summary/order-processed.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderProcessedIcon: function() { return /* binding */ OrderProcessedIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst OrderProcessedIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#EA9453\",\n                d: \"M25.067 4.373V24A1.07 1.07 0 0 1 24 25.067H1.6A1.07 1.07 0 0 1 .533 24V4.373h24.534Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 5,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#FCB36B\",\n                d: \"M24.642 4.8H.96a.427.427 0 0 1-.33-.693L3.333.693a.422.422 0 0 1 .33-.16h18.272a.422.422 0 0 1 .33.16l2.708 3.414a.427.427 0 0 1-.33.693Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 9,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#fff\",\n                d: \"M9.066 18.667H3.733a.533.533 0 0 0-.534.533v2.667c0 .294.239.533.534.533h5.333a.533.533 0 0 0 .533-.533V19.2a.533.533 0 0 0-.533-.533Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#30947F\",\n                d: \"M12.308 22.695a.557.557 0 0 0 .351.456l1.112.444a.968.968 0 0 1 .558.622c.142.455.325.896.547 1.318a.97.97 0 0 1 .045.832l-.468 1.104a.562.562 0 0 0 .087.586c.35.417.737.802 1.154 1.152a.56.56 0 0 0 .573.076l1.103-.47a.966.966 0 0 1 .833.045 7.8 7.8 0 0 0 1.316.547.968.968 0 0 1 .622.558l.444 1.112a.56.56 0 0 0 .464.352c.55.051 1.104.051 1.653 0a.554.554 0 0 0 .453-.351l.444-1.112a.968.968 0 0 1 .622-.559 7.8 7.8 0 0 0 1.316-.546.967.967 0 0 1 .833-.046l1.103.471a.563.563 0 0 0 .587-.087c.416-.35.801-.737 1.151-1.154a.558.558 0 0 0 .076-.574l-.47-1.103a.968.968 0 0 1 .045-.832c.222-.422.405-.863.547-1.318a.97.97 0 0 1 .557-.622l1.112-.444a.557.557 0 0 0 .352-.455 8.347 8.347 0 0 0 0-1.658.556.556 0 0 0-.352-.456l-1.112-.444a.97.97 0 0 1-.558-.622 7.824 7.824 0 0 0-.546-1.317.97.97 0 0 1-.045-.832l.47-1.104a.564.564 0 0 0-.086-.587 9.568 9.568 0 0 0-1.155-1.152.558.558 0 0 0-.58-.077l-1.102.471a.97.97 0 0 1-.833-.046 7.8 7.8 0 0 0-1.317-.546.97.97 0 0 1-.621-.558l-.444-1.112a.556.556 0 0 0-.464-.352 8.9 8.9 0 0 0-1.654 0 .554.554 0 0 0-.452.35l-.444 1.113a.968.968 0 0 1-.622.558 7.791 7.791 0 0 0-1.316.546.969.969 0 0 1-.833.046l-1.097-.47a.562.562 0 0 0-.587.088c-.416.35-.801.736-1.152 1.153a.558.558 0 0 0-.075.574l.47 1.103a.967.967 0 0 1-.045.833 7.828 7.828 0 0 0-.547 1.317.97.97 0 0 1-.558.622l-1.112.444a.56.56 0 0 0-.352.462 8.379 8.379 0 0 0-.001 1.651Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#fff\",\n                d: \"M21.867 27.2a5.333 5.333 0 1 0 0-10.667 5.333 5.333 0 0 0 0 10.667Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#30947F\",\n                d: \"M21.868 25.067a3.2 3.2 0 1 0 0-6.4 3.2 3.2 0 0 0 0 6.4Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 25,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#914747\",\n                d: \"m9.066 4.8 1.6-4.267h4.267l1.6 4.267H9.066Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"#723535\",\n                d: \"M9.066 4.8v6.589a.197.197 0 0 0 .315.16l1.436-1.076a.197.197 0 0 1 .236 0l1.632 1.223a.196.196 0 0 0 .236 0l1.63-1.222a.196.196 0 0 1 .235 0l1.432 1.074a.197.197 0 0 0 .315-.16V4.8H9.066Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n                lineNumber: 30,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\summary\\\\order-processed.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n_c = OrderProcessedIcon;\nvar _c;\n$RefreshReg$(_c, \"OrderProcessedIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/summary/order-processed.tsx\n"));

/***/ })

}]);