global:
  checkNewVersion: false
  sendAnonymousUsage: false

api:
  dashboard: true
  debug: true
  insecure: true

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: ecommerce-network
  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

certificatesResolvers:
  myresolver:
    acme:
      tlsChallenge: {}
      email: <EMAIL>
      storage: /letsencrypt/acme.json

log:
  level: INFO

accessLog: {}
