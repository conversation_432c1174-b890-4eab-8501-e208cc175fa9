(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2300],{74483:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/flash-sale",function(){return n(24394)}])},35484:function(e,t,n){"use strict";var l=n(85893),r=n(93967),o=n.n(r),a=n(98388);t.Z=e=>{let{title:t,className:n,...r}=e;return(0,l.jsx)("h2",{className:(0,a.m6)(o()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...r,children:t})}},37912:function(e,t,n){"use strict";var l=n(85893),r=n(5114),o=n(80287),a=n(93967),s=n.n(a),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:v,...m}=e,{register:h,handleSubmit:x,watch:b,reset:g,formState:{errors:S}}=(0,i.cI)({defaultValues:{searchText:""}}),y=b("searchText"),{t:P}=(0,c.$G)();(0,u.useEffect)(()=>{y||n({searchText:""})},[y]);let A=s()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,l.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(s()("relative flex w-full items-center",t)),onSubmit:x(n),children:[(0,l.jsx)("label",{htmlFor:"search",className:"sr-only",children:P("form:input-label-search")}),(0,l.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,l.jsx)(o.W,{className:"h-5 w-5"})}),(0,l.jsx)("input",{type:"text",id:"search",...h("searchText"),className:(0,d.m6)(A),placeholder:null!=v?v:P("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...m}),S.searchText&&(0,l.jsx)("p",{children:S.searchText.message}),!!y&&(0,l.jsx)("button",{type:"button",onClick:function(){g(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,l.jsx)(r.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){"use strict";n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var l=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,l.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,l.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},97670:function(e,t,n){"use strict";n.r(t);var l=n(85893),r=n(78985),o=n(79362),a=n(8144),s=n(74673),u=n(99494),i=n(5233),c=n(1631),d=n(11163),p=n(48583),f=n(93967),v=n.n(f),m=n(30824),h=n(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:n}=(0,i.$G)(),[r,a]=(0,p.KO)(o.Hf),{childMenu:s}=t,{width:u}=(0,h.Z)();return(0,l.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:a,icon:s,childMenu:i}=e;return(0,l.jsx)(c.Z,{href:t,label:n(a),icon:s,childMenu:i,miniSidebar:r&&u>=o.h2},a)})})},SideBarGroup=()=>{var e;let{t}=(0,i.$G)(),[n,r]=(0,p.KO)(o.Hf),a=null===u.siteSettings||void 0===u.siteSettings?void 0:null===(e=u.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(a),{width:c}=(0,h.Z)();return(0,l.jsx)(l.Fragment,{children:null==s?void 0:s.map((e,r)=>{var s;return(0,l.jsxs)("div",{className:v()("flex flex-col px-5",n&&c>=o.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,l.jsx)("div",{className:v()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",n&&c>=o.h2?"hidden":""),children:t(null===(s=a[e])||void 0===s?void 0:s.label)}),(0,l.jsx)(SidebarItemMap,{menuItems:a[e]})]},r)})})};t.default=e=>{let{children:t}=e,{locale:n}=(0,d.useRouter)(),[u,i]=(0,p.KO)(o.Hf),[c]=(0,p.KO)(o.GH),[f]=(0,p.KO)(o.W4),{width:x}=(0,h.Z)();return(0,l.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===n||"he"===n?"rtl":"ltr",children:[(0,l.jsx)(r.Z,{}),(0,l.jsx)(s.Z,{children:(0,l.jsx)(SideBarGroup,{})}),(0,l.jsxs)("div",{className:"flex flex-1",children:[(0,l.jsx)("aside",{className:v()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",x>=o.h2&&(c||f)?"lg:pt-[8.75rem]":"pt-20",u&&x>=o.h2?"lg:w-24":"lg:w-76"),children:(0,l.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,l.jsx)(m.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,l.jsx)(SideBarGroup,{})})})}),(0,l.jsxs)("main",{className:v()("relative flex w-full flex-col justify-start transition-[padding] duration-300",x>=o.h2&&(c||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",u&&x>=o.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,l.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,l.jsx)(a.Z,{})]})]})]})}},8953:function(e,t,n){"use strict";var l=n(85893),r=n(93967),o=n.n(r),a=n(5233),s=n(98388);t.Z=e=>{let{t}=(0,a.$G)(),{className:n,color:r,textColor:u,text:i,textKey:c,animate:d=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("span",{className:(0,s.m6)(o()("inline-block",p.root,{[p.default]:!r,[p.text]:!u,[p.animate]:d},r,u,n)),children:c?t(c):i})})}},18230:function(e,t,n){"use strict";n.d(t,{Z:function(){return pagination}});var l=n(85893),r=n(55891),o=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,l.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,l.jsx)(r.Z,{nextIcon:(0,l.jsx)(o.T,{}),prevIcon:(0,l.jsx)(ArrowPrev,{}),...e})},80246:function(e,t,n){"use strict";n.d(t,{Mv:function(){return useCreateFlashSaleMutation},in:function(){return useDeleteFlashSaleMutation},gr:function(){return useFlashSaleQuery},AX:function(){return useFlashSalesQuery},Aq:function(){return useProductFlashSaleInfo},yp:function(){return useUpdateFlashSaleMutation}});var l=n(11163),r=n.n(l),o=n(88767),a=n(22920),s=n(5233),u=n(28597),i=n(97514),c=n(47869),d=n(93345),p=n(55191),f=n(3737);let v={...(0,p.h)(c.P.FLASH_SALE),all:function(){let{title:e,shop_id:t,...n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return f.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:t,...n,search:f.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{slug:t,language:n,shop_id:l}=e;return f.eN.get("".concat(c.P.FLASH_SALE,"/").concat(t),{language:n,shop_id:l,slug:t,with:"products"})},paginated:e=>{let{title:t,shop_id:n,...l}=e;return f.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:n,...l,search:f.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>f.eN.post(c.P.FLASH_SALE,e),disapprove:e=>f.eN.post(c.P.FLASH_SALE,e),getFlashSaleInfoByProductID(e){let{id:t,language:n}=e;return f.eN.get(c.P.PRODUCT_FLASH_SALE_INFO,{searchJoin:"and",id:t,language:n,with:"flash_sales"})}},useFlashSaleQuery=e=>{let{slug:t,language:n,shop_id:l}=e,{data:r,error:a,isLoading:s}=(0,o.useQuery)([c.P.FLASH_SALE,{slug:t,language:n,shop_id:l}],()=>v.get({slug:t,language:n,shop_id:l}));return{flashSale:r,error:a,loading:s}},useFlashSalesQuery=e=>{var t;let{data:n,error:l,isLoading:r}=(0,o.useQuery)([c.P.FLASH_SALE,e],e=>{let{queryKey:t,pageParam:n}=e;return v.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{flashSale:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(n),error:l,loading:r}},useCreateFlashSaleMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,l.useRouter)(),{t:n}=(0,s.$G)();return(0,o.useMutation)(v.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.flashSale.list):i.Z.flashSale.list;await r().push(e,void 0,{locale:d.Config.defaultLanguage}),a.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var t;a.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useQueryClient)(),n=(0,l.useRouter)();return(0,o.useMutation)(v.update,{onSuccess:async t=>{let l=n.query.shop?"/".concat(n.query.shop).concat(i.Z.flashSale.list):i.Z.flashSale.list;await n.push(l,void 0,{locale:d.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)},onError:t=>{var n;a.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteFlashSaleMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,s.$G)();return(0,o.useMutation)(v.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var n;a.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})},useProductFlashSaleInfo=e=>{let{id:t,language:n}=e,{data:l,error:r,isLoading:a}=(0,o.useQuery)([c.P.PRODUCT_FLASH_SALE_INFO,{id:t,language:n}],()=>v.getFlashSaleInfoByProductID({id:t,language:n}));return{flashSaleInfo:l,error:r,loading:a}}},24394:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return L},default:function(){return FlashSales}});var l=n(85893),r=n(92072),o=n(35484),a=n(37912),s=n(804),u=n(77556),i=n(34927),c=n(18230),d=n(27899),p=n(78998),f=n(97514),v=n(10265),m=n(76518),h=n(27484),x=n.n(h),b=n(84110),g=n.n(b),S=n(29387),y=n.n(S),P=n(70178),A=n.n(P),E=n(5233),N=n(11163),I=n(67294),j=n(8953);x().extend(g()),x().extend(A()),x().extend(y());var flash_sale_list=e=>{let{flashSale:t,paginatorInfo:n,onPagination:r,onSort:o,onOrder:a}=e,{t:h}=(0,E.$G)();(0,N.useRouter)();let{alignLeft:b,alignRight:g}=(0,m.S)(),[S,y]=(0,I.useState)({sort:null===v.As||void 0===v.As?void 0:v.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{o(e=>e===(null===v.As||void 0===v.As?void 0:v.As.Desc)?null===v.As||void 0===v.As?void 0:v.As.Asc:null===v.As||void 0===v.As?void 0:v.As.Desc),a(e),y({sort:(null==S?void 0:S.sort)===(null===v.As||void 0===v.As?void 0:v.As.Desc)?null===v.As||void 0===v.As?void 0:v.As.Asc:null===v.As||void 0===v.As?void 0:v.As.Desc,column:e})}}),P=[{title:(0,l.jsx)(p.Z,{title:h("table:table-item-id"),ascending:S.sort===v.As.Asc&&"id"===S.column,isActive:"id"===S.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:b,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(h("table:table-item-id"),": ").concat(e)},{title:(0,l.jsx)(p.Z,{title:h("table:table-item-title"),ascending:(null==S?void 0:S.sort)===(null===v.As||void 0===v.As?void 0:v.As.Asc)&&(null==S?void 0:S.column)==="title",isActive:(null==S?void 0:S.column)==="title"}),className:"cursor-pointer",dataIndex:"title",key:"title",align:b,ellipsis:!0,width:200,onHeaderCell:()=>onHeaderClick("title"),render:e=>(0,l.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,l.jsx)(p.Z,{title:h("table:table-item-description"),ascending:(null==S?void 0:S.sort)===(null===v.As||void 0===v.As?void 0:v.As.Asc)&&(null==S?void 0:S.column)==="description",isActive:(null==S?void 0:S.column)==="description"}),className:"cursor-pointer",dataIndex:"description",key:"description",align:b,width:350,ellipsis:!0,onHeaderCell:()=>onHeaderClick("description"),render:e=>(0,l.jsx)("span",{dangerouslySetInnerHTML:{__html:(null==e?void 0:e.length)<130?e:(null==e?void 0:e.substring(0,130))+"..."}})},{title:(0,l.jsx)(p.Z,{title:h("table:table-item-start-date"),ascending:(null==S?void 0:S.sort)===(null===v.As||void 0===v.As?void 0:v.As.Asc)&&(null==S?void 0:S.column)==="start_date",isActive:(null==S?void 0:S.column)==="start_date"}),className:"cursor-pointer",dataIndex:"start_date",key:"start_date",align:"center",onHeaderCell:()=>onHeaderClick("start_date"),render:e=>{let t=x()(e).format("DD MMM YYYY");return(0,l.jsx)("span",{className:"whitespace-nowrap",children:t})}},{title:(0,l.jsx)(p.Z,{title:h("table:table-item-end-date"),ascending:(null==S?void 0:S.sort)===(null===v.As||void 0===v.As?void 0:v.As.Asc)&&(null==S?void 0:S.column)==="end_date",isActive:(null==S?void 0:S.column)==="end_date"}),className:"cursor-pointer",dataIndex:"end_date",key:"end_date",align:"center",onHeaderCell:()=>onHeaderClick("end_date"),render:e=>{let t=x()(e).format("DD MMM YYYY");return(0,l.jsx)("span",{className:"whitespace-nowrap",children:t})}},{title:(0,l.jsx)(p.Z,{title:h("table:table-item-status"),ascending:S.sort===v.As.Asc&&"sale_status"===S.column,isActive:"sale_status"===S.column}),className:"cursor-pointer",dataIndex:"sale_status",key:"sale_status",align:"center",width:150,onHeaderCell:()=>onHeaderClick("sale_status"),render:e=>(0,l.jsx)(j.Z,{textKey:e?"Active":"Inactive",color:e?"bg-accent/10 !text-accent":"bg-status-failed/10 text-status-failed"})},{title:h("table:table-item-details"),dataIndex:"id",key:"actions",align:"center",width:150,render:(e,t)=>{let{slug:n,is_approved:r}=t;return(0,l.jsx)(s.Z,{id:e,detailsUrl:"/flash-sale/".concat(n)})}},{title:h("table:table-item-actions"),dataIndex:"slug",key:"actions",align:g,width:150,render:(e,t)=>(0,l.jsx)(i.Z,{slug:e,record:t,deleteModalView:"DELETE_FLASH_SALE",routes:null===f.Z||void 0===f.Z?void 0:f.Z.flashSale})}];return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,l.jsx)(d.i,{columns:P,emptyText:()=>(0,l.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,l.jsx)(u.m,{className:"w-52"}),(0,l.jsx)("div",{className:"pt-6 mb-1 text-base font-semibold text-heading",children:h("table:empty-table-data")}),(0,l.jsx)("p",{className:"text-[13px]",children:h("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==n?void 0:n.total)&&(0,l.jsx)("div",{className:"flex items-center justify-end",children:(0,l.jsx)(c.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:r})})]})},C=n(97670),T=n(45957),_=n(61616),k=n(55846),F=n(93345),O=n(80246),D=n(16203),L=!0;function FlashSales(){var e;let{t}=(0,E.$G)(),{locale:n}=(0,N.useRouter)(),[s,u]=(0,I.useState)("created_at"),[i,c]=(0,I.useState)(v.As.Desc),[d,p]=(0,I.useState)(""),[m,h]=(0,I.useState)(1),{flashSale:x,loading:b,paginatorInfo:g,error:S}=(0,O.AX)({language:n,limit:20,page:m,title:d,orderBy:s,sortedBy:i});return b?(0,l.jsx)(k.Z,{text:t("common:text-loading")}):S?(0,l.jsx)(T.Z,{message:S.message}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(r.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,l.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,l.jsx)(o.Z,{title:t("form:form-title-flash-sale-campaigns")})}),(0,l.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:w-2/3 md:flex-row md:space-y-0 xl:w-3/4 2xl:w-1/2",children:[(0,l.jsx)(a.Z,{onSearch:function(e){let{searchText:t}=e;p(t),h(1)}}),n===F.Config.defaultLanguage&&(0,l.jsxs)(_.Z,{href:null===f.Z||void 0===f.Z?void 0:null===(e=f.Z.flashSale)||void 0===e?void 0:e.create,className:"h-12 w-full md:w-auto md:ms-6",children:[(0,l.jsxs)("span",{className:"hidden xl:block",children:["+ ",t("text-non-translated-title")," ",t("text-campaign")]}),(0,l.jsxs)("span",{className:"xl:hidden",children:["+ ",t("form:button-label-add")]})]})]})]}),(0,l.jsx)(flash_sale_list,{flashSale:x,paginatorInfo:g,onPagination:function(e){h(e)},onOrder:u,onSort:c})]})}FlashSales.authenticate={permissions:D.M$},FlashSales.Layout=C.default},28368:function(e,t,n){"use strict";n.d(t,{p:function(){return I}});var l,r,o,a=n(67294),s=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),v=n(14157),m=n(15466),h=n(73781);let x=null!=(o=a.startTransition)?o:function(e){e()};var b=((l=b||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l),g=((r=g||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let S={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},y=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(y);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}y.displayName="DisclosureContext";let P=(0,a.createContext)(null);P.displayName="DisclosureAPIContext";let A=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,S,e,t)}A.displayName="DisclosurePanelContext";let E=a.Fragment,N=u.AN.RenderStrategy|u.AN.Static,I=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...l}=e,r=(0,a.useRef)(null),o=(0,i.T)(t,(0,i.h)(e=>{r.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:v,buttonId:x},b]=p,g=(0,h.z)(e=>{b({type:1});let t=(0,m.r)(r);if(!t||!x)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(x):t.getElementById(x);null==n||n.focus()}),S=(0,a.useMemo)(()=>({close:g}),[g]),A=(0,a.useMemo)(()=>({open:0===v,close:g}),[v,g]);return a.createElement(y.Provider,{value:p},a.createElement(P.Provider,{value:S},a.createElement(f.up,{value:(0,s.E)(v,{0:f.ZM.Open,1:f.ZM.Closed})},(0,u.sY)({ourProps:{ref:o},theirProps:l,slot:A,defaultTag:E,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:l=`headlessui-disclosure-button-${n}`,...r}=e,[o,s]=M("Disclosure.Button"),f=(0,a.useContext)(A),m=null!==f&&f===o.panelId,x=(0,a.useRef)(null),b=(0,i.T)(x,t,m?null:o.buttonRef);(0,a.useEffect)(()=>{if(!m)return s({type:2,buttonId:l}),()=>{s({type:2,buttonId:null})}},[l,s,m]);let g=(0,h.z)(e=>{var t;if(m){if(1===o.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),S=(0,h.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),y=(0,h.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(m?(s({type:0}),null==(n=o.buttonRef.current)||n.focus()):s({type:0}))}),P=(0,a.useMemo)(()=>({open:0===o.disclosureState}),[o]),E=(0,v.f)(e,x),N=m?{ref:b,type:E,onKeyDown:g,onClick:y}:{ref:b,id:l,type:E,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:g,onKeyUp:S,onClick:y};return(0,u.sY)({ourProps:N,theirProps:r,slot:P,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:l=`headlessui-disclosure-panel-${n}`,...r}=e,[o,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(P);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,o.panelRef,e=>{x(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:l}),()=>{s({type:3,panelId:null})}),[l,s]);let v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===o.disclosureState,h=(0,a.useMemo)(()=>({open:0===o.disclosureState,close:d}),[o,d]);return a.createElement(A.Provider,{value:o.panelId},(0,u.sY)({ourProps:{ref:p,id:l},theirProps:r,slot:h,defaultTag:"div",features:N,visible:m,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){"use strict";n.d(t,{J:function(){return L}});var l,r,o=n(67294),a=n(32984),s=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),v=n(14157),m=n(39650),h=n(15466),x=n(51074),b=n(14007),g=n(46045),S=n(73781),y=n(45662),P=n(3855),A=n(16723),E=n(65958),N=n(2740),I=((l=I||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l),j=((r=j||{})[r.TogglePopover=0]="TogglePopover",r[r.ClosePopover=1]="ClosePopover",r[r.SetButton=2]="SetButton",r[r.SetButtonId=3]="SetButtonId",r[r.SetPanel=4]="SetPanel",r[r.SetPanelId=5]="SetPanelId",r);let C={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},T=(0,o.createContext)(null);function oe(e){let t=(0,o.useContext)(T);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}T.displayName="PopoverContext";let _=(0,o.createContext)(null);function fe(e){let t=(0,o.useContext)(_);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}_.displayName="PopoverAPIContext";let k=(0,o.createContext)(null);function Ee(){return(0,o.useContext)(k)}k.displayName="PopoverGroupContext";let F=(0,o.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,C,e,t)}F.displayName="PopoverPanelContext";let O=s.AN.RenderStrategy|s.AN.Static,D=s.AN.RenderStrategy|s.AN.Static,L=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:l=!1,...r}=e,i=(0,o.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,o.useRef)([]),v=(0,o.useReducer)(Ne,{__demoMode:l,popoverState:l?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,o.createRef)(),afterPanelSentinel:(0,o.createRef)()}),[{popoverState:h,button:g,buttonId:y,panel:A,panelId:I,beforePanelSentinel:j,afterPanelSentinel:C},k]=v,O=(0,x.i)(null!=(n=i.current)?n:g),D=(0,o.useMemo)(()=>{if(!g||!A)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(g))^Number(null==e?void 0:e.contains(A)))return!0;let e=(0,p.GO)(),t=e.indexOf(g),n=(t+e.length-1)%e.length,l=(t+1)%e.length,r=e[n],o=e[l];return!A.contains(r)&&!A.contains(o)},[g,A]),L=(0,P.E)(y),Z=(0,P.E)(I),R=(0,o.useMemo)(()=>({buttonId:L,panelId:Z,close:()=>k({type:1})}),[L,Z,k]),H=Ee(),B=null==H?void 0:H.registerPopover,z=(0,S.z)(()=>{var e;return null!=(e=null==H?void 0:H.isFocusWithinPopoverGroup())?e:(null==O?void 0:O.activeElement)&&((null==g?void 0:g.contains(O.activeElement))||(null==A?void 0:A.contains(O.activeElement)))});(0,o.useEffect)(()=>null==B?void 0:B(R),[B,R]);let[G,$]=(0,N.k)(),K=(0,E.v)({mainTreeNodeRef:null==H?void 0:H.mainTreeNodeRef,portals:G,defaultContainers:[g,A]});(0,b.O)(null==O?void 0:O.defaultView,"focus",e=>{var t,n,l,r;e.target!==window&&e.target instanceof HTMLElement&&0===h&&(z()||g&&A&&(K.contains(e.target)||null!=(n=null==(t=j.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(r=null==(l=C.current)?void 0:l.contains)&&r.call(l,e.target)||k({type:1})))},!0),(0,m.O)(K.resolveContainers,(e,t)=>{k({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==g||g.focus())},0===h);let Q=(0,S.z)(e=>{k({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:g:g;null==t||t.focus()}),V=(0,o.useMemo)(()=>({close:Q,isPortalled:D}),[Q,D]),U=(0,o.useMemo)(()=>({open:0===h,close:Q}),[h,Q]);return o.createElement(F.Provider,{value:null},o.createElement(T.Provider,{value:v},o.createElement(_.Provider,{value:V},o.createElement(f.up,{value:(0,a.E)(h,{0:f.ZM.Open,1:f.ZM.Closed})},o.createElement($,null,(0,s.sY)({ourProps:{ref:c},theirProps:r,slot:U,defaultTag:"div",name:"Popover"}),o.createElement(K.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-button-${n}`,...r}=e,[f,m]=oe("Popover.Button"),{isPortalled:h}=fe("Popover.Button"),b=(0,o.useRef)(null),P=`headlessui-focus-sentinel-${(0,i.M)()}`,A=Ee(),E=null==A?void 0:A.closeOthers,N=null!==(0,o.useContext)(F);(0,o.useEffect)(()=>{if(!N)return m({type:3,buttonId:l}),()=>{m({type:3,buttonId:null})}},[N,l,m]);let[I]=(0,o.useState)(()=>Symbol()),j=(0,u.T)(b,t,N?null:e=>{if(e)f.buttons.current.push(I);else{let e=f.buttons.current.indexOf(I);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&m({type:2,button:e})}),C=(0,u.T)(b,t),T=(0,x.i)(b),_=(0,S.z)(e=>{var t,n,l;if(N){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),m({type:1}),null==(l=f.button)||l.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==E||E(f.buttonId)),m({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==E?void 0:E(f.buttonId);if(!b.current||null!=T&&T.activeElement&&!b.current.contains(T.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1})}}),k=(0,S.z)(e=>{N||e.key===c.R.Space&&e.preventDefault()}),O=(0,S.z)(t=>{var n,l;(0,d.P)(t.currentTarget)||e.disabled||(N?(m({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==E||E(f.buttonId)),m({type:0}),null==(l=f.button)||l.focus()))}),D=(0,S.z)(e=>{e.preventDefault(),e.stopPropagation()}),L=0===f.popoverState,Z=(0,o.useMemo)(()=>({open:L}),[L]),R=(0,v.f)(e,b),H=N?{ref:C,type:R,onKeyDown:_,onClick:O}:{ref:j,id:f.buttonId,type:R,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:_,onKeyUp:k,onClick:O,onMouseDown:D},B=(0,y.l)(),z=(0,S.z)(()=>{let e=f.panel;e&&(0,a.E)(B.current,{[y.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[y.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(B.current,{[y.N.Forwards]:p.TO.Next,[y.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return o.createElement(o.Fragment,null,(0,s.sY)({ourProps:H,theirProps:r,slot:Z,defaultTag:"button",name:"Popover.Button"}),L&&!N&&h&&o.createElement(g._,{id:P,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:z}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-overlay-${n}`,...r}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,u.T)(t),v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===a,h=(0,S.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),x=(0,o.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:p,id:l,"aria-hidden":!0,onClick:h},theirProps:r,slot:x,defaultTag:"div",features:O,visible:m,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-panel-${n}`,focus:r=!1,...d}=e,[v,m]=oe("Popover.Panel"),{close:h,isPortalled:b}=fe("Popover.Panel"),P=`headlessui-focus-sentinel-before-${(0,i.M)()}`,E=`headlessui-focus-sentinel-after-${(0,i.M)()}`,N=(0,o.useRef)(null),I=(0,u.T)(N,t,e=>{m({type:4,panel:e})}),j=(0,x.i)(N);(0,A.e)(()=>(m({type:5,panelId:l}),()=>{m({type:5,panelId:null})}),[l,m]);let C=(0,f.oJ)(),T=null!==C?(C&f.ZM.Open)===f.ZM.Open:0===v.popoverState,_=(0,S.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==v.popoverState||!N.current||null!=j&&j.activeElement&&!N.current.contains(j.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1}),null==(t=v.button)||t.focus()}});(0,o.useEffect)(()=>{var t;e.static||1===v.popoverState&&(null==(t=e.unmount)||t)&&m({type:4,panel:null})},[v.popoverState,e.unmount,e.static,m]),(0,o.useEffect)(()=>{if(v.__demoMode||!r||0!==v.popoverState||!N.current)return;let e=null==j?void 0:j.activeElement;N.current.contains(e)||(0,p.jA)(N.current,p.TO.First)},[v.__demoMode,r,N,v.popoverState]);let k=(0,o.useMemo)(()=>({open:0===v.popoverState,close:h}),[v,h]),O={ref:I,id:l,onKeyDown:_,onBlur:r&&0===v.popoverState?e=>{var t,n,l,r,o;let a=e.relatedTarget;a&&N.current&&(null!=(t=N.current)&&t.contains(a)||(m({type:1}),(null!=(l=null==(n=v.beforePanelSentinel.current)?void 0:n.contains)&&l.call(n,a)||null!=(o=null==(r=v.afterPanelSentinel.current)?void 0:r.contains)&&o.call(r,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},L=(0,y.l)(),Z=(0,S.z)(()=>{let e=N.current;e&&(0,a.E)(L.current,{[y.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=v.afterPanelSentinel.current)||t.focus())},[y.N.Backwards]:()=>{var e;null==(e=v.button)||e.focus({preventScroll:!0})}})}),R=(0,S.z)(()=>{let e=N.current;e&&(0,a.E)(L.current,{[y.N.Forwards]:()=>{var e;if(!v.button)return;let t=(0,p.GO)(),n=t.indexOf(v.button),l=t.slice(0,n+1),r=[...t.slice(n+1),...l];for(let t of r.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=v.panel)&&e.contains(t)){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}(0,p.jA)(r,p.TO.First,{sorted:!1})},[y.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=v.button)||t.focus())}})});return o.createElement(F.Provider,{value:l},T&&b&&o.createElement(g._,{id:P,ref:v.beforePanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:Z}),(0,s.sY)({ourProps:O,theirProps:d,slot:k,defaultTag:"div",features:D,visible:T,name:"Popover.Panel"}),T&&b&&o.createElement(g._,{id:E,ref:v.afterPanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:R}))}),Group:(0,s.yV)(function(e,t){let n=(0,o.useRef)(null),l=(0,u.T)(n,t),[r,a]=(0,o.useState)([]),i=(0,E.H)(),c=(0,S.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,S.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,S.z)(()=>{var e;let t=(0,h.r)(n);if(!t)return!1;let l=t.activeElement;return!!(null!=(e=n.current)&&e.contains(l))||r.some(e=>{var n,r;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(l))||(null==(r=t.getElementById(e.panelId.current))?void 0:r.contains(l))})}),f=(0,S.z)(e=>{for(let t of r)t.buttonId.current!==e&&t.close()}),v=(0,o.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),m=(0,o.useMemo)(()=>({}),[]);return o.createElement(k.Provider,{value:v},(0,s.sY)({ourProps:{ref:l},theirProps:e,slot:m,defaultTag:"div",name:"Popover.Group"}),o.createElement(i.MainTreeNode,null))})})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,8504,2713,9774,2888,179],function(){return e(e.s=74483)}),_N_E=e.O()}]);