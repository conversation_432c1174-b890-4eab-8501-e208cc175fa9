{"version": 3, "file": "TextEncoder.js", "sourceRoot": "", "sources": ["../../../src/common/TextEncoder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAE1D,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAYlC;;;;GAIG;AACH;IAOE,qBAAY,KAAc,EAAE,OAA4B;QAEtD,IAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,+DAA+D;QAE/D,eAAe;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,gCAAgC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,eAAe;QACf,+BAA+B;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,8BAA8B;QAC9B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC;QAErE,0CAA0C;QAC1C,oBAAoB;QACpB,gDAAgD;QAChD,2DAA2D;QAE3D,4CAA4C;QAC5C,IAAI,OAAO,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC,EAAE;YAC1D,wBAAwB;YACxB,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACnD,IAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa;gBACtD,MAAM,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC5B,MAAM,KAAK,CAAC,sBAAsB;oBAChC,uDAAuD,CAAC,CAAC;aAC5D;YACD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC5B,oBAAoB;YACpB,6GAA6G;YAC7G,gDAAgD;SAC/C;aAAM;YACL,qBAAqB;YACrB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;YAEtC,IAAM,GAAG,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;YAEnC,IAAI,KAAK,KAAK,SAAS,IAAI,SAAS,IAAI,GAAG,EAAE;gBAC3C,OAAO,CAAC,IAAI,CAAC,sDAAsD;sBAC/D,mBAAmB,CAAC,CAAC;aAC1B;SACF;QAED,wBAAwB;QACxB,8BAA8B;QAC9B,oDAAoD;QAEpD,iBAAiB;QACjB,cAAc;IAChB,CAAC;IASD,sBAAI,iCAAQ;QAPZ,8BAA8B;QAC9B,mEAAmE;QACnE,+DAA+D;QAC/D,iCAAiC;QACjC,qEAAqE;QACrE,QAAQ;QACR,IAAI;aACJ;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,CAAC;;;OAAA;IAED;;;;OAIG;IACH,4BAAM,GAAN,UAAO,UAAkB,EAAE,OAAuB;QAChD,UAAU,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChE,IAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,0DAA0D;QAC1D,gEAAgE;QAChE,0DAA0D;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa;YACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5C,KAAK,EAAE,IAAI,CAAC,MAAM,KAAK,OAAO;aAC/B,CAAC,CAAC;QACL,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnD,gCAAgC;QAChC,IAAM,KAAK,GAAG,IAAI,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;QAEzD,gCAAgC;QAChC,IAAM,MAAM,GAAG,EAAE,CAAC;QAElB,wCAAwC;QACxC,IAAI,MAAyB,CAAC;QAC9B,qCAAqC;QACrC,OAAO,IAAI,EAAE;YACX,oDAAoD;YACpD,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YAC3B,IAAI,KAAK,KAAK,aAAa;gBACzB,MAAM;YACR,+DAA+D;YAC/D,iBAAiB;YACjB,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,MAAM,KAAK,QAAQ;gBACrB,MAAM;YACR,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,4BAA4B,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC;;gBAEhE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvB;QACD,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,OAAO,IAAI,EAAE;gBACX,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpD,IAAI,MAAM,KAAK,QAAQ;oBACrB,MAAM;gBACR,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,4BAA4B,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC;;oBAEhE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACvB;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;QACD,iEAAiE;QACjE,8DAA8D;QAC9D,qBAAqB;QACrB,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IACH,kBAAC;AAAD,CAAC,AAvID,IAuIC"}