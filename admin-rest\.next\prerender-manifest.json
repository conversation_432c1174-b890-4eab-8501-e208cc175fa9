{"version": 4, "routes": {"/en/attributes": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes.json"}, "/en/authors": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/authors.json"}, "/en/become-seller": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/become-seller.json"}, "/en/categories/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/categories/create.json"}, "/en/categories": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/categories.json"}, "/en/coupons": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons.json"}, "/en/faqs/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs/create.json"}, "/en/faqs": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs.json"}, "/en/flash-sale/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale/create.json"}, "/en/flash-sale": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale.json"}, "/en/groups": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/groups.json"}, "/en/groups/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/groups/create.json"}, "/en/logout": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/logout.json"}, "/en/login": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/login.json"}, "/en/manufacturers/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers/create.json"}, "/en/manufacturers": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers.json"}, "/en/new-shops": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/new-shops.json"}, "/en/message": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/message.json"}, "/en/notify-logs": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/notify-logs.json"}, "/en/my-shops": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/my-shops.json"}, "/en/authors/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/authors/create.json"}, "/en/orders/checkout": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/checkout.json"}, "/en/orders/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/create.json"}, "/en/orders": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/orders.json"}, "/en/orders/transaction": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/transaction.json"}, "/en/products": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/products.json"}, "/en/products/product-stock": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/products/product-stock.json"}, "/en/products/inventory": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/products/inventory.json"}, "/en/questions": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/questions.json"}, "/en/owner-message": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/owner-message.json"}, "/en/refund-policies/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-policies/create.json"}, "/en/refund-policies": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-policies.json"}, "/en/profile-update": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/profile-update.json"}, "/en/refund-reasons/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-reasons/create.json"}, "/en/refund-reasons": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-reasons.json"}, "/en/register": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/register.json"}, "/en/refunds": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/refunds.json"}, "/en/reviews": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/reviews.json"}, "/en/settings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings.json"}, "/en/settings/events": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/events.json"}, "/en/settings/maintenance": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/maintenance.json"}, "/en/settings/promotion-popup": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/promotion-popup.json"}, "/en/coupons/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons/create.json"}, "/en/settings/shop": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/shop.json"}, "/en/products/draft": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/products/draft.json"}, "/en/settings/seo": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/seo.json"}, "/en/settings/company-information": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/company-information.json"}, "/en/settings/payment": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/payment.json"}, "/en/shippings/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings/create.json"}, "/en/shippings": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings.json"}, "/en/shop-transfer": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shop-transfer.json"}, "/en/notice": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/notice.json"}, "/en/my-shop": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/my-shop.json"}, "/en/shops": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shops.json"}, "/en/store-notices": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/store-notices.json"}, "/en/store-notices/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/store-notices/create.json"}, "/en/tags": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/tags.json"}, "/en/taxes/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/create.json"}, "/en/taxes": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes.json"}, "/en/terms-and-conditions/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/terms-and-conditions/create.json"}, "/en/terms-and-conditions": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/terms-and-conditions.json"}, "/en/tags/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/tags/create.json"}, "/en/users/customer": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/customer.json"}, "/en/users/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/create.json"}, "/en/users": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users.json"}, "/en/users/my-staffs": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/my-staffs.json"}, "/en/users/vendor-staffs": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendor-staffs.json"}, "/en/verify-email": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/verify-email.json"}, "/en/verify-license": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/verify-license.json"}, "/en/users/vendors": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendors.json"}, "/en/users/vendors/pending": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendors/pending.json"}, "/en/withdraws": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/withdraws.json"}, "/en/users/admins": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/users/admins.json"}, "/en/shops/create": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shops/create.json"}, "/en/shop-transfer/vendor": {"initialRevalidateSeconds": false, "srcRoute": null, "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/shop-transfer/vendor.json"}}, "dynamicRoutes": {"/[shop]": {"routeRegex": "^/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/[shop].json", "fallback": null, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)\\.json$"}, "/attributes/[attributeId]/[action]": {"routeRegex": "^/attributes/([^/]+?)/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes/[attributeId]/[action].json", "fallback": null, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes/([^/]+?)/([^/]+?)\\.json$"}, "/taxes/edit/[id]": {"routeRegex": "^/taxes/edit/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/edit/[id].json", "fallback": null, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/edit/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "00c833e422e883b6079d86936964fbaf", "previewModeSigningKey": "8b1caf96f565ef2a69cb99a6cd77103199b8fe3fce94502e188de9116e00c1c8", "previewModeEncryptionKey": "1c8a95bd1ba7d569b6974f6850843853e626a5a7e6936f63afc33b60ff702571"}}