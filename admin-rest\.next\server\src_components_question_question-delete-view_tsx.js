"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_question_question-delete-view_tsx";
exports.ids = ["src_components_question_question-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/question/question-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/question/question-delete-view.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_question__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/question */ \"./src/data/question.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_question__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_question__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst QuestionDeleteView = ()=>{\n    const { mutate: deleteQuestion, isLoading: loading } = (0,_data_question__WEBPACK_IMPORTED_MODULE_3__.useDeleteQuestionMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteQuestion({\n            id: data?.id\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\question\\\\question-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/question/question-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/question.ts":
/*!*************************************!*\
  !*** ./src/data/client/question.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionClient: () => (/* binding */ questionClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst questionClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS),\n    get ({ id }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS}/${id}`);\n    },\n    paginated: ({ type, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.QUESTIONS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/question.ts\n");

/***/ }),

/***/ "./src/data/question.ts":
/*!******************************!*\
  !*** ./src/data/question.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeleteQuestionMutation: () => (/* binding */ useDeleteQuestionMutation),\n/* harmony export */   useQuestionsQuery: () => (/* binding */ useQuestionsQuery),\n/* harmony export */   useReplyQuestionMutation: () => (/* binding */ useReplyQuestionMutation)\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _data_client_question__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/client/question */ \"./src/data/client/question.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _data_client_question__WEBPACK_IMPORTED_MODULE_5__]);\n([_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _data_client_question__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useQuestionsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        questions: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_1__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\nconst useReplyQuestionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\nconst useDeleteQuestionMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_question__WEBPACK_IMPORTED_MODULE_5__.questionClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.QUESTIONS);\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/question.ts\n");

/***/ })

};
;