"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5657,9930],{39762:function(e,t,r){r.r(t);var o=r(85893),n=r(76785),s=r(60802),i=r(45957),u=r(79828),a=r(86599),c=r(30042),l=r(99930),d=r(10265),f=r(16203),p=r(41609),m=r.n(p),g=r(5233),S=r(25675),v=r.n(S),b=r(67294),y=r(87536);let formatOptionLabel=e=>{let{logo:t,name:r}=e;return(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"relative mr-3 h-6 w-6 shrink-0 overflow-hidden rounded-full",children:m()(null==t?void 0:t.thumbnail)?(0,o.jsx)(n.E,{className:"text-[1.5rem]",color:"#DDDDDD"}):(0,o.jsx)(v(),{src:null==t?void 0:t.thumbnail,alt:r,className:"product-image object-contain",fill:!0,sizes:"(max-width: 768px) 100vw"})}),(0,o.jsx)("div",{className:"truncate",children:r})]})};t.default=()=>{let[e,t]=(0,b.useState)(null),[r,n]=(0,b.useState)(!1),{t:p}=(0,g.$G)(),{permissions:m}=(0,f.WA)(),S=(0,f.Ft)(f.M$,m),v={limit:1e3,page:1,orderBy:"created_at",sortedBy:d.As.Desc},{shops:E,loading:P,error:h}=(0,c.uL)(v),{admins:A,loading:R,error:M}=(0,l.Jc)(v),{mutate:_,isLoading:D}=(0,a.As)(),{handleSubmit:x}=(0,y.cI)();if(S?h:M)return(0,o.jsx)(i.Z,{message:null==h?void 0:h.message});async function onSubmit(){(e||!r)&&_({shop_id:e})}return(0,o.jsxs)("div",{className:"m-auto block max-w-lg rounded bg-light p-6 md:w-[32.5rem]",children:[(0,o.jsx)("h2",{className:"mb-6 text-base font-medium",children:p("text-starting-chat")}),(0,o.jsxs)("form",{onSubmit:x(onSubmit),children:[(0,o.jsx)(u.Z,{options:S?E:A,isLoading:S?P:R,getOptionLabel:e=>e.name,getOptionValue:e=>e.slug,placeholder:"Find Vendor",onChange:e=>{t(null==e?void 0:e.id),n(null==e?void 0:e.is_active)},isClearable:!0,formatOptionLabel:formatOptionLabel}),(0,o.jsx)("div",{className:"mt-6 text-right",children:(0,o.jsx)(s.Z,{className:"px-4 text-base ",loading:D,disabled:!!D||!e||!r,children:p("text-start-conversation")})})]})]})}},87077:function(e,t,r){r.d(t,{W:function(){return n},X:function(){return o}});let o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){var o=r(85893),n=r(76518),s=r(67294),i=r(23157),u=r(87077);let a=s.forwardRef((e,t)=>{let{isRTL:r}=(0,n.S)();return(0,o.jsx)(i.ZP,{ref:t,styles:u.X,isRtl:r,...e})});a.displayName="Select",t.Z=a},44498:function(e,t,r){r.d(t,{B:function(){return s}});var o=r(47869),n=r(3737);let s={me:()=>n.eN.get(o.P.ME),login:e=>n.eN.post(o.P.TOKEN,e),logout:()=>n.eN.post(o.P.LOGOUT,{}),register:e=>n.eN.post(o.P.REGISTER,e),update:e=>{let{id:t,input:r}=e;return n.eN.put("".concat(o.P.USERS,"/").concat(t),r)},changePassword:e=>n.eN.post(o.P.CHANGE_PASSWORD,e),forgetPassword:e=>n.eN.post(o.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>n.eN.post(o.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>n.eN.post(o.P.RESET_PASSWORD,e),makeAdmin:e=>n.eN.post(o.P.MAKE_ADMIN,e),block:e=>n.eN.post(o.P.BLOCK_USER,e),unblock:e=>n.eN.post(o.P.UNBLOCK_USER,e),addWalletPoints:e=>n.eN.post(o.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>n.eN.post(o.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...r}=e;return n.eN.get(o.P.USERS,{searchJoin:"and",with:"wallet",...r,search:n.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return n.eN.get(o.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return n.eN.get("".concat(o.P.USERS,"/").concat(t))},resendVerificationEmail:()=>n.eN.post(o.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return n.eN.post(o.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...r}=e;return n.eN.get(o.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...r})},fetchCustomers:e=>{let{...t}=e;return n.eN.get(o.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:r,name:s,...i}=e;return n.eN.get(o.P.MY_STAFFS,{searchJoin:"and",shop_id:r,...i,search:n.eN.formatSearchParams({name:s,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:r,...s}=e;return n.eN.get(o.P.ALL_STAFFS,{searchJoin:"and",...s,search:n.eN.formatSearchParams({name:r,is_active:t})})}}},99930:function(e,t,r){r.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var o=r(79362),n=r(97514),s=r(31955),i=r(5233),u=r(11163),a=r(88767),c=r(22920),l=r(47869),d=r(44498),f=r(28597),p=r(87066),m=r(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,u.useRouter)();return(0,a.useQuery)([l.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===n.Z.verifyLicense&&t.replace(n.Z.dashboard),t.pathname===n.Z.verifyEmail&&((0,m.Fu)(!0),t.replace(n.Z.dashboard))},onError:r=>{if(p.Z.isAxiosError(r)){var o,s;if((null===(o=r.response)||void 0===o?void 0:o.status)===417){t.replace(n.Z.verifyLicense);return}if((null===(s=r.response)||void 0===s?void 0:s.status)===409){(0,m.Fu)(!1),t.replace(n.Z.verifyEmail);return}e.clear(),t.replace(n.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,u.useRouter)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{s.Z.remove(o.E$),e.replace(n.Z.login),c.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{c.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(l.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.ME),t.invalidateQueries(l.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};c.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(l.P.ME),t.invalidateQueries(l.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,i.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{c.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,c.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,i.$G)();(0,a.useQueryClient)();let t=(0,u.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{c.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{c.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{c.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(l.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{c.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(l.P.USERS),e.invalidateQueries(l.P.STAFFS),e.invalidateQueries(l.P.ADMIN_LIST),e.invalidateQueries(l.P.CUSTOMERS),e.invalidateQueries(l.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{c.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(l.P.USERS),e.invalidateQueries(l.P.STAFFS),e.invalidateQueries(l.P.ADMIN_LIST),e.invalidateQueries(l.P.CUSTOMERS),e.invalidateQueries(l.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([l.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}},useAdminsQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}},useVendorsQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}},useCustomersQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}},useMyStaffsQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}},useAllStaffsQuery=e=>{var t;let{data:r,isLoading:o,error:n}=(0,a.useQuery)([l.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(r),loading:o,error:n}}},23157:function(e,t,r){r.d(t,{ZP:function(){return u}});var o=r(65342),n=r(87462),s=r(67294),i=r(76416);r(48711),r(73935),r(73469);var u=(0,s.forwardRef)(function(e,t){var r=(0,o.u)(e);return s.createElement(i.S,(0,n.Z)({ref:t},r))})},97326:function(e,t,r){r.d(t,{Z:function(){return _assertThisInitialized}});function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},15671:function(e,t,r){r.d(t,{Z:function(){return _classCallCheck}});function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}},43144:function(e,t,r){r.d(t,{Z:function(){return _createClass}});var o=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,o.Z)(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var o=r(71002),n=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,s=_getPrototypeOf(e);if(t){var i=_getPrototypeOf(this).constructor;r=Reflect.construct(s,arguments,i)}else r=s.apply(this,arguments);return function(e,t){if(t&&("object"==(0,o.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,r)}}},60136:function(e,t,r){r.d(t,{Z:function(){return _inherits}});var o=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.Z)(e,t)}},1413:function(e,t,r){r.d(t,{Z:function(){return _objectSpread2}});var o=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}}}]);