@echo off
echo 🚀 Starting E-commerce Platform with Docker Compose...

REM Create necessary directories
if not exist "letsencrypt" mkdir letsencrypt

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker-compose down

REM Build and start all services
echo 🔨 Building and starting all services...
docker-compose up --build -d

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 30 /nobreak > nul

REM Run database migrations and seeding
echo 🗄️ Setting up database...
docker-compose exec api-rest npm run db:migrate
docker-compose exec api-rest node simple-seed.js

echo ✅ Setup complete!
echo.
echo 🌐 Access your services:
echo    • Shop Frontend: http://shop.localhost
echo    • Admin Panel: http://admin.localhost
echo    • API Documentation: http://api.localhost/docs
echo    • MinIO Console: http://minio-console.localhost
echo    • MinIO API: http://minio.localhost
echo    • Traefik Dashboard: http://traefik.localhost:8080
echo.
echo 📝 Default credentials:
echo    • Admin: <EMAIL> / password
echo    • MinIO: minioadmin / minioadmin123
echo.
echo 💡 If services are not accessible, add these entries to your hosts file:
echo    127.0.0.1 shop.localhost
echo    127.0.0.1 admin.localhost
echo    127.0.0.1 api.localhost
echo    127.0.0.1 minio.localhost
echo    127.0.0.1 minio-console.localhost
echo    127.0.0.1 traefik.localhost

pause
