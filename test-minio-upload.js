const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

async function testMinioUpload() {
  console.log('🧪 Testing MinIO file upload integration...');

  // Create a test file
  const testFilePath = path.join(__dirname, 'test-image.txt');
  const testContent = 'This is a test file for MinIO upload testing.';
  fs.writeFileSync(testFilePath, testContent);

  try {
    // Create form data
    const form = new FormData();
    form.append('attachment[]', fs.createReadStream(testFilePath), {
      filename: 'test-image.txt',
      contentType: 'text/plain'
    });

    // Test upload to API
    console.log('📤 Uploading test file...');
    const response = await axios.post('http://localhost:5000/api/attachments', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 10000,
    });

    console.log('✅ Upload successful!');
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));

    // Test if the file is accessible
    if (response.data && response.data.length > 0) {
      const fileUrl = response.data[0].original;
      console.log('🔗 Testing file accessibility...');
      
      try {
        const fileResponse = await axios.get(fileUrl, { timeout: 5000 });
        console.log('✅ File is accessible via URL!');
        console.log('📊 File size:', fileResponse.data.length, 'bytes');
      } catch (error) {
        console.log('❌ File not accessible:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ Upload failed:', error.message);
    if (error.response) {
      console.error('📄 Error response:', error.response.data);
    }
  } finally {
    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
  }
}

// Run the test
testMinioUpload().catch(console.error);
