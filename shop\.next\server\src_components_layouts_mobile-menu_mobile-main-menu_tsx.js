"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_mobile-menu_mobile-main-menu_tsx";
exports.ids = ["src_components_layouts_mobile-menu_mobile-main-menu_tsx"];
exports.modules = {

/***/ "./src/components/layouts/mobile-menu/mobile-main-menu.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/layouts/mobile-menu/mobile-main-menu.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileMainMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/drawer/drawer-wrapper */ \"./src/components/ui/drawer/drawer-wrapper.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/site */ \"./src/config/site.ts\");\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_4__, _store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction MobileMainMenu() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [_, closeSidebar] = (0,jotai__WEBPACK_IMPORTED_MODULE_4__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_5__.drawerAtom);\n    const { headerLinks } = _config_site__WEBPACK_IMPORTED_MODULE_6__.siteSettings;\n    // function handleClick(path: string) {\n    //   router.push(path);\n    //   closeSidebar({ display: false, view: '' });\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_drawer_drawer_wrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"grow\",\n            children: headerLinks?.map(({ href, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        href: href,\n                        className: \"flex items-center px-5 py-3 text-sm font-semibold capitalize transition duration-200 cursor-pointer text-heading hover:text-accent md:px-6\",\n                        title: t(label),\n                        onClick: ()=>closeSidebar({\n                                display: false,\n                                view: \"\"\n                            }),\n                        children: t(label)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, `${href}${label}`, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\mobile-menu\\\\mobile-main-menu.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21vYmlsZS1tZW51L21vYmlsZS1tYWluLW1lbnUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0M7QUFDTTtBQUNvQjtBQUNsQztBQUNpQjtBQUNKO0FBQ0w7QUFFekIsU0FBU087SUFDdEIsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1AsNERBQWNBLENBQUM7SUFDN0IsTUFBTVEsU0FBU1Qsc0RBQVNBO0lBQ3hCLE1BQU0sQ0FBQ1UsR0FBR0MsYUFBYSxHQUFHUiw4Q0FBT0EsQ0FBQ0MsMERBQVVBO0lBQzVDLE1BQU0sRUFBRVEsV0FBVyxFQUFFLEdBQUdQLHNEQUFZQTtJQUVwQyx1Q0FBdUM7SUFDdkMsdUJBQXVCO0lBQ3ZCLGdEQUFnRDtJQUNoRCxJQUFJO0lBRUoscUJBQ0UsOERBQUNILDRFQUFhQTtrQkFDWiw0RUFBQ1c7WUFBR0MsV0FBVTtzQkFDWEYsYUFBYUcsSUFBSSxDQUFDLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLGlCQUNoQyw4REFBQ0M7OEJBQ0MsNEVBQUNaLDJEQUFJQTt3QkFDSFUsTUFBTUE7d0JBQ05GLFdBQVU7d0JBQ1ZLLE9BQU9YLEVBQUVTO3dCQUNURyxTQUFTLElBQU1ULGFBQWE7Z0NBQUVVLFNBQVM7Z0NBQU9DLE1BQU07NEJBQUc7a0NBRXREZCxFQUFFUzs7Ozs7O21CQVBFLENBQUMsRUFBRUQsS0FBSyxFQUFFQyxNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7OztBQWNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21vYmlsZS1tZW51L21vYmlsZS1tYWluLW1lbnUudHN4PzI3ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XHJcbmltcG9ydCBEcmF3ZXJXcmFwcGVyIGZyb20gJ0AvY29tcG9uZW50cy91aS9kcmF3ZXIvZHJhd2VyLXdyYXBwZXInO1xyXG5pbXBvcnQgeyB1c2VBdG9tIH0gZnJvbSAnam90YWknO1xyXG5pbXBvcnQgeyBkcmF3ZXJBdG9tIH0gZnJvbSAnQC9zdG9yZS9kcmF3ZXItYXRvbSc7XHJcbmltcG9ydCB7IHNpdGVTZXR0aW5ncyB9IGZyb20gJ0AvY29uZmlnL3NpdGUnO1xyXG5pbXBvcnQgTGluayBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGluayc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNb2JpbGVNYWluTWVudSgpIHtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBbXywgY2xvc2VTaWRlYmFyXSA9IHVzZUF0b20oZHJhd2VyQXRvbSk7XHJcbiAgY29uc3QgeyBoZWFkZXJMaW5rcyB9ID0gc2l0ZVNldHRpbmdzO1xyXG5cclxuICAvLyBmdW5jdGlvbiBoYW5kbGVDbGljayhwYXRoOiBzdHJpbmcpIHtcclxuICAvLyAgIHJvdXRlci5wdXNoKHBhdGgpO1xyXG4gIC8vICAgY2xvc2VTaWRlYmFyKHsgZGlzcGxheTogZmFsc2UsIHZpZXc6ICcnIH0pO1xyXG4gIC8vIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxEcmF3ZXJXcmFwcGVyPlxyXG4gICAgICA8dWwgY2xhc3NOYW1lPVwiZ3Jvd1wiPlxyXG4gICAgICAgIHtoZWFkZXJMaW5rcz8ubWFwKCh7IGhyZWYsIGxhYmVsIH0pID0+IChcclxuICAgICAgICAgIDxsaSBrZXk9e2Ake2hyZWZ9JHtsYWJlbH1gfT5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPXtocmVmfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTUgcHktMyB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgY2FwaXRhbGl6ZSB0cmFuc2l0aW9uIGR1cmF0aW9uLTIwMCBjdXJzb3ItcG9pbnRlciB0ZXh0LWhlYWRpbmcgaG92ZXI6dGV4dC1hY2NlbnQgbWQ6cHgtNlwiXHJcbiAgICAgICAgICAgICAgdGl0bGU9e3QobGFiZWwpfVxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNsb3NlU2lkZWJhcih7IGRpc3BsYXk6IGZhbHNlLCB2aWV3OiAnJyB9KX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KGxhYmVsKX1cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9saT5cclxuICAgICAgICApKX1cclxuICAgICAgPC91bD5cclxuICAgIDwvRHJhd2VyV3JhcHBlcj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiLCJ1c2VUcmFuc2xhdGlvbiIsIkRyYXdlcldyYXBwZXIiLCJ1c2VBdG9tIiwiZHJhd2VyQXRvbSIsInNpdGVTZXR0aW5ncyIsIkxpbmsiLCJNb2JpbGVNYWluTWVudSIsInQiLCJyb3V0ZXIiLCJfIiwiY2xvc2VTaWRlYmFyIiwiaGVhZGVyTGlua3MiLCJ1bCIsImNsYXNzTmFtZSIsIm1hcCIsImhyZWYiLCJsYWJlbCIsImxpIiwidGl0bGUiLCJvbkNsaWNrIiwiZGlzcGxheSIsInZpZXciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/layouts/mobile-menu/mobile-main-menu.tsx\n");

/***/ }),

/***/ "./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteSettings: () => (/* binding */ siteSettings)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n\nconst siteSettings = {\n    name: \"PickBazar\",\n    description: \"\",\n    logo: {\n        url: \"/logo.svg\",\n        alt: \"PickBazar\",\n        href: \"/grocery\",\n        width: 128,\n        height: 40\n    },\n    defaultLanguage: \"en\",\n    currencyCode: \"USD\",\n    product: {\n        placeholderImage: \"/product-placeholder.svg\",\n        cardMaps: {\n            grocery: \"Krypton\",\n            furniture: \"Radon\",\n            bag: \"Oganesson\",\n            makeup: \"Neon\",\n            book: \"Xenon\",\n            medicine: \"Helium\",\n            default: \"Argon\"\n        }\n    },\n    authorizedLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        }\n    ],\n    authorizedLinksMobile: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"auth-menu-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"auth-menu-my-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.checkout,\n            label: \"auth-menu-checkout\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        }\n    ],\n    dashboardSidebarMenu: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.profile,\n            label: \"profile-sidebar-profile\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.changePassword,\n            label: \"profile-sidebar-password\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.notifyLogs,\n            label: \"profile-sidebar-notifications\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.cards,\n            label: \"profile-sidebar-my-cards\",\n            // MultiPayment: Make it dynamic or from mapper\n            cardsPayment: [\n                _types__WEBPACK_IMPORTED_MODULE_1__.PaymentGateway.STRIPE\n            ]\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.orders,\n            label: \"profile-sidebar-orders\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.downloads,\n            label: \"profile-sidebar-downloads\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.wishlists,\n            label: \"profile-sidebar-my-wishlist\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.questions,\n            label: \"profile-sidebar-my-questions\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.refunds,\n            label: \"text-my-refunds\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.reports,\n            label: \"profile-sidebar-my-reports\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"profile-sidebar-help\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.logout,\n            label: \"profile-sidebar-logout\"\n        }\n    ],\n    sellingAdvertisement: {\n        image: {\n            src: \"/selling.png\",\n            alt: \"Selling Advertisement\"\n        }\n    },\n    cta: {\n        mockup_img_src: \"/mockup-img.png\",\n        play_store_link: \"/\",\n        app_store_link: \"/\"\n    },\n    headerLinks: [\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops,\n            icon: null,\n            label: \"nav-menu-shops\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons,\n            icon: null,\n            label: \"nav-menu-offer\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs,\n            label: \"nav-menu-contact\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.becomeSeller,\n            label: \"Become a seller\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.flashSale,\n            label: \"nav-menu-flash-sale\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.manufacturers,\n            label: \"text-manufacturers\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors,\n            label: \"text-authors\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help,\n            label: \"nav-menu-faq\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms,\n            label: \"nav-menu-terms\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies,\n            label: \"nav-menu-refund-policy\"\n        },\n        {\n            href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies,\n            label: \"nav-menu-vendor-refund-policy\"\n        }\n    ],\n    footer: {\n        // copyright: {\n        //   name: 'RedQ, Inc',\n        //   href: 'https://redq.io/',\n        // },\n        // address: '2429 River Drive, Suite 35 Cottonhall, CA 2296 United Kingdom',\n        // email: '<EMAIL>',\n        // phone: '******-698-0694',\n        menus: [\n            {\n                title: \"text-explore\",\n                links: [\n                    {\n                        name: \"Shops\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shops\n                    },\n                    {\n                        name: \"Authors\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.authors\n                    },\n                    {\n                        name: \"Flash Deals\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.flashSale\n                    },\n                    {\n                        name: \"Coupon\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.coupons\n                    }\n                ]\n            },\n            {\n                title: \"text-customer-service\",\n                links: [\n                    {\n                        name: \"text-faq-help\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.help\n                    },\n                    {\n                        name: \"Vendor Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.vendorRefundPolicies\n                    },\n                    {\n                        name: \"Customer Refund Policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.customerRefundPolicies\n                    }\n                ]\n            },\n            {\n                title: \"text-our-information\",\n                links: [\n                    {\n                        name: \"Manufacturers\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes?.manufacturers\n                    },\n                    {\n                        name: \"Privacy policies\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.privacy\n                    },\n                    {\n                        name: \"text-terms-condition\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.terms\n                    },\n                    {\n                        name: \"text-contact-us\",\n                        href: _config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.contactUs\n                    }\n                ]\n            }\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/site.ts\n");

/***/ })

};
;