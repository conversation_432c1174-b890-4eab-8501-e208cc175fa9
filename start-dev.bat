@echo off
echo 🚀 Starting E-commerce Development Environment...
echo.

echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.simple.yml down

echo.
echo 🗄️ Starting backend services (Docker)...
docker-compose -f docker-compose.simple.yml up -d

echo.
echo ⏳ Waiting for services to be ready...
timeout /t 15 /nobreak > nul

echo.
echo 🌱 Seeding database...
docker-compose -f docker-compose.simple.yml exec api-rest node simple-seed.js

echo.
echo ✅ Backend services are ready!
echo.
echo 🌐 Backend Services:
echo    • API: http://localhost:5000/api
echo    • API Docs: http://localhost:5000/docs
echo    • MinIO Console: http://localhost:9001
echo    • MinIO API: http://localhost:9000
echo.
echo 🔐 MinIO Credentials:
echo    • Username: minioadmin
echo    • Password: minioadmin123
echo.
echo 🎯 Next Steps:
echo    1. Open new terminal: cd admin-rest ^&^& yarn dev
echo    2. Open new terminal: cd shop ^&^& yarn dev:rest
echo.
echo 💡 Frontend URLs (after starting):
echo    • Admin Panel: http://localhost:3002
echo    • Shop: http://localhost:3003
echo.

pause
