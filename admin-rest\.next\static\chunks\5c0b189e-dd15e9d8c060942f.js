"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6342],{93075:function(e,t,n){let r;n.d(t,{KK:function(){return useFocus},NI:function(){return useInteractions},XI:function(){return useHover},Y$:function(){return y},YF:function(){return useFloating},Y_:function(){return useTransitionStyles},bQ:function(){return useDismiss},eS:function(){return useClick},ll:function(){return FloatingPortal},qs:function(){return useRole}});var o,u=n(67294),l=n(97145),i=n(37317),c=n(88388),s=n(73935),a=n(82364);let f={...o||(o=n.t(u,2))},d=f.useInsertionEffect,v=d||(e=>e());function useEffectEvent(e){let t=u.useRef(()=>{});return v(()=>{t.current=e}),u.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}var m="undefined"!=typeof document?u.useLayoutEffect:u.useEffect;function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}let p=!1,E=0,genId=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+E++,g=f.useId,b=g||function(){let[e,t]=u.useState(()=>p?genId():void 0);return m(()=>{null==e&&t(genId())},[]),u.useEffect(()=>{p=!0},[]),e},y=u.forwardRef(function(e,t){let{context:{placement:n,elements:{floating:r},middlewareData:{arrow:o,shift:l}},width:c=14,height:s=7,tipRadius:a=0,strokeWidth:f=0,staticOffset:d,stroke:v,d:p,style:{transform:E,...g}={},...y}=e,h=b(),[R,k]=u.useState(!1);if(m(()=>{if(!r)return;let e="rtl"===(0,i.Dx)(r).direction;e&&k(!0)},[r]),!r)return null;let[x,w]=n.split("-"),C="top"===x||"bottom"===x,M=d;(C&&null!=l&&l.x||!C&&null!=l&&l.y)&&(M=null);let L=2*f,T=L/2,P=c/2*(-(a/8)+1),O=s/2*a/4,A=!!p,K=M&&"end"===w?"bottom":"top",S=M&&"end"===w?"right":"left";M&&R&&(S="end"===w?"left":"right");let F=(null==o?void 0:o.x)!=null?M||o.x:"",D=(null==o?void 0:o.y)!=null?M||o.y:"",I=p||"M0,0 H"+c+" L"+(c-P)+","+(s-O)+(" Q"+c/2+","+s+" ")+P+","+(s-O)+" Z";return u.createElement("svg",_extends({},y,{"aria-hidden":!0,ref:t,width:A?c:c+L,height:c,viewBox:"0 0 "+c+" "+(s>c?s:c),style:{position:"absolute",pointerEvents:"none",[S]:F,[K]:D,[x]:C||A?"100%":"calc(100% - "+L/2+"px)",transform:[{top:A?"rotate(180deg)":"",left:A?"rotate(90deg)":"rotate(-90deg)",bottom:A?"":"rotate(180deg)",right:A?"rotate(-90deg)":"rotate(90deg)"}[x],E].filter(e=>!!e).join(" "),...g}}),L>0&&u.createElement("path",{clipPath:"url(#"+h+")",fill:"none",stroke:v,strokeWidth:L+(p?0:1),d:I}),u.createElement("path",{stroke:L&&!p?y.fill:"none",d:I}),u.createElement("clipPath",{id:h},u.createElement("rect",{x:-T,y:T*(A?-1:1),width:c+L,height:c})))}),h=u.createContext(null),R=u.createContext(null),useFloatingParentNodeId=()=>{var e;return(null==(e=u.useContext(h))?void 0:e.id)||null},useFloatingTree=()=>u.useContext(R);function createAttribute(e){return"data-floating-ui-"+e}function useLatestRef(e){let t=(0,u.useRef)(e);return m(()=>{t.current=e}),t}let k=createAttribute("safe-polygon");function getDelay(e,t,n){return n&&!(0,l.r)(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function useHover(e,t){void 0===t&&(t={});let{open:n,onOpenChange:r,dataRef:o,events:c,elements:s}=e,{enabled:a=!0,delay:f=0,handleClose:d=null,mouseOnly:v=!1,restMs:p=0,move:E=!0}=t,g=useFloatingTree(),b=useFloatingParentNodeId(),y=useLatestRef(d),h=useLatestRef(f),R=useLatestRef(n),x=u.useRef(),w=u.useRef(-1),C=u.useRef(),M=u.useRef(-1),L=u.useRef(!0),T=u.useRef(!1),P=u.useRef(()=>{}),O=u.useRef(!1),A=u.useCallback(()=>{var e;let t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[o]);u.useEffect(()=>{if(a)return c.on("openchange",onOpenChange),()=>{c.off("openchange",onOpenChange)};function onOpenChange(e){let{open:t}=e;t||(clearTimeout(w.current),clearTimeout(M.current),L.current=!0,O.current=!1)}},[a,c]),u.useEffect(()=>{if(!a||!y.current||!n)return;function onLeave(e){A()&&r(!1,e,"hover")}let e=(0,l.Me)(s.floating).documentElement;return e.addEventListener("mouseleave",onLeave),()=>{e.removeEventListener("mouseleave",onLeave)}},[s.floating,n,r,a,y,A]);let K=u.useCallback(function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");let o=getDelay(h.current,"close",x.current);o&&!C.current?(clearTimeout(w.current),w.current=window.setTimeout(()=>r(!1,e,n),o)):t&&(clearTimeout(w.current),r(!1,e,n))},[h,r]),S=useEffectEvent(()=>{P.current(),C.current=void 0}),F=useEffectEvent(()=>{if(T.current){let e=(0,l.Me)(s.floating).body;e.style.pointerEvents="",e.removeAttribute(k),T.current=!1}});u.useEffect(()=>{if(a&&(0,i.kK)(s.domReference)){var e;let t=s.domReference;return n&&t.addEventListener("mouseleave",onScrollMouseLeave),null==(e=s.floating)||e.addEventListener("mouseleave",onScrollMouseLeave),E&&t.addEventListener("mousemove",onMouseEnter,{once:!0}),t.addEventListener("mouseenter",onMouseEnter),t.addEventListener("mouseleave",onMouseLeave),()=>{var e;n&&t.removeEventListener("mouseleave",onScrollMouseLeave),null==(e=s.floating)||e.removeEventListener("mouseleave",onScrollMouseLeave),E&&t.removeEventListener("mousemove",onMouseEnter),t.removeEventListener("mouseenter",onMouseEnter),t.removeEventListener("mouseleave",onMouseLeave)}}function isClickLikeOpenEvent(){return!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)}function onMouseEnter(e){if(clearTimeout(w.current),L.current=!1,v&&!(0,l.r)(x.current)||p>0&&!getDelay(h.current,"open"))return;let t=getDelay(h.current,"open",x.current);t?w.current=window.setTimeout(()=>{R.current||r(!0,e,"hover")},t):r(!0,e,"hover")}function onMouseLeave(e){if(isClickLikeOpenEvent())return;P.current();let t=(0,l.Me)(s.floating);if(clearTimeout(M.current),O.current=!1,y.current&&o.current.floatingContext){n||clearTimeout(w.current),C.current=y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){F(),S(),K(e,!0,"safe-polygon")}});let r=C.current;t.addEventListener("mousemove",r),P.current=()=>{t.removeEventListener("mousemove",r)};return}let r="touch"!==x.current||!(0,l.r3)(s.floating,e.relatedTarget);r&&K(e)}function onScrollMouseLeave(e){!isClickLikeOpenEvent()&&o.current.floatingContext&&(null==y.current||y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){F(),S(),K(e)}})(e))}},[s,a,e,v,p,E,K,S,F,r,n,R,g,h,y,o]),m(()=>{var e,t;if(a&&n&&null!=(e=y.current)&&e.__options.blockPointerEvents&&A()){T.current=!0;let e=s.floating;if((0,i.kK)(s.domReference)&&e){let n=(0,l.Me)(s.floating).body;n.setAttribute(k,"");let r=s.domReference,o=null==g||null==(t=g.nodesRef.current.find(e=>e.id===b))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}},[a,n,b,s,g,y,A]),m(()=>{n||(x.current=void 0,O.current=!1,S(),F())},[n,S,F]),u.useEffect(()=>()=>{S(),clearTimeout(w.current),clearTimeout(M.current),F()},[a,s.domReference,S,F]);let D=u.useMemo(()=>{function setPointerRef(e){x.current=e.pointerType}return{onPointerDown:setPointerRef,onPointerEnter:setPointerRef,onMouseMove(e){let{nativeEvent:t}=e;function handleMouseMove(){L.current||R.current||r(!0,t,"hover")}!(!v||(0,l.r)(x.current))||n||0===p||O.current&&e.movementX**2+e.movementY**2<2||(clearTimeout(M.current),"touch"===x.current?handleMouseMove():(O.current=!0,M.current=window.setTimeout(handleMouseMove,p)))}}},[v,r,n,R,p]),I=u.useMemo(()=>({onMouseEnter(){clearTimeout(w.current)},onMouseLeave(e){K(e.nativeEvent,!1)}}),[K]);return u.useMemo(()=>a?{reference:D,floating:I}:{},[a,D,I])}function getChildren(e,t){let n=e.filter(e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}),r=n;for(;r.length;)r=e.filter(e=>{var t;return null==(t=r)?void 0:t.some(t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)})}),n=n.concat(r);return n}let getTabbableOptions=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function getTabbableIn(e,t){let n=(0,c.ht)(e,getTabbableOptions());"prev"===t&&n.reverse();let r=n.indexOf((0,l.AW)((0,l.Me)(e))),o=n.slice(r+1);return o[0]}function isOutsideEvent(e,t){let n=t||e.currentTarget,r=e.relatedTarget;return!r||!(0,l.r3)(n,r)}function enableFocusInside(e){let t=e.querySelectorAll("[data-tabindex]");t.forEach(e=>{let t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")})}let x={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};function setActiveElementOnTab(e){"Tab"===e.key&&(e.target,clearTimeout(r))}let w=u.forwardRef(function(e,t){let[n,r]=u.useState();m(()=>((0,l.G6)()&&r("button"),document.addEventListener("keydown",setActiveElementOnTab),()=>{document.removeEventListener("keydown",setActiveElementOnTab)}),[]);let o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[createAttribute("focus-guard")]:"",style:x};return u.createElement("span",_extends({},e,o))}),C=u.createContext(null),M=createAttribute("portal");function FloatingPortal(e){let{children:t,id:n,root:r=null,preserveTabOrder:o=!0}=e,l=function(e){void 0===e&&(e={});let{id:t,root:n}=e,r=b(),o=usePortalContext(),[l,c]=u.useState(null),s=u.useRef(null);return m(()=>()=>{null==l||l.remove(),queueMicrotask(()=>{s.current=null})},[l]),m(()=>{if(!r||s.current)return;let e=t?document.getElementById(t):null;if(!e)return;let n=document.createElement("div");n.id=r,n.setAttribute(M,""),e.appendChild(n),s.current=n,c(n)},[t,r]),m(()=>{if(!r||s.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!(0,i.kK)(e)&&(e=e.current),e=e||document.body;let u=null;t&&((u=document.createElement("div")).id=t,e.appendChild(u));let l=document.createElement("div");l.id=r,l.setAttribute(M,""),(e=u||e).appendChild(l),s.current=l,c(l)},[t,n,r,o]),l}({id:n,root:r}),[a,f]=u.useState(null),d=u.useRef(null),v=u.useRef(null),p=u.useRef(null),E=u.useRef(null),g=null==a?void 0:a.modal,y=null==a?void 0:a.open,h=!!a&&!a.modal&&a.open&&o&&!!(r||l);return u.useEffect(()=>{if(l&&o&&!g)return l.addEventListener("focusin",onFocus,!0),l.addEventListener("focusout",onFocus,!0),()=>{l.removeEventListener("focusin",onFocus,!0),l.removeEventListener("focusout",onFocus,!0)};function onFocus(e){if(l&&isOutsideEvent(e)){let t="focusin"===e.type;(t?enableFocusInside:function(e){let t=(0,c.ht)(e,getTabbableOptions());t.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")})})(l)}}},[l,o,g]),u.useEffect(()=>{l&&(y||enableFocusInside(l))},[y,l]),u.createElement(C.Provider,{value:u.useMemo(()=>({preserveTabOrder:o,beforeOutsideRef:d,afterOutsideRef:v,beforeInsideRef:p,afterInsideRef:E,portalNode:l,setFocusManagerState:f}),[o,l])},h&&l&&u.createElement(w,{"data-type":"outside",ref:d,onFocus:e=>{if(isOutsideEvent(e,l)){var t;null==(t=p.current)||t.focus()}else{let e=getTabbableIn(document.body,"prev")||(null==a?void 0:a.refs.domReference.current);null==e||e.focus()}}}),h&&l&&u.createElement("span",{"aria-owns":l.id,style:x}),l&&s.createPortal(t,l),h&&l&&u.createElement(w,{"data-type":"outside",ref:v,onFocus:e=>{if(isOutsideEvent(e,l)){var t;null==(t=E.current)||t.focus()}else{let t=getTabbableIn(document.body,"next")||(null==a?void 0:a.refs.domReference.current);null==t||t.focus(),(null==a?void 0:a.closeOnFocusOut)&&(null==a||a.onOpenChange(!1,e.nativeEvent,"focus-out"))}}}))}let usePortalContext=()=>u.useContext(C);function isButtonTarget(e){return(0,i.Re)(e.target)&&"BUTTON"===e.target.tagName}function useClick(e,t){void 0===t&&(t={});let{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:c=!0,event:s="click",toggle:a=!0,ignoreMouse:f=!1,keyboardHandlers:d=!0}=t,v=u.useRef(),m=u.useRef(!1),p=u.useMemo(()=>({onPointerDown(e){v.current=e.pointerType},onMouseDown(e){let t=v.current;0===e.button&&"click"!==s&&((0,l.r)(t,!0)&&f||(n&&a&&(!o.current.openEvent||"mousedown"===o.current.openEvent.type)?r(!1,e.nativeEvent,"click"):(e.preventDefault(),r(!0,e.nativeEvent,"click"))))},onClick(e){let t=v.current;if("mousedown"===s&&v.current){v.current=void 0;return}(0,l.r)(t,!0)&&f||(n&&a&&(!o.current.openEvent||"click"===o.current.openEvent.type)?r(!1,e.nativeEvent,"click"):r(!0,e.nativeEvent,"click"))},onKeyDown(e){v.current=void 0,e.defaultPrevented||!d||isButtonTarget(e)||(" "!==e.key||(0,l.j7)(i)||(e.preventDefault(),m.current=!0),"Enter"===e.key&&(n&&a?r(!1,e.nativeEvent,"click"):r(!0,e.nativeEvent,"click")))},onKeyUp(e){!(e.defaultPrevented||!d||isButtonTarget(e)||(0,l.j7)(i))&&" "===e.key&&m.current&&(m.current=!1,n&&a?r(!1,e.nativeEvent,"click"):r(!0,e.nativeEvent,"click"))}}),[o,i,s,f,d,r,n,a]);return u.useMemo(()=>c?{reference:p}:{},[c,p])}let L={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},T={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},normalizeProp=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function useDismiss(e,t){void 0===t&&(t={});let{open:n,onOpenChange:r,elements:o,dataRef:c}=e,{enabled:s=!0,escapeKey:a=!0,outsidePress:f=!0,outsidePressEvent:d="pointerdown",referencePress:v=!1,referencePressEvent:m="pointerdown",ancestorScroll:p=!1,bubbles:E,capture:g}=t,b=useFloatingTree(),y=useEffectEvent("function"==typeof f?f:()=>!1),h="function"==typeof f?y:f,R=u.useRef(!1),k=u.useRef(!1),{escapeKey:x,outsidePress:w}=normalizeProp(E),{escapeKey:C,outsidePress:M}=normalizeProp(g),P=u.useRef(!1),O=useEffectEvent(e=>{var t;if(!n||!s||!a||"Escape"!==e.key||P.current)return;let o=null==(t=c.current.floatingContext)?void 0:t.nodeId,u=b?getChildren(b.nodesRef.current,o):[];if(!x&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach(t=>{var n;if(null!=(n=t.context)&&n.open&&!t.context.dataRef.current.__escapeKeyBubbles){e=!1;return}}),!e)return}r(!1,(0,l.MM)(e)?e.nativeEvent:e,"escape-key")}),A=useEffectEvent(e=>{var t;let callback=()=>{var t;O(e),null==(t=(0,l.U9)(e))||t.removeEventListener("keydown",callback)};null==(t=(0,l.U9)(e))||t.addEventListener("keydown",callback)}),K=useEffectEvent(e=>{var t;let n=R.current;R.current=!1;let u=k.current;if(k.current=!1,"click"===d&&u||n||"function"==typeof h&&!h(e))return;let s=(0,l.U9)(e),a="["+createAttribute("inert")+"]",f=(0,l.Me)(o.floating).querySelectorAll(a),v=(0,i.kK)(s)?s:null;for(;v&&!(0,i.Py)(v);){let e=(0,i.Ow)(v);if((0,i.Py)(e)||!(0,i.kK)(e))break;v=e}if(f.length&&(0,i.kK)(s)&&!(0,l.ex)(s)&&!(0,l.r3)(s,o.floating)&&Array.from(f).every(e=>!(0,l.r3)(v,e)))return;if((0,i.Re)(s)&&D){let t=s.clientWidth>0&&s.scrollWidth>s.clientWidth,n=s.clientHeight>0&&s.scrollHeight>s.clientHeight,r=n&&e.offsetX>s.clientWidth;if(n){let t="rtl"===(0,i.Dx)(s).direction;t&&(r=e.offsetX<=s.offsetWidth-s.clientWidth)}if(r||t&&e.offsetY>s.clientHeight)return}let m=null==(t=c.current.floatingContext)?void 0:t.nodeId,p=b&&getChildren(b.nodesRef.current,m).some(t=>{var n;return(0,l.Pe)(e,null==(n=t.context)?void 0:n.elements.floating)});if((0,l.Pe)(e,o.floating)||(0,l.Pe)(e,o.domReference)||p)return;let E=b?getChildren(b.nodesRef.current,m):[];if(E.length>0){let e=!0;if(E.forEach(t=>{var n;if(null!=(n=t.context)&&n.open&&!t.context.dataRef.current.__outsidePressBubbles){e=!1;return}}),!e)return}r(!1,e,"outside-press")}),S=useEffectEvent(e=>{var t;let callback=()=>{var t;K(e),null==(t=(0,l.U9)(e))||t.removeEventListener(d,callback)};null==(t=(0,l.U9)(e))||t.addEventListener(d,callback)});u.useEffect(()=>{if(!n||!s)return;c.current.__escapeKeyBubbles=x,c.current.__outsidePressBubbles=w;let e=-1;function onScroll(e){r(!1,e,"ancestor-scroll")}function handleCompositionStart(){window.clearTimeout(e),P.current=!0}function handleCompositionEnd(){e=window.setTimeout(()=>{P.current=!1},(0,i.Pf)()?5:0)}let t=(0,l.Me)(o.floating);a&&(t.addEventListener("keydown",C?A:O,C),t.addEventListener("compositionstart",handleCompositionStart),t.addEventListener("compositionend",handleCompositionEnd)),h&&t.addEventListener(d,M?S:K,M);let u=[];return p&&((0,i.kK)(o.domReference)&&(u=(0,i.Kx)(o.domReference)),(0,i.kK)(o.floating)&&(u=u.concat((0,i.Kx)(o.floating))),!(0,i.kK)(o.reference)&&o.reference&&o.reference.contextElement&&(u=u.concat((0,i.Kx)(o.reference.contextElement)))),(u=u.filter(e=>{var n;return e!==(null==(n=t.defaultView)?void 0:n.visualViewport)})).forEach(e=>{e.addEventListener("scroll",onScroll,{passive:!0})}),()=>{a&&(t.removeEventListener("keydown",C?A:O,C),t.removeEventListener("compositionstart",handleCompositionStart),t.removeEventListener("compositionend",handleCompositionEnd)),h&&t.removeEventListener(d,M?S:K,M),u.forEach(e=>{e.removeEventListener("scroll",onScroll)}),window.clearTimeout(e)}},[c,o,a,h,d,n,r,p,s,x,w,O,C,A,K,M,S]),u.useEffect(()=>{R.current=!1},[h,d]);let F=u.useMemo(()=>({onKeyDown:O,[L[m]]:e=>{v&&r(!1,e.nativeEvent,"reference-press")}}),[O,r,v,m]),D=u.useMemo(()=>({onKeyDown:O,onMouseDown(){k.current=!0},onMouseUp(){k.current=!0},[T[d]]:()=>{R.current=!0}}),[O,d]);return u.useMemo(()=>s?{reference:F,floating:D}:{},[s,F,D])}function useFloating(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:r}=e,o=b(),l=u.useRef({}),[i]=u.useState(()=>(function(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}})()),c=null!=useFloatingParentNodeId(),[s,a]=u.useState(r.reference),f=useEffectEvent((e,t,r)=>{l.current.openEvent=e?t:void 0,i.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)}),d=u.useMemo(()=>({setPositionReference:a}),[]),v=u.useMemo(()=>({reference:s||r.reference||null,floating:r.floating||null,domReference:r.reference}),[s,r.reference,r.floating]);return u.useMemo(()=>({dataRef:l,open:t,onOpenChange:f,elements:v,events:i,floatingId:o,refs:d}),[t,f,v,i,o,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,o=r.elements,[l,c]=u.useState(null),[s,f]=u.useState(null),d=null==o?void 0:o.reference,v=d||l,p=u.useRef(null),E=useFloatingTree();m(()=>{v&&(p.current=v)},[v]);let g=(0,a.YF)({...e,elements:{...o,...s&&{reference:s}}}),y=u.useCallback(e=>{let t=(0,i.kK)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;f(t),g.refs.setReference(t)},[g.refs]),h=u.useCallback(e=>{((0,i.kK)(e)||null===e)&&(p.current=e,c(e)),((0,i.kK)(g.refs.reference.current)||null===g.refs.reference.current||null!==e&&!(0,i.kK)(e))&&g.refs.setReference(e)},[g.refs]),R=u.useMemo(()=>({...g.refs,setReference:h,setPositionReference:y,domReference:p}),[g.refs,h,y]),k=u.useMemo(()=>({...g.elements,domReference:v}),[g.elements,v]),x=u.useMemo(()=>({...g,...r,refs:R,elements:k,nodeId:t}),[g,R,k,t,r]);return m(()=>{r.dataRef.current.floatingContext=x;let e=null==E?void 0:E.nodesRef.current.find(e=>e.id===t);e&&(e.context=x)}),u.useMemo(()=>({...g,context:x,refs:R,elements:k}),[g,R,k,x])}function useFocus(e,t){void 0===t&&(t={});let{open:n,onOpenChange:r,events:o,dataRef:c,elements:s}=e,{enabled:a=!0,visibleOnly:f=!0}=t,d=u.useRef(!1),v=u.useRef(),m=u.useRef(!0);u.useEffect(()=>{if(!a)return;let e=(0,i.Jj)(s.domReference);function onBlur(){!n&&(0,i.Re)(s.domReference)&&s.domReference===(0,l.AW)((0,l.Me)(s.domReference))&&(d.current=!0)}function onKeyDown(){m.current=!0}return e.addEventListener("blur",onBlur),e.addEventListener("keydown",onKeyDown,!0),()=>{e.removeEventListener("blur",onBlur),e.removeEventListener("keydown",onKeyDown,!0)}},[s.domReference,n,a]),u.useEffect(()=>{if(a)return o.on("openchange",onOpenChange),()=>{o.off("openchange",onOpenChange)};function onOpenChange(e){let{reason:t}=e;("reference-press"===t||"escape-key"===t)&&(d.current=!0)}},[o,a]),u.useEffect(()=>()=>{clearTimeout(v.current)},[]);let p=u.useMemo(()=>({onPointerDown(e){(0,l.cr)(e.nativeEvent)||(m.current=!1)},onMouseLeave(){d.current=!1},onFocus(e){if(d.current)return;let t=(0,l.U9)(e.nativeEvent);if(f&&(0,i.kK)(t))try{if((0,l.G6)()&&(0,l.V5)())throw Error();if(!t.matches(":focus-visible"))return}catch(e){if(!m.current&&!(0,l.j7)(t))return}r(!0,e.nativeEvent,"focus")},onBlur(e){d.current=!1;let t=e.relatedTarget,n=e.nativeEvent,o=(0,i.kK)(t)&&t.hasAttribute(createAttribute("focus-guard"))&&"outside"===t.getAttribute("data-type");v.current=window.setTimeout(()=>{var e;let u=(0,l.AW)(s.domReference?s.domReference.ownerDocument:document);if(t||u!==s.domReference){if((0,l.r3)(null==(e=c.current.floatingContext)?void 0:e.refs.floating.current,u)||(0,l.r3)(s.domReference,u)||o)return;r(!1,n,"focus")}})}}),[c,s.domReference,r,f]);return u.useMemo(()=>a?{reference:p}:{},[a,p])}let P="active",O="selected";function mergeProps(e,t,n){let r=new Map,o="item"===n,u=e;if(o&&e){let{[P]:t,[O]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,"data-floating-ui-focusable":""},...u,...t.map(t=>{let r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[n,u]=t;if(!(o&&[P,O].includes(n))){if(0===n.indexOf("on")){if(r.has(n)||r.set(n,[]),"function"==typeof u){var l;null==(l=r.get(n))||l.push(u),e[n]=function(){for(var e,t=arguments.length,o=Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map(e=>e(...o)).find(e=>void 0!==e)}}}else e[n]=u}}),e),{})}}function useInteractions(e){void 0===e&&(e=[]);let t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),o=u.useCallback(t=>mergeProps(t,e,"reference"),t),l=u.useCallback(t=>mergeProps(t,e,"floating"),n),i=u.useCallback(t=>mergeProps(t,e,"item"),r);return u.useMemo(()=>({getReferenceProps:o,getFloatingProps:l,getItemProps:i}),[o,l,i])}let A=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function useRole(e,t){var n;void 0===t&&(t={});let{open:r,floatingId:o}=e,{enabled:l=!0,role:i="dialog"}=t,c=null!=(n=A.get(i))?n:i,s=b(),a=useFloatingParentNodeId(),f=null!=a,d=u.useMemo(()=>"tooltip"===c||"label"===i?{["aria-"+("label"===i?"labelledby":"describedby")]:r?o:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===c?"dialog":c,"aria-controls":r?o:void 0,..."listbox"===c&&{role:"combobox"},..."menu"===c&&{id:s},..."menu"===c&&f&&{role:"menuitem"},..."select"===i&&{"aria-autocomplete":"none"},..."combobox"===i&&{"aria-autocomplete":"list"}},[c,o,f,r,s,i]),v=u.useMemo(()=>{let e={id:o,...c&&{role:c}};return"tooltip"===c||"label"===i?e:{...e,..."menu"===c&&{"aria-labelledby":s}}},[c,o,s,i]),m=u.useCallback(e=>{let{active:t,selected:n}=e,r={role:"option",...t&&{id:o+"-option"}};switch(i){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}},[o,i]);return u.useMemo(()=>l?{reference:d,floating:v,item:m}:{},[l,d,v,m])}let camelCaseToKebabCase=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,(e,t)=>(t?"-":"")+e.toLowerCase());function execWithArgsOrReturn(e,t){return"function"==typeof e?e(t):e}function useTransitionStyles(e,t){void 0===t&&(t={});let{initial:n={opacity:0},open:r,close:o,common:l,duration:i=250}=t,c=e.placement,s=c.split("-")[0],a=u.useMemo(()=>({side:s,placement:c}),[s,c]),f="number"==typeof i,d=(f?i:i.open)||0,v=(f?i:i.close)||0,[p,E]=u.useState(()=>({...execWithArgsOrReturn(l,a),...execWithArgsOrReturn(n,a)})),{isMounted:g,status:b}=function(e,t){void 0===t&&(t={});let{open:n,elements:{floating:r}}=e,{duration:o=250}=t,l=("number"==typeof o?o:o.close)||0,[i,c]=u.useState("unmounted"),s=function(e,t){let[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect(()=>{if(!e&&n){let e=setTimeout(()=>r(!1),t);return()=>clearTimeout(e)}},[e,n,t]),n}(n,l);return s||"close"!==i||c("unmounted"),m(()=>{if(r){if(n){c("initial");let e=requestAnimationFrame(()=>{c("open")});return()=>{cancelAnimationFrame(e)}}c("close")}},[n,r]),{isMounted:s,status:i}}(e,{duration:i}),y=useLatestRef(n),h=useLatestRef(r),R=useLatestRef(o),k=useLatestRef(l);return m(()=>{let e=execWithArgsOrReturn(y.current,a),t=execWithArgsOrReturn(R.current,a),n=execWithArgsOrReturn(k.current,a),r=execWithArgsOrReturn(h.current,a)||Object.keys(e).reduce((e,t)=>(e[t]="",e),{});if("initial"===b&&E(t=>({transitionProperty:t.transitionProperty,...n,...e})),"open"===b&&E({transitionProperty:Object.keys(r).map(camelCaseToKebabCase).join(","),transitionDuration:d+"ms",...n,...r}),"close"===b){let r=t||e;E({transitionProperty:Object.keys(r).map(camelCaseToKebabCase).join(","),transitionDuration:v+"ms",...n,...r})}},[v,R,y,h,k,d,b,a]),{isMounted:g,styles:p}}}}]);