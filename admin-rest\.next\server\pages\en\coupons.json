{"pageProps": {"_nextI18Next": {"initialI18nStore": {"en": {"form": {"form-title-permission": "Permission", "form-title-create-product": "Create New Product", "form-title-edit-product": "Edit Product", "form-title-edit-faq": "Edit FAQ", "form-title-edit-shop": "Edit Shop", "form-title-create-type": "Create New Group", "form-title-product-type": "Product Type", "form-description-product-type": "Select product type form here", "form-title-simple-product-info": "Simple Product Information", "form-description-simple-product-info": "your simple product description and necessary information from here", "form-title-variation-product-info": "Product Variation Information", "form-description-variation-product-info": "your product variation and necessary information from here", "form-description-attribute-value": "your attribute value and necessary information from here", "form-description-attribute-name": "your attribute name and necessary information from here", "form-title-options": "Options", "form-title-variation-added": "Variations Added", "form-title-variant": "<PERSON><PERSON><PERSON>", "form-title-edit-type": "Edit Group", "form-title-create-category": "Create New Category", "form-title-edit-category": "Edit Category", "form-title-create-order-status": "Create Order Status", "form-title-edit-order-status": "Edit Order Status", "form-title-create-customer": "Create New Customer", "form-title-update-customer": "Update Customer", "form-title-create-coupon": "Create New Coupon", "form-title-edit-coupon": "Edit Coupon", "form-title-create-tax": "Create New Tax", "form-title-update-tax": "Update Tax", "form-title-create-shipping": "Create New Shipping", "form-title-update-shipping": "Update Shipping", "form-title-transactions": "Transactions", "form-title-create-flash-sale": "Create flash sale schedule", "form-title-flash-sale-campaigns": "Flash Sale campaigns", "form-title-currently-flash-sales": "Currently on going flash sales.", "form-title-my-products-flash-sales": "My products on going flash sales.", "form-title-do-you-approve": "Do you really want to approve ?", "form-title-settings": "Settings", "form-title-seo-settings": "SEO Settings", "form-title-payment-settings": "Payment Settings", "form-title-events-settings": "Events Settings", "form-title-forgot-password": "Forgot Password", "form-title-profile-settings": "Profile Settings", "form-title-faqs": "FAQs", "form-title-all-notifications": "All notifications", "order-status-description-helper-text": "order status from here", "featured-image-title": "Featured Image", "featured-image-help-text": "Upload your product featured image here", "gallery-title": "Gallery", "gallery-help-text": "Upload your product image gallery here", "type-and-category": "Group & Categories", "type-and-category-help-text": "Select product group and categories from here", "item-description": "Description", "item-description-update": "Update", "item-description-choose": "<PERSON><PERSON>", "item-description-edit": "Edit", "item-description-add": "Add", "product-description-help-text": "your product description and necessary information from here", "total-variation-added": "<PERSON><PERSON><PERSON>", "input-label-enable-free-shipping": "Enable Free Shipping", "input-label-attribute-name": "Attribute Name", "input-label-attribute-value": "Attribute Value", "input-label-disable-variant": "Disable This Variant", "input-label-logo": "Logo", "input-label-collapse-logo": "Collapse Logo", "input-label-email": "Email", "input-label-token": "Put your token you got from email", "input-label-password": "Password", "input-label-bio": "Bio", "input-label-contact": "Contact Number", "input-label-avatar": "Avatar", "input-label-old-password": "Old Password", "input-label-new-password": "New Password", "input-label-confirm-password": "Confirm Password", "input-label-products": "Products", "input-label-inventory": "Inventory", "input-label-categories": "Categories", "input-label-coupons": "Coupons", "input-label-orders": "Orders", "input-label-order-id": "Order ID", "input-label-sku": "SKU", "input-note-multilang-sku": "Make sure SKU is identical for all languages.", "input-label-name": "Name", "input-label-description": "Description", "input-label-price": "Price", "input-label-sale-price": "Sale Price", "input-label-quantity": "Quantity", "input-label-unit": "Unit", "input-label-width": "<PERSON><PERSON><PERSON>", "input-label-height": "Height", "input-label-length": "Length", "input-label-status": "Status", "input-label-under-review": "Under Review", "input-label-published": "Published", "input-label-unpublish": "Unpublish", "input-label-draft": "Draft", "input-label-approved": "Approved", "input-label-rejected": "Rejected", "input-label-select-icon": "Select Icon", "input-label-details": "Details", "input-label-offering-campaign": "Select which type of offering is applicable in this campaign", "input-label-offering-campaign-filter-option": "Select products filter option.", "input-label-offering-campaign-choose-products": "Choose Products", "input-label-offering-campaign-choose-details": "Select all the products you want to set for this flash sale campaign. You can see details from the campaign list", "input-label-group": "Group", "input-label-slug": "Slug", "input-label-output": "Output", "input-label-type": "Type", "input-label-prompt": "Prompt", "input-label-types": "Types", "input-label-parent-category": "Parent Category", "input-label-order-status": "Order Status", "input-label-serial": "Serial", "input-label-code": "Code", "input-label-amount": "Amount", "input-label-minimum-cart-amount": "Minimum cart amount", "input-label-serial-help-text": "The order status should follow(ex: 1-[9])", "input-label-color": "Color", "button-label-sync-content": "Sync Content", "button-label-back": "Back", "input-label-image": "Image", "input-label-search": "Search", "input-label-taxes": "Taxes", "input-label-rate": "Rate", "input-label-country": "Country", "input-label-city": "City", "input-label-state": "State", "input-label-zip": "ZIP", "input-label-shippings": "Shippings", "input-label-free": "Free", "input-label-fixed": "Fixed", "input-label-site-title": "Site Title", "input-label-site-subtitle": "Site Subtitle", "input-label-site-link": "Site Link", "input-label-currency": "<PERSON><PERSON><PERSON><PERSON>", "input-label-tax-class": "Tax Class", "input-label-shipping-class": "Shipping Class", "input-label-free-shipping": "Free Shipping", "input-label-percentage": "Percentage", "input-label-meta": "Meta", "input-label-meta-title": "Meta Title", "input-label-meta-description": "Meta Description", "input-label-meta-tags": "Meta Tags", "input-label-canonical-url": "Canonical URL", "input-label-og-title": "OG Title", "input-label-og-description": "OG Description", "input-label-og-image": "OG Image", "input-label-twitter-handle": "Twitter Handle", "input-label-twitter-card-type": "Twitter Card Type", "button-label-update-product": "Update Product", "button-label-add-product": "Add Product", "input-placeholder-prompt-suggestion": "Please Select Some auto generated Prompt suggestion", "input-placeholder-search": "Type your query and press enter", "input-placeholder-order-status": "Order status", "input-placeholder-search-deals": "Search deals...", "button-label-remove": "Remove", "button-label-add-option": "Add an option", "button-label-add": "Add", "button-label-update": "Update", "button-label-shipping": "Shipping", "button-label-order-status": "Order Status", "button-label-add-type": "Add Type", "button-label-add-types": "Add Types", "button-label-generate-ai": "Generate With AI", "button-label-regenerate-ai": "Regenerate With AI", "button-label-add-group": "Add Group", "button-label-add-categories": "Add Categories", "button-label-add-order-status": "Add Order Status", "button-label-update-group": "Update Group", "button-label-update-terms-conditions": "Update Terms & Conditions", "button-label-add-terms-conditions": "Add Terms & Conditions", "group-description-help-text": "your group description and necessary information from here", "button-label-add-category": "Add Category", "button-label-update-category": "Update Category", "category-image-helper-text": "Upload your category image here", "category-description-helper-text": "your category details and necessary information from here", "button-label-change": "Change", "button-label-change-status": "Change Status", "input-label-users": "Users", "input-label-customers": "Customers", "input-label-staffs": "Staffs", "button-label-add-user": "Add User", "button-label-add-customer": "Add Customer", "button-label-login": "<PERSON><PERSON>", "button-label-add-coupon": "Add Coupon", "button-label-update-coupon": "Update Coupon", "input-forgot-password-label": "Forgot password?", "button-label-create-customer": "Create Customer", "button-label-preview-product-on-shop": "Preview", "form-title-information": "Information", "general-setting-title-information": "Basic settings", "form-title-payment": "Payment", "payment-help-text": "Configure Payment Option", "customer-form-info-help-text": "Add your customer information and create a new customer from here", "coupon-image-helper-text": "Upload your coupon image here", "coupon-form-info-help-text": "your coupon description and necessary information from here", "shipping-form-info-help-text": "your shipping description and necessary information from here", "coupon-active-from": "Active From", "coupon-expire-at": "Will Expire", "button-label-tax": "Tax", "button-label-add-tax": "Add Tax", "tax-form-info-help-text": "your tax information from here", "logo-help-text": "Upload your site logo from here.", "logo-collapse-help-text": "Upload your site collapse logo from here.", "logo-dimension-help-text": "Dimension of the logo should be", "site-info-help-text": "Change your site information from here", "form-title-footer-information": "Footer", "site-info-footer-description": "Change your Footer information from here", "input-label-copyright-text": "Copyright Text", "input-label-external-text": "External Text", "input-label-external-link": "External Link", "avatar-help-text": "Upload your profile image from here. Dimension of the avatar should be 140 x 140px", "button-label-save-settings": "Save Settings", "button-label-change-password": "Change Password", "button-label-save": "Save", "button-label-send": "Send", "password-help-text": "Change your password from here", "profile-info-help-text": "Add your profile information from here", "image-uploader-title": "Upload an image", "image-drag-n-drop-title": "or drag and drop", "tax-form-seo-info-help-text": "Change your site SEO from here", "create-new-attribute-value": "Create New Attribute Value", "create-new-attribute": "Create New Attribute", "update-attribute-value": "Update Attribute Value", "edit-attribute": "Edit Attribute", "error-message-required": "Message is required", "error-name-required": "Name is required", "error-value-required": "Value is required", "error-meta-required": "Meta is required", "error-author-name-required": "Author name is required", "error-manufacturer-name-required": "Manufacturer name is required", "error-attribute-name-required": "Attribute name is required", "error-invalid-coupon": "This coupon code is not valid", "text-payment-method-preparing": "Please wait payment method is preparing...", "text-register": "Register", "text-no-account": "Don't have any account?", "text-already-account": "Already have an account?", "text-customer": "Customer", "text-staff": "Staff", "text-store-owner": "Store Owner", "text-super-admin": "Super Admin", "button-label-add-tag": "Add Tag", "button-label-update-tag": "Update Tag", "button-label-add-withdraw": "Request Withdrawal", "button-label-update-withdraw": "Update <PERSON><PERSON><PERSON>", "form-title-create-withdraw": "C<PERSON> <PERSON><PERSON><PERSON>", "withdraw-description-helper-text": "With<PERSON>wal request from here", "input-label-cash-on-delivery": "Enable Cash On Delivery", "input-label-payment-method": "Payment Method", "input-label-vendor-id": "Vendor ID", "input-label-note": "Note", "form-title-create-tag": "Create Tag", "tag-image-helper-text": "Upload your tag image here", "tag-description-helper-text": "your tag details and necessary information from here", "form-title-create-shop": "Create Shop", "shop-logo-help-text": "Upload your shop logo from here", "shop-cover-image-title": "Cover Image", "shop-cover-image-help-text": "Upload your shop cover image from here", "cover-image-dimension-help-text": "Dimension of the cover image should be", "shop-basic-info": "Basic Info", "shop-basic-info-help-text": "Add some basic info about your shop from here", "shop-payment-info": "Payment Info", "payment-info-helper-text": "Add your payment information from here", "input-label-admin-commission-rate": "Commission rate for admin", "input-label-account-holder-name": "Account Holder Name", "input-label-account-holder-email": "Account Hold<PERSON>", "input-label-bank-name": "Bank Name", "input-label-account-number": "Account Number", "shop-address": "Shop Address", "shop-address-helper-text": "Add your physical shop address from here", "footer-address": "Address", "footer-address-helper-text": "Add your address from here", "email-change-helper-text": "Change your email from here", "input-label-street-address": "Street Address", "input-label-value": "Value", "button-label-add-value": "Add Value", "button-label-add-staff": "Add Staff", "form-title-create-staff": "Create Staff", "form-description-staff-info": "Add your staff information and create a new staff from here", "link-register-shop-owner": "Register as Shop Owner", "button-label-submit": "Submit", "error-token-required": "To<PERSON> is Required!", "error-old-password-required": "Old Password is Required!", "error-password-required": "Password is Required!", "error-confirm-password": "Please confirm password!", "error-match-passwords": "Passwords should match!", "error-credential-wrong": "The credentials was wrong!", "error-enough-permission": "Doesn't have enough permission", "error-email-format": "The provided email address format is not valid", "error-email-required": "You must need to provide your email address", "error-attribute-required": "Attribute is required", "error-type-required": "Group is required", "error-admin-commission": "You must need to set your commission rate", "error-coupon-code-required": "Code is required", "error-specify-number": "You must specify a number", "error-amount-number": "Amount should be a number", "error-amount-required": "You must set an amount", "error-payment-required": "You must set a Payment method", "error-select-single-products-required": "Please select a single products", "error-gateway-required": "Gateway Is Required", "error-bank-name-required": "Bank name required", "error-url-required": "URL is required", "error-invalid-url": "URL is not valid. e.g: https://exmple.io https://www.example.com", "error-url-valid-required": "URL is not valid", "error-website-required": "Website is required", "error-phone-number-required": "Phone number is required", "error-phone-number-valid-required": "Phone number is not valid", "error-contact-number-required": "Contact number is required", "error-contact-number-valid-required": "Contact number is not valid", "error-account-number-required": "Account number is required", "error-account-number-positive-required": "Account number must be positive", "error-account-number-integer-required": "Account number must be integer", "error-account-holder-email-required": "Account holder email required", "error-account-holder-name-required": "Account holder name require.", "error-amount-must-positive": "Amount must be positive", "error-amount-must-number": "Amount must be a number", "error-must-number": "Must be a number", "error-minimum-coupon-amount-number": "Minimum coupon amount should be a number", "error-minimum-coupon-amount-must-positive": "Minimum coupon amount must be positive", "error-coupon-amount-must-positive": "Coupon amount must be positive", "error-rate-must-positive": "Rate must be a positive", "error-rate-must-number": "Rate must be a number", "error-rate-must-integer": "Rate must be an integer", "error-rate-required": "Rate is required", "error-serial-must-positive": "Serial must be positive", "error-serial-must-integer": "Serial must be an integer", "error-serial-required": "Serial is required", "error-priority-required": "Priority is required", "error-faq-title-required": "FAQ title is required", "error-faq-description-required": "FAQ description is required", "error-notice-title-required": "Notice title is required", "error-notice-description-required": "Notice description is required", "error-expire-date-required": "You must set an expire date", "error-active-date-required": "You must set an active date", "error-color-required": "Color is required", "error-product-type-required": "Product type is required", "error-sku-required": "SKU is required", "error-price-must-number": "Price must be a number", "error-account-must-number": "Account must be a number", "error-price-must-positive": "Price must be positive", "error-price-required": "Price is required", "error-sale-price-less-number": "Sale Price should be less than", "error-sale-price-must-positive": "Sale price must be positive", "error-free-shipping-amount-must-positive": "Free shipping amount must be positive", "error-quantity-must-number": "Quantity must be a number", "error-quantity-must-positive": "Quantity must be positive", "error-quantity-must-integer": "Quantity must be integer", "error-quantity-required": "Quantity is required", "error-unit-required": "Unit is required", "error-status-required": "Status is required", "error-email-string": "Email must be string", "error-currency-required": "Currency is required", "text-submit-email": "Submit Email", "text-submit-token": "Submit Token", "text-reset-password": "Reset Password", "token-label": "Put your token you got from email", "input-label-autocomplete": "Set location from map", "input-label-website": "Website", "shop-settings": "Shop Settings", "shop-settings-helper-text": "Add your shop settings information from here", "button-label-add-social": "Add New Social Profile", "input-label-select-platform": "Select social platform", "input-label-social-url": "Add profile url", "placeholder-search-location": "Search location form here", "placeholder-type-message": "Type your message here..", "banner-slider-help-text": "Add your banner image with title and description from here. Dimension of the banner should be 1920 x 1080 px for full screen banner and 1500 x 450 px for small banner", "input-title": "Title", "input-description": "Description", "input-gallery": "Gallery", "button-label-add-banner": "Add Banner", "button-label-description-ai": "Generate Description With AI", "text-delivery-schedule": "Delivery Schedule", "delivery-schedule-help-text": "Add your delivery schedule time with proper description from here", "input-delivery-time-title": "Title/Time", "input-delivery-time-description": "Description", "button-label-add-delivery-time": "Add Delivery Time", "error-add-at-least-one-delivery-time": "Add at least one delivery time", "error-min-one-banner": "Add at least one banner", "error-title-required": "Title is required", "error-fractional-grater-then-one-required": "Fractional must be grater than 1", "error-fractional-not-grater-then-one-required": "Fractional number can not be grater than 5", "input-label-min-order-amount": "Minimum cart amount", "input-banner": "Banner", "input-label-product-card-type": "Select Product Card", "input-label-layout-type": "Select Layout", "input-label-is-home": "Is Main Homepage?", "promotional-slider": "Promotional Sliders", "promotional-slider-help-text": "Upload Promotional Sliders", "error-product-card-required": "Please select a product card", "error-product-one-required": "Please select at least one products", "group-settings": "Select group related settings", "group-settings-help-text": "Please make sure you selected the necessary settings", "input-label-add-wallet-points": "Add Wallet Points", "input-label-wallet-currency-ratio": "Wallet C<PERSON><PERSON><PERSON>", "input-label-signup-points": "Sign Up Points", "input-label-digital-file": "Digital File", "input-label-is-digital": "Is Digital", "input-label-is-external": "Is External", "input-label-external-product-url": "External Product URL", "input-label-external-product-button-text": "External Product Button Label", "form-title-additional-type": "Additional Product Type", "form-description-additional-type": "Choose additional product types from here. You can select only one type at a time. If you select one then you can't select another one. To select another one please unselect the selected one first.", "button-label-add-manufacturer-publication": "Add Manufacturer/Publication", "button-label-update-manufacturer-publication": "Update Manufacturer/Publication", "form-title-create-manufacturer": "Create Manufacturer/Publication", "form-title-update-manufacturer": "Update Manufacturer/Publication", "input-label-cover-image": "Cover Image", "manufacturer-form-info-help-text": "Cover Image", "manufacturer-form-description-details": "Add some information and manufacturer description from here.", "form-title-create-author": "Create Author", "form-title-update-author": "Update Author", "button-label-add-author": "Add Author", "author-form-description-details": "Add author's information and bio from here.", "input-label-languages": "Languages", "placeholder-add-languages-comma-separated": "Please add comma separated languages", "input-label-quote": "Quote", "input-label-author-born": "Born", "input-label-author-death": "Death", "button-label-update-author": "Update Author", "input-label-create-order": "Create Order", "author-image-helper-text": "Add author's profile image from here. Dimension should be 450 x 450 px.", "author-cover-image-helper-text": "Add author's cover image from here.", "manufacturer-image-helper-text": "Upload your Manufacturer/Publication logo from here. Dimension should be 160 x 160 px.", "manufacturer-cover-image-helper-text": "Upload your Manufacturer/Publication cover image from here, Dimension should be 960 x 340 px.", "text-upload-digital-file": "Upload a digital file from here or drag and drop", "form-title-edit-tags": "Edit Tags", "input-label-enable-otp": "Use OTP at checkout", "button-text-reply": "Reply", "input-answer-placeholder": "Type your answer here", "error-answer-required": "Answer should not be empty", "input-label-reviews": "Reviews", "error-maximum-question-limit": "Maximum question limit is required", "error-max-shop-distance": "Maximum Search Location Distance is required", "error-max-shop-distance-must-positive": "Maximum Search Location Distance is must be positive", "input-label-maximum-question-limit": "Maximum Question Limit", "error-digital-file-input-required": "You must need to upload your digital file", "input-label-store-notices": "Store Notices", "button-label-add-store-notices": "Add Store Notice", "form-title-create-store-notice": "Create Store Notice", "store-notice-active-from": "Active Date", "store-notice-expire-at": "Expire Date", "button-label-update-store-notice": "Update Store Notice", "button-label-add-store-notice": "Add Store Notice", "form-title-edit-store-notice": "Edit Store Notice", "store-notice-form-info-help-text": "Store Notice necessary information from here", "faq-form-info-help-text": "FAQ necessary information from here", "terms-conditions-form-info-help-text": "Terms and Condition necessary information from here", "input-label-terms-conditions-vendors": "Enable Terms & Conditions for vendors", "input-label-coupons-vendors": "Enable coupons for vendors", "input-label-priority": "Priority", "input-label-received-by": "Received By", "error-received-by-required": "Received By is required", "free-shipping-input-label-amount": "Minimum cart amount for free shipping", "coupon-input-label-amount": "Coupon discount amount", "form-notification-title": "Email Notification", "form-notification-description": "Set your email notification for messaging feature", "input-notification-email": "Notification email", "input-enable-notification": "Enable Notification", "input-label-use-google-map-service": "Enable Google Map Address", "input-label-product-for-review": "Enable product review system before publish ?", "input-label-use-must-verify-email": "Enable Must Verify Email", "input-label-soft-disabled": "Soft disabled", "currency-options-info-help-text": "The following options effect how prices are displayed on the frontend", "input-label-currency-formations": "Select Currency Formation", "input-label-currency-number-of-decimal": "Number of Factional Digit", "error-currency-thousand-separator-required": "Thousand separator is required", "error-currency-decimal-separator-required": "Decimal separator is required", "error-currency-number of decimals-required": "Number of decimals is required", "input-placeholder-currency-number-of-decimal": "Enter number Between 1-5", "error-fractions-must-positive": "Number of decimal must be positive", "error-fractions-must-be-number": "Number of decimal must be a number", "error-end-start-date": "End date has to be after than start date", "error-products-filter-option-required": "Products filter option field is required", "input-label-enable-open-ai": "Enable AI", "input-label-enable-guest-checkout": "Enable Guest Checkout", "video-title": "Video Title", "video-help-text": "Add video link", "input-label-video-embed": "Video Embed ", "button-label-add-video": "Add Video", "title-sms-event-settings": "SMS Event Setting", "title-email-event-settings": "Email Event Setting", "description-sms-event-settings": "Set This to Send SMS on Selected Event", "description-email-event-settings": "Set This to Send SMS on Selected Event", "input-label-sms-options": "Select SMS Options", "input-label-email-options": "Select Email Options", "size-help-text": "Image size should not be more than", "input-label-select-ai": "Select AI", "title-realtime-notification-settings": "Realtime notification setting", "description-realtime-notification-settings": "Set this to get notification in realtime", "input-label-realtime-notification-options": "Select Notification Options", "enter-notice-heading": "Enter your notice title/heading", "enter-notice-description": "Enter your notice description", "input-placeholder-search-name": "Search by Name", "input-placeholder-search-code": "Search by Code", "input-placeholder-search-tracking-number": "Search by Tracking Number", "input-placeholder-search-notice": "Search by Notice", "social-settings": "Social Profile Settings", "social-settings-helper-text": "Add your social profile information form here", "input-label-url": "Url", "refund-policy-form-description-details": " Refund Policy's information from here.", "refund-reason-form-description-details": " Refund Reason's information from here.", "error-refund-policy-title-required": "Title is required", "error-refund-reason-title-required": "Title is required", "error-refund-policy-target-required": "Target is required", "button-label-add-refund-policy": "Add Refund Policy", "button-label-add-refund-reason": "Add Refund Reason", "form-title-create-refund-policy": "Add New Refund Policy", "form-title-create-refund-reason": "Add New Refund Reason", "form-title-update-refund-policy": "Update Refund Policy", "form-title-update-refund-reason": "Update Refund Reason", "button-label-update-faq": "Update FAQ", "button-label-add-faq": "Add FAQ", "button-label-update-refund-policy": "Update Refund Policy", "button-label-update-refund-reason": "Update Refund Reason", "input-label-refund-policy-heading": "Refund Policy Heading", "input-label-refund-reason-heading": "Refund Reason Title", "input-label-license-key": "License Key", "input-label-refund-policy-description": "Refund Policy Description", "input-label-refund-policy-heading-placeholder": "Enter Your Refund Policy Heading", "input-label-refund-reason-heading-placeholder": "Enter Your Refund Reason Title", "input-label-license-key-placeholder": "Enter Your License Key", "form-select-text-select-refund-policy": "Select Refund Policy Users", "form-applicable-for": "Applicable For", "VENDOR": "<PERSON><PERSON><PERSON>", "CUSTOMER": "Customer", "input-placeholder-search-heading": "Search by Heading", "input-label-my-staffs": "My Staffs", "input-label-all-staffs": "All Staffs", "flash-sale-thumb-image-title": "Flash sale thumbnail", "flash-sale-thumb-image-help-text": "This thumbnail will be used for flash sale grid.", "flash-sale-cover-image-help-text": "Set cover image", "flash-sale-grid-image-dimension-help-text": "Recommended dimension of the image :", "info-flash-sale-select-dates-text": "Please select dates in which no other flash sale is on going.", "info-flash-sale-campaign-rate-text": "Please select those products only, which prices will not compromise with this campaign rate", "info-about-product-chose-on-flash-sale": "Notice : Products already in an existing campaign will not appear until that sale period is over or that product is removed from campaign.", "error-insufficient-balance": "Insufficient Balance", "error-banner-file-input-required": "You need to upload your banner image here.", "for-help-contact-support-portal": "For help contact support portal", "input-label-domains": "Licensed Domain List", "info-about-digital-product": "When you have come to edit product, please re-upload the digital file again.", "form-title-maintenance-settings": "Maintenance Settings", "input-label-enable-maintenance-mode": "Enable Maintenance Mode", "input-label-title": "Title", "form-title-maintenance-information": "Maintenance information", "input-label-maintenance-cover-image": "Maintenance cover image", "maintenance-start-time": "Maintenance start time", "maintenance-end-date": "Maintenance end time", "maintenance-cover-image-help-text": "Upload your maintenance cover image from here", "site-maintenance-info-help-text": "Change your site maintenance information from here", "error-terms-title-required": "Terms title is required.", "error-term-description-required": "Terms description is required.", "error-flash-sale-title-required": "Flash sale title is required.", "error-flash-sale-description-required": "Flash sale description is required.", "error-flash-sale-campaign-type": "Campaign type is required.", "error-minimum-title": "You can offered minimum 1%.", "error-maximum-title": "You can offered maximum 99%.", "error-description-maximum-title": "Must be exactly 10000 characters.", "error-coupon-code-cannot-contain-white-space": "Coupon Code cannot contain white space and special character", "input-label-verified-customer": "Only For Verified Customer", "error-become-seller-faqs-item": "Add at least one FAQs item.", "error-become-seller-purpose-item": "Add at least one business purpose", "error-become-seller-steps-item": "Add at least one seller steps.", "error-minimum-card-and-discount-amount": "The minimum card amount must be greater than the coupon discount amount", "error-expire-and-active-date": "Expire date can't be before active date", "create-new-vendor-request": "Create new vendor request", "info-flash-sale-info-help-text": "Change your flash sale information from here", "input-label-vendor": "Select a vendor", "input-placeholder-vendor": "Select a vendor", "button-label-transfer": "Transfer", "form-title-transfer-shop-ownership": "Transfer Shop Ownership", "input-label-shop-name": "Shop name", "shop-transfer-helper-text": "Be careful: Once you give away a shop, you can't undo it, and you won't be able to access it anymore.", "text-popup-settings": "Promo Popup", "text-popup-switch": "Enable Promo Popup", "site-popup-info-help-text": "Change your site promo popup information from here", "input-label-popup-cover-image": "Promo popup cover image", "popup-cover-image-help-text": "Upload your promo pop up cover image from here", "form-title-popup-information": "Popup information", "form-title-popup-control": "Popup Control", "title-popup-delay": "<PERSON><PERSON>", "title-popup-delay-info": "This input field value count in milliseconds example: 3000", "title-popup-expired-in": "Popup Expired In", "title-popup-expired-in-info": "This input field value count in days example: 1", "title-popup-checkbox": "This popup not show again?", "error-description-required": "Description is required", "error-popup-delay-min": "Please set minimum value 1000ms.", "error-popup-delay": "Popup delay is required", "error-popup-expired-min": "Please set minimum value 1 days.", "error-popup-expired": "Popup expired in is required", "error-image": "You need to provide a image file.", "input-tooltip-site-title": "Set the “Site Title” for your business. It usually shows in the header or top corner of each page.", "input-tooltip-site-sub-title": "A “Site Subtitle” provides context context to a website. This is essential for business SEO.", "input-tooltip-signUp-point": "Set the “Sign Up Points” as a reward for signing up of a customer. The customers can spent the points for discounts, special deals, and other incentives on the e-commerce site, boosting user involvement and loyalty.", "input-tooltip-minimum-cart-amount": "The “Minimum Cart Amount” refers to the lowest number of  items that a customer must have in their shopping cart before they are allowed to proceed with the checkout process.", "input-tooltip-wallet-currency-ratio": "The “Wallet Currency Ratio” represents the exchange rate or equivalence between the virtual currency stored in a user's wallet and the actual currency used for transactions. It determines how much real money corresponds to a unit of the virtual currency within the user's wallet.", "input-tooltip-maximum-question-limit": "The “Maximum Question Limit” refers to the predefined or set number of inquiries or questions a user can make to manage customer support or engagement interactions.", "input-tooltip-tax-class": "A “Tax Class” is a categorization system that assigns specific tax rates or rules to different products or services based on their nature or regulatory requirements.", "input-tooltip-shipping-class": "The “Shipping Class” is set to categories products and assign delivery charges or methods based on their qualities or shipping needs.", "input-tooltip-otp": "A website's “Use OTP at checkout” security safeguard requires customers to submit a One-Time Password during checkout for better transaction authentication.  ", "input-tooltip-enable-verify-email": "The “Enable Must Verify Email” function requires users to verify their email addresses before accessing or making transactions, improving account security.", "input-tooltip-enable-guest-checkout": "“Enable Guest Checkout” to allow users to make purchases without having an account, making transactions quick and easy.", "input-tooltip-enable-ai": "“Enable AI” allows the system to use advanced algorithms and machine learning for personalised recommendations, customer support, and data analysis to improve the shopping experience.", "input-tooltip-enable-free-shipping": "“Enable Free Shipping” allows clients to receive their orders without shipping charges, usually based on a minimum purchase amount or specified products.", "input-tooltip-enable-cash-on-delivery": "“Enable Cash On Delivery” is a feature that allows customers to pay for their purchases with cash upon delivery of the ordered goods.", "input-tooltip-currency": "“Currency”  is the standardized unit of money used to represent and conduct transactions, providing a framework for pricing and facilitating international trade.", "input-tooltip-enable-gateway": "“Enable Gateway” activates a payment gateway, enabling secure and seamless online transactions by transferring payment information between customers and merchants.", "input-tooltip-currency-formation": "In “Select Currency Formation” you can choose the currency format or structure to match regional preferences or standards.", "input-tooltip-fractional-digits": "“Number of Fractional Digits” controls the decimal places used to display and handle fractional quantities in pricing and financial transactions.", "input-tooltip-meta-title": "“Meta Title” is a concise and relevant HTML tag that provides a brief and descriptive title for a specific web page, often displayed on search engine results pages.", "input-tooltip-meta-description": "“Meta Description” is a brief HTML tag that summaries a web page's content and informs users of its relevance on search engine results pages.", "input-tooltip-meta-tags": "“Meta Tags” are HTML elements that include metadata, such as title, description, and keywords, providing information about the webpage's content and improving its visibility and relevance on search engine results pages.", "input-tooltip-canonical-url": "A “Canonical URL” helps search engines prioritize and consolidate indexing efforts for content with identical or very similar information.", "input-tooltip-og-title": "“OG Title” is the Open Graph metadata tag specifying the title to be displayed when the website's content is shared on social media platforms, optimizing the presentation of shared links.", "input-tooltip-og-description": "“OG Description” sets the brief and informative description to be displayed when the website's content is shared on social media, increasing link exposure and engagement.", "input-tooltip-og-image": "“OG Image” is the Open Graph metadata tag specifying the designated image to accompany shared links on social media, enhancing visual appeal and engagement when the content is shared.", "input-tooltip-twitter-handle": "“Twitter Handle” is used as a social media identify and in online communications and advertising.", "input-tooltip-twitter-card-type": "“Twitter Card Type” is a metadata tag that handles how Twitter displays material, adding context and media to connections.", "input-tooltip-notification-options": "“Select notification options” lets users choose how they want to receive alerts, updates, and notifications about orders, store notice, and messaging activity.", "input-tooltip-sms-options": "“Select SMS Options” refers to the feature that enables users to choose their preferences for receiving text message notifications.", "input-tooltip-email-options": "“Select Email Options” for an e-commerce website allows users to customize their preferences for receiving email notifications.", "input-tooltip-shop-title": "“Title/Time”  refers to the specified title or designation along with the corresponding time slots allocated for product deliveries, providing customers with clear information about expected shipment times.", "input-tooltip-shop-description": "“Description” provides a brief description of each time slot, including projected delivery duration, conditions, and other pertinent information to help clients understand the shipment process.", "input-tooltip-shop-product-review": "“Enable product review system before publish?” suggests the option to activate a product review system that allows customers to submit feedback and reviews before the delivery schedule is finalized or published, promoting user engagement and transparency.", "input-tooltip-shop-enable-google-map": "“Enable Google Map Address” lets consumers use Google Maps to enter or verify delivery addresses, improving accuracy and simplicity.", "input-tooltip-shop-enable-terms": "“Enable Terms & Conditions for vendors” refers to the option to activate and enforce specific terms and conditions that vendors must adhere.", "input-tooltip-shop-enable-coupons": "“Enable coupons for vendors” allows vendors the option to apply and utilize coupons or promotional discounts.", "input-tooltip-enable-maintenance-mode": "“Enable Maintenance Mode” allows the activation of a temporary state that restricts user access to the site, useful for performing maintenance, updates, or resolving issues without disruptions to the customer experience.", "input-tooltip-maintenance-title": "The “Title”  is the designated heading or label displayed during the temporary restricted access, informing users that the site is undergoing maintenance for improvements.", "input-tooltip-maintenance-description": "The “Description” is a brief message explaining the temporary unavailability, indicating that the site is undergoing maintenance to enhance performance or address technical issues.", "input-tooltip-maintenance-start-time": "“Maintenance start time” is the designated time frame when scheduled maintenance activities will start.", "input-tooltip-maintenance-end-time": "“Maintenance end time” is the designated time frame when scheduled maintenance activities will end.", "input-tooltip-maintenance-overlay-color": "The “Overlay color enable?” option activates a coloured overlay on the site to better indicate maintenance mode.", "input-tooltip-maintenance-button-one": "“Button Title One” is the designated label for the primary action button displayed during the temporary restricted access, typically prompting users for relevant actions.", "input-tooltip-maintenance-button-two": "“Button Title Two” is the designated label for the primary action button displayed during the temporary restricted access, typically prompting users for relevant actions.", "input-tooltip-maintenance-newsletter-title": "“Newsletter Title” offers users to subscribe for newsletters or updates during maintenance, allowing continuing engagement.", "input-tooltip-maintenance-newsletter-description": "“Description” is a brief statement to users urging them to subscribe to a newsletter for updates on repair progress and site turnaround.", "input-tooltip-maintenance-drawer-title": "“About Us Heading” is the title or heading that briefly describes the company or website to users during the temporary restricted access period.", "input-tooltip-maintenance-drawer-description": "“Description” briefs users on the purpose and details of the maintenance, reassuring them of the site's temporary downtime.", "input-tooltip-maintenance-contact-us": "The “Contact Us Heading” title or header instructs users how to contact support during the temporary restricted access period.", "input-tooltip-company-city": "Enter your City here.", "input-tooltip-company-country": "Enter your Country here.", "input-tooltip-company-state": "Enter your State here.", "input-tooltip-company-zip": "Enter your ZIP here.", "input-tooltip-company-street-address": "Enter your Street Address here.", "input-tooltip-company-contact-number": "Enter your Contact Number here.", "input-tooltip-company-website": "Enter your Website URL name here.", "input-tooltip-company-email": "Enter your Company Email name here.", "input-tooltip-company-social-platform": "Select a social media platform from the dropdown list.", "input-tooltip-company-profile-url": "Enter the social media profile URL here.", "input-tooltip-company-site-link": "Enter the site link here.", "input-tooltip-company-copyright-text": "Enter the Copyright Text here.", "input-tooltip-company-external-text": "Enter the External Text here.", "input-tooltip-company-external-link": "Enter the External Link here.", "input-tooltip-promo-enable": "“Enable Promo Popup” for an e-commerce website is the option to activate a promotional popup, allowing the display of targeted offers, discounts, or announcements to visitors, enhancing marketing efforts and user engagement.", "input-tooltip-promo-title": "“Title” lets website visitors see and hear about special deals and announcements by activating a title or heading for promotional pop-ups.", "input-tooltip-promo-description": "“Description” refers to the option allowing the activation of a promotional popup, typically displaying a brief description or message, to attract user attention and communicate special offers, discounts, or promotions.", "input-tooltip-promo-delay": "“Popup Delay” is the specified time interval before a promotional popup appears on the screen, allowing a delay period to enhance user experience and engagement timing..", "input-tooltip-promo-expired": "“Popup Expired In” is the designated time frame indicating the expiration period of a promotional popup, specifying how long the promotional message or offer will be displayed to users before being automatically removed.", "input-tooltip-promo-not-show": "“This popup not show again?” user interface option lets users select to hide a specific popup message or notice in future interactions.", "input-tooltip-promo-not-show-title": "“Title” refers to the title or label associated with a checkbox or option in a popup, allowing users to choose whether they want to suppress the display of the popup in future visits or interactions with the site.", "input-tooltip-promo-not-show-expired": "The “Popup Expired In” specifies the duration until the popup automatically expires or stops appearing.", "end": "end", "text-conventional-review-system": "Give purchased product a review only for one time. (By default)", "text-order-basis-review-system": "Give review per order basis", "text-enable-review-popup": "Enable review popup?", "text-review-system": "Review mechanism", "input-tooltip-review-system": "In this feature Super admin can control review system. Either each product can be reviewed by one customer only one time, or customer can reviewed the same product on each order. Incase of Digital Product if customer gave review already then he/she can update it.", "input-tooltip-enable-review-popup": "When a customer's order is completed, then a pop-up box will appear in the shop end to inform customer about giving a review", "text-video-tooltip": "Please enter your Iframe embed code here.", "text-shop-approve-modal-title": "Commission Rate", "text-shop-approve-modal-message-title": "Message from <PERSON><PERSON><PERSON>", "text-shop-approve-modal-message-button": "Connect With Vendor", "text-shop-approve-switch": "Enable Custom Commission?", "text-shop-approve-button-title": "Go With Default Commission", "input-label-subtitle": "Subtitle", "error-subtitle-required": "Subtitle is required", "upload-image-help-text": "Upload your image.", "upload-image-help-text-dimension": "Dimension of the image should be", "become-seller-form-title": "How to be a seller? Fill out all the information.", "banner-title": "Banner", "banner-description": "Set banner content here.", "text-news-ticker-title": "News ticker title", "text-news-ticker-link": "News ticker link", "text-primary-button-name": "Primary button name", "text-primary-button-link": "Primary button link", "text-secondary-button-name": "Secondary button name", "text-secondary-button-link": "Secondary button link", "start-selling-title": "Start selling", "start-selling-description": "Please set start selling options.", "text-selling-steps": "Selling Steps", "remove-item-confirmation": "Do you really want to remove this item?", "text-add-sellng-steps": "Add selling steps", "user-story-title": "User Story", "user-story-description": "Please set user stories for become a seller page.", "text-story-item": "Story item", "text-video-link": "Video Link", "text-video-thumbnail": "Video Thumbnail", "text-add-user-story": "Add User Story", "business-purpose-title": "Business Purpose", "business-purpose-description": "Please set all business purposes for become a seller page.", "text-business-purpose-item": "Business Purpose Item", "text-add-business-purpose": "Add business purposes", "pricing-plan-title": "Pricing Plan", "pricing-plan-description": "Please set all pricing plan chart for become a seller page.", "text-commission-item": "Commission Item", "input-label-minimum-balance": "Minimum balance", "input-label-maximum-balance": "Maximum balance", "input-label-commission": "Commission", "text-add-commission": "Add Commission", "label-default-commission-details": "Default Commission Details Information", "label-default-commission-rate": "Default Commission Rate", "dashboard-showcase-title": "Dashboard Showcase", "dashboard-showcase-description": "Set dashboard showcase content here.", "guideline-title": "Guideline", "guideline-description": "Please add guidelines for become a seller page.", "text-guideline-item": "Guideline Item", "label-link": "Link", "text-add-guideline": "Add Guideline", "faq-title": "FAQs", "faq-description": "Please set all business purposes for become a seller page.", "text-faq-item": "Faq Item", "text-add-faqs": "Add FAQs", "contact-title": "Contact", "contact-description": "Please add contact section title and subtitle for become a seller page.", "seller-opportunity-title": "Seller Opportunity", "seller-opportunity-description": "Set seller opportunity content here.", "error-minimum-one-required": "Minimum 1 item is required", "error-icon-required": "Icon is required", "error-commission-rate-required": "Commission rate is required", "error-commission-rate-type": "Commission rate must be a number", "error-commission-rate-positive": "Commission rate must be positive", "error-minimum-balance-must-be-number": "Minimum balance must be a number", "error-minimum-balance-is-required": "Minimum balance is required", "error-maximum-balance-type": "Maximum balance number or 'over' text", "error-maximum-balance-is-required": "Maximum balance is required", "text-save-seller-information": "Save Seller Information", "error-digital-file-is-required": "Digital file is required", "error-external-product-url-required": "External product URL is required", "error-external-product-button-text-required": "External product button text is required"}, "common": {"text-guest": "Guest", "text-read-more": "Read more", "text-less": "Less", "text-or": "Or", "text-nav-menu": "<PERSON><PERSON>", "description": "Track Order", "error-heading": "Error code: 404", "nav-menu-track-order": "Track Order", "nav-menu-offer": "Offers", "nav-menu-faq": "FAQ", "nav-menu-contact": "Contact", "user-avatar": "user avatar", "auth-menu-profile": "Contact", "auth-menu-checkout": "Checkout", "auth-menu-my-orders": "My Orders", "auth-menu-logout": "Logout", "join-button": "Join", "change-locale": "Switch to german", "admin-login-title": "Login to dashboard", "admin-register-title": "Register new account", "billing-address": "Billing Address", "shipping-address": "Shipping Address", "no-order-found": "No Order Found", "no-message-found": "No Message Found", "no-notification-found": "No Notification Found", "order-sub-total": "Sub total", "order-tax": "Tax", "order-delivery-fee": "Delivery fee", "order-discount": "Discount", "order-total": "Total", "signing-out-text": "Signing out...", "sale-history": "Sale History", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "attribute": "Attribute", "attribute-values": "Attribute Values", "pixel": "Pixel", "text-loading": "Loading...", "sidebar-nav-item-dashboard": "Dashboard", "sidebar-nav-item-products": "Products", "sidebar-nav-item-attributes": "Attributes", "sidebar-nav-item-attribute-value": "Attribute Values", "sidebar-nav-item-groups": "Groups", "sidebar-nav-item-categories": "Categories", "sidebar-nav-item-orders": "Orders", "sidebar-nav-item-order-status": "Order Status", "sidebar-nav-item-users": "Users", "sidebar-nav-item-coupons": "Coupons", "sidebar-nav-item-taxes": "Taxes", "sidebar-nav-item-shippings": "Shippings", "sidebar-nav-item-settings": "Settings", "sidebar-nav-item-store-notice": "Store Notice", "sidebar-nav-item-message": "Message", "sidebar-nav-item-shops": "Shops", "sidebar-nav-item-my-shops": "My Shops", "sidebar-nav-item-my-shops-dashboard": "Shops Dashboard", "sidebar-nav-item-tags": "Tags", "sidebar-nav-item-withdraws": "<PERSON><PERSON><PERSON><PERSON>", "sidebar-nav-item-my-staffs": "My Staffs", "sidebar-nav-item-vendor-staffs": "Vendor Staffs", "navbar-add-products": "Add Products", "authorized-nav-item-settings": "Settings", "authorized-nav-item-profile": "Profile", "authorized-nav-item-logout": "Logout", "authorized-nav-item-submit": "Submit", "text-delete": "Delete", "text-bio": "Bio", "text-resend-verification-email": "Resend Verification Email", "text-terms-conditions": "Terms And Conditions", "text-listed-terms-conditions": "Listed Terms And Conditions", "text-create-terms-conditions": "Create Terms And Conditions", "text-add-terms-conditions": "Add Terms And Conditions", "text-edit": "Edit", "text-view": "View", "text-see-all-notification": "See all notification", "text-see-all-notifications": "See all notifications", "text-vendors": "Vend<PERSON>", "text-admins": "Admins", "text-ban-user": "Ban User", "text-activate-user": "Activate User", "update-success": "Successfully updated!", "name-required": "You must need to provide a name", "email-required": "You must need to provide your email address", "invalid-email": "Provided email address format is not valid", "password-required": "You must need to provide your password", "currency-required": "Please select a currency", "token-required": "You must need to provide your token address", "confirm-password": "Please confirm password!", "password-should-match": "Passwords should match!", "invalid-token": "Invalid <PERSON>!", "status-required": "Status is required", "type-required": "Type is required", "amount-required": "Amount is required", "amount-greater-than-zeo": "Amount must be greater than zero", "code-required": "Code is required", "serial-required": "Serial is required", "color-required": "Color is required", "tax-required": "Tax rate is required", "tax-greater-than-zeo": "Tax must be greater than zero", "tax-must-be-integer": "Tax must be integer", "serial-greater-than-zeo": "Serial must be greater than zero", "serial-must-be-integer": "Serial must be integer", "active-from-date-required": "Active from date is required", "expire-date-required": "Expire date is required", "delete-item": "Delete Item", "delete-item-confirm": "Are you sure, you want to delete?", "button-cancel": "Cancel", "button-delete": "Delete", "block-customer": "Block Customer", "block-customer-confirm": "Are you sure, you want to block this customer?", "button-block": "Block", "text-permission-message": "Your shop is not activated yet. You can't proceed further operations.", "sidebar-nav-item-staffs": "Staff", "text-no-contact": "No contact number available", "text-visit-shop": "Visit Shop", "text-edit-shop": "Edit Shop", "text-enabled": "Enabled", "text-disabled": "Disabled", "text-active": "Active", "text-inactive": "Inactive", "text-products": "Products", "text-total-products": "Total Products", "text-items-sold": "Items Sold", "text-revenue": "Revenue", "text-order": "Order", "text-orders": "Orders", "text-clear": "Clear", "text-faq": "FAQ", "text-no-search": "No search left", "text-total-orders": "Total Orders", "text-gross-sales": "Gross Sales", "text-quick-page-links": "Quick Page Links", "text-others": "Others", "text-commission-rate": "Admin Commission Rate", "text-current-balance": "Current Balance", "text-registered-since": "Registered Since", "text-payment-info": "Payment Information", "text-shop-dashboard": "View Dashboard", "text-no-shop": "No Shops Found", "text-px": "px", "text-create-shop": "Create Shop", "text-add-your": "Please add your", "text-pending": "Pending", "text-approved": "Approved", "text-on-hold": "On hold", "text-rejected": "Rejected", "text-processing": "Processing", "text-amount": "Amount", "text-payment-method": "Payment Method", "text-status": "Status", "text-details": "Details", "text-note": "Note", "text-logo": "Logo", "text-name": "Name", "text-email": "Email", "text-is-created": "is Created", "text-bank": "Bank", "text-image": "Image", "text-account-no": "Account No.", "text-no-address": "No Address Found", "text-withdrawal-info": "Withdrawal Information", "password-changed-successfully": "Successfully changed your password!", "successfully-updated": "Successfully updated!", "successfully-created": "Successfully created!", "successfully-deleted": "Successfully deleted!", "successfully-decline": "Successfully decline!", "successfully-unblock": "Unblock Successfully.", "successfully-block": "Block Successfully.", "successfully-logout": "<PERSON><PERSON>ut Successfully.", "successfully-register": "Register Successfully.", "successfully-login": "Login Successfully.", "PICKBAZAR_ERROR.NOT_FOUND": "Not Found", "PICKBAZAR_ERROR.NOT_AUTHORIZED": "Not Authorized", "PICKBAZAR_ERROR.INVALID_CREDENTIALS": "Invalid Credentials", "PICKBAZAR_ERROR.SOMETHING_WENT_WRONG": "Something went wrong", "PICKBAZAR_ERROR.PAYMENT_FAILED": "Payment Failed", "PICKBAZAR_ERROR.OPERATION_NOT": "Operation Not", "PICKBAZAR_ERROR.INSUFFICIENT_BALANCE": "Insufficient Balance", "PICKBAZAR_ERROR.WITHDRAW_MUST_BE_ATTACHED_TO_SHOP": "Withdrawal must be attached to shop", "PICKBAZAR_ERROR.PLEASE_LOGIN_USING_FACEBOOK_OR_GOOGLE": "Please login using Facebook or Google", "PICKBAZAR_ERROR.ACTION_NOT_VALID": "Action not valid", "PICKBAZAR_ERROR.SHOP_NOT_APPROVED": "Shop not approved", "PICKBAZAR_ERROR.ALREADY_REFUNDED": "Refund Already Approved", "PICKBAZAR_ERROR.INVALID_GATEWAY": "Invalid Gateway", "PICKBAZAR_ERROR.OTP_SEND_FAIL": "OTP send failed", "PICKBAZAR_ERROR.OTP_VERIFICATION_FAILED": "OTP verification failed", "PICKBAZAR_ERROR.CONTACT_UPDATE_FAILED": "Fail to update contact information", "PICKBAZAR_ERROR.ORDER_ALREADY_HAS_REFUND_REQUEST": "Order already has refund request", "PICKBAZAR_ERROR.REFUND_ONLY_ALLOWED_FOR_MAIN_ORDER": "Refund only allowed for main order", "PICKBAZAR_ERROR.WRONG_REFUND": "Wrong refund", "PICKBAZAR_ERROR.CSV_NOT_FOUND": "CSV not found", "PICKBAZAR_ERROR.USER_NOT_FOUND": "User not found", "PICKBAZAR_ERROR.TOKEN_NOT_FOUND": "Token not found", "PICKBAZAR_ERROR.INVALID_COUPON_CODE": "Invalid coupon code! please try again.", "PICKBAZAR_ERROR.COUPON_CODE_IS_NOT_APPLICABLE": "Sorry, This coupon can't satisfy your order amount.", "PICKBAZAR_ERROR.ALREADY_FREE_SHIPPING_ACTIVATED": "Already free shipping activated! please try again.", "PICKBAZAR_MESSAGE.NOT_FOUND": "Not Found", "PICKBAZAR_MESSAGE.CHECK_INBOX_FOR_PASSWORD_RESET_EMAIL": "Check inbox for password reset email", "PICKBAZAR_MESSAGE.SOMETHING_WENT_WRONG": "Something went wrong", "PICKBAZAR_MESSAGE.TOKEN_IS_VALID": "Token is valid", "PICKBAZAR_MESSAGE.INVALID_TOKEN": "Token is not valid", "PICKBAZAR_MESSAGE.PASSWORD_RESET_SUCCESSFUL": "Password reset successful", "PICKBAZAR_MESSAGE.INVALID_CREDENTIALS": "Provided credentials is not valid", "PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL": "Email successfully sent", "PICKBAZAR_MESSAGE.OLD_PASSWORD_INCORRECT": "Incorrect Password", "PICKBAZAR_MESSAGE.OTP_SEND_SUCCESSFUL": "Successfully sent an OTP", "PICKBAZAR_MESSAGE.REQUIRED_INFO_MISSING": "Required information is missing", "PICKBAZAR_MESSAGE.CONTACT_UPDATE_SUCCESSFUL": "Successfully updated contact information", "PICKBAZAR_ERROR.YOU_CAN_NOT_SEND_MESSAGE_TO_YOUR_OWN_SHOP": "You can't send message to your own shop", "text-access-denied": "Access Denied", "text-access-denied-message": "You don't have the permission to access this page.", "text-return-home": "Return to home", "text-upload-highlight": "Upload an image", "text-upload-message": "or drag and drop", "text-img-format": "PNG, JPG", "text-shop-approve-button": "Submit", "text-shop-approve-title": "Disapprove Shop", "text-shop-approve-description": "Are you sure?", "text-approve-shop": "Approve Shop", "text-disapprove-shop": "Disapprove Shop ?", "filter-by-group": "Filter By Group", "filter-by-group-placeholder": "Filter by Group", "filter-by-category": "Filter By Category", "filter-by-author": "Filter by Author", "filter-by-category-placeholder": "Filter by Category", "filter-by-author-placeholder": "Filter by author", "filter-by-reason": "Filter By Reason", "filter-by-reason-placeholder": "Filter by Reason", "filter-by-order": "Order By", "filter-by-order-placeholder": "Order By", "text-filter": "Filter", "text-download": "Download", "text-map-cant-load": "Map cannot be loaded right now, sorry.", "text-banner": "Banner", "text-export-import": "Export/Import", "text-export-products": "Export Products", "text-export-product-variations": "Export Product Variations", "text-import-products": "Import Products", "text-import-product-variations": "Import Product Variations", "text-import-attributes": "Import Attributes", "text-export-attributes": "Export Attributes", "attribute-imported-successfully": "Attributes Imported Successfully", "product-imported-successfully": "Products Imported Successfully", "variation-options-imported-successfully": "Variation Options Imported Successfully", "PICKBAZAR_ERROR.WRONG_CSV": "You have uploaded the wrong csv file!", "sidebar-nav-item-refunds": "Refunds", "text-update-refund": "Update Refund Status", "text-refund-id": "Refund ID", "text-language": "Language", "text-refund-status": "Refund Status", "text-status-required": "Status is required", "text-refund-created": "Refund Created", "text-order-created": "Order Created", "text-order-status": "Order Status", "text-low-stock-products": "Low stock products", "text-most-rated-products": "Top 10 Most Rated Products", "text-most-category-products": "Top 10 Category with most products", "text-customer-email": "Customer <PERSON><PERSON>", "text-reason": "Refund Reason", "text-report-reason": "Report Product Review", "text-images": "Images", "text-today": "Today", "text-weekly": "Weekly", "text-monthly": "Monthly", "text-yearly": "Yearly", "text-order-details": "Order Details", "text-no-image-found": "No image found", "MARVEL_ERROR.ALREADY_REFUNDED": "Already Refunded", "text-item": "<PERSON><PERSON>", "text-add": "Add", "text-cart": "<PERSON><PERSON>", "text-add-cart": "Add To Shopping Cart", "text-close": "close", "text-no-products": "No products found", "text-checkout": "Checkout", "text-yes": "Yes", "text-make-admin": "Assign <PERSON> Revoke Admin Permission ", "text-description-make-admin": "Are you sure you want to assign/revoke admin permission?", "text-customer": "Customer", "text-contact-number": "Contact Number", "text-billing-address": "Billing Address", "text-shipping-address": "Shipping Address", "text-delivery-schedule": "Delivery Schedule", "text-no-customer": "No Customer Found", "text-add-new": "Add New", "text-select": "Select", "text-save": "Save", "text-update": "Update", "text-address": "Address", "text-billing": "Billing", "text-shipping": "Shipping", "text-type": "Type", "text-title": "Title", "text-flash-sale": "Flash Sale", "text-country": "Country", "text-city": "City", "text-state": "State", "text-zip": "ZIP", "text-street-address": "Street Address", "text-place-order": "Place Order", "text-your-order": "Your Order", "text-cash-on-delivery": "Cash On Delivery", "text-card-info": "Card Information", "text-check-availability": "Check Availability", "text-estimated-shipping": "Estimated Shipping", "text-calculated-checkout": "Calculated at checkout", "text-proceed-checkout": "Proceed to checkout", "text-sub-total": "Sub Total", "text-total": "Total", "text-tax": "Tax", "text-discount": "Discount", "text-free-shipping": "Free Shipping", "text-have-coupon": "Do you have a coupon?", "text-enter-coupon": "Enter coupon code here", "text-coupon-required": "A Coupon code is required", "text-apply": "Apply", "text-choose-payment": "Choose Payment Method", "text-cod-message": "Please Pay After You Receive Your Goods!", "text-payable": "Payable", "text-cash-message": "Please pay with cash", "text-manufacturers": "Manufacturers", "text-manufacturers-publications": "Manufacturers/Publications", "sidebar-nav-item-manufacturers": "Manufacturers/Publications", "sidebar-nav-item-authors": "Authors", "sidebar-nav-item-refund-policy": "Refund Policies", "text-authors": "Authors", "sidebar-nav-item-create-order": "Create Order", "text-wallet": "Wallet", "text-balance": "balance", "text-currency": "currency", "text-total-points": "Total points", "text-points-used": "Points used", "text-points": "Points", "text-wallet-use": "Do you want to use wallet?", "text-available-balance": "Available Balance", "text-invoice": "Invoice", "error-author-name-required": "Author name is required", "error-manufacturer-name-required": "Manufacturer name is required", "error-invalid-coupon": "This coupon code is not valid", "text-price": "Price", "text-not-found": "Sorry, No Product Found :(", "text-notice-not-found": "Sorry, No Notice Found :(", "sidebar-nav-item-reviews": "Reviews", "sidebar-nav-item-questions": "Questions", "text-product-id": "Product ID", "text-view-images": "View Images", "text-out-of-stock": "out of stock", "text-low-in-stock": "Low in stock", "text-low-quantity": "low quantity", "text-see-details": "See details", "text-campaign-status": "Campaign status", "text-campaign-type-on": "Campaign type on", "text-deals-rate": "Deals rate", "text-deals": "Deals", "error-add-both-address": "Please add both shipping and billing address", "sort-by-quantity": "Sort by Quantity", "text-by": "By", "text-accept-report-modal-description": "Are you sure you want to accept this request? It will delete the review.", "text-accept": "Accept", "text-decline": "Decline", "text-decline-report-modal-description": "Are you sure you want to decline this request?", "text-abuse-report": "Abusive Report", "text-abuse-report-submitted": "Abusive report submitted successfully", "text-report": "Report", "text-summary": "Summary", "text-campaign": "campaign", "text-pending-order": "Pending Order", "text-processing-order": "Processing Order", "text-completed-order": "Completed Order", "text-cancelled-order": "Cancelled Order", "text-failed-order": "Failed Order", "text-order-local-facility": "Order ar local facility", "text-order-out-delivery": "Order out for delivery", "text-refunded-order": "Refunded Order", "text-notification": "Notification", "text-notifications": "Notifications", "text-visit-site": "Visit Site", "text-visit-store": "Visit Store", "text-created-new-order": "Created a new order", "text-sent-new-store-notice": "created a new store notice.", "text-sent-new-message": "sent a new message.", "text-export-orders": "Export Orders", "text-non-translated-title": "Create", "text-translated-title": "Edit", "text-quantity": "Quantity", "text-follow-us-on": "Follow us on", "text-invoice-no": "Invoice No", "text-date": "Date", "text-mark-all-read": "Mark all as read", "error-file-too-large": "File too large", "text-payment-status": "Payment Status", "error-invalid-file-type": "Your uploaded file type isn't supported", "order-pending": "Pending", "order-processing": "Processing", "order-completed": "Completed", "order-cancelled": "Cancelled", "order-failed": "Failed", "order-refunded": "Refunded", "order-at-local-facility": "Order At Local Facility", "order-out-for-delivery": "Order Out For Delivery", "payment-pending": "Payment Pending", "payment-processing": "Payment Processing", "payment-success": "Payment Success", "payment-failed": "Payment Failed", "payment-reversal": "Payment Reversal", "payment-wallet": "Wallet Payment", "payment-cash-on-delivery": "Cash On Delivery", "payment-cash": "Cash", "payment-refunded": "Payment Refunded", "payment-awaiting-for-approval": "Awaiting for Approval", "text-messages": "Messages", "text-location": "Location", "text-webhook-url": "Webhook URL", "email-not-verified": "Your email is not verified. Please verify your email first.", "license-not-verified": "Your license is not verified. Please verify your license first.", "text-no-message-found": "<PERSON><PERSON>! You haven't stared any conversation yet!", "text-no-shop-found": "Op<PERSON>! There's no shop available.", "text-starting-chat": "Starting Chat", "text-start-conversation": "Starting Conversation", "text-input-search": "Search by shop (type at least 3 character)", "text-compose": "Compose Message", "text-inbox-empty": "Your Inbox is Empty", "text-blocked-content-one": "You blocked messages from", "text-account": "account", "text-blocked-content-two": "You can't message them in this chat, and you won't receive their messages.", "text-something-wrong": "Something going wrong", "text-select-your-conversation": "Select Your Conversation", "notice-active-date": "Active Date", "notice-created-by": "Created By", "notice-expire-date": "Expire Date", "text-empty-notice": "Empty Notice", "text-load-more": "Load More", "text-back-to-home": "Back to home", "text-select-payment-gateway": "Select Payment Gateway", "text-select-default-payment-gateway": "Set default payment gateway", "text-message-sent": "Message Sent...", "text-order-pending": "Order Pending", "text-order-processing": "Order Processing", "text-order-at-local-facility": "Order at local facility", "text-order-out-for-delivery": "Order Out For Delivery", "text-order-completed": "Order Completed", "text-order-cancelled": "Order Cancelled", "text-order-refunded": "Order Refunded", "text-total-amount": "Total Amount", "text-shipping-charge": "Shipping Charge", "text-paid-from-wallet": "Paid <PERSON> Wallet", "text-total-item": "Total Item", "text-deliver-time": "Delivery Time", "text-order-failed": "Order Failed", "text-paid_from_wallet": "Wallet Payment", "text-amount-due": "Amount Due", "text-refund-policies": "Refund Policies", "text-refund-reasons": "Refund Reasons", "text-select-refund-policy-for": "Select Refund Policy For", "filter-by-refund-policy": "Filter By User Group", "filter-by-notification-type": "Filter By Notification Type", "filter-by-status": "Filter By Status", "percentage": "Percentage", "text-approval-action": "Approval Action", "text-enable-gateway": "Enable Gateway", "text-currency-options": "Currency Options", "text-seo": "SEO", "text-max-search-location-distance": "Maximum Search Location Distance(km)", "text-main": "Main", "text-shop-management": "Shop management", "text-all-shops": "All shops", "text-add-all-shops": "Add new shop", "text-inactive-shops": "Inactive/New shops", "text-product-management": "Product management", "text-all-products": "All Product", "text-new-products": "Add new product", "text-my-draft": "My Draft", "text-my-draft-products": "My Draft products", "text-all-out-of-stock": "All Low & Out of Stock products", "text-inventory": "Inventory", "text-e-commerce-management": "E-commerce Management", "text-reported-refunds": "Reported refunds", "text-new-refund-policy": "Add new refund policy", "text-new-refund-reasons": "Add new refund reason", "text-page-control": "Layout/Page control", "text-groups": "Home pages / Groups", "text-faqs": "FAQs", "text-all-faqs": "All FAQs", "text-new-faq": "Add new FAQ", "text-all-terms": "All Terms", "text-new-terms": "Add new Terms", "text-order-management": "Order management", "text-transactions": "Transactions", "text-user-control": "User control", "text-all-users": "All users", "text-admin-list": "Admin list", "text-all-vendors": "All vendors", "text-pending-vendors": "Pending vendors", "text-customers": "Customers", "text-feedback-control": "Feedback control", "text-promotional-management": "Promotional management", "text-all-campaigns": "All campaigns", "text-new-campaigns": "Add new campaigns", "text-feature-management": "Feature Management", "text-site-management": "Site management", "text-general-settings": "General settings", "text-payment-settings": "Payment settings", "text-seo-settings": "SEO settings", "text-events-settings": "Events settings", "text-shop-settings": "Shop settings", "text-company-settings": "Company Information", "text-maintenance-settings": "Maintenance Settings", "text-financial-management": "Financial Management", "text-promotional-control": "Promotional control", "text-available-flash-deals": "Available flash deals", "text-my-products-in-deals": "My products in deals", "text-page-management": "Layout/Page management", "text-low-out-of-stock": "Low & Out of Stock", "text-no-log-found": "No Notification found", "fixed_rate": "Fixed Rate", "text-preview": "Preview", "text-shop-disapprove-description": "Then all the products created in this shop will be drafted.", "button-label-update-terms-conditions": "Update terms and conditions.", "button-label-add-terms-conditions": "Add terms and conditions.", "text-add-wallet-points": "Add Wallet Points", "text-maintenance-mode-title": "Maintenance Mode Begins In", "text-maintenance-mode-start-title": "Maintenance mode is start.", "text-top-bar-search-placeholder": "Search your route...", "text-title-commission": "Commission", "text-title-sale": "Sale", "text-title-balance": "Balance", "text-title-withdraw": "Withdraw", "text-title-description": "Description", "text-approve": "Approve", "text-disapprove": "Disapprove", "text-approve-coupon": "Approve Coupon", "text-want-approve-coupon": "Are you want to approve the coupon?", "text-want-disapprove-coupon": "Are you want to disapprove the coupon?", "text-disapprove-coupon": "Disapprove Coupon", "text-all-coupons": "All coupons", "text-new-coupon": "Add new coupon", "text-transfer-shop-ownership": "Transfer Ownership", "successfully-transferred": "Ownership has been Transferred Successfully to ", "text-popup-settings": "Promo Popup", "text-image-uploading-message": "Please wait! Image is uploading", "text-quote-title": "Quote", "text-transfer-shop-ownership-status": "Transfer shop ownership.", "text-pixel-dot": "pixel."}, "table": {"recent-order-table-title": "Recent Orders", "popular-products-table-title": "Popular Products", "empty-table-data": "No data found", "empty-table-sorry-text": "Sorry we couldn’t found any data", "table-item-id": "ID", "table-item-image": "Image", "table-item-logo": "Logo", "table-item-title": "Name", "table-item-title-title": "Title", "table-item-start-date": "Start date", "table-item-end-date": "End date", "table-item-product": "Product", "table-item-products": "Products", "table-item-type": "Type", "table-item-product-type": "Product Type", "table-item-stock-status": "Stock status", "table-item-category-id": "Category ID", "table-item-category-name": "Category Name", "table-item-Product-count": "Product Count", "table-item-regular-price": "Regular price", "table-item-product-price": "Product price", "table-item-deal-offering": "Deal offering", "table-item-discount": "Discount", "table-item-payment-gateway": "Payment gateway", "table-item-payment-status": "Payment Status", "table-item-taxable-amount": "Taxable amount", "table-item-unit": "Price/Unit", "table-item-quantity": "Quantity", "table-item-sold-quantity": "Sold Quantity", "table-item-description": "Description", "table-item-status": "Status", "table-item-actions": "Actions", "table-item-tracking-number": "Tracking Number", "table-item-total": "Total", "table-item-order-date": "Order Date", "table-item-icon": "Icon", "table-item-slug": "Slug", "table-item-sku": "SKU", "table-item-details": "Details", "table-item-delivery-fee": "Delivery Fee", "table-item-shipping-address": "Shipping Address", "table-item-serial": "Serial", "table-item-email": "Email", "table-item-avatar": "Avatar", "table-item-banner": "Banner", "table-item-code": "Code", "table-item-amount": "Amount", "table-item-minimum-amount": "Minimum Amount", "table-item-active": "Active", "table-item-expired": "Expired", "table-item-rate": "Rate", "table-item-country": "Country", "table-item-city": "City", "table-item-state": "State", "table-item-zip": "ZIP", "table-item-global": "Global", "table-item-values": "Values", "table-item-group": "Group", "table-item-shop-id": "Shop ID", "table-item-shop-name": "Shop Name", "table-item-payment": "Payment", "table-item-payment-method": "Payment Method", "table-item-note": "Note", "table-item-owner-name": "Owner Name", "table-item-total-products": "Products", "table-item-total-orders": "Orders", "table-item-shop": "Shop", "withdraw-table-title": "Recent Withdrawals", "table-item-created-at": "Created", "table-shipping-type": "Shipping Type", "table-item-available_wallet_points": "Available wallet points", "table-item-order-id": "Order ID", "table-item-customer-email": "Customer <PERSON><PERSON>", "table-item-is-approved": "Is Approved", "table-item-approval-action": "Approval Action", "table-item-permissions": "Permissions", "table-item-question-answer": "Question & Answer", "table-item-customer-name": "Customer Name", "table-item-customer": "Customer", "table-item-product-name": "Product Name", "table-item-date": "Date", "table-item-customer-review": "Customer Review", "table-item-ratings": "Ratings", "table-item-reports": "Reports", "table-item-message": "Message", "table-item-customer-details": "Customer Details", "table-item-feedbacks": "Feedbacks", "table-item-notice": "Notice", "table-item-priority": "Priority", "table-item-receiver": "Receiver", "table-item-effective-from": "Effective From", "table-item-expired-at": "Expired At", "table-item-issued-by": "Issued By", "table-item-heading": "Heading", "table-homepage-name": "Homepage name", "table-item-url": "Domains", "table-item-target": "Applied on", "table-item-coupon-amount": "Coupon Amount", "table-item-minimum-cart-amount": "Minimum Cart Amount"}}}, "initialLocale": "en", "ns": ["form", "common", "table"], "userConfig": {"i18n": {"defaultLocale": "en", "locales": ["en"]}, "localePath": "C:\\GithubProjects\\LOLgorithm\\logorithm-e-site\\admin-rest\\public\\locales", "reloadOnPrerender": false, "default": {"i18n": {"defaultLocale": "en", "locales": ["en"]}, "localePath": "C:\\GithubProjects\\LOLgorithm\\logorithm-e-site\\admin-rest\\public\\locales", "reloadOnPrerender": false}}}}, "__N_SSG": true}