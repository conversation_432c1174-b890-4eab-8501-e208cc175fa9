"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_terms-and-conditions_approve-term-view_tsx";
exports.ids = ["src_components_terms-and-conditions_approve-term-view_tsx"];
exports.modules = {

/***/ "./src/components/terms-and-conditions/approve-term-view.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/terms-and-conditions/approve-term-view.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_form_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/form/form */ \"./src/components/ui/form/form.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _data_terms_and_condition__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/terms-and-condition */ \"./src/data/terms-and-condition.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_form_form__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _data_terms_and_condition__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_ui_form_form__WEBPACK_IMPORTED_MODULE_1__, _components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _data_terms_and_condition__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ApproveShopView = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { mutate: approveTermMutation, isLoading: loading } = (0,_data_terms_and_condition__WEBPACK_IMPORTED_MODULE_5__.useApproveTermAndConditionMutation)();\n    const { data: shopId } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__.useModalAction)();\n    function onSubmit() {\n        approveTermMutation({\n            id: shopId\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_form__WEBPACK_IMPORTED_MODULE_1__.Form, {\n        onSubmit: onSubmit,\n        children: ({ register, formState: { errors } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"m-auto flex w-full max-w-sm flex-col rounded bg-light p-5 sm:w-[24rem]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mb-4 text-lg font-semibold text-muted-black\",\n                        children: t(\"form:form-title-do-you-approve\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\approve-term-view.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            type: \"submit\",\n                            loading: loading,\n                            disabled: loading,\n                            children: t(\"form:button-label-submit\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\approve-term-view.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\approve-term-view.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\approve-term-view.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\terms-and-conditions\\\\approve-term-view.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ApproveShopView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/terms-and-conditions/approve-term-view.tsx\n");

/***/ }),

/***/ "./src/components/ui/form/form.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/form/form.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Form: () => (/* binding */ Form)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hook-form */ \"react-hook-form\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"@hookform/resolvers/yup\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_2__]);\n([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Form = ({ onSubmit, children, options, validationSchema, serverError, resetValues, ...props })=>{\n    const methods = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_1__.useForm)(//@ts-ignore\n    {\n        ...!!validationSchema && {\n            resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_2__.yupResolver)(validationSchema)\n        },\n        ...!!options && options\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (serverError) {\n            Object.entries(serverError).forEach(([key, value])=>{\n                methods.setError(key, {\n                    type: \"manual\",\n                    message: value\n                });\n            });\n        }\n    }, [\n        serverError,\n        methods\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (resetValues) {\n            methods.reset(resetValues);\n        }\n    }, [\n        resetValues,\n        methods\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: methods.handleSubmit(onSubmit),\n        noValidate: true,\n        ...props,\n        children: children(methods)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\ui\\\\form\\\\form.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/form/form.tsx\n");

/***/ }),

/***/ "./src/data/client/terms-and-condition.ts":
/*!************************************************!*\
  !*** ./src/data/client/terms-and-condition.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   termsAndConditionClients: () => (/* binding */ termsAndConditionClients)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst termsAndConditionClients = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TERMS_AND_CONDITIONS),\n    paginated: ({ title, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TERMS_AND_CONDITIONS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                title,\n                shop_id\n            })\n        });\n    },\n    approve: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.APPROVE_TERMS_AND_CONDITIONS, variables);\n    },\n    disapprove: (variables)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.DISAPPROVE_TERMS_AND_CONDITIONS, variables);\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/terms-and-condition.ts\n");

/***/ }),

/***/ "./src/data/terms-and-condition.ts":
/*!*****************************************!*\
  !*** ./src/data/terms-and-condition.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApproveTermAndConditionMutation: () => (/* binding */ useApproveTermAndConditionMutation),\n/* harmony export */   useCreateTermsAndConditionsMutation: () => (/* binding */ useCreateTermsAndConditionsMutation),\n/* harmony export */   useDeleteTermsAndConditionsMutation: () => (/* binding */ useDeleteTermsAndConditionsMutation),\n/* harmony export */   useDisApproveTermAndConditionMutation: () => (/* binding */ useDisApproveTermAndConditionMutation),\n/* harmony export */   useTermsAndConditionQuery: () => (/* binding */ useTermsAndConditionQuery),\n/* harmony export */   useTermsAndConditionsQuery: () => (/* binding */ useTermsAndConditionsQuery),\n/* harmony export */   useUpdateTermsAndConditionsMutation: () => (/* binding */ useUpdateTermsAndConditionsMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/terms-and-condition */ \"./src/data/client/terms-and-condition.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// approve terms\nconst useApproveTermAndConditionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.approve, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        }\n    });\n};\n// disapprove terms\nconst useDisApproveTermAndConditionMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.disapprove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        }\n    });\n};\n// Read Single Terms And Conditions\nconst useTermsAndConditionQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.get({\n            slug,\n            language\n        }));\n    return {\n        termsAndConditions: data,\n        error,\n        loading: isLoading\n    };\n};\n// Read All Terms And Conditions\nconst useTermsAndConditionsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        termsAndConditions: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Create Terms And Conditions\nconst useCreateTermsAndConditionsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Update Terms And Conditions\nconst useUpdateTermsAndConditionsMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.termsAndCondition.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Delete Terms And Conditions\nconst useDeleteTermsAndConditionsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_terms_and_condition__WEBPACK_IMPORTED_MODULE_8__.termsAndConditionClients.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.TERMS_AND_CONDITIONS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS90ZXJtcy1hbmQtY29uZGl0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0Q7QUFNM0I7QUFDa0I7QUFDTztBQUNVO0FBUWY7QUFDYztBQUNyQjtBQUMyQztBQUU3RSxnQkFBZ0I7QUFFVCxNQUFNWSxxQ0FBcUM7SUFDaEQsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1AsNERBQWNBO0lBQzVCLE1BQU1RLGNBQWNWLDJEQUFjQTtJQUNsQyxPQUFPRCx3REFBV0EsQ0FBQ1Esc0ZBQXdCQSxDQUFDSSxPQUFPLEVBQUU7UUFDbkRDLFdBQVc7WUFDVFgsaURBQUtBLENBQUNZLE9BQU8sQ0FBQ0osRUFBRTtRQUNsQjtRQUNBLHlDQUF5QztRQUN6Q0ssV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQ1YsZ0VBQWFBLENBQUNXLG9CQUFvQjtRQUNsRTtJQUNGO0FBQ0YsRUFBRTtBQUVGLG1CQUFtQjtBQUVaLE1BQU1DLHdDQUF3QztJQUNuRCxNQUFNLEVBQUVSLENBQUMsRUFBRSxHQUFHUCw0REFBY0E7SUFDNUIsTUFBTVEsY0FBY1YsMkRBQWNBO0lBQ2xDLE9BQU9ELHdEQUFXQSxDQUFDUSxzRkFBd0JBLENBQUNXLFVBQVUsRUFBRTtRQUN0RE4sV0FBVztZQUNUWCxpREFBS0EsQ0FBQ1ksT0FBTyxDQUFDSixFQUFFO1FBQ2xCO1FBQ0EseUNBQXlDO1FBQ3pDSyxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDVixnRUFBYUEsQ0FBQ1csb0JBQW9CO1FBQ2xFO0lBQ0Y7QUFDRixFQUFFO0FBR0YsbUNBQW1DO0FBRTVCLE1BQU1HLDRCQUE0QixDQUFDLEVBQUVDLElBQUksRUFBRUMsUUFBUSxFQUFhO0lBQ3JFLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLFNBQVMsRUFBRSxHQUFHMUIscURBQVFBLENBQ3pDO1FBQUNPLGdFQUFhQSxDQUFDVyxvQkFBb0I7UUFBRTtZQUFFSTtZQUFNQztRQUFTO0tBQUUsRUFDeEQsSUFBTWQsc0ZBQXdCQSxDQUFDa0IsR0FBRyxDQUFDO1lBQUVMO1lBQU1DO1FBQVM7SUFHdEQsT0FBTztRQUNMSyxvQkFBb0JKO1FBQ3BCQztRQUNBSSxTQUFTSDtJQUNYO0FBQ0YsRUFBRTtBQUVGLGdDQUFnQztBQUV6QixNQUFNSSw2QkFBNkIsQ0FDeENDO0lBRUEsTUFBTSxFQUFFUCxJQUFJLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFLEdBQUcxQixxREFBUUEsQ0FJekM7UUFBQ08sZ0VBQWFBLENBQUNXLG9CQUFvQjtRQUFFYTtLQUFRLEVBQzdDLENBQUMsRUFBRUMsUUFBUSxFQUFFQyxTQUFTLEVBQUUsR0FDdEJ4QixzRkFBd0JBLENBQUN5QixTQUFTLENBQ2hDQyxPQUFPQyxNQUFNLENBQUMsQ0FBQyxHQUFHSixRQUFRLENBQUMsRUFBRSxFQUFFQyxhQUVuQztRQUNFSSxrQkFBa0I7SUFDcEI7SUFHRixPQUFPO1FBQ0xULG9CQUFvQkosTUFBTUEsUUFBUSxFQUFFO1FBQ3BDYyxlQUFlakMscUVBQWdCQSxDQUFDbUI7UUFDaENDO1FBQ0FJLFNBQVNIO0lBQ1g7QUFDRixFQUFFO0FBSUYsOEJBQThCO0FBRXZCLE1BQU1hLHNDQUFzQztJQUNqRCxNQUFNM0IsY0FBY1YsMkRBQWNBO0lBQ2xDLE1BQU1zQyxTQUFTekMsc0RBQVNBO0lBQ3hCLE1BQU0sRUFBRVksQ0FBQyxFQUFFLEdBQUdQLDREQUFjQTtJQUU1QixPQUFPSCx3REFBV0EsQ0FBQ1Esc0ZBQXdCQSxDQUFDZ0MsTUFBTSxFQUFFO1FBQ2xEM0IsV0FBVztZQUNULE1BQU00QixzQkFBc0JGLE9BQU9HLEtBQUssQ0FBQ0MsSUFBSSxHQUN6QyxDQUFDLENBQUMsRUFBRUosT0FBT0csS0FBSyxDQUFDQyxJQUFJLENBQUMsRUFBRXRDLGtEQUFNQSxDQUFDdUMsaUJBQWlCLENBQUNDLElBQUksQ0FBQyxDQUFDLEdBQ3ZEeEMsa0RBQU1BLENBQUN1QyxpQkFBaUIsQ0FBQ0MsSUFBSTtZQUNqQyxNQUFNaEQsdURBQVcsQ0FBQzRDLHFCQUFxQk0sV0FBVztnQkFDaERDLFFBQVF6QywyQ0FBTUEsQ0FBQzBDLGVBQWU7WUFDaEM7WUFDQS9DLGlEQUFLQSxDQUFDWSxPQUFPLENBQUNKLEVBQUU7UUFDbEI7UUFDQSx5Q0FBeUM7UUFDekNLLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUNWLGdFQUFhQSxDQUFDVyxvQkFBb0I7UUFDbEU7UUFDQWlDLFNBQVMsQ0FBQzFCO1lBQ1J0QixpREFBS0EsQ0FBQ3NCLEtBQUssQ0FBQ2QsRUFBRSxDQUFDLE9BQU8sRUFBRWMsT0FBTzJCLFVBQVU1QixLQUFLNkIsUUFBUSxDQUFDO1FBQ3pEO0lBQ0Y7QUFDRixFQUFFO0FBRUYsOEJBQThCO0FBRXZCLE1BQU1DLHNDQUFzQztJQUNqRCxNQUFNLEVBQUUzQyxDQUFDLEVBQUUsR0FBR1AsNERBQWNBO0lBQzVCLE1BQU1RLGNBQWNWLDJEQUFjQTtJQUNsQyxNQUFNc0MsU0FBU3pDLHNEQUFTQTtJQUN4QixPQUFPRSx3REFBV0EsQ0FBQ1Esc0ZBQXdCQSxDQUFDOEMsTUFBTSxFQUFFO1FBQ2xEekMsV0FBVyxPQUFPVTtZQUNoQixNQUFNa0Isc0JBQXNCRixPQUFPRyxLQUFLLENBQUNDLElBQUksR0FDekMsQ0FBQyxDQUFDLEVBQUVKLE9BQU9HLEtBQUssQ0FBQ0MsSUFBSSxDQUFDLEVBQUV0QyxrREFBTUEsQ0FBQ3VDLGlCQUFpQixDQUFDQyxJQUFJLENBQUMsQ0FBQyxHQUN2RHhDLGtEQUFNQSxDQUFDdUMsaUJBQWlCLENBQUNDLElBQUk7WUFDakMsTUFBTU4sT0FBT08sSUFBSSxDQUFDTCxxQkFBcUJNLFdBQVc7Z0JBQ2hEQyxRQUFRekMsMkNBQU1BLENBQUMwQyxlQUFlO1lBQ2hDO1lBQ0EvQyxpREFBS0EsQ0FBQ1ksT0FBTyxDQUFDSixFQUFFO1FBQ2xCO1FBQ0EseUNBQXlDO1FBQ3pDSyxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDVixnRUFBYUEsQ0FBQ1csb0JBQW9CO1FBQ2xFO1FBQ0FpQyxTQUFTLENBQUMxQjtZQUNSdEIsaURBQUtBLENBQUNzQixLQUFLLENBQUNkLEVBQUUsQ0FBQyxPQUFPLEVBQUVjLE9BQU8yQixVQUFVNUIsS0FBSzZCLFFBQVEsQ0FBQztRQUN6RDtJQUNGO0FBQ0YsRUFBRTtBQUVGLDhCQUE4QjtBQUV2QixNQUFNRyxzQ0FBc0M7SUFDakQsTUFBTTVDLGNBQWNWLDJEQUFjQTtJQUNsQyxNQUFNLEVBQUVTLENBQUMsRUFBRSxHQUFHUCw0REFBY0E7SUFFNUIsT0FBT0gsd0RBQVdBLENBQUNRLHNGQUF3QkEsQ0FBQ2dELE1BQU0sRUFBRTtRQUNsRDNDLFdBQVc7WUFDVFgsaURBQUtBLENBQUNZLE9BQU8sQ0FBQ0osRUFBRTtRQUNsQjtRQUNBLHlDQUF5QztRQUN6Q0ssV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQ1YsZ0VBQWFBLENBQUNXLG9CQUFvQjtRQUNsRTtRQUNBaUMsU0FBUyxDQUFDMUI7WUFDUnRCLGlEQUFLQSxDQUFDc0IsS0FBSyxDQUFDZCxFQUFFLENBQUMsT0FBTyxFQUFFYyxPQUFPMkIsVUFBVTVCLEtBQUs2QixRQUFRLENBQUM7UUFDekQ7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvZGF0YS90ZXJtcy1hbmQtY29uZGl0aW9uLnRzP2FlZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJvdXRlciwgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCB7XHJcbiAgdXNlUXVlcnksXHJcbiAgdXNlSW5maW5pdGVRdWVyeSxcclxuICB1c2VNdXRhdGlvbixcclxuICB1c2VRdWVyeUNsaWVudCxcclxufSBmcm9tICdyZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtdG9hc3RpZnknO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XHJcbmltcG9ydCB7IG1hcFBhZ2luYXRvckRhdGEgfSBmcm9tICdAL3V0aWxzL2RhdGEtbWFwcGVycyc7XHJcbmltcG9ydCB0eXBlIHsgVXNlSW5maW5pdGVRdWVyeU9wdGlvbnMgfSBmcm9tICdyZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7XHJcbiAgR2V0UGFyYW1zLFxyXG4gIFRlcm1zQW5kQ29uZGl0aW9ucyxcclxuICBUZXJtc0FuZENvbmRpdGlvbnNQYWdpbmF0b3IsXHJcbiAgVGVybXNBbmRDb25kaXRpb25zUXVlcnlPcHRpb25zLFxyXG59IGZyb20gJ0AvdHlwZXMnO1xyXG5pbXBvcnQgeyBSb3V0ZXMgfSBmcm9tICdAL2NvbmZpZy9yb3V0ZXMnO1xyXG5pbXBvcnQgeyBBUElfRU5EUE9JTlRTIH0gZnJvbSAnLi9jbGllbnQvYXBpLWVuZHBvaW50cyc7XHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gJ0AvY29uZmlnJztcclxuaW1wb3J0IHsgdGVybXNBbmRDb25kaXRpb25DbGllbnRzIH0gZnJvbSAnQC9kYXRhL2NsaWVudC90ZXJtcy1hbmQtY29uZGl0aW9uJztcclxuXHJcbi8vIGFwcHJvdmUgdGVybXNcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VBcHByb3ZlVGVybUFuZENvbmRpdGlvbk11dGF0aW9uID0gKCkgPT4ge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XHJcbiAgcmV0dXJuIHVzZU11dGF0aW9uKHRlcm1zQW5kQ29uZGl0aW9uQ2xpZW50cy5hcHByb3ZlLCB7XHJcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgdG9hc3Quc3VjY2Vzcyh0KCdjb21tb246c3VjY2Vzc2Z1bGx5LXVwZGF0ZWQnKSk7XHJcbiAgICB9LFxyXG4gICAgLy8gQWx3YXlzIHJlZmV0Y2ggYWZ0ZXIgZXJyb3Igb3Igc3VjY2VzczpcclxuICAgIG9uU2V0dGxlZDogKCkgPT4ge1xyXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyhBUElfRU5EUE9JTlRTLlRFUk1TX0FORF9DT05ESVRJT05TKTtcclxuICAgIH0sXHJcbiAgfSk7XHJcbn07XHJcblxyXG4vLyBkaXNhcHByb3ZlIHRlcm1zXHJcblxyXG5leHBvcnQgY29uc3QgdXNlRGlzQXBwcm92ZVRlcm1BbmRDb25kaXRpb25NdXRhdGlvbiA9ICgpID0+IHtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XHJcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xyXG4gIHJldHVybiB1c2VNdXRhdGlvbih0ZXJtc0FuZENvbmRpdGlvbkNsaWVudHMuZGlzYXBwcm92ZSwge1xyXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3ModCgnY29tbW9uOnN1Y2Nlc3NmdWxseS11cGRhdGVkJykpO1xyXG4gICAgfSxcclxuICAgIC8vIEFsd2F5cyByZWZldGNoIGFmdGVyIGVycm9yIG9yIHN1Y2Nlc3M6XHJcbiAgICBvblNldHRsZWQ6ICgpID0+IHtcclxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoQVBJX0VORFBPSU5UUy5URVJNU19BTkRfQ09ORElUSU9OUyk7XHJcbiAgICB9LFxyXG4gIH0pO1xyXG59O1xyXG5cclxuXHJcbi8vIFJlYWQgU2luZ2xlIFRlcm1zIEFuZCBDb25kaXRpb25zXHJcblxyXG5leHBvcnQgY29uc3QgdXNlVGVybXNBbmRDb25kaXRpb25RdWVyeSA9ICh7IHNsdWcsIGxhbmd1YWdlIH06IEdldFBhcmFtcykgPT4ge1xyXG4gIGNvbnN0IHsgZGF0YSwgZXJyb3IsIGlzTG9hZGluZyB9ID0gdXNlUXVlcnk8VGVybXNBbmRDb25kaXRpb25zLCBFcnJvcj4oXHJcbiAgICBbQVBJX0VORFBPSU5UUy5URVJNU19BTkRfQ09ORElUSU9OUywgeyBzbHVnLCBsYW5ndWFnZSB9XSxcclxuICAgICgpID0+IHRlcm1zQW5kQ29uZGl0aW9uQ2xpZW50cy5nZXQoeyBzbHVnLCBsYW5ndWFnZSB9KVxyXG4gICk7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICB0ZXJtc0FuZENvbmRpdGlvbnM6IGRhdGEsXHJcbiAgICBlcnJvcixcclxuICAgIGxvYWRpbmc6IGlzTG9hZGluZyxcclxuICB9O1xyXG59O1xyXG5cclxuLy8gUmVhZCBBbGwgVGVybXMgQW5kIENvbmRpdGlvbnNcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VUZXJtc0FuZENvbmRpdGlvbnNRdWVyeSA9IChcclxuICBvcHRpb25zOiBQYXJ0aWFsPFRlcm1zQW5kQ29uZGl0aW9uc1F1ZXJ5T3B0aW9ucz5cclxuKSA9PiB7XHJcbiAgY29uc3QgeyBkYXRhLCBlcnJvciwgaXNMb2FkaW5nIH0gPSB1c2VRdWVyeTxcclxuICAgIFRlcm1zQW5kQ29uZGl0aW9uc1BhZ2luYXRvcixcclxuICAgIEVycm9yXHJcbiAgPihcclxuICAgIFtBUElfRU5EUE9JTlRTLlRFUk1TX0FORF9DT05ESVRJT05TLCBvcHRpb25zXSxcclxuICAgICh7IHF1ZXJ5S2V5LCBwYWdlUGFyYW0gfSkgPT5cclxuICAgICAgdGVybXNBbmRDb25kaXRpb25DbGllbnRzLnBhZ2luYXRlZChcclxuICAgICAgICBPYmplY3QuYXNzaWduKHt9LCBxdWVyeUtleVsxXSwgcGFnZVBhcmFtKVxyXG4gICAgICApLFxyXG4gICAge1xyXG4gICAgICBrZWVwUHJldmlvdXNEYXRhOiB0cnVlLFxyXG4gICAgfVxyXG4gICk7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICB0ZXJtc0FuZENvbmRpdGlvbnM6IGRhdGE/LmRhdGEgPz8gW10sXHJcbiAgICBwYWdpbmF0b3JJbmZvOiBtYXBQYWdpbmF0b3JEYXRhKGRhdGEpLFxyXG4gICAgZXJyb3IsXHJcbiAgICBsb2FkaW5nOiBpc0xvYWRpbmcsXHJcbiAgfTtcclxufTtcclxuXHJcblxyXG5cclxuLy8gQ3JlYXRlIFRlcm1zIEFuZCBDb25kaXRpb25zXHJcblxyXG5leHBvcnQgY29uc3QgdXNlQ3JlYXRlVGVybXNBbmRDb25kaXRpb25zTXV0YXRpb24gPSAoKSA9PiB7XHJcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuXHJcbiAgcmV0dXJuIHVzZU11dGF0aW9uKHRlcm1zQW5kQ29uZGl0aW9uQ2xpZW50cy5jcmVhdGUsIHtcclxuICAgIG9uU3VjY2VzczogYXN5bmMgKCkgPT4ge1xyXG4gICAgICBjb25zdCBnZW5lcmF0ZVJlZGlyZWN0VXJsID0gcm91dGVyLnF1ZXJ5LnNob3BcclxuICAgICAgICA/IGAvJHtyb3V0ZXIucXVlcnkuc2hvcH0ke1JvdXRlcy50ZXJtc0FuZENvbmRpdGlvbi5saXN0fWBcclxuICAgICAgICA6IFJvdXRlcy50ZXJtc0FuZENvbmRpdGlvbi5saXN0O1xyXG4gICAgICBhd2FpdCBSb3V0ZXIucHVzaChnZW5lcmF0ZVJlZGlyZWN0VXJsLCB1bmRlZmluZWQsIHtcclxuICAgICAgICBsb2NhbGU6IENvbmZpZy5kZWZhdWx0TGFuZ3VhZ2UsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKHQoJ2NvbW1vbjpzdWNjZXNzZnVsbHktY3JlYXRlZCcpKTtcclxuICAgIH0sXHJcbiAgICAvLyBBbHdheXMgcmVmZXRjaCBhZnRlciBlcnJvciBvciBzdWNjZXNzOlxyXG4gICAgb25TZXR0bGVkOiAoKSA9PiB7XHJcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKEFQSV9FTkRQT0lOVFMuVEVSTVNfQU5EX0NPTkRJVElPTlMpO1xyXG4gICAgfSxcclxuICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgIHRvYXN0LmVycm9yKHQoYGNvbW1vbjoke2Vycm9yPy5yZXNwb25zZT8uZGF0YS5tZXNzYWdlfWApKTtcclxuICAgIH0sXHJcbiAgfSk7XHJcbn07XHJcblxyXG4vLyBVcGRhdGUgVGVybXMgQW5kIENvbmRpdGlvbnNcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VVcGRhdGVUZXJtc0FuZENvbmRpdGlvbnNNdXRhdGlvbiA9ICgpID0+IHtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XHJcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIHJldHVybiB1c2VNdXRhdGlvbih0ZXJtc0FuZENvbmRpdGlvbkNsaWVudHMudXBkYXRlLCB7XHJcbiAgICBvblN1Y2Nlc3M6IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcclxuICAgICAgY29uc3QgZ2VuZXJhdGVSZWRpcmVjdFVybCA9IHJvdXRlci5xdWVyeS5zaG9wXHJcbiAgICAgICAgPyBgLyR7cm91dGVyLnF1ZXJ5LnNob3B9JHtSb3V0ZXMudGVybXNBbmRDb25kaXRpb24ubGlzdH1gXHJcbiAgICAgICAgOiBSb3V0ZXMudGVybXNBbmRDb25kaXRpb24ubGlzdDtcclxuICAgICAgYXdhaXQgcm91dGVyLnB1c2goZ2VuZXJhdGVSZWRpcmVjdFVybCwgdW5kZWZpbmVkLCB7XHJcbiAgICAgICAgbG9jYWxlOiBDb25maWcuZGVmYXVsdExhbmd1YWdlLFxyXG4gICAgICB9KTtcclxuICAgICAgdG9hc3Quc3VjY2Vzcyh0KCdjb21tb246c3VjY2Vzc2Z1bGx5LXVwZGF0ZWQnKSk7XHJcbiAgICB9LFxyXG4gICAgLy8gQWx3YXlzIHJlZmV0Y2ggYWZ0ZXIgZXJyb3Igb3Igc3VjY2VzczpcclxuICAgIG9uU2V0dGxlZDogKCkgPT4ge1xyXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyhBUElfRU5EUE9JTlRTLlRFUk1TX0FORF9DT05ESVRJT05TKTtcclxuICAgIH0sXHJcbiAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICB0b2FzdC5lcnJvcih0KGBjb21tb246JHtlcnJvcj8ucmVzcG9uc2U/LmRhdGEubWVzc2FnZX1gKSk7XHJcbiAgICB9LFxyXG4gIH0pO1xyXG59O1xyXG5cclxuLy8gRGVsZXRlIFRlcm1zIEFuZCBDb25kaXRpb25zXHJcblxyXG5leHBvcnQgY29uc3QgdXNlRGVsZXRlVGVybXNBbmRDb25kaXRpb25zTXV0YXRpb24gPSAoKSA9PiB7XHJcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuXHJcbiAgcmV0dXJuIHVzZU11dGF0aW9uKHRlcm1zQW5kQ29uZGl0aW9uQ2xpZW50cy5kZWxldGUsIHtcclxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKHQoJ2NvbW1vbjpzdWNjZXNzZnVsbHktZGVsZXRlZCcpKTtcclxuICAgIH0sXHJcbiAgICAvLyBBbHdheXMgcmVmZXRjaCBhZnRlciBlcnJvciBvciBzdWNjZXNzOlxyXG4gICAgb25TZXR0bGVkOiAoKSA9PiB7XHJcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKEFQSV9FTkRQT0lOVFMuVEVSTVNfQU5EX0NPTkRJVElPTlMpO1xyXG4gICAgfSxcclxuICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgIHRvYXN0LmVycm9yKHQoYGNvbW1vbjoke2Vycm9yPy5yZXNwb25zZT8uZGF0YS5tZXNzYWdlfWApKTtcclxuICAgIH0sXHJcbiAgfSk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSb3V0ZXIiLCJ1c2VSb3V0ZXIiLCJ1c2VRdWVyeSIsInVzZU11dGF0aW9uIiwidXNlUXVlcnlDbGllbnQiLCJ0b2FzdCIsInVzZVRyYW5zbGF0aW9uIiwibWFwUGFnaW5hdG9yRGF0YSIsIlJvdXRlcyIsIkFQSV9FTkRQT0lOVFMiLCJDb25maWciLCJ0ZXJtc0FuZENvbmRpdGlvbkNsaWVudHMiLCJ1c2VBcHByb3ZlVGVybUFuZENvbmRpdGlvbk11dGF0aW9uIiwidCIsInF1ZXJ5Q2xpZW50IiwiYXBwcm92ZSIsIm9uU3VjY2VzcyIsInN1Y2Nlc3MiLCJvblNldHRsZWQiLCJpbnZhbGlkYXRlUXVlcmllcyIsIlRFUk1TX0FORF9DT05ESVRJT05TIiwidXNlRGlzQXBwcm92ZVRlcm1BbmRDb25kaXRpb25NdXRhdGlvbiIsImRpc2FwcHJvdmUiLCJ1c2VUZXJtc0FuZENvbmRpdGlvblF1ZXJ5Iiwic2x1ZyIsImxhbmd1YWdlIiwiZGF0YSIsImVycm9yIiwiaXNMb2FkaW5nIiwiZ2V0IiwidGVybXNBbmRDb25kaXRpb25zIiwibG9hZGluZyIsInVzZVRlcm1zQW5kQ29uZGl0aW9uc1F1ZXJ5Iiwib3B0aW9ucyIsInF1ZXJ5S2V5IiwicGFnZVBhcmFtIiwicGFnaW5hdGVkIiwiT2JqZWN0IiwiYXNzaWduIiwia2VlcFByZXZpb3VzRGF0YSIsInBhZ2luYXRvckluZm8iLCJ1c2VDcmVhdGVUZXJtc0FuZENvbmRpdGlvbnNNdXRhdGlvbiIsInJvdXRlciIsImNyZWF0ZSIsImdlbmVyYXRlUmVkaXJlY3RVcmwiLCJxdWVyeSIsInNob3AiLCJ0ZXJtc0FuZENvbmRpdGlvbiIsImxpc3QiLCJwdXNoIiwidW5kZWZpbmVkIiwibG9jYWxlIiwiZGVmYXVsdExhbmd1YWdlIiwib25FcnJvciIsInJlc3BvbnNlIiwibWVzc2FnZSIsInVzZVVwZGF0ZVRlcm1zQW5kQ29uZGl0aW9uc011dGF0aW9uIiwidXBkYXRlIiwidXNlRGVsZXRlVGVybXNBbmRDb25kaXRpb25zTXV0YXRpb24iLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/data/terms-and-condition.ts\n");

/***/ })

};
;