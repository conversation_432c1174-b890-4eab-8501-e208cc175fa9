/** @type {import('next').NextConfig} */
const { i18n } = require('./next-i18next.config');

module.exports = {
  reactStrictMode: false,
  i18n,
  output: 'standalone',
  images: {
    domains: [
      'pickbazarlaravel.s3.ap-southeast-1.amazonaws.com',
      'pixarlaravel.s3.ap-southeast-1.amazonaws.com',
      'lh3.googleusercontent.com',
      'localhost',
      '127.0.0.1',
      'i.pravatar.cc',
      'minio',
      'minio:9000',
    ],
  },
  // Optimize for development and prevent hanging
  experimental: {
    workerThreads: false,
    cpus: 1,
  },
  // Add timeout for API calls during SSR
  serverRuntimeConfig: {
    apiTimeout: 5000, // 5 second timeout for API calls
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Optimize build performance
  swcMinify: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};
