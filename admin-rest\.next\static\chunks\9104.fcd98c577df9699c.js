"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9104],{59104:function(e,t,s){s.r(t);var a=s(85893),u=s(71421),o=s(75814),n=s(60385);t.default=()=>{let{mutate:e,isLoading:t}=(0,n.Hk)(),{data:s}=(0,o.X9)(),{closeModal:r}=(0,o.SO)();return(0,a.jsx)(u.Z,{onCancel:r,onDelete:function(){e({id:s}),r()},deleteBtnLoading:t})}},60385:function(e,t,s){s.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var a=s(11163),u=s.n(a),o=s(88767),n=s(22920),r=s(5233),l=s(28597),i=s(97514),c=s(47869),S=s(93345),d=s(55191),F=s(3737);let _={...(0,d.h)(c.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return F.eN.get(c.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...s,search:F.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:s,shop_id:a}=e;return F.eN.get("".concat(c.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:s,shop_id:a,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:s,...a}=e;return F.eN.get(c.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:s,...a,search:F.eN.formatSearchParams({title:t,shop_id:s})})},approve:e=>F.eN.post(c.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>F.eN.post(c.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...s}=e;return F.eN.get(c.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...s,search:F.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:s,error:a,isLoading:u}=(0,o.useQuery)([c.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return _.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(s),error:a,loading:u}},useRequestedListForFlashSale=e=>{let{id:t,language:s,shop_id:a}=e,{data:u,error:n,isLoading:r}=(0,o.useQuery)([c.P.FLASH_SALE,{id:t,language:s,shop_id:a}],()=>_.get({id:t,language:s,shop_id:a}));return{flashSaleRequest:u,error:n,loading:r}},useRequestedProductsForFlashSale=e=>{var t;let{data:s,error:a,isLoading:u}=(0,o.useQuery)([c.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return _.requestedProducts(Object.assign({},t[1],s))},{keepPreviousData:!0});return{products:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(s),error:a,loading:u}},useCreateFlashSaleRequestMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,a.useRouter)(),{t:s}=(0,r.$G)();return(0,o.useMutation)(_.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await u().push(e,void 0,{locale:S.Config.defaultLanguage}),n.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;n.Am.error(s("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)(),s=(0,a.useRouter)();return(0,o.useMutation)(_.update,{onSuccess:async t=>{let a=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await s.push(a,void 0,{locale:S.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var s;n.Am.error(e("common:".concat(null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,r.$G)();return(0,o.useMutation)(_.delete,{onSuccess:()=>{n.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var s;n.Am.error(t("common:".concat(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)(),s=(0,a.useRouter)();return(0,o.useMutation)(_.approve,{onSuccess:async()=>{let t=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await u().push(t,void 0,{locale:S.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(_.disapprove,{onSuccess:()=>{n.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)}})}}}]);