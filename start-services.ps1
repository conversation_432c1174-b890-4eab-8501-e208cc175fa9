Write-Host "🚀 Starting E-commerce Services..." -ForegroundColor Green

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.simple.yml down

# Start database and MinIO services
Write-Host "🗄️ Starting database and MinIO services..." -ForegroundColor Blue
docker-compose -f docker-compose.simple.yml up -d postgres minio

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# Start API service
Write-Host "🔧 Starting API service..." -ForegroundColor Blue
docker-compose -f docker-compose.simple.yml up -d api-rest

# Wait for API to be ready
Write-Host "⏳ Waiting for API to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 15

# Seed database
Write-Host "🌱 Seeding database..." -ForegroundColor Magenta
docker-compose -f docker-compose.simple.yml exec api-rest node simple-seed.js

Write-Host "✅ Backend services are ready!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Backend Services:" -ForegroundColor White
Write-Host "   • API: http://localhost:5000/api" -ForegroundColor Cyan
Write-Host "   • API Docs: http://localhost:5000/docs" -ForegroundColor Cyan
Write-Host "   • MinIO Console: http://localhost:9001" -ForegroundColor Cyan
Write-Host "   • MinIO API: http://localhost:9000" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔐 MinIO Credentials:" -ForegroundColor White
Write-Host "   • Username: minioadmin" -ForegroundColor Yellow
Write-Host "   • Password: minioadmin123" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor White
Write-Host "   1. Start admin panel: cd admin-rest; yarn dev" -ForegroundColor Gray
Write-Host "   2. Start shop: cd shop; yarn dev:rest" -ForegroundColor Gray
Write-Host ""
Write-Host "Frontend URLs (after starting):" -ForegroundColor White
Write-Host "   • Admin Panel: http://localhost:3002" -ForegroundColor Gray
Write-Host "   • Shop: http://localhost:3003" -ForegroundColor Gray

Read-Host "Press Enter to continue"
