(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2161],{58385:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/flash-sale/[slug]",function(){return r(20351)}])},13202:function(e,t,r){"use strict";r.d(t,{M:function(){return o}});var a=r(47869),u=r(55191),n=r(3737);let o={...(0,u.h)(a.P.PRODUCTS),get(e){let{slug:t,language:r}=e;return n.eN.get("".concat(a.P.PRODUCTS,"/").concat(t),{language:r,with:"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file"})},paginated:e=>{let{type:t,name:r,categories:u,shop_id:o,product_type:s,status:c,...l}=e;return n.eN.get(a.P.PRODUCTS,{searchJoin:"and",with:"shop;type;categories",shop_id:o,...l,search:n.eN.formatSearchParams({type:t,name:r,categories:u,shop_id:o,product_type:s,status:c})})},popular(e){let{shop_id:t,...r}=e;return n.eN.get(a.P.POPULAR_PRODUCTS,{searchJoin:"and",with:"type;shop",...r,search:n.eN.formatSearchParams({shop_id:t})})},lowStock(e){let{shop_id:t,...r}=e;return n.eN.get(a.P.LOW_STOCK_PRODUCTS_ANALYTICS,{searchJoin:"and",with:"type;shop",...r,search:n.eN.formatSearchParams({shop_id:t})})},generateDescription:e=>n.eN.post(a.P.GENERATE_DESCRIPTION,e),newOrInActiveProducts:e=>{let{user_id:t,shop_id:r,status:u,name:o,...s}=e;return n.eN.get(a.P.NEW_OR_INACTIVE_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:u,name:o,...s,search:n.eN.formatSearchParams({status:u,name:o})})},lowOrOutOfStockProducts:e=>{let{user_id:t,shop_id:r,status:u,categories:o,name:s,type:c,...l}=e;return n.eN.get(a.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:u,name:s,...l,search:n.eN.formatSearchParams({status:u,name:s,categories:o,type:c})})},productByCategory(e){let{limit:t,language:r}=e;return n.eN.get(a.P.CATEGORY_WISE_PRODUCTS,{limit:t,language:r})},mostSoldProductByCategory(e){let{shop_id:t,...r}=e;return n.eN.get(a.P.CATEGORY_WISE_PRODUCTS_SALE,{searchJoin:"and",...r,search:n.eN.formatSearchParams({shop_id:t})})},getProductsByFlashSale:e=>{let{user_id:t,shop_id:r,slug:u,name:o,...s}=e;return n.eN.get(a.P.PRODUCTS_BY_FLASH_SALE,{searchJoin:"and",user_id:t,shop_id:r,slug:u,name:o,...s,search:n.eN.formatSearchParams({name:o})})},topRated(e){let{shop_id:t,...r}=e;return n.eN.get(a.P.TOP_RATED_PRODUCTS,{searchJoin:"and",...r,search:n.eN.formatSearchParams({shop_id:t})})}}},93242:function(e,t,r){"use strict";r.d(t,{FA:function(){return useProductQuery},Uc:function(){return useProductsByFlashSaleQuery},YC:function(){return useInActiveProductsQuery},bJ:function(){return useGenerateDescriptionMutation},eH:function(){return useProductStockQuery},kN:function(){return useProductsQuery},qX:function(){return useCreateProductMutation},wE:function(){return useUpdateProductMutation},xq:function(){return useDeleteProductMutation}});var a=r(11163),u=r.n(a),n=r(22920),o=r(5233),s=r(88767),c=r(47869),l=r(13202),i=r(28597),d=r(97514),P=r(93345);let useCreateProductMutation=()=>{let e=(0,s.useQueryClient)(),t=(0,a.useRouter)(),{t:r}=(0,o.$G)();return(0,s.useMutation)(l.M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.product.list):d.Z.product.list;await u().push(e,void 0,{locale:P.Config.defaultLanguage}),n.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{let{data:t,status:a}=null==e?void 0:e.response;if(422===a){let e=Object.values(t).flat();n.Am.error(e[0])}else{var u;n.Am.error(r("common:".concat(null==e?void 0:null===(u=e.response)||void 0===u?void 0:u.data.message)))}}})},useUpdateProductMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,s.useQueryClient)(),r=(0,a.useRouter)();return(0,s.useMutation)(l.M.update,{onSuccess:async t=>{let a=r.query.shop?"/".concat(r.query.shop).concat(d.Z.product.list):d.Z.product.list;await r.push("".concat(a,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:P.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.PRODUCTS)},onError:t=>{var r;n.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteProductMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)();return(0,s.useMutation)(l.M.delete,{onSuccess:()=>{n.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{var r;n.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})},useProductQuery=e=>{let{slug:t,language:r}=e,{data:a,error:u,isLoading:n}=(0,s.useQuery)([c.P.PRODUCTS,{slug:t,language:r}],()=>l.M.get({slug:t,language:r}));return{product:a,error:u,isLoading:n}},useProductsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:a,error:u,isLoading:n}=(0,s.useQuery)([c.P.PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{products:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(a),error:u,loading:n}},useGenerateDescriptionMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)("common");return(0,s.useMutation)(l.M.generateDescription,{onSuccess:()=>{n.Am.success(t("Generated..."))},onSettled:t=>{e.refetchQueries(c.P.GENERATE_DESCRIPTION)}})},useInActiveProductsQuery=e=>{var t;let{data:r,error:a,isLoading:u}=(0,s.useQuery)([c.P.NEW_OR_INACTIVE_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.newOrInActiveProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:a,loading:u}},useProductStockQuery=e=>{var t;let{data:r,error:a,isLoading:u}=(0,s.useQuery)([c.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.lowOrOutOfStockProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:a,loading:u}},useProductsByFlashSaleQuery=e=>{var t;let{data:r,error:a,isLoading:u}=(0,s.useQuery)([c.P.PRODUCTS_BY_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.getProductsByFlashSale(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:a,loading:u}}},20351:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSP:function(){return T}});var a=r(85893),u=r(97670),n=r(45957),o=r(55846),s=r(5233),c=r(11163),l=r(27484),i=r.n(l),d=r(84110),P=r.n(d),S=r(70178),g=r.n(S),h=r(29387),p=r.n(h),O=r(80246),v=r(34739),_=r(16203),m=r(90255),f=r(93242),C=r(67294),y=r(10265);i().extend(P()),i().extend(g()),i().extend(p());let FlashSalePage=()=>{let[e,t]=(0,C.useState)(""),[r,u]=(0,C.useState)("created_at"),[l,i]=(0,C.useState)(y.As.Desc),[d,P]=(0,C.useState)(1),[S,g]=(0,C.useState)(5),{query:h,locale:p}=(0,c.useRouter)(),{t:_}=(0,s.$G)(),{flashSale:T,loading:N,error:R}=(0,O.gr)({slug:null==h?void 0:h.slug,language:p}),{products:D,paginatorInfo:A,error:E,loading:Q}=(0,f.Uc)({name:e,slug:null==h?void 0:h.slug,language:p,limit:null!=S?S:5,page:null!=d?d:1}),U=(0,C.useCallback)(e=>{g(null==e?void 0:e.value),P(1)},[g,P]),M=(0,C.useCallback)(e=>{let{searchText:r}=e;t(r),P(1)},[t,P]),I=(0,C.useCallback)(e=>{P(e)},[P]);return N||Q?(0,a.jsx)(o.Z,{text:_("common:text-loading")}):R?(0,a.jsx)(n.Z,{message:R.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{data:T,className:"mb-10"}),(0,a.jsx)(v.Z,{products:D,paginatorInfo:A,onPagination:I,onOrder:u,onSort:i,handleSearch:M,handleOnLimitChange:U,type:null==T?void 0:T.type,rate:null==T?void 0:T.rate})]})};FlashSalePage.authenticate={permissions:_.M$},FlashSalePage.Layout=u.default;var T=!0;t.default=FlashSalePage}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,1036,9494,5535,8186,1285,1631,7556,4928,3539,9774,2888,179],function(){return e(e.s=58385)}),_N_E=e.O()}]);