"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6705],{96705:function(e,t,a){a.r(t);var n=a(85893),u=a(71421),s=a(75814),o=a(92001);t.default=()=>{let{mutate:e,isLoading:t}=(0,o.V7)(),{data:a}=(0,s.X9)(),{closeModal:r}=(0,s.SO)();return(0,n.jsx)(u.Z,{onCancel:r,onDelete:function(){e({id:a}),r()},deleteBtnLoading:t})}},92001:function(e,t,a){a.d(t,{Mf:function(){return useCreateFaqsMutation},V7:function(){return useDeleteFaqsMutation},cb:function(){return useFaqQuery},uo:function(){return useFaqsQuery},Zg:function(){return useUpdateFaqsMutation}});var n=a(11163),u=a.n(n),s=a(88767),o=a(22920),r=a(5233),c=a(28597),l=a(97514),i=a(47869),d=a(93345),f=a(55191),m=a(3737);let g={...(0,f.h)(i.P.FAQS),all:function(){let{faq_title:e,shop_id:t,...a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return m.eN.get(i.P.FAQS,{searchJoin:"and",shop_id:t,...a,search:m.eN.formatSearchParams({faq_title:e,shop_id:t})})},get(e){let{id:t,language:a}=e;return m.eN.get("".concat(i.P.FAQS,"/").concat(t),{language:a})},paginated:e=>{let{faq_title:t,shop_id:a,...n}=e;return m.eN.get(i.P.FAQS,{searchJoin:"and",shop_id:a,...n,search:m.eN.formatSearchParams({faq_title:t,shop_id:a})})}},useFaqQuery=e=>{let{id:t,language:a}=e,{data:n,error:u,isLoading:o}=(0,s.useQuery)([i.P.FAQS,{id:t,language:a}],()=>g.get({id:t,language:a}));return{faqs:n,error:u,loading:o}},useFaqsQuery=e=>{var t;let{data:a,error:n,isLoading:u}=(0,s.useQuery)([i.P.FAQS,e],e=>{let{queryKey:t,pageParam:a}=e;return g.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{faqs:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(a),error:n,loading:u}},useCreateFaqsMutation=()=>{let e=(0,s.useQueryClient)(),t=(0,n.useRouter)(),{t:a}=(0,r.$G)();return(0,s.useMutation)(g.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(l.Z.faqs.list):l.Z.faqs.list;await u().push(e,void 0,{locale:d.Config.defaultLanguage}),o.Am.success(a("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.FAQS)},onError:e=>{var t;o.Am.error(a("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFaqsMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,s.useQueryClient)(),a=(0,n.useRouter)();return(0,s.useMutation)(g.update,{onSuccess:async t=>{let n=a.query.shop?"/".concat(a.query.shop).concat(l.Z.faqs.list):l.Z.faqs.list;await a.push(n,void 0,{locale:d.Config.defaultLanguage}),o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.FAQS)},onError:t=>{var a;o.Am.error(e("common:".concat(null==t?void 0:null===(a=t.response)||void 0===a?void 0:a.data.message)))}})},useDeleteFaqsMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,r.$G)();return(0,s.useMutation)(g.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.FAQS)},onError:e=>{var a;o.Am.error(t("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}})}}}]);