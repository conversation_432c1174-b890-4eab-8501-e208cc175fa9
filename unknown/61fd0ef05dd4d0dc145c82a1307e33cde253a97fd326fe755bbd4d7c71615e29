import type {
  CategoryQueryOptions,
  HomePageProps,
  PopularProductQueryOptions,
  SettingsQueryOptions,
  TypeQueryOptions,
  BestSellingProductQueryOptions,
} from '@/types';
import type { GetStaticPaths, GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { QueryClient } from 'react-query';
import { dehydrate } from 'react-query/hydration';
import invariant from 'tiny-invariant';
import client from './client';
import { API_ENDPOINTS } from './client/api-endpoints';
import {
  CATEGORIES_PER_PAGE,
  PRODUCTS_PER_PAGE,
  TYPES_PER_PAGE,
} from './client/variables';

type ParsedQueryParams = {
  pages: string[];
};

// This function gets called at build time
export const getStaticPaths: GetStaticPaths<ParsedQueryParams> = async ({
  locales,
}) => {
  invariant(locales, 'locales is not defined');

  // For development, return minimal paths to avoid build hanging
  // In production, you can enable full path generation
  console.log('Generating static paths for locales:', locales);

  return {
    paths: locales?.map((locale) => ({ params: { pages: [] }, locale })) || [],
    fallback: 'blocking', // This allows dynamic generation of other paths
  };
};

// Helper function to create a timeout promise
const withTimeout = <T>(promise: Promise<T>, timeoutMs: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), timeoutMs),
    ),
  ]);
};

export const getStaticProps: GetStaticProps<
  HomePageProps,
  ParsedQueryParams
> = async ({ locale, params }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        retryDelay: 1000,
        staleTime: 5 * 60 * 1000, // 5 minutes
      },
    },
  });

  // Fetch settings with timeout
  try {
    await withTimeout(
      queryClient.prefetchQuery(
        [API_ENDPOINTS.SETTINGS, { language: locale }],
        ({ queryKey }) =>
          client.settings.all(queryKey[1] as SettingsQueryOptions),
      ),
      5000, // 5 second timeout
    );
  } catch (error) {
    console.warn('Failed to fetch settings:', error);
  }

  // Fetch types with timeout
  let types: any[] = [];
  try {
    types = await withTimeout(
      queryClient.fetchQuery(
        [API_ENDPOINTS.TYPES, { limit: TYPES_PER_PAGE, language: locale }],
        ({ queryKey }) => client.types.all(queryKey[1] as TypeQueryOptions),
      ),
      5000, // 5 second timeout
    );
  } catch (error) {
    console.warn('Failed to fetch types:', error);
    types = [];
  }

  const { pages } = params!;
  let pageType: string | undefined;

  // Handle case when no types exist in database
  if (!types || types.length === 0) {
    pageType = 'default'; // Use a default type when no types exist
  } else if (!pages) {
    pageType =
      types.find((type) => type?.settings?.isHome)?.slug ?? types?.[0]?.slug;
  } else {
    pageType = pages[0];
  }

  // Only return notFound if we have types but the requested type doesn't exist
  if (
    types &&
    types.length > 0 &&
    pageType !== 'default' &&
    !types?.some((t) => t.slug === pageType)
  ) {
    return {
      notFound: true,
      // This is require to regenerate the page
      revalidate: 120,
    };
  }

  // Only fetch type details if it's not the default fallback
  if (pageType !== 'default') {
    try {
      await withTimeout(
        queryClient.prefetchQuery(
          [API_ENDPOINTS.TYPES, { slug: pageType, language: locale }],
          ({ queryKey }: any) => client.types.get(queryKey[1]),
        ),
        3000, // 3 second timeout
      );
    } catch (error) {
      console.warn('Failed to fetch type details:', error);
    }
  }

  const productVariables = {
    type: pageType === 'default' ? undefined : pageType,
    limit: PRODUCTS_PER_PAGE,
  };

  try {
    await withTimeout(
      queryClient.prefetchInfiniteQuery(
        [
          API_ENDPOINTS.PRODUCTS,
          {
            limit: PRODUCTS_PER_PAGE,
            type: pageType === 'default' ? undefined : pageType,
            language: locale,
          },
        ],
        ({ queryKey }) => client.products.all(queryKey[1] as any),
      ),
      3000, // 3 second timeout
    );
  } catch (error) {
    console.warn('Failed to fetch products:', error);
  }

  const popularProductVariables = {
    ...(pageType !== 'default' && { type_slug: pageType }),
    limit: 10,
    with: 'type;author',
    language: locale,
  };

  // Only prefetch popular products for `book` demo
  if (pageType === 'book') {
    try {
      await withTimeout(
        Promise.all([
          queryClient.prefetchQuery(
            [API_ENDPOINTS.PRODUCTS_POPULAR, popularProductVariables],
            ({ queryKey }) =>
              client.products.popular(
                queryKey[1] as PopularProductQueryOptions,
              ),
          ),
          queryClient.prefetchQuery(
            [API_ENDPOINTS.BEST_SELLING_PRODUCTS, popularProductVariables],
            ({ queryKey }) =>
              client.products.bestSelling(
                queryKey[1] as BestSellingProductQueryOptions,
              ),
          ),
        ]),
        3000, // 3 second timeout for both requests
      );
    } catch (error) {
      console.warn('Failed to fetch popular/bestselling products:', error);
    }
  }

  const currentType = types?.find((t) => t.slug === pageType);
  const categoryVariables = {
    type: pageType === 'default' ? undefined : pageType,
    limit: CATEGORIES_PER_PAGE,
    language: locale,
    parent: currentType?.settings?.layoutType === 'minimal' ? 'all' : 'null',
  };

  try {
    await withTimeout(
      queryClient.prefetchInfiniteQuery(
        [API_ENDPOINTS.CATEGORIES, categoryVariables],
        ({ queryKey }) =>
          client.categories.all(queryKey[1] as CategoryQueryOptions),
      ),
      3000, // 3 second timeout
    );
  } catch (error) {
    console.warn('Failed to fetch categories:', error);
  }

  return {
    props: {
      variables: {
        popularProducts: popularProductVariables,
        products: productVariables,
        categories: categoryVariables,
        bestSellingProducts: popularProductVariables,
        layoutSettings: {
          ...currentType?.settings,
        },
        types: {
          type: pageType,
        },
      },
      layout: currentType?.settings?.layoutType ?? 'classic',
      ...(await serverSideTranslations(locale!, ['common', 'banner'])),
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),
    },
    revalidate: 120,
  };
};

/* Fix : locales: 14kB,
popularProducts: 30kB,
category: 22kB,
groups: 8kB,
group: 2kB,
settings: 2kB,
perProduct: 4.2 * 30 = 120kB,
total = 14 + 30 + 22 + 8 + 2 + 2 + 120 = 198kB
others: 225 - 198 = 27kB

 */
