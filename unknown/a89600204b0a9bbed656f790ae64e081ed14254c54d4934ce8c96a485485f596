{"name": "@marvel-mock/api-rest", "version": "11.10.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:migrate": "sequelize-cli db:migrate", "db:migrate:status": "sequelize-cli db:migrate:status", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:migrate:undo:all": "sequelize-cli db:migrate:undo:all", "db:seed": "sequelize-cli db:seed:all", "db:seed:undo": "sequelize-cli db:seed:undo:all", "migration:generate": "sequelize-cli migration:generate --name"}, "dependencies": {"@nestjs/common": "^9.0.11", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.11", "@nestjs/mapped-types": "1.1.0", "@nestjs/platform-express": "^9.0.11", "@nestjs/sequelize": "^11.0.0", "@nestjs/swagger": "^6.0.5", "@paypal/checkout-server-sdk": "^1.0.3", "@types/uuid": "^10.0.0", "axios": "^1.2.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "fuse.js": "^6.6.2", "multer": "^2.0.1", "nestjs-stripe": "^1.0.0", "paypal-rest-sdk": "^1.8.1", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "qs": "^6.11.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.6", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "sqlite3": "^5.1.7", "stripe": "^11.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "^9.0.11", "@types/express": "^4.17.13", "@types/jest": "^28.1.7", "@types/multer": "^1.4.13", "@types/node": "^18.7.6", "@types/sequelize": "^4.28.20", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "28.1.3", "prettier": "^2.7.1", "sequelize-cli": "^6.6.3", "supertest": "^6.2.4", "ts-jest": "^28.0.8", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}