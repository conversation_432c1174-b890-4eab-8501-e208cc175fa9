"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_refund-policy_refund-policy-delete-view_tsx"],{

/***/ "./src/components/refund-policy/refund-policy-delete-view.tsx":
/*!********************************************************************!*\
  !*** ./src/components/refund-policy/refund-policy-delete-view.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\n/* harmony import */ var _data_refund_policy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/refund-policy */ \"./src/data/refund-policy.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst RefundPolicyDeleteView = ()=>{\n    _s();\n    const { mutate: deleteRefundPolicy, isLoading: loading } = (0,_data_refund_policy__WEBPACK_IMPORTED_MODULE_4__.useDeleteRefundPolicyMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        try {\n            deleteRefundPolicy({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\refund-policy\\\\refund-policy-delete-view.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RefundPolicyDeleteView, \"QUMVdJFlvQ9z4Zf/SyyvcLVBuOU=\", false, function() {\n    return [\n        _data_refund_policy__WEBPACK_IMPORTED_MODULE_4__.useDeleteRefundPolicyMutation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = RefundPolicyDeleteView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RefundPolicyDeleteView);\nvar _c;\n$RefreshReg$(_c, \"RefundPolicyDeleteView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/refund-policy/refund-policy-delete-view.tsx\n"));

/***/ }),

/***/ "./src/data/client/refund-policy.ts":
/*!******************************************!*\
  !*** ./src/data/client/refund-policy.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RefundPolicyClient: function() { return /* binding */ RefundPolicyClient; }\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\n\n\n\nconst RefundPolicyClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REFUND_POLICIES),\n    paginated: (param)=>{\n        let { target, title, status, ...params } = param;\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REFUND_POLICIES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                title,\n                target,\n                status\n            })\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/refund-policy.ts\n"));

/***/ }),

/***/ "./src/data/refund-policy.ts":
/*!***********************************!*\
  !*** ./src/data/refund-policy.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateRefundPolicyMutation: function() { return /* binding */ useCreateRefundPolicyMutation; },\n/* harmony export */   useDeleteRefundPolicyMutation: function() { return /* binding */ useDeleteRefundPolicyMutation; },\n/* harmony export */   useRefundPoliciesQuery: function() { return /* binding */ useRefundPoliciesQuery; },\n/* harmony export */   useRefundPolicyQuery: function() { return /* binding */ useRefundPolicyQuery; },\n/* harmony export */   useUpdateRefundPolicyMutation: function() { return /* binding */ useUpdateRefundPolicyMutation; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/client/refund-policy */ \"./src/data/client/refund-policy.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n\n\n\n\n\n\n\n\n\nconst useCreateRefundPolicyMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list) : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\nconst useDeleteRefundPolicyMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\nconst useUpdateRefundPolicyMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? \"/\".concat(router.query.shop).concat(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list) : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.refundPolicies.list;\n            await router.push(\"\".concat(generateRedirectUrl, \"/\").concat(data === null || data === void 0 ? void 0 : data.slug, \"/edit\"), undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES);\n        },\n        onError: (error)=>{\n            var _error_response;\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(\"common:\".concat(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data.message)));\n        }\n    });\n};\nconst useRefundPolicyQuery = (param)=>{\n    let { slug, language } = param;\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.get({\n            slug,\n            language\n        }));\n    return {\n        refundPolicy: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useRefundPoliciesQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.REFUND_POLICIES,\n        options\n    ], (param)=>{\n        let { queryKey, pageParam } = param;\n        return _data_client_refund_policy__WEBPACK_IMPORTED_MODULE_7__.RefundPolicyClient.paginated(Object.assign({}, queryKey[1], pageParam));\n    }, {\n        keepPreviousData: true\n    });\n    var _data_data;\n    return {\n        refundPolicies: (_data_data = data === null || data === void 0 ? void 0 : data.data) !== null && _data_data !== void 0 ? _data_data : [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/refund-policy.ts\n"));

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; }\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n"));

/***/ })

}]);