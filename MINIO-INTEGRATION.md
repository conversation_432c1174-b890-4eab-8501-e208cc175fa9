# 🪣 MinIO File Upload Integration

## ✅ **Integration Complete!**

The entire e-commerce platform now uses MinIO for file storage instead of hardcoded S3 URLs. All file uploads are handled through MinIO's S3-compatible API.

## 🏗️ **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API REST      │    │     <PERSON><PERSON>       │
│  (Admin/Shop)   │───▶│   NestJS        │───▶│   S3-Compatible │
│                 │    │                 │    │    Storage      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
   File Upload              File Processing         Object Storage
   Components               & Validation            & URL Generation
```

## 🔧 **What Was Changed**

### **API REST Service**
- ✅ Added MinIO SDK (`minio` package)
- ✅ Created `MinioService` for file operations
- ✅ Updated `UploadsController` with proper file handling
- ✅ Added file validation and size limits (10MB)
- ✅ Configured automatic bucket creation
- ✅ Added Swagger documentation for upload endpoints

### **Frontend Services**
- ✅ Updated Next.js image domains to include MinIO URLs
- ✅ Added MinIO endpoint environment variables
- ✅ Configured CORS for MinIO access
- ✅ No changes needed to upload components (they already work!)

### **Docker Configuration**
- ✅ Added MinIO environment variables to API service
- ✅ Updated Traefik routing for MinIO access
- ✅ Configured MinIO bucket policies for public read access

## 📁 **File Upload Flow**

1. **Frontend Upload**: User selects files in admin/shop interface
2. **API Processing**: Files sent to `/api/attachments` endpoint
3. **Validation**: File type, size, and format validation
4. **MinIO Storage**: Files uploaded to `ecommerce-uploads` bucket
5. **URL Generation**: Public URLs returned to frontend
6. **Database Storage**: File metadata stored in database

## 🌐 **Endpoints**

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/attachments` | Upload multiple files |
| DELETE | `/api/attachments/:fileName` | Delete a file |
| GET | `/api/attachments/:fileName/url` | Get file URL |

## 📊 **File Support**

### **Supported File Types**
- **Images**: JPEG, PNG, GIF, WebP
- **Documents**: PDF, TXT, DOC, DOCX
- **Size Limit**: 10MB per file
- **Multiple Files**: Up to 10 files per upload

### **File Validation**
```typescript
const allowedMimes = [
  'image/jpeg',
  'image/png', 
  'image/gif',
  'image/webp',
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];
```

## 🔐 **Security Features**

- ✅ File type validation
- ✅ File size limits
- ✅ Unique file naming (UUID-based)
- ✅ Public read-only bucket policy
- ✅ CORS configuration
- ✅ Secure file paths

## 🚀 **Usage Examples**

### **Upload Files (Frontend)**
```javascript
// Already implemented in admin/shop components
const formData = new FormData();
files.forEach(file => {
  formData.append('attachment[]', file);
});

const response = await fetch('/api/attachments', {
  method: 'POST',
  body: formData
});

const uploadedFiles = await response.json();
```

### **Response Format**
```json
[
  {
    "id": "uuid-string",
    "original": "http://minio.localhost/ecommerce-uploads/uploads/file.jpg",
    "thumbnail": "http://minio.localhost/ecommerce-uploads/uploads/file.jpg",
    "file_name": "original-name.jpg",
    "size": 1024000,
    "mime_type": "image/jpeg"
  }
]
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# MinIO Configuration
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=ecommerce-uploads
MINIO_REGION=us-east-1
```

### **Docker Services**
- **MinIO Server**: Port 9000 (API), Port 9001 (Console)
- **Bucket**: `ecommerce-uploads` (auto-created)
- **Access**: Public read, authenticated write
- **Traefik Routes**: `minio.localhost`, `minio-console.localhost`

## 🧪 **Testing**

### **Manual Testing**
1. Access admin panel: http://admin.localhost
2. Go to product creation/editing
3. Upload images using the file uploader
4. Verify files appear in MinIO console: http://minio-console.localhost
5. Check file URLs are accessible

### **API Testing**
```bash
# Test upload endpoint
curl -X POST http://api.localhost/api/attachments \
  -F "attachment[]=@test-image.jpg" \
  -H "Content-Type: multipart/form-data"
```

### **MinIO Console Access**
- **URL**: http://minio-console.localhost
- **Username**: minioadmin
- **Password**: minioadmin123

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Files not uploading**
   - Check MinIO service is running: `docker-compose ps`
   - Verify bucket exists in MinIO console
   - Check API logs: `docker-compose logs api-rest`

2. **Images not displaying**
   - Verify Next.js image domains include MinIO URLs
   - Check CORS configuration
   - Ensure bucket has public read policy

3. **File size errors**
   - Default limit is 10MB
   - Check `upload_max_filesize` in settings
   - Verify Docker container memory limits

### **Debug Commands**
```bash
# Check MinIO bucket
docker-compose exec minio mc ls minio/ecommerce-uploads

# View API logs
docker-compose logs -f api-rest

# Test MinIO connection
docker-compose exec api-rest node test-minio-upload.js
```

## 📈 **Performance & Scalability**

- **Concurrent Uploads**: Supports multiple simultaneous uploads
- **File Deduplication**: UUID-based naming prevents conflicts
- **Caching**: MinIO provides built-in caching
- **CDN Ready**: Can be fronted with CDN for better performance
- **Backup**: MinIO supports replication and backup strategies

## 🎯 **Next Steps**

1. **Image Processing**: Add thumbnail generation for images
2. **File Compression**: Implement automatic compression
3. **CDN Integration**: Add CloudFront or similar CDN
4. **Backup Strategy**: Configure MinIO replication
5. **Monitoring**: Add file upload metrics and monitoring

**The file upload system is now fully integrated with MinIO and ready for production use! 🎉**
