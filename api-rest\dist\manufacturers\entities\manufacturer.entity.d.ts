import { Model } from 'sequelize-typescript';
import { Type } from '../../types/entities/type.entity';
export declare class Manufacturer extends Model {
    id: number;
    cover_image?: any;
    description?: string;
    image?: any;
    is_approved?: boolean;
    name: string;
    products_count?: number;
    slug?: string;
    socials?: any;
    type_id?: number;
    type: Type;
    website?: string;
    language?: string;
    translated_languages?: string[];
    created_at: Date;
    updated_at: Date;
}
