Write-Host "🚀 Starting E-commerce Platform with Fixed Docker Setup..." -ForegroundColor Green

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.working.yml down

# Build and start all services
Write-Host "🔨 Building and starting all services..." -ForegroundColor Blue
Write-Host "⚠️  This may take 5-10 minutes for the first build..." -ForegroundColor Yellow
docker-compose -f docker-compose.working.yml up --build -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Check if API is ready
Write-Host "🔍 Checking API service..." -ForegroundColor Magenta
$apiReady = $false
$attempts = 0
while (-not $apiReady -and $attempts -lt 10) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/api" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            $apiReady = $true
            Write-Host "✅ API service is ready!" -ForegroundColor Green
        }
    } catch {
        $attempts++
        Write-Host "⏳ Waiting for API... (attempt $attempts/10)" -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
}

if ($apiReady) {
    # Seed database
    Write-Host "🌱 Seeding database..." -ForegroundColor Magenta
    try {
        docker-compose -f docker-compose.working.yml exec api-rest node simple-seed.js
        Write-Host "✅ Database seeded successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Database seeding failed, but services are running" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ API service failed to start properly" -ForegroundColor Red
}

Write-Host ""
Write-Host "✅ Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access your services:" -ForegroundColor White
Write-Host "   • Shop Frontend: http://localhost:3003" -ForegroundColor Cyan
Write-Host "   • Admin Panel: http://localhost:3002" -ForegroundColor Cyan
Write-Host "   • API Documentation: http://localhost:5000/docs" -ForegroundColor Cyan
Write-Host "   • MinIO Console: http://localhost:9001" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔐 Default credentials:" -ForegroundColor White
Write-Host "   • Admin: <EMAIL> / password" -ForegroundColor Yellow
Write-Host "   • MinIO: minioadmin / minioadmin123" -ForegroundColor Yellow
Write-Host ""
Write-Host "📁 File Upload Features:" -ForegroundColor White
Write-Host "   • Upload endpoint: http://localhost:5000/api/attachments" -ForegroundColor Gray
Write-Host "   • Supported formats: Images, PDFs, Documents" -ForegroundColor Gray
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor White
Write-Host "   • View logs: docker-compose -f docker-compose.working.yml logs -f" -ForegroundColor Gray
Write-Host "   • Stop services: docker-compose -f docker-compose.working.yml down" -ForegroundColor Gray
Write-Host "   • Restart: docker-compose -f docker-compose.working.yml restart" -ForegroundColor Gray

Read-Host "Press Enter to continue"
