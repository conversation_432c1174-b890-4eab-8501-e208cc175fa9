"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5111],{55111:function(e,n,s){s.r(n);var t=s(85893),r=s(60802),o=s(75814),d=s(8657),a=s(48583),c=s(5233),u=s(67294),l=s(67555),i=s.n(l);s(48705),n.default=()=>{let{closeModal:e}=(0,o.SO)(),{t:n}=(0,c.$G)("common"),[s,l]=(0,u.useState)(""),[m,x]=(0,a.KO)(d.lu);return(0,t.jsxs)("div",{className:"flex min-h-screen flex-col justify-center bg-light p-5 sm:p-8 md:min-h-0 md:rounded-xl",children:[(0,t.jsxs)("h1",{className:"mb-5 text-center text-sm font-semibold text-heading sm:mb-6",children:[n(m?"text-update":"text-add-new")," ",n("text-contact-number")]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i(),{country:"us",value:s,onChange:e=>l("+".concat(e)),inputClass:"!p-0 !pe-4 !ps-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base !border-e-0 !rounded !rounded-e-none focus:!border-accent !h-12",dropdownClass:"focus:!ring-0 !border !border-border-base !shadow-350"}),(0,t.jsx)(r.Z,{className:"!rounded-s-none",onClick:function(){s&&(x(s),e())},children:n("text-save")})]})]})}}}]);