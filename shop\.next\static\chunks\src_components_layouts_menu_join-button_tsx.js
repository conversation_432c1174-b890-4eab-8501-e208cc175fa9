"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_layouts_menu_join-button_tsx"],{

/***/ "./src/components/layouts/menu/join-button.tsx":
/*!*****************************************************!*\
  !*** ./src/components/layouts/menu/join-button.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JoinButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction JoinButton(param) {\n    let { title = \"join-button\", size = \"small\", className = \"font-semibold\" } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleJoin() {\n        return openModal(\"LOGIN_VIEW\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        className: className,\n        size: size,\n        onClick: handleJoin,\n        children: t(title)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\join-button.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(JoinButton, \"NdvIevGNwAGqOdFnzIN+OWaYbpo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction\n    ];\n});\n_c = JoinButton;\nvar _c;\n$RefreshReg$(_c, \"JoinButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21lbnUvam9pbi1idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ3lCO0FBRXZCO0FBRS9CLFNBQVNHLFdBQVcsS0FJaUI7UUFKakIsRUFDakNDLFFBQVEsYUFBYSxFQUNyQkMsT0FBTyxPQUFPLEVBQ2RDLFlBQVksZUFBZSxFQUN1QixHQUpqQjs7SUFLakMsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR0wsNERBQWNBLENBQUM7SUFDN0IsTUFBTSxFQUFFTSxTQUFTLEVBQUUsR0FBR1Asa0ZBQWNBO0lBQ3BDLFNBQVNRO1FBQ1AsT0FBT0QsVUFBVTtJQUNuQjtJQUNBLHFCQUNFLDhEQUFDUiw2REFBTUE7UUFBQ00sV0FBV0E7UUFBV0QsTUFBTUE7UUFBTUssU0FBU0Q7a0JBQ2hERixFQUFFSDs7Ozs7O0FBR1Q7R0Fmd0JEOztRQUtSRCx3REFBY0E7UUFDTkQsOEVBQWNBOzs7S0FOZEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0cy9tZW51L2pvaW4tYnV0dG9uLnRzeD8xMDVlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XHJcbmltcG9ydCB7IHVzZU1vZGFsQWN0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsL21vZGFsLmNvbnRleHQnO1xyXG5pbXBvcnQgeyBCdXR0b25Qcm9wcyB9IGZyb20gJ0AvdHlwZXMnO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBKb2luQnV0dG9uKHtcclxuICB0aXRsZSA9ICdqb2luLWJ1dHRvbicsXHJcbiAgc2l6ZSA9ICdzbWFsbCcsXHJcbiAgY2xhc3NOYW1lID0gJ2ZvbnQtc2VtaWJvbGQnLFxyXG59OiBQaWNrPEJ1dHRvblByb3BzLCAndGl0bGUnIHwgJ3NpemUnIHwgJ2NsYXNzTmFtZSc+KSB7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbignY29tbW9uJyk7XHJcbiAgY29uc3QgeyBvcGVuTW9kYWwgfSA9IHVzZU1vZGFsQWN0aW9uKCk7XHJcbiAgZnVuY3Rpb24gaGFuZGxlSm9pbigpIHtcclxuICAgIHJldHVybiBvcGVuTW9kYWwoJ0xPR0lOX1ZJRVcnKTtcclxuICB9XHJcbiAgcmV0dXJuIChcclxuICAgIDxCdXR0b24gY2xhc3NOYW1lPXtjbGFzc05hbWV9IHNpemU9e3NpemV9IG9uQ2xpY2s9e2hhbmRsZUpvaW59PlxyXG4gICAgICB7dCh0aXRsZSl9XHJcbiAgICA8L0J1dHRvbj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJ1c2VNb2RhbEFjdGlvbiIsInVzZVRyYW5zbGF0aW9uIiwiSm9pbkJ1dHRvbiIsInRpdGxlIiwic2l6ZSIsImNsYXNzTmFtZSIsInQiLCJvcGVuTW9kYWwiLCJoYW5kbGVKb2luIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/layouts/menu/join-button.tsx\n"));

/***/ })

}]);