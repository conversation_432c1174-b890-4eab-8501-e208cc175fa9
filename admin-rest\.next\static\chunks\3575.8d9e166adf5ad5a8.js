"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3575],{13575:function(e,t,u){u.r(t);var n=u(85893),s=u(71421),a=u(75814),l=u(16720);t.default=()=>{let{mutate:e,isLoading:t}=(0,l.e7)(),{data:u}=(0,a.X9)(),{closeModal:r}=(0,a.SO)();async function handleDelete(){e({id:u}),r()}return(0,n.jsx)(s.Z,{onCancel:r,onDelete:handleDelete,deleteBtnLoading:t})}},16720:function(e,t,u){u.d(t,{jT:function(){return useCreateTypeMutation},e7:function(){return useDeleteTypeMutation},F2:function(){return useTypeQuery},qs:function(){return useTypesQuery},oy:function(){return useUpdateTypeMutation}});var n=u(11163),s=u.n(n),a=u(88767),l=u(22920),r=u(5233),o=u(97514),c=u(47869),i=u(55191),d=u(3737);let y={...(0,i.h)(c.P.TYPES),all:e=>{let{name:t,...u}=e;return d.eN.get(c.P.TYPES,{searchJoin:"and",...u,search:d.eN.formatSearchParams({name:t})})}};var p=u(93345);let useCreateTypeMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(y.create,{onSuccess:()=>{s().push(o.Z.type.list,void 0,{locale:p.Config.defaultLanguage}),l.Am.success(e("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(c.P.TYPES)}})},useDeleteTypeMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,r.$G)();return(0,a.useMutation)(y.delete,{onSuccess:()=>{l.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.TYPES)}})},useUpdateTypeMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useRouter)(),u=(0,a.useQueryClient)();return(0,a.useMutation)(y.update,{onSuccess:async u=>{let n=t.query.shop?"/".concat(t.query.shop).concat(o.Z.type.list):o.Z.type.list;await t.push("".concat(n,"/").concat(null==u?void 0:u.slug,"/edit"),void 0,{locale:p.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(c.P.TYPES)}})},useTypeQuery=e=>{let{slug:t,language:u}=e;return(0,a.useQuery)([c.P.TYPES,{slug:t,language:u}],()=>y.get({slug:t,language:u}))},useTypesQuery=e=>{let{data:t,isLoading:u,error:n}=(0,a.useQuery)([c.P.TYPES,e],e=>{let{queryKey:t,pageParam:u}=e;return y.all(Object.assign({},t[1],u))},{keepPreviousData:!0});return{types:null!=t?t:[],loading:u,error:n}}}}]);