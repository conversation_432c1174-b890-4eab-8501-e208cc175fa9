"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3924],{63924:function(e,t,a){a.r(t);var l=a(85893),r=a(27899),s=a(5233),n=a(93967),i=a.n(n),c=a(76518),d=a(77556);t.default=e=>{let{products:t,title:a,className:n}=e,{t:o}=(0,s.$G)(),{alignLeft:b}=(0,c.S)(),m=[{title:o("table:table-item-category-id"),dataIndex:"category_id",key:"category_id",align:b,width:120,render:e=>"#".concat(o("table:table-item-id"),": ").concat(e)},{title:o("table:table-item-category-name"),className:"cursor-pointer",dataIndex:"category_name",key:"category_name",align:b,width:220,ellipsis:!0},{title:o("table:table-item-shop"),dataIndex:"shop_name",key:"shop",align:b,ellipsis:!0,width:200,render:e=>(0,l.jsx)("span",{className:"truncate whitespace-nowrap",children:e})},{title:o("table:table-item-Product-count"),className:"cursor-pointer",dataIndex:"product_count",key:"product_count",width:180,align:"center",render:e=>(0,l.jsx)("span",{children:e})}];return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:i()("overflow-hidden rounded-lg bg-white p-7",n),children:[(0,l.jsx)("div",{className:"flex items-center justify-between pb-7",children:(0,l.jsx)("h3",{className:"before:content-'' relative mt-1.5 bg-light text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:w-1 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-7 rtl:before:-right-7",children:o(a)})}),(0,l.jsx)(r.i,{columns:m,emptyText:()=>(0,l.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,l.jsx)(d.m,{className:"w-52"}),(0,l.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:o("table:empty-table-data")}),(0,l.jsx)("p",{className:"text-[13px]",children:o("table:empty-table-sorry-text")})]}),data:t,rowKey:"category_id",scroll:{x:200}})]})})}}}]);