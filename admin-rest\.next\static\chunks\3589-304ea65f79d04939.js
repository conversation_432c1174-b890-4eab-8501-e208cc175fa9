"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3589],{92072:function(e,t,r){var n=r(85893),s=r(93967),o=r.n(s),a=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,a.m6)(o()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},35484:function(e,t,r){var n=r(85893),s=r(93967),o=r.n(s),a=r(98388);t.Z=e=>{let{title:t,className:r,...s}=e;return(0,n.jsx)("h2",{className:(0,a.m6)(o()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",r)),...s,children:t})}},37912:function(e,t,r){var n=r(85893),s=r(5114),o=r(80287),a=r(93967),l=r.n(a),i=r(67294),d=r(87536),c=r(5233),u=r(98388);t.Z=e=>{let{className:t,onSearch:r,variant:a="outline",shadow:p=!1,inputClassName:x,placeholderText:m,...v}=e,{register:h,handleSubmit:f,watch:g,reset:w,formState:{errors:b}}=(0,d.cI)({defaultValues:{searchText:""}}),y=g("searchText"),{t:N}=(0,c.$G)();(0,i.useEffect)(()=>{y||r({searchText:""})},[y]);let A=l()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},x);return(0,n.jsxs)("form",{noValidate:!0,role:"search",className:(0,u.m6)(l()("relative flex w-full items-center",t)),onSubmit:f(r),children:[(0,n.jsx)("label",{htmlFor:"search",className:"sr-only",children:N("form:input-label-search")}),(0,n.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,n.jsx)(o.W,{className:"h-5 w-5"})}),(0,n.jsx)("input",{type:"text",id:"search",...h("searchText"),className:(0,u.m6)(A),placeholder:null!=m?m:N("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),b.searchText&&(0,n.jsx)("p",{children:b.searchText.message}),!!y&&(0,n.jsx)("button",{type:"button",onClick:function(){w(),r({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,n.jsx)(s.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,r){r.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var n=r(85893);let ArrowNext=e=>{let{...t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,n.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,n.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,n.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},7058:function(e,t,r){var n=r(85893),s=r(804),o=r(77556),a=r(18230),l=r(27899),i=r(78998),d=r(10265),c=r(76518),u=r(27484),p=r.n(u),x=r(84110),m=r.n(x),v=r(29387),h=r.n(v),f=r(70178),g=r.n(f),w=r(5233),b=r(67294),y=r(8953),N=r(78628),A=r(16203),j=r(79362);p().extend(m()),p().extend(g()),p().extend(h()),t.Z=e=>{let{userRole:t,user:r,ownershipTransfer:u,paginatorInfo:p,onPagination:x,onSort:m,onOrder:v}=e,{t:h}=(0,w.$G)(),{permissions:f}=(0,A.WA)(),{alignLeft:g,alignRight:C}=(0,c.S)(),[S,T]=(0,b.useState)({sort:null===d.As||void 0===d.As?void 0:d.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{m(e=>e===(null===d.As||void 0===d.As?void 0:d.As.Desc)?null===d.As||void 0===d.As?void 0:d.As.Asc:null===d.As||void 0===d.As?void 0:d.As.Desc),v(e),T({sort:(null==S?void 0:S.sort)===(null===d.As||void 0===d.As?void 0:d.As.Desc)?null===d.As||void 0===d.As?void 0:d.As.Asc:null===d.As||void 0===d.As?void 0:d.As.Desc,column:e})}}),R=[{title:(0,n.jsx)(i.Z,{title:h("table:table-item-id"),ascending:S.sort===d.As.Asc&&"id"===S.column,isActive:"id"===S.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:g,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(h("table:table-item-id"),": ").concat(e)},{title:(0,n.jsx)(i.Z,{title:"Request tracker",ascending:(null==S?void 0:S.sort)===(null===d.As||void 0===d.As?void 0:d.As.Asc)&&(null==S?void 0:S.column)==="transaction_identifier",isActive:(null==S?void 0:S.column)==="transaction_identifier"}),className:"cursor-pointer",dataIndex:"transaction_identifier",key:"transaction_identifier",align:g,ellipsis:!0,width:200,onHeaderCell:()=>onHeaderClick("transaction_identifier"),render:e=>(0,n.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:"Message",className:"cursor-pointer",dataIndex:"message",key:"message",align:g,width:350,ellipsis:!0,render:e=>e?(0,n.jsx)("span",{dangerouslySetInnerHTML:{__html:(null==e?void 0:e.length)<130?e:(null==e?void 0:e.substring(0,130))+"..."}}):""},{title:"Request created by",className:"cursor-pointer",dataIndex:"previous_owner",key:"previous_owner",align:"center",render:e=>(0,n.jsx)("span",{className:"whitespace-nowrap",children:null==e?void 0:e.name})},{title:"Shop transfer to",className:"cursor-pointer",dataIndex:"current_owner",key:"current_owner",align:"center",render:e=>(0,n.jsx)("span",{className:"whitespace-nowrap",children:null==e?void 0:e.name})},{title:(0,n.jsx)(i.Z,{title:"Transfer status",ascending:S.sort===d.As.Asc&&"status"===S.column,isActive:"status"===S.column}),className:"cursor-pointer",dataIndex:"status",key:"status",align:"center",width:150,onHeaderCell:()=>onHeaderClick("status"),render:e=>(0,n.jsx)(y.Z,{text:h(e),className:"capitalize",color:(0,N.Z)(e)})},{title:h("table:table-item-details"),dataIndex:"id",key:"details_actions",align:"center",width:150,render:(e,r)=>{let{transaction_identifier:o,is_approved:a}=r;return(0,n.jsx)(s.Z,{id:e,detailsUrl:t&&"super_admin"===t?"/shop-transfer/".concat(o):"/shop-transfer/vendor/".concat(o)})}},{title:h("table:table-item-actions"),dataIndex:"transaction_identifier",key:"delete_actions",align:C,width:150,render:(e,t)=>{var r;return(0,n.jsx)(s.Z,{id:null==t?void 0:null===(r=t.id)||void 0===r?void 0:r.toString(),deleteModalView:"DELETE_OWNERSHIP_TRANSFER_REQUEST"})}}];return(null==f?void 0:f.includes(j.Mc))||(R=null==R?void 0:R.filter(e=>(null==e?void 0:e.key)!=="delete_actions")),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,n.jsx)(l.i,{columns:R,emptyText:()=>(0,n.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,n.jsx)(o.m,{className:"w-52"}),(0,n.jsx)("div",{className:"pt-6 mb-1 text-base font-semibold text-heading",children:h("table:empty-table-data")}),(0,n.jsx)("p",{className:"text-[13px]",children:h("table:empty-table-sorry-text")})]}),data:u,rowKey:"id",scroll:{x:1e3}})}),!!(null==p?void 0:p.total)&&(0,n.jsx)("div",{className:"flex items-center justify-end",children:(0,n.jsx)(a.Z,{total:p.total,current:p.currentPage,pageSize:p.perPage,onChange:x})})]})}},8953:function(e,t,r){var n=r(85893),s=r(93967),o=r.n(s),a=r(5233),l=r(98388);t.Z=e=>{let{t}=(0,a.$G)(),{className:r,color:s,textColor:i,text:d,textKey:c,animate:u=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("span",{className:(0,l.m6)(o()("inline-block",p.root,{[p.default]:!s,[p.text]:!i,[p.animate]:u},s,i,r)),children:c?t(c):d})})}},18230:function(e,t,r){r.d(t,{Z:function(){return pagination}});var n=r(85893),s=r(55891),o=r(14713);let ArrowPrev=e=>{let{...t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,n.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};r(5871);var pagination=e=>(0,n.jsx)(s.Z,{nextIcon:(0,n.jsx)(o.T,{}),prevIcon:(0,n.jsx)(ArrowPrev,{}),...e})},78998:function(e,t,r){r.d(t,{Z:function(){return title_with_sort}});var n=r(85893),s=r(93967),o=r.n(s);r(67294);let TriangleArrowDown=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,n.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,n.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:t,ascending:r,isActive:s=!0,className:a}=e;return(0,n.jsxs)("span",{className:o()("inline-flex items-center",a),children:[(0,n.jsx)("span",{title:"Sort by ".concat(t),children:t}),r?(0,n.jsx)(TriangleArrowUp,{width:"9",className:o()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":s})}):(0,n.jsx)(TriangleArrowDown,{width:"9",className:o()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":s})})]})}},76240:function(e,t,r){r.d(t,{wA:function(){return useDeleteOwnerTransferMutation},OM:function(){return useOwnerShipTransferQuery},il:function(){return useOwnerShipTransfersQuery},so:function(){return useUpdateOwnerTransferMutation}});var n=r(11163),s=r(88767),o=r(22920),a=r(5233),l=r(28597),i=r(97514),d=r(47869),c=r(93345),u=r(55191),p=r(3737);let x={...(0,u.h)(d.P.OWNERSHIP_TRANSFER),all:function(){let{transaction_identifier:e,shop_id:t,...r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return p.eN.get(d.P.OWNERSHIP_TRANSFER,{searchJoin:"and",shop_id:t,...r})},get(e){let{transaction_identifier:t,language:r,shop_id:n,request_view_type:s}=e;return p.eN.get("".concat(d.P.OWNERSHIP_TRANSFER,"/").concat(t),{language:r,shop_id:n,transaction_identifier:t,request_view_type:s})},paginated:e=>{let{transaction_identifier:t,shop_id:r,...n}=e;return p.eN.get(d.P.OWNERSHIP_TRANSFER,{searchJoin:"and",shop_id:r,...n,search:p.eN.formatSearchParams({transaction_identifier:t})})},approve:e=>p.eN.post(d.P.OWNERSHIP_TRANSFER,e),disapprove:e=>p.eN.post(d.P.OWNERSHIP_TRANSFER,e)},useOwnerShipTransferQuery=e=>{let{transaction_identifier:t,language:r,shop_id:n,request_view_type:o}=e,{data:a,error:l,isLoading:i,refetch:c}=(0,s.useQuery)([d.P.OWNERSHIP_TRANSFER,{transaction_identifier:t,language:r,shop_id:n}],()=>x.get({transaction_identifier:t,language:r,shop_id:n,request_view_type:o}));return{ownershipTransfer:a,error:l,loading:i,refetch:c}},useOwnerShipTransfersQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,s.useQuery)([d.P.OWNERSHIP_TRANSFER,e],e=>{let{queryKey:t,pageParam:r}=e;return x.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0});return{ownershipTransfer:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:n,loading:o}},useUpdateOwnerTransferMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,s.useQueryClient)(),r=(0,n.useRouter)();return(0,s.useMutation)(x.update,{onSuccess:async t=>{let n=r.query.shop?"/".concat(r.query.shop).concat(i.Z.ownershipTransferRequest.list):i.Z.ownershipTransferRequest.list;await r.push(n,void 0,{locale:c.Config.defaultLanguage}),o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.OWNERSHIP_TRANSFER)},onError:t=>{var r;o.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteOwnerTransferMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,a.$G)();return(0,s.useMutation)(x.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.OWNERSHIP_TRANSFER)},onError:e=>{var r;o.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})}},78628:function(e,t){t.Z=e=>(null==e?void 0:e.toLowerCase())==="order-pending"||(null==e?void 0:e.toLowerCase())==="pending"||(null==e?void 0:e.toLowerCase())==="payment-pending"?"bg-status-pending bg-opacity-10 text-status-pending":(null==e?void 0:e.toLowerCase())==="order-processing"||(null==e?void 0:e.toLowerCase())==="processing"||(null==e?void 0:e.toLowerCase())==="payment-processing"?"bg-status-processing bg-opacity-10 text-status-processing":(null==e?void 0:e.toLowerCase())==="order-completed"||(null==e?void 0:e.toLowerCase())==="approved"||(null==e?void 0:e.toLowerCase())==="payment-success"?"bg-status-complete bg-opacity-10 text-status-complete":(null==e?void 0:e.toLowerCase())==="order-cancelled"||(null==e?void 0:e.toLowerCase())==="rejected"||(null==e?void 0:e.toLowerCase())==="payment-reversal"?"bg-status-canceled bg-opacity-10 text-status-canceled":(null==e?void 0:e.toLowerCase())==="order-failed"||(null==e?void 0:e.toLowerCase())==="payment-failed"?"bg-status-failed bg-opacity-10 text-status-failed":(null==e?void 0:e.toLowerCase())==="order-at-local-facility"?"bg-status-out-for-delivery bg-opacity-10 text-status-out-for-delivery":(null==e?void 0:e.toLowerCase())==="order-out-for-delivery"?"bg-status-out-for-delivery bg-opacity-10 text-status-out-for-delivery":(null==e?void 0:e.toLowerCase())==="order-refunded"||(null==e?void 0:e.toLowerCase())==="refunded"||(null==e?void 0:e.toLowerCase())==="payment-refunded"?"bg-rose-400 bg-opacity-10 text-status-pending":"bg-accent bg-opacity-10 !text-accent"}}]);