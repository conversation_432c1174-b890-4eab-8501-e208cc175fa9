(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5250],{20640:function(e,t,r){"use strict";var o=r(11742),n={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var r,a,l,i,s,c,u,d,f=!1;t||(t={}),l=t.debug||!1;try{if(s=o(),c=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(r){if(r.stopPropagation(),t.format){if(r.preventDefault(),void 0===r.clipboardData){l&&console.warn("unable to use e.clipboardData"),l&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var o=n[t.format]||n.default;window.clipboardData.setData(o,e)}else r.clipboardData.clearData(),r.clipboardData.setData(t.format,e)}t.onCopy&&(r.preventDefault(),t.onCopy(r.clipboardData))}),document.body.appendChild(d),c.selectNodeContents(d),u.addRange(c),!document.execCommand("copy"))throw Error("copy command was unsuccessful");f=!0}catch(o){l&&console.error("unable to copy using execCommand: ",o),l&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),f=!0}catch(o){l&&console.error("unable to copy using clipboardData: ",o),l&&console.error("falling back to prompt"),r="message"in t?t.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=r.replace(/#{\s*key\s*}/g,a),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(c):u.removeAllRanges()),d&&document.body.removeChild(d),s()}return f}},86779:function(e,t,r){"use strict";r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var o=r(85893);let InfoIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,o.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},43335:function(e,t,r){"use strict";r.r(t);var o=r(85893),n=r(75814),a=r(5233),l=r(95414),i=r(60802),s=r(93242),c=r(86366),u=r(87536),d=r(41609),f=r.n(d),p=r(79828),h=r(67294),m=r(87077);t.default=()=>{var e;let{t}=(0,a.$G)("common"),{closeModal:r}=(0,n.SO)(),{mutate:d,isLoading:x,data:g}=(0,s.bJ)(),v=(0,h.useCallback)(e=>{let{prompt:t}=e;f()(t)||d({prompt:t})},[]),{data:{name:b,set_value:y,key:w,suggestion:j,maxMenuHeight:C}}=(0,n.X9)(),S=(0,u.cI)({defaultValues:{prompt:"Write a description about ".concat(null!=b?b:"")}}),{register:P,handleSubmit:O,control:D,setValue:z,formState:{errors:R}}=S,E=(0,u.qo)({control:D,name:"prompt"}),_=(0,h.useCallback)(e=>{z("prompt",null==e?void 0:e.title)},[]),N=(0,h.useCallback)(e=>{f()(e)||(y(w,e),r())},[g]),[T,M]=(0,c.Z)();return(0,o.jsxs)("div",{className:"flex h-full min-h-screen w-screen flex-col justify-center bg-light p-4 md:h-auto md:min-h-0 md:max-w-[590px] md:rounded-xl md:p-12 lg:max-w-[836px]",children:[(0,o.jsx)("form",{onSubmit:O(v),className:"mb-8",children:(0,o.jsxs)("div",{className:"space-y-4",children:[f()(j)?"":(0,o.jsx)(p.Z,{isDisabled:x,options:j,getOptionLabel:e=>null==e?void 0:e.title,getOptionValue:e=>null==e?void 0:e.title,placeholder:t("form:input-placeholder-prompt-suggestion"),onChange:_,className:"shadow-promptSuggestion",styles:m.W,maxMenuHeight:null!=C?C:495}),(0,o.jsxs)("div",{className:"",children:[(0,o.jsx)(l.Z,{label:t("form:input-label-prompt"),...P("prompt"),error:t(null===(e=R.prompt)||void 0===e?void 0:e.message),variant:"outline",className:"w-full",disabled:x}),(0,o.jsx)("div",{className:"mt-6 flex shrink-0 justify-end",children:(0,o.jsx)(i.Z,{loading:x,disabled:x||f()(E),children:t(f()(null==g?void 0:g.result)?"form:button-label-generate-ai":"form:button-label-regenerate-ai")})})]})]})}),(0,o.jsxs)("div",{className:"group relative space-y-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(l.Z,{name:"Generated Description",inputClassName:"h-72",label:t("form:input-label-output"),value:null==g?void 0:g.result,disabled:!0}),x&&(0,o.jsx)("div",{className:"absolute top-1/2 left-1/2 h-4 w-4 -translate-x-1/2 -translate-y-1/2 transform",children:(0,o.jsx)("span",{className:"block h-full w-full animate-spin rounded-full border-2 border-t-2 border-transparent ms-2",style:{borderTopColor:"var(--color-accent)"}})})]}),(0,o.jsx)("div",{className:"text-right",children:(0,o.jsx)(i.Z,{disabled:f()(null==g?void 0:g.result)||x,onClick:()=>N(null==g?void 0:g.result),children:t("form:button-label-sync-content")})})]})]})}},23091:function(e,t,r){"use strict";var o=r(85893),n=r(93967),a=r.n(n),l=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,o.jsx)("label",{className:(0,l.m6)(a()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return n},X:function(){return o}});let o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var o=r(85893),n=r(76518),a=r(67294),l=r(23157),i=r(87077);let s=a.forwardRef((e,t)=>{let{isRTL:r}=(0,n.S)();return(0,o.jsx)(l.ZP,{ref:t,styles:i.X,isRtl:r,...e})});s.displayName="Select",t.Z=s},95414:function(e,t,r){"use strict";var o=r(85893),n=r(71611),a=r(93967),l=r.n(a),i=r(67294),s=r(98388);let c=i.forwardRef((e,t)=>{let{className:r,label:a,toolTipText:i,name:c,error:u,variant:d="normal",shadow:f=!1,inputClassName:p,disabled:h,required:m,...x}=e,g=l()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===d,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===d,"border border-border-base focus:border-accent":"outline"===d},{"focus:shadow":f},p);return(0,o.jsxs)("div",{className:(0,s.m6)(l()(r)),children:[a&&(0,o.jsx)(n.Z,{htmlFor:c,toolTipText:i,label:a,required:m}),(0,o.jsx)("textarea",{id:c,name:c,className:(0,s.m6)(l()(g,h?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:h,...x}),u&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:u})]})});c.displayName="TextArea",t.Z=c},71611:function(e,t,r){"use strict";var o=r(85893),n=r(86779),a=r(71943),l=r(23091),i=r(98388);t.Z=e=>{let{className:t,required:r,label:s,toolTipText:c,htmlFor:u}=e;return(0,o.jsxs)(l.Z,{className:(0,i.m6)(t),htmlFor:u,children:[s,r?(0,o.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",c?(0,o.jsx)(a.u,{content:c,children:(0,o.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,o.jsx)(n.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){"use strict";r.d(t,{u:function(){return Tooltip}});var o=r(85893),n=r(67294),a=r(93075),l=r(82364),i=r(24750),s=r(93967),c=r.n(s),u=r(67421),d=r(98388);let f={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:s=8,animation:h="zoomIn",placement:m="top",size:x="md",rounded:g="DEFAULT",shadow:v="md",color:b="default",className:y,arrowClassName:w,showArrow:j=!0}=e,[C,S]=(0,n.useState)(!1),P=(0,n.useRef)(null),{t:O}=(0,u.$G)(),{x:D,y:z,refs:R,strategy:E,context:_}=(0,a.YF)({placement:m,open:C,onOpenChange:S,middleware:[(0,l.x7)({element:P}),(0,l.cv)(s),(0,l.RR)(),(0,l.uY)({padding:8})],whileElementsMounted:i.Me}),{getReferenceProps:N,getFloatingProps:T}=(0,a.NI)([(0,a.XI)(_),(0,a.KK)(_),(0,a.qs)(_,{role:"tooltip"}),(0,a.bQ)(_)]),{isMounted:M,styles:A}=(0,a.Y_)(_,{duration:{open:150,close:150},...p[h]});return(0,o.jsxs)(o.Fragment,{children:[(0,n.cloneElement)(t,N({ref:R.setReference,...t.props})),(M||C)&&(0,o.jsx)(a.ll,{children:(0,o.jsxs)("div",{role:"tooltip",ref:R.setFloating,className:(0,d.m6)(c()(f.base,f.size[x],f.rounded[g],f.variant.solid.base,f.variant.solid.color[b],f.shadow[v],y)),style:{position:E,top:null!=z?z:0,left:null!=D?D:0,...A},...T(),children:[O("".concat(r)),j&&(0,o.jsx)(a.Y$,{ref:P,context:_,className:c()(f.arrow.color[b],w),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},13202:function(e,t,r){"use strict";r.d(t,{M:function(){return l}});var o=r(47869),n=r(55191),a=r(3737);let l={...(0,n.h)(o.P.PRODUCTS),get(e){let{slug:t,language:r}=e;return a.eN.get("".concat(o.P.PRODUCTS,"/").concat(t),{language:r,with:"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file"})},paginated:e=>{let{type:t,name:r,categories:n,shop_id:l,product_type:i,status:s,...c}=e;return a.eN.get(o.P.PRODUCTS,{searchJoin:"and",with:"shop;type;categories",shop_id:l,...c,search:a.eN.formatSearchParams({type:t,name:r,categories:n,shop_id:l,product_type:i,status:s})})},popular(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.POPULAR_PRODUCTS,{searchJoin:"and",with:"type;shop",...r,search:a.eN.formatSearchParams({shop_id:t})})},lowStock(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.LOW_STOCK_PRODUCTS_ANALYTICS,{searchJoin:"and",with:"type;shop",...r,search:a.eN.formatSearchParams({shop_id:t})})},generateDescription:e=>a.eN.post(o.P.GENERATE_DESCRIPTION,e),newOrInActiveProducts:e=>{let{user_id:t,shop_id:r,status:n,name:l,...i}=e;return a.eN.get(o.P.NEW_OR_INACTIVE_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:n,name:l,...i,search:a.eN.formatSearchParams({status:n,name:l})})},lowOrOutOfStockProducts:e=>{let{user_id:t,shop_id:r,status:n,categories:l,name:i,type:s,...c}=e;return a.eN.get(o.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:n,name:i,...c,search:a.eN.formatSearchParams({status:n,name:i,categories:l,type:s})})},productByCategory(e){let{limit:t,language:r}=e;return a.eN.get(o.P.CATEGORY_WISE_PRODUCTS,{limit:t,language:r})},mostSoldProductByCategory(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.CATEGORY_WISE_PRODUCTS_SALE,{searchJoin:"and",...r,search:a.eN.formatSearchParams({shop_id:t})})},getProductsByFlashSale:e=>{let{user_id:t,shop_id:r,slug:n,name:l,...i}=e;return a.eN.get(o.P.PRODUCTS_BY_FLASH_SALE,{searchJoin:"and",user_id:t,shop_id:r,slug:n,name:l,...i,search:a.eN.formatSearchParams({name:l})})},topRated(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.TOP_RATED_PRODUCTS,{searchJoin:"and",...r,search:a.eN.formatSearchParams({shop_id:t})})}}},93242:function(e,t,r){"use strict";r.d(t,{FA:function(){return useProductQuery},Uc:function(){return useProductsByFlashSaleQuery},YC:function(){return useInActiveProductsQuery},bJ:function(){return useGenerateDescriptionMutation},eH:function(){return useProductStockQuery},kN:function(){return useProductsQuery},qX:function(){return useCreateProductMutation},wE:function(){return useUpdateProductMutation},xq:function(){return useDeleteProductMutation}});var o=r(11163),n=r.n(o),a=r(22920),l=r(5233),i=r(88767),s=r(47869),c=r(13202),u=r(28597),d=r(97514),f=r(93345);let useCreateProductMutation=()=>{let e=(0,i.useQueryClient)(),t=(0,o.useRouter)(),{t:r}=(0,l.$G)();return(0,i.useMutation)(c.M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.product.list):d.Z.product.list;await n().push(e,void 0,{locale:f.Config.defaultLanguage}),a.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(s.P.PRODUCTS)},onError:e=>{let{data:t,status:o}=null==e?void 0:e.response;if(422===o){let e=Object.values(t).flat();a.Am.error(e[0])}else{var n;a.Am.error(r("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}}})},useUpdateProductMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,i.useQueryClient)(),r=(0,o.useRouter)();return(0,i.useMutation)(c.M.update,{onSuccess:async t=>{let o=r.query.shop?"/".concat(r.query.shop).concat(d.Z.product.list):d.Z.product.list;await r.push("".concat(o,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:f.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(s.P.PRODUCTS)},onError:t=>{var r;a.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteProductMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,l.$G)();return(0,i.useMutation)(c.M.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(s.P.PRODUCTS)},onError:e=>{var r;a.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})},useProductQuery=e=>{let{slug:t,language:r}=e,{data:o,error:n,isLoading:a}=(0,i.useQuery)([s.P.PRODUCTS,{slug:t,language:r}],()=>c.M.get({slug:t,language:r}));return{product:o,error:n,isLoading:a}},useProductsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:o,error:n,isLoading:a}=(0,i.useQuery)([s.P.PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return c.M.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{products:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(o),error:n,loading:a}},useGenerateDescriptionMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,l.$G)("common");return(0,i.useMutation)(c.M.generateDescription,{onSuccess:()=>{a.Am.success(t("Generated..."))},onSettled:t=>{e.refetchQueries(s.P.GENERATE_DESCRIPTION)}})},useInActiveProductsQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,i.useQuery)([s.P.NEW_OR_INACTIVE_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return c.M.newOrInActiveProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(r),error:o,loading:n}},useProductStockQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,i.useQuery)([s.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return c.M.lowOrOutOfStockProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(r),error:o,loading:n}},useProductsByFlashSaleQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,i.useQuery)([s.P.PRODUCTS_BY_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:r}=e;return c.M.getProductsByFlashSale(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(r),error:o,loading:n}}},76518:function(e,t,r){"use strict";r.d(t,{X:function(){return l},S:function(){return useIsRTL}});var o=r(85893),n=r(11163);let a=["ar","he"];function useIsRTL(){let{locale:e}=(0,n.useRouter)();return e&&a.includes(e)?{isRTL:!0,alignLeft:"right",alignRight:"left"}:{isRTL:!1,alignLeft:"left",alignRight:"right"}}let l=[{id:"ar",name:"عربى",value:"ar",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:r,viewBox:"0 0 512 512",children:[(0,o.jsx)("mask",{id:"a",children:(0,o.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,o.jsxs)("g",{mask:"url(#a)",children:[(0,o.jsx)("path",{fill:"#496e2d",d:"M0 0h512v512H0z"}),(0,o.jsxs)("g",{fill:"#eee",children:[(0,o.jsx)("path",{d:"M144.7 306c0 18.5 15 33.5 33.4 33.5h100.2a27.8 27.8 0 0 0 27.8 27.8h33.4a27.8 27.8 0 0 0 27.8-27.8V306zm225.4-161.3v78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9H370zm-239.3 78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9h-33.4z"}),(0,o.jsx)("path",{d:"M320 144.7h33.4v78H320zm-50 44.5a5.6 5.6 0 0 1-11.2 0v-44.5h-33.4v44.5a5.6 5.6 0 0 1-11.1 0v-44.5h-33.4v44.5a39 39 0 0 0 39 39 38.7 38.7 0 0 0 22.2-7 38.7 38.7 0 0 0 22.2 7c1.7 0 3.4-.1 5-.3a22.3 22.3 0 0 1-21.6 17v33.4c30.6 0 55.6-25 55.6-55.7v-77.9H270z"}),(0,o.jsx)("path",{d:"M180.9 244.9h50v33.4h-50z"})]})]})]})},{width:"28px",height:"28px"})},{id:"zh",name:"中国人",value:"zh",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:r,viewBox:"0 0 512 512",children:[(0,o.jsx)("mask",{id:"a",children:(0,o.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,o.jsxs)("g",{mask:"url(#a)",children:[(0,o.jsx)("path",{fill:"#d80027",d:"M0 0h512v512H0z"}),(0,o.jsx)("path",{fill:"#ffda44",d:"m140.1 155.8 22.1 68h71.5l-57.8 42.1 22.1 68-57.9-42-57.9 42 22.2-68-57.9-42.1H118zm163.4 240.7-16.9-20.8-25 9.7 14.5-22.5-16.9-20.9 25.9 6.9 14.6-22.5 1.4 26.8 26 6.9-25.1 9.6zm33.6-61 8-25.6-21.9-15.5 26.8-.4 7.9-25.6 8.7 25.4 26.8-.3-21.5 16 8.6 25.4-21.9-15.5zm45.3-147.6L370.6 212l19.2 18.7-26.5-3.8-11.8 24-4.6-26.4-26.6-3.8 23.8-12.5-4.6-26.5 19.2 18.7zm-78.2-73-2 26.7 24.9 10.1-26.1 6.4-1.9 26.8-14.1-22.8-26.1 6.4 17.3-20.5-14.2-22.7 24.9 10.1z"})]})]})},{width:"28px",height:"28px"})},{id:"en",name:"English",value:"en",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:r,viewBox:"0 0 512 512",children:[(0,o.jsx)("mask",{id:"a",children:(0,o.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,o.jsxs)("g",{mask:"url(#a)",children:[(0,o.jsx)("path",{fill:"#eee",d:"M256 0h256v64l-32 32 32 32v64l-32 32 32 32v64l-32 32 32 32v64l-256 32L0 448v-64l32-32-32-32v-64z"}),(0,o.jsx)("path",{fill:"#d80027",d:"M224 64h288v64H224Zm0 128h288v64H256ZM0 320h512v64H0Zm0 128h512v64H0Z"}),(0,o.jsx)("path",{fill:"#0052b4",d:"M0 0h256v256H0Z"}),(0,o.jsx)("path",{fill:"#eee",d:"m187 243 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67zm162-81 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Zm162-82 57-41h-70l57 41-22-67Zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Z"})]})]})},{width:"28px",height:"28px"})},{id:"de",name:"Deutsch",value:"de",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:r,viewBox:"0 0 512 512",children:[(0,o.jsx)("mask",{id:"a",children:(0,o.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,o.jsxs)("g",{mask:"url(#a)",children:[(0,o.jsx)("path",{fill:"#ffda44",d:"m0 345 256.7-25.5L512 345v167H0z"}),(0,o.jsx)("path",{fill:"#d80027",d:"m0 167 255-23 257 23v178H0z"}),(0,o.jsx)("path",{fill:"#333",d:"M0 0h512v167H0z"})]})]})},{width:"28px",height:"28px"})},{id:"he",name:"rעברית",value:"he",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 480",width:t,height:r,children:[(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"il-a",children:(0,o.jsx)("path",{fillOpacity:".7",d:"M-87.6 0H595v512H-87.6z"})})}),(0,o.jsxs)("g",{fillRule:"evenodd",clipPath:"url(#il-a)",transform:"translate(82.1) scale(.94)",children:[(0,o.jsx)("path",{fill:"#fff",d:"M619.4 512H-112V0h731.4z"}),(0,o.jsx)("path",{fill:"#00c",d:"M619.4 115.2H-112V48h731.4zm0 350.5H-112v-67.2h731.4zm-483-275l110.1 191.6L359 191.6l-222.6-.8z"}),(0,o.jsx)("path",{fill:"#fff",d:"M225.8 317.8l20.9 35.5 21.4-35.3-42.4-.2z"}),(0,o.jsx)("path",{fill:"#00c",d:"M136 320.6L246.2 129l112.4 190.8-222.6.8z"}),(0,o.jsx)("path",{fill:"#fff",d:"M225.8 191.6l20.9-35.5 21.4 35.4-42.4.1zM182 271.1l-21.7 36 41-.1-19.3-36zm-21.3-66.5l41.2.3-19.8 36.3-21.4-36.6zm151.2 67l20.9 35.5-41.7-.5 20.8-35zm20.5-67l-41.2.3 19.8 36.3 21.4-36.6zm-114.3 0L189.7 256l28.8 50.3 52.8 1.2 32-51.5-29.6-52-55.6.5z"})]})]})},{width:"28px",height:"28px"})},{id:"es",name:"Espa\xf1ol",value:"es",icon:(0,o.jsx)(e=>{let{width:t="640px",height:r="480px"}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:r,viewBox:"0 0 512 512",children:[(0,o.jsx)("mask",{id:"a",children:(0,o.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,o.jsxs)("g",{mask:"url(#a)",children:[(0,o.jsx)("path",{fill:"#ffda44",d:"m0 128 256-32 256 32v256l-256 32L0 384Z"}),(0,o.jsx)("path",{fill:"#d80027",d:"M0 0h512v128H0zm0 384h512v128H0z"}),(0,o.jsxs)("g",{fill:"#eee",children:[(0,o.jsx)("path",{d:"M144 304h-16v-80h16zm128 0h16v-80h-16z"}),(0,o.jsx)("ellipse",{cx:"208",cy:"296",rx:"48",ry:"32"})]}),(0,o.jsxs)("g",{fill:"#d80027",children:[(0,o.jsx)("rect",{width:"16",height:"24",x:"128",y:"192",rx:"8"}),(0,o.jsx)("rect",{width:"16",height:"24",x:"272",y:"192",rx:"8"}),(0,o.jsx)("path",{d:"M208 272v24a24 24 0 0 0 24 24 24 24 0 0 0 24-24v-24h-24z"})]}),(0,o.jsx)("rect",{width:"32",height:"16",x:"120",y:"208",fill:"#ff9811",ry:"8"}),(0,o.jsx)("rect",{width:"32",height:"16",x:"264",y:"208",fill:"#ff9811",ry:"8"}),(0,o.jsx)("rect",{width:"32",height:"16",x:"120",y:"304",fill:"#ff9811",rx:"8"}),(0,o.jsx)("rect",{width:"32",height:"16",x:"264",y:"304",fill:"#ff9811",rx:"8"}),(0,o.jsx)("path",{fill:"#ff9811",d:"M160 272v24c0 8 4 14 9 19l5-6 5 10a21 21 0 0 0 10 0l5-10 5 6c6-5 9-11 9-19v-24h-9l-5 8-5-8h-10l-5 8-5-8z"}),(0,o.jsx)("path",{d:"M122 252h172m-172 24h28m116 0h28"}),(0,o.jsx)("path",{fill:"#d80027",d:"M122 248a4 4 0 0 0-4 4 4 4 0 0 0 4 4h172a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm0 24a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm144 0a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4z"}),(0,o.jsx)("path",{fill:"#eee",d:"M196 168c-7 0-13 5-15 11l-5-1c-9 0-16 7-16 16s7 16 16 16c7 0 13-4 15-11a16 16 0 0 0 17-4 16 16 0 0 0 17 4 16 16 0 1 0 10-20 16 16 0 0 0-27-5c-3-4-7-6-12-6zm0 8c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm24 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm-44 10 4 1 4 8c0 4-4 7-8 7s-8-3-8-8c0-4 4-8 8-8zm64 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-7l4-8z"}),(0,o.jsx)("path",{fill:"none",d:"M220 284v12c0 7 5 12 12 12s12-5 12-12v-12z"}),(0,o.jsx)("path",{fill:"#ff9811",d:"M200 160h16v32h-16z"}),(0,o.jsx)("path",{fill:"#eee",d:"M208 224h48v48h-48z"}),(0,o.jsx)("path",{fill:"#d80027",d:"m248 208-8 8h-64l-8-8c0-13 18-24 40-24s40 11 40 24zm-88 16h48v48h-48z"}),(0,o.jsx)("rect",{width:"20",height:"32",x:"222",y:"232",fill:"#d80027",rx:"10",ry:"10"}),(0,o.jsx)("path",{fill:"#ff9811",d:"M168 232v8h8v16h-8v8h32v-8h-8v-16h8v-8zm8-16h64v8h-64z"}),(0,o.jsxs)("g",{fill:"#ffda44",children:[(0,o.jsx)("circle",{cx:"186",cy:"202",r:"6"}),(0,o.jsx)("circle",{cx:"208",cy:"202",r:"6"}),(0,o.jsx)("circle",{cx:"230",cy:"202",r:"6"})]}),(0,o.jsx)("path",{fill:"#d80027",d:"M169 272v43a24 24 0 0 0 10 4v-47h-10zm20 0v47a24 24 0 0 0 10-4v-43h-10z"}),(0,o.jsxs)("g",{fill:"#338af3",children:[(0,o.jsx)("circle",{cx:"208",cy:"272",r:"16"}),(0,o.jsx)("rect",{width:"32",height:"16",x:"264",y:"320",ry:"8"}),(0,o.jsx)("rect",{width:"32",height:"16",x:"120",y:"320",ry:"8"})]})]})]})},{width:"28px",height:"28px"})}]},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return i}});var o=r(65342),n=r(87462),a=r(67294),l=r(76416);r(48711),r(73935),r(73469);var i=(0,a.forwardRef)(function(e,t){var r=(0,o.u)(e);return a.createElement(l.S,(0,n.Z)({ref:t},r))})},86366:function(e,t,r){"use strict";r.d(t,{Z:function(){return esm_useCopyToClipboard}});var o=r(20640),n=r.n(o),a=r(67294),esm_useSetState=function(e){void 0===e&&(e={});var t=(0,a.useState)(e),r=t[0],o=t[1];return[r,(0,a.useCallback)(function(e){o(function(t){return Object.assign({},t,e instanceof Function?e(t):e)})},[])]},esm_useCopyToClipboard=function(){var e,t,r=(e=(0,a.useRef)(!1),t=(0,a.useCallback)(function(){return e.current},[]),(0,a.useEffect)(function(){return e.current=!0,function(){e.current=!1}},[]),t),o=esm_useSetState({value:void 0,error:void 0,noUserInteraction:!0}),l=o[0],i=o[1];return[l,(0,a.useCallback)(function(e){if(r())try{if("string"!=typeof e&&"number"!=typeof e){var t,o,a=Error("Cannot copy typeof "+typeof e+" to clipboard, must be a string");i({value:e,error:a,noUserInteraction:!0});return}if(""===e){var a=Error("Cannot copy empty string to clipboard.");i({value:e,error:a,noUserInteraction:!0});return}o=e.toString(),t=n()(o),i({value:o,error:void 0,noUserInteraction:t})}catch(e){i({value:o,error:e,noUserInteraction:t})}},[])]}},11742:function(e){e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],o=0;o<e.rangeCount;o++)r.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},97326:function(e,t,r){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,r){"use strict";r.d(t,{Z:function(){return _createClass}});var o=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,o.Z)(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var o=r(71002),n=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,a=_getPrototypeOf(e);if(t){var l=_getPrototypeOf(this).constructor;r=Reflect.construct(a,arguments,l)}else r=a.apply(this,arguments);return function(e,t){if(t&&("object"==(0,o.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,r)}}},60136:function(e,t,r){"use strict";r.d(t,{Z:function(){return _inherits}});var o=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.Z)(e,t)}},1413:function(e,t,r){"use strict";r.d(t,{Z:function(){return _objectSpread2}});var o=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}}}]);