import { Injectable } from '@nestjs/common';
import { MinioService, UploadedFile } from './minio.service';

@Injectable()
export class UploadsService {
  constructor(private readonly minioService: MinioService) {}

  async uploadFiles(files: Express.Multer.File[]): Promise<UploadedFile[]> {
    return this.minioService.uploadFiles(files);
  }

  async deleteFile(fileName: string): Promise<void> {
    return this.minioService.deleteFile(fileName);
  }

  async getFileUrl(fileName: string): Promise<string> {
    return this.minioService.getFileUrl(fileName);
  }

  getPublicUrl(fileName: string): string {
    return this.minioService.getPublicUrl(fileName);
  }
}
