(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9113,9930],{56357:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/verify-license",function(){return s(14752)}])},86779:function(e,t,s){"use strict";s.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var n=s(85893);let InfoIcon=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,n.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},29160:function(e,t,s){"use strict";s.d(t,{Z:function(){return AuthPageLayout}});var n=s(85893),r=s(51237);function AuthPageLayout(e){let{children:t}=e;return(0,n.jsx)("div",{className:"flex h-screen items-center justify-center bg-light sm:bg-gray-100",children:(0,n.jsxs)("div",{className:"m-auto w-full max-w-[420px] rounded bg-light p-5 sm:p-8 sm:shadow",children:[(0,n.jsx)("div",{className:"mb-2 flex justify-center",children:(0,n.jsx)(r.Z,{})}),t]})})}s(67294)},33e3:function(e,t,s){"use strict";var n=s(85893),r=s(71611),o=s(93967),i=s.n(o),a=s(67294),l=s(98388);let u={small:"text-sm h-10",medium:"h-12",big:"h-14"},c=a.forwardRef((e,t)=>{let{className:s,label:o,note:a,name:c,error:d,children:m,variant:f="normal",dimension:g="medium",shadow:p=!1,type:h="text",inputClassName:v,disabled:S,showLabel:y=!0,required:x,toolTipText:E,labelClassName:P,...w}=e,b=i()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===f,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===f,"border border-border-base focus:border-accent":"outline"===f},{"focus:shadow":p},u[g],v),N="number"===h&&S?"number-disable":"";return(0,n.jsxs)("div",{className:(0,l.m6)(s),children:[y||o?(0,n.jsx)(r.Z,{htmlFor:c,toolTipText:E,label:o,required:x,className:P}):"",(0,n.jsx)("input",{id:c,name:c,type:h,ref:t,className:(0,l.m6)(i()(S?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(N," select-none"):"",b)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:S,"aria-invalid":d?"true":"false",...w}),a&&(0,n.jsx)("p",{className:"mt-2 text-xs text-body",children:a}),d&&(0,n.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:d})]})});c.displayName="Input",t.Z=c},23091:function(e,t,s){"use strict";var n=s(85893),r=s(93967),o=s.n(r),i=s(98388);t.Z=e=>{let{className:t,...s}=e;return(0,n.jsx)("label",{className:(0,i.m6)(o()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...s})}},51237:function(e,t,s){"use strict";var n=s(85893),r=s(8152),o=s(93967),i=s.n(o),a=s(99494),l=s(48583),u=s(79362),c=s(62964),d=s(25675),m=s.n(d),f=s(11163),g=s(90573);t.Z=e=>{var t,s,o,d,p,h,v,S,y,x,E;let{className:P,...w}=e,{locale:b}=(0,f.useRouter)(),{settings:N}=(0,g.n)({language:b}),[M,A]=(0,l.KO)(u.Hf),{width:_}=(0,c.Z)();return(0,n.jsx)(r.Z,{href:null===a.siteSettings||void 0===a.siteSettings?void 0:null===(t=a.siteSettings.logo)||void 0===t?void 0:t.href,className:i()("inline-flex items-center gap-3",P),children:M&&_>=u.h2?(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:a.siteSettings.collapseLogo.width,height:a.siteSettings.collapseLogo.height},children:(0,n.jsx)(m(),{src:null!==(S=null==N?void 0:null===(o=N.options)||void 0===o?void 0:null===(s=o.collapseLogo)||void 0===s?void 0:s.original)&&void 0!==S?S:a.siteSettings.collapseLogo.url,alt:null!==(y=null==N?void 0:null===(d=N.options)||void 0===d?void 0:d.siteTitle)&&void 0!==y?y:a.siteSettings.collapseLogo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})}):(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:a.siteSettings.logo.width,height:a.siteSettings.logo.height},children:(0,n.jsx)(m(),{src:null!==(x=null==N?void 0:null===(h=N.options)||void 0===h?void 0:null===(p=h.logo)||void 0===p?void 0:p.original)&&void 0!==x?x:a.siteSettings.logo.url,alt:null!==(E=null==N?void 0:null===(v=N.options)||void 0===v?void 0:v.siteTitle)&&void 0!==E?E:a.siteSettings.logo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})})})}},71611:function(e,t,s){"use strict";var n=s(85893),r=s(86779),o=s(71943),i=s(23091),a=s(98388);t.Z=e=>{let{className:t,required:s,label:l,toolTipText:u,htmlFor:c}=e;return(0,n.jsxs)(i.Z,{className:(0,a.m6)(t),htmlFor:c,children:[l,s?(0,n.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",u?(0,n.jsx)(o.u,{content:u,children:(0,n.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,n.jsx)(r.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,s){"use strict";s.d(t,{u:function(){return Tooltip}});var n=s(85893),r=s(67294),o=s(93075),i=s(82364),a=s(24750),l=s(93967),u=s.n(l),c=s(67421),d=s(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},f={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:s,gap:l=8,animation:g="zoomIn",placement:p="top",size:h="md",rounded:v="DEFAULT",shadow:S="md",color:y="default",className:x,arrowClassName:E,showArrow:P=!0}=e,[w,b]=(0,r.useState)(!1),N=(0,r.useRef)(null),{t:M}=(0,c.$G)(),{x:A,y:_,refs:Q,strategy:R,context:I}=(0,o.YF)({placement:p,open:w,onOpenChange:b,middleware:[(0,i.x7)({element:N}),(0,i.cv)(l),(0,i.RR)(),(0,i.uY)({padding:8})],whileElementsMounted:a.Me}),{getReferenceProps:L,getFloatingProps:T}=(0,o.NI)([(0,o.XI)(I),(0,o.KK)(I),(0,o.qs)(I,{role:"tooltip"}),(0,o.bQ)(I)]),{isMounted:U,styles:k}=(0,o.Y_)(I,{duration:{open:150,close:150},...f[g]});return(0,n.jsxs)(n.Fragment,{children:[(0,r.cloneElement)(t,L({ref:Q.setReference,...t.props})),(U||w)&&(0,n.jsx)(o.ll,{children:(0,n.jsxs)("div",{role:"tooltip",ref:Q.setFloating,className:(0,d.m6)(u()(m.base,m.size[h],m.rounded[v],m.variant.solid.base,m.variant.solid.color[y],m.shadow[S],x)),style:{position:R,top:null!=_?_:0,left:null!=A?A:0,...k},...T(),children:[M("".concat(s)),P&&(0,n.jsx)(o.Y$,{ref:N,context:I,className:u()(m.arrow.color[y],E),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},44498:function(e,t,s){"use strict";s.d(t,{B:function(){return o}});var n=s(47869),r=s(3737);let o={me:()=>r.eN.get(n.P.ME),login:e=>r.eN.post(n.P.TOKEN,e),logout:()=>r.eN.post(n.P.LOGOUT,{}),register:e=>r.eN.post(n.P.REGISTER,e),update:e=>{let{id:t,input:s}=e;return r.eN.put("".concat(n.P.USERS,"/").concat(t),s)},changePassword:e=>r.eN.post(n.P.CHANGE_PASSWORD,e),forgetPassword:e=>r.eN.post(n.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>r.eN.post(n.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>r.eN.post(n.P.RESET_PASSWORD,e),makeAdmin:e=>r.eN.post(n.P.MAKE_ADMIN,e),block:e=>r.eN.post(n.P.BLOCK_USER,e),unblock:e=>r.eN.post(n.P.UNBLOCK_USER,e),addWalletPoints:e=>r.eN.post(n.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>r.eN.post(n.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...s}=e;return r.eN.get(n.P.USERS,{searchJoin:"and",with:"wallet",...s,search:r.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return r.eN.get(n.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return r.eN.get("".concat(n.P.USERS,"/").concat(t))},resendVerificationEmail:()=>r.eN.post(n.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return r.eN.post(n.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...s}=e;return r.eN.get(n.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...s})},fetchCustomers:e=>{let{...t}=e;return r.eN.get(n.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:s,name:o,...i}=e;return r.eN.get(n.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...i,search:r.eN.formatSearchParams({name:o,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:s,...o}=e;return r.eN.get(n.P.ALL_STAFFS,{searchJoin:"and",...o,search:r.eN.formatSearchParams({name:s,is_active:t})})}}},99930:function(e,t,s){"use strict";s.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var n=s(79362),r=s(97514),o=s(31955),i=s(5233),a=s(11163),l=s(88767),u=s(22920),c=s(47869),d=s(44498),m=s(28597),f=s(87066),g=s(16203);let useMeQuery=()=>{let e=(0,l.useQueryClient)(),t=(0,a.useRouter)();return(0,l.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===r.Z.verifyLicense&&t.replace(r.Z.dashboard),t.pathname===r.Z.verifyEmail&&((0,g.Fu)(!0),t.replace(r.Z.dashboard))},onError:s=>{if(f.Z.isAxiosError(s)){var n,o;if((null===(n=s.response)||void 0===n?void 0:n.status)===417){t.replace(r.Z.verifyLicense);return}if((null===(o=s.response)||void 0===o?void 0:o.status)===409){(0,g.Fu)(!1),t.replace(r.Z.verifyEmail);return}e.clear(),t.replace(r.Z.login)}}})};function useLogin(){return(0,l.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,a.useRouter)(),{t}=(0,i.$G)();return(0,l.useMutation)(d.B.logout,{onSuccess:()=>{o.Z.remove(n.E$),e.replace(r.Z.login),u.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,i.$G)();return(0,l.useMutation)(d.B.register,{onSuccess:()=>{u.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(d.B.update,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(d.B.updateEmail,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};u.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,l.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,l.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,i.$G)("common");return(0,l.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{u.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,u.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,i.$G)();(0,l.useQueryClient)();let t=(0,a.useRouter)();return(0,l.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{u.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{u.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,l.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,l.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,i.$G)();return(0,l.useMutation)(d.B.makeAdmin,{onSuccess:()=>{u.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,i.$G)();return(0,l.useMutation)(d.B.block,{onSuccess:()=>{u.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,i.$G)();return(0,l.useMutation)(d.B.unblock,{onSuccess:()=>{u.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,l.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useAdminsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useVendorsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useCustomersQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useMyStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useAllStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,l.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}}},14752:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSG:function(){return p},default:function(){return VerifyLicenseKeyActions}});var n=s(85893),r=s(29160),o=s(60802),i=s(33e3),a=s(16310);let l=a.Ry().shape({license_key:a.Z_().min(3).required("form:error-refund-reason-title-required").matches(/^([a-f0-9]{8})-(([a-f0-9]{4})-){3}([a-f0-9]{12})$/i,"Please Enter a valid License Key.")});var u=s(99930),c=s(9140),d=s(47533),m=s(5233),f=s(11163),g=s(87536),p=!0;function VerifyLicenseKeyActions(){var e;let{t}=(0,m.$G)("common");(0,u.UE)();let{mutate:s,isLoading:a}=(0,u.L8)();(0,f.useRouter)();let{register:p,handleSubmit:h,control:v,watch:S,setError:y,setValue:x,formState:{errors:E}}=(0,g.cI)({shouldUnregister:!0,resolver:(0,d.X)(l),defaultValues:{license_key:""}}),onSubmit=async e=>{let t={...e};try{await s({...t})}catch(t){let e=(0,c.e)(t);Object.keys(null==e?void 0:e.validation).forEach(t=>{y(t.split(".")[1],{type:"manual",message:null==e?void 0:e.validation[t][0]})})}};return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(r.Z,{children:[(0,n.jsx)("h3",{className:"mt-4 mb-6 text-center text-base italic text-red-500 text-body",children:t("common:license-not-verified")}),(0,n.jsx)("div",{className:"w-full space-y-3",children:(0,n.jsxs)("form",{onSubmit:h(onSubmit),children:[(0,n.jsx)("label",{htmlFor:"licenseKey",className:"mb-5 cursor-pointer",title:t("form:for-help-contact-support-portal"),children:"".concat(t("form:input-label-license-key"),"*")}),(0,n.jsx)(i.Z,{...p("license_key"),placeholder:t("form:input-label-license-key-placeholder"),id:"licenseKey",error:t(null===(e=E.license_key)||void 0===e?void 0:e.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(o.Z,{disabled:a,className:"w-full",children:t("common:authorized-nav-item-submit")})]})})]})})}},9140:function(e,t,s){"use strict";s.d(t,{e:function(){return getErrorMessage}});var n=s(11163),r=s.n(n),o=s(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let s of e.graphQLErrors){if(s.extensions&&"validation"===s.extensions.category)return t.message=s.message,t.validation=s.extensions.validation,t;s.extensions&&"authorization"===s.extensions.category&&(o.Z.remove("auth_token"),o.Z.remove("auth_permissions"),r().push("/"))}return t.message=e.message,t}}},function(e){e.O(0,[6342,4750,5921,2964,7536,2216,9494,9774,2888,179],function(){return e(e.s=56357)}),_N_E=e.O()}]);