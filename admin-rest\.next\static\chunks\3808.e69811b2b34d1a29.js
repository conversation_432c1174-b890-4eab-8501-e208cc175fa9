"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3808],{86779:function(e,t,n){n.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var l=n(85893);let InfoIcon=e=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,l.jsx)("g",{children:(0,l.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,l.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,l.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,l.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},3808:function(e,t,n){n.r(t),n.d(t,{default:function(){return AbuseReport}});var l=n(85893),a=n(5233),c=n(1587),u=n(95414),d=n(60802),f=n(70402);function AbuseReport(e){let{data:t}=e,{t:n}=(0,a.$G)("common"),{mutate:m,isLoading:p}=(0,f.Cw)();return(0,l.jsx)("div",{className:"flex h-full min-h-screen w-screen flex-col justify-center bg-light p-7 md:h-auto md:min-h-0 md:max-w-[590px] md:rounded-xl",children:(0,l.jsx)(c.l,{onSubmit:function(e){m({...t,...e})},children:e=>{let{register:t}=e;return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(u.Z,{label:n("text-report-reason"),...t("message")}),(0,l.jsx)(d.Z,{loading:p,disabled:p,children:n("text-report")})]})}})})}},1587:function(e,t,n){n.d(t,{l:function(){return Form}});var l=n(85893),a=n(87536),c=n(47533),u=n(67294);let Form=e=>{let{onSubmit:t,children:n,options:d,validationSchema:f,serverError:m,resetValues:p,...h}=e,x=(0,a.cI)({...!!f&&{resolver:(0,c.X)(f)},...!!d&&d});return(0,u.useEffect)(()=>{m&&Object.entries(m).forEach(e=>{let[t,n]=e;x.setError(t,{type:"manual",message:n})})},[m,x]),(0,u.useEffect)(()=>{p&&x.reset(p)},[p,x]),(0,l.jsx)("form",{onSubmit:x.handleSubmit(t),noValidate:!0,...h,children:n(x)})}},23091:function(e,t,n){var l=n(85893),a=n(93967),c=n.n(a),u=n(98388);t.Z=e=>{let{className:t,...n}=e;return(0,l.jsx)("label",{className:(0,u.m6)(c()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...n})}},95414:function(e,t,n){var l=n(85893),a=n(71611),c=n(93967),u=n.n(c),d=n(67294),f=n(98388);let m=d.forwardRef((e,t)=>{let{className:n,label:c,toolTipText:d,name:m,error:p,variant:h="normal",shadow:x=!1,inputClassName:g,disabled:b,required:w,...v}=e,y=u()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===h,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===h,"border border-border-base focus:border-accent":"outline"===h},{"focus:shadow":x},g);return(0,l.jsxs)("div",{className:(0,f.m6)(u()(n)),children:[c&&(0,l.jsx)(a.Z,{htmlFor:m,toolTipText:d,label:c,required:w}),(0,l.jsx)("textarea",{id:m,name:m,className:(0,f.m6)(u()(y,b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:b,...v}),p&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:p})]})});m.displayName="TextArea",t.Z=m},71611:function(e,t,n){var l=n(85893),a=n(86779),c=n(71943),u=n(23091),d=n(98388);t.Z=e=>{let{className:t,required:n,label:f,toolTipText:m,htmlFor:p}=e;return(0,l.jsxs)(u.Z,{className:(0,d.m6)(t),htmlFor:p,children:[f,n?(0,l.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",m?(0,l.jsx)(c.u,{content:m,children:(0,l.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,l.jsx)(a.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,n){n.d(t,{u:function(){return Tooltip}});var l=n(85893),a=n(67294),c=n(93075),u=n(82364),d=n(24750),f=n(93967),m=n.n(f),p=n(67421),h=n(98388);let x={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},g={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:n,gap:f=8,animation:b="zoomIn",placement:w="top",size:v="md",rounded:y="DEFAULT",shadow:E="md",color:j="default",className:N,arrowClassName:R,showArrow:I=!0}=e,[S,C]=(0,a.useState)(!1),V=(0,a.useRef)(null),{t:M}=(0,p.$G)(),{x:A,y:P,refs:z,strategy:Q,context:k}=(0,c.YF)({placement:w,open:S,onOpenChange:C,middleware:[(0,u.x7)({element:V}),(0,u.cv)(f),(0,u.RR)(),(0,u.uY)({padding:8})],whileElementsMounted:d.Me}),{getReferenceProps:D,getFloatingProps:_}=(0,c.NI)([(0,c.XI)(k),(0,c.KK)(k),(0,c.qs)(k,{role:"tooltip"}),(0,c.bQ)(k)]),{isMounted:F,styles:T}=(0,c.Y_)(k,{duration:{open:150,close:150},...g[b]});return(0,l.jsxs)(l.Fragment,{children:[(0,a.cloneElement)(t,D({ref:z.setReference,...t.props})),(F||S)&&(0,l.jsx)(c.ll,{children:(0,l.jsxs)("div",{role:"tooltip",ref:z.setFloating,className:(0,h.m6)(m()(x.base,x.size[v],x.rounded[y],x.variant.solid.base,x.variant.solid.color[j],x.shadow[E],N)),style:{position:Q,top:null!=P?P:0,left:null!=A?A:0,...T},..._(),children:[M("".concat(n)),I&&(0,l.jsx)(c.Y$,{ref:V,context:k,className:m()(x.arrow.color[j],R),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},70402:function(e,t,n){n.d(t,{Cw:function(){return useAbuseReportMutation},Bs:function(){return useDeclineReviewMutation},hI:function(){return useDeleteReviewMutation},$B:function(){return useReviewQuery},_s:function(){return useReviewsQuery}});var l=n(88767),a=n(22920),c=n(5233),u=n(28597),d=n(47869),f=n(75814),m=n(55191),p=n(3737);let h={...(0,m.h)(d.P.REVIEWS),reportAbuse:e=>p.eN.post(d.P.ABUSIVE_REPORTS,e),decline:e=>p.eN.post(d.P.ABUSIVE_REPORTS_DECLINE,e),get(e){let{id:t}=e;return p.eN.get("".concat(d.P.REVIEWS,"/").concat(t),{with:"abusive_reports.user;product;user"})},paginated:e=>{let{type:t,shop_id:n,...l}=e;return p.eN.get(d.P.REVIEWS,{searchJoin:"and",with:"product;user",...l,search:p.eN.formatSearchParams({type:t,shop_id:n})})}},useAbuseReportMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,c.$G)("common"),{closeModal:n}=(0,f.SO)();return(0,l.useMutation)(h.reportAbuse,{onSuccess:()=>{a.Am.success(t("text-abuse-report-submitted"))},onSettled:()=>{e.refetchQueries(d.P.REVIEWS),n()}})},useDeclineReviewMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,c.$G)("common");return(0,l.useMutation)(h.decline,{onSuccess:()=>{a.Am.success(t("successfully-decline"))},onSettled:()=>{e.refetchQueries(d.P.REVIEWS)}})},useDeleteReviewMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,c.$G)();return(0,l.useMutation)(h.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.REVIEWS)}})},useReviewQuery=e=>(0,l.useQuery)([d.P.REVIEWS,e],()=>h.get({id:e})),useReviewsQuery=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:a,error:c,isLoading:f}=(0,l.useQuery)([d.P.REVIEWS,e],e=>{let{queryKey:t,pageParam:n}=e;return h.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0,...n});return{reviews:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(a),error:c,loading:f}}},47533:function(e,t,n){n.d(t,{X:function(){return o}});var l=n(87536);let s=(e,t,n)=>{if(e&&"reportValidity"in e){let a=(0,l.U2)(n,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},resolvers_o=(e,t)=>{for(let n in t.fields){let l=t.fields[n];l&&l.ref&&"reportValidity"in l.ref?s(l.ref,n,e):l.refs&&l.refs.forEach(t=>s(t,n,e))}},r=(e,t)=>{t.shouldUseNativeValidation&&resolvers_o(e,t);let n={};for(let a in e){let c=(0,l.U2)(t.fields,a),u=Object.assign(e[a]||{},{ref:c&&c.ref});if(i(t.names||Object.keys(e),a)){let e=Object.assign({},(0,l.U2)(n,a));(0,l.t8)(e,"root",u),(0,l.t8)(n,a,e)}else(0,l.t8)(n,a,u)}return n},i=(e,t)=>e.some(e=>e.startsWith(t+"."));function o(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),function(a,c,u){try{return Promise.resolve(function(l,d){try{var f=(t.context,Promise.resolve(e["sync"===n.mode?"validateSync":"validate"](a,Object.assign({abortEarly:!1},t,{context:c}))).then(function(e){return u.shouldUseNativeValidation&&resolvers_o({},u),{values:n.raw?a:e,errors:{}}}))}catch(e){return d(e)}return f&&f.then?f.then(void 0,d):f}(0,function(e){var t;if(!e.inner)throw e;return{values:{},errors:r((t=!u.shouldUseNativeValidation&&"all"===u.criteriaMode,(e.inner||[]).reduce(function(e,n){if(e[n.path]||(e[n.path]={message:n.message,type:n.type}),t){var a=e[n.path].types,c=a&&a[n.type];e[n.path]=(0,l.KN)(n.path,t,e,n.type,c?[].concat(c,n.message):n.message)}return e},{})),u)}}))}catch(e){return Promise.reject(e)}}}}}]);