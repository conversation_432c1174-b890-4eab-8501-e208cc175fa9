(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2603,2007,2036],{72092:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/withdraws/[withdrawId]",function(){return r(13944)}])},92072:function(e,t,r){"use strict";var n=r(85893),o=r(93967),i=r.n(o),s=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,s.m6)(i()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},86779:function(e,t,r){"use strict";r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var n=r(85893);let InfoIcon=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,n.jsx)("g",{children:(0,n.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,n.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},97670:function(e,t,r){"use strict";r.r(t);var n=r(85893),o=r(78985),i=r(79362),s=r(8144),l=r(74673),a=r(99494),c=r(5233),d=r(1631),u=r(11163),f=r(48583),p=r(93967),m=r.n(p),x=r(30824),h=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,c.$G)(),[o,s]=(0,f.KO)(i.Hf),{childMenu:l}=t,{width:a}=(0,h.Z)();return(0,n.jsx)("div",{className:"space-y-2",children:null==l?void 0:l.map(e=>{let{href:t,label:s,icon:l,childMenu:c}=e;return(0,n.jsx)(d.Z,{href:t,label:r(s),icon:l,childMenu:c,miniSidebar:o&&a>=i.h2},s)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[r,o]=(0,f.KO)(i.Hf),s=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,l=Object.keys(s),{width:d}=(0,h.Z)();return(0,n.jsx)(n.Fragment,{children:null==l?void 0:l.map((e,o)=>{var l;return(0,n.jsxs)("div",{className:m()("flex flex-col px-5",r&&d>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,n.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&d>=i.h2?"hidden":""),children:t(null===(l=s[e])||void 0===l?void 0:l.label)}),(0,n.jsx)(SidebarItemMap,{menuItems:s[e]})]},o)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,u.useRouter)(),[a,c]=(0,f.KO)(i.Hf),[d]=(0,f.KO)(i.GH),[p]=(0,f.KO)(i.W4),{width:g}=(0,h.Z)();return(0,n.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,n.jsx)(o.Z,{}),(0,n.jsx)(l.Z,{children:(0,n.jsx)(SideBarGroup,{})}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-20",a&&g>=i.h2?"lg:w-24":"lg:w-76"),children:(0,n.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,n.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,n.jsx)(SideBarGroup,{})})})}),(0,n.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&g>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,n.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,n.jsx)(s.Z,{})]})]})]})}},66271:function(e,t,r){"use strict";var n=r(85893),o=r(93967),i=r.n(o),s=r(98388);t.Z=e=>{let{message:t,className:r}=e;return(0,n.jsx)("p",{className:(0,s.m6)(i()("my-2 text-xs text-start text-red-500",r)),children:t})}},23091:function(e,t,r){"use strict";var n=r(85893),o=r(93967),i=r.n(o),s=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,n.jsx)("label",{className:(0,s.m6)(i()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},28454:function(e,t,r){"use strict";var n=r(85893),o=r(79828),i=r(71611),s=r(87536);t.Z=e=>{let{control:t,options:r,name:l,rules:a,getOptionLabel:c,getOptionValue:d,disabled:u,isMulti:f,isClearable:p,isLoading:m,placeholder:x,label:h,required:g,toolTipText:b,error:v,...w}=e;return(0,n.jsxs)(n.Fragment,{children:[h?(0,n.jsx)(i.Z,{htmlFor:l,toolTipText:b,label:h,required:g}):"",(0,n.jsx)(s.Qr,{control:t,name:l,rules:a,...w,render:e=>{let{field:t}=e;return(0,n.jsx)(o.Z,{...t,getOptionLabel:c,getOptionValue:d,placeholder:x,isMulti:f,isClearable:p,isLoading:m,options:r,isDisabled:u})}}),v&&(0,n.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:v})]})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return o},X:function(){return n}});let n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var n=r(85893),o=r(76518),i=r(67294),s=r(23157),l=r(87077);let a=i.forwardRef((e,t)=>{let{isRTL:r}=(0,o.S)();return(0,n.jsx)(s.ZP,{ref:t,styles:l.X,isRtl:r,...e})});a.displayName="Select",t.Z=a},71611:function(e,t,r){"use strict";var n=r(85893),o=r(86779),i=r(71943),s=r(23091),l=r(98388);t.Z=e=>{let{className:t,required:r,label:a,toolTipText:c,htmlFor:d}=e;return(0,n.jsxs)(s.Z,{className:(0,l.m6)(t),htmlFor:d,children:[a,r?(0,n.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",c?(0,n.jsx)(i.u,{content:c,children:(0,n.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,n.jsx)(o.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){"use strict";r.d(t,{u:function(){return Tooltip}});var n=r(85893),o=r(67294),i=r(93075),s=r(82364),l=r(24750),a=r(93967),c=r.n(a),d=r(67421),u=r(98388);let f={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:a=8,animation:m="zoomIn",placement:x="top",size:h="md",rounded:g="DEFAULT",shadow:b="md",color:v="default",className:w,arrowClassName:j,showArrow:y=!0}=e,[N,S]=(0,o.useState)(!1),O=(0,o.useRef)(null),{t:R}=(0,d.$G)(),{x:_,y:D,refs:C,strategy:F,context:P}=(0,i.YF)({placement:x,open:N,onOpenChange:S,middleware:[(0,s.x7)({element:O}),(0,s.cv)(a),(0,s.RR)(),(0,s.uY)({padding:8})],whileElementsMounted:l.Me}),{getReferenceProps:E,getFloatingProps:Z}=(0,i.NI)([(0,i.XI)(P),(0,i.KK)(P),(0,i.qs)(P,{role:"tooltip"}),(0,i.bQ)(P)]),{isMounted:I,styles:A}=(0,i.Y_)(P,{duration:{open:150,close:150},...p[m]});return(0,n.jsxs)(n.Fragment,{children:[(0,o.cloneElement)(t,E({ref:C.setReference,...t.props})),(I||N)&&(0,n.jsx)(i.ll,{children:(0,n.jsxs)("div",{role:"tooltip",ref:C.setFloating,className:(0,u.m6)(c()(f.base,f.size[h],f.rounded[g],f.variant.solid.base,f.variant.solid.color[v],f.shadow[b],w)),style:{position:F,top:null!=D?D:0,left:null!=_?_:0,...A},...Z(),children:[R("".concat(r)),y&&(0,n.jsx)(i.Y$,{ref:O,context:P,className:c()(f.arrow.color[v],j),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},86153:function(e,t,r){"use strict";r.d(t,{by:function(){return useApproveWithdrawMutation},mJ:function(){return useCreateWithdrawMutation},rX:function(){return useWithdrawQuery},qv:function(){return useWithdrawsQuery}});var n=r(11163),o=r(88767),i=r(22920),s=r(5233),l=r(47869),a=r(28597),c=r(55191),d=r(3737);let u={...(0,c.h)(l.P.WITHDRAWS),get(e){let{id:t}=e;return d.eN.get("".concat(l.P.WITHDRAWS,"/").concat(t))},paginated:e=>{let{shop_id:t,...r}=e;return d.eN.get(l.P.WITHDRAWS,{shop_id:t,searchJoin:"and",...r,search:d.eN.formatSearchParams({shop_id:t})})},approve:e=>d.eN.post(l.P.APPROVE_WITHDRAW,e)},useCreateWithdrawMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,n.useRouter)();return(0,o.useMutation)(u.create,{onSuccess:()=>{t.push("/".concat(t.query.shop,"/withdraws"))},onSettled:()=>{e.invalidateQueries(l.P.WITHDRAWS)}})},useApproveWithdrawMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(u.approve,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.APPROVE_WITHDRAW)}})},useWithdrawQuery=e=>{let{id:t}=e,{data:r,error:n,isLoading:i}=(0,o.useQuery)([l.P.WITHDRAWS,{id:t}],()=>u.get({id:t}));return{withdraw:r,error:n,isLoading:i}},useWithdrawsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:n,error:i,isLoading:s}=(0,o.useQuery)([l.P.WITHDRAWS,e],e=>{let{queryKey:t,pageParam:r}=e;return u.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{withdraws:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(n),error:i,loading:s}}},13944:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSP:function(){return b}});var n=r(85893),o=r(16203),i=r(11163),s=r(55846),l=r(45957),a=r(60802),c=r(87536),d=r(28454),u=r(66271),f=r(5233),p=r(67294),m=r(97670),x=r(86153),h=r(92072);let g=[{name:"Approved",id:"approved"},{name:"On Hold",id:"on_hold"},{name:"Processing",id:"processing"},{name:"Pending",id:"pending"},{name:"Rejected",id:"rejected"}],Withdraw=()=>{var e,t,r;let o=(0,i.useRouter)(),{t:m}=(0,f.$G)(),{query:{withdrawId:b}}=o,{withdraw:v,error:w,isLoading:j}=(0,x.rX)({id:b});(0,p.useEffect)(()=>{(null==v?void 0:v.status)&&S("status",null==g?void 0:g.find(e=>e.id===(null==v?void 0:v.status)))},[null==v?void 0:v.status]);let{handleSubmit:y,control:N,setValue:S,formState:{errors:O}}=(0,c.cI)(),{mutate:R,isLoading:_}=(0,x.by)();return j?(0,n.jsx)(s.Z,{text:m("common:text-loading")}):w?(0,n.jsx)(l.Z,{message:w.message}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{className:"mb-6 w-full text-xl font-semibold text-heading",children:m("common:text-withdrawal-info")}),(0,n.jsx)(h.Z,{className:"flex flex-col",children:(0,n.jsxs)("div",{className:"flex flex-col items-start md:flex-row",children:[(0,n.jsxs)("form",{onSubmit:y(function(e){let{status:t}=e;R({id:b,status:t.id})}),className:"mb-5 flex w-full items-start ms-auto md:order-2 md:mb-0 md:w-1/2 md:ps-3",children:[(0,n.jsxs)("div",{className:"z-20 w-full me-5",children:[(0,n.jsx)(d.Z,{name:"status",control:N,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:g,placeholder:m("form:input-placeholder-order-status"),rules:{required:"form:error-status-required"}}),(0,n.jsx)(u.Z,{message:m(null==O?void 0:null===(e=O.status)||void 0===e?void 0:e.message)})]}),(0,n.jsxs)(a.Z,{loading:_,children:[(0,n.jsx)("span",{className:"hidden sm:block",children:m("form:button-label-change-status")}),(0,n.jsx)("span",{className:"block sm:hidden",children:m("form:form:button-label-change")})]})]}),(0,n.jsxs)("div",{className:"w-full md:order-1 md:w-1/2 md:pe-3",children:[(0,n.jsxs)("div",{className:"mb-2 flex items-center justify-start",children:[(0,n.jsxs)("div",{className:"flex w-4/12 flex-shrink-0 justify-between text-sm text-body me-5",children:[(0,n.jsx)("span",{children:m("common:text-amount")}),(0,n.jsx)("span",{children:":"})]}),(0,n.jsx)("div",{className:"flex w-full items-center rounded border border-gray-300 px-4 py-3 xl:w-5/12",children:(0,n.jsx)("span",{className:"font-semibold text-heading",children:null==v?void 0:v.amount})})]}),(0,n.jsxs)("div",{className:"mb-2 flex items-center",children:[(0,n.jsxs)("div",{className:"flex w-4/12 flex-shrink-0 justify-between text-sm text-body me-5",children:[(0,n.jsx)("span",{children:m("common:text-payment-method")}),(0,n.jsx)("span",{children:":"})]}),(0,n.jsx)("span",{className:"w-full text-sm font-semibold text-heading",children:null!==(r=null==v?void 0:v.payment_method)&&void 0!==r?r:"N/A"})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsxs)("div",{className:"flex w-4/12 flex-shrink-0 justify-between text-sm text-body me-5",children:[(0,n.jsx)("span",{children:m("common:text-status")}),(0,n.jsx)("span",{children:":"})]}),(0,n.jsx)("span",{className:"w-full text-sm font-semibold text-heading",children:null==g?void 0:null===(t=g.find(e=>e.id===(null==v?void 0:v.status)))||void 0===t?void 0:t.name})]})]})]})}),(0,n.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-5 md:grid-cols-2",children:[(null==v?void 0:v.details)&&(0,n.jsxs)(h.Z,{className:"flex flex-col",children:[(0,n.jsx)("div",{className:"mb-2 text-sm font-semibold text-heading",children:(0,n.jsxs)("span",{children:[m("common:text-details")," :"]})}),(0,n.jsx)("span",{className:"text-sm text-body",children:null==v?void 0:v.details})]}),(null==v?void 0:v.note)&&(0,n.jsxs)(h.Z,{className:"flex flex-col",children:[(0,n.jsx)("div",{className:"mb-2 text-sm font-semibold text-heading",children:(0,n.jsxs)("span",{children:[m("common:text-note")," :"]})}),(0,n.jsx)("span",{className:"text-sm text-body",children:null==v?void 0:v.note})]})]})]})};var b=!0;t.default=Withdraw,Withdraw.authenticate={permissions:o.M$},Withdraw.Layout=m.default},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return l}});var n=r(65342),o=r(87462),i=r(67294),s=r(76416);r(48711),r(73935),r(73469);var l=(0,i.forwardRef)(function(e,t){var r=(0,n.u)(e);return i.createElement(s.S,(0,o.Z)({ref:t},r))})},97326:function(e,t,r){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,r){"use strict";r.d(t,{Z:function(){return _createClass}});var n=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,(0,n.Z)(o.key),o)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var n=r(71002),o=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,i=_getPrototypeOf(e);if(t){var s=_getPrototypeOf(this).constructor;r=Reflect.construct(i,arguments,s)}else r=i.apply(this,arguments);return function(e,t){if(t&&("object"==(0,n.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,o.Z)(e)}(this,r)}}},60136:function(e,t,r){"use strict";r.d(t,{Z:function(){return _inherits}});var n=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.Z)(e,t)}},1413:function(e,t,r){"use strict";r.d(t,{Z:function(){return _objectSpread2}});var n=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,n.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,6994,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=72092)}),_N_E=e.O()}]);