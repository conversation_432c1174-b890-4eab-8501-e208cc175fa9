"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3524],{63524:function(e,t,n){n.r(t);var a=n(85893),u=n(71421),s=n(75814),r=n(15824),o=n(9140);t.default=()=>{let{mutate:e,isLoading:t}=(0,r.BW)(),{data:n}=(0,s.X9)(),{closeModal:i}=(0,s.SO)();return(0,a.jsx)(u.Z,{onCancel:i,onDelete:function(){try{e({id:n}),i()}catch(e){i(),(0,o.e)(e)}},deleteBtnLoading:t})}},15824:function(e,t,n){n.d(t,{be:function(){return useCreateTagMutation},BW:function(){return useDeleteTagMutation},wt:function(){return useTagQuery},Qd:function(){return useTagsQuery},go:function(){return useUpdateTagMutation}});var a=n(11163),u=n.n(a),s=n(88767),r=n(22920),o=n(5233),i=n(28597),l=n(47869),c=n(97514),g=n(55191),d=n(3737);let f={...(0,g.h)(l.P.TAGS),paginated:e=>{let{type:t,name:n,...a}=e;return d.eN.get(l.P.TAGS,{searchJoin:"and",...a,search:d.eN.formatSearchParams({type:t,name:n})})}};var m=n(93345);let useCreateTagMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)();return(0,s.useMutation)(f.create,{onSuccess:()=>{u().push(c.Z.tag.list,void 0,{locale:m.Config.defaultLanguage}),r.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.TAGS)}})},useDeleteTagMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)();return(0,s.useMutation)(f.delete,{onSuccess:()=>{r.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.TAGS)}})},useUpdateTagMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useRouter)(),n=(0,s.useQueryClient)();return(0,s.useMutation)(f.update,{onSuccess:async n=>{let a=t.query.shop?"/".concat(t.query.shop).concat(c.Z.tag.list):c.Z.tag.list;await t.push("".concat(a,"/").concat(null==n?void 0:n.slug,"/edit"),void 0,{locale:m.Config.defaultLanguage}),r.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(l.P.TAGS)}})},useTagQuery=e=>{let{slug:t,language:n}=e,{data:a,error:u,isLoading:r}=(0,s.useQuery)([l.P.TYPES,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{tag:a,error:u,loading:r}},useTagsQuery=e=>{var t;let{data:n,error:a,isLoading:u}=(0,s.useQuery)([l.P.TAGS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{tags:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(n),error:a,loading:u}}},9140:function(e,t,n){n.d(t,{e:function(){return getErrorMessage}});var a=n(11163),u=n.n(a),s=n(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let n of e.graphQLErrors){if(n.extensions&&"validation"===n.extensions.category)return t.message=n.message,t.validation=n.extensions.validation,t;n.extensions&&"authorization"===n.extensions.category&&(s.Z.remove("auth_token"),s.Z.remove("auth_permissions"),u().push("/"))}return t.message=e.message,t}}}]);