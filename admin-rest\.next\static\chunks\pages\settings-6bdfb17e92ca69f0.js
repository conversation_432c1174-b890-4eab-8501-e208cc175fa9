(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662,2007,2036],{73406:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings",function(){return r(50421)}])},62003:function(e,t,r){"use strict";r.d(t,{s:function(){return ChevronLeft}});var n=r(85893);let ChevronLeft=e=>(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},12032:function(e,t,r){"use strict";r.d(t,{N:function(){return SaveIcon}});var n=r(85893);let SaveIcon=e=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,n.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},97670:function(e,t,r){"use strict";r.r(t);var n=r(85893),i=r(78985),o=r(79362),l=r(8144),s=r(74673),a=r(99494),c=r(5233),d=r(1631),p=r(11163),m=r(48583),f=r(93967),b=r.n(f),g=r(30824),h=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,c.$G)(),[i,l]=(0,m.KO)(o.Hf),{childMenu:s}=t,{width:a}=(0,h.Z)();return(0,n.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:l,icon:s,childMenu:c}=e;return(0,n.jsx)(d.Z,{href:t,label:r(l),icon:s,childMenu:c,miniSidebar:i&&a>=o.h2},l)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[r,i]=(0,m.KO)(o.Hf),l=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(l),{width:d}=(0,h.Z)();return(0,n.jsx)(n.Fragment,{children:null==s?void 0:s.map((e,i)=>{var s;return(0,n.jsxs)("div",{className:b()("flex flex-col px-5",r&&d>=o.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,n.jsx)("div",{className:b()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&d>=o.h2?"hidden":""),children:t(null===(s=l[e])||void 0===s?void 0:s.label)}),(0,n.jsx)(SidebarItemMap,{menuItems:l[e]})]},i)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,p.useRouter)(),[a,c]=(0,m.KO)(o.Hf),[d]=(0,m.KO)(o.GH),[f]=(0,m.KO)(o.W4),{width:x}=(0,h.Z)();return(0,n.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,n.jsx)(i.Z,{}),(0,n.jsx)(s.Z,{children:(0,n.jsx)(SideBarGroup,{})}),(0,n.jsxs)("div",{className:"flex flex-1",children:[(0,n.jsx)("aside",{className:b()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",x>=o.h2&&(d||f)?"lg:pt-[8.75rem]":"pt-20",a&&x>=o.h2?"lg:w-24":"lg:w-76"),children:(0,n.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,n.jsx)(g.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,n.jsx)(SideBarGroup,{})})})}),(0,n.jsxs)("main",{className:b()("relative flex w-full flex-col justify-start transition-[padding] duration-300",x>=o.h2&&(d||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&x>=o.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,n.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,n.jsx)(l.Z,{})]})]})]})}},59122:function(e,t,r){"use strict";r.d(t,{Z:function(){return SettingsPageHeader}});var n=r(85893),i=r(11163),o=r(99494),l=r(8152),s=r(93967),a=r.n(s),c=r(5233),d=r(67294),p=r(86998),m=r(62003);function SettingsPageHeader(e){var t,r,s,f;let{pageTitle:b}=e,{t:g}=(0,c.$G)(),h=(0,i.useRouter)(),{sliderEl:x,sliderPrevBtn:v,sliderNextBtn:y,scrollToTheRight:S,scrollToTheLeft:j}=function(){let e=(0,d.useRef)(null),t=(0,d.useRef)(null),r=(0,d.useRef)(null);return(0,d.useEffect)(()=>{let n=e.current,i=t.current,o=r.current,l=n.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=n.offsetWidth;n.scrollWidth>e?(null==o||o.classList.remove("opacity-0","invisible"),l&&(null==n||n.classList.add("!-mb-[43px]"))):(null==o||o.classList.add("opacity-0","invisible"),l&&(null==n||n.classList.remove("!-mb-[43px]"))),null==i||i.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==n?void 0:n.scrollLeft,t=null==n?void 0:n.offsetWidth;(null==n?void 0:n.scrollWidth)-e==t?(null==o||o.classList.add("opacity-0","invisible"),null==i||i.classList.remove("opacity-0","invisible")):null==o||o.classList.remove("opacity-0","invisible"),0===e?(null==i||i.classList.add("opacity-0","invisible"),null==o||o.classList.remove("opacity-0","invisible")):null==i||i.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),n.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),n.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:r,scrollToTheRight:function(){let r=e.current.offsetWidth;e.current.scrollLeft+=r/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,r.current.classList.remove("opacity-0","invisible")}}}(),w=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(f=o.siteSettings.sidebarLinks)||void 0===f?void 0:null===(s=f.admin)||void 0===s?void 0:null===(r=s.settings)||void 0===r?void 0:null===(t=r.childMenu[0])||void 0===t?void 0:t.childMenu,N=h.asPath.split("#")[0].split("?")[0];return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:g(b)})}),(0,n.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,n.jsx)("button",{title:"Prev",ref:v,onClick:()=>j(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,n.jsx)(m.s,{className:"h-[18px] w-[18px]"})}),(0,n.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,n.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:x,children:null==w?void 0:w.map((e,t)=>(0,n.jsx)(l.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:a()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",N===e.href?"text-heading before:w-full":null),children:g(e.label)},t))})}),(0,n.jsx)("button",{title:"Next",ref:y,onClick:()=>S(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,n.jsx)(p._,{className:"h-[18px] w-[18px]"})})]})]})}},28454:function(e,t,r){"use strict";var n=r(85893),i=r(79828),o=r(71611),l=r(87536);t.Z=e=>{let{control:t,options:r,name:s,rules:a,getOptionLabel:c,getOptionValue:d,disabled:p,isMulti:m,isClearable:f,isLoading:b,placeholder:g,label:h,required:x,toolTipText:v,error:y,...S}=e;return(0,n.jsxs)(n.Fragment,{children:[h?(0,n.jsx)(o.Z,{htmlFor:s,toolTipText:v,label:h,required:x}):"",(0,n.jsx)(l.Qr,{control:t,name:s,rules:a,...S,render:e=>{let{field:t}=e;return(0,n.jsx)(i.Z,{...t,getOptionLabel:c,getOptionValue:d,placeholder:g,isMulti:m,isClearable:f,isLoading:b,options:r,isDisabled:p})}}),y&&(0,n.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:y})]})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return i},X:function(){return n}});let n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},i={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var n=r(85893),i=r(76518),o=r(67294),l=r(23157),s=r(87077);let a=o.forwardRef((e,t)=>{let{isRTL:r}=(0,i.S)();return(0,n.jsx)(l.ZP,{ref:t,styles:s.X,isRtl:r,...e})});a.displayName="Select",t.Z=a},22220:function(e,t,r){"use strict";var n=r(85893),i=r(93967),o=r.n(i),l=r(98388);t.Z=e=>{let{children:t,className:r,...i}=e;return(0,n.jsx)("div",{className:(0,l.m6)(o()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...i,children:t})}},77180:function(e,t,r){"use strict";var n=r(85893),i=r(66271),o=r(71611),l=r(77768),s=r(93967),a=r.n(s),c=r(5233),d=r(87536),p=r(98388);t.Z=e=>{let{control:t,label:r,name:s,error:m,disabled:f,required:b,toolTipText:g,className:h,labelClassName:x,...v}=e,{t:y}=(0,c.$G)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:(0,p.m6)(a()("flex items-center gap-x-4",h)),children:[(0,n.jsx)(d.Qr,{name:s,control:t,...v,render:e=>{let{field:{onChange:t,value:i}}=e;return(0,n.jsxs)(l.r,{checked:i,onChange:t,disabled:f,className:"".concat(i?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(f?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,n.jsxs)("span",{className:"sr-only",children:["Enable ",r]}),(0,n.jsx)("span",{className:"".concat(i?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),r?(0,n.jsx)(o.Z,{htmlFor:s,className:a()("mb-0",x),toolTipText:g,label:r,required:b}):""]}),m?(0,n.jsx)(i.Z,{message:m}):""]})}},64179:function(e,t,r){"use strict";r.d(t,{Zj:function(){return useCreateShippingMutation},_L:function(){return useDeleteShippingClassMutation},ET:function(){return useShippingClassesQuery},f1:function(){return useShippingQuery},eI:function(){return useUpdateShippingMutation}});var n=r(97514),i=r(11163),o=r(88767),l=r(47869),s=r(5233),a=r(22920),c=r(55191),d=r(3737);let p={...(0,c.h)(l.P.SHIPPINGS),get(e){let{id:t}=e;return d.eN.get("".concat(l.P.SHIPPINGS,"/").concat(t))},paginated:e=>{let{name:t,...r}=e;return d.eN.get(l.P.SHIPPINGS,{searchJoin:"and",...r,search:d.eN.formatSearchParams({name:t})})},all:e=>{let{name:t,...r}=e;return d.eN.get(l.P.SHIPPINGS,{searchJoin:"and",...r,search:d.eN.formatSearchParams({name:t})})}},useCreateShippingMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,i.useRouter)(),{t:r}=(0,s.$G)();return(0,o.useMutation)(p.create,{onSuccess:()=>{t.push(n.Z.shipping.list),a.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.SHIPPINGS)}})},useDeleteShippingClassMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,s.$G)();return(0,o.useMutation)(p.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.SHIPPINGS)}})},useUpdateShippingMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.update,{onSuccess:()=>{a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.SHIPPINGS)}})},useShippingQuery=e=>(0,o.useQuery)([l.P.SHIPPINGS,e],()=>p.get({id:e})),useShippingClassesQuery=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{data:t,error:r,isLoading:n}=(0,o.useQuery)([l.P.SHIPPINGS,e],e=>{let{queryKey:t,pageParam:r}=e;return p.all(Object.assign({},t[1],r))},{keepPreviousData:!0});return{shippingClasses:null!=t?t:[],error:r,loading:n}}},32232:function(e,t,r){"use strict";r.d(t,{wo:function(){return useCreateTaxClassMutation},MA:function(){return useDeleteTaxMutation},io:function(){return useTaxQuery},sQ:function(){return useTaxesQuery},y2:function(){return useUpdateTaxClassMutation}});var n=r(97514),i=r(11163),o=r(88767),l=r(47869),s=r(22920),a=r(5233),c=r(55191),d=r(3737);let p={...(0,c.h)(l.P.TAXES),get(e){let{id:t}=e;return d.eN.get("".concat(l.P.TAXES,"/").concat(t))},paginated:e=>{let{name:t,...r}=e;return d.eN.get(l.P.TAXES,{searchJoin:"and",...r,search:d.eN.formatSearchParams({name:t})})},all:e=>{let{name:t,...r}=e;return d.eN.get(l.P.TAXES,{searchJoin:"and",...r,search:d.eN.formatSearchParams({name:t})})}},useCreateTaxClassMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,i.useRouter)(),{t:r}=(0,a.$G)();return(0,o.useMutation)(p.create,{onSuccess:()=>{t.push(n.Z.tax.list),s.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.TAXES)}})},useDeleteTaxMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,a.$G)();return(0,o.useMutation)(p.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.TAXES)}})},useUpdateTaxClassMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.update,{onSuccess:()=>{s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.TAXES)}})},useTaxQuery=e=>(0,o.useQuery)([l.P.TAXES,e],()=>p.get({id:e})),useTaxesQuery=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{data:t,error:r,isLoading:n}=(0,o.useQuery)([l.P.TAXES,e],e=>{let{queryKey:t,pageParam:r}=e;return p.all(Object.assign({},t[1],r))},{keepPreviousData:!0});return{taxes:null!=t?t:[],error:r,loading:n}}},50421:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return A},default:function(){return Settings}});var n=r(85893),i=r(97670),o=r(92072),l=r(12032);let s=[{name:"openai",value:"openai"}];var a=r(16310);let c=a.Ry().shape({maximumQuestionLimit:a.Rx().positive().required("form:error-maximum-question-limit").typeError("form:error-maximum-question-limit"),minimumOrderAmount:a.Rx().transform(e=>isNaN(e)?void 0:e).moreThan(-1,"form:error-sale-price-must-positive"),freeShippingAmount:a.Rx().moreThan(-1,"form:error-free-shipping-amount-must-positive").typeError("form:error-amount-number")});var d=r(60802),p=r(80602),m=r(66272),f=r(33e3),b=r(23091),g=r(28454),h=r(22220),x=r(77180),v=r(90573),y=r(99494),S=r(3986),j=r(47533),w=r(5233),N=r(11163),C=r(67294),P=r(87536);function GeneralSettingsForm(e){var t,r,i,a,T,E,k,L,O,Z,A,R;let{settings:_,taxClasses:D,shippingClasses:F}=e,{t:M}=(0,w.$G)(),{locale:I}=(0,N.useRouter)(),{mutate:B,isLoading:G}=(0,v.B)(),{options:Q}=null!=_?_:{},[z,V]=(0,C.useState)(null==Q?void 0:Q.server_info),{register:$,handleSubmit:W,control:X,reset:K,watch:q,formState:{errors:U,isDirty:J}}=(0,P.cI)({shouldUnregister:!0,resolver:(0,j.X)(c),defaultValues:{...Q,server_info:z,logo:null!==(O=null==Q?void 0:Q.logo)&&void 0!==O?O:"",collapseLogo:null!==(Z=null==Q?void 0:Q.collapseLogo)&&void 0!==Z?Z:"",useEnableGateway:null===(A=null==Q?void 0:Q.useEnableGateway)||void 0===A||A,guestCheckout:null===(R=null==Q?void 0:Q.guestCheckout)||void 0===R||R,defaultAi:(null==Q?void 0:Q.defaultAi)?s.find(e=>e.value==(null==Q?void 0:Q.defaultAi)):"openai",taxClass:(null==D?void 0:D.length)?null==D?void 0:D.find(e=>e.id==(null==Q?void 0:Q.taxClass)):"",shippingClass:(null==F?void 0:F.length)?null==F?void 0:F.find(e=>e.id==(null==Q?void 0:Q.shippingClass)):""}}),Y=q("freeShipping");async function onSubmit(e){var t,r,n;B({language:I,options:{...Q,...e,server_info:z,signupPoints:Number(e.signupPoints),currencyToWalletRatio:Number(e.currencyToWalletRatio),minimumOrderAmount:Number(e.minimumOrderAmount),freeShippingAmount:Number(e.freeShippingAmount),defaultAi:null==e?void 0:null===(t=e.defaultAi)||void 0===t?void 0:t.value,guestCheckout:null==e?void 0:e.guestCheckout,taxClass:null==e?void 0:null===(r=e.taxClass)||void 0===r?void 0:r.id,shippingClass:null==e?void 0:null===(n=e.shippingClass)||void 0===n?void 0:n.id,logo:null==e?void 0:e.logo,collapseLogo:null==e?void 0:e.collapseLogo}}),K(e,{keepValues:!0})}(0,S.H)({isDirty:J});let ee=q("useAi"),et=Number(null==Q?void 0:null===(t=Q.server_info)||void 0===t?void 0:t.upload_max_filesize)/1024,er=(0,n.jsxs)("span",{children:[M("form:logo-help-text")," ",(0,n.jsx)("br",{}),M("form:logo-dimension-help-text")," \xa0",(0,n.jsxs)("span",{className:"font-bold",children:[y.siteSettings.logo.width,"x",y.siteSettings.logo.height," ",M("common:pixel")]}),(0,n.jsx)("br",{}),M("form:size-help-text")," \xa0",(0,n.jsxs)("span",{className:"font-bold",children:[et," MB "]})]}),en=(0,n.jsxs)("span",{children:[M("form:logo-collapse-help-text")," ",(0,n.jsx)("br",{}),M("form:logo-dimension-help-text")," \xa0",(0,n.jsxs)("span",{className:"font-bold",children:[y.siteSettings.collapseLogo.width,"x",y.siteSettings.collapseLogo.height," ",M("common:pixel")]}),(0,n.jsx)("br",{}),M("form:size-help-text")," \xa0",(0,n.jsxs)("span",{className:"font-bold",children:[et," MB "]})]});return(0,n.jsxs)("form",{onSubmit:W(onSubmit),children:[(0,n.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,n.jsx)(p.Z,{title:M("form:input-label-logo"),details:er,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,n.jsx)(o.Z,{className:"w-full logo-field-area sm:w-8/12 md:w-2/3",children:(0,n.jsx)(m.Z,{name:"logo",control:X,multiple:!1})})]}),(0,n.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,n.jsx)(p.Z,{title:M("form:input-label-collapse-logo"),details:en,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,n.jsx)(o.Z,{className:"w-full logo-field-area sm:w-8/12 md:w-2/3",children:(0,n.jsx)(m.Z,{name:"collapseLogo",control:X,multiple:!1})})]}),(0,n.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,n.jsx)(p.Z,{title:M("form:form-title-information"),details:M("form:site-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,n.jsxs)(o.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,n.jsx)(f.Z,{label:M("form:input-label-site-title"),toolTipText:M("form:input-tooltip-site-title"),...$("siteTitle"),error:M(null===(r=U.siteTitle)||void 0===r?void 0:r.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(f.Z,{label:M("form:input-label-site-subtitle"),toolTipText:M("form:input-tooltip-site-sub-title"),...$("siteSubtitle"),error:M(null===(i=U.siteSubtitle)||void 0===i?void 0:i.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(f.Z,{label:M("form:input-label-signup-points"),toolTipText:M("form:input-tooltip-signUp-point"),...$("signupPoints"),type:"number",error:M(null===(a=U.signupPoints)||void 0===a?void 0:a.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(f.Z,{label:M("form:input-label-min-order-amount"),toolTipText:M("form:input-tooltip-minimum-cart-amount"),...$("minimumOrderAmount"),type:"number",error:M(null===(T=U.minimumOrderAmount)||void 0===T?void 0:T.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(f.Z,{label:M("form:input-label-wallet-currency-ratio"),toolTipText:M("form:input-tooltip-wallet-currency-ratio"),...$("currencyToWalletRatio"),type:"number",error:M(null===(E=U.currencyToWalletRatio)||void 0===E?void 0:E.message),variant:"outline",className:"mb-5"}),(0,n.jsx)(f.Z,{label:M("form:input-label-maximum-question-limit"),toolTipText:M("form:input-tooltip-maximum-question-limit"),...$("maximumQuestionLimit"),type:"number",error:M(null===(k=U.maximumQuestionLimit)||void 0===k?void 0:k.message),variant:"outline",className:"mb-5"}),(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(g.Z,{name:"taxClass",control:X,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:D,label:M("form:input-label-tax-class"),toolTipText:M("form:input-tooltip-tax-class")})}),(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(g.Z,{name:"shippingClass",control:X,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:F,label:M("form:input-label-shipping-class"),toolTipText:M("form:input-tooltip-shipping-class")})}),(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(x.Z,{name:"useOtp",control:X,label:M("form:input-label-enable-otp"),toolTipText:M("form:input-tooltip-otp")})}),(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(x.Z,{name:"useMustVerifyEmail",control:X,label:M("form:input-label-use-must-verify-email"),toolTipText:M("form:input-tooltip-enable-verify-email")})}),(0,n.jsx)("div",{className:"mb-5",children:(0,n.jsx)(x.Z,{name:"guestCheckout",control:X,label:M("form:input-label-enable-guest-checkout"),toolTipText:M("form:input-tooltip-enable-guest-checkout")})}),(0,n.jsx)(x.Z,{name:"freeShipping",control:X,checked:Y,label:M("form:input-label-enable-free-shipping"),toolTipText:M("form:input-tooltip-enable-free-shipping")}),Y&&(0,n.jsx)(f.Z,{label:M("form:free-shipping-input-label-amount"),...$("freeShippingAmount"),error:M(null===(L=U.freeShippingAmount)||void 0===L?void 0:L.message),variant:"outline",type:"number",className:"mt-5"}),(0,n.jsx)("div",{className:"mt-5 mb-5",children:(0,n.jsx)(x.Z,{name:"useAi",control:X,label:M("form:input-label-enable-open-ai"),toolTipText:M("form:input-tooltip-enable-ai")})}),ee?(0,n.jsxs)("div",{className:"mb-5",children:[(0,n.jsx)(b.Z,{children:M("form:input-label-select-ai")}),(0,n.jsx)(g.Z,{name:"defaultAi",control:X,getOptionLabel:e=>e.name,getOptionValue:e=>e.value,options:s})]}):"",(0,n.jsx)(x.Z,{name:"isMultiCommissionRate",control:X,label:"Enable Multi Commission Rate"})]})]}),(0,n.jsx)(h.Z,{className:"z-0",children:(0,n.jsxs)(d.Z,{loading:G,disabled:G||!J,className:"text-sm md:text-base",children:[(0,n.jsx)(l.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),M("form:button-label-save-settings")]})})]})}var T=r(45957),E=r(55846),k=r(64179),L=r(32232),O=r(16203),Z=r(59122),A=!0;function Settings(){let{t:e}=(0,w.$G)(),{locale:t}=(0,N.useRouter)(),{taxes:r,loading:i}=(0,L.sQ)({limit:999}),{shippingClasses:o,loading:l}=(0,k.ET)(),{settings:s,loading:a,error:c}=(0,v.n)({language:t});return a||l||i?(0,n.jsx)(E.Z,{text:e("common:text-loading")}):c?(0,n.jsx)(T.Z,{message:c.message}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.Z,{pageTitle:"form:form-title-settings"}),(0,n.jsx)(GeneralSettingsForm,{settings:s,taxClasses:r,shippingClasses:o})]})}Settings.authenticate={permissions:O.M$},Settings.Layout=i.default},3986:function(e,t,r){"use strict";r.d(t,{H:function(){return useConfirmRedirectIfDirty}});var n=r(67294),i=r(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:r="You have unsaved changes - are you sure you wish to leave this page?"}=e,o=(0,i.useRouter)(),l=(0,n.useRef)(t),s=(0,n.useRef)(r);(0,n.useEffect)(()=>{l.current=t},[t]),(0,n.useEffect)(()=>{s.current=r},[r]);let a=(0,n.useCallback)(e=>{if(l.current)return e.preventDefault(),e.returnValue=s.current},[]),c=(0,n.useCallback)(()=>{if(l.current&&!window.confirm(s.current))throw o.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,n.useEffect)(()=>(window.addEventListener("beforeunload",a),o.events.on("routeChangeStart",c),()=>{window.removeEventListener("beforeunload",a),o.events.off("routeChangeStart",c)}),[a,c])}},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return s}});var n=r(65342),i=r(87462),o=r(67294),l=r(76416);r(48711),r(73935),r(73469);var s=(0,o.forwardRef)(function(e,t){var r=(0,n.u)(e);return o.createElement(l.S,(0,i.Z)({ref:t},r))})},97326:function(e,t,r){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,r){"use strict";r.d(t,{Z:function(){return _createClass}});var n=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(0,n.Z)(i.key),i)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var n=r(71002),i=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,o=_getPrototypeOf(e);if(t){var l=_getPrototypeOf(this).constructor;r=Reflect.construct(o,arguments,l)}else r=o.apply(this,arguments);return function(e,t){if(t&&("object"==(0,n.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,i.Z)(e)}(this,r)}}},60136:function(e,t,r){"use strict";r.d(t,{Z:function(){return _inherits}});var n=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,n.Z)(e,t)}},1413:function(e,t,r){"use strict";r.d(t,{Z:function(){return _objectSpread2}});var n=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,n.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}},95389:function(e,t,r){"use strict";r.d(t,{_:function(){return d},b:function(){return H}});var n=r(67294),i=r(19946),o=r(12351),l=r(16723),s=r(23784),a=r(73781);let c=(0,n.createContext)(null);function H(){let[e,t]=(0,n.useState)([]);return[e.length>0?e.join(" "):void 0,(0,n.useMemo)(()=>function(e){let r=(0,a.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),i=(0,n.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props}),[r,e.slot,e.name,e.props]);return n.createElement(c.Provider,{value:i},e.children)},[t])]}let d=Object.assign((0,o.yV)(function(e,t){let r=(0,i.M)(),{id:a=`headlessui-label-${r}`,passive:d=!1,...p}=e,m=function u(){let e=(0,n.useContext)(c);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),f=(0,s.T)(t);(0,l.e)(()=>m.register(a),[a,m.register]);let b={ref:f,...m.props,id:a};return d&&("onClick"in b&&(delete b.htmlFor,delete b.onClick),"onClick"in p&&delete p.onClick),(0,o.sY)({ourProps:b,theirProps:p,slot:m.slot||{},defaultTag:"label",name:m.name||"Label"})}),{})},77768:function(e,t,r){"use strict";r.d(t,{r:function(){return y}});var n=r(67294),i=r(12351),o=r(19946),l=r(61363),s=r(64103),a=r(95389),c=r(39516),d=r(14157),p=r(23784),m=r(46045),f=r(18689),b=r(73781),g=r(31147),h=r(94192);let x=(0,n.createContext)(null);x.displayName="GroupContext";let v=n.Fragment,y=Object.assign((0,i.yV)(function(e,t){let r=(0,o.M)(),{id:a=`headlessui-switch-${r}`,checked:c,defaultChecked:v=!1,onChange:y,name:S,value:j,form:w,...N}=e,C=(0,n.useContext)(x),P=(0,n.useRef)(null),T=(0,p.T)(P,t,null===C?null:C.setSwitch),[E,k]=(0,g.q)(c,y,v),L=(0,b.z)(()=>null==k?void 0:k(!E)),O=(0,b.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),L()}),Z=(0,b.z)(e=>{e.key===l.R.Space?(e.preventDefault(),L()):e.key===l.R.Enter&&(0,f.g)(e.currentTarget)}),A=(0,b.z)(e=>e.preventDefault()),R=(0,n.useMemo)(()=>({checked:E}),[E]),_={id:a,ref:T,role:"switch",type:(0,d.f)(e,P),tabIndex:0,"aria-checked":E,"aria-labelledby":null==C?void 0:C.labelledby,"aria-describedby":null==C?void 0:C.describedby,onClick:O,onKeyUp:Z,onKeyPress:A},D=(0,h.G)();return(0,n.useEffect)(()=>{var e;let t=null==(e=P.current)?void 0:e.closest("form");t&&void 0!==v&&D.addEventListener(t,"reset",()=>{k(v)})},[P,k]),n.createElement(n.Fragment,null,null!=S&&E&&n.createElement(m._,{features:m.A.Hidden,...(0,i.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:w,checked:E,name:S,value:j})}),(0,i.sY)({ourProps:_,theirProps:N,slot:R,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[r,o]=(0,n.useState)(null),[l,s]=(0,a.b)(),[d,p]=(0,c.f)(),m=(0,n.useMemo)(()=>({switch:r,setSwitch:o,labelledby:l,describedby:d}),[r,o,l,d]);return n.createElement(p,{name:"Switch.Description"},n.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(t=m.switch)?void 0:t.id,onClick(e){r&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),r.click(),r.focus({preventScroll:!0}))}}},n.createElement(x.Provider,{value:m},(0,i.sY)({ourProps:{},theirProps:e,defaultTag:v,name:"Switch.Group"}))))},Label:a._,Description:c.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,9494,5535,8186,1285,1631,5468,9774,2888,179],function(){return e(e.s=73406)}),_N_E=e.O()}]);