(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2599,2036],{62663:function(e){e.exports=function(e,t,n,a){var r=-1,l=null==e?0:e.length;for(a&&l&&(n=e[++r]);++r<l;)n=t(n,e[r],r,e);return n}},49029:function(e){var t=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;e.exports=function(e){return e.match(t)||[]}},23933:function(e,t,n){var a=n(44239),r=n(37005);e.exports=function(e){return r(e)&&"[object RegExp]"==a(e)}},18674:function(e){e.exports=function(e){return function(t){return null==e?void 0:e[t]}}},35393:function(e,t,n){var a=n(62663),r=n(53816),l=n(58748),o=RegExp("['’]","g");e.exports=function(e){return function(t){return a(l(r(t).replace(o,"")),e,"")}}},69389:function(e,t,n){var a=n(18674)({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});e.exports=a},93157:function(e){var t=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;e.exports=function(e){return t.test(e)}},65776:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var a=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==a||"symbol"!=a&&t.test(e))&&e>-1&&e%1==0&&e<n}},16612:function(e,t,n){var a=n(77813),r=n(98612),l=n(65776),o=n(13218);e.exports=function(e,t,n){if(!o(n))return!1;var s=typeof t;return("number"==s?!!(r(n)&&l(t,n.length)):"string"==s&&t in n)&&a(n[t],e)}},2757:function(e){var t="\ud800-\udfff",n="\\u2700-\\u27bf",a="a-z\\xdf-\\xf6\\xf8-\\xff",r="A-Z\\xc0-\\xd6\\xd8-\\xde",l="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",o="['’]",s="["+l+"]",u="["+a+"]",i="[^"+t+l+"\\d+"+n+a+r+"]",d="(?:\ud83c[\udde6-\uddff]){2}",f="[\ud800-\udbff][\udc00-\udfff]",c="["+r+"]",m="(?:"+u+"|"+i+")",v="(?:"+o+"(?:d|ll|m|re|s|t|ve))?",p="(?:"+o+"(?:D|LL|M|RE|S|T|VE))?",b="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\ud83c[\udffb-\udfff])?",x="[\\ufe0e\\ufe0f]?",g="(?:\\u200d(?:"+["[^"+t+"]",d,f].join("|")+")"+x+b+")*",O="(?:"+["["+n+"]",d,f].join("|")+")"+(x+b+g),h=RegExp([c+"?"+u+"+"+v+"(?="+[s,c,"$"].join("|")+")","(?:"+c+"|"+i+")+"+p+"(?="+[s,c+m,"$"].join("|")+")",c+"?"+m+"+"+v,c+"+"+p,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])","\\d+",O].join("|"),"g");e.exports=function(e){return e.match(h)||[]}},68929:function(e,t,n){var a=n(48403),r=n(35393)(function(e,t,n){return t=t.toLowerCase(),e+(n?a(t):t)});e.exports=r},53816:function(e,t,n){var a=n(69389),r=n(79833),l=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,o=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");e.exports=function(e){return(e=r(e))&&e.replace(l,a).replace(o,"")}},77813:function(e){e.exports=function(e,t){return e===t||e!=e&&t!=t}},96347:function(e,t,n){var a=n(23933),r=n(7518),l=n(31167),o=l&&l.isRegExp,s=o?r(o):a;e.exports=s},71640:function(e,t,n){var a=n(80531),r=n(40180),l=n(62689),o=n(16612),s=n(96347),u=n(83140),i=n(79833);e.exports=function(e,t,n){return(n&&"number"!=typeof n&&o(e,t,n)&&(t=n=void 0),n=void 0===n?4294967295:n>>>0)?(e=i(e))&&("string"==typeof t||null!=t&&!s(t))&&!(t=a(t))&&l(e)?r(u(e),0,n):e.split(t,n):[]}},18029:function(e,t,n){var a=n(35393),r=n(11700),l=a(function(e,t,n){return e+(n?" ":"")+r(t)});e.exports=l},58748:function(e,t,n){var a=n(49029),r=n(93157),l=n(79833),o=n(2757);e.exports=function(e,t,n){return(e=l(e),void 0===(t=n?void 0:t))?r(e)?o(e):a(e):e.match(t)||[]}},21926:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/events",function(){return n(46530)}])},46530:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return I},default:function(){return EventsSettings}});var a=n(85893),r=n(97670),l=n(92072),o=n(12032),s=n(16310);let u=s.Ry().shape({}),i=[{label:"Admin",options:[{value:"admin-statusChangeOrder",label:"Status Change Order"},{value:"admin-refundOrder",label:"Refund Order"},{value:"admin-paymentOrder",label:"Payment "}]},{label:"Store Owner",options:[{value:"vendor-statusChangeOrder",label:"Status Change Order"},{value:"vendor-refundOrder",label:"Refund Order"},{value:"vendor-paymentOrder",label:"Payment "}]},{label:"Customer",options:[{value:"customer-statusChangeOrder",label:"Status Change Order"},{value:"customer-refundOrder",label:"Refund Order"},{value:"customer-paymentOrder",label:"Payment "}]}],d=[{label:"Admin",options:[{value:"admin-statusChangeOrder",label:"Status Change Order"},{value:"admin-refundOrder",label:"Refund Order"},{value:"admin-paymentOrder",label:"Payment "}]},{label:"Store Owner",options:[{value:"vendor-statusChangeOrder",label:"Status Change Order"},{value:"vendor-refundOrder",label:"Refund Order"},{value:"vendor-paymentOrder",label:"Payment "},{value:"vendor-createQuestion",label:"Create Question"},{value:"vendor-createReview",label:"Create Review"}]},{label:"Customer",options:[{value:"customer-statusChangeOrder",label:"Status Change Order"},{value:"customer-refundOrder",label:"Refund Order"},{value:"customer-paymentOrder",label:"Payment "},{value:"customer-answerQuestion",label:"Answer Question"}]}],f=[{label:"Available for admin & vendor",options:[{value:"all-storeNotice",label:"Store notice notification"},{value:"all-order",label:"Order notification"},{value:"all-message",label:"Message notification "}]}];var c=n(60802),m=n(80602),v=n(66271),p=n(28454),b=n(22220),x=n(90573),g=n(3986),O=n(68929),h=n.n(O),E=n(18029),w=n.n(E);function formatEventOptions(e){let t={admin:{},vendor:{},customer:{},all:{}};for(let n of e){let e=null==n?void 0:n.value,a=null==e?void 0:e.match(/admin|vendor|customer|all/);if(a){let n="^".concat(a[0],"-"),r=RegExp(n,"g");t[a[0]]={...t[a[0]],[e.replace(r,"")]:!0}}}return t}function formatEventAPIData(e){let t=Object.keys(e).map(t=>Object.keys(e[t]).filter(n=>!0===e[t][n]).map(e=>({value:"".concat(t,"-").concat(h()(e)),label:"".concat(w()(e))}))).flat();return null!=t?t:{}}var j=n(47533),y=n(71640),N=n.n(y),C=n(5233),S=n(11163),A=n(67294),Z=n(87536);function EventsSettingsForm(e){var t,n,r;let{settings:s}=e,{t:O}=(0,C.$G)(),{locale:h}=(0,S.useRouter)(),[E,w]=(0,A.useState)(!1),{mutate:y,isLoading:R}=(0,x.B)(),{language:_,options:L}=null!=s?s:{},{handleSubmit:T,control:I,reset:P,formState:{errors:z,isDirty:U}}=(0,Z.cI)({shouldUnregister:!0,resolver:(0,j.X)(u),defaultValues:{...L,smsEvent:(null==L?void 0:L.smsEvent)?formatEventAPIData(null==L?void 0:L.smsEvent):null,emailEvent:(null==L?void 0:L.emailEvent)?formatEventAPIData(null==L?void 0:L.emailEvent):null,pushNotification:(null==L?void 0:L.pushNotification)?formatEventAPIData(null==L?void 0:L.pushNotification):null}});async function onSubmit(e){let t=formatEventOptions(e.smsEvent),n=formatEventOptions(e.emailEvent),a=formatEventOptions(e.pushNotification);y({language:h,options:{...L,...e,smsEvent:t,emailEvent:n,pushNotification:a}}),P(e,{keepValues:!0})}return(0,g.H)({isDirty:U}),(0,a.jsxs)("form",{onSubmit:T(onSubmit),children:[(0,a.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,a.jsx)(m.Z,{title:O("form:title-realtime-notification-settings"),details:O("form:description-realtime-notification-settings"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,a.jsx)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)(p.Z,{name:"pushNotification",control:I,getOptionLabel:e=>"all"===N()(e.value,"-")[0].toLowerCase()?"Admin & Vendor : ".concat(e.label):e.label,isCloseMenuOnSelect:!1,options:f,isMulti:!0,label:O("form:input-label-realtime-notification-options"),toolTipText:O("form:input-tooltip-notification-options")}),(0,a.jsx)(v.Z,{message:O(null===(t=z.currency)||void 0===t?void 0:t.message)})]})})]}),(0,a.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,a.jsx)(m.Z,{title:O("form:title-sms-event-settings"),details:O("form:description-sms-event-settings"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,a.jsx)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)(p.Z,{name:"smsEvent",control:I,getOptionLabel:e=>{switch(N()(e.value,"-")[0].toLowerCase()){case"customer":return"Customer: ".concat(e.label);case"vendor":return"Owner: ".concat(e.label);default:return"Admin: ".concat(e.label)}},isCloseMenuOnSelect:!1,options:i,isMulti:!0,label:O("form:input-label-sms-options"),toolTipText:O("form:input-tooltip-sms-options")}),(0,a.jsx)(v.Z,{message:O(null===(n=z.currency)||void 0===n?void 0:n.message)})]})})]}),(0,a.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:mt-8 sm:mb-3",children:[(0,a.jsx)(m.Z,{title:O("form:title-email-event-settings"),details:O("form:description-email-event-settings"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,a.jsx)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,a.jsxs)("div",{className:"mb-5",children:[(0,a.jsx)(p.Z,{name:"emailEvent",control:I,getOptionLabel:e=>{switch(N()(e.value,"-")[0].toLowerCase()){case"customer":return"Customer: ".concat(e.label);case"vendor":return"Owner: ".concat(e.label);default:return"Admin: ".concat(e.label)}},isCloseMenuOnSelect:!1,options:d,isMulti:!0,label:O("form:input-label-email-options"),toolTipText:O("form:input-tooltip-email-options")}),(0,a.jsx)(v.Z,{message:O(null===(r=z.currency)||void 0===r?void 0:r.message)})]})})]}),(0,a.jsx)(b.Z,{className:"z-0",children:(0,a.jsxs)(c.Z,{loading:R,disabled:R||!U,className:"text-sm md:text-base",children:[(0,a.jsx)(o.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),O("form:button-label-save-settings")]})})]})}var R=n(59122),_=n(45957),L=n(55846),T=n(16203),I=!0;function EventsSettings(){let{t:e}=(0,C.$G)(),{locale:t}=(0,S.useRouter)(),{settings:n,loading:r,error:l}=(0,x.n)({language:t});return r?(0,a.jsx)(L.Z,{text:e("common:text-loading")}):l?(0,a.jsx)(_.Z,{message:l.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(R.Z,{pageTitle:"form:form-title-events-settings"}),(0,a.jsx)(EventsSettingsForm,{settings:n})]})}EventsSettings.authenticate={permissions:T.M$},EventsSettings.Layout=r.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,9494,5535,8186,1285,1631,963,9774,2888,179],function(){return e(e.s=21926)}),_N_E=e.O()}]);