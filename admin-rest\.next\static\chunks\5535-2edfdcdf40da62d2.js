"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5535,9930],{80287:function(a,e,t){t.d(e,{W:function(){return SearchIcon}});var n=t(85893);let SearchIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...a,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})},94885:function(a,e,t){t.r(e),t.d(e,{AdminListIcon:function(){return AdminListIcon},AttributeIcon:function(){return AttributeIcon},AttributeValueIcon:function(){return AttributeValueIcon},AuthorIcon:function(){return AuthorIcon},CalendarScheduleIcon:function(){return CalendarScheduleIcon},CancelationIcon:function(){return CancelationIcon},CategoriesIcon:function(){return CategoriesIcon},ChatIcon:function(){return ChatIcon},ChatOwnerIcon:function(){return ChatOwnerIcon},CouponsIcon:function(){return CouponsIcon},CreateOrderIcon:function(){return CreateOrderIcon},CustomersIcon:function(){return CustomersIcon},DashboardIcon:function(){return DashboardIcon},DiaryIcon:function(){return DiaryIcon},FaqIcon:function(){return FaqIcon},FlashDealsIcon:function(){return FlashDealsIcon},FountainPenIcon:function(){return FountainPenIcon},HomeIcon:function(){return HomeIcon},InformationIcon:function(){return InformationIcon},InventoryIcon:function(){return InventoryIcon},LogOutIcon:function(){return LogOutIcon},MaintenanceIcon:function(){return MaintenanceIcon},ManufacturersIcon:function(){return ManufacturersIcon},MyShopIcon:function(){return MyShopIcon},MyShopOwnerIcon:function(){return MyShopOwnerIcon},OrderTrackingIcon:function(){return OrderTrackingIcon},OrdersIcon:function(){return OrdersIcon},OrdersStatusIcon:function(){return OrdersStatusIcon},ProductsIcon:function(){return ProductsIcon},QuestionIcon:function(){return QuestionIcon},RefundsIcon:function(){return RefundsIcon},ReviewIcon:function(){return ReviewIcon},SettingsIcon:function(){return SettingsIcon},ShippingsIcon:function(){return ShippingsIcon},ShopIcon:function(){return ShopIcon},StaffIcon:function(){return StaffIcon},StoreNoticeIcon:function(){return StoreNoticeIcon},StoreNoticeOwnerIcon:function(){return StoreNoticeOwnerIcon},TagIcon:function(){return TagIcon},TaxesIcon:function(){return TaxesIcon},TermsIcon:function(){return TermsIcon},TransactionsIcon:function(){return TransactionsIcon},TypesIcon:function(){return TypesIcon},UserIcon:function(){return UserIcon},UsersIcon:function(){return UsersIcon},VendorsIcon:function(){return VendorsIcon},WithdrawIcon:function(){return WithdrawIcon}});var n=t(85893);let AttributeIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.984 6.094 15.68 8.06a.398.398 0 0 0-.13.39l.69 2.927a.406.406 0 0 1-.606.435l-2.611-1.552a.413.413 0 0 0-.42 0L9.99 11.812a.407.407 0 0 1-.607-.435l.694-2.922a.399.399 0 0 0-.13-.391l-2.307-1.97a.4.4 0 0 1 .235-.704l3.037-.257a.407.407 0 0 0 .337-.245l1.188-2.768a.41.41 0 0 1 .75 0l1.187 2.768a.407.407 0 0 0 .34.244l3.037.258a.4.4 0 0 1 .232.704Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.7 5.476a1.029 1.029 0 0 0-.895-.71l-2.908-.247-1.135-2.644a1.035 1.035 0 0 0-1.899 0L10.73 4.52l-2.909.245a1.026 1.026 0 0 0-.585 1.802l2.2 1.875-.658 2.791a1.023 1.023 0 0 0 .953 1.26c.204.01.405-.04.58-.144l2.5-1.484 2.5 1.484a1.035 1.035 0 0 0 1.498-.526c.07-.189.082-.394.036-.59l-.659-2.791 2.2-1.875a1.022 1.022 0 0 0 .316-1.09Zm-3.426 2.11a1.02 1.02 0 0 0-.333 1.015l.571 2.422-2.17-1.29a1.034 1.034 0 0 0-1.06 0l-2.17 1.282.572-2.421a1.015 1.015 0 0 0-.332-1.016L8.456 5.967l2.507-.213a1.028 1.028 0 0 0 .86-.62l.99-2.303.987 2.303a1.03 1.03 0 0 0 .86.62l2.506.213-1.892 1.619ZM6.692 9.817l-4.375 4.375a.626.626 0 0 1-.884-.884l4.375-4.375a.625.625 0 0 1 .884.884Zm1.25 4.375-4.375 4.375a.626.626 0 0 1-.884-.884l4.375-4.375a.625.625 0 1 1 .884.884Zm5.625-.884a.624.624 0 0 1 0 .884l-4.375 4.375a.626.626 0 0 1-.884-.884l4.375-4.375a.626.626 0 0 1 .884 0Z"})]}),FaqIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M18.125 10c0 .977-1.392 1.715-1.772 2.632-.366.884.111 2.394-.608 3.113-.718.72-2.229.243-3.113.608-.913.38-1.655 1.772-2.632 1.772-.977 0-1.719-1.392-2.632-1.772-.884-.366-2.395.111-3.113-.608-.72-.718-.242-2.229-.608-3.113-.38-.913-1.772-1.655-1.772-2.632 0-.977 1.392-1.719 1.772-2.632.366-.884-.111-2.395.608-3.113.718-.72 2.23-.242 3.113-.608.917-.38 1.655-1.772 2.632-1.772.977 0 1.719 1.392 2.632 1.772.884.366 2.395-.111 3.113.608.72.718.243 2.229.608 3.113.38.917 1.772 1.655 1.772 2.632Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.645 8.033c-.294-.308-.599-.625-.714-.904-.106-.256-.112-.679-.119-1.09-.011-.762-.024-1.626-.625-2.226-.6-.601-1.464-.614-2.227-.626-.41-.006-.833-.012-1.089-.118-.278-.115-.596-.42-.904-.714C11.428 1.837 10.816 1.25 10 1.25c-.816 0-1.427.587-1.967 1.105-.308.294-.625.599-.904.714-.254.106-.679.112-1.09.119-.762.011-1.626.024-2.226.624-.601.601-.61 1.465-.626 2.228-.006.41-.012.833-.118 1.089-.115.278-.42.596-.714.904C1.837 8.572 1.25 9.184 1.25 10c0 .816.587 1.427 1.105 1.967.294.308.599.625.714.904.106.256.112.679.119 1.09.011.762.024 1.626.624 2.226.601.601 1.465.614 2.228.625.41.007.833.013 1.089.12.278.114.596.419.904.713.539.518 1.151 1.105 1.967 1.105.816 0 1.427-.587 1.967-1.105.308-.294.625-.599.904-.714.256-.106.679-.112 1.09-.119.762-.011 1.626-.024 2.226-.625.601-.6.614-1.464.625-2.227.007-.41.013-.833.12-1.089.114-.278.419-.596.713-.904.518-.539 1.105-1.151 1.105-1.967 0-.816-.587-1.427-1.105-1.967Zm-.902 3.07c-.374.39-.762.794-.967 1.29-.197.476-.206 1.021-.213 1.548-.008.547-.017 1.12-.26 1.362s-.812.252-1.362.26c-.527.007-1.072.016-1.548.213-.496.205-.9.593-1.29.967-.391.374-.79.757-1.103.757-.313 0-.715-.384-1.102-.757-.388-.373-.795-.762-1.291-.967-.477-.197-1.021-.206-1.548-.213-.547-.008-1.12-.017-1.362-.26s-.252-.812-.26-1.362c-.007-.527-.016-1.072-.213-1.548-.205-.496-.593-.9-.967-1.29-.374-.391-.757-.79-.757-1.103 0-.313.384-.715.757-1.102.373-.388.762-.795.967-1.291.197-.477.206-1.021.213-1.548.008-.547.017-1.12.26-1.362s.812-.252 1.362-.26c.527-.007 1.071-.016 1.548-.213.496-.205.9-.593 1.29-.967.391-.374.79-.757 1.103-.757.313 0 .715.384 1.102.757.388.373.795.762 1.291.967.476.197 1.021.206 1.548.213.547.008 1.12.017 1.362.26s.252.812.26 1.362c.007.527.016 1.071.213 1.548.205.496.593.9.967 1.29.374.391.757.79.757 1.103 0 .313-.384.715-.757 1.102Zm-5.805 2.96a.937.937 0 1 1-1.875 0 .937.937 0 0 1 1.874 0Zm2.187-5.626c0 1.358-1.075 2.495-2.5 2.757v.056a.624.624 0 1 1-1.25 0v-.625A.625.625 0 0 1 10 10c1.034 0 1.875-.703 1.875-1.563 0-.859-.841-1.562-1.875-1.562s-1.875.703-1.875 1.563v.312a.625.625 0 0 1-1.25 0v-.313c0-1.55 1.402-2.812 3.125-2.812s3.125 1.262 3.125 2.813Z"})]}),AuthorIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M15 7.5a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.04 16.563c-1.19-2.057-3.023-3.532-5.163-4.232a5.625 5.625 0 1 0-5.754 0c-2.14.699-3.974 2.174-5.164 4.232a.625.625 0 1 0 1.082.625c1.472-2.544 4.074-4.063 6.959-4.063s5.487 1.519 6.959 4.063a.627.627 0 0 0 1.047.079.625.625 0 0 0 .035-.704ZM5.626 7.5a4.375 4.375 0 1 1 8.75 0 4.375 4.375 0 0 1-8.75 0Z"})]}),HomeIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 9.026v7.849h-5V12.5a.624.624 0 0 0-.625-.625h-2.5a.625.625 0 0 0-.625.625v4.375h-5V9.026a.625.625 0 0 1 .205-.462l6.25-5.902a.625.625 0 0 1 .841 0l6.25 5.902a.625.625 0 0 1 .204.462Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.75 16.25H17.5V9.027a1.25 1.25 0 0 0-.404-.92l-6.25-5.897a1.25 1.25 0 0 0-1.69-.009l-.01.01-6.242 5.896a1.25 1.25 0 0 0-.404.92v7.223H1.25a.625.625 0 0 0 0 1.25h17.5a.625.625 0 1 0 0-1.25Zm-15-7.223.009-.007L10 3.125l6.242 5.893.009.008v7.224H12.5V12.5a1.25 1.25 0 0 0-1.25-1.25h-2.5A1.25 1.25 0 0 0 7.5 12.5v3.75H3.75V9.027Zm7.5 7.223h-2.5V12.5h2.5v3.75Z"})]}),AttributeValueIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32.986 19.2",...a,children:(0,n.jsxs)("g",{id:"attribute_value","data-name":"attribute value",transform:"translate(-6003.613 -2943.9)",children:[(0,n.jsx)("path",{id:"settings-outline",d:"M57.257,48.321a2.784,2.784,0,0,0-2.775,1.527,2.636,2.636,0,0,0,.545,3.054,2.828,2.828,0,0,0,3.142.53,2.679,2.679,0,0,0,1.571-2.7A2.737,2.737,0,0,0,57.257,48.321ZM63.925,51a6.312,6.312,0,0,1-.066.874l1.956,1.491a.446.446,0,0,1,.106.578l-1.85,3.112a.474.474,0,0,1-.569.193l-1.943-.76a.714.714,0,0,0-.656.074,7.168,7.168,0,0,1-.932.527.672.672,0,0,0-.382.511L59.3,59.614a.475.475,0,0,1-.462.386h-3.7a.477.477,0,0,1-.463-.373l-.291-2.011a.678.678,0,0,0-.389-.514,6.767,6.767,0,0,1-.928-.529.709.709,0,0,0-.654-.072l-1.942.76a.474.474,0,0,1-.569-.193l-1.85-3.112a.446.446,0,0,1,.106-.578l1.653-1.262a.667.667,0,0,0,.26-.592c-.016-.175-.025-.35-.025-.526s.009-.348.025-.519a.665.665,0,0,0-.263-.586l-1.652-1.262a.446.446,0,0,1-.1-.575l1.85-3.112a.474.474,0,0,1,.569-.193l1.943.76a.714.714,0,0,0,.656-.074A7.168,7.168,0,0,1,54,44.91a.672.672,0,0,0,.382-.511l.291-2.014A.475.475,0,0,1,55.135,42h3.7a.477.477,0,0,1,.463.373l.291,2.011a.678.678,0,0,0,.389.514,6.767,6.767,0,0,1,.928.529.709.709,0,0,0,.654.072l1.942-.76a.474.474,0,0,1,.569.193l1.85,3.112a.446.446,0,0,1-.106.578l-1.653,1.262a.667.667,0,0,0-.262.592C63.915,50.65,63.925,50.825,63.925,51Z",transform:"translate(5956.265 2902.5)",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"}),(0,n.jsx)("path",{id:"arrow-up-outline_1_","data-name":"arrow-up-outline (1)",d:"M112,104.5l4.5-4.5,4.5,4.5m-4.5-3.875V115.75",transform:"translate(5914.75 2845.5)",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"})]})}),CategoriesIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"m5 5 3.125 9.375h-6.25L5 5Zm10.625.938a3.438 3.438 0 1 0-6.875 0 3.438 3.438 0 0 0 6.875 0Zm-5 5.937v4.375H17.5v-4.375h-6.875Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 11.25h-6.875a.625.625 0 0 0-.625.625v4.375a.625.625 0 0 0 .625.625H17.5a.625.625 0 0 0 .625-.625v-4.375a.625.625 0 0 0-.625-.625Zm-.625 4.375H11.25V12.5h5.625v3.125ZM5.593 4.802a.625.625 0 0 0-1.186 0l-3.125 9.375a.625.625 0 0 0 .593.823h6.25a.625.625 0 0 0 .593-.823L5.593 4.802Zm-2.85 8.948L5 6.977l2.258 6.773H2.742ZM16.25 5.937a4.062 4.062 0 1 0-8.125 0 4.062 4.062 0 0 0 8.125 0Zm-6.875 0a2.812 2.812 0 1 1 5.625 0 2.812 2.812 0 0 1-5.625 0Z"})]}),CouponsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.25 10v5.625a.624.624 0 0 1-.625.625H4.375a.625.625 0 0 1-.625-.625V10h12.5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 5.625h-2.74c.03-.026.061-.05.09-.078A2.306 2.306 0 0 0 15 3.873a2.546 2.546 0 0 0-2.622-2.623 2.308 2.308 0 0 0-1.675.774c-.292.339-.529.72-.703 1.132a4.292 4.292 0 0 0-.703-1.132 2.307 2.307 0 0 0-1.675-.774A2.549 2.549 0 0 0 5 3.873a2.307 2.307 0 0 0 .774 1.674c.03.026.061.05.092.078H3.125a1.25 1.25 0 0 0-1.25 1.25v2.5a1.25 1.25 0 0 0 1.25 1.25v5a1.25 1.25 0 0 0 1.25 1.25h11.25a1.25 1.25 0 0 0 1.25-1.25v-5a1.25 1.25 0 0 0 1.25-1.25v-2.5a1.25 1.25 0 0 0-1.25-1.25Zm-5.234-2.773a1.07 1.07 0 0 1 .78-.351h.04a1.298 1.298 0 0 1 1.289 1.333 1.07 1.07 0 0 1-.352.782c-.741.656-1.971.887-2.734.968.094-.827.352-2.03.977-2.732Zm-5.007.029a1.3 1.3 0 0 1 .912-.38h.038a1.07 1.07 0 0 1 .782.35c.655.741.886 1.97.968 2.729-.76-.078-1.988-.313-2.729-.968a1.07 1.07 0 0 1-.351-.781 1.297 1.297 0 0 1 .376-.95h.004ZM3.125 6.875h6.25v2.5h-6.25v-2.5Zm1.25 3.75h5v5h-5v-5Zm11.25 5h-5v-5h5v5Zm1.25-6.25h-6.25v-2.5h6.25v2.5Z"})]}),OrdersIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 5v10h-8.75V5h8.75Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 10a.625.625 0 0 1-.625.625h-8.75a.625.625 0 0 1 0-1.25h8.75A.625.625 0 0 1 17.5 10ZM8.125 5.625h8.75a.625.625 0 0 0 0-1.25h-8.75a.625.625 0 0 0 0 1.25Zm8.75 8.75h-8.75a.625.625 0 1 0 0 1.25h8.75a.625.625 0 0 0 0-1.25ZM3.405 4.309l.345-.173v3.989a.625.625 0 0 0 1.25 0v-5a.625.625 0 0 0-.905-.56l-1.25.626a.625.625 0 0 0 .56 1.118Zm2.827 7.935a1.855 1.855 0 0 0-.75-1.246 1.942 1.942 0 0 0-2.665.367 1.846 1.846 0 0 0-.279.505.625.625 0 1 0 1.172.427.614.614 0 0 1 .092-.167.685.685 0 0 1 1.108.058.597.597 0 0 1-.044.694l-2.242 2.994a.626.626 0 0 0 .501.999h2.5a.625.625 0 0 0 0-1.25h-1.25l1.49-1.995a1.833 1.833 0 0 0 .367-1.386Z"})]}),CreateOrderIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M15.625 6.875 10 12.5H7.5V10l5.625-5.625 2.5 2.5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"m17.942 4.558-2.5-2.5a.626.626 0 0 0-.884 0l-7.5 7.5a.625.625 0 0 0-.183.442v2.5a.625.625 0 0 0 .625.625H10a.624.624 0 0 0 .442-.183l7.5-7.5a.626.626 0 0 0 0-.884Zm-8.2 7.317H8.124v-1.616l5-5 1.616 1.616-5 5Zm5.883-5.884-1.616-1.616.991-.991L16.616 5l-.991.991ZM17.5 9.375v6.875a1.25 1.25 0 0 1-1.25 1.25H3.75a1.25 1.25 0 0 1-1.25-1.25V3.75A1.25 1.25 0 0 1 3.75 2.5h6.875a.625.625 0 1 1 0 1.25H3.75v12.5h12.5V9.375a.625.625 0 1 1 1.25 0Z"})]}),OrderTrackingIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M10 10.085v8.04a.625.625 0 0 1-.3-.078l-6.875-3.764a.625.625 0 0 1-.325-.547V6.264c0-.087.019-.174.055-.254L10 10.085Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.475 5.168 10.6 1.406a1.24 1.24 0 0 0-1.2 0L2.525 5.17a1.25 1.25 0 0 0-.65 1.094v7.472a1.25 1.25 0 0 0 .65 1.094L9.4 18.592a1.24 1.24 0 0 0 1.2 0l6.875-3.763a1.251 1.251 0 0 0 .65-1.094V6.264a1.25 1.25 0 0 0-.65-1.096ZM10 2.5l6.277 3.437-2.326 1.274-6.278-3.438L10 2.5Zm0 6.875L3.723 5.937l2.649-1.45 6.276 3.438L10 9.375ZM3.125 7.031l6.25 3.42v6.703l-6.25-3.418V7.03Zm13.75 6.702-6.25 3.42v-6.698l2.5-1.368v2.788a.625.625 0 1 0 1.25 0V8.402l2.5-1.37v6.7Z"})]}),TransactionsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 3.75v8.125a.624.624 0 0 1-.625.625h-3.125v3.75a.624.624 0 0 1-.625.625H3.75a.625.625 0 0 1-.625-.625V8.125A.625.625 0 0 1 3.75 7.5h3.125V3.75a.625.625 0 0 1 .625-.625h8.75a.625.625 0 0 1 .625.625Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 3.75v8.125a1.25 1.25 0 0 1-1.25 1.25H7.759l.808.808a.626.626 0 0 1-.884.884l-1.875-1.875a.625.625 0 0 1 0-.884l1.875-1.875a.625.625 0 1 1 .884.884l-.808.808h8.491V3.75H7.5v.625a.625.625 0 0 1-1.25 0V3.75A1.25 1.25 0 0 1 7.5 2.5h8.75a1.25 1.25 0 0 1 1.25 1.25ZM13.125 15a.624.624 0 0 0-.625.625v.625H3.75V8.125h8.491l-.808.808a.625.625 0 0 0 .884.884l1.875-1.875a.626.626 0 0 0 0-.884l-1.875-1.875a.625.625 0 0 0-.884.884l.808.808H3.75a1.25 1.25 0 0 0-1.25 1.25v8.125a1.25 1.25 0 0 0 1.25 1.25h8.75a1.25 1.25 0 0 0 1.25-1.25v-.625a.624.624 0 0 0-.625-.625Z"})]}),OrdersStatusIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17.717 20.3",stroke:"currentColor",...a,children:(0,n.jsxs)("g",{"data-name":"order status",children:[(0,n.jsx)("g",{"data-name":"Group 53",children:(0,n.jsx)("g",{"data-name":"Group 52",children:(0,n.jsx)("path",{"data-name":"Path 82",d:"M17.488 5.67l-.015-.026a.945.945 0 00-.071-.122L14.62.712a1.128 1.128 0 00-.973-.562H4.071a1.128 1.128 0 00-.975.563L.268 5.619a.587.587 0 00-.057.137.991.991 0 00-.062.345v12.671a1.38 1.38 0 001.379 1.378h14.66a1.38 1.38 0 001.378-1.378V6.05v-.051a.583.583 0 00-.078-.329zM9.465 1.322h4.155l2.188 3.784H9.465zm-5.367 0h4.19v3.784H1.917zm12.3 17.449a.206.206 0 01-.206.206H1.528a.206.206 0 01-.206-.206V6.279h15.073z",stroke:"currentColor",strokeWidth:".3"})})}),(0,n.jsx)("g",{"data-name":"Group 61",children:(0,n.jsx)("g",{"data-name":"Group 60",children:(0,n.jsx)("path",{"data-name":"Path 86",d:"M8.877 8.059a4.545 4.545 0 104.546 4.545 4.551 4.551 0 00-4.546-4.545zm0 8.427a3.882 3.882 0 113.882-3.882 3.886 3.886 0 01-3.882 3.882z",stroke:"currentColor",strokeWidth:".6"})})}),(0,n.jsx)("g",{"data-name":"Group 63",children:(0,n.jsx)("g",{"data-name":"Group 62",children:(0,n.jsx)("path",{"data-name":"Path 87",d:"M10.425 12.604H8.987v-1.991a.332.332 0 00-.664 0v2.322a.332.332 0 00.332.332h1.77a.332.332 0 000-.664z",stroke:"currentColor",strokeWidth:".6"})})})]})}),CancelationIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 10a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M12.942 7.942 10.884 10l2.058 2.058a.624.624 0 1 1-.884.884L10 10.884l-2.058 2.058a.624.624 0 1 1-.884-.884L9.116 10 7.058 7.942a.625.625 0 0 1 .884-.884L10 9.116l2.058-2.058a.626.626 0 0 1 .884.884ZM18.125 10A8.125 8.125 0 1 1 10 1.875 8.133 8.133 0 0 1 18.125 10Zm-1.25 0A6.875 6.875 0 1 0 10 16.875 6.883 6.883 0 0 0 16.875 10Z"})]}),ProductsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M10 10.085v8.04a.625.625 0 0 1-.3-.078l-6.875-3.764a.625.625 0 0 1-.325-.547V6.264c0-.087.019-.174.055-.254L10 10.085Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.475 5.168 10.6 1.406a1.24 1.24 0 0 0-1.2 0L2.525 5.17a1.25 1.25 0 0 0-.65 1.094v7.472a1.25 1.25 0 0 0 .65 1.094L9.4 18.592a1.24 1.24 0 0 0 1.2 0l6.875-3.763a1.251 1.251 0 0 0 .65-1.094V6.264a1.25 1.25 0 0 0-.65-1.096ZM10 2.5l6.277 3.437-2.326 1.274-6.278-3.438L10 2.5Zm0 6.875L3.723 5.937l2.649-1.45 6.276 3.438L10 9.375ZM3.125 7.031l6.25 3.42v6.703l-6.25-3.418V7.03Zm13.75 6.702-6.25 3.42v-6.698l2.5-1.368v2.788a.625.625 0 1 0 1.25 0V8.402l2.5-1.37v6.7Z"})]}),DashboardIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M8.75 4.375v3.75a.625.625 0 0 1-.625.625h-3.75a.625.625 0 0 1-.625-.625v-3.75a.625.625 0 0 1 .625-.625h3.75a.625.625 0 0 1 .625.625Zm6.875-.625h-3.75a.625.625 0 0 0-.625.625v3.75a.625.625 0 0 0 .625.625h3.75a.625.625 0 0 0 .625-.625v-3.75a.625.625 0 0 0-.625-.625Zm-7.5 7.5h-3.75a.625.625 0 0 0-.625.625v3.75a.625.625 0 0 0 .625.625h3.75a.625.625 0 0 0 .625-.625v-3.75a.625.625 0 0 0-.625-.625Zm7.5 0h-3.75a.624.624 0 0 0-.625.625v3.75a.624.624 0 0 0 .625.625h3.75a.624.624 0 0 0 .625-.625v-3.75a.624.624 0 0 0-.625-.625Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M15.625 10.625h-3.75a1.25 1.25 0 0 0-1.25 1.25v3.75a1.25 1.25 0 0 0 1.25 1.25h3.75a1.25 1.25 0 0 0 1.25-1.25v-3.75a1.25 1.25 0 0 0-1.25-1.25Zm0 5h-3.75v-3.75h3.75v3.75Zm-7.5-12.5h-3.75a1.25 1.25 0 0 0-1.25 1.25v3.75a1.25 1.25 0 0 0 1.25 1.25h3.75a1.25 1.25 0 0 0 1.25-1.25v-3.75a1.25 1.25 0 0 0-1.25-1.25Zm0 5h-3.75v-3.75h3.75v3.75Zm7.5-5h-3.75a1.25 1.25 0 0 0-1.25 1.25v3.75a1.25 1.25 0 0 0 1.25 1.25h3.75a1.25 1.25 0 0 0 1.25-1.25v-3.75a1.25 1.25 0 0 0-1.25-1.25Zm0 5h-3.75v-3.75h3.75v3.75Zm-7.5 2.5h-3.75a1.25 1.25 0 0 0-1.25 1.25v3.75a1.25 1.25 0 0 0 1.25 1.25h3.75a1.25 1.25 0 0 0 1.25-1.25v-3.75a1.25 1.25 0 0 0-1.25-1.25Zm0 5h-3.75v-3.75h3.75v3.75Z"})]}),SettingsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M15.625 3.125v13.75H4.375V3.125h11.25Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M10.625 9.375v7.5a.624.624 0 1 1-1.25 0v-7.5a.625.625 0 0 1 1.25 0Zm5 5.625a.624.624 0 0 0-.625.625v1.25a.624.624 0 1 0 1.25 0v-1.25a.624.624 0 0 0-.625-.625Zm1.875-2.5h-1.25V3.125a.625.625 0 1 0-1.25 0V12.5h-1.25a.624.624 0 1 0 0 1.25h3.75a.624.624 0 1 0 0-1.25Zm-13.125 0a.625.625 0 0 0-.625.625v3.75a.625.625 0 1 0 1.25 0v-3.75a.625.625 0 0 0-.625-.625ZM6.25 10H5V3.125a.625.625 0 0 0-1.25 0V10H2.5a.625.625 0 1 0 0 1.25h3.75a.625.625 0 1 0 0-1.25Zm5.625-3.75h-1.25V3.125a.625.625 0 1 0-1.25 0V6.25h-1.25a.625.625 0 0 0 0 1.25h3.75a.625.625 0 1 0 0-1.25Z"})]}),ShopIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"m17.5 5.625-2.228 7.243a1.25 1.25 0 0 1-1.196.882H6.568a1.25 1.25 0 0 1-1.202-.906L3.304 5.625H17.5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M7.5 16.875a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0Zm6.875-1.25a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5Zm3.723-9.816-2.23 7.243a1.866 1.866 0 0 1-1.791 1.323H6.568a1.883 1.883 0 0 1-1.802-1.36l-2.827-9.89H.625a.625.625 0 0 1 0-1.25h1.314a1.256 1.256 0 0 1 1.202.906L3.775 5H17.5a.625.625 0 0 1 .598.809Zm-1.444.441H4.132l1.835 6.422a.625.625 0 0 0 .601.453h7.509a.625.625 0 0 0 .597-.441l1.98-6.434Z"})]}),MyShopIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 4.375V6.25h-15V4.375a.625.625 0 0 1 .625-.625h13.75a.625.625 0 0 1 .625.625Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 3.125H3.125a1.25 1.25 0 0 0-1.25 1.25v11.25a1.25 1.25 0 0 0 1.25 1.25h13.75a1.25 1.25 0 0 0 1.25-1.25V4.375a1.25 1.25 0 0 0-1.25-1.25Zm0 1.25v1.25H3.125v-1.25h13.75Zm0 11.25H3.125v-8.75h13.75v8.75ZM13.75 8.75a3.75 3.75 0 0 1-7.5 0 .625.625 0 0 1 1.25 0 2.5 2.5 0 0 0 5 0 .625.625 0 1 1 1.25 0Z"})]}),MyShopOwnerIcon=a=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,n.jsx)("path",{opacity:.2,d:"M17.5 7.5v1.25a2.5 2.5 0 01-5 0V7.5h-5v1.25a2.5 2.5 0 01-5 0V7.5l1.12-3.922a.625.625 0 01.599-.453H15.78a.625.625 0 01.601.453L17.5 7.5z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M18.125 7.5a.62.62 0 00-.023-.172L16.98 3.406a1.256 1.256 0 00-1.199-.906H4.22a1.256 1.256 0 00-1.2.906L1.9 7.328a.617.617 0 00-.024.172v1.25a3.125 3.125 0 001.25 2.5v5a1.25 1.25 0 001.25 1.25h11.25a1.25 1.25 0 001.25-1.25v-5a3.125 3.125 0 001.25-2.5V7.5zM4.219 3.75H15.78l.892 3.125H3.33l.89-3.125zm3.906 4.375h3.75v.625a1.875 1.875 0 11-3.75 0v-.625zm-1.25 0v.625a1.875 1.875 0 11-3.75 0v-.625h3.75zm8.75 8.125H4.375v-4.438A3.126 3.126 0 007.5 10.624a3.126 3.126 0 005 .001 3.125 3.125 0 003.125 1.188v4.437zM15 10.625a1.875 1.875 0 01-1.875-1.875v-.625h3.75v.625A1.875 1.875 0 0115 10.625z",fill:"currentColor"})]}),TagIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"m18.567 11.953-6.614 6.614a.625.625 0 0 1-.883 0l-7.762-7.76a.625.625 0 0 1-.183-.44V3.124h7.241c.166 0 .325.066.442.183l7.76 7.76a.626.626 0 0 1 0 .885Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M19.009 10.625 11.25 2.866a1.238 1.238 0 0 0-.884-.366H3.125a.625.625 0 0 0-.625.625v7.241a1.239 1.239 0 0 0 .366.884l7.759 7.759a1.25 1.25 0 0 0 1.768 0l6.616-6.616a1.25 1.25 0 0 0 0-1.768Zm-7.5 7.5L3.75 10.366V3.75h6.616l7.759 7.759-6.616 6.616ZM7.5 6.563a.938.938 0 1 1-1.875 0 .938.938 0 0 1 1.875 0Z"})]}),AdminListIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsxs)("g",{fill:"currentColor",clipPath:"url(#a)",children:[(0,n.jsx)("path",{d:"M9.375 8.125a3.125 3.125 0 1 1-6.25 0 3.125 3.125 0 0 1 6.25 0Z",opacity:.2}),(0,n.jsx)("path",{d:"M11.25 6.25a.625.625 0 0 1 .625-.625h7.5a.625.625 0 1 1 0 1.25h-7.5a.625.625 0 0 1-.625-.625Zm8.125 3.125h-7.5a.625.625 0 0 0 0 1.25h7.5a.624.624 0 1 0 0-1.25Zm0 3.75H13.75a.625.625 0 1 0 0 1.25h5.625a.624.624 0 1 0 0-1.25Zm-7.52 1.719a.625.625 0 0 1-1.211.313c-.482-1.87-2.37-3.282-4.395-3.282-2.024 0-3.913 1.41-4.394 3.281a.625.625 0 0 1-1.211-.312c.436-1.696 1.706-3.07 3.317-3.75a3.75 3.75 0 1 1 4.576 0c1.612.68 2.882 2.054 3.318 3.75ZM6.25 10.625a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"a",children:(0,n.jsx)("path",{fill:"#fff",d:"M0 0h20v20H0z"})})})]}),TypesIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 86.02 86.02",...a,fill:"currentColor",children:(0,n.jsx)("path",{d:"M.354 48.874l.118 25.351a.89.89 0 00.467.779l20.249 10.602a.886.886 0 00.421.106h.002a.926.926 0 00.211.182.882.882 0 00.878.02l19.992-10.842a.89.89 0 00.392-.445.898.898 0 00.364.379l20.248 10.602a.89.89 0 00.422.106h.002a.965.965 0 00.21.182.894.894 0 00.878.02L85.2 75.071a.89.89 0 00.467-.783V47.911c0-.008-.004-.016-.004-.022l.002-.021c-.001-.023-.01-.049-.014-.072a1.174 1.174 0 00-.027-.146.91.91 0 00-.038-.093c-.019-.042-.037-.082-.062-.12a1.048 1.048 0 00-.154-.181.925.925 0 00-.083-.066c-.02-.012-.034-.03-.056-.043-.02-.011-.041-.017-.062-.025-.019-.01-.03-.022-.049-.029l-20.603-9.978c-.082-.034-.17-.038-.257-.047V10.865l-.002-.022c-.001-.007.001-.013.001-.02-.001-.025-.012-.049-.015-.073a1.159 1.159 0 00-.027-.145.755.755 0 00-.038-.093c-.02-.042-.036-.083-.062-.12-.02-.03-.041-.057-.062-.084a.88.88 0 00-.174-.162c-.021-.014-.035-.032-.056-.045-.021-.011-.042-.016-.062-.026-.019-.009-.031-.021-.048-.027L43.118.07a.885.885 0 00-.746.025L22.009 10.71a.89.89 0 00-.489.79l.002.016a.886.886 0 00-.063.312l.118 25.233a.892.892 0 00-.311.079L.903 47.755a.891.891 0 00-.489.791c0 .005.003.009.003.015a.844.844 0 00-.063.313zm60.967-37.91L43.372 21l-19.005-9.485 18.438-9.646 18.516 9.095zm1.165 26.044l-18.214 9.586V22.535l18.214-10.18v24.653zm3.188 22.572l18.214-10.179v24.355l-18.214 9.883V59.58zM45.77 48.559l18.438-9.646 18.515 9.099-17.948 10.033-19.005-9.486zM23.165 59.58L41.38 49.402v24.355l-18.215 9.882V59.58zM3.262 48.559L21.7 38.913l18.515 9.099-17.949 10.033-19.004-9.486z"})}),CustomersIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M8.125 3.125a1.875 1.875 0 1 1 3.75 0 1.875 1.875 0 0 1-3.75 0Zm8.476 7.774-3.54-4.014a1.875 1.875 0 0 0-1.407-.635H8.346a1.875 1.875 0 0 0-1.406.635L3.4 10.9a.94.94 0 0 0 1.327 1.328L7.5 10l-1.787 6.791a.938.938 0 0 0 1.7.793L10 13.125l2.588 4.459a.938.938 0 0 0 1.699-.793L12.5 10l2.774 2.226a.939.939 0 1 0 1.328-1.328l-.001.001Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M12.5 3.125a2.5 2.5 0 1 0-5 0 2.5 2.5 0 0 0 5 0ZM10 4.375a1.25 1.25 0 1 1 0-2.5 1.25 1.25 0 0 1 0 2.5Zm7.058 6.098-3.53-4.002a2.5 2.5 0 0 0-1.874-.846H8.346a2.5 2.5 0 0 0-1.875.846l-3.529 4.002a1.562 1.562 0 0 0 2.198 2.22l1.273-1.021-1.292 4.912a1.563 1.563 0 0 0 2.848 1.291L10 14.37l2.031 3.505a1.562 1.562 0 0 0 2.845-1.29l-1.29-4.913 1.274 1.022a1.563 1.563 0 0 0 2.198-2.221Zm-.9 1.31a.311.311 0 0 1-.442 0c-.016-.016-.032-.031-.05-.045L12.89 9.512a.625.625 0 0 0-.996.644l1.788 6.797c.009.036.022.072.037.106a.312.312 0 1 1-.566.264.502.502 0 0 0-.026-.05l-2.587-4.46a.625.625 0 0 0-1.082 0L6.875 17.27a.487.487 0 0 0-.026.049.313.313 0 0 1-.567-.264.601.601 0 0 0 .038-.106l1.785-6.793a.625.625 0 0 0-.996-.644l-2.775 2.226c-.018.014-.034.03-.05.045a.311.311 0 0 1-.517-.097.313.313 0 0 1 .074-.345.46.46 0 0 0 .027-.028l3.54-4.015a1.25 1.25 0 0 1 .938-.423h3.308a1.25 1.25 0 0 1 .937.423l3.541 4.014.027.03a.313.313 0 0 1 0 .44Z"})]}),InventoryIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 6.25 10 10.625 2.5 6.25 10 1.875l7.5 4.375Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.04 13.437a.625.625 0 0 1-.227.853l-7.5 4.375a.625.625 0 0 1-.63 0l-7.5-4.375a.625.625 0 0 1 .63-1.08L10 17.401l7.188-4.19a.625.625 0 0 1 .852.226Zm-.852-3.977L10 13.651l-7.188-4.19a.625.625 0 0 0-.624 1.079l7.5 4.375a.625.625 0 0 0 .63 0l7.5-4.375a.627.627 0 0 0 .068-1.043.626.626 0 0 0-.698-.037ZM1.874 6.25a.625.625 0 0 1 .313-.54l7.5-4.375a.625.625 0 0 1 .63 0l7.5 4.375a.625.625 0 0 1 0 1.08l-7.5 4.375a.625.625 0 0 1-.63 0l-7.5-4.375a.625.625 0 0 1-.313-.54Zm1.866 0L10 9.902l6.26-3.652L10 2.598 3.74 6.25Z"})]}),UsersIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M13.125 11.25a3.125 3.125 0 1 1-6.25 0 3.125 3.125 0 0 1 6.25 0ZM5 4.375a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm10 0a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M19.125 11.75a.625.625 0 0 1-.875-.125A4.032 4.032 0 0 0 15 10a.625.625 0 1 1 0-1.25 1.875 1.875 0 1 0-1.816-2.344.626.626 0 0 1-1.21-.312 3.125 3.125 0 1 1 5.135 3.086c.85.368 1.589.952 2.143 1.694a.624.624 0 0 1-.127.876Zm-4.21 4.812a.623.623 0 0 1-.454.947.623.623 0 0 1-.627-.322 4.454 4.454 0 0 0-7.668 0 .625.625 0 1 1-1.082-.625 5.63 5.63 0 0 1 2.636-2.337 3.75 3.75 0 1 1 4.56 0 5.63 5.63 0 0 1 2.636 2.337ZM10 13.75a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM5.625 9.375A.625.625 0 0 0 5 8.75a1.875 1.875 0 1 1 1.816-2.344.625.625 0 1 0 1.21-.312A3.125 3.125 0 1 0 2.892 9.18 5.308 5.308 0 0 0 .75 10.874a.625.625 0 0 0 1 .75A4.031 4.031 0 0 1 5 10a.625.625 0 0 0 .625-.625Z"})]}),UserIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 10a7.483 7.483 0 0 1-2.484 5.576A5.625 5.625 0 0 0 10 12.5a3.125 3.125 0 1 0 0-6.25 3.125 3.125 0 0 0 0 6.25 5.624 5.624 0 0 0-5.016 3.076A7.5 7.5 0 1 1 17.5 10Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M10 1.875A8.125 8.125 0 1 0 18.125 10 8.133 8.133 0 0 0 10 1.875ZM5.787 15.43a5 5 0 0 1 8.425 0 6.862 6.862 0 0 1-8.425 0ZM7.5 9.375a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Zm7.637 5.188a6.223 6.223 0 0 0-2.817-2.246 3.75 3.75 0 1 0-4.64 0 6.223 6.223 0 0 0-2.817 2.246 6.875 6.875 0 1 1 10.274 0Z"})]}),StaffIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M8.75 13.125a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM6.25 2.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm7.5 8.125a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Zm0-3.125a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M2.125 9.875A.625.625 0 0 0 3 9.75a4.063 4.063 0 0 1 6.5 0 .625.625 0 0 0 1 0 4.062 4.062 0 0 1 6.5 0A.625.625 0 0 0 18 9a5.3 5.3 0 0 0-2.14-1.695 3.125 3.125 0 1 0-4.215 0 5.256 5.256 0 0 0-1.64 1.118 5.256 5.256 0 0 0-1.64-1.118 3.125 3.125 0 1 0-4.215 0A5.303 5.303 0 0 0 2 9a.625.625 0 0 0 .125.875Zm11.625-6.75a1.875 1.875 0 1 1 0 3.75 1.875 1.875 0 0 1 0-3.75Zm-7.5 0a1.875 1.875 0 1 1 0 3.75 1.875 1.875 0 0 1 0-3.75Zm9.61 12.305a3.125 3.125 0 1 0-4.215 0 5.255 5.255 0 0 0-1.64 1.118 5.255 5.255 0 0 0-1.64-1.118 3.125 3.125 0 1 0-4.215 0A5.302 5.302 0 0 0 2 17.125a.625.625 0 0 0 1 .75 4.063 4.063 0 0 1 6.5 0 .625.625 0 0 0 1 0 4.063 4.063 0 0 1 6.5 0 .625.625 0 0 0 1-.75 5.3 5.3 0 0 0-2.14-1.695Zm-9.61-4.18a1.875 1.875 0 1 1 0 3.75 1.875 1.875 0 0 1 0-3.75Zm7.5 0a1.875 1.875 0 1 1 0 3.75 1.875 1.875 0 0 1 0-3.75Z"})]}),VendorsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 7.5v1.25a2.5 2.5 0 0 1-5 0V7.5h-5v1.25a2.5 2.5 0 0 1-5 0V7.5l1.12-3.922a.625.625 0 0 1 .599-.453H15.78a.625.625 0 0 1 .601.453L17.5 7.5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.125 7.5a.62.62 0 0 0-.023-.172L16.98 3.406a1.256 1.256 0 0 0-1.199-.906H4.22a1.256 1.256 0 0 0-1.2.906L1.9 7.328a.617.617 0 0 0-.024.172v1.25a3.125 3.125 0 0 0 1.25 2.5v5a1.25 1.25 0 0 0 1.25 1.25h11.25a1.25 1.25 0 0 0 1.25-1.25v-5a3.125 3.125 0 0 0 1.25-2.5V7.5ZM4.219 3.75H15.78l.892 3.125H3.33l.89-3.125Zm3.906 4.375h3.75v.625a1.875 1.875 0 1 1-3.75 0v-.625Zm-1.25 0v.625a1.875 1.875 0 1 1-3.75 0v-.625h3.75Zm8.75 8.125H4.375v-4.438A3.126 3.126 0 0 0 7.5 10.624a3.126 3.126 0 0 0 5 .001 3.125 3.125 0 0 0 3.125 1.188v4.437ZM15 10.625a1.875 1.875 0 0 1-1.875-1.875v-.625h3.75v.625A1.875 1.875 0 0 1 15 10.625Z"})]}),ShippingsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M18.75 9.375v5a.624.624 0 0 1-.625.625H16.25a1.875 1.875 0 1 0-3.75 0h-5a1.875 1.875 0 1 0-3.75 0H1.875a.625.625 0 0 1-.625-.625V11.25h12.5V9.375h5Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"m19.33 9.14-1.094-2.734a1.245 1.245 0 0 0-1.16-.781h-2.701V5a.625.625 0 0 0-.625-.625H1.875a1.25 1.25 0 0 0-1.25 1.25v8.75a1.25 1.25 0 0 0 1.25 1.25h1.328a2.5 2.5 0 0 0 4.844 0h3.906a2.5 2.5 0 0 0 4.844 0h1.328a1.25 1.25 0 0 0 1.25-1.25v-5c0-.08-.015-.16-.045-.234Zm-4.955-2.265h2.702l.75 1.875h-3.452V6.875Zm-12.5-1.25h11.25v5H1.875v-5Zm3.75 10.625a1.25 1.25 0 1 1 0-2.5 1.25 1.25 0 0 1 0 2.5Zm6.328-1.875H8.047a2.5 2.5 0 0 0-4.844 0H1.875v-2.5h11.25v.962a2.507 2.507 0 0 0-1.172 1.538Zm2.422 1.875a1.25 1.25 0 1 1 0-2.5 1.25 1.25 0 0 1 0 2.5Zm3.75-1.875h-1.328a2.505 2.505 0 0 0-2.422-1.875V10h3.75v4.375Z"})]}),InformationIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 10a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M11.25 13.75a.624.624 0 0 1-.625.625 1.25 1.25 0 0 1-1.25-1.25V10a.625.625 0 0 1 0-1.25 1.25 1.25 0 0 1 1.25 1.25v3.125a.624.624 0 0 1 .625.625ZM18.125 10A8.125 8.125 0 1 1 10 1.875 8.133 8.133 0 0 1 18.125 10Zm-1.25 0A6.875 6.875 0 1 0 10 16.875 6.883 6.883 0 0 0 16.875 10ZM9.687 7.5a.938.938 0 1 0 0-1.875.938.938 0 0 0 0 1.875Z"})]}),FlashDealsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"m7.5 18.75 1.25-6.25-5-1.875L12.5 1.25 11.25 7.5l5 1.875L7.5 18.75Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M16.859 9.232a.625.625 0 0 0-.391-.442l-4.5-1.689 1.145-5.729a.625.625 0 0 0-1.07-.546L3.293 10.2a.625.625 0 0 0 .234 1.015l4.503 1.689-1.142 5.722a.625.625 0 0 0 1.07.547l8.75-9.375a.626.626 0 0 0 .15-.567Zm-8.314 7.487.818-4.093a.625.625 0 0 0-.391-.707l-4.128-1.551 6.61-7.083-.817 4.092a.625.625 0 0 0 .391.708l4.125 1.547-6.608 7.087Z"})]}),WithdrawIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 4.375V16.25L15 15l-2.5 1.25L10 15l-2.5 1.25L5 15l-2.5 1.25V4.375a.625.625 0 0 1 .625-.625h13.75a.625.625 0 0 1 .625.625Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M5.625 8.125A.625.625 0 0 1 6.25 7.5h7.5a.625.625 0 1 1 0 1.25h-7.5a.625.625 0 0 1-.625-.625Zm.625 3.125h7.5a.624.624 0 1 0 0-1.25h-7.5a.625.625 0 1 0 0 1.25Zm11.875-6.875V16.25a.625.625 0 0 1-.905.559L15 15.699l-2.22 1.11a.626.626 0 0 1-.56 0L10 15.699l-2.22 1.11a.626.626 0 0 1-.56 0L5 15.699l-2.22 1.11a.624.624 0 0 1-.905-.559V4.375a1.25 1.25 0 0 1 1.25-1.25h13.75a1.25 1.25 0 0 1 1.25 1.25Zm-1.25 0H3.125v10.864l1.595-.798a.626.626 0 0 1 .56 0l2.22 1.11 2.22-1.11a.626.626 0 0 1 .56 0l2.22 1.11 2.22-1.11a.626.626 0 0 1 .56 0l1.595.798V4.375Z"})]}),RefundsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsxs)("g",{fill:"currentColor",clipPath:"url(#a)",children:[(0,n.jsx)("path",{d:"M1.25 11.875h2.5v4.375h-2.5a.625.625 0 0 1-.625-.625V12.5a.625.625 0 0 1 .625-.625Zm14.688-7.5c-.325 0-.645.073-.938.212a2.188 2.188 0 1 0-1.25 2.077 2.188 2.188 0 1 0 2.188-2.289Z",opacity:.2}),(0,n.jsx)("path",{d:"M17.994 11.02a1.908 1.908 0 0 0-1.659-.33l-3.269.751a2.187 2.187 0 0 0-2.129-2.691h-3.91a2.486 2.486 0 0 0-1.768.732L3.49 11.25H1.25A1.25 1.25 0 0 0 0 12.5v3.125a1.25 1.25 0 0 0 1.25 1.25h8.125a.624.624 0 0 0 .152-.019l5-1.25a.543.543 0 0 0 .093-.031l3.036-1.292.035-.016a1.921 1.921 0 0 0 .307-3.247h-.004ZM1.25 12.5h1.875v3.125H1.25V12.5Zm15.893.641-2.969 1.264-4.877 1.22H4.375v-3.491l1.768-1.768A1.239 1.239 0 0 1 7.027 10h3.91a.938.938 0 0 1 0 1.875H8.75a.625.625 0 1 0 0 1.25h2.5a.651.651 0 0 0 .14-.016l5.234-1.204.024-.006a.673.673 0 0 1 .493 1.242h.002ZM12.813 7.5c.154 0 .308-.012.46-.037a2.812 2.812 0 1 0 2.205-3.672A2.812 2.812 0 1 0 12.813 7.5Zm4.687-.938a1.562 1.562 0 1 1-3.125 0 1.562 1.562 0 0 1 3.125 0Zm-4.688-3.437a1.562 1.562 0 0 1 1.504 1.141 2.813 2.813 0 0 0-1.171 1.948 1.563 1.563 0 1 1-.332-3.09Z"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"a",children:(0,n.jsx)("path",{fill:"#fff",d:"M0 0h20v20H0z"})})})]}),TaxesIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M7.484 4.39a2.187 2.187 0 1 1-3.093 3.094 2.187 2.187 0 0 1 3.093-3.093Zm8.125 8.126a2.188 2.188 0 1 0-3.093 3.094 2.188 2.188 0 0 0 3.093-3.094Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"m16.067 4.816-11.25 11.25a.625.625 0 1 1-.884-.885l11.25-11.25a.625.625 0 1 1 .884.884ZM3.948 7.925a2.813 2.813 0 1 1 3.978-3.977 2.813 2.813 0 0 1-3.978 3.977Zm.427-1.988A1.562 1.562 0 1 0 7.5 5.935a1.562 1.562 0 0 0-3.125.002Zm12.5 8.125a2.812 2.812 0 1 1-5.625 0 2.812 2.812 0 0 1 5.625 0Zm-1.25 0a1.562 1.562 0 1 0-3.124 0 1.562 1.562 0 0 0 3.124 0Z"})]}),DiaryIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16.199",height:"20.2",viewBox:"0 0 16.199 20.2",...a,children:(0,n.jsx)("path",{"data-name":"Path 3652",d:"M14.854.1H2.806A2.628 2.628 0 0 0 .1 2.617v14.966A2.628 2.628 0 0 0 2.806 20.1h12.056a1.289 1.289 0 0 0 .887-.331 1.121 1.121 0 0 0 .352-.81V1.264A1.21 1.21 0 0 0 14.854.1Zm-5.346.952h2.994v4.467l-1.063-1.583a.494.494 0 0 0-.175-.163.53.53 0 0 0-.7.163L9.508 5.512ZM1.132 2.617a1.629 1.629 0 0 1 1.674-1.565h5.683v4.857a.864.864 0 0 0 .634.857 1.012 1.012 0 0 0 1.091-.5l.792-1.19.792 1.19a1.029 1.029 0 0 0 1.1.5.864.864 0 0 0 .634-.852v-4.85h1.328a.21.21 0 0 1 .214.2v13.809a1.327 1.327 0 0 0-.214-.016H2.806a2.813 2.813 0 0 0-1.674.545Zm13.947 14.495-11.75.012a.477.477 0 1 0 0 .952l11.75-.012v.886a.2.2 0 0 1-.061.143.225.225 0 0 1-.153.057H2.806a1.569 1.569 0 1 1 0-3.131h12.056a.21.21 0 0 1 .214.2v.893Z",fill:"currentColor",stroke:"#fff",strokeWidth:".2"})}),FountainPenIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20.201",height:"20.2",viewBox:"0 0 20.201 20.2",...a,children:(0,n.jsx)("path",{"data-name":"001-pen-tool",d:"M19.929 8.063 12.138.271a.586.586 0 0 0-.829 0L8.195 3.385a.586.586 0 0 0 0 .829l.468.468-6.485 3.3a.586.586 0 0 0-.313.43L.108 19.421a.586.586 0 0 0 .579.679.592.592 0 0 0 .092-.007l11.007-1.757a.586.586 0 0 0 .43-.313l3.3-6.485.468.468a.586.586 0 0 0 .829 0l3.113-3.113a.586.586 0 0 0 0-.829Zm-5.281 2.6-3.341 6.558-8.928 1.429 6.672-6.672a.994.994 0 0 0 .117.007.954.954 0 1 0-.945-.837L1.55 17.821l1.426-8.927 6.558-3.342 1.229 1.229Zm-5.326.21Zm7.08-.114-4.479-4.476-1.861-1.861-.624-.622 2.285-2.285 6.963 6.963Zm0 0",fill:"currentColor",stroke:"#fff",strokeWidth:".2"})}),ManufacturersIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.875 10.625v6.25H3.125v-10l5 3.75v-3.75l5 3.75h3.75Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M9.063 13.75a.625.625 0 0 1-.626.625H6.25a.625.625 0 1 1 0-1.25h2.188a.625.625 0 0 1 .624.625Zm4.687-.625h-2.188a.624.624 0 1 0 0 1.25h2.188a.624.624 0 1 0 0-1.25Zm5.625 3.75a.624.624 0 0 1-.625.625H1.25a.625.625 0 1 1 0-1.25H2.5V6.875a.625.625 0 0 1 1-.5l4 3v-2.5a.625.625 0 0 1 1-.5l3.027 2.27.903-6.322a1.256 1.256 0 0 1 1.237-1.073h1.416a1.256 1.256 0 0 1 1.237 1.073l1.172 8.214s.006.06.006.088v5.625h1.25a.626.626 0 0 1 .627.625ZM12.667 9.5l.667.5h2.82l-1.071-7.5h-1.416l-1 7ZM3.75 16.25h12.5v-5h-3.125a.625.625 0 0 1-.375-.125l-1.125-.844L8.75 8.125v2.5a.625.625 0 0 1-1 .5l-4-3v8.125Z"})]}),CalendarScheduleIcon=a=>(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18.2",height:"20.2",viewBox:"0 0 18.2 20.2",...a,children:(0,n.jsxs)("g",{"data-name":"Create Order",children:[(0,n.jsx)("g",{"data-name":"Group 3007",children:(0,n.jsx)("g",{"data-name":"Group 3006",children:(0,n.jsx)("path",{"data-name":"Path 3653",d:"M15.175 11.687V5.273a.966.966 0 0 0-.137-.5L12.645.62a1.058 1.058 0 0 0-.914-.52H3.545a1.058 1.058 0 0 0-.913.522L.271 4.759a1.017 1.017 0 0 0-.171.565v10.427A1.458 1.458 0 0 0 1.564 17.2h8.03a4.367 4.367 0 1 0 5.58-5.512ZM8.245 1.272h3.41l1.74 3.02h-5.15Zm-4.626 0H7.06v3.02H1.898Zm5.736 14.754H1.564a.278.278 0 0 1-.279-.276V5.465h12.7v5.986a4.66 4.66 0 0 0-.265-.008 4.357 4.357 0 0 0-4.375 4.328c0 .086.007.171.012.256Zm4.367 2.9a3.156 3.156 0 1 1 3.19-3.156 3.177 3.177 0 0 1-3.187 3.157Z",fill:"currentColor",stroke:"#fff",strokeWidth:".2"})})}),(0,n.jsx)("g",{"data-name":"Group 3009",children:(0,n.jsx)("g",{"data-name":"Group 3008",children:(0,n.jsx)("path",{"data-name":"Path 3654",d:"M15.581 15.185h-1.264v-1.368a.593.593 0 0 0-1.185 0v1.954a.589.589 0 0 0 .593.586h1.857a.586.586 0 1 0 0-1.172Z",fill:"currentColor",stroke:"#fff",strokeWidth:".2"})})})]})}),ReviewIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M14.385 11.539a.632.632 0 0 0-.198.616l1.056 4.574a.624.624 0 0 1-.929.679l-3.992-2.422a.62.62 0 0 0-.644 0l-3.992 2.422a.626.626 0 0 1-.929-.68l1.056-4.573a.631.631 0 0 0-.198-.616L2.09 8.465a.625.625 0 0 1 .354-1.1l4.646-.401a.625.625 0 0 0 .52-.381l1.816-4.325a.625.625 0 0 1 1.146 0l1.815 4.325a.624.624 0 0 0 .521.381l4.646.402a.625.625 0 0 1 .354 1.099l-3.524 3.074Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.688 7.6a1.25 1.25 0 0 0-1.08-.859l-4.64-.4-1.818-4.325a1.246 1.246 0 0 0-2.3 0L7.037 6.341l-4.646.403a1.25 1.25 0 0 0-.711 2.192l3.524 3.08-1.056 4.573a1.25 1.25 0 0 0 1.862 1.355l3.985-2.422 3.993 2.422a1.25 1.25 0 0 0 1.862-1.355l-1.056-4.578 3.524-3.075a1.25 1.25 0 0 0 .37-1.335Zm-1.19.391-3.523 3.075a1.25 1.25 0 0 0-.397 1.228l1.059 4.58-3.99-2.421a1.242 1.242 0 0 0-1.292 0L5.37 16.875l1.052-4.578a1.25 1.25 0 0 0-.397-1.228L2.5 7.996v-.007l4.644-.402a1.25 1.25 0 0 0 1.043-.761L10 2.506l1.813 4.32a1.25 1.25 0 0 0 1.042.761l4.645.402v.005l-.002-.003Z"})]}),QuestionIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 10a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M10.938 14.063a.937.937 0 1 1-1.875 0 .937.937 0 0 1 1.874 0ZM10 5.624c-1.723 0-3.125 1.262-3.125 2.813v.312a.625.625 0 0 0 1.25 0v-.313c0-.859.841-1.562 1.875-1.562s1.875.703 1.875 1.563C11.875 9.296 11.034 10 10 10a.625.625 0 0 0-.625.625v.625a.625.625 0 1 0 1.25 0v-.056c1.425-.262 2.5-1.399 2.5-2.757 0-1.55-1.402-2.812-3.125-2.812ZM18.125 10A8.125 8.125 0 1 1 10 1.875 8.133 8.133 0 0 1 18.125 10Zm-1.25 0A6.875 6.875 0 1 0 10 16.875 6.883 6.883 0 0 0 16.875 10Z"})]}),ChatIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M18.106 16.901a.469.469 0 0 1-.58.58l-2.333-.666a5.628 5.628 0 0 1-8-3.074 5.625 5.625 0 0 0 5.614-7.482 5.625 5.625 0 0 1 4.631 8.31l.668 2.332Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M18.109 14.635a6.25 6.25 0 0 0-4.861-8.964A6.25 6.25 0 1 0 1.89 10.885l-.598 2.096a1.094 1.094 0 0 0 1.352 1.351l2.095-.598c.632.31 1.312.512 2.012.596a6.25 6.25 0 0 0 8.508 3.154l2.095.598a1.094 1.094 0 0 0 1.352-1.351l-.598-2.096ZM4.807 12.44a.64.64 0 0 0-.172.024l-2.063.59.59-2.064a.625.625 0 0 0-.053-.469 4.996 4.996 0 1 1 1.997 1.998.62.62 0 0 0-.299-.079Zm12.031 2.3.59 2.063-2.063-.59a.625.625 0 0 0-.469.054 5.005 5.005 0 0 1-6.744-1.925 6.245 6.245 0 0 0 5.496-7.333 5 5 0 0 1 3.243 7.26.624.624 0 0 0-.053.471Z"})]}),ChatOwnerIcon=a=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,n.jsx)("path",{opacity:.2,d:"M17.5 7.5v10L14.408 15H6.875a.625.625 0 01-.625-.625V11.25h6.875a.624.624 0 00.625-.625v-3.75h3.125a.625.625 0 01.625.625z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M16.875 6.25h-2.5v-2.5a1.25 1.25 0 00-1.25-1.25h-10a1.25 1.25 0 00-1.25 1.25v10a.625.625 0 001.016.486l2.734-2.205v2.344a1.25 1.25 0 001.25 1.25h7.312l2.922 2.36a.625.625 0 00.833-.043.624.624 0 00.183-.442v-10a1.25 1.25 0 00-1.25-1.25zM5.199 10.764l-2.074 1.677V3.75h10v6.875H5.592a.625.625 0 00-.393.14zm11.676 5.427l-2.074-1.677a.625.625 0 00-.39-.139H6.874v-2.5h6.25a1.25 1.25 0 001.25-1.25V7.5h2.5v8.691z",fill:"currentColor"})]}),MaintenanceIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 7.5a5 5 0 0 1-7.417 4.375l-4.38 5.078a1.878 1.878 0 1 1-2.656-2.656l5.078-4.38a5 5 0 0 1 6.25-7.054L11.25 6.25l.442 2.058 2.058.442 3.387-3.125c.24.596.364 1.233.363 1.875Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M17.716 5.39a.626.626 0 0 0-1.004-.224L13.564 8.07l-1.346-.289-.29-1.346 2.906-3.148a.625.625 0 0 0-.225-1.003A5.625 5.625 0 0 0 6.875 7.5a5.652 5.652 0 0 0 .469 2.261L2.64 13.828l-.034.03a2.5 2.5 0 1 0 3.567 3.504l4.066-4.706A5.625 5.625 0 0 0 18.125 7.5a5.592 5.592 0 0 0-.41-2.11ZM12.5 11.876a4.386 4.386 0 0 1-2.115-.547.626.626 0 0 0-.775.139l-4.367 5.057a1.25 1.25 0 0 1-1.767-1.767l5.054-4.366a.625.625 0 0 0 .138-.776 4.375 4.375 0 0 1 4.56-6.43l-2.438 2.64a.626.626 0 0 0-.152.556l.443 2.057a.625.625 0 0 0 .48.48l2.058.442a.625.625 0 0 0 .555-.152l2.64-2.438a4.38 4.38 0 0 1-4.314 5.105Z"})]}),StoreNoticeIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M17.5 15a1.875 1.875 0 0 1-1.875 1.875h-8.75A1.875 1.875 0 0 0 8.75 15c0-.781-.625-1.25-.625-1.25h8.75s.625.469.625 1.25Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M7.5 8.125a.625.625 0 0 1 .625-.625h5a.625.625 0 1 1 0 1.25h-5a.625.625 0 0 1-.625-.625Zm.625 3.125h5a.624.624 0 1 0 0-1.25h-5a.625.625 0 1 0 0 1.25Zm10 3.75a2.5 2.5 0 0 1-2.5 2.5h-8.75a2.5 2.5 0 0 1-2.5-2.5V5a1.25 1.25 0 0 0-2.5 0c0 .448.377.752.381.755a.625.625 0 0 1-.755.994C1.41 6.683.625 6.063.625 5a2.5 2.5 0 0 1 2.5-2.5H13.75a2.5 2.5 0 0 1 2.5 2.5v8.125h.625c.135 0 .267.044.375.125.094.067.875.687.875 1.75ZM7.52 13.553a.63.63 0 0 1 .605-.428H15V5a1.25 1.25 0 0 0-1.25-1.25H5.288c.221.38.338.81.337 1.25v10a1.25 1.25 0 0 0 2.5 0c0-.448-.377-.752-.381-.755a.611.611 0 0 1-.224-.692ZM16.875 15a.983.983 0 0 0-.252-.625H9.279c.063.202.095.413.094.625.001.439-.115.87-.335 1.25h6.587a1.25 1.25 0 0 0 1.25-1.25Z"})]}),StoreNoticeOwnerIcon=a=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,n.jsx)("path",{opacity:.2,d:"M17.5 10a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M11.25 13.75a.624.624 0 01-.625.625 1.25 1.25 0 01-1.25-1.25V10a.625.625 0 010-1.25 1.25 1.25 0 011.25 1.25v3.125a.624.624 0 01.625.625zM18.125 10A8.125 8.125 0 1110 1.875 8.133 8.133 0 0118.125 10zm-1.25 0A6.875 6.875 0 1010 16.875 6.883 6.883 0 0016.875 10zM9.687 7.5a.938.938 0 100-1.875.938.938 0 000 1.875z",fill:"currentColor"})]}),TermsIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M16.25 6.875h-4.375V2.5l4.375 4.375Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"m16.692 6.433-4.375-4.375a.625.625 0 0 0-.442-.183h-7.5a1.25 1.25 0 0 0-1.25 1.25v13.75a1.25 1.25 0 0 0 1.25 1.25h11.25a1.25 1.25 0 0 0 1.25-1.25v-10a.624.624 0 0 0-.183-.442ZM12.5 4.009l2.241 2.241H12.5V4.009Zm3.125 12.866H4.375V3.125h6.875v3.75a.625.625 0 0 0 .625.625h3.75v9.375Zm-2.5-6.25a.624.624 0 0 1-.625.625h-5a.625.625 0 1 1 0-1.25h5a.624.624 0 0 1 .625.625Zm0 2.5a.624.624 0 0 1-.625.625h-5a.625.625 0 1 1 0-1.25h5a.624.624 0 0 1 .625.625Z"})]}),LogOutIcon=a=>(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...a,children:[(0,n.jsx)("path",{fill:"currentColor",d:"m16.875 10-3.125 3.125v-6.25L16.875 10Z",opacity:.2}),(0,n.jsx)("path",{fill:"currentColor",d:"M8.75 16.875a.625.625 0 0 1-.625.625H3.75a1.25 1.25 0 0 1-1.25-1.25V3.75A1.25 1.25 0 0 1 3.75 2.5h4.375a.625.625 0 0 1 0 1.25H3.75v12.5h4.375a.625.625 0 0 1 .625.625Zm8.567-6.433-3.125 3.125a.625.625 0 0 1-1.067-.442v-2.5h-5a.625.625 0 1 1 0-1.25h5v-2.5a.625.625 0 0 1 1.067-.442l3.125 3.125a.626.626 0 0 1 0 .884ZM15.99 10l-1.614-1.616v3.232L15.989 10Z"})]})},95535:function(a,e,t){t.d(e,{Z:function(){return search_bar}});var n=t(85893),r=t(80287),l=t(94885),o=t(79362),s=t(8152),i=t(30824),c=t(99930),u=t(99494),h=t(16203),d=t(41609),v=t.n(d);function formatOwnerLinks(a,e){if(v()(e))return[];let t=new Map;for(let n of a){let a=n.href(e.toString());t.has(a)||t.set(a,{href:a,label:n.label,icon:n.icon,permissions:n.permissions})}return Array.from(t.values())}function extractHrefObjects(a){let e=[];!function processMenu(a){for(let t of a)t.childMenu?processMenu(t.childMenu):t.href&&e.push(t)}(a);let t=Array.from(new Set(e));return t}var f=t(93967),m=t.n(f),p=t(48583),x=t(5233),g=t(11163),w=t(67294),search_bar=a=>{let{}=a,{t:e}=(0,x.$G)(),t=[],[d,f]=(0,w.useState)(""),[Z,M]=(0,w.useState)(t),[j]=(0,p.KO)(o.Hd),{query:{shop:I},locale:C}=(0,g.useRouter)(),{permissions:S}=(0,h.WA)(),{data:L}=(0,c.UE)(),getAuthorizedURL=a=>[...a].filter(a=>(0,h.Ft)(null==a?void 0:a.permissions,S)),handleSearch=a=>{if(f(a),!a||a.length<1){M([]);return}let{sidebarLinks:{admin:e,shop:t,ownerDashboard:n}}=u.siteSettings,r=extractHrefObjects(Object.values(e)),l=extractHrefObjects(Object.values(t)),s=extractHrefObjects(Object.values(n)),i=[],c=[];if((0,h.Ft)([o.G9],S)){var d;I=null==L?void 0:null===(d=L.managed_shop)||void 0===d?void 0:d.slug}switch(!0){case!v()(I):a="".concat(I,"/").concat(a),i=getAuthorizedURL(c=formatOwnerLinks(l,I));break;case v()(I)&&(0,h.Ft)(h.M$,S):i=r;break;case v()(I)&&(0,h.Ft)(h.Ho,S):c=[...s],null==L||L.shops.map(a=>c.push(...formatOwnerLinks(l,a.slug))),i=getAuthorizedURL(c);break;default:i=getAuthorizedURL(r)}let m=function(a,e){try{if(!e)return[];let t=e.toLowerCase(),n=new Set,r=[],processLink=a=>{let e=a.href&&a.href.includes(t)||a.label.includes(t),l=e&&!n.has(a.href);if(l){let e={...a,customLabel:function(a){let[,e,...t]=[...a.split("/")],n=e.replace(/\b\w/g,a=>a.toUpperCase()),r=[...t].join(" - ");return"".concat(n," - ").concat(r).replace(/\s*-\s*$/g,"")}(a.href)};r.push(e),n.add(a.href)}a.childMenu&&a.childMenu.forEach(processLink)};return a.forEach(processLink),r}catch(a){return[]}}(i,a);M([...m])};return(0,w.useEffect)(()=>{""===d?M(t):handleSearch(d)},[d]),(0,n.jsxs)(w.Fragment,{children:[(0,n.jsx)("div",{className:m()("fixed inset-0",""===d&&"hidden"),onClick:()=>f("")}),(0,n.jsxs)("div",{className:"relative w-full max-w-lg rounded-3xl",children:[(0,n.jsx)(r.W,{className:"absolute inset-y-0 left-4 my-auto h-4 w-4"}),(0,n.jsx)("input",{type:"text",className:"block w-full rounded-3xl border border-solid border-border-200 bg-gray-50 py-2 text-sm text-heading transition-[border] placeholder:text-gray-400 focus:border-accent focus:bg-white focus:outline-none focus:ring-0 ltr:pl-12 rtl:pr-12 sm:text-sm sm:leading-6",placeholder:e("text-top-bar-search-placeholder"),value:d,onChange:a=>{var e;return handleSearch(null==a?void 0:null===(e=a.target)||void 0===e?void 0:e.value)}}),!v()(Z)&&(0,n.jsx)("button",{className:"absolute top-1/2 h-auto w-auto -translate-y-1/2 px-0 text-sm font-medium text-gray-500 hover:text-accent-hover ltr:right-4 rtl:left-4",onClick:a=>{a.preventDefault(),f("")},children:e("text-clear")})]}),v()(Z)?null:(0,n.jsx)("div",{className:"sidebar-scrollbar absolute top-12 z-30 h-[418px] max-h-[418px] w-full max-w-lg rounded-xl border border-solid border-gray-200 bg-white py-4 shadow-box lg:top-[74px]",children:(0,n.jsx)(i.Z,{className:"max-h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("h4",{className:"px-6 pb-2 text-sm font-medium text-black xl:text-base",children:e("text-quick-page-links")}),(0,n.jsx)("div",{className:"mx-3",children:null==Z?void 0:Z.map(a=>(0,n.jsxs)(s.Z,{href:a.href,onClick:()=>{M([]),f("")},className:"group flex items-center rounded-lg py-2.5 px-3 text-sm text-gray-700 transition duration-200 ease-in-out hover:bg-gray-100 hover:text-heading",children:[(0,n.jsx)("span",{className:"inline-flex shrink-0 items-center justify-center rounded-md border border-gray-200 p-2 text-gray-500 group-hover:border-gray-300",children:(0,n.jsx)(l.TermsIcon,{className:"h-5 w-5"})}),(0,n.jsxs)("div",{className:"flex flex-col ltr:pl-3 rtl:pr-3",children:[(0,n.jsx)("span",{className:"whitespace-nowrap font-medium capitalize",children:v()(I)?e(a.customLabel):e(a.label)}),(0,n.jsx)("span",{className:"text-gray-500",children:null==a?void 0:a.href})]})]},a.href))})]})})})]})}},30824:function(a,e,t){var n=t(85893),r=t(93967),l=t.n(r),o=t(42189);t(85251),e.Z=a=>{let{options:e,children:t,style:r,className:s,...i}=a;return(0,n.jsx)(o.E,{options:{scrollbars:{autoHide:"scroll"},...e},className:l()("os-theme-thin-dark",s),style:r,...i,children:t})}},44498:function(a,e,t){t.d(e,{B:function(){return l}});var n=t(47869),r=t(3737);let l={me:()=>r.eN.get(n.P.ME),login:a=>r.eN.post(n.P.TOKEN,a),logout:()=>r.eN.post(n.P.LOGOUT,{}),register:a=>r.eN.post(n.P.REGISTER,a),update:a=>{let{id:e,input:t}=a;return r.eN.put("".concat(n.P.USERS,"/").concat(e),t)},changePassword:a=>r.eN.post(n.P.CHANGE_PASSWORD,a),forgetPassword:a=>r.eN.post(n.P.FORGET_PASSWORD,a),verifyForgetPasswordToken:a=>r.eN.post(n.P.VERIFY_FORGET_PASSWORD_TOKEN,a),resetPassword:a=>r.eN.post(n.P.RESET_PASSWORD,a),makeAdmin:a=>r.eN.post(n.P.MAKE_ADMIN,a),block:a=>r.eN.post(n.P.BLOCK_USER,a),unblock:a=>r.eN.post(n.P.UNBLOCK_USER,a),addWalletPoints:a=>r.eN.post(n.P.ADD_WALLET_POINTS,a),addLicenseKey:a=>r.eN.post(n.P.ADD_LICENSE_KEY_VERIFY,a),fetchUsers:a=>{let{name:e,...t}=a;return r.eN.get(n.P.USERS,{searchJoin:"and",with:"wallet",...t,search:r.eN.formatSearchParams({name:e})})},fetchAdmins:a=>{let{...e}=a;return r.eN.get(n.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...e})},fetchUser:a=>{let{id:e}=a;return r.eN.get("".concat(n.P.USERS,"/").concat(e))},resendVerificationEmail:()=>r.eN.post(n.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:a=>{let{email:e}=a;return r.eN.post(n.P.UPDATE_EMAIL,{email:e})},fetchVendors:a=>{let{is_active:e,...t}=a;return r.eN.get(n.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:e,...t})},fetchCustomers:a=>{let{...e}=a;return r.eN.get(n.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...e})},getMyStaffs:a=>{let{is_active:e,shop_id:t,name:l,...o}=a;return r.eN.get(n.P.MY_STAFFS,{searchJoin:"and",shop_id:t,...o,search:r.eN.formatSearchParams({name:l,is_active:e})})},getAllStaffs:a=>{let{is_active:e,name:t,...l}=a;return r.eN.get(n.P.ALL_STAFFS,{searchJoin:"and",...l,search:r.eN.formatSearchParams({name:t,is_active:e})})}}},99930:function(a,e,t){t.d(e,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var n=t(79362),r=t(97514),l=t(31955),o=t(5233),s=t(11163),i=t(88767),c=t(22920),u=t(47869),h=t(44498),d=t(28597),v=t(87066),f=t(16203);let useMeQuery=()=>{let a=(0,i.useQueryClient)(),e=(0,s.useRouter)();return(0,i.useQuery)([u.P.ME],h.B.me,{retry:!1,onSuccess:()=>{e.pathname===r.Z.verifyLicense&&e.replace(r.Z.dashboard),e.pathname===r.Z.verifyEmail&&((0,f.Fu)(!0),e.replace(r.Z.dashboard))},onError:t=>{if(v.Z.isAxiosError(t)){var n,l;if((null===(n=t.response)||void 0===n?void 0:n.status)===417){e.replace(r.Z.verifyLicense);return}if((null===(l=t.response)||void 0===l?void 0:l.status)===409){(0,f.Fu)(!1),e.replace(r.Z.verifyEmail);return}a.clear(),e.replace(r.Z.login)}}})};function useLogin(){return(0,i.useMutation)(h.B.login)}let useLogoutMutation=()=>{let a=(0,s.useRouter)(),{t:e}=(0,o.$G)();return(0,i.useMutation)(h.B.logout,{onSuccess:()=>{l.Z.remove(n.E$),a.replace(r.Z.login),c.Am.success(e("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let a=(0,i.useQueryClient)(),{t:e}=(0,o.$G)();return(0,i.useMutation)(h.B.register,{onSuccess:()=>{c.Am.success(e("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{a.invalidateQueries(u.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:a}=(0,o.$G)(),e=(0,i.useQueryClient)();return(0,i.useMutation)(h.B.update,{onSuccess:()=>{c.Am.success(a("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(u.P.ME),e.invalidateQueries(u.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:a}=(0,o.$G)(),e=(0,i.useQueryClient)();return(0,i.useMutation)(h.B.updateEmail,{onSuccess:()=>{c.Am.success(a("common:successfully-updated"))},onError:a=>{let{response:{data:e}}=null!=a?a:{};c.Am.error(null==e?void 0:e.message)},onSettled:()=>{e.invalidateQueries(u.P.ME),e.invalidateQueries(u.P.USERS)}})},useChangePasswordMutation=()=>(0,i.useMutation)(h.B.changePassword),useForgetPasswordMutation=()=>(0,i.useMutation)(h.B.forgetPassword),useResendVerificationEmail=()=>{let{t:a}=(0,o.$G)("common");return(0,i.useMutation)(h.B.resendVerificationEmail,{onSuccess:()=>{c.Am.success(a("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,c.Am)(a("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:a}=(0,o.$G)();(0,i.useQueryClient)();let e=(0,s.useRouter)();return(0,i.useMutation)(h.B.addLicenseKey,{onSuccess:()=>{c.Am.success(a("common:successfully-updated")),setTimeout(()=>{e.reload()},1e3)},onError:()=>{c.Am.error(a("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,i.useMutation)(h.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,i.useMutation)(h.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let a=(0,i.useQueryClient)(),{t:e}=(0,o.$G)();return(0,i.useMutation)(h.B.makeAdmin,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{a.invalidateQueries(u.P.USERS)}})},useBlockUserMutation=()=>{let a=(0,i.useQueryClient)(),{t:e}=(0,o.$G)();return(0,i.useMutation)(h.B.block,{onSuccess:()=>{c.Am.success(e("common:successfully-block"))},onSettled:()=>{a.invalidateQueries(u.P.USERS),a.invalidateQueries(u.P.STAFFS),a.invalidateQueries(u.P.ADMIN_LIST),a.invalidateQueries(u.P.CUSTOMERS),a.invalidateQueries(u.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let a=(0,i.useQueryClient)(),{t:e}=(0,o.$G)();return(0,i.useMutation)(h.B.unblock,{onSuccess:()=>{c.Am.success(e("common:successfully-unblock"))},onSettled:()=>{a.invalidateQueries(u.P.USERS),a.invalidateQueries(u.P.STAFFS),a.invalidateQueries(u.P.ADMIN_LIST),a.invalidateQueries(u.P.CUSTOMERS),a.invalidateQueries(u.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:a}=(0,o.$G)(),e=(0,i.useQueryClient)();return(0,i.useMutation)(h.B.addWalletPoints,{onSuccess:()=>{c.Am.success(a("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(u.P.USERS)}})},useUserQuery=a=>{let{id:e}=a;return(0,i.useQuery)([u.P.USERS,e],()=>h.B.fetchUser({id:e}),{enabled:!!e})},useUsersQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.USERS,a],()=>h.B.fetchUsers(a),{keepPreviousData:!0});return{users:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}},useAdminsQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.ADMIN_LIST,a],()=>h.B.fetchAdmins(a),{keepPreviousData:!0});return{admins:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}},useVendorsQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.VENDORS_LIST,a],()=>h.B.fetchVendors(a),{keepPreviousData:!0});return{vendors:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}},useCustomersQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.CUSTOMERS,a],()=>h.B.fetchCustomers(a),{keepPreviousData:!0});return{customers:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}},useMyStaffsQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.MY_STAFFS,a],()=>h.B.getMyStaffs(a),{keepPreviousData:!0});return{myStaffs:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}},useAllStaffsQuery=a=>{var e;let{data:t,isLoading:n,error:r}=(0,i.useQuery)([u.P.ALL_STAFFS,a],()=>h.B.getAllStaffs(a),{keepPreviousData:!0});return{allStaffs:null!==(e=null==t?void 0:t.data)&&void 0!==e?e:[],paginatorInfo:(0,d.Q)(t),loading:n,error:r}}}}]);