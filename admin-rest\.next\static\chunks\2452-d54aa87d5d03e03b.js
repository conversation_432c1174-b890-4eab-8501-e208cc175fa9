"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2452],{33656:function(e,t,n){n.d(t,{Z:function(){return TypeFilter}});var r=n(85893),o=n(79828);n(67294);var l=n(5233),a=n(93967),u=n.n(a),s=n(16720),i=n(11163);function TypeFilter(e){let{onTypeFilter:t,className:n}=e,{t:a}=(0,l.$G)(),{locale:c}=(0,i.useRouter)(),{types:p,loading:d}=(0,s.qs)({language:c});return(0,r.jsx)("div",{className:u()("flex w-full",n),children:(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(o.Z,{options:p,isLoading:d,getOptionLabel:e=>e.name,getOptionValue:e=>e.slug,placeholder:a("common:filter-by-group-placeholder"),onChange:t,isClearable:!0})})})}},35484:function(e,t,n){var r=n(85893),o=n(93967),l=n.n(o),a=n(98388);t.Z=e=>{let{title:t,className:n,...o}=e;return(0,r.jsx)("h2",{className:(0,a.m6)(l()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...o,children:t})}},37912:function(e,t,n){var r=n(85893),o=n(5114),l=n(80287),a=n(93967),u=n.n(a),s=n(67294),i=n(87536),c=n(5233),p=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:d=!1,inputClassName:f,placeholderText:v,...m}=e,{register:b,handleSubmit:P,watch:h,reset:g,formState:{errors:y}}=(0,i.cI)({defaultValues:{searchText:""}}),E=h("searchText"),{t:S}=(0,c.$G)();(0,s.useEffect)(()=>{E||n({searchText:""})},[E]);let x=u()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":d},f);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,p.m6)(u()("relative flex w-full items-center",t)),onSubmit:P(n),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(l.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,p.m6)(x),placeholder:null!=v?v:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...m}),y.searchText&&(0,r.jsx)("p",{children:y.searchText.message}),!!E&&(0,r.jsx)("button",{type:"button",onClick:function(){g(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(o.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var r=n(85893),o=n(55891),l=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,r.jsx)(o.Z,{nextIcon:(0,r.jsx)(l.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},28368:function(e,t,n){n.d(t,{p:function(){return N}});var r,o,l,a=n(67294),u=n(32984),s=n(12351),i=n(23784),c=n(19946),p=n(61363),d=n(64103),f=n(16567),v=n(14157),m=n(15466),b=n(73781);let P=null!=(l=a.startTransition)?l:function(e){e()};var h=((r=h||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),g=((o=g||{})[o.ToggleDisclosure=0]="ToggleDisclosure",o[o.CloseDisclosure=1]="CloseDisclosure",o[o.SetButtonId=2]="SetButtonId",o[o.SetPanelId=3]="SetPanelId",o[o.LinkPanel=4]="LinkPanel",o[o.UnlinkPanel=5]="UnlinkPanel",o);let y={0:e=>({...e,disclosureState:(0,u.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},E=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(E);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}E.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let x=(0,a.createContext)(null);function Y(e,t){return(0,u.E)(t.type,y,e,t)}x.displayName="DisclosurePanelContext";let T=a.Fragment,I=s.AN.RenderStrategy|s.AN.Static,N=Object.assign((0,s.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,o=(0,a.useRef)(null),l=(0,i.T)(t,(0,i.h)(e=>{o.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),p=(0,a.useRef)(null),d=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:p,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:v,buttonId:P},h]=d,g=(0,b.z)(e=>{h({type:1});let t=(0,m.r)(o);if(!t||!P)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(P):t.getElementById(P);null==n||n.focus()}),y=(0,a.useMemo)(()=>({close:g}),[g]),x=(0,a.useMemo)(()=>({open:0===v,close:g}),[v,g]);return a.createElement(E.Provider,{value:d},a.createElement(S.Provider,{value:y},a.createElement(f.up,{value:(0,u.E)(v,{0:f.ZM.Open,1:f.ZM.Closed})},(0,s.sY)({ourProps:{ref:l},theirProps:r,slot:x,defaultTag:T,name:"Disclosure"}))))}),{Button:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-button-${n}`,...o}=e,[l,u]=M("Disclosure.Button"),f=(0,a.useContext)(x),m=null!==f&&f===l.panelId,P=(0,a.useRef)(null),h=(0,i.T)(P,t,m?null:l.buttonRef);(0,a.useEffect)(()=>{if(!m)return u({type:2,buttonId:r}),()=>{u({type:2,buttonId:null})}},[r,u,m]);let g=(0,b.z)(e=>{var t;if(m){if(1===l.disclosureState)return;switch(e.key){case p.R.Space:case p.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0}),null==(t=l.buttonRef.current)||t.focus()}}else switch(e.key){case p.R.Space:case p.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0})}}),y=(0,b.z)(e=>{e.key===p.R.Space&&e.preventDefault()}),E=(0,b.z)(t=>{var n;(0,d.P)(t.currentTarget)||e.disabled||(m?(u({type:0}),null==(n=l.buttonRef.current)||n.focus()):u({type:0}))}),S=(0,a.useMemo)(()=>({open:0===l.disclosureState}),[l]),T=(0,v.f)(e,P),I=m?{ref:h,type:T,onKeyDown:g,onClick:E}:{ref:h,id:r,type:T,"aria-expanded":0===l.disclosureState,"aria-controls":l.linkedPanel?l.panelId:void 0,onKeyDown:g,onKeyUp:y,onClick:E};return(0,s.sY)({ourProps:I,theirProps:o,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-panel-${n}`,...o}=e,[l,u]=M("Disclosure.Panel"),{close:p}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),d=(0,i.T)(t,l.panelRef,e=>{P(()=>u({type:e?4:5}))});(0,a.useEffect)(()=>(u({type:3,panelId:r}),()=>{u({type:3,panelId:null})}),[r,u]);let v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===l.disclosureState,b=(0,a.useMemo)(()=>({open:0===l.disclosureState,close:p}),[l,p]);return a.createElement(x.Provider,{value:l.panelId},(0,s.sY)({ourProps:{ref:d,id:r},theirProps:o,slot:b,defaultTag:"div",features:I,visible:m,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){n.d(t,{J:function(){return F}});var r,o,l=n(67294),a=n(32984),u=n(12351),s=n(23784),i=n(19946),c=n(61363),p=n(64103),d=n(84575),f=n(16567),v=n(14157),m=n(39650),b=n(15466),P=n(51074),h=n(14007),g=n(46045),y=n(73781),E=n(45662),S=n(3855),x=n(16723),T=n(65958),I=n(2740),N=((r=N||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),C=((o=C||{})[o.TogglePopover=0]="TogglePopover",o[o.ClosePopover=1]="ClosePopover",o[o.SetButton=2]="SetButton",o[o.SetButtonId=3]="SetButtonId",o[o.SetPanel=4]="SetPanel",o[o.SetPanelId=5]="SetPanelId",o);let k={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},O=(0,l.createContext)(null);function oe(e){let t=(0,l.useContext)(O);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}O.displayName="PopoverContext";let R=(0,l.createContext)(null);function fe(e){let t=(0,l.useContext)(R);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}R.displayName="PopoverAPIContext";let D=(0,l.createContext)(null);function Ee(){return(0,l.useContext)(D)}D.displayName="PopoverGroupContext";let j=(0,l.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,k,e,t)}j.displayName="PopoverPanelContext";let B=u.AN.RenderStrategy|u.AN.Static,A=u.AN.RenderStrategy|u.AN.Static,F=Object.assign((0,u.yV)(function(e,t){var n;let{__demoMode:r=!1,...o}=e,i=(0,l.useRef)(null),c=(0,s.T)(t,(0,s.h)(e=>{i.current=e})),p=(0,l.useRef)([]),v=(0,l.useReducer)(Ne,{__demoMode:r,popoverState:r?0:1,buttons:p,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,l.createRef)(),afterPanelSentinel:(0,l.createRef)()}),[{popoverState:b,button:g,buttonId:E,panel:x,panelId:N,beforePanelSentinel:C,afterPanelSentinel:k},D]=v,B=(0,P.i)(null!=(n=i.current)?n:g),A=(0,l.useMemo)(()=>{if(!g||!x)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(g))^Number(null==e?void 0:e.contains(x)))return!0;let e=(0,d.GO)(),t=e.indexOf(g),n=(t+e.length-1)%e.length,r=(t+1)%e.length,o=e[n],l=e[r];return!x.contains(o)&&!x.contains(l)},[g,x]),F=(0,S.E)(E),z=(0,S.E)(N),L=(0,l.useMemo)(()=>({buttonId:F,panelId:z,close:()=>D({type:1})}),[F,z,D]),_=Ee(),Z=null==_?void 0:_.registerPopover,$=(0,y.z)(()=>{var e;return null!=(e=null==_?void 0:_.isFocusWithinPopoverGroup())?e:(null==B?void 0:B.activeElement)&&((null==g?void 0:g.contains(B.activeElement))||(null==x?void 0:x.contains(B.activeElement)))});(0,l.useEffect)(()=>null==Z?void 0:Z(L),[Z,L]);let[G,V]=(0,I.k)(),H=(0,T.v)({mainTreeNodeRef:null==_?void 0:_.mainTreeNodeRef,portals:G,defaultContainers:[g,x]});(0,h.O)(null==B?void 0:B.defaultView,"focus",e=>{var t,n,r,o;e.target!==window&&e.target instanceof HTMLElement&&0===b&&($()||g&&x&&(H.contains(e.target)||null!=(n=null==(t=C.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(o=null==(r=k.current)?void 0:r.contains)&&o.call(r,e.target)||D({type:1})))},!0),(0,m.O)(H.resolveContainers,(e,t)=>{D({type:1}),(0,d.sP)(t,d.tJ.Loose)||(e.preventDefault(),null==g||g.focus())},0===b);let K=(0,y.z)(e=>{D({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:g:g;null==t||t.focus()}),J=(0,l.useMemo)(()=>({close:K,isPortalled:A}),[K,A]),U=(0,l.useMemo)(()=>({open:0===b,close:K}),[b,K]);return l.createElement(j.Provider,{value:null},l.createElement(O.Provider,{value:v},l.createElement(R.Provider,{value:J},l.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},l.createElement(V,null,(0,u.sY)({ourProps:{ref:c},theirProps:o,slot:U,defaultTag:"div",name:"Popover"}),l.createElement(H.MainTreeNode,null))))))}),{Button:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-button-${n}`,...o}=e,[f,m]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),h=(0,l.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,x=Ee(),T=null==x?void 0:x.closeOthers,I=null!==(0,l.useContext)(j);(0,l.useEffect)(()=>{if(!I)return m({type:3,buttonId:r}),()=>{m({type:3,buttonId:null})}},[I,r,m]);let[N]=(0,l.useState)(()=>Symbol()),C=(0,s.T)(h,t,I?null:e=>{if(e)f.buttons.current.push(N);else{let e=f.buttons.current.indexOf(N);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&m({type:2,button:e})}),k=(0,s.T)(h,t),O=(0,P.i)(h),R=(0,y.z)(e=>{var t,n,r;if(I){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),m({type:1}),null==(r=f.button)||r.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==T||T(f.buttonId)),m({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==T?void 0:T(f.buttonId);if(!h.current||null!=O&&O.activeElement&&!h.current.contains(O.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1})}}),D=(0,y.z)(e=>{I||e.key===c.R.Space&&e.preventDefault()}),B=(0,y.z)(t=>{var n,r;(0,p.P)(t.currentTarget)||e.disabled||(I?(m({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==T||T(f.buttonId)),m({type:0}),null==(r=f.button)||r.focus()))}),A=(0,y.z)(e=>{e.preventDefault(),e.stopPropagation()}),F=0===f.popoverState,z=(0,l.useMemo)(()=>({open:F}),[F]),L=(0,v.f)(e,h),_=I?{ref:k,type:L,onKeyDown:R,onClick:B}:{ref:C,id:f.buttonId,type:L,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:R,onKeyUp:D,onClick:B,onMouseDown:A},Z=(0,E.l)(),$=(0,y.z)(()=>{let e=f.panel;e&&(0,a.E)(Z.current,{[E.N.Forwards]:()=>(0,d.jA)(e,d.TO.First),[E.N.Backwards]:()=>(0,d.jA)(e,d.TO.Last)})===d.fE.Error&&(0,d.jA)((0,d.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(Z.current,{[E.N.Forwards]:d.TO.Next,[E.N.Backwards]:d.TO.Previous}),{relativeTo:f.button})});return l.createElement(l.Fragment,null,(0,u.sY)({ourProps:_,theirProps:o,slot:z,defaultTag:"button",name:"Popover.Button"}),F&&!I&&b&&l.createElement(g._,{id:S,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:$}))}),Overlay:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-overlay-${n}`,...o}=e,[{popoverState:a},c]=oe("Popover.Overlay"),d=(0,s.T)(t),v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===a,b=(0,y.z)(e=>{if((0,p.P)(e.currentTarget))return e.preventDefault();c({type:1})}),P=(0,l.useMemo)(()=>({open:0===a}),[a]);return(0,u.sY)({ourProps:{ref:d,id:r,"aria-hidden":!0,onClick:b},theirProps:o,slot:P,defaultTag:"div",features:B,visible:m,name:"Popover.Overlay"})}),Panel:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-panel-${n}`,focus:o=!1,...p}=e,[v,m]=oe("Popover.Panel"),{close:b,isPortalled:h}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,T=`headlessui-focus-sentinel-after-${(0,i.M)()}`,I=(0,l.useRef)(null),N=(0,s.T)(I,t,e=>{m({type:4,panel:e})}),C=(0,P.i)(I);(0,x.e)(()=>(m({type:5,panelId:r}),()=>{m({type:5,panelId:null})}),[r,m]);let k=(0,f.oJ)(),O=null!==k?(k&f.ZM.Open)===f.ZM.Open:0===v.popoverState,R=(0,y.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==v.popoverState||!I.current||null!=C&&C.activeElement&&!I.current.contains(C.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1}),null==(t=v.button)||t.focus()}});(0,l.useEffect)(()=>{var t;e.static||1===v.popoverState&&(null==(t=e.unmount)||t)&&m({type:4,panel:null})},[v.popoverState,e.unmount,e.static,m]),(0,l.useEffect)(()=>{if(v.__demoMode||!o||0!==v.popoverState||!I.current)return;let e=null==C?void 0:C.activeElement;I.current.contains(e)||(0,d.jA)(I.current,d.TO.First)},[v.__demoMode,o,I,v.popoverState]);let D=(0,l.useMemo)(()=>({open:0===v.popoverState,close:b}),[v,b]),B={ref:N,id:r,onKeyDown:R,onBlur:o&&0===v.popoverState?e=>{var t,n,r,o,l;let a=e.relatedTarget;a&&I.current&&(null!=(t=I.current)&&t.contains(a)||(m({type:1}),(null!=(r=null==(n=v.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,a)||null!=(l=null==(o=v.afterPanelSentinel.current)?void 0:o.contains)&&l.call(o,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},F=(0,E.l)(),z=(0,y.z)(()=>{let e=I.current;e&&(0,a.E)(F.current,{[E.N.Forwards]:()=>{var t;(0,d.jA)(e,d.TO.First)===d.fE.Error&&(null==(t=v.afterPanelSentinel.current)||t.focus())},[E.N.Backwards]:()=>{var e;null==(e=v.button)||e.focus({preventScroll:!0})}})}),L=(0,y.z)(()=>{let e=I.current;e&&(0,a.E)(F.current,{[E.N.Forwards]:()=>{var e;if(!v.button)return;let t=(0,d.GO)(),n=t.indexOf(v.button),r=t.slice(0,n+1),o=[...t.slice(n+1),...r];for(let t of o.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=v.panel)&&e.contains(t)){let e=o.indexOf(t);-1!==e&&o.splice(e,1)}(0,d.jA)(o,d.TO.First,{sorted:!1})},[E.N.Backwards]:()=>{var t;(0,d.jA)(e,d.TO.Previous)===d.fE.Error&&(null==(t=v.button)||t.focus())}})});return l.createElement(j.Provider,{value:r},O&&h&&l.createElement(g._,{id:S,ref:v.beforePanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:z}),(0,u.sY)({ourProps:B,theirProps:p,slot:D,defaultTag:"div",features:A,visible:O,name:"Popover.Panel"}),O&&h&&l.createElement(g._,{id:T,ref:v.afterPanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:L}))}),Group:(0,u.yV)(function(e,t){let n=(0,l.useRef)(null),r=(0,s.T)(n,t),[o,a]=(0,l.useState)([]),i=(0,T.H)(),c=(0,y.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),p=(0,y.z)(e=>(a(t=>[...t,e]),()=>c(e))),d=(0,y.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||o.some(e=>{var n,o;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(o=t.getElementById(e.panelId.current))?void 0:o.contains(r))})}),f=(0,y.z)(e=>{for(let t of o)t.buttonId.current!==e&&t.close()}),v=(0,l.useMemo)(()=>({registerPopover:p,unregisterPopover:c,isFocusWithinPopoverGroup:d,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[p,c,d,f,i.mainTreeNodeRef]),m=(0,l.useMemo)(()=>({}),[]);return l.createElement(D.Provider,{value:v},(0,u.sY)({ourProps:{ref:r},theirProps:e,slot:m,defaultTag:"div",name:"Popover.Group"}),l.createElement(i.MainTreeNode,null))})})}}]);