"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_manufacturer_manufacturer-delete-view_tsx";
exports.ids = ["src_components_manufacturer_manufacturer-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/manufacturer/manufacturer-delete-view.tsx":
/*!******************************************************************!*\
  !*** ./src/components/manufacturer/manufacturer-delete-view.tsx ***!
  \******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/manufacturer */ \"./src/data/manufacturer.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_manufacturer__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ManufacturerDeleteView = ()=>{\n    const { mutate: deleteManufacturerMutation, isLoading: loading } = (0,_data_manufacturer__WEBPACK_IMPORTED_MODULE_3__.useDeleteManufacturerMutation)();\n    const { data: modalData } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteManufacturerMutation({\n            id: modalData\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\manufacturer\\\\manufacturer-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ManufacturerDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9tYW51ZmFjdHVyZXIvbWFudWZhY3R1cmVyLWRlbGV0ZS12aWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXFFO0FBSXhCO0FBQ3VCO0FBRXBFLE1BQU1JLHlCQUF5QjtJQUM3QixNQUFNLEVBQUVDLFFBQVFDLDBCQUEwQixFQUFFQyxXQUFXQyxPQUFPLEVBQUUsR0FDOURMLGlGQUE2QkE7SUFFL0IsTUFBTSxFQUFFTSxNQUFNQyxTQUFTLEVBQUUsR0FBR1IsaUZBQWFBO0lBQ3pDLE1BQU0sRUFBRVMsVUFBVSxFQUFFLEdBQUdWLGtGQUFjQTtJQUVyQyxTQUFTVztRQUNQTiwyQkFBMkI7WUFDekJPLElBQUlIO1FBQ047UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDWCw0RUFBZ0JBO1FBQ2ZjLFVBQVVIO1FBQ1ZJLFVBQVVIO1FBQ1ZJLGtCQUFrQlI7Ozs7OztBQUd4QjtBQUVBLGlFQUFlSixzQkFBc0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvY29tcG9uZW50cy9tYW51ZmFjdHVyZXIvbWFudWZhY3R1cmVyLWRlbGV0ZS12aWV3LnRzeD9mNmEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb25maXJtYXRpb25DYXJkIGZyb20gJ0AvY29tcG9uZW50cy9jb21tb24vY29uZmlybWF0aW9uLWNhcmQnO1xyXG5pbXBvcnQge1xyXG4gIHVzZU1vZGFsQWN0aW9uLFxyXG4gIHVzZU1vZGFsU3RhdGUsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL21vZGFsL21vZGFsLmNvbnRleHQnO1xyXG5pbXBvcnQgeyB1c2VEZWxldGVNYW51ZmFjdHVyZXJNdXRhdGlvbiB9IGZyb20gJ0AvZGF0YS9tYW51ZmFjdHVyZXInO1xyXG5cclxuY29uc3QgTWFudWZhY3R1cmVyRGVsZXRlVmlldyA9ICgpID0+IHtcclxuICBjb25zdCB7IG11dGF0ZTogZGVsZXRlTWFudWZhY3R1cmVyTXV0YXRpb24sIGlzTG9hZGluZzogbG9hZGluZyB9ID1cclxuICAgIHVzZURlbGV0ZU1hbnVmYWN0dXJlck11dGF0aW9uKCk7XHJcblxyXG4gIGNvbnN0IHsgZGF0YTogbW9kYWxEYXRhIH0gPSB1c2VNb2RhbFN0YXRlKCk7XHJcbiAgY29uc3QgeyBjbG9zZU1vZGFsIH0gPSB1c2VNb2RhbEFjdGlvbigpO1xyXG5cclxuICBmdW5jdGlvbiBoYW5kbGVEZWxldGUoKSB7XHJcbiAgICBkZWxldGVNYW51ZmFjdHVyZXJNdXRhdGlvbih7XHJcbiAgICAgIGlkOiBtb2RhbERhdGEgYXMgc3RyaW5nLFxyXG4gICAgfSk7XHJcbiAgICBjbG9zZU1vZGFsKCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPENvbmZpcm1hdGlvbkNhcmRcclxuICAgICAgb25DYW5jZWw9e2Nsb3NlTW9kYWx9XHJcbiAgICAgIG9uRGVsZXRlPXtoYW5kbGVEZWxldGV9XHJcbiAgICAgIGRlbGV0ZUJ0bkxvYWRpbmc9e2xvYWRpbmd9XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBNYW51ZmFjdHVyZXJEZWxldGVWaWV3O1xyXG4iXSwibmFtZXMiOlsiQ29uZmlybWF0aW9uQ2FyZCIsInVzZU1vZGFsQWN0aW9uIiwidXNlTW9kYWxTdGF0ZSIsInVzZURlbGV0ZU1hbnVmYWN0dXJlck11dGF0aW9uIiwiTWFudWZhY3R1cmVyRGVsZXRlVmlldyIsIm11dGF0ZSIsImRlbGV0ZU1hbnVmYWN0dXJlck11dGF0aW9uIiwiaXNMb2FkaW5nIiwibG9hZGluZyIsImRhdGEiLCJtb2RhbERhdGEiLCJjbG9zZU1vZGFsIiwiaGFuZGxlRGVsZXRlIiwiaWQiLCJvbkNhbmNlbCIsIm9uRGVsZXRlIiwiZGVsZXRlQnRuTG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/manufacturer/manufacturer-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/manufacturer.ts":
/*!*****************************************!*\
  !*** ./src/data/client/manufacturer.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   manufacturerClient: () => (/* binding */ manufacturerClient)\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst manufacturerClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.MANUFACTURERS),\n    paginated: ({ name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.MANUFACTURERS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvbWFudWZhY3R1cmVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBeUQ7QUFRRztBQUNMO0FBRWhELE1BQU1HLHFCQUFxQjtJQUNoQyxHQUFHSCxzRUFBV0EsQ0FDWkMscUVBQWFBLENBQUNHLGFBQWEsQ0FDNUI7SUFDREMsV0FBVyxDQUFDLEVBQ1ZDLElBQUksRUFDSkMsT0FBTyxFQUNQLEdBQUdDLFFBQytCO1FBQ2xDLE9BQU9OLGdFQUFVQSxDQUFDTyxHQUFHLENBQXdCUixxRUFBYUEsQ0FBQ0csYUFBYSxFQUFFO1lBQ3hFTSxZQUFZO1lBQ1osR0FBR0YsTUFBTTtZQUNURyxRQUFRVCxnRUFBVUEsQ0FBQ1Usa0JBQWtCLENBQUM7Z0JBQUVOO2dCQUFNQztZQUFRO1FBQ3hEO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vc3JjL2RhdGEvY2xpZW50L21hbnVmYWN0dXJlci50cz9hYjdjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNydWRGYWN0b3J5IH0gZnJvbSAnQC9kYXRhL2NsaWVudC9jdXJkLWZhY3RvcnknO1xyXG5pbXBvcnQge1xyXG4gIENyZWF0ZU1hbnVmYWN0dXJlcklucHV0LFxyXG4gIE1hbnVmYWN0dXJlcixcclxuICBNYW51ZmFjdHVyZXJQYWdpbmF0b3IsXHJcbiAgTWFudWZhY3R1cmVyUXVlcnlPcHRpb25zLFxyXG4gIFF1ZXJ5T3B0aW9ucyxcclxufSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJ0AvZGF0YS9jbGllbnQvYXBpLWVuZHBvaW50cyc7XHJcbmltcG9ydCB7IEh0dHBDbGllbnQgfSBmcm9tICdAL2RhdGEvY2xpZW50L2h0dHAtY2xpZW50JztcclxuXHJcbmV4cG9ydCBjb25zdCBtYW51ZmFjdHVyZXJDbGllbnQgPSB7XHJcbiAgLi4uY3J1ZEZhY3Rvcnk8TWFudWZhY3R1cmVyLCBRdWVyeU9wdGlvbnMsIENyZWF0ZU1hbnVmYWN0dXJlcklucHV0PihcclxuICAgIEFQSV9FTkRQT0lOVFMuTUFOVUZBQ1RVUkVSU1xyXG4gICksXHJcbiAgcGFnaW5hdGVkOiAoe1xyXG4gICAgbmFtZSxcclxuICAgIHNob3BfaWQsXHJcbiAgICAuLi5wYXJhbXNcclxuICB9OiBQYXJ0aWFsPE1hbnVmYWN0dXJlclF1ZXJ5T3B0aW9ucz4pID0+IHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxNYW51ZmFjdHVyZXJQYWdpbmF0b3I+KEFQSV9FTkRQT0lOVFMuTUFOVUZBQ1RVUkVSUywge1xyXG4gICAgICBzZWFyY2hKb2luOiAnYW5kJyxcclxuICAgICAgLi4ucGFyYW1zLFxyXG4gICAgICBzZWFyY2g6IEh0dHBDbGllbnQuZm9ybWF0U2VhcmNoUGFyYW1zKHsgbmFtZSwgc2hvcF9pZCB9KSxcclxuICAgIH0pO1xyXG4gIH0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJjcnVkRmFjdG9yeSIsIkFQSV9FTkRQT0lOVFMiLCJIdHRwQ2xpZW50IiwibWFudWZhY3R1cmVyQ2xpZW50IiwiTUFOVUZBQ1RVUkVSUyIsInBhZ2luYXRlZCIsIm5hbWUiLCJzaG9wX2lkIiwicGFyYW1zIiwiZ2V0Iiwic2VhcmNoSm9pbiIsInNlYXJjaCIsImZvcm1hdFNlYXJjaFBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/client/manufacturer.ts\n");

/***/ }),

/***/ "./src/data/manufacturer.ts":
/*!**********************************!*\
  !*** ./src/data/manufacturer.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateManufacturerMutation: () => (/* binding */ useCreateManufacturerMutation),\n/* harmony export */   useDeleteManufacturerMutation: () => (/* binding */ useDeleteManufacturerMutation),\n/* harmony export */   useManufacturerQuery: () => (/* binding */ useManufacturerQuery),\n/* harmony export */   useManufacturersQuery: () => (/* binding */ useManufacturersQuery),\n/* harmony export */   useUpdateManufacturerMutation: () => (/* binding */ useUpdateManufacturerMutation),\n/* harmony export */   useUpdateManufacturerMutationInList: () => (/* binding */ useUpdateManufacturerMutationInList)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/client/manufacturer */ \"./src/data/client/manufacturer.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateManufacturerMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useDeleteManufacturerMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useUpdateManufacturerMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.manufacturer.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useUpdateManufacturerMutationInList = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.update, {\n        onSuccess: async ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS);\n        }\n    });\n};\nconst useManufacturerQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.get({\n            slug,\n            language\n        }));\n    return {\n        manufacturer: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useManufacturersQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.MANUFACTURERS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_manufacturer__WEBPACK_IMPORTED_MODULE_7__.manufacturerClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        manufacturers: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/manufacturer.ts\n");

/***/ })

};
;