"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_group-products_grid_tsx";
exports.ids = ["src_components_products_group-products_grid_tsx"];
exports.modules = {

/***/ "./src/components/products/group-products/grid.tsx":
/*!*********************************************************!*\
  !*** ./src/components/products/group-products/grid.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _lib_is_even__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/is-even */ \"./src/lib/is-even.ts\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n\n\n\n\n\n\nconst GroupProductsGrid = ({ products })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid w-full gap-5 sm:grid-cols-3 lg:grid-cols-4\",\n        children: products?.map((product, idx)=>{\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                href: _config_routes__WEBPACK_IMPORTED_MODULE_3__.Routes.product(product?.slug),\n                className: \"relative grid w-full bg-gray-100 lg:even:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_1__.Image, {\n                    src: product?.image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_5__.productPlaceholder,\n                    alt: product?.name,\n                    width: (0,_lib_is_even__WEBPACK_IMPORTED_MODULE_4__.isEven)(idx) ? 960 : 1560,\n                    height: 960,\n                    className: \"h-[43.75rem] rounded-lg lg:rounded-2xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\grid.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 13\n                }, undefined)\n            }, product?.id, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\grid.tsx\",\n                lineNumber: 13,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\grid.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GroupProductsGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wcm9kdWN0cy9ncm91cC1wcm9kdWN0cy9ncmlkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEM7QUFDTjtBQUNDO0FBQ0Y7QUFDaUI7QUFHeEQsTUFBTUssb0JBQW9CLENBQUMsRUFBRUMsUUFBUSxFQUEyQjtJQUM5RCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDWkYsVUFBVUcsSUFBSSxDQUFDQyxTQUFTQztZQUN2QixxQkFDRSw4REFBQ1YsMkRBQUlBO2dCQUNIVyxNQUFNVixrREFBTUEsQ0FBQ1EsT0FBTyxDQUFDQSxTQUFTRztnQkFDOUJMLFdBQVU7MEJBR1YsNEVBQUNSLHVEQUFLQTtvQkFDSmMsS0FBS0osU0FBU0ssT0FBT0MsWUFBWVosaUVBQWtCQTtvQkFDbkRhLEtBQUtQLFNBQVNRO29CQUNkQyxPQUFPaEIsb0RBQU1BLENBQUNRLE9BQU8sTUFBTTtvQkFDM0JTLFFBQVE7b0JBQ1JaLFdBQVU7Ozs7OztlQVBQRSxTQUFTVzs7Ozs7UUFXcEI7Ozs7OztBQUdOO0FBRUEsaUVBQWVoQixpQkFBaUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbXBvbmVudHMvcHJvZHVjdHMvZ3JvdXAtcHJvZHVjdHMvZ3JpZC50c3g/YzI3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbWFnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbWFnZSc7XHJcbmltcG9ydCBMaW5rIGZyb20gJ0AvY29tcG9uZW50cy91aS9saW5rJztcclxuaW1wb3J0IHsgUm91dGVzIH0gZnJvbSAnQC9jb25maWcvcm91dGVzJztcclxuaW1wb3J0IHsgaXNFdmVuIH0gZnJvbSAnQC9saWIvaXMtZXZlbic7XHJcbmltcG9ydCB7IHByb2R1Y3RQbGFjZWhvbGRlciB9IGZyb20gJ0AvbGliL3BsYWNlaG9sZGVycyc7XHJcbmltcG9ydCB7IFByb2R1Y3QgfSBmcm9tICdAL3R5cGVzJztcclxuXHJcbmNvbnN0IEdyb3VwUHJvZHVjdHNHcmlkID0gKHsgcHJvZHVjdHMgfTogeyBwcm9kdWN0czogUHJvZHVjdFtdIH0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIHctZnVsbCBnYXAtNSBzbTpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNFwiPlxyXG4gICAgICB7cHJvZHVjdHM/Lm1hcCgocHJvZHVjdCwgaWR4KSA9PiB7XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgIGhyZWY9e1JvdXRlcy5wcm9kdWN0KHByb2R1Y3Q/LnNsdWcpfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBncmlkIHctZnVsbCBiZy1ncmF5LTEwMCBsZzpldmVuOmNvbC1zcGFuLTJcIlxyXG4gICAgICAgICAgICBrZXk9e3Byb2R1Y3Q/LmlkfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICBzcmM9e3Byb2R1Y3Q/LmltYWdlPy5vcmlnaW5hbCA/PyBwcm9kdWN0UGxhY2Vob2xkZXJ9XHJcbiAgICAgICAgICAgICAgYWx0PXtwcm9kdWN0Py5uYW1lfVxyXG4gICAgICAgICAgICAgIHdpZHRoPXtpc0V2ZW4oaWR4KSA/IDk2MCA6IDE1NjB9XHJcbiAgICAgICAgICAgICAgaGVpZ2h0PXs5NjB9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1bNDMuNzVyZW1dIHJvdW5kZWQtbGcgbGc6cm91bmRlZC0yeGxcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICk7XHJcbiAgICAgIH0pfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEdyb3VwUHJvZHVjdHNHcmlkO1xyXG4iXSwibmFtZXMiOlsiSW1hZ2UiLCJMaW5rIiwiUm91dGVzIiwiaXNFdmVuIiwicHJvZHVjdFBsYWNlaG9sZGVyIiwiR3JvdXBQcm9kdWN0c0dyaWQiLCJwcm9kdWN0cyIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsInByb2R1Y3QiLCJpZHgiLCJocmVmIiwic2x1ZyIsInNyYyIsImltYWdlIiwib3JpZ2luYWwiLCJhbHQiLCJuYW1lIiwid2lkdGgiLCJoZWlnaHQiLCJpZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/products/group-products/grid.tsx\n");

/***/ }),

/***/ "./src/lib/is-even.ts":
/*!****************************!*\
  !*** ./src/lib/is-even.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isEven: () => (/* binding */ isEven)\n/* harmony export */ });\nfunction isEven(number) {\n    return number % 2 === 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2lzLWV2ZW4udHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLE9BQU9DLE1BQWM7SUFDbkMsT0FBT0EsU0FBUyxNQUFNO0FBQ3hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9saWIvaXMtZXZlbi50cz82MTljIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpc0V2ZW4obnVtYmVyOiBudW1iZXIpOiBib29sZWFuIHtcclxuICByZXR1cm4gbnVtYmVyICUgMiA9PT0gMDtcclxufVxyXG4iXSwibmFtZXMiOlsiaXNFdmVuIiwibnVtYmVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/lib/is-even.ts\n");

/***/ })

};
;