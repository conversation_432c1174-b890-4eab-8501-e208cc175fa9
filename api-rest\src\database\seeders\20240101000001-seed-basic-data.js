'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Seed Types
    await queryInterface.bulkInsert('types', [
      {
        name: 'General',
        slug: 'general',
        image: JSON.stringify({}),
        icon: 'ShoppingBagIcon',
        promotional_sliders: JSON.stringify([]),
        settings: JSON.stringify({
          isHome: true,
          layoutType: 'classic',
          productCard: 'neon'
        }),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Electronics',
        slug: 'electronics',
        image: JSON.stringify({}),
        icon: 'DeviceTabletIcon',
        promotional_sliders: JSON.stringify([]),
        settings: JSON.stringify({
          isHome: false,
          layoutType: 'classic',
          productCard: 'neon'
        }),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });

    // Seed Categories
    await queryInterface.bulkInsert('categories', [
      {
        name: 'Smartphones',
        slug: 'smartphones',
        parent_id: null,
        type_id: 2, // Electronics
        details: JSON.stringify({}),
        image: JSON.stringify({}),
        icon: 'DeviceMobileIcon',
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Laptops',
        slug: 'laptops',
        parent_id: null,
        type_id: 2, // Electronics
        details: JSON.stringify({}),
        image: JSON.stringify({}),
        icon: 'ComputerDesktopIcon',
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Clothing',
        slug: 'clothing',
        parent_id: null,
        type_id: 1, // General
        details: JSON.stringify({}),
        image: JSON.stringify({}),
        icon: 'ShirtIcon',
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });

    // Seed Tags
    await queryInterface.bulkInsert('tags', [
      {
        name: 'Popular',
        slug: 'popular',
        details: JSON.stringify({}),
        image: JSON.stringify({}),
        icon: 'StarIcon',
        type_id: 1,
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'New Arrival',
        slug: 'new-arrival',
        details: JSON.stringify({}),
        image: JSON.stringify({}),
        icon: 'SparklesIcon',
        type_id: 1,
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });

    // Seed Users (Admin)
    await queryInterface.bulkInsert('users', [
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });

    // Seed Shops
    await queryInterface.bulkInsert('shops', [
      {
        owner_id: 1,
        name: 'Demo Shop',
        slug: 'demo-shop',
        description: 'A demo shop for testing purposes',
        cover_image: JSON.stringify({}),
        logo: JSON.stringify({}),
        is_active: true,
        address: JSON.stringify({
          street_address: '123 Demo Street',
          city: 'Demo City',
          state: 'Demo State',
          zip: '12345',
          country: 'Demo Country'
        }),
        settings: JSON.stringify({
          contact: '<EMAIL>',
          website: 'https://demo-shop.com'
        }),
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('shops', null, {});
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('tags', null, {});
    await queryInterface.bulkDelete('categories', null, {});
    await queryInterface.bulkDelete('types', null, {});
  }
};
