(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3684],{10945:function(e,t,a){"use strict";a.d(t,{Z:function(){return CreateOrUpdateFlashSaleForm}});var l=a(85893),r=a(67294),s=a(87536),i=a(46531),n=a(33e3),o=a(60802),d=a(80602),c=a(92072),m=a(23091),p=a(11163),h=a(5233),f=a(47533),x=a(10265),b=a(9140),v=a(93345),g=a(16203),y=a(73263),j=a(90573),_=a(75814),S=a(88762);let chatbotAutoSuggestionForFlashSale=e=>{let{name:t}=e;return[{id:1,title:"Announce a Limited-Time Flash Sale"},{id:2,title:"Create a Compelling Flash Sale Ad"},{id:3,title:"Write a Flash Sale Email Newsletter"},{id:4,title:"Promote Your Flash Sale on Social Media"},{id:5,title:"Design a Flash Sale campaign for Your Website"},{id:6,title:"Write a Flash Sale Countdown Blog Post"},{id:7,title:"Craft an Irresistible Flash Sale Slogan"},{id:8,title:"Prepare Flash Sale Teasers for Social Media"},{id:9,title:"Write a Flash Sale Reminder Email"},{id:10,title:"Generate Flash Sale Messages for customers."}]};var w=a(99930),N=a(30042),F=a(16310),Z=a(79362);let k=F.Ry().shape({title:F.Z_().required("form:error-flash-sale-title-required"),description:F.Z_().required("form:error-flash-sale-description-required").max(Z.$5,"form:error-description-maximum-title").test({name:"description",skipAbsent:!0,test(e,t){var a,l;return!!((null==e?void 0:null===(l=e.replace(/<(.|\n)*?>/g,""))||void 0===l?void 0:null===(a=l.trim())||void 0===a?void 0:a.length)!==0||(null==e?void 0:e.includes("<img")))||t.createError({message:"form:error-flash-sale-description-required"})}}),start_date:F.hT().required("form:error-active-date-required"),type:F.Z_().required("form:error-flash-sale-campaign-type").nullable(),rate:F.Rx().when("type",{is:e=>"percentage"===e,then:()=>F.Rx().min(1,"form:error-minimum-title").max(99,"form:error-maximum-title").transform(e=>isNaN(e)?void 0:e).positive("form:error-must-number").required("form:error-rate-required"),otherwise:()=>F.Rx().transform(e=>isNaN(e)?void 0:e).nullable()}),sale_builder:F.Ry().shape({data_type:F.Z_().required("form:error-products-filter-option-required").nullable()}),products:F.IX().required("form:error-select-single-products-required").min(1,"form:error-product-one-required"),end_date:F.hT().required("form:error-expire-date-required").min(F.iH("start_date"),"form:error-expire-and-active-date")});var E=a(80246),D=a(28454),A=a(66272),L=a(93242),C=a(83987),O=a(77180),P=a(15724),M=a(22220),q=a(21587),T=a(79828),I=a(93967),R=a.n(I);function ShopFilter(e){let{onShopFilter:t,className:a,shop:r,enableShop:s}=e,{locale:i}=(0,p.useRouter)(),{t:n}=(0,h.$G)(),{shops:o,loading:d}=(0,N.uL)({is_active:!0});return(0,l.jsx)("div",{className:R()("flex w-full flex-col space-y-5 rtl:space-x-reverse md:flex-row md:items-end md:space-x-5 md:space-y-0",a),children:s?(0,l.jsxs)("div",{className:"w-full",children:[(0,l.jsx)(m.Z,{children:"Filter by shops"}),(0,l.jsx)(T.Z,{options:o,isLoading:d,getOptionLabel:e=>e.name,getOptionValue:e=>e.slug,placeholder:"filter products by listed shops",onChange:t,isClearable:!0})]}):""})}var G=a(60687);function CreateOrUpdateFlashSaleForm(e){var t,a,F,Z,T,I,R,Q,B,$,K,U,z;let{initialValues:V}=e,W=(0,p.useRouter)(),{locale:Y}=W,{t:X}=(0,h.$G)(),[J,ee]=(0,r.useState)(""),[et,ea]=(0,r.useState)(""),[el,er]=(0,r.useState)(""),[es,ei]=(0,r.useState)(""),[en,eo]=(0,r.useState)(""),{permissions:ed}=(0,g.WA)(),{data:ec,isLoading:eu,error:em}=(0,w.UE)(),{currency:ep}=(0,y.rV)(),{openModal:eh}=(0,_.SO)(),{settings:{options:ef}}=(0,j.n)({language:Y}),{data:ex}=(0,N.DZ)({slug:W.query.shop},{enabled:!!W.query.shop});null==ex||ex.id;let{products:eb,loading:ev}=(0,L.kN)({limit:999,language:Y,type:et,categories:es,flash_sale_builder:!0,status:"publish",shop_id:el,searchedByUser:"super_admin_builder",author:J,manufacturer:en}),{register:eg,handleSubmit:ey,control:ej,watch:e_,setError:eS,setValue:ew,formState:{errors:eN}}=(0,s.cI)({defaultValues:V?{...V,start_date:new Date(V.start_date),end_date:new Date(null==V?void 0:V.end_date),image:null==V?void 0:V.image,cover_image:null==V?void 0:V.cover_image,type:null==V?void 0:V.type,rate:null==V?void 0:V.rate,sale_status:null==V?void 0:V.sale_status,sale_builder:(null==V?void 0:V.sale_builder)?null==V?void 0:V.sale_builder:[]}:{start_date:new Date,type:x.Sw.PERCENTAGE,sale_builder:{data_type:"handpicked_products"}},resolver:(0,f.X)(k)}),{mutate:eF,isLoading:eZ}=(0,E.Mv)(),{mutate:ek,isLoading:eE}=(0,E.yp)(),eD=e_("title"),eA=(0,r.useMemo)(()=>chatbotAutoSuggestionForFlashSale({name:null!=eD?eD:""}),[eD]),eL=(0,r.useCallback)(()=>{eh("GENERATE_DESCRIPTION",{control:ej,name:eD,set_value:ew,key:"description",suggestion:eA})},[eD]),[eC,eO]=e_(["start_date","end_date"]),eP=e_("type"),eM=e_("sale_builder"),eq=e_("sale_builder.data_type");(0,r.useEffect)(()=>{switch(eq){case"handpick_shop":ea(""),ei(""),ee(""),eo("");break;case"handpick_category":er(""),ee(""),eo("");break;case"handpick_author":er(""),ea(""),ei(""),eo("");break;case"handpick_manufacturer":er(""),ea(""),ei(""),ee("")}},[eq]);let eT=W.locale!==v.Config.defaultLanguage,onSubmit=async e=>{var t,a;let l={language:W.locale,title:e.title,description:e.description,image:e.image,cover_image:e.cover_image,start_date:new Date(e.start_date).toISOString(),end_date:new Date(e.end_date).toISOString(),type:e.type,rate:e.rate,sale_status:e.sale_status,sale_builder:{data_type:null==e?void 0:null===(t=e.sale_builder)||void 0===t?void 0:t.data_type,product_ids:null==e?void 0:null===(a=e.products)||void 0===a?void 0:a.map(e=>null==e?void 0:e.id)}};try{V&&V.translated_languages.includes(W.locale)?ek({...l,id:V.id}):eF({...l})}catch(t){let e=(0,b.e)(t);Object.keys(null==e?void 0:e.validation).forEach(t=>{eS(t.split(".")[1],{type:"manual",message:null==e?void 0:e.validation[t][0]})})}},eI=(0,l.jsxs)("span",{children:[X("form:flash-sale-thumb-image-help-text")," ",(0,l.jsx)("br",{}),X("form:flash-sale-grid-image-dimension-help-text")," \xa0",(0,l.jsxs)("span",{className:"font-bold",children:["520 x 347",X("common:text-px")]})]}),eR=(0,l.jsxs)("span",{children:[X("form:flash-sale-cover-image-help-text")," ",(0,l.jsx)("br",{}),X("form:cover-image-dimension-help-text")," \xa0",(0,l.jsxs)("span",{className:"font-bold",children:["1920 x 700",X("common:text-px")]})]});return(0,l.jsxs)("form",{onSubmit:ey(onSubmit),children:[(0,l.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,l.jsx)(d.Z,{title:X("form:form-title-information"),details:X("form:info-flash-sale-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,l.jsx)(c.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,l.jsx)("div",{className:"my-5",children:(0,l.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,l.jsx)(O.Z,{name:"sale_status",control:ej}),(0,l.jsx)(m.Z,{className:"!mb-0.5",children:X("Enable flash deals")})]})})})]}),(0,l.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,l.jsx)(d.Z,{title:X("form:flash-sale-thumb-image-title"),details:eI,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,l.jsx)(c.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,l.jsx)(A.Z,{name:"image",control:ej,multiple:!1})})]}),(0,l.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,l.jsx)(d.Z,{title:X("form:shop-cover-image-title"),details:eR,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,l.jsx)(c.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,l.jsx)(A.Z,{name:"cover_image",control:ej,multiple:!1})})]}),(0,l.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,l.jsx)(d.Z,{title:X("form:input-label-description"),details:"".concat(X(V?"form:item-description-edit":"form:item-description-add")," campaign here."),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5 "}),(0,l.jsxs)(c.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,l.jsx)(n.Z,{label:"".concat(X("form:input-title")),...eg("title"),error:X(null===(t=eN.title)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5",required:!0}),(0,l.jsxs)("div",{className:"relative",children:[(null==ef?void 0:ef.useAi)&&(0,l.jsx)(S.Z,{title:X("form:button-label-description-ai"),onClick:eL}),(0,l.jsx)(G.Z,{title:X("form:input-label-description"),error:X(null==eN?void 0:null===(a=eN.description)||void 0===a?void 0:a.message),name:"description",control:ej,required:!0})]}),(0,l.jsx)(q.Z,{message:X("form:info-flash-sale-select-dates-text"),variant:"info",closeable:!1,className:"mt-5 mb-5"}),(0,l.jsxs)("div",{className:"flex flex-col mb-4 sm:flex-row",children:[(0,l.jsx)("div",{className:"w-full p-0 mb-5 sm:mb-0 sm:w-1/2 sm:pe-2",children:(0,l.jsx)(i.Z,{control:ej,name:"start_date",dateFormat:"dd/MM/yyyy",minDate:new Date,maxDate:new Date(eO),startDate:new Date(eC),endDate:new Date(eO),className:"border border-border-base",disabled:eT,label:X("form:store-notice-active-from"),error:X(null===(F=eN.start_date)||void 0===F?void 0:F.message),required:!0})}),(0,l.jsx)("div",{className:"w-full p-0 sm:w-1/2 sm:ps-2",children:(0,l.jsx)(i.Z,{control:ej,name:"end_date",dateFormat:"dd/MM/yyyy",startDate:new Date(eC),endDate:new Date(eO),minDate:new Date(eC),className:"border border-border-base",disabled:eT,required:!0,label:X("form:store-notice-expire-at"),error:X(null===(Z=eN.end_date)||void 0===Z?void 0:Z.message)})})]}),(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsxs)(m.Z,{children:[X("form:input-label-offering-campaign"),(0,l.jsx)("span",{className:"ltr:ml-0.5 rtl:mr-0.5 text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"mt-5 space-y-3.5",children:[(0,l.jsx)(C.Z,{label:"Fixed rate",...eg("type"),id:"fixed_rate",value:x.Sw.FIXED_RATE,disabled:eT}),(0,l.jsx)(C.Z,{label:X("form:input-label-percentage"),...eg("type"),id:"percentage",value:x.Sw.PERCENTAGE,disabled:eT}),(null===(T=eN.type)||void 0===T?void 0:T.message)&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:X(null===(I=eN.type)||void 0===I?void 0:I.message)})]})]}),eP&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(n.Z,{label:"".concat(X(eP)," amount applicable for this campaign (").concat(ep,")"),...eg("rate"),error:X(null===(R=eN.rate)||void 0===R?void 0:R.message),variant:"outline",className:"mb-5",disabled:eT,required:!0})}),eP===x.Sw.FIXED_RATE&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(q.Z,{message:X("form:info-flash-sale-campaign-rate-text"),variant:"info",closeable:!1,className:"mt-5"})})]})]}),(0,l.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,l.jsx)(d.Z,{title:X("form:input-label-offering-campaign-choose-products"),details:X("form:input-label-offering-campaign-choose-details"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,l.jsxs)(c.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsxs)(m.Z,{children:[X("form:input-label-offering-campaign-filter-option"),(0,l.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"mt-5 space-y-3.5",children:[(0,l.jsx)(C.Z,{label:"Handpicked products",...eg("sale_builder.data_type"),id:"handpicked_products",value:"handpicked_products",disabled:eT}),(0,l.jsx)(C.Z,{label:"Filter products by group & related Category",...eg("sale_builder.data_type"),id:"handpick_category",value:"handpick_category",disabled:eT}),(0,l.jsx)(C.Z,{label:"Filter products by shops",...eg("sale_builder.data_type"),id:"handpick_shop",value:"handpick_shop",disabled:eT}),(0,l.jsx)(C.Z,{label:"Filter products by authors",...eg("sale_builder.data_type"),id:"handpick_author",value:"handpick_author",disabled:eT}),(0,l.jsx)(C.Z,{label:"Filter products by manufacturer/publications",...eg("sale_builder.data_type"),id:"handpick_manufacturer",value:"handpick_manufacturer",disabled:eT}),(null==eN?void 0:null===(B=eN.sale_builder)||void 0===B?void 0:null===(Q=B.data_type)||void 0===Q?void 0:Q.message)&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:X(null==eN?void 0:null===(K=eN.sale_builder)||void 0===K?void 0:null===($=K.data_type)||void 0===$?void 0:$.message)})]})]}),(null==eM?void 0:eM.data_type)==="handpick_category"?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"mt-10",children:(0,l.jsx)(P.Z,{className:"w-full",type:et,enableCategory:!0,enableType:!0,onCategoryFilter:e=>{ei(null==e?void 0:e.slug)},onTypeFilter:e=>{ea(null==e?void 0:e.slug)}})})}):"",(null==eM?void 0:eM.data_type)==="handpick_shop"?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"mt-10",children:(0,l.jsx)(ShopFilter,{className:"w-full",shop:el,enableShop:!0,onShopFilter:e=>{er(null==e?void 0:e.id)}})})}):"",(null==eM?void 0:eM.data_type)==="handpick_author"?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"mt-10",children:(0,l.jsx)(P.Z,{className:"w-full",enableAuthor:!0,onAuthorFilter:e=>{ee(null==e?void 0:e.id)}})})}):"",(null==eM?void 0:eM.data_type)==="handpick_manufacturer"?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("div",{className:"mt-10",children:(0,l.jsx)(P.Z,{className:"w-full",enableManufacturer:!0,onManufactureFilter:e=>{eo(null==e?void 0:e.id)}})})}):"",eq&&(0,l.jsxs)("div",{className:"mt-10",children:[(0,l.jsxs)(m.Z,{children:[X("form:input-label-offering-campaign-choose-products")," ",(0,l.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"})]}),(0,l.jsx)(D.Z,{name:"products",control:ej,getOptionLabel:e=>"".concat(e.name," ").concat((null==e?void 0:e.price)?"- ".concat(ep," ").concat(null==e?void 0:e.price):""),getOptionValue:e=>e.id,options:eb,isClearable:!0,isLoading:ev,isMulti:!0}),(0,l.jsx)(q.Z,{message:X("form:info-about-product-chose-on-flash-sale"),variant:"warning",closeable:!1,className:"mt-5"}),(null==eN?void 0:null===(U=eN.products)||void 0===U?void 0:U.message)&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:X(null==eN?void 0:null===(z=eN.products)||void 0===z?void 0:z.message)})]})]})]}),(0,l.jsx)(M.Z,{className:"z-0",children:(0,l.jsxs)("div",{className:"text-end",children:[V&&(0,l.jsx)(o.Z,{variant:"outline",onClick:W.back,className:"text-sm me-4 md:text-base",type:"button",children:X("form:button-label-back")}),(0,l.jsx)(o.Z,{loading:eE||eZ,disabled:eE||eZ,className:"text-sm md:text-base",children:V?"Update Campaign":"Add Campaign"})]})})]})}},97670:function(e,t,a){"use strict";a.r(t);var l=a(85893),r=a(78985),s=a(79362),i=a(8144),n=a(74673),o=a(99494),d=a(5233),c=a(1631),m=a(11163),p=a(48583),h=a(93967),f=a.n(h),x=a(30824),b=a(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:a}=(0,d.$G)(),[r,i]=(0,p.KO)(s.Hf),{childMenu:n}=t,{width:o}=(0,b.Z)();return(0,l.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:t,label:i,icon:n,childMenu:d}=e;return(0,l.jsx)(c.Z,{href:t,label:a(i),icon:n,childMenu:d,miniSidebar:r&&o>=s.h2},i)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[a,r]=(0,p.KO)(s.Hf),i=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(i),{width:c}=(0,b.Z)();return(0,l.jsx)(l.Fragment,{children:null==n?void 0:n.map((e,r)=>{var n;return(0,l.jsxs)("div",{className:f()("flex flex-col px-5",a&&c>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,l.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",a&&c>=s.h2?"hidden":""),children:t(null===(n=i[e])||void 0===n?void 0:n.label)}),(0,l.jsx)(SidebarItemMap,{menuItems:i[e]})]},r)})})};t.default=e=>{let{children:t}=e,{locale:a}=(0,m.useRouter)(),[o,d]=(0,p.KO)(s.Hf),[c]=(0,p.KO)(s.GH),[h]=(0,p.KO)(s.W4),{width:v}=(0,b.Z)();return(0,l.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===a||"he"===a?"rtl":"ltr",children:[(0,l.jsx)(r.Z,{}),(0,l.jsx)(n.Z,{children:(0,l.jsx)(SideBarGroup,{})}),(0,l.jsxs)("div",{className:"flex flex-1",children:[(0,l.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",v>=s.h2&&(c||h)?"lg:pt-[8.75rem]":"pt-20",o&&v>=s.h2?"lg:w-24":"lg:w-76"),children:(0,l.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,l.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,l.jsx)(SideBarGroup,{})})})}),(0,l.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",v>=s.h2&&(c||h)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",o&&v>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,l.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,l.jsx)(i.Z,{})]})]})]})}},88762:function(e,t,a){"use strict";a.d(t,{Z:function(){return OpenAIButton}});var l=a(85893),r=a(93967),s=a.n(r);function OpenAIButton(e){let{className:t,onClick:a,title:r,...i}=e;return(0,l.jsx)("div",{onClick:a,className:s()("absolute right-0 -top-1 z-10 cursor-pointer text-sm font-medium text-accent hover:text-accent-hover",t),...i,children:r})}},46531:function(e,t,a){"use strict";var l=a(85893),r=a(66271),s=a(71611),i=a(93967),n=a.n(i),o=a(42298),d=a(9198),c=a.n(d);a(35890);var m=a(87536),p=a(98388);t.Z=e=>{let{control:t,minDate:a,startDate:i,locale:d,disabled:h,placeholder:f="Start Date",todayButton:x="Today",name:b,label:v,toolTipText:g,required:y,error:j,dateFormat:_,className:S,maxDate:w,endDate:N,showTimeSelect:F,timeFormat:Z,timeIntervals:k,timeCaption:E,filterTime:D,...A}=e;return(0,l.jsxs)(l.Fragment,{children:[v?(0,l.jsx)(s.Z,{htmlFor:b,toolTipText:g,label:v,required:y}):"",(0,l.jsx)(m.Qr,{control:t,name:b,render:e=>{let{field:t}=e;return(0,l.jsx)(c(),{...t,minDate:a,selected:(null==t?void 0:t.value)?new Date(null==t?void 0:t.value):"",startDate:new Date(i),locale:d,todayButton:x,placeholderText:f,disabled:h,className:S,maxDate:w,endDate:N,...F&&{showTimeSelect:F,timeFormat:Z,timeIntervals:k,timeCaption:E,filterTime:D},customInput:(0,l.jsx)("div",{className:(0,p.m6)(n()("border border-border-base px-4 h-12 flex items-center w-full rounded transition duration-300 ease-in-out text-heading text-sm cursor-pointer",h?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] select-none":"")),children:(null==t?void 0:t.value)?(0,o.default)(new Date(null==t?void 0:t.value),"MMMM, dd yyyy pp"):null==t?void 0:t.value})})},...A}),(0,l.jsx)(r.Z,{message:j})]})}},83987:function(e,t,a){"use strict";var l=a(85893),r=a(67294),s=a(29974),i=a.n(s);let n=r.forwardRef((e,t)=>{let{className:a,label:r,name:s,id:n,error:o,...d}=e;return(0,l.jsxs)("div",{className:a,children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{id:n,name:s,type:"radio",ref:t,className:i().radio_input,...d}),(0,l.jsx)("label",{htmlFor:n,className:"text-sm text-body",children:r})]}),o&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:o})]})});n.displayName="Radio",t.Z=n},28454:function(e,t,a){"use strict";var l=a(85893),r=a(79828),s=a(71611),i=a(87536);t.Z=e=>{let{control:t,options:a,name:n,rules:o,getOptionLabel:d,getOptionValue:c,disabled:m,isMulti:p,isClearable:h,isLoading:f,placeholder:x,label:b,required:v,toolTipText:g,error:y,...j}=e;return(0,l.jsxs)(l.Fragment,{children:[b?(0,l.jsx)(s.Z,{htmlFor:n,toolTipText:g,label:b,required:v}):"",(0,l.jsx)(i.Qr,{control:t,name:n,rules:o,...j,render:e=>{let{field:t}=e;return(0,l.jsx)(r.Z,{...t,getOptionLabel:d,getOptionValue:c,placeholder:x,isMulti:p,isClearable:h,isLoading:f,options:a,isDisabled:m})}}),y&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:y})]})}},22220:function(e,t,a){"use strict";var l=a(85893),r=a(93967),s=a.n(r),i=a(98388);t.Z=e=>{let{children:t,className:a,...r}=e;return(0,l.jsx)("div",{className:(0,i.m6)(s()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",a)),...r,children:t})}},77180:function(e,t,a){"use strict";var l=a(85893),r=a(66271),s=a(71611),i=a(77768),n=a(93967),o=a.n(n),d=a(5233),c=a(87536),m=a(98388);t.Z=e=>{let{control:t,label:a,name:n,error:p,disabled:h,required:f,toolTipText:x,className:b,labelClassName:v,...g}=e,{t:y}=(0,d.$G)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:(0,m.m6)(o()("flex items-center gap-x-4",b)),children:[(0,l.jsx)(c.Qr,{name:n,control:t,...g,render:e=>{let{field:{onChange:t,value:r}}=e;return(0,l.jsxs)(i.r,{checked:r,onChange:t,disabled:h,className:"".concat(r?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(h?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:n,children:[(0,l.jsxs)("span",{className:"sr-only",children:["Enable ",a]}),(0,l.jsx)("span",{className:"".concat(r?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),a?(0,l.jsx)(s.Z,{htmlFor:n,className:o()("mb-0",v),toolTipText:x,label:a,required:f}):""]}),p?(0,l.jsx)(r.Z,{message:p}):""]})}},60687:function(e,t,a){"use strict";var l=a(85893),r=a(5152),s=a.n(r),i=a(55846),n=a(67294);t.Z=e=>{let{title:t,placeholder:r,control:o,className:d,editorClassName:c,name:m,required:p,disabled:h,error:f,...x}=e,b=(0,n.useMemo)(()=>s()(()=>Promise.all([a.e(5556),a.e(3305),a.e(2937),a.e(939),a.e(6870),a.e(3373)]).then(a.bind(a,97148)),{loadableGenerated:{webpack:()=>[97148]},ssr:!1,loading:()=>(0,l.jsx)("div",{className:"py-8 flex",children:(0,l.jsx)(i.Z,{simple:!0,className:"h-6 w-6 mx-auto"})})}),[]);return(0,l.jsx)(b,{title:t,placeholder:r,control:o,className:d,editorClassName:c,name:m,required:p,disabled:h,error:f,...x})}},80246:function(e,t,a){"use strict";a.d(t,{Mv:function(){return useCreateFlashSaleMutation},in:function(){return useDeleteFlashSaleMutation},gr:function(){return useFlashSaleQuery},AX:function(){return useFlashSalesQuery},Aq:function(){return useProductFlashSaleInfo},yp:function(){return useUpdateFlashSaleMutation}});var l=a(11163),r=a.n(l),s=a(88767),i=a(22920),n=a(5233),o=a(28597),d=a(97514),c=a(47869),m=a(93345),p=a(55191),h=a(3737);let f={...(0,p.h)(c.P.FLASH_SALE),all:function(){let{title:e,shop_id:t,...a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:t,...a,search:h.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{slug:t,language:a,shop_id:l}=e;return h.eN.get("".concat(c.P.FLASH_SALE,"/").concat(t),{language:a,shop_id:l,slug:t,with:"products"})},paginated:e=>{let{title:t,shop_id:a,...l}=e;return h.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:a,...l,search:h.eN.formatSearchParams({title:t,shop_id:a})})},approve:e=>h.eN.post(c.P.FLASH_SALE,e),disapprove:e=>h.eN.post(c.P.FLASH_SALE,e),getFlashSaleInfoByProductID(e){let{id:t,language:a}=e;return h.eN.get(c.P.PRODUCT_FLASH_SALE_INFO,{searchJoin:"and",id:t,language:a,with:"flash_sales"})}},useFlashSaleQuery=e=>{let{slug:t,language:a,shop_id:l}=e,{data:r,error:i,isLoading:n}=(0,s.useQuery)([c.P.FLASH_SALE,{slug:t,language:a,shop_id:l}],()=>f.get({slug:t,language:a,shop_id:l}));return{flashSale:r,error:i,loading:n}},useFlashSalesQuery=e=>{var t;let{data:a,error:l,isLoading:r}=(0,s.useQuery)([c.P.FLASH_SALE,e],e=>{let{queryKey:t,pageParam:a}=e;return f.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{flashSale:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,o.Q)(a),error:l,loading:r}},useCreateFlashSaleMutation=()=>{let e=(0,s.useQueryClient)(),t=(0,l.useRouter)(),{t:a}=(0,n.$G)();return(0,s.useMutation)(f.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.flashSale.list):d.Z.flashSale.list;await r().push(e,void 0,{locale:m.Config.defaultLanguage}),i.Am.success(a("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var t;i.Am.error(a("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleMutation=()=>{let{t:e}=(0,n.$G)(),t=(0,s.useQueryClient)(),a=(0,l.useRouter)();return(0,s.useMutation)(f.update,{onSuccess:async t=>{let l=a.query.shop?"/".concat(a.query.shop).concat(d.Z.flashSale.list):d.Z.flashSale.list;await a.push(l,void 0,{locale:m.Config.defaultLanguage}),i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)},onError:t=>{var a;i.Am.error(e("common:".concat(null==t?void 0:null===(a=t.response)||void 0===a?void 0:a.data.message)))}})},useDeleteFlashSaleMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,n.$G)();return(0,s.useMutation)(f.delete,{onSuccess:()=>{i.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var a;i.Am.error(t("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}})},useProductFlashSaleInfo=e=>{let{id:t,language:a}=e,{data:l,error:r,isLoading:i}=(0,s.useQuery)([c.P.PRODUCT_FLASH_SALE_INFO,{id:t,language:a}],()=>f.getFlashSaleInfoByProductID({id:t,language:a}));return{flashSaleInfo:l,error:r,loading:i}}},9140:function(e,t,a){"use strict";a.d(t,{e:function(){return getErrorMessage}});var l=a(11163),r=a.n(l),s=a(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let a of e.graphQLErrors){if(a.extensions&&"validation"===a.extensions.category)return t.message=a.message,t.validation=a.extensions.validation,t;a.extensions&&"authorization"===a.extensions.category&&(s.Z.remove("auth_token"),s.Z.remove("auth_permissions"),r().push("/"))}return t.message=e.message,t}},29974:function(e){e.exports={radio_input:"radio_radio_input__EWRL5"}},1413:function(e,t,a){"use strict";a.d(t,{Z:function(){return _objectSpread2}});var l=a(4942);function ownKeys(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,l)}return a}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(a),!0).forEach(function(t){(0,l.Z)(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}},95389:function(e,t,a){"use strict";a.d(t,{_:function(){return c},b:function(){return H}});var l=a(67294),r=a(19946),s=a(12351),i=a(16723),n=a(23784),o=a(73781);let d=(0,l.createContext)(null);function H(){let[e,t]=(0,l.useState)([]);return[e.length>0?e.join(" "):void 0,(0,l.useMemo)(()=>function(e){let a=(0,o.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let a=t.slice(),l=a.indexOf(e);return -1!==l&&a.splice(l,1),a}))),r=(0,l.useMemo)(()=>({register:a,slot:e.slot,name:e.name,props:e.props}),[a,e.slot,e.name,e.props]);return l.createElement(d.Provider,{value:r},e.children)},[t])]}let c=Object.assign((0,s.yV)(function(e,t){let a=(0,r.M)(),{id:o=`headlessui-label-${a}`,passive:c=!1,...m}=e,p=function u(){let e=(0,l.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),h=(0,n.T)(t);(0,i.e)(()=>p.register(o),[o,p.register]);let f={ref:h,...p.props,id:o};return c&&("onClick"in f&&(delete f.htmlFor,delete f.onClick),"onClick"in m&&delete m.onClick),(0,s.sY)({ourProps:f,theirProps:m,slot:p.slot||{},defaultTag:"label",name:p.name||"Label"})}),{})},77768:function(e,t,a){"use strict";a.d(t,{r:function(){return y}});var l=a(67294),r=a(12351),s=a(19946),i=a(61363),n=a(64103),o=a(95389),d=a(39516),c=a(14157),m=a(23784),p=a(46045),h=a(18689),f=a(73781),x=a(31147),b=a(94192);let v=(0,l.createContext)(null);v.displayName="GroupContext";let g=l.Fragment,y=Object.assign((0,r.yV)(function(e,t){let a=(0,s.M)(),{id:o=`headlessui-switch-${a}`,checked:d,defaultChecked:g=!1,onChange:y,name:j,value:_,form:S,...w}=e,N=(0,l.useContext)(v),F=(0,l.useRef)(null),Z=(0,m.T)(F,t,null===N?null:N.setSwitch),[k,E]=(0,x.q)(d,y,g),D=(0,f.z)(()=>null==E?void 0:E(!k)),A=(0,f.z)(e=>{if((0,n.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),D()}),L=(0,f.z)(e=>{e.key===i.R.Space?(e.preventDefault(),D()):e.key===i.R.Enter&&(0,h.g)(e.currentTarget)}),C=(0,f.z)(e=>e.preventDefault()),O=(0,l.useMemo)(()=>({checked:k}),[k]),P={id:o,ref:Z,role:"switch",type:(0,c.f)(e,F),tabIndex:0,"aria-checked":k,"aria-labelledby":null==N?void 0:N.labelledby,"aria-describedby":null==N?void 0:N.describedby,onClick:A,onKeyUp:L,onKeyPress:C},M=(0,b.G)();return(0,l.useEffect)(()=>{var e;let t=null==(e=F.current)?void 0:e.closest("form");t&&void 0!==g&&M.addEventListener(t,"reset",()=>{E(g)})},[F,E]),l.createElement(l.Fragment,null,null!=j&&k&&l.createElement(p._,{features:p.A.Hidden,...(0,r.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:S,checked:k,name:j,value:_})}),(0,r.sY)({ourProps:P,theirProps:w,slot:O,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[a,s]=(0,l.useState)(null),[i,n]=(0,o.b)(),[c,m]=(0,d.f)(),p=(0,l.useMemo)(()=>({switch:a,setSwitch:s,labelledby:i,describedby:c}),[a,s,i,c]);return l.createElement(m,{name:"Switch.Description"},l.createElement(n,{name:"Switch.Label",props:{htmlFor:null==(t=p.switch)?void 0:t.id,onClick(e){a&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),a.click(),a.focus({preventScroll:!0}))}}},l.createElement(v.Provider,{value:p},(0,r.sY)({ourProps:{},theirProps:e,defaultTag:g,name:"Switch.Group"}))))},Label:o._,Description:d.d})}}]);