"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ui_search_search_tsx";
exports.ids = ["src_components_ui_search_search_tsx"];
exports.modules = {

/***/ "./src/components/ui/search/search.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/search/search.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/search/search-box */ \"./src/components/ui/search/search-box.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _search_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./search.context */ \"./src/components/ui/search/search.context.tsx\");\n\n\n\n\n\nconst Search = ({ label, variant, className, inputClassName, ...props })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { searchTerm, updateSearchTerm } = (0,_search_context__WEBPACK_IMPORTED_MODULE_4__.useSearch)();\n    const handleOnChange = (e)=>{\n        const { value } = e.target;\n        updateSearchTerm(value);\n    };\n    const onSearch = (e)=>{\n        e.preventDefault();\n        if (!searchTerm) return;\n        const { pathname, query } = router;\n        router.push({\n            pathname,\n            query: {\n                ...query,\n                text: searchTerm\n            }\n        }, undefined, {\n            scroll: false\n        });\n    };\n    function clearSearch() {\n        updateSearchTerm(\"\");\n        const { pathname, query } = router;\n        const { text, ...rest } = query;\n        if (text) {\n            router.push({\n                pathname,\n                query: {\n                    ...rest\n                }\n            }, undefined, {\n                scroll: false\n            });\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search_search_box__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        label: label,\n        onSubmit: onSearch,\n        onClearSearch: clearSearch,\n        onChange: handleOnChange,\n        value: searchTerm,\n        name: \"search\",\n        placeholder: t(\"common:text-search-placeholder\"),\n        variant: variant,\n        className: className,\n        inputClassName: inputClassName,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\search\\\\search.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Search);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/search/search.tsx\n");

/***/ })

};
;