(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8917,9930],{48654:function(e,r,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/profile-update",function(){return s(90934)}])},33367:function(e,r,s){"use strict";s.d(r,{b:function(){return Eye}});var t=s(85893);let Eye=e=>(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:[(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})},71626:function(e,r,s){"use strict";s.d(r,{Z:function(){return AppLayout}});var t=s(85893),o=s(79362),n=s(5152),a=s.n(n);let l=a()(()=>Promise.all([s.e(6342),s.e(4750),s.e(5921),s.e(1609),s.e(2964),s.e(8544),s.e(2023),s.e(6117),s.e(9494),s.e(5535),s.e(8186),s.e(1285),s.e(1631),s.e(2007)]).then(s.bind(s,97670)),{loadableGenerated:{webpack:()=>[97670]}}),i=a()(()=>Promise.all([s.e(1609),s.e(2964),s.e(8544),s.e(2023),s.e(6117),s.e(9709),s.e(9494),s.e(5535),s.e(8186),s.e(1285),s.e(2801),s.e(2036)]).then(s.bind(s,82801)),{loadableGenerated:{webpack:()=>[82801]}});function AppLayout(e){let{userPermissions:r,...s}=e;return(null==r?void 0:r.includes(o.Mc))?(0,t.jsx)(l,{...s}):(0,t.jsx)(i,{...s})}},33359:function(e,r,s){"use strict";s.d(r,{Z:function(){return c}});var t=s(85893),o=s(33367);let EyeOff=e=>(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"})});var n=s(93967),a=s.n(n),l=s(67294),i=s(8152);let u={root:"ltr:pl-4 rtl:pr-4 ltr:pr-12 rtl:pl-12 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",normal:"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent",solid:"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent",outline:"border border-border-base focus:border-accent",shadow:"focus:shadow"},d=l.forwardRef((e,r)=>{let{className:s,inputClassName:n,forgotPassHelpText:d,label:c,name:m,error:f,children:p,variant:b="normal",shadow:v=!1,type:x="text",forgotPageLink:h="",required:g,...w}=e,[S,y]=(0,l.useState)(!1),P=a()(u.root,{[u.normal]:"normal"===b,[u.solid]:"solid"===b,[u.outline]:"outline"===b},!0==v&&u.shadow,n);return(0,t.jsxs)("div",{className:s,children:[(0,t.jsxs)("div",{className:"mb-3 flex items-center justify-between",children:[(0,t.jsxs)("label",{htmlFor:m,className:"text-sm font-semibold leading-none text-body-dark",children:[c,g?(0,t.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):""]}),h&&d&&(0,t.jsx)(i.Z,{href:h,className:"text-xs text-accent transition-colors duration-200 hover:text-accent-hover focus:font-semibold focus:text-accent-700 focus:outline-none",children:d})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:m,name:m,type:S?"text":"password",ref:r,className:P,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",...w}),(0,t.jsx)("label",{htmlFor:m,className:"absolute top-5 -mt-2 text-body end-4",onClick:()=>y(e=>!e),children:S?(0,t.jsx)(EyeOff,{className:"h-5 w-5"}):(0,t.jsx)(o.b,{className:"h-5 w-5"})})]}),f&&(0,t.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:f})]})});d.displayName="PasswordInput";var c=d},92732:function(e,r,s){"use strict";var t=s(85893),o=s(87536),n=s(93967),a=s.n(n);s(67294);var l=s(67555),i=s.n(l);s(48705);var u=s(98388),d=s(71611);r.Z=e=>{let{label:r,required:s,showLabel:n=!0,error:l,className:c,inputClassName:m,toolTipText:f,disabled:p,note:b,name:v,control:x,...h}=e;return(0,t.jsxs)("div",{className:(0,u.m6)(a()("mb-5",c)),children:[(0,t.jsx)(o.Qr,{render:e=>{let{field:{onChange:o,value:c}}=e;return(0,t.jsxs)(t.Fragment,{children:[n?(0,t.jsx)(d.Z,{htmlFor:v,toolTipText:f,label:r,required:s}):"",(0,t.jsx)(i(),{value:c,onChange:o,inputClass:(0,u.m6)(a()("!p-0 !pe-4 !ps-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base !rounded focus:!border-accent !h-12",p?"cursor-not-allowed !border-[#D4D8DD] !bg-[#EEF1F4] select-none":"",m)),dropdownClass:"focus:!ring-0 !border !border-border-base !shadow-350","aria-invalid":l?"true":"false",disabled:p})]})},id:v,name:v,control:x,...h}),b&&(0,t.jsx)("p",{className:"mt-2 text-xs text-body",children:b}),l&&(0,t.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:l})]})}},77180:function(e,r,s){"use strict";var t=s(85893),o=s(66271),n=s(71611),a=s(77768),l=s(93967),i=s.n(l),u=s(5233),d=s(87536),c=s(98388);r.Z=e=>{let{control:r,label:s,name:l,error:m,disabled:f,required:p,toolTipText:b,className:v,labelClassName:x,...h}=e,{t:g}=(0,u.$G)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:(0,c.m6)(i()("flex items-center gap-x-4",v)),children:[(0,t.jsx)(d.Qr,{name:l,control:r,...h,render:e=>{let{field:{onChange:r,value:o}}=e;return(0,t.jsxs)(a.r,{checked:o,onChange:r,disabled:f,className:"".concat(o?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(f?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:l,children:[(0,t.jsxs)("span",{className:"sr-only",children:["Enable ",s]}),(0,t.jsx)("span",{className:"".concat(o?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),s?(0,t.jsx)(n.Z,{htmlFor:l,className:i()("mb-0",x),toolTipText:b,label:s,required:p}):""]}),m?(0,t.jsx)(o.Z,{message:m}):""]})}},95414:function(e,r,s){"use strict";var t=s(85893),o=s(71611),n=s(93967),a=s.n(n),l=s(67294),i=s(98388);let u=l.forwardRef((e,r)=>{let{className:s,label:n,toolTipText:l,name:u,error:d,variant:c="normal",shadow:m=!1,inputClassName:f,disabled:p,required:b,...v}=e,x=a()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===c,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===c,"border border-border-base focus:border-accent":"outline"===c},{"focus:shadow":m},f);return(0,t.jsxs)("div",{className:(0,i.m6)(a()(s)),children:[n&&(0,t.jsx)(o.Z,{htmlFor:u,toolTipText:l,label:n,required:b}),(0,t.jsx)("textarea",{id:u,name:u,className:(0,i.m6)(a()(x,p?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:r,disabled:p,...v}),d&&(0,t.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:d})]})});u.displayName="TextArea",r.Z=u},44498:function(e,r,s){"use strict";s.d(r,{B:function(){return n}});var t=s(47869),o=s(3737);let n={me:()=>o.eN.get(t.P.ME),login:e=>o.eN.post(t.P.TOKEN,e),logout:()=>o.eN.post(t.P.LOGOUT,{}),register:e=>o.eN.post(t.P.REGISTER,e),update:e=>{let{id:r,input:s}=e;return o.eN.put("".concat(t.P.USERS,"/").concat(r),s)},changePassword:e=>o.eN.post(t.P.CHANGE_PASSWORD,e),forgetPassword:e=>o.eN.post(t.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>o.eN.post(t.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>o.eN.post(t.P.RESET_PASSWORD,e),makeAdmin:e=>o.eN.post(t.P.MAKE_ADMIN,e),block:e=>o.eN.post(t.P.BLOCK_USER,e),unblock:e=>o.eN.post(t.P.UNBLOCK_USER,e),addWalletPoints:e=>o.eN.post(t.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>o.eN.post(t.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:r,...s}=e;return o.eN.get(t.P.USERS,{searchJoin:"and",with:"wallet",...s,search:o.eN.formatSearchParams({name:r})})},fetchAdmins:e=>{let{...r}=e;return o.eN.get(t.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...r})},fetchUser:e=>{let{id:r}=e;return o.eN.get("".concat(t.P.USERS,"/").concat(r))},resendVerificationEmail:()=>o.eN.post(t.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:r}=e;return o.eN.post(t.P.UPDATE_EMAIL,{email:r})},fetchVendors:e=>{let{is_active:r,...s}=e;return o.eN.get(t.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:r,...s})},fetchCustomers:e=>{let{...r}=e;return o.eN.get(t.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...r})},getMyStaffs:e=>{let{is_active:r,shop_id:s,name:n,...a}=e;return o.eN.get(t.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...a,search:o.eN.formatSearchParams({name:n,is_active:r})})},getAllStaffs:e=>{let{is_active:r,name:s,...n}=e;return o.eN.get(t.P.ALL_STAFFS,{searchJoin:"and",...n,search:o.eN.formatSearchParams({name:s,is_active:r})})}}},99930:function(e,r,s){"use strict";s.d(r,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var t=s(79362),o=s(97514),n=s(31955),a=s(5233),l=s(11163),i=s(88767),u=s(22920),d=s(47869),c=s(44498),m=s(28597),f=s(87066),p=s(16203);let useMeQuery=()=>{let e=(0,i.useQueryClient)(),r=(0,l.useRouter)();return(0,i.useQuery)([d.P.ME],c.B.me,{retry:!1,onSuccess:()=>{r.pathname===o.Z.verifyLicense&&r.replace(o.Z.dashboard),r.pathname===o.Z.verifyEmail&&((0,p.Fu)(!0),r.replace(o.Z.dashboard))},onError:s=>{if(f.Z.isAxiosError(s)){var t,n;if((null===(t=s.response)||void 0===t?void 0:t.status)===417){r.replace(o.Z.verifyLicense);return}if((null===(n=s.response)||void 0===n?void 0:n.status)===409){(0,p.Fu)(!1),r.replace(o.Z.verifyEmail);return}e.clear(),r.replace(o.Z.login)}}})};function useLogin(){return(0,i.useMutation)(c.B.login)}let useLogoutMutation=()=>{let e=(0,l.useRouter)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(c.B.logout,{onSuccess:()=>{n.Z.remove(t.E$),e.replace(o.Z.login),u.Am.success(r("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,i.useQueryClient)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(c.B.register,{onSuccess:()=>{u.Am.success(r("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(d.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,a.$G)(),r=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.update,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(d.P.ME),r.invalidateQueries(d.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,a.$G)(),r=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.updateEmail,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:r}}=null!=e?e:{};u.Am.error(null==r?void 0:r.message)},onSettled:()=>{r.invalidateQueries(d.P.ME),r.invalidateQueries(d.P.USERS)}})},useChangePasswordMutation=()=>(0,i.useMutation)(c.B.changePassword),useForgetPasswordMutation=()=>(0,i.useMutation)(c.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,a.$G)("common");return(0,i.useMutation)(c.B.resendVerificationEmail,{onSuccess:()=>{u.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,u.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,a.$G)();(0,i.useQueryClient)();let r=(0,l.useRouter)();return(0,i.useMutation)(c.B.addLicenseKey,{onSuccess:()=>{u.Am.success(e("common:successfully-updated")),setTimeout(()=>{r.reload()},1e3)},onError:()=>{u.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,i.useMutation)(c.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,i.useMutation)(c.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,i.useQueryClient)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(c.B.makeAdmin,{onSuccess:()=>{u.Am.success(r("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(d.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,i.useQueryClient)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(c.B.block,{onSuccess:()=>{u.Am.success(r("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(d.P.USERS),e.invalidateQueries(d.P.STAFFS),e.invalidateQueries(d.P.ADMIN_LIST),e.invalidateQueries(d.P.CUSTOMERS),e.invalidateQueries(d.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,i.useQueryClient)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(c.B.unblock,{onSuccess:()=>{u.Am.success(r("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(d.P.USERS),e.invalidateQueries(d.P.STAFFS),e.invalidateQueries(d.P.ADMIN_LIST),e.invalidateQueries(d.P.CUSTOMERS),e.invalidateQueries(d.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,a.$G)(),r=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.addWalletPoints,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(d.P.USERS)}})},useUserQuery=e=>{let{id:r}=e;return(0,i.useQuery)([d.P.USERS,r],()=>c.B.fetchUser({id:r}),{enabled:!!r})},useUsersQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.USERS,e],()=>c.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}},useAdminsQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.ADMIN_LIST,e],()=>c.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}},useVendorsQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.VENDORS_LIST,e],()=>c.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}},useCustomersQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.CUSTOMERS,e],()=>c.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}},useMyStaffsQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.MY_STAFFS,e],()=>c.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}},useAllStaffsQuery=e=>{var r;let{data:s,isLoading:t,error:o}=(0,i.useQuery)([d.P.ALL_STAFFS,e],()=>c.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(s),loading:t,error:o}}},90934:function(e,r,s){"use strict";s.r(r),s.d(r,{__N_SSG:function(){return A},default:function(){return ProfilePage}});var t=s(85893),o=s(71626),n=s(33e3),a=s(87536),l=s(60802),i=s(80602),u=s(92072),d=s(99930),c=s(95414),m=s(5233),f=s(66272),p=s(78718),b=s.n(p),v=s(77180),x=s(23091),h=s(16203),g=s(47533),w=s(16310);let S=w.Ry().shape({name:w.Z_().required("form:error-name-required"),profile:w.Ry().shape({contact:w.Z_().max(19,"maximum 19 digit").optional()})});var y=s(92732);function ProfileUpdate(e){var r,s,o,p,w,P,N,E;let{me:j}=e,{t:M}=(0,m.$G)(),{mutate:A,isLoading:_}=(0,d.kD)(),{permissions:Q}=(0,h.WA)(),R=(0,h.Ft)(h.M$,Q),{register:Z,handleSubmit:k,control:L,formState:{errors:C}}=(0,a.cI)({resolver:(0,g.X)(S),defaultValues:{...j&&b()(j,["name","profile.bio","profile.contact","profile.avatar","profile.notifications.email","profile.notifications.enable"])}});async function onSubmit(e){var r,s,t,o;let{name:n,profile:a}=e,{notifications:l}=a,i={id:null==j?void 0:j.id,input:{name:n,profile:{id:null==j?void 0:null===(r=j.profile)||void 0===r?void 0:r.id,bio:null==a?void 0:a.bio,contact:null==a?void 0:a.contact,avatar:{thumbnail:null==a?void 0:null===(s=a.avatar)||void 0===s?void 0:s.thumbnail,original:null==a?void 0:null===(t=a.avatar)||void 0===t?void 0:t.original,id:null==a?void 0:null===(o=a.avatar)||void 0===o?void 0:o.id},notifications:{...l}}}};A({...i})}return(0,t.jsxs)("form",{onSubmit:k(onSubmit),children:[(0,t.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,t.jsx)(i.Z,{title:M("form:input-label-avatar"),details:M("form:avatar-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,t.jsx)(u.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,t.jsx)(f.Z,{name:"profile.avatar",control:L,multiple:!1})})]}),R?(0,t.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,t.jsx)(i.Z,{title:M("form:form-notification-title"),details:M("form:form-notification-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,t.jsxs)(u.Z,{className:"w-full mb-5 sm:w-8/12 md:w-2/3",children:[(0,t.jsx)(n.Z,{label:M("form:input-notification-email"),...Z("profile.notifications.email"),error:M(null==C?void 0:null===(o=C.profile)||void 0===o?void 0:null===(s=o.notifications)||void 0===s?void 0:null===(r=s.email)||void 0===r?void 0:r.message),variant:"outline",className:"mb-5",type:"email"}),(0,t.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,t.jsx)(v.Z,{name:"profile.notifications.enable",control:L}),(0,t.jsx)(x.Z,{className:"!mb-0.5",children:M("form:input-enable-notification")})]})]})]}):"",(0,t.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,t.jsx)(i.Z,{title:M("form:form-title-information"),details:M("form:profile-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,t.jsxs)(u.Z,{className:"w-full mb-5 sm:w-8/12 md:w-2/3",children:[(0,t.jsx)(n.Z,{label:M("form:input-label-name"),...Z("name"),error:M(null===(p=C.name)||void 0===p?void 0:p.message),variant:"outline",className:"mb-5"}),(0,t.jsx)(c.Z,{label:M("form:input-label-bio"),...Z("profile.bio"),error:M(null===(P=C.profile)||void 0===P?void 0:null===(w=P.bio)||void 0===w?void 0:w.message),variant:"outline",className:"mb-6"}),(0,t.jsx)(y.Z,{label:M("form:input-label-contact"),...Z("profile.contact"),control:L,error:M(null===(E=C.profile)||void 0===E?void 0:null===(N=E.contact)||void 0===N?void 0:N.message)})]}),(0,t.jsx)("div",{className:"w-full text-end",children:(0,t.jsx)(l.Z,{loading:_,disabled:_,children:M("form:button-label-save")})})]})]})}var P=s(22920),N=s(33359);let E=w.Ry().shape({oldPassword:w.Z_().required("form:error-old-password-required"),newPassword:w.Z_().required("form:error-password-required"),passwordConfirmation:w.Z_().oneOf([w.iH("newPassword")],"form:error-match-passwords").required("form:error-confirm-password")});var change_password_from=()=>{var e,r,s;let{t:o}=(0,m.$G)(),{mutate:n,isLoading:c}=(0,d.$h)(),{register:f,handleSubmit:p,setError:b,reset:v,formState:{errors:x}}=(0,a.cI)({resolver:(0,g.X)(E)});async function onSubmit(e){n({oldPassword:e.oldPassword,newPassword:e.newPassword},{onError:e=>{var r;Object.keys(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data).forEach(r=>{var s;b(r,{type:"manual",message:null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data[r][0]})})},onSuccess:e=>{if(null==e?void 0:e.success)(null==e?void 0:e.success)&&(P.Am.success(o("common:password-changed-successfully")),v());else{var r;b("oldPassword",{type:"manual",message:null!==(r=null==e?void 0:e.message)&&void 0!==r?r:""})}}})}return(0,t.jsx)("form",{noValidate:!0,onSubmit:p(onSubmit),children:(0,t.jsxs)("div",{className:"my-5 flex flex-wrap sm:my-8",children:[(0,t.jsx)(i.Z,{title:o("form:input-label-password"),details:o("form:password-help-text"),className:"sm:pe-4 md:pe-5 w-full px-0 pb-5 sm:w-4/12 sm:py-8 md:w-1/3"}),(0,t.jsxs)(u.Z,{className:"mb-5 w-full sm:w-8/12 md:w-2/3",children:[(0,t.jsx)(N.Z,{label:o("form:input-label-old-password"),...f("oldPassword"),variant:"outline",error:o(null===(e=x.oldPassword)||void 0===e?void 0:e.message),className:"mb-5"}),(0,t.jsx)(N.Z,{label:o("form:input-label-new-password"),...f("newPassword"),variant:"outline",error:o(null===(r=x.newPassword)||void 0===r?void 0:r.message),className:"mb-5"}),(0,t.jsx)(N.Z,{label:o("form:input-label-confirm-password"),...f("passwordConfirmation"),variant:"outline",error:o(null===(s=x.passwordConfirmation)||void 0===s?void 0:s.message)})]}),(0,t.jsx)("div",{className:"text-end w-full",children:(0,t.jsx)(l.Z,{loading:c,disabled:c,children:o("form:button-label-change-password")})})]})})},j=s(45957),M=s(55846);function EmailUpdateForm(e){var r;let{me:s}=e,{t:o}=(0,m.$G)(),{mutate:c,isLoading:f}=(0,d.of)(),{register:p,handleSubmit:v,formState:{errors:x}}=(0,a.cI)({defaultValues:{...s&&b()(s,["email"])}});async function onSubmit(e){let{email:r}=e;c({email:r})}return(0,t.jsx)("form",{onSubmit:v(onSubmit),children:(0,t.jsxs)("div",{className:"my-5 flex flex-wrap border-b border-dashed border-border-base pb-8 sm:my-8",children:[(0,t.jsx)(i.Z,{title:o("common:text-email"),details:o("form:email-change-helper-text"),className:"sm:pe-4 md:pe-5 w-full px-0 pb-5 sm:w-4/12 sm:py-8 md:w-1/3"}),(0,t.jsx)(u.Z,{className:"mb-5 w-full sm:w-8/12 md:w-2/3",children:(0,t.jsx)(n.Z,{label:o("form:input-label-email"),...p("email"),error:o(null===(r=x.email)||void 0===r?void 0:r.message),variant:"outline",className:"mb-5"})}),(0,t.jsx)("div",{className:"text-end w-full",children:(0,t.jsx)(l.Z,{loading:f,disabled:f,children:o("form:button-label-save")})})]})})}var A=!0;function ProfilePage(){let{t:e}=(0,m.$G)(),{data:r,isLoading:s,error:o}=(0,d.UE)();return s?(0,t.jsx)(M.Z,{text:e("common:text-loading")}):o?(0,t.jsx)(j.Z,{message:o.message}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:form-title-profile-settings")})}),(0,t.jsx)(EmailUpdateForm,{me:r}),(0,t.jsx)(ProfileUpdate,{me:r}),(0,t.jsx)(change_password_from,{})]})}ProfilePage.Layout=o.Z}},function(e){e.O(0,[6342,4750,5921,7536,2216,2512,7755,8680,8468,5468,9774,2888,179],function(){return e(e.s=48654)}),_N_E=e.O()}]);