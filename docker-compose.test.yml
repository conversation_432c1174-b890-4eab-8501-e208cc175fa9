services:
  # Test PostgreSQL connection
  postgres-test:
    image: postgres:15-alpine
    container_name: postgres-test
    environment:
      POSTGRES_DB: ecommerce
      POSTGRES_USER: ecommerce_owner
      POSTGRES_PASSWORD: npg_aI0Dn8AMfbWj
    ports:
      - "5433:5432"
    networks:
      - test-network

  # Test MinIO
  minio-test:
    image: minio/minio:latest
    container_name: minio-test
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9002:9000"
      - "9003:9001"
    networks:
      - test-network

networks:
  test-network:
    driver: bridge
