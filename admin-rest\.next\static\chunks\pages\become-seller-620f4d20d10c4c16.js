(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[561,2007,2036],{29314:function(e,o,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/become-seller",function(){return i(63211)}])},46626:function(e,o,i){"use strict";i.d(o,{a:function(){return ArrowUp}});var l=i(85893);i(67294);let ArrowUp=e=>{let{color:o="currentColor",width:i="12px",height:r="12px",...t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:i,height:r,viewBox:"0 0 11.996 12",...t,children:(0,l.jsx)("path",{"data-name":"Path 2462",d:"M18.276,12.1,12.7,6.524a.424.424,0,0,0-.6,0L6.524,12.1a.424.424,0,0,0,0,.6.424.424,0,0,0,.6,0l4.854-4.854V17.977a.423.423,0,1,0,.847,0V7.846L17.677,12.7a.424.424,0,0,0,.6,0A.434.434,0,0,0,18.276,12.1Z",transform:"translate(-6.4 -6.4)",fill:o})})}},97670:function(e,o,i){"use strict";i.r(o);var l=i(85893),r=i(78985),t=i(79362),n=i(8144),s=i(74673),a=i(99494),d=i(5233),c=i(1631),m=i(11163),p=i(48583),v=i(93967),f=i.n(v),g=i(30824),b=i(62964);let SidebarItemMap=e=>{let{menuItems:o}=e,{t:i}=(0,d.$G)(),[r,n]=(0,p.KO)(t.Hf),{childMenu:s}=o,{width:a}=(0,b.Z)();return(0,l.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:o,label:n,icon:s,childMenu:d}=e;return(0,l.jsx)(c.Z,{href:o,label:i(n),icon:s,childMenu:d,miniSidebar:r&&a>=t.h2},n)})})},SideBarGroup=()=>{var e;let{t:o}=(0,d.$G)(),[i,r]=(0,p.KO)(t.Hf),n=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(n),{width:c}=(0,b.Z)();return(0,l.jsx)(l.Fragment,{children:null==s?void 0:s.map((e,r)=>{var s;return(0,l.jsxs)("div",{className:f()("flex flex-col px-5",i&&c>=t.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,l.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",i&&c>=t.h2?"hidden":""),children:o(null===(s=n[e])||void 0===s?void 0:s.label)}),(0,l.jsx)(SidebarItemMap,{menuItems:n[e]})]},r)})})};o.default=e=>{let{children:o}=e,{locale:i}=(0,m.useRouter)(),[a,d]=(0,p.KO)(t.Hf),[c]=(0,p.KO)(t.GH),[v]=(0,p.KO)(t.W4),{width:x}=(0,b.Z)();return(0,l.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===i||"he"===i?"rtl":"ltr",children:[(0,l.jsx)(r.Z,{}),(0,l.jsx)(s.Z,{children:(0,l.jsx)(SideBarGroup,{})}),(0,l.jsxs)("div",{className:"flex flex-1",children:[(0,l.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",x>=t.h2&&(c||v)?"lg:pt-[8.75rem]":"pt-20",a&&x>=t.h2?"lg:w-24":"lg:w-76"),children:(0,l.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,l.jsx)(g.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,l.jsx)(SideBarGroup,{})})})}),(0,l.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",x>=t.h2&&(c||v)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&x>=t.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,l.jsx)("div",{className:"h-full p-5 md:p-8",children:o}),(0,l.jsx)(n.Z,{})]})]})]})}},28454:function(e,o,i){"use strict";var l=i(85893),r=i(79828),t=i(71611),n=i(87536);o.Z=e=>{let{control:o,options:i,name:s,rules:a,getOptionLabel:d,getOptionValue:c,disabled:m,isMulti:p,isClearable:v,isLoading:f,placeholder:g,label:b,required:x,toolTipText:h,error:y,..._}=e;return(0,l.jsxs)(l.Fragment,{children:[b?(0,l.jsx)(t.Z,{htmlFor:s,toolTipText:h,label:b,required:x}):"",(0,l.jsx)(n.Qr,{control:o,name:s,rules:a,..._,render:e=>{let{field:o}=e;return(0,l.jsx)(r.Z,{...o,getOptionLabel:d,getOptionValue:c,placeholder:g,isMulti:p,isClearable:v,isLoading:f,options:i,isDisabled:m})}}),y&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:y})]})}},87077:function(e,o,i){"use strict";i.d(o,{W:function(){return r},X:function(){return l}});let l={option:(e,o)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:o.isSelected?"#E5E7EB":o.isFocused?"#F9FAFB":"#ffffff"}),control:(e,o)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==o?void 0:o.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==o?void 0:o.isDisabled)?"#D4D8DD":o.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:o.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,o)=>({...e,paddingLeft:16}),singleValue:(e,o)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,o)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,o)=>({...e,paddingLeft:o.isRtl?0:12,paddingRight:o.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,o)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},r={option:(e,o)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:o.isSelected?"#EEF1F4":o.isFocused?"#EEF1F4":"#ffffff"}),control:(e,o)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==o?void 0:o.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==o?void 0:o.isDisabled)?"#D4D8DD":o.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:o.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,o)=>({...e,paddingLeft:16}),singleValue:(e,o)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,o)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,o)=>({...e,paddingLeft:o.isRtl?0:12,paddingRight:o.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,o)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,o,i){"use strict";var l=i(85893),r=i(76518),t=i(67294),n=i(23157),s=i(87077);let a=t.forwardRef((e,o)=>{let{isRTL:i}=(0,r.S)();return(0,l.jsx)(n.ZP,{ref:o,styles:s.X,isRtl:i,...e})});a.displayName="Select",o.Z=a},22220:function(e,o,i){"use strict";var l=i(85893),r=i(93967),t=i.n(r),n=i(98388);o.Z=e=>{let{children:o,className:i,...r}=e;return(0,l.jsx)("div",{className:(0,n.m6)(t()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",i)),...r,children:o})}},77180:function(e,o,i){"use strict";var l=i(85893),r=i(66271),t=i(71611),n=i(77768),s=i(93967),a=i.n(s),d=i(5233),c=i(87536),m=i(98388);o.Z=e=>{let{control:o,label:i,name:s,error:p,disabled:v,required:f,toolTipText:g,className:b,labelClassName:x,...h}=e,{t:y}=(0,d.$G)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:(0,m.m6)(a()("flex items-center gap-x-4",b)),children:[(0,l.jsx)(c.Qr,{name:s,control:o,...h,render:e=>{let{field:{onChange:o,value:r}}=e;return(0,l.jsxs)(n.r,{checked:r,onChange:o,disabled:v,className:"".concat(r?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(v?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,l.jsxs)("span",{className:"sr-only",children:["Enable ",i]}),(0,l.jsx)("span",{className:"".concat(r?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),i?(0,l.jsx)(t.Z,{htmlFor:s,className:a()("mb-0",x),toolTipText:g,label:i,required:f}):""]}),p?(0,l.jsx)(r.Z,{message:p}):""]})}},95414:function(e,o,i){"use strict";var l=i(85893),r=i(71611),t=i(93967),n=i.n(t),s=i(67294),a=i(98388);let d=s.forwardRef((e,o)=>{let{className:i,label:t,toolTipText:s,name:d,error:c,variant:m="normal",shadow:p=!1,inputClassName:v,disabled:f,required:g,...b}=e,x=n()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===m,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===m,"border border-border-base focus:border-accent":"outline"===m},{"focus:shadow":p},v);return(0,l.jsxs)("div",{className:(0,a.m6)(n()(i)),children:[t&&(0,l.jsx)(r.Z,{htmlFor:d,toolTipText:s,label:t,required:g}),(0,l.jsx)("textarea",{id:d,name:d,className:(0,a.m6)(n()(x,f?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:o,disabled:f,...b}),c&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});d.displayName="TextArea",o.Z=d},60687:function(e,o,i){"use strict";var l=i(85893),r=i(5152),t=i.n(r),n=i(55846),s=i(67294);o.Z=e=>{let{title:o,placeholder:r,control:a,className:d,editorClassName:c,name:m,required:p,disabled:v,error:f,...g}=e,b=(0,s.useMemo)(()=>t()(()=>Promise.all([i.e(5556),i.e(3305),i.e(2937),i.e(939),i.e(6870),i.e(3373)]).then(i.bind(i,97148)),{loadableGenerated:{webpack:()=>[97148]},ssr:!1,loading:()=>(0,l.jsx)("div",{className:"py-8 flex",children:(0,l.jsx)(n.Z,{simple:!0,className:"h-6 w-6 mx-auto"})})}),[]);return(0,l.jsx)(b,{title:o,placeholder:r,control:a,className:d,editorClassName:c,name:m,required:p,disabled:v,error:f,...g})}},63211:function(e,o,i){"use strict";i.r(o),i.d(o,{__N_SSG:function(){return el},default:function(){return BecomeSeller}});var l={};i.r(l),i.d(l,{BullsEyeIcon:function(){return BullsEyeIcon},ChatIcon:function(){return ChatIcon},ReceiptIcon:function(){return ReceiptIcon},RegisteredDocumentIcon:function(){return RegisteredDocumentIcon},ShoppingBagIcon:function(){return ShoppingBagIcon},StoreIcon:function(){return StoreIcon}});var r=i(85893),t=i(16310),n=i(79362);let s=t.Ry().shape({image:t.nK().required().test("file","form:error-image",e=>!!e&&Object.keys(e).length>0),title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required"),buttonName:t.Z_().nullable(),buttonLink:t.Z_().url().nullable(),button2Name:t.Z_().nullable(),button2Link:t.Z_().url().nullable()}),a=t.Ry().shape({page_options:t.Ry().shape({banner:t.Ry().shape({image:t.nK().required().test("file","form:error-image",e=>!!e&&Object.keys(e).length>0),newsTickerTitle:t.Z_().nullable(),newsTickerURL:t.Z_().url().nullable(),title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required"),button1Name:t.Z_().nullable(),button1Link:t.Z_().url().nullable(),button2Name:t.Z_().nullable(),button2Link:t.Z_().url().nullable()}),sellingStepsTitle:t.Z_().required("form:error-title-required"),sellingStepsDescription:t.Z_().required("form:error-description-required"),sellingStepsItem:t.IX().min(1,"form:error-minimum-one-required").of(t.Ry().shape({title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required"),image:t.nK().required().test("file","form:error-image",e=>!!e&&Object.keys(e).length>0)})).required(),userStoryTitle:t.Z_().nullable(),userStoryDescription:t.Z_().nullable(),userStories:t.IX().of(t.Ry().shape({title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required"),link:t.Z_().url("form:error-url-valid-required").required("form:error-url-required"),thumbnail:t.nK().nullable()})),purposeTitle:t.Z_().required("form:error-title-required"),purposeDescription:t.Z_().required("form:error-description-required"),purposeItems:t.IX().min(1,"form:error-minimum-one-required").of(t.Ry().shape({title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required"),icon:t.Ry().shape({value:t.Z_().required("form:error-icon-required")}).required("form:error-icon-required")})).required(),commissionTitle:t.Z_().required("form:error-title-required"),commissionDescription:t.Z_().required("form:error-title-required"),dashboard:s,guidelineTitle:t.Z_().required("form:error-title-required"),guidelineDescription:t.Z_().required("form:error-description-required"),guidelineItems:t.IX().min(1,"form:error-minimum-one-required").of(t.Ry().shape({title:t.Z_().required("form:error-title-required"),link:t.Z_().url().nullable()})).required(),faqTitle:t.Z_().required("form:error-title-required"),faqDescription:t.Z_().required("form:error-title-required"),faqItems:t.IX().min(1,"form:error-minimum-one-required").of(t.Ry().shape({title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required").max(n.$5,"form:error-description-maximum-title").test({name:"description",skipAbsent:!0,test(e,o){var i,l;return!!((null==e?void 0:null===(l=e.replace(/<(.|\n)*?>/g,""))||void 0===l?void 0:null===(i=l.trim())||void 0===i?void 0:i.length)!==0||(null==e?void 0:e.includes("<img")))||o.createError({message:"form:error-description-required"})}})})).required(),contact:t.Ry().shape({title:t.Z_().required("form:error-title-required"),description:t.Z_().required("form:error-description-required")}),sellerOpportunity:s,defaultCommissionDetails:t.Z_().when("isMultiCommissionRate",{is:e=>!e,then:()=>t.Z_().required("form:error-description-required"),otherwise:()=>t.Z_().nullable()}),defaultCommissionRate:t.Rx().when("isMultiCommissionRate",{is:e=>!e,then:()=>t.Rx().required("form:error-commission-rate-required").typeError("form:error-commission-rate-type").positive("form:error-commission-rate-positive"),otherwise:()=>t.Rx().nullable()})}),commissions:t.IX().when("page_options.isMultiCommissionRate",{is:e=>e,then:()=>t.IX().min(1,"form:error-minimum-one-required").of(t.Ry().shape({level:t.Z_().required("form:error-title-required"),sub_level:t.Z_().required("form:error-subtitle-required"),description:t.Z_().required("form:error-description-required"),min_balance:t.Rx().typeError("form:error-minimum-balance-must-be-number").required("form:error-minimum-balance-is-required"),max_balance:t.Z_().matches(/^[0-9]|\W|over$/,"form:error-maximum-balance-type").required("form:error-maximum-balance-is-required"),commission:t.Rx().required("form:error-commission-rate-required").typeError("form:error-commission-rate-type").positive("form:error-commission-rate-positive"),image:t.nK().test("file","form:error-image",e=>!!e&&Object.keys(e).length>0)})).required(),otherwise:()=>t.IX().nullable()})});var d=i(92072),c=i(80602),m=i(66272),p=i(33e3),v=i(95414),f=i(5233);let bytesToMegabytes=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;return e/1024/1024},Banner=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,_,j,N,Z,S,q,C,I,k,E,T,R,D,B,O;let{register:P,control:L,errors:z,max_fileSize:F}=e,{t:A}=(0,f.$G)();return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:A("form:banner-title"),details:A("form:banner-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:A("form:text-news-ticker-title"),...P("page_options.banner.newsTickerTitle"),error:A(null==z?void 0:null===(l=z.page_options)||void 0===l?void 0:null===(i=l.banner)||void 0===i?void 0:null===(o=i.newsTickerTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:A("form:text-news-ticker-link"),...P("page_options.banner.newsTickerURL"),error:A(null==z?void 0:null===(s=z.page_options)||void 0===s?void 0:null===(n=s.banner)||void 0===n?void 0:null===(t=n.newsTickerURL)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{label:A("form:input-title"),...P("page_options.banner.title"),error:A(null==z?void 0:null===(b=z.page_options)||void 0===b?void 0:null===(g=b.banner)||void 0===g?void 0:null===(a=g.title)||void 0===a?void 0:a.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:A("form:input-description"),variant:"outline",...P("page_options.banner.description"),error:A(null==z?void 0:null===(y=z.page_options)||void 0===y?void 0:null===(h=y.banner)||void 0===h?void 0:null===(x=h.description)||void 0===x?void 0:x.message),className:"mb-5",required:!0}),(0,r.jsx)(p.Z,{label:A("form:text-primary-button-name"),...P("page_options.banner.button1Name"),error:A(null==z?void 0:null===(N=z.page_options)||void 0===N?void 0:null===(j=N.banner)||void 0===j?void 0:null===(_=j.button1Name)||void 0===_?void 0:_.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:A("form:text-primary-button-link"),...P("page_options.banner.button1Link"),error:A(null==z?void 0:null===(q=z.page_options)||void 0===q?void 0:null===(S=q.banner)||void 0===S?void 0:null===(Z=S.button1Link)||void 0===Z?void 0:Z.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{label:A("form:text-secondary-button-name"),...P("page_options.banner.button2Name"),error:A(null==z?void 0:null===(k=z.page_options)||void 0===k?void 0:null===(I=k.banner)||void 0===I?void 0:null===(C=I.button2Name)||void 0===C?void 0:C.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:A("form:text-secondary-button-link"),...P("page_options.banner.button2Link"),error:A(null==z?void 0:null===(R=z.page_options)||void 0===R?void 0:null===(T=R.banner)||void 0===T?void 0:null===(E=T.button2Link)||void 0===E?void 0:E.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(m.Z,{name:"page_options.banner.image",control:L,multiple:!1,maxSize:F,required:!0,label:A("form:input-label-image"),error:A(null==z?void 0:null===(O=z.page_options)||void 0===O?void 0:null===(B=O.banner)||void 0===B?void 0:null===(D=B.image)||void 0===D?void 0:D.message),toolTipText:"".concat(A("form:upload-image-help-text")," ").concat(A("form:upload-image-help-text-dimension")," 630 x 682 ").concat(A("common:text-pixel-dot").toLowerCase()," ").concat(A("form:size-help-text")," ").concat(bytesToMegabytes(F)," MB")})]})]})},StoreIcon=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M30.203 17.928v8.751a4.726 4.726 0 01-1.332 4.002 4.707 4.707 0 01-3.995 1.334h-4.74a.532.532 0 01-.533-.533v-5.475a3.715 3.715 0 00-2.943-3.723 3.554 3.554 0 00-3.824 2.007c-.219.47-.333.98-.335 1.499v5.692a.534.534 0 01-.532.533H7.122a4.7 4.7 0 01-3.995-1.334 4.719 4.719 0 01-1.331-4.002v-8.75a7.85 7.85 0 003.373.747 8.877 8.877 0 005.45-1.886 8.646 8.646 0 0010.777 0 8.878 8.878 0 005.451 1.886 7.83 7.83 0 003.356-.747zM8.01 0C3.57 0 2.89 1.458 2.16 3.625L.313 9.09a5.21 5.21 0 002.13 6.19 5.421 5.421 0 002.726.727 5.818 5.818 0 005.424-3.558 5.884 5.884 0 0010.812 0 5.818 5.818 0 005.424 3.557 5.42 5.42 0 002.727-.725 5.21 5.21 0 002.13-6.191l-1.838-5.466C29.108 1.458 28.428 0 23.988 0H8.01z",fill:"currentColor"})}),BullsEyeIcon=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("path",{d:"M27.622 5.731l-1.433-1.433V0l-4.458 4.617-.318 3.662-4.617 4.617H16a3.193 3.193 0 00-3.184 3.184A3.193 3.193 0 0016 19.264a3.193 3.193 0 003.184-3.184c0-.319 0-.478-.16-.796l4.618-4.617 3.662-.319 4.616-4.617h-4.298z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M24.597 12.1h-.16l-2.228 2.388c.16.477.16 1.114.16 1.592 0 3.502-2.867 6.368-6.37 6.368-3.502 0-6.367-2.866-6.367-6.368 0-3.503 2.866-6.369 6.368-6.369.637 0 1.114.16 1.592.16l2.388-2.388v-.16c-1.274-.477-2.547-.796-3.98-.796-5.254 0-9.552 4.299-9.552 9.553 0 5.253 4.298 9.552 9.552 9.552s9.552-4.299 9.552-9.553c0-1.432-.318-2.706-.955-3.98z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M30.488 9.552l-2.389 2.23c.319 1.273.637 2.706.637 4.298 0 7.005-5.731 12.736-12.736 12.736-7.005 0-12.736-5.731-12.736-12.736C3.264 9.075 8.995 3.343 16 3.343c1.433 0 2.866.319 4.299.796V3.98l2.387-2.388C20.616.637 18.389.159 16 .159 7.244.16.08 7.323.08 16.08.08 24.837 7.244 32 16 32s15.92-7.164 15.92-15.92c0-2.388-.477-4.617-1.433-6.528z",fill:"currentColor"})]}),ChatIcon=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M24 1.333H8A6.675 6.675 0 001.333 8v10.667A6.679 6.679 0 006.667 25.2v4.133a1.333 1.333 0 002.073 1.11l7.66-5.11H24a6.675 6.675 0 006.667-6.666V8A6.674 6.674 0 0024 1.333zm-2.666 16H10.667a1.333 1.333 0 010-2.666h10.667a1.333 1.333 0 010 2.666zM24 12H8a1.333 1.333 0 110-2.667h16A1.333 1.333 0 0124 12z",fill:"currentColor"})}),ReceiptIcon=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 30 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M24.6 0H5.4A4.8 4.8 0 00.6 4.8v23.68a3.2 3.2 0 004.576 2.896c.384-.192.72-.4 1.072-.608a3.76 3.76 0 012.352-.752 3.807 3.807 0 012.368.752A6.864 6.864 0 0015 32a6.848 6.848 0 004.032-1.232 3.792 3.792 0 012.368-.752 3.728 3.728 0 012.352.752c.347.216.704.414 1.072.592a3.2 3.2 0 004.576-2.88V4.8A4.8 4.8 0 0024.6 0zM15 22.4H8.6a1.6 1.6 0 010-3.2H15a1.6 1.6 0 010 3.2zm6.4-6.4H8.6a1.6 1.6 0 110-3.2h12.8a1.6 1.6 0 010 3.2zm0-6.4H8.6a1.6 1.6 0 010-3.2h12.8a1.6 1.6 0 010 3.2z",fill:"currentColor"})}),RegisteredDocumentIcon=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("path",{d:"M3.864 32h16.534c-2.934-1.6-4.934-4.667-4.934-8.267 0-5.2 4.267-9.466 9.467-9.466h.133v-10.8C25.064 1.6 23.464 0 21.598 0H3.863C1.997 0 .397 1.6.397 3.467v25.066C.397 30.4 1.864 32 3.864 32zM6.931 5.333h11.466c.8 0 1.334.667 1.334 1.334 0 .8-.534 1.333-1.334 1.333H6.932c-.8 0-1.334-.533-1.334-1.333 0-.667.667-1.334 1.334-1.334zm0 6.267h7.467c.8 0 1.333.667 1.333 1.333 0 .8-.534 1.334-1.333 1.334H6.93c-.8 0-1.334-.534-1.334-1.334S6.264 11.6 6.931 11.6z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M24.93 17.067c-3.732 0-6.8 3.066-6.8 6.8 0 3.733 3.068 6.8 6.8 6.8 3.734 0 6.8-3.067 6.8-6.8 0-3.734-3.066-6.8-6.8-6.8zm3.334 6.133l-3.333 3.2c-.267.267-.534.4-.934.4s-.666-.133-.933-.4l-1.6-1.733c-.533-.534-.533-1.334.134-1.867a1.289 1.289 0 011.866 0l.667.8 2.4-2.267a1.289 1.289 0 011.867 0c.4.4.4 1.334-.134 1.867z",fill:"currentColor"})]}),ShoppingBagIcon=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 32 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M31.922 26.088L28.896 3.011A3.462 3.462 0 0025.465 0H6.784a3.462 3.462 0 00-3.432 3.011L.326 26.088A3.461 3.461 0 003.758 30H28.49a3.461 3.461 0 003.432-3.912zM9.197 5.771a6.926 6.926 0 006.923 6.923 6.927 6.927 0 006.923-6.923 1.155 1.155 0 00-2.308 0 4.618 4.618 0 01-4.615 4.615 4.618 4.618 0 01-4.615-4.615 1.154 1.154 0 00-2.308 0z",fill:"currentColor"})});var g=i(47559);let b=[{value:"StoreIcon",label:"Store"},{value:"BullsEyeIcon",label:"Bulls Eye"},{value:"ChatIcon",label:"Cart"},{value:"ReceiptIcon",label:"Receipt"},{value:"RegisteredDocumentIcon",label:"Registered Document"},{value:"ShoppingBagIcon",label:"Shopping Bag"}].map(e=>(e.label=(0,r.jsxs)("div",{className:"flex items-center space-s-5",children:[(0,r.jsx)("span",{className:"flex items-center justify-center w-5 h-5",children:(0,g.q)({iconList:l,iconName:e.value,className:"max-h-full max-w-full"})}),(0,r.jsx)("span",{children:e.label})]}),e));var x=i(28368),h=i(11355),y=i(67294);let Chevron=e=>{let{color:o="currentColor",width:i="12",height:l="7",...t}=e;return(0,r.jsx)("svg",{width:i,height:l,viewBox:"0 0 12 7",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:(0,r.jsx)("path",{d:"M5.665.542a.791.791 0 00-.561.232L.232 5.646a.793.793 0 101.122 1.122l4.31-4.31 4.311 4.31a.793.793 0 101.122-1.123L6.226.774a.791.791 0 00-.561-.232z",fill:o})})};var _=i(21587),j=i(93967),N=i.n(j),Z=i(86779);let Warning=e=>(0,r.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:0,viewBox:"0 0 512 512",height:"1em",width:"1em",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M449.07 399.08L278.64 82.58c-12.08-22.44-44.26-22.44-56.35 0L51.87 399.08A32 32 0 0080 446.25h340.89a32 32 0 0028.18-47.17zm-198.6-1.83a20 20 0 1120-20 20 20 0 01-20 20zm21.72-201.15l-5.74 122a16 16 0 01-32 0l-5.74-121.95a21.73 21.73 0 0121.5-22.69h.21a21.74 21.74 0 0121.73 22.7z",stroke:"none"})});var S=i(98388),accordion=e=>{let{defaultOpen:o,isError:i,buttonTitle:l,children:t,icon:n=i?(0,r.jsx)(Warning,{className:"text-lg"}):(0,r.jsx)(Z.s,{className:"text-lg"}),className:s}=e;return(0,r.jsx)(x.p,{defaultOpen:o,children:e=>{let{open:o}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.p.Button,{className:"w-full",children:(0,r.jsxs)(_.Z,{childClassName:N()("flex gap-2 items-center w-full",i?"animate-pulse":""),message:l,variant:i?"error":"success",children:[n,(0,r.jsx)(Chevron,{className:N()("ml-auto transform transition-transform duration-300",o?"rotate-180":"rotate-0")})]})}),(0,r.jsx)(h.u,{show:o,enter:"transition duration-100 ease-out",enterFrom:"transform scale-95 opacity-0",enterTo:"transform scale-100 opacity-100",leave:"transition duration-75 ease-out",leaveFrom:"transform scale-100 opacity-100",leaveTo:"transform scale-95 opacity-0",children:(0,r.jsx)(x.p.Panel,{className:(0,S.m6)(N()(s)),static:!0,children:t})})]})}})},q=i(60802),C=i(28454),I=i(41609),k=i.n(I),E=i(87536);let BusinessPurpose=e=>{var o,i,l,t,n,s,a,m,g,x,h,y,j,Z;let{register:S,control:I,errors:T,watch:R,purposeItems:D}=e,{t:B}=(0,f.$G)(),{fields:O,append:P,remove:L}=(0,E.Dq)({control:I,name:"page_options.purposeItems"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:B("form:business-purpose-title"),details:B("form:business-purpose-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:B("form:input-title"),...S("page_options.purposeTitle"),error:B(null==T?void 0:null===(i=T.page_options)||void 0===i?void 0:null===(o=i.purposeTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:B("form:input-description"),variant:"outline",...S("page_options.purposeDescription"),error:B(null==T?void 0:null===(t=T.page_options)||void 0===t?void 0:null===(l=t.purposeDescription)||void 0===l?void 0:l.message),className:"mb-5",required:!0}),(0,r.jsxs)("div",{className:N()(k()(O)?"":"border-t pt-5 border-dashed border-border-200"),children:[null==O?void 0:O.map((e,o)=>{var i,l,t,n,s,a,d,c,m,f,g,x,h,y,_,j,N,Z,q,E,O,P,z,F,A,G,V,$;let K=(null==T?void 0:null===(s=T.page_options)||void 0===s?void 0:null===(n=s.purposeItems)||void 0===n?void 0:null===(t=n[o])||void 0===t?void 0:null===(l=t.icon)||void 0===l?void 0:null===(i=l.value)||void 0===i?void 0:i.message)||(null==T?void 0:null===(m=T.page_options)||void 0===m?void 0:null===(c=m.purposeItems)||void 0===c?void 0:null===(d=c[o])||void 0===d?void 0:null===(a=d.title)||void 0===a?void 0:a.message)||(null==T?void 0:null===(h=T.page_options)||void 0===h?void 0:null===(x=h.purposeItems)||void 0===x?void 0:null===(g=x[o])||void 0===g?void 0:null===(f=g.description)||void 0===f?void 0:f.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(R("page_options.purposeItems.".concat(o,".title")))?"".concat(B("form:text-business-purpose-item")," ").concat(o+1):R("page_options.purposeItems.".concat(o,".title")),defaultOpen:!0,isError:!!K,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"space-y-5 sm:col-span-4",children:[(0,r.jsx)(p.Z,{showLabel:!1,...S("page_options.purposeItems.".concat(o,".id")),variant:"outline",type:"hidden",value:(null==D?void 0:null===(y=D[o])||void 0===y?void 0:y.id)?null==D?void 0:null===(_=D[o])||void 0===_?void 0:_.id:o+1,disabled:!0}),(0,r.jsx)("div",{children:(0,r.jsx)(C.Z,{name:"page_options.purposeItems.".concat(o,".icon"),control:I,options:b,isClearable:!0,placeholder:B("form:input-label-select-icon"),label:B("form:input-label-select-icon"),error:B(null==T?void 0:null===(E=T.page_options)||void 0===E?void 0:null===(q=E.purposeItems)||void 0===q?void 0:null===(Z=q[o])||void 0===Z?void 0:null===(N=Z.icon)||void 0===N?void 0:null===(j=N.value)||void 0===j?void 0:j.message),required:!0})}),(0,r.jsx)(p.Z,{label:B("form:input-title"),variant:"outline",...S("page_options.purposeItems.".concat(o,".title")),error:B(null==T?void 0:null===(F=T.page_options)||void 0===F?void 0:null===(z=F.purposeItems)||void 0===z?void 0:null===(P=z[o])||void 0===P?void 0:null===(O=P.title)||void 0===O?void 0:O.message),required:!0}),(0,r.jsx)(v.Z,{label:B("form:input-description"),variant:"outline",...S("page_options.purposeItems.".concat(o,".description")),error:B(null==T?void 0:null===($=T.page_options)||void 0===$?void 0:null===(V=$.purposeItems)||void 0===V?void 0:null===(G=V[o])||void 0===G?void 0:null===(A=G.description)||void 0===A?void 0:A.message),required:!0})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(B("form:remove-item-confirmation"))&&L(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:B("form:button-label-remove")})})]})},e.id)}),(null==T?void 0:null===(s=T.page_options)||void 0===s?void 0:null===(n=s.purposeItems)||void 0===n?void 0:n.message)||(null==T?void 0:null===(g=T.page_options)||void 0===g?void 0:null===(m=g.purposeItems)||void 0===m?void 0:null===(a=m.root)||void 0===a?void 0:a.message)?(0,r.jsx)(_.Z,{message:B((null==T?void 0:null===(h=T.page_options)||void 0===h?void 0:null===(x=h.purposeItems)||void 0===x?void 0:x.message)||(null==T?void 0:null===(Z=T.page_options)||void 0===Z?void 0:null===(j=Z.purposeItems)||void 0===j?void 0:null===(y=j.root)||void 0===y?void 0:y.message)),variant:"error",className:"my-5"}):""]}),(0,r.jsx)(q.Z,{type:"button",onClick:()=>P({title:"",description:"",icon:{value:""}}),className:"w-full sm:w-auto",children:B("form:text-add-business-purpose")})]})]})};var T=i(60687);let FAQ=e=>{var o,i,l,t,n,s,a,m,g,b,x,h,y,j;let{register:N,control:Z,errors:S,watch:C}=e,{t:I}=(0,f.$G)(),{fields:R,append:D,remove:B}=(0,E.Dq)({control:Z,name:"page_options.faqItems"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:I("form:faq-title"),details:I("form:faq-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:I("form:input-title"),...N("page_options.faqTitle"),error:I(null==S?void 0:null===(i=S.page_options)||void 0===i?void 0:null===(o=i.faqTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:I("form:input-description"),variant:"outline",...N("page_options.faqDescription"),error:I(null==S?void 0:null===(t=S.page_options)||void 0===t?void 0:null===(l=t.faqDescription)||void 0===l?void 0:l.message),className:"mb-5",required:!0}),(0,r.jsxs)("div",{children:[null==R?void 0:R.map((e,o)=>{var i,l,t,n,s,a,d,c,m,v,f,g,b,x,h,y;let _=(null==S?void 0:null===(n=S.page_options)||void 0===n?void 0:null===(t=n.faqItems)||void 0===t?void 0:null===(l=t[o])||void 0===l?void 0:null===(i=l.title)||void 0===i?void 0:i.message)||(null==S?void 0:null===(c=S.page_options)||void 0===c?void 0:null===(d=c.faqItems)||void 0===d?void 0:null===(a=d[o])||void 0===a?void 0:null===(s=a.description)||void 0===s?void 0:s.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(C("page_options.faqItems.".concat(o,".title")))?"".concat(I("form:text-faq-item")," ").concat(o+1):C("page_options.faqItems.".concat(o,".title")),defaultOpen:!0,isError:!!_,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:col-span-4",children:[(0,r.jsx)(p.Z,{label:I("form:input-title"),variant:"outline",...N("page_options.faqItems.".concat(o,".title")),required:!0,error:I(null==S?void 0:null===(g=S.page_options)||void 0===g?void 0:null===(f=g.faqItems)||void 0===f?void 0:null===(v=f[o])||void 0===v?void 0:null===(m=v.title)||void 0===m?void 0:m.message)}),(0,r.jsx)(T.Z,{title:I("form:input-description"),control:Z,name:"page_options.faqItems.".concat(o,".description"),error:I(null==S?void 0:null===(y=S.page_options)||void 0===y?void 0:null===(h=y.faqItems)||void 0===h?void 0:null===(x=h[o])||void 0===x?void 0:null===(b=x.description)||void 0===b?void 0:b.message),editorClassName:"mb-0",required:!0})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(I("form:remove-item-confirmation"))&&B(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:I("form:button-label-remove")})})]})},e.id)}),(null==S?void 0:null===(s=S.page_options)||void 0===s?void 0:null===(n=s.faqItems)||void 0===n?void 0:n.message)||(null==S?void 0:null===(g=S.page_options)||void 0===g?void 0:null===(m=g.faqItems)||void 0===m?void 0:null===(a=m.root)||void 0===a?void 0:a.message)?(0,r.jsx)(_.Z,{message:I((null==S?void 0:null===(h=S.page_options)||void 0===h?void 0:null===(x=h.faqItems)||void 0===x?void 0:null===(b=x.root)||void 0===b?void 0:b.message)||(null==S?void 0:null===(j=S.page_options)||void 0===j?void 0:null===(y=j.faqItems)||void 0===y?void 0:y.message)),variant:"error",className:"my-5"}):""]}),(0,r.jsx)(q.Z,{type:"button",onClick:()=>D({title:"",description:""}),className:"w-full sm:w-auto",children:I("form:text-add-faqs")})]})]})};var R=i(77180);let Commission=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,j,Z;let{register:S,control:C,errors:I,max_fileSize:T,watch:D,isMultiCommissionRate:B,commissions:O}=e,{t:P}=(0,f.$G)(),{fields:L,append:z,remove:F}=(0,E.Dq)({control:C,name:"commissions"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:P("form:pricing-plan-title"),details:P("form:pricing-plan-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:P("form:input-title"),...S("page_options.commissionTitle"),error:P(null==I?void 0:null===(i=I.page_options)||void 0===i?void 0:null===(o=i.commissionTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:P("form:input-description"),variant:"outline",...S("page_options.commissionDescription"),error:P(null==I?void 0:null===(t=I.page_options)||void 0===t?void 0:null===(l=t.commissionDescription)||void 0===l?void 0:l.message),className:"mb-5",required:!0}),(0,r.jsx)(R.Z,{name:"page_options.isMultiCommissionRate",control:C,className:"hidden"}),B?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:N()(k()(L)?"":"border-t pt-5 border-dashed border-border-200"),children:null==L?void 0:L.map((e,o)=>{var i,l,t,n,s,a,d,c,f,g,b,x,h,y,_,j,N,Z,q,E,R,B,L,z,A,G,V,$,K,U,X,Q,W,J,ee,eo,ei,el,er,et,en,es,ea,ed,eu,ec;let em=(null==I?void 0:null===(t=I.commissions)||void 0===t?void 0:null===(l=t[o])||void 0===l?void 0:null===(i=l.image)||void 0===i?void 0:i.message)||(null==I?void 0:null===(a=I.commissions)||void 0===a?void 0:null===(s=a[o])||void 0===s?void 0:null===(n=s.level)||void 0===n?void 0:n.message)||(null==I?void 0:null===(f=I.commissions)||void 0===f?void 0:null===(c=f[o])||void 0===c?void 0:null===(d=c.sub_level)||void 0===d?void 0:d.message)||(null==I?void 0:null===(x=I.commissions)||void 0===x?void 0:null===(b=x[o])||void 0===b?void 0:null===(g=b.description)||void 0===g?void 0:g.message)||(null==I?void 0:null===(_=I.commissions)||void 0===_?void 0:null===(y=_[o])||void 0===y?void 0:null===(h=y.min_balance)||void 0===h?void 0:h.message)||(null==I?void 0:null===(Z=I.commissions)||void 0===Z?void 0:null===(N=Z[o])||void 0===N?void 0:null===(j=N.max_balance)||void 0===j?void 0:j.message)||(null==I?void 0:null===(R=I.commissions)||void 0===R?void 0:null===(E=R[o])||void 0===E?void 0:null===(q=E.commission)||void 0===q?void 0:q.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(D("commissions.".concat(o,".level")))?"".concat(P("form:text-commission-item")," ").concat(o+1):D("commissions.".concat(o,".level")),defaultOpen:!0,isError:!!em,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"space-y-5 sm:col-span-4",children:[(0,r.jsx)(p.Z,{showLabel:!1,...S("commissions.".concat(o,".id")),defaultValue:(null==O?void 0:null===(B=O[o])||void 0===B?void 0:B.id)?null==O?void 0:null===(L=O[o])||void 0===L?void 0:L.id:o+1,variant:"outline",type:"hidden",value:(null==O?void 0:null===(z=O[o])||void 0===z?void 0:z.id)?null==O?void 0:null===(A=O[o])||void 0===A?void 0:A.id:o+1,disabled:!0}),(0,r.jsx)(m.Z,{label:P("form:input-label-image"),name:"commissions.".concat(o,".image"),control:C,multiple:!1,maxSize:T,required:!0,error:P(null==I?void 0:null===($=I.commissions)||void 0===$?void 0:null===(V=$[o])||void 0===V?void 0:null===(G=V.image)||void 0===G?void 0:G.message),toolTipText:"".concat(P("form:upload-image-help-text")," ").concat(P("form:upload-image-help-text-dimension")," 80 x 80 ").concat(P("common:text-pixel-dot").toLowerCase()," ").concat(P("form:size-help-text")," ").concat(bytesToMegabytes(T)," MB")}),(0,r.jsx)(p.Z,{required:!0,label:P("form:input-title"),variant:"outline",...S("commissions.".concat(o,".level")),error:P(null==I?void 0:null===(X=I.commissions)||void 0===X?void 0:null===(U=X[o])||void 0===U?void 0:null===(K=U.level)||void 0===K?void 0:K.message)}),(0,r.jsx)(p.Z,{required:!0,label:P("form:input-label-subtitle"),variant:"outline",...S("commissions.".concat(o,".sub_level")),error:P(null==I?void 0:null===(J=I.commissions)||void 0===J?void 0:null===(W=J[o])||void 0===W?void 0:null===(Q=W.sub_level)||void 0===Q?void 0:Q.message)}),(0,r.jsx)(v.Z,{label:P("form:input-description"),required:!0,variant:"outline",...S("commissions.".concat(o,".description")),error:P(null==I?void 0:null===(ei=I.commissions)||void 0===ei?void 0:null===(eo=ei[o])||void 0===eo?void 0:null===(ee=eo.description)||void 0===ee?void 0:ee.message)}),(0,r.jsx)(p.Z,{type:"number",label:P("form:input-label-minimum-balance"),required:!0,variant:"outline",...S("commissions.".concat(o,".min_balance")),error:P(null==I?void 0:null===(et=I.commissions)||void 0===et?void 0:null===(er=et[o])||void 0===er?void 0:null===(el=er.min_balance)||void 0===el?void 0:el.message)}),(0,r.jsx)(p.Z,{type:"number",label:P("form:input-label-maximum-balance"),required:!0,variant:"outline",...S("commissions.".concat(o,".max_balance")),error:P(null==I?void 0:null===(ea=I.commissions)||void 0===ea?void 0:null===(es=ea[o])||void 0===es?void 0:null===(en=es.max_balance)||void 0===en?void 0:en.message)}),(0,r.jsx)(p.Z,{type:"number",label:P("form:input-label-commission"),required:!0,variant:"outline",...S("commissions.".concat(o,".commission")),error:P(null==I?void 0:null===(ec=I.commissions)||void 0===ec?void 0:null===(eu=ec[o])||void 0===eu?void 0:null===(ed=eu.commission)||void 0===ed?void 0:ed.message)})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(P("form:remove-item-confirmation"))&&F(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:P("form:button-label-remove")})})]})},e.id)})}),(null==I?void 0:null===(n=I.commissions)||void 0===n?void 0:n.message)||(null==I?void 0:null===(a=I.commissions)||void 0===a?void 0:null===(s=a.root)||void 0===s?void 0:s.message)?(0,r.jsx)(_.Z,{message:P((null==I?void 0:null===(g=I.commissions)||void 0===g?void 0:g.message)||(null==I?void 0:null===(x=I.commissions)||void 0===x?void 0:null===(b=x.root)||void 0===b?void 0:b.message)),variant:"error",className:"my-5"}):"",(0,r.jsx)(q.Z,{type:"button",onClick:()=>z({id:"",level:"",sub_level:"",description:"",min_balance:0,max_balance:0,commission:0,image:{id:"",original:"",thumbnail:""}}),className:"w-full sm:w-auto",children:P("form:text-add-commission")})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.Z,{label:P("form:label-default-commission-details"),variant:"outline",...S("page_options.defaultCommissionDetails"),error:P(null==I?void 0:null===(y=I.page_options)||void 0===y?void 0:null===(h=y.defaultCommissionDetails)||void 0===h?void 0:h.message),required:!0,className:"mb-5"}),(0,r.jsx)(p.Z,{label:P("form:label-default-commission-rate"),required:!0,variant:"outline",...S("page_options.defaultCommissionRate"),error:P(null==I?void 0:null===(Z=I.page_options)||void 0===Z?void 0:null===(j=Z.defaultCommissionRate)||void 0===j?void 0:j.message)})]})]})]})},StartSelling=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,j,Z;let{register:S,control:C,errors:I,max_fileSize:T,watch:R}=e,{t:D}=(0,f.$G)(),{fields:B,append:O,remove:P}=(0,E.Dq)({control:C,name:"page_options.sellingStepsItem"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:D("form:start-selling-title"),details:D("form:start-selling-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:D("form:input-title"),...S("page_options.sellingStepsTitle"),error:D(null==I?void 0:null===(i=I.page_options)||void 0===i?void 0:null===(o=i.sellingStepsTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:D("form:input-description"),variant:"outline",...S("page_options.sellingStepsDescription"),error:D(null==I?void 0:null===(t=I.page_options)||void 0===t?void 0:null===(l=t.sellingStepsDescription)||void 0===l?void 0:l.message),className:"mb-5",required:!0}),(0,r.jsxs)("div",{className:N()(k()(B)?"":"border-t pt-5 border-dashed border-border-200"),children:[null==B?void 0:B.map((e,o)=>{var i,l,t,n,s,a,d,c,f,g,b,x,h,y,_,j,N,Z,q,E,B,O,L,z;let F=(null==I?void 0:null===(n=I.page_options)||void 0===n?void 0:null===(t=n.sellingStepsItem)||void 0===t?void 0:null===(l=t[o])||void 0===l?void 0:null===(i=l.image)||void 0===i?void 0:i.message)||(null==I?void 0:null===(c=I.page_options)||void 0===c?void 0:null===(d=c.sellingStepsItem)||void 0===d?void 0:null===(a=d[o])||void 0===a?void 0:null===(s=a.title)||void 0===s?void 0:s.message)||(null==I?void 0:null===(x=I.page_options)||void 0===x?void 0:null===(b=x.sellingStepsItem)||void 0===b?void 0:null===(g=b[o])||void 0===g?void 0:null===(f=g.description)||void 0===f?void 0:f.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(R("page_options.sellingStepsItem.".concat(o,".title")))?"".concat(D("form:text-selling-steps")," ").concat(o+1):R("page_options.sellingStepsItem.".concat(o,".title")),defaultOpen:!0,isError:!!F,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"sm:col-span-4 space-y-5",children:[(0,r.jsx)(m.Z,{label:D("form:input-label-image"),name:"page_options.sellingStepsItem.".concat(o,".image"),control:C,multiple:!1,maxSize:T,required:!0,error:D(null==I?void 0:null===(j=I.page_options)||void 0===j?void 0:null===(_=j.sellingStepsItem)||void 0===_?void 0:null===(y=_[o])||void 0===y?void 0:null===(h=y.image)||void 0===h?void 0:h.message),toolTipText:"".concat(D("form:upload-image-help-text")," ").concat(D("form:upload-image-help-text-dimension")," 280 x 188 ").concat(D("common:pixel").toLowerCase()," ").concat(D("form:size-help-text")," ").concat(bytesToMegabytes(T)," MB")}),(0,r.jsx)(p.Z,{label:D("form:input-title"),variant:"outline",...S("page_options.sellingStepsItem.".concat(o,".title")),required:!0,error:D(null==I?void 0:null===(E=I.page_options)||void 0===E?void 0:null===(q=E.sellingStepsItem)||void 0===q?void 0:null===(Z=q[o])||void 0===Z?void 0:null===(N=Z.title)||void 0===N?void 0:N.message)}),(0,r.jsx)(v.Z,{label:D("form:input-description"),variant:"outline",...S("page_options.sellingStepsItem.".concat(o,".description")),required:!0,error:D(null==I?void 0:null===(z=I.page_options)||void 0===z?void 0:null===(L=z.sellingStepsItem)||void 0===L?void 0:null===(O=L[o])||void 0===O?void 0:null===(B=O.description)||void 0===B?void 0:B.message)})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(D("form:remove-item-confirmation"))&&P(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:D("form:button-label-remove")})})]})},e.id)}),(null==I?void 0:null===(s=I.page_options)||void 0===s?void 0:null===(n=s.sellingStepsItem)||void 0===n?void 0:n.message)||(null==I?void 0:null===(b=I.page_options)||void 0===b?void 0:null===(g=b.sellingStepsItem)||void 0===g?void 0:null===(a=g.root)||void 0===a?void 0:a.message)?(0,r.jsx)(_.Z,{message:D((null==I?void 0:null===(h=I.page_options)||void 0===h?void 0:null===(x=h.sellingStepsItem)||void 0===x?void 0:x.message)||(null==I?void 0:null===(Z=I.page_options)||void 0===Z?void 0:null===(j=Z.sellingStepsItem)||void 0===j?void 0:null===(y=j.root)||void 0===y?void 0:y.message)),variant:"error",className:"my-5"}):""]}),(0,r.jsx)(q.Z,{type:"button",onClick:()=>O({title:"",description:"",image:{thumbnail:"",original:"",id:""}}),className:"w-full sm:w-auto",children:D("form:text-add-sellng-steps")})]})]})};var D=i(46626),B=i(93075),O=i(82364),P=i(24750);let L=(0,y.createContext)(void 0),BackToTopProvider=e=>{let{children:o}=e,{x:i,y:l,strategy:t,update:n,refs:s}=(0,B.YF)({strategy:"fixed",placement:"left",middleware:[(0,O.cv)(30),(0,O.RR)(),(0,O.uY)()]});return(0,y.useEffect)(()=>{if(s.reference.current&&s.floating.current)return(0,P.Me)(s.reference.current,s.floating.current,n)},[s.reference,s.floating,n]),(0,r.jsx)(L.Provider,{value:{refs:s,x:i,y:l,strategy:t},children:o})},useBackToTop=()=>{let e=(0,y.useContext)(L);if(void 0===e)throw Error("useBackToTop must be used within a BackToTopProvider");return e},Slot=e=>{let{children:o,...i}=e;return y.isValidElement(o)?y.cloneElement(o,{...i,...o.props,style:{...i.style,...o.props.style},className:(0,S.m6)(i.className,o.props.className)}):(y.Children.count(o)>1&&y.Children.only(null),null)},z=y.forwardRef((e,o)=>{let{className:i,asChild:l=!1,offset:t=100,...n}=e,s=l?Slot:"button",[a,d]=(0,y.useState)(!1),handleVisibleButton=()=>{var e;d((null===(e=window)||void 0===e?void 0:e.scrollY)>t)};(0,y.useEffect)(()=>{window.addEventListener("scroll",handleVisibleButton)},[]);let{refs:c,strategy:m,x:p,y:v}=useBackToTop();return(0,r.jsx)("div",{ref:c.setFloating,style:{position:m,top:null!=v?v:"",left:null!=p?p:"",zIndex:1},children:(0,r.jsx)(s,{className:(0,S.m6)(N()("transition duration-300 transform",i,a?"opacity-100":"opacity-0")),ref:o,...n,onClick:()=>{window.scrollTo({left:0,top:0,behavior:"smooth"})}})})});z.displayName="BackToTopButton";var F=i(22220),A=i(88767),G=i(22920),V=i(47869),$=i(55191),K=i(3737);let U={...(0,$.h)(V.P.BECAME_SELLER),all(e){let{language:o}=e;return K.eN.get(V.P.BECAME_SELLER,{language:o})},update:e=>{let{...o}=e;return K.eN.post(V.P.BECAME_SELLER,{...o})}},useBecomeSellerQuery=e=>{let{language:o}=e,{data:i,error:l,isLoading:r}=(0,A.useQuery)([V.P.BECAME_SELLER,{language:o}],()=>U.all({language:o}));return{becomeSellerData:i,error:l,loading:r}},useUpdateBecomeSellerMutation=()=>{let{t:e}=(0,f.$G)(),o=(0,A.useQueryClient)();return(0,A.useMutation)(U.update,{onError:e=>{console.log(e)},onSuccess:async o=>{G.Am.success(e("common:successfully-updated"))},onSettled:()=>{o.invalidateQueries(V.P.BECAME_SELLER)}})};var X=i(47533),Q=i(11163);let DashboardShowcase=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,_,j,N,Z,S,q,C,I,k;let{register:E,control:T,errors:R,max_fileSize:D}=e,{t:B}=(0,f.$G)();return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:B("form:dashboard-showcase-title"),details:B("form:dashboard-showcase-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:B("form:input-title"),...E("page_options.dashboard.title"),error:B(null==R?void 0:null===(l=R.page_options)||void 0===l?void 0:null===(i=l.dashboard)||void 0===i?void 0:null===(o=i.title)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:B("form:input-description"),variant:"outline",...E("page_options.dashboard.description"),error:B(null==R?void 0:null===(s=R.page_options)||void 0===s?void 0:null===(n=s.dashboard)||void 0===n?void 0:null===(t=n.description)||void 0===t?void 0:t.message),className:"mb-5",required:!0}),(0,r.jsx)(p.Z,{label:B("form:text-primary-button-name"),...E("page_options.dashboard.buttonName"),error:B(null==R?void 0:null===(b=R.page_options)||void 0===b?void 0:null===(g=b.dashboard)||void 0===g?void 0:null===(a=g.buttonName)||void 0===a?void 0:a.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:B("form:text-primary-button-link"),...E("page_options.dashboard.buttonLink"),error:B(null==R?void 0:null===(y=R.page_options)||void 0===y?void 0:null===(h=y.dashboard)||void 0===h?void 0:null===(x=h.buttonLink)||void 0===x?void 0:x.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{label:B("form:text-secondary-button-name"),...E("page_options.dashboard.button2Name"),error:B(null==R?void 0:null===(N=R.page_options)||void 0===N?void 0:null===(j=N.dashboard)||void 0===j?void 0:null===(_=j.button2Name)||void 0===_?void 0:_.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:B("form:text-secondary-button-link"),...E("page_options.dashboard.button2Link"),error:B(null==R?void 0:null===(q=R.page_options)||void 0===q?void 0:null===(S=q.dashboard)||void 0===S?void 0:null===(Z=S.button2Link)||void 0===Z?void 0:Z.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(m.Z,{name:"page_options.dashboard.image",control:T,multiple:!1,maxSize:D,required:!0,label:"Image",error:B(null==R?void 0:null===(k=R.page_options)||void 0===k?void 0:null===(I=k.dashboard)||void 0===I?void 0:null===(C=I.image)||void 0===C?void 0:C.message),toolTipText:"".concat(B("form:upload-image-help-text")," ").concat(B("form:upload-image-help-text-dimension")," 813 x 555 ").concat(B("common:pixel").toLowerCase()," ").concat(B("form:size-help-text")," ").concat(bytesToMegabytes(D)," MB")})]})]})},Guideline=e=>{var o,i,l,t,n,s,a,m,g,b,x,h,y,j;let{register:Z,control:S,errors:C,watch:I}=e,{t:T}=(0,f.$G)(),{fields:R,append:D,remove:B}=(0,E.Dq)({control:S,name:"page_options.guidelineItems"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:T("form:guideline-title"),details:T("form:guideline-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:T("form:input-title"),...Z("page_options.guidelineTitle"),error:T(null==C?void 0:null===(i=C.page_options)||void 0===i?void 0:null===(o=i.guidelineTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:T("form:input-description"),variant:"outline",...Z("page_options.guidelineDescription"),error:T(null==C?void 0:null===(t=C.page_options)||void 0===t?void 0:null===(l=t.guidelineDescription)||void 0===l?void 0:l.message),className:"mb-5",required:!0}),(0,r.jsxs)("div",{className:N()(k()(R)?"":"border-t pt-5 border-dashed border-border-200"),children:[null==R?void 0:R.map((e,o)=>{var i,l,t,n,s,a,d,c,m,v,f,g,b,x,h,y;let _=(null==C?void 0:null===(n=C.page_options)||void 0===n?void 0:null===(t=n.guidelineItems)||void 0===t?void 0:null===(l=t[o])||void 0===l?void 0:null===(i=l.title)||void 0===i?void 0:i.message)||(null==C?void 0:null===(c=C.page_options)||void 0===c?void 0:null===(d=c.guidelineItems)||void 0===d?void 0:null===(a=d[o])||void 0===a?void 0:null===(s=a.link)||void 0===s?void 0:s.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(I("page_options.guidelineItems.".concat(o,".title")))?"".concat(T("form:text-guideline-item")," ").concat(o+1):I("page_options.guidelineItems.".concat(o,".title")),defaultOpen:!0,isError:!!_,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"space-y-5 sm:col-span-4",children:[(0,r.jsx)(p.Z,{label:T("form:input-title"),variant:"outline",...Z("page_options.guidelineItems.".concat(o,".title")),error:T(null==C?void 0:null===(g=C.page_options)||void 0===g?void 0:null===(f=g.guidelineItems)||void 0===f?void 0:null===(v=f[o])||void 0===v?void 0:null===(m=v.title)||void 0===m?void 0:m.message),required:!0}),(0,r.jsx)(p.Z,{type:"url",label:T("form:label-link"),variant:"outline",...Z("page_options.guidelineItems.".concat(o,".link")),error:T(null==C?void 0:null===(y=C.page_options)||void 0===y?void 0:null===(h=y.guidelineItems)||void 0===h?void 0:null===(x=h[o])||void 0===x?void 0:null===(b=x.link)||void 0===b?void 0:b.message)})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(T("form:remove-item-confirmation"))&&B(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:T("form:button-label-remove")})})]})},e.id)}),(null==C?void 0:null===(s=C.page_options)||void 0===s?void 0:null===(n=s.guidelineItems)||void 0===n?void 0:n.message)||(null==C?void 0:null===(g=C.page_options)||void 0===g?void 0:null===(m=g.guidelineItems)||void 0===m?void 0:null===(a=m.root)||void 0===a?void 0:a.message)?(0,r.jsx)(_.Z,{message:T((null==C?void 0:null===(x=C.page_options)||void 0===x?void 0:null===(b=x.guidelineItems)||void 0===b?void 0:b.message)||(null==C?void 0:null===(j=C.page_options)||void 0===j?void 0:null===(y=j.guidelineItems)||void 0===y?void 0:null===(h=y.root)||void 0===h?void 0:h.message)),variant:"error",className:"my-5"}):""]}),(0,r.jsx)(q.Z,{type:"button",onClick:()=>D({title:"",link:""}),className:"w-full sm:w-auto",children:T("form:text-add-guideline")})]})]})},SellerOpportunity=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,_,j,N,Z,S,q,C,I,k;let{register:E,control:T,errors:R,max_fileSize:D}=e,{t:B}=(0,f.$G)();return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:B("form:seller-opportunity-title"),details:B("form:seller-opportunity-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:B("form:input-title"),...E("page_options.sellerOpportunity.title"),error:B(null==R?void 0:null===(l=R.page_options)||void 0===l?void 0:null===(i=l.sellerOpportunity)||void 0===i?void 0:null===(o=i.title)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:B("form:input-description"),variant:"outline",...E("page_options.sellerOpportunity.description"),error:B(null==R?void 0:null===(s=R.page_options)||void 0===s?void 0:null===(n=s.sellerOpportunity)||void 0===n?void 0:null===(t=n.description)||void 0===t?void 0:t.message),className:"mb-5",required:!0}),(0,r.jsx)(p.Z,{label:B("form:text-primary-button-name"),...E("page_options.sellerOpportunity.buttonName"),error:B(null==R?void 0:null===(b=R.page_options)||void 0===b?void 0:null===(g=b.sellerOpportunity)||void 0===g?void 0:null===(a=g.buttonName)||void 0===a?void 0:a.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:B("form:text-primary-button-link"),...E("page_options.sellerOpportunity.buttonLink"),error:B(null==R?void 0:null===(y=R.page_options)||void 0===y?void 0:null===(h=y.sellerOpportunity)||void 0===h?void 0:null===(x=h.buttonLink)||void 0===x?void 0:x.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{label:B("form:text-secondary-button-name"),...E("page_options.sellerOpportunity.button2Name"),error:B(null==R?void 0:null===(N=R.page_options)||void 0===N?void 0:null===(j=N.sellerOpportunity)||void 0===j?void 0:null===(_=j.button2Name)||void 0===_?void 0:_.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(p.Z,{type:"url",label:B("form:text-secondary-button-link"),...E("page_options.sellerOpportunity.button2Link"),error:B(null==R?void 0:null===(q=R.page_options)||void 0===q?void 0:null===(S=q.sellerOpportunity)||void 0===S?void 0:null===(Z=S.button2Link)||void 0===Z?void 0:Z.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(m.Z,{name:"page_options.sellerOpportunity.image",control:T,multiple:!1,maxSize:D,required:!0,label:"Image",error:B(null==R?void 0:null===(k=R.page_options)||void 0===k?void 0:null===(I=k.sellerOpportunity)||void 0===I?void 0:null===(C=I.image)||void 0===C?void 0:C.message),toolTipText:"".concat(B("form:upload-image-help-text")," ").concat(B("form:upload-image-help-text-dimension")," 937 x 545 ").concat(B("common:pixel").toLowerCase()," ").concat(B("form:size-help-text")," ").concat(bytesToMegabytes(D)," MB")})]})]})},UserStory=e=>{var o,i,l,t,n,s,a,g,b,x,h,y,j,Z;let{register:S,control:C,errors:I,max_fileSize:T,watch:R}=e,{t:D}=(0,f.$G)(),{fields:B,append:O,remove:P}=(0,E.Dq)({control:C,name:"page_options.userStories"});return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:D("form:user-story-title"),details:D("form:user-story-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:D("form:input-title"),...S("page_options.userStoryTitle"),error:D(null==I?void 0:null===(i=I.page_options)||void 0===i?void 0:null===(o=i.userStoryTitle)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(v.Z,{label:D("form:input-description"),variant:"outline",...S("page_options.userStoryDescription"),error:D(null==I?void 0:null===(t=I.page_options)||void 0===t?void 0:null===(l=t.userStoryDescription)||void 0===l?void 0:l.message),className:"mb-5"}),(0,r.jsxs)("div",{className:N()(k()(B)?"":"border-t pt-5 border-dashed border-border-200"),children:[null==B?void 0:B.map((e,o)=>{var i,l,t,n,s,a,d,c,f,g,b,x,h,y,_,j,N,Z,q,E,B,O,L,z,F,A,G,V;let $=(null==I?void 0:null===(n=I.page_options)||void 0===n?void 0:null===(t=n.userStories)||void 0===t?void 0:null===(l=t[o])||void 0===l?void 0:null===(i=l.title)||void 0===i?void 0:i.message)||(null==I?void 0:null===(c=I.page_options)||void 0===c?void 0:null===(d=c.userStories)||void 0===d?void 0:null===(a=d[o])||void 0===a?void 0:null===(s=a.description)||void 0===s?void 0:s.message)||(null==I?void 0:null===(x=I.page_options)||void 0===x?void 0:null===(b=x.userStories)||void 0===b?void 0:null===(g=b[o])||void 0===g?void 0:null===(f=g.link)||void 0===f?void 0:f.message);return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:(0,r.jsxs)(accordion,{buttonTitle:k()(R("page_options.userStories.".concat(o,".title")))?"".concat(D("form:text-story-item")," ").concat(o+1):R("page_options.userStories.".concat(o,".title")),defaultOpen:!0,isError:!!$,className:"pt-5 grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:col-span-4",children:[(0,r.jsx)(p.Z,{label:D("form:input-title"),variant:"outline",...S("page_options.userStories.".concat(o,".title")),required:!0,error:D(null==I?void 0:null===(j=I.page_options)||void 0===j?void 0:null===(_=j.userStories)||void 0===_?void 0:null===(y=_[o])||void 0===y?void 0:null===(h=y.title)||void 0===h?void 0:h.message)}),(0,r.jsx)(v.Z,{label:D("form:input-description"),variant:"outline",...S("page_options.userStories.".concat(o,".description")),error:D(null==I?void 0:null===(E=I.page_options)||void 0===E?void 0:null===(q=E.userStories)||void 0===q?void 0:null===(Z=q[o])||void 0===Z?void 0:null===(N=Z.title)||void 0===N?void 0:N.message),required:!0}),(0,r.jsx)(p.Z,{type:"url",label:"".concat(D("form:text-video-link")," (Youtube/Vimeo)"),variant:"outline",...S("page_options.userStories.".concat(o,".link")),required:!0,error:D(null==I?void 0:null===(z=I.page_options)||void 0===z?void 0:null===(L=z.userStories)||void 0===L?void 0:null===(O=L[o])||void 0===O?void 0:null===(B=O.link)||void 0===B?void 0:B.message)}),(0,r.jsx)(m.Z,{label:D("form:text-video-thumbnail"),name:"page_options.userStories.".concat(o,".thumbnail"),control:C,multiple:!1,maxSize:T,error:D(null==I?void 0:null===(V=I.page_options)||void 0===V?void 0:null===(G=V.userStories)||void 0===G?void 0:null===(A=G[o])||void 0===A?void 0:null===(F=A.thumbnail)||void 0===F?void 0:F.message),toolTipText:"".concat(D("form:upload-image-help-text")," ").concat(D("form:upload-image-help-text-dimension")," 602 x 339 ").concat(D("common:text-pixel-dot").toLowerCase()," ").concat(D("form:size-help-text")," ").concat(bytesToMegabytes(T)," MB")})]}),(0,r.jsx)("div",{className:"sm:col-span-1 sm:mt-4 sm:text-right",children:(0,r.jsx)("button",{onClick:()=>{window.confirm(D("form:remove-item-confirmation"))&&P(o)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none",children:D("form:button-label-remove")})})]})},e.id)}),(null==I?void 0:null===(s=I.page_options)||void 0===s?void 0:null===(n=s.userStories)||void 0===n?void 0:n.message)||(null==I?void 0:null===(b=I.page_options)||void 0===b?void 0:null===(g=b.userStories)||void 0===g?void 0:null===(a=g.root)||void 0===a?void 0:a.message)?(0,r.jsx)(_.Z,{message:D((null==I?void 0:null===(h=I.page_options)||void 0===h?void 0:null===(x=h.userStories)||void 0===x?void 0:x.message)||(null==I?void 0:null===(Z=I.page_options)||void 0===Z?void 0:null===(j=Z.userStories)||void 0===j?void 0:null===(y=j.root)||void 0===y?void 0:y.message)),variant:"error",className:"my-5"}):""]}),(0,r.jsx)(q.Z,{type:"button",onClick:()=>O({title:"",description:"",link:"",thumbnail:""}),className:"w-full sm:w-auto",children:D("form:text-add-user-story")})]})]})},Contact=e=>{var o,i,l,t,n,s;let{register:a,errors:m}=e,{t:g}=(0,f.$G)();return(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(c.Z,{title:g("form:contact-title"),details:g("form:contact-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(d.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(p.Z,{label:g("form:input-title"),...a("page_options.contact.title"),error:g(null==m?void 0:null===(l=m.page_options)||void 0===l?void 0:null===(i=l.contact)||void 0===i?void 0:null===(o=i.title)||void 0===o?void 0:o.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsx)(v.Z,{label:g("form:input-description"),variant:"outline",...a("page_options.contact.description"),error:g(null==m?void 0:null===(s=m.page_options)||void 0===s?void 0:null===(n=s.contact)||void 0===n?void 0:null===(t=n.description)||void 0===t?void 0:t.message),className:"mb-5",required:!0})]})]})};function BecomeSellerInfoForm(e){var o,i,l,t,n;let{becomeSellerData:s,settings:d}=e,{t:c}=(0,f.$G)(),{locale:m,reload:p}=(0,Q.useRouter)(),{mutate:v,isLoading:g}=useUpdateBecomeSellerMutation(),{page_options:x,commissions:h}=null!=s?s:{},{options:_}=null!=d?d:{},j=(null==_?void 0:null===(o=_.server_info)||void 0===o?void 0:o.upload_max_filesize)*1024,N=(0,A.useQueryClient)(),{register:Z,handleSubmit:S,control:C,watch:I,setValue:k,getValues:T,formState:{errors:R,isDirty:B}}=(0,E.cI)({shouldUnregister:!0,resolver:(0,X.X)(a),...s?{defaultValues:{page_options:{...null==x?void 0:x.page_options,purposeItems:(null==x?void 0:null===(i=x.page_options)||void 0===i?void 0:i.purposeItems)?null==x?void 0:null===(t=x.page_options)||void 0===t?void 0:null===(l=t.purposeItems)||void 0===l?void 0:l.map(e=>({description:null==e?void 0:e.description,title:null==e?void 0:e.title,icon:null==b?void 0:b.find(o=>{var i;return(null==o?void 0:o.value)===(null==e?void 0:null===(i=e.icon)||void 0===i?void 0:i.value)})})):[]},commissions:h}}:{}});async function onSubmit(e){var o,i;v({language:m,...e,page_options:{...e.page_options,purposeItems:null==e?void 0:null===(i=e.page_options)||void 0===i?void 0:null===(o=i.purposeItems)||void 0===o?void 0:o.map(e=>{var o;return{description:null==e?void 0:e.description,title:null==e?void 0:e.title,icon:{value:null==e?void 0:null===(o=e.icon)||void 0===o?void 0:o.value}}})}},{onSuccess:()=>{N.invalidateQueries([V.P.BECAME_SELLER,{language:m}])}})}let{refs:O}=useBackToTop();return(0,y.useEffect)(()=>{k("page_options.isMultiCommissionRate",!!(null==_?void 0:_.isMultiCommissionRate))},[null==_?void 0:_.isMultiCommissionRate]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("form",{onSubmit:S(onSubmit),children:[(0,r.jsx)(Banner,{register:Z,control:C,errors:R,max_fileSize:j}),(0,r.jsx)(StartSelling,{register:Z,control:C,errors:R,max_fileSize:j,watch:I}),(0,r.jsx)(UserStory,{register:Z,control:C,errors:R,max_fileSize:j,watch:I}),(0,r.jsx)(BusinessPurpose,{register:Z,control:C,errors:R,watch:I,purposeItems:null==x?void 0:null===(n=x.page_options)||void 0===n?void 0:n.purposeItems}),(0,r.jsx)(Commission,{register:Z,control:C,errors:R,max_fileSize:j,watch:I,isMultiCommissionRate:null==_?void 0:_.isMultiCommissionRate,commissions:h}),(0,r.jsx)(DashboardShowcase,{register:Z,control:C,errors:R,max_fileSize:j}),(0,r.jsx)(Guideline,{register:Z,control:C,errors:R,watch:I}),(0,r.jsx)(FAQ,{register:Z,control:C,errors:R,watch:I}),(0,r.jsx)(Contact,{register:Z,errors:R,control:C}),(0,r.jsx)(SellerOpportunity,{register:Z,control:C,errors:R,max_fileSize:j}),(0,r.jsx)(F.Z,{className:"z-0",children:(0,r.jsx)(q.Z,{loading:g,disabled:g,className:"text-sm md:text-base",ref:O.setReference,children:c("form:text-save-seller-information")})})]}),(0,r.jsx)(z,{asChild:!0,className:"shadow-md",children:(0,r.jsx)(q.Z,{className:"text-sm md:text-base",children:(0,r.jsx)(D.a,{})})})]})}var W=i(97670),J=i(45957),ee=i(55846),eo=i(90573),ei=i(16203),el=!0;function BecomeSeller(){let{t:e}=(0,f.$G)(),{locale:o}=(0,Q.useRouter)(),{becomeSellerData:i,loading:l,error:t}=useBecomeSellerQuery({language:o}),{settings:n,loading:s}=(0,eo.n)({language:o});return l||s?(0,r.jsx)(ee.Z,{text:e("common:text-loading")}):t?(0,r.jsx)(J.Z,{message:t.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Z,{className:"mb-8 flex flex-col items-center xl:flex-row",children:(0,r.jsx)("div",{className:"mb-4 md:w-1/4 xl:mb-0",children:(0,r.jsx)("h1",{className:"before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 xl:before:w-1",children:e("form:become-seller-form-title")})})}),(0,r.jsx)(BackToTopProvider,{children:(0,r.jsx)(BecomeSellerInfoForm,{becomeSellerData:i,settings:n})})]})}BecomeSeller.authenticate={permissions:ei.M$},BecomeSeller.Layout=W.default},23157:function(e,o,i){"use strict";i.d(o,{ZP:function(){return s}});var l=i(65342),r=i(87462),t=i(67294),n=i(76416);i(48711),i(73935),i(73469);var s=(0,t.forwardRef)(function(e,o){var i=(0,l.u)(e);return t.createElement(n.S,(0,r.Z)({ref:o},i))})},97326:function(e,o,i){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}i.d(o,{Z:function(){return _assertThisInitialized}})},15671:function(e,o,i){"use strict";function _classCallCheck(e,o){if(!(e instanceof o))throw TypeError("Cannot call a class as a function")}i.d(o,{Z:function(){return _classCallCheck}})},43144:function(e,o,i){"use strict";i.d(o,{Z:function(){return _createClass}});var l=i(83997);function _defineProperties(e,o){for(var i=0;i<o.length;i++){var r=o[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(0,l.Z)(r.key),r)}}function _createClass(e,o,i){return o&&_defineProperties(e.prototype,o),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,o,i){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}i.d(o,{Z:function(){return _createSuper}});var l=i(71002),r=i(97326);function _createSuper(e){var o=_isNativeReflectConstruct();return function(){var i,t=_getPrototypeOf(e);if(o){var n=_getPrototypeOf(this).constructor;i=Reflect.construct(t,arguments,n)}else i=t.apply(this,arguments);return function(e,o){if(o&&("object"==(0,l.Z)(o)||"function"==typeof o))return o;if(void 0!==o)throw TypeError("Derived constructors may only return object or undefined");return(0,r.Z)(e)}(this,i)}}},60136:function(e,o,i){"use strict";i.d(o,{Z:function(){return _inherits}});var l=i(89611);function _inherits(e,o){if("function"!=typeof o&&null!==o)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(o&&o.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),o&&(0,l.Z)(e,o)}},1413:function(e,o,i){"use strict";i.d(o,{Z:function(){return _objectSpread2}});var l=i(4942);function ownKeys(e,o){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);o&&(l=l.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),i.push.apply(i,l)}return i}function _objectSpread2(e){for(var o=1;o<arguments.length;o++){var i=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(i),!0).forEach(function(o){(0,l.Z)(e,o,i[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(i,o))})}return e}},28368:function(e,o,i){"use strict";i.d(o,{p:function(){return C}});var l,r,t,n=i(67294),s=i(32984),a=i(12351),d=i(23784),c=i(19946),m=i(61363),p=i(64103),v=i(16567),f=i(14157),g=i(15466),b=i(73781);let x=null!=(t=n.startTransition)?t:function(e){e()};var h=((l=h||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l),y=((r=y||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let _={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,o)=>e.buttonId===o.buttonId?e:{...e,buttonId:o.buttonId},3:(e,o)=>e.panelId===o.panelId?e:{...e,panelId:o.panelId}},j=(0,n.createContext)(null);function M(e){let o=(0,n.useContext)(j);if(null===o){let o=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,M),o}return o}j.displayName="DisclosureContext";let N=(0,n.createContext)(null);N.displayName="DisclosureAPIContext";let Z=(0,n.createContext)(null);function Y(e,o){return(0,s.E)(o.type,_,e,o)}Z.displayName="DisclosurePanelContext";let S=n.Fragment,q=a.AN.RenderStrategy|a.AN.Static,C=Object.assign((0,a.yV)(function(e,o){let{defaultOpen:i=!1,...l}=e,r=(0,n.useRef)(null),t=(0,d.T)(o,(0,d.h)(e=>{r.current=e},void 0===e.as||e.as===n.Fragment)),c=(0,n.useRef)(null),m=(0,n.useRef)(null),p=(0,n.useReducer)(Y,{disclosureState:i?0:1,linkedPanel:!1,buttonRef:m,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:f,buttonId:x},h]=p,y=(0,b.z)(e=>{h({type:1});let o=(0,g.r)(r);if(!o||!x)return;let i=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:o.getElementById(x):o.getElementById(x);null==i||i.focus()}),_=(0,n.useMemo)(()=>({close:y}),[y]),Z=(0,n.useMemo)(()=>({open:0===f,close:y}),[f,y]);return n.createElement(j.Provider,{value:p},n.createElement(N.Provider,{value:_},n.createElement(v.up,{value:(0,s.E)(f,{0:v.ZM.Open,1:v.ZM.Closed})},(0,a.sY)({ourProps:{ref:t},theirProps:l,slot:Z,defaultTag:S,name:"Disclosure"}))))}),{Button:(0,a.yV)(function(e,o){let i=(0,c.M)(),{id:l=`headlessui-disclosure-button-${i}`,...r}=e,[t,s]=M("Disclosure.Button"),v=(0,n.useContext)(Z),g=null!==v&&v===t.panelId,x=(0,n.useRef)(null),h=(0,d.T)(x,o,g?null:t.buttonRef);(0,n.useEffect)(()=>{if(!g)return s({type:2,buttonId:l}),()=>{s({type:2,buttonId:null})}},[l,s,g]);let y=(0,b.z)(e=>{var o;if(g){if(1===t.disclosureState)return;switch(e.key){case m.R.Space:case m.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(o=t.buttonRef.current)||o.focus()}}else switch(e.key){case m.R.Space:case m.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),_=(0,b.z)(e=>{e.key===m.R.Space&&e.preventDefault()}),j=(0,b.z)(o=>{var i;(0,p.P)(o.currentTarget)||e.disabled||(g?(s({type:0}),null==(i=t.buttonRef.current)||i.focus()):s({type:0}))}),N=(0,n.useMemo)(()=>({open:0===t.disclosureState}),[t]),S=(0,f.f)(e,x),q=g?{ref:h,type:S,onKeyDown:y,onClick:j}:{ref:h,id:l,type:S,"aria-expanded":0===t.disclosureState,"aria-controls":t.linkedPanel?t.panelId:void 0,onKeyDown:y,onKeyUp:_,onClick:j};return(0,a.sY)({ourProps:q,theirProps:r,slot:N,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,a.yV)(function(e,o){let i=(0,c.M)(),{id:l=`headlessui-disclosure-panel-${i}`,...r}=e,[t,s]=M("Disclosure.Panel"),{close:m}=function w(e){let o=(0,n.useContext)(N);if(null===o){let o=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,w),o}return o}("Disclosure.Panel"),p=(0,d.T)(o,t.panelRef,e=>{x(()=>s({type:e?4:5}))});(0,n.useEffect)(()=>(s({type:3,panelId:l}),()=>{s({type:3,panelId:null})}),[l,s]);let f=(0,v.oJ)(),g=null!==f?(f&v.ZM.Open)===v.ZM.Open:0===t.disclosureState,b=(0,n.useMemo)(()=>({open:0===t.disclosureState,close:m}),[t,m]);return n.createElement(Z.Provider,{value:t.panelId},(0,a.sY)({ourProps:{ref:p,id:l},theirProps:r,slot:b,defaultTag:"div",features:q,visible:g,name:"Disclosure.Panel"}))})})},95389:function(e,o,i){"use strict";i.d(o,{_:function(){return c},b:function(){return H}});var l=i(67294),r=i(19946),t=i(12351),n=i(16723),s=i(23784),a=i(73781);let d=(0,l.createContext)(null);function H(){let[e,o]=(0,l.useState)([]);return[e.length>0?e.join(" "):void 0,(0,l.useMemo)(()=>function(e){let i=(0,a.z)(e=>(o(o=>[...o,e]),()=>o(o=>{let i=o.slice(),l=i.indexOf(e);return -1!==l&&i.splice(l,1),i}))),r=(0,l.useMemo)(()=>({register:i,slot:e.slot,name:e.name,props:e.props}),[i,e.slot,e.name,e.props]);return l.createElement(d.Provider,{value:r},e.children)},[o])]}let c=Object.assign((0,t.yV)(function(e,o){let i=(0,r.M)(),{id:a=`headlessui-label-${i}`,passive:c=!1,...m}=e,p=function u(){let e=(0,l.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),v=(0,s.T)(o);(0,n.e)(()=>p.register(a),[a,p.register]);let f={ref:v,...p.props,id:a};return c&&("onClick"in f&&(delete f.htmlFor,delete f.onClick),"onClick"in m&&delete m.onClick),(0,t.sY)({ourProps:f,theirProps:m,slot:p.slot||{},defaultTag:"label",name:p.name||"Label"})}),{})},77768:function(e,o,i){"use strict";i.d(o,{r:function(){return y}});var l=i(67294),r=i(12351),t=i(19946),n=i(61363),s=i(64103),a=i(95389),d=i(39516),c=i(14157),m=i(23784),p=i(46045),v=i(18689),f=i(73781),g=i(31147),b=i(94192);let x=(0,l.createContext)(null);x.displayName="GroupContext";let h=l.Fragment,y=Object.assign((0,r.yV)(function(e,o){let i=(0,t.M)(),{id:a=`headlessui-switch-${i}`,checked:d,defaultChecked:h=!1,onChange:y,name:_,value:j,form:N,...Z}=e,S=(0,l.useContext)(x),q=(0,l.useRef)(null),C=(0,m.T)(q,o,null===S?null:S.setSwitch),[I,k]=(0,g.q)(d,y,h),E=(0,f.z)(()=>null==k?void 0:k(!I)),T=(0,f.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),E()}),R=(0,f.z)(e=>{e.key===n.R.Space?(e.preventDefault(),E()):e.key===n.R.Enter&&(0,v.g)(e.currentTarget)}),D=(0,f.z)(e=>e.preventDefault()),B=(0,l.useMemo)(()=>({checked:I}),[I]),O={id:a,ref:C,role:"switch",type:(0,c.f)(e,q),tabIndex:0,"aria-checked":I,"aria-labelledby":null==S?void 0:S.labelledby,"aria-describedby":null==S?void 0:S.describedby,onClick:T,onKeyUp:R,onKeyPress:D},P=(0,b.G)();return(0,l.useEffect)(()=>{var e;let o=null==(e=q.current)?void 0:e.closest("form");o&&void 0!==h&&P.addEventListener(o,"reset",()=>{k(h)})},[q,k]),l.createElement(l.Fragment,null,null!=_&&I&&l.createElement(p._,{features:p.A.Hidden,...(0,r.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:N,checked:I,name:_,value:j})}),(0,r.sY)({ourProps:O,theirProps:Z,slot:B,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var o;let[i,t]=(0,l.useState)(null),[n,s]=(0,a.b)(),[c,m]=(0,d.f)(),p=(0,l.useMemo)(()=>({switch:i,setSwitch:t,labelledby:n,describedby:c}),[i,t,n,c]);return l.createElement(m,{name:"Switch.Description"},l.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(o=p.switch)?void 0:o.id,onClick(e){i&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),i.click(),i.focus({preventScroll:!0}))}}},l.createElement(x.Provider,{value:p},(0,r.sY)({ourProps:{},theirProps:e,defaultTag:h,name:"Switch.Group"}))))},Label:a._,Description:d.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,9494,5535,8186,1285,1631,5468,9774,2888,179],function(){return e(e.s=29314)}),_N_E=e.O()}]);