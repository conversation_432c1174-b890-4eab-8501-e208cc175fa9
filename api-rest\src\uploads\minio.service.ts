import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';
import { v4 as uuidv4 } from 'uuid';

export interface UploadedFile {
  id: string;
  original: string;
  thumbnail: string;
  file_name: string;
  size: number;
  mime_type: string;
}

@Injectable()
export class MinioService implements OnModuleInit {
  private readonly logger = new Logger(MinioService.name);
  private minioClient: Minio.Client;
  private bucketName: string;
  private endPoint: string;
  private port: number;
  private useSSL: boolean;

  constructor(private configService: ConfigService) {
    const minioConfig = this.configService.get('minio');
    this.endPoint = minioConfig.endPoint;
    this.port = minioConfig.port;
    this.useSSL = minioConfig.useSSL;
    this.bucketName = minioConfig.bucketName;

    this.minioClient = new Minio.Client({
      endPoint: this.endPoint,
      port: this.port,
      useSSL: this.useSSL,
      accessKey: minioConfig.accessKey,
      secretKey: minioConfig.secretKey,
    });
  }

  async onModuleInit() {
    await this.ensureBucketExists();
  }

  private async ensureBucketExists() {
    try {
      const bucketExists = await this.minioClient.bucketExists(this.bucketName);
      if (!bucketExists) {
        await this.minioClient.makeBucket(this.bucketName, 'us-east-1');
        this.logger.log(`Bucket ${this.bucketName} created successfully`);
        
        // Set bucket policy to allow public read access
        const policy = {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: { AWS: ['*'] },
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${this.bucketName}/*`],
            },
          ],
        };
        
        await this.minioClient.setBucketPolicy(this.bucketName, JSON.stringify(policy));
        this.logger.log(`Bucket policy set for ${this.bucketName}`);
      } else {
        this.logger.log(`Bucket ${this.bucketName} already exists`);
      }
    } catch (error) {
      this.logger.error(`Error ensuring bucket exists: ${error.message}`);
    }
  }

  async uploadFile(file: Express.Multer.File): Promise<UploadedFile> {
    const fileId = uuidv4();
    const fileExtension = file.originalname.split('.').pop();
    const fileName = `${fileId}.${fileExtension}`;
    const filePath = `uploads/${fileName}`;

    try {
      // Upload the file
      await this.minioClient.putObject(
        this.bucketName,
        filePath,
        file.buffer,
        file.size,
        {
          'Content-Type': file.mimetype,
          'Content-Disposition': `inline; filename="${file.originalname}"`,
        }
      );

      // Generate URLs
      const protocol = this.useSSL ? 'https' : 'http';
      const portSuffix = (this.port === 80 && !this.useSSL) || (this.port === 443 && this.useSSL) ? '' : `:${this.port}`;
      const baseUrl = `${protocol}://${this.endPoint}${portSuffix}/${this.bucketName}`;
      
      const originalUrl = `${baseUrl}/${filePath}`;
      
      // For now, use the same URL for thumbnail. In a real implementation,
      // you might want to generate actual thumbnails for images
      const thumbnailUrl = file.mimetype.startsWith('image/') 
        ? originalUrl 
        : `${baseUrl}/uploads/file-icon.png`; // You could add a default file icon

      const uploadedFile: UploadedFile = {
        id: fileId,
        original: originalUrl,
        thumbnail: thumbnailUrl,
        file_name: file.originalname,
        size: file.size,
        mime_type: file.mimetype,
      };

      this.logger.log(`File uploaded successfully: ${fileName}`);
      return uploadedFile;
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  async uploadFiles(files: Express.Multer.File[]): Promise<UploadedFile[]> {
    const uploadPromises = files.map(file => this.uploadFile(file));
    return Promise.all(uploadPromises);
  }

  async deleteFile(fileName: string): Promise<void> {
    try {
      await this.minioClient.removeObject(this.bucketName, `uploads/${fileName}`);
      this.logger.log(`File deleted successfully: ${fileName}`);
    } catch (error) {
      this.logger.error(`Error deleting file: ${error.message}`);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  async getFileUrl(fileName: string): Promise<string> {
    try {
      // Generate a presigned URL valid for 7 days
      const url = await this.minioClient.presignedGetObject(this.bucketName, `uploads/${fileName}`, 7 * 24 * 60 * 60);
      return url;
    } catch (error) {
      this.logger.error(`Error generating file URL: ${error.message}`);
      throw new Error(`Failed to generate file URL: ${error.message}`);
    }
  }

  getPublicUrl(fileName: string): string {
    const protocol = this.useSSL ? 'https' : 'http';
    const portSuffix = (this.port === 80 && !this.useSSL) || (this.port === 443 && this.useSSL) ? '' : `:${this.port}`;
    return `${protocol}://${this.endPoint}${portSuffix}/${this.bucketName}/uploads/${fileName}`;
  }
}
