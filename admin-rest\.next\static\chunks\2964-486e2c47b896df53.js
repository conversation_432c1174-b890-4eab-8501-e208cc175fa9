"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2964],{16713:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=r(67294);t.default=function(e){n.useEffect(e,[])}},79050:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=r(97582),o=r(67294),a=n.__importDefault(r(86258));t.default=function(e){var t=o.useRef(0),r=o.useState(e),n=r[0],i=r[1],c=o.useCallback(function(e){cancelAnimationFrame(t.current),t.current=requestAnimationFrame(function(){i(e)})},[]);return a.default(function(){cancelAnimationFrame(t.current)}),[n,c]}},86258:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=r(97582),o=r(67294),a=n.__importDefault(r(16713));t.default=function(e){var t=o.useRef(e);t.current=e,a.default(function(){return function(){return t.current()}})}},62964:function(e,t,r){var n=r(97582),o=r(67294),a=n.__importDefault(r(79050)),i=r(88387);t.Z=function(e,t){void 0===e&&(e=1/0),void 0===t&&(t=1/0);var r=a.default({width:i.isBrowser?window.innerWidth:e,height:i.isBrowser?window.innerHeight:t}),n=r[0],c=r[1];return o.useEffect(function(){if(i.isBrowser){var handler_1=function(){c({width:window.innerWidth,height:window.innerHeight})};return i.on(window,"resize",handler_1),function(){i.off(window,"resize",handler_1)}}},[]),n}},97582:function(e,t,r){r.r(t),r.d(t,{__addDisposableResource:function(){return __addDisposableResource},__assign:function(){return __assign},__asyncDelegator:function(){return __asyncDelegator},__asyncGenerator:function(){return __asyncGenerator},__asyncValues:function(){return __asyncValues},__await:function(){return __await},__awaiter:function(){return __awaiter},__classPrivateFieldGet:function(){return __classPrivateFieldGet},__classPrivateFieldIn:function(){return __classPrivateFieldIn},__classPrivateFieldSet:function(){return __classPrivateFieldSet},__createBinding:function(){return n},__decorate:function(){return __decorate},__disposeResources:function(){return __disposeResources},__esDecorate:function(){return __esDecorate},__exportStar:function(){return __exportStar},__extends:function(){return __extends},__generator:function(){return __generator},__importDefault:function(){return __importDefault},__importStar:function(){return __importStar},__makeTemplateObject:function(){return __makeTemplateObject},__metadata:function(){return __metadata},__param:function(){return __param},__propKey:function(){return __propKey},__read:function(){return __read},__rest:function(){return __rest},__rewriteRelativeImportExtension:function(){return __rewriteRelativeImportExtension},__runInitializers:function(){return __runInitializers},__setFunctionName:function(){return __setFunctionName},__spread:function(){return __spread},__spreadArray:function(){return __spreadArray},__spreadArrays:function(){return __spreadArrays},__values:function(){return __values}});var extendStatics=function(e,t){return(extendStatics=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function __extends(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}extendStatics(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}var __assign=function(){return(__assign=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function __rest(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function __decorate(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}function __param(e,t){return function(r,n){t(r,n,e)}}function __esDecorate(e,t,r,n,o,a){function accept(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var i,c=n.kind,u="getter"===c?"get":"setter"===c?"set":"value",s=!t&&e?n.static?e:e.prototype:null,f=t||(s?Object.getOwnPropertyDescriptor(s,n.name):{}),l=!1,_=r.length-1;_>=0;_--){var p={};for(var d in n)p[d]="access"===d?{}:n[d];for(var d in n.access)p.access[d]=n.access[d];p.addInitializer=function(e){if(l)throw TypeError("Cannot add initializers after decoration has completed");a.push(accept(e||null))};var y=(0,r[_])("accessor"===c?{get:f.get,set:f.set}:f[u],p);if("accessor"===c){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw TypeError("Object expected");(i=accept(y.get))&&(f.get=i),(i=accept(y.set))&&(f.set=i),(i=accept(y.init))&&o.unshift(i)}else(i=accept(y))&&("field"===c?o.unshift(i):f[u]=i)}s&&Object.defineProperty(s,n.name,f),l=!0}function __runInitializers(e,t,r){for(var n=arguments.length>2,o=0;o<t.length;o++)r=n?t[o].call(e,r):t[o].call(e);return n?r:void 0}function __propKey(e){return"symbol"==typeof e?e:"".concat(e)}function __setFunctionName(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}function __metadata(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function __awaiter(e,t,r,n){return new(r||(r=Promise))(function(o,a){function fulfilled(e){try{step(n.next(e))}catch(e){a(e)}}function rejected(e){try{step(n.throw(e))}catch(e){a(e)}}function step(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(fulfilled,rejected)}step((n=n.apply(e,t||[])).next())})}function __generator(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=verb(0),i.throw=verb(1),i.return=verb(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function verb(c){return function(u){return function(c){if(r)throw TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&c[0]?n.return:c[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,c[1])).done)return o;switch(n=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,n=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===c[0]||2===c[0])){a=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){a.label=c[1];break}if(6===c[0]&&a.label<o[1]){a.label=o[1],o=c;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(c);break}o[2]&&a.ops.pop(),a.trys.pop();continue}c=t.call(e,a)}catch(e){c=[6,e],n=0}finally{r=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}var n=Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]};function __exportStar(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)}function __values(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i}function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}function __spreadArrays(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),o=0,t=0;t<r;t++)for(var a=arguments[t],i=0,c=a.length;i<c;i++,o++)n[o]=a[i];return n}function __spreadArray(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}function __asyncGenerator(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),a=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),verb("next"),verb("throw"),verb("return",function(e){return function(t){return Promise.resolve(t).then(e,reject)}}),n[Symbol.asyncIterator]=function(){return this},n;function verb(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){a.push([e,t,r,n])>1||resume(e,t)})},t&&(n[e]=t(n[e])))}function resume(e,t){try{var r;(r=o[e](t)).value instanceof __await?Promise.resolve(r.value.v).then(fulfill,reject):settle(a[0][2],r)}catch(e){settle(a[0][3],e)}}function fulfill(e){resume("next",e)}function reject(e){resume("throw",e)}function settle(e,t){e(t),a.shift(),a.length&&resume(a[0][0],a[0][1])}}function __asyncDelegator(e){var t,r;return t={},verb("next"),verb("throw",function(e){throw e}),verb("return"),t[Symbol.iterator]=function(){return this},t;function verb(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:__await(e[n](t)),done:!1}:o?o(t):t}:o}}function __asyncValues(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=__values(e),t={},verb("next"),verb("throw"),verb("return"),t[Symbol.asyncIterator]=function(){return this},t);function verb(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){!function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)}(n,o,(t=e[r](t)).done,t.value)})}}}function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var o=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},ownKeys=function(e){return(ownKeys=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)};function __importStar(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=ownKeys(e),a=0;a<r.length;a++)"default"!==r[a]&&n(t,e,r[a]);return o(t,e),t}function __importDefault(e){return e&&e.__esModule?e:{default:e}}function __classPrivateFieldGet(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function __classPrivateFieldSet(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r}function __classPrivateFieldIn(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function __addDisposableResource(e,t,r){if(null!=t){var n,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(o=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");o&&(n=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var a="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};function __disposeResources(e){function fail(t){e.error=e.hasError?new a(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var t,r=0;return function next(){for(;t=e.stack.pop();)try{if(!t.async&&1===r)return r=0,e.stack.push(t),Promise.resolve().then(next);if(t.dispose){var n=t.dispose.call(t.value);if(t.async)return r|=2,Promise.resolve(n).then(next,function(e){return fail(e),next()})}else r|=1}catch(e){fail(e)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function __rewriteRelativeImportExtension(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,r,n,o,a){return r?t?".jsx":".js":!n||o&&a?n+o+"."+a.toLowerCase()+"js":e}):e}t.default={__extends,__assign,__rest,__decorate,__param,__esDecorate,__runInitializers,__propKey,__setFunctionName,__metadata,__awaiter,__generator,__createBinding:n,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources,__rewriteRelativeImportExtension}}}]);