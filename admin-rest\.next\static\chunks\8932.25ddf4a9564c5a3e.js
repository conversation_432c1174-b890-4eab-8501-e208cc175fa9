"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8932],{8932:function(e,t,o){o.r(t);var u=o(85893),n=o(71421),r=o(75814),s=o(9140),a=o(26654);t.default=()=>{let{mutate:e,isLoading:t}=(0,a.KU)(),{data:o}=(0,r.X9)(),{closeModal:i}=(0,r.SO)();return(0,u.jsx)(n.Z,{onCancel:i,onDelete:function(){try{e({id:o}),i()}catch(e){i(),(0,s.e)(e)}},deleteBtnLoading:t})}},26654:function(e,t,o){o.d(t,{_H:function(){return useAuthorQuery},MT:function(){return useAuthorsQuery},bz:function(){return useCreateAuthorMutation},KU:function(){return useDeleteAuthorMutation},Oh:function(){return useUpdateAuthorMutation},WF:function(){return useUpdateAuthorMutationInList}});var u=o(11163),n=o.n(u),r=o(88767),s=o(22920),a=o(5233),i=o(97514),l=o(47869),c=o(28597),d=o(55191),h=o(3737);let m={...(0,d.h)(l.P.AUTHORS),paginated:e=>{let{type:t,name:o,is_approved:u,...n}=e;return h.eN.get(l.P.AUTHORS,{searchJoin:"and",...n,search:h.eN.formatSearchParams({type:t,name:o,is_approved:u})})}};var v=o(93345);let useCreateAuthorMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)(),o=(0,u.useRouter)();return(0,r.useMutation)(m.create,{onSuccess:async()=>{let e=o.query.shop?"/".concat(o.query.shop).concat(i.Z.author.list):i.Z.author.list;await n().push(e,void 0,{locale:v.Config.defaultLanguage}),s.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.AUTHORS)},onError:e=>{var o;s.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))}})},useDeleteAuthorMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)();return(0,r.useMutation)(m.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.AUTHORS)},onError:e=>{var o;s.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))}})},useUpdateAuthorMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,u.useRouter)(),o=(0,r.useQueryClient)();return(0,r.useMutation)(m.update,{onSuccess:async o=>{let u=t.query.shop?"/".concat(t.query.shop).concat(i.Z.author.list):i.Z.author.list;await t.push("".concat(u,"/").concat(null==o?void 0:o.slug,"/edit"),void 0,{locale:v.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{o.invalidateQueries(l.P.AUTHORS)},onError:t=>{var o;s.Am.error(e("common:".concat(null==t?void 0:null===(o=t.response)||void 0===o?void 0:o.data.message)))}})},useUpdateAuthorMutationInList=()=>{let{t:e}=(0,a.$G)(),t=(0,r.useQueryClient)();return(0,r.useMutation)(m.update,{onSuccess:async()=>{s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.AUTHORS)},onError:t=>{var o;s.Am.error(e("common:".concat(null==t?void 0:null===(o=t.response)||void 0===o?void 0:o.data.message)))}})},useAuthorQuery=e=>{let{slug:t,language:o}=e,{data:u,error:n,isLoading:s}=(0,r.useQuery)([l.P.AUTHORS,{slug:t,language:o}],()=>m.get({slug:t,language:o}));return{author:u,error:n,loading:s}},useAuthorsQuery=e=>{var t;let{data:o,error:u,isLoading:n}=(0,r.useQuery)([l.P.AUTHORS,e],e=>{let{queryKey:t,pageParam:o}=e;return m.paginated(Object.assign({},t[1],o))},{keepPreviousData:!0});return{authors:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(o),error:u,loading:n}}},9140:function(e,t,o){o.d(t,{e:function(){return getErrorMessage}});var u=o(11163),n=o.n(u),r=o(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let o of e.graphQLErrors){if(o.extensions&&"validation"===o.extensions.category)return t.message=o.message,t.validation=o.extensions.validation,t;o.extensions&&"authorization"===o.extensions.category&&(r.Z.remove("auth_token"),r.Z.remove("auth_permissions"),n().push("/"))}return t.message=e.message,t}}}]);