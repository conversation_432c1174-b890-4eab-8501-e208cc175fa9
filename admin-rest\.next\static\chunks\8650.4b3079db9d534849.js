"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8650],{68650:function(e,t,s){s.r(t);var a=s(85893),o=s(71421),n=s(59272),u=s(75814),r=s(60385);t.default=()=>{let{mutate:e,isLoading:t}=(0,r.f0)(),{data:s}=(0,u.X9)(),{closeModal:l}=(0,u.SO)();async function handleDelete(){e({id:s},{onSettled:()=>{l()}})}return(0,a.jsx)(o.Z,{onCancel:l,onDelete:handleDelete,deleteBtnLoading:t,deleteBtnText:"text-shop-approve-button",icon:(0,a.jsx)(n.c,{className:"m-auto mt-4 h-10 w-10 text-accent"}),deleteBtnClassName:"!bg-accent focus:outline-none hover:!bg-accent-hover focus:!bg-accent-hover",cancelBtnClassName:"!bg-red-600 focus:outline-none hover:!bg-red-700 focus:!bg-red-700",title:"text-shop-approve-description",description:""})}},59272:function(e,t,s){s.d(t,{O:function(){return CheckMarkGhost},c:function(){return CheckMarkCircle}});var a=s(85893);s(67294);let CheckMarkCircle=e=>{let{...t}=e;return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 330 330",fill:"currentColor",...t,children:[(0,a.jsx)("path",{d:"M165 0C74.019 0 0 74.019 0 165s74.019 165 165 165 165-74.019 165-165S255.981 0 165 0zm0 300c-74.44 0-135-60.561-135-135S90.56 30 165 30s135 60.561 135 135-60.561 135-135 135z"}),(0,a.jsx)("path",{d:"M226.872 106.664l-84.854 84.853-38.89-38.891c-5.857-5.857-15.355-5.858-21.213-.001-5.858 5.858-5.858 15.355 0 21.213l49.496 49.498a15 15 0 0010.606 4.394h.001c3.978 0 7.793-1.581 10.606-4.393l95.461-95.459c5.858-5.858 5.858-15.355 0-21.213-5.858-5.858-15.355-5.859-21.213-.001z"})]})},CheckMarkGhost=e=>{let{...t}=e;return(0,a.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,a.jsx)("path",{opacity:.2,d:"M17.5 10a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z",fill:"currentColor"}),(0,a.jsx)("path",{d:"M13.567 7.683a.626.626 0 010 .884l-4.375 4.375a.626.626 0 01-.884 0l-1.875-1.875a.625.625 0 11.884-.884l1.433 1.433 3.933-3.933a.625.625 0 01.884 0zM18.125 10A8.125 8.125 0 1110 1.875 8.133 8.133 0 0118.125 10zm-1.25 0A6.875 6.875 0 1010 16.875 6.883 6.883 0 0016.875 10z",fill:"currentColor"})]})}},60385:function(e,t,s){s.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var a=s(11163),o=s.n(a),n=s(88767),u=s(22920),r=s(5233),l=s(28597),c=s(97514),i=s(47869),d=s(93345),S=s(55191),h=s(3737);let p={...(0,S.h)(i.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h.eN.get(i.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...s,search:h.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:s,shop_id:a}=e;return h.eN.get("".concat(i.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:s,shop_id:a,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:s,...a}=e;return h.eN.get(i.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:s,...a,search:h.eN.formatSearchParams({title:t,shop_id:s})})},approve:e=>h.eN.post(i.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>h.eN.post(i.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...s}=e;return h.eN.get(i.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...s,search:h.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:s,error:a,isLoading:o}=(0,n.useQuery)([i.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(s),error:a,loading:o}},useRequestedListForFlashSale=e=>{let{id:t,language:s,shop_id:a}=e,{data:o,error:u,isLoading:r}=(0,n.useQuery)([i.P.FLASH_SALE,{id:t,language:s,shop_id:a}],()=>p.get({id:t,language:s,shop_id:a}));return{flashSaleRequest:o,error:u,loading:r}},useRequestedProductsForFlashSale=e=>{var t;let{data:s,error:a,isLoading:o}=(0,n.useQuery)([i.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.requestedProducts(Object.assign({},t[1],s))},{keepPreviousData:!0});return{products:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(s),error:a,loading:o}},useCreateFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),t=(0,a.useRouter)(),{t:s}=(0,r.$G)();return(0,n.useMutation)(p.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(c.Z.vendorRequestForFlashSale.list):c.Z.vendorRequestForFlashSale.list;await o().push(e,void 0,{locale:d.Config.defaultLanguage}),u.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;u.Am.error(s("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)(),s=(0,a.useRouter)();return(0,n.useMutation)(p.update,{onSuccess:async t=>{let a=s.query.shop?"/".concat(s.query.shop).concat(c.Z.vendorRequestForFlashSale.list):c.Z.vendorRequestForFlashSale.list;await s.push(a,void 0,{locale:d.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var s;u.Am.error(e("common:".concat(null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)();return(0,n.useMutation)(p.delete,{onSuccess:()=>{u.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var s;u.Am.error(t("common:".concat(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)(),s=(0,a.useRouter)();return(0,n.useMutation)(p.approve,{onSuccess:async()=>{let t=s.query.shop?"/".concat(s.query.shop).concat(c.Z.vendorRequestForFlashSale.list):c.Z.vendorRequestForFlashSale.list;await o().push(t,void 0,{locale:d.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(p.disapprove,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.FLASH_SALE)}})}}}]);