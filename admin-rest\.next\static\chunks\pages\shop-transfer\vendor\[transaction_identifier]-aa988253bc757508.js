(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8756],{38693:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shop-transfer/vendor/[transaction_identifier]",function(){return t(37900)}])},37900:function(e,n,t){"use strict";t.r(n),t.d(n,{__N_SSP:function(){return O}});var i=t(85893),r=t(82801),a=t(24724),s=t(45957),u=t(55846),o=t(76240),d=t(99930),l=t(16203),_=t(27484),f=t.n(_),g=t(84110),c=t.n(g),p=t(29387),w=t.n(p),h=t(70178),v=t.n(h),x=t(5233),P=t(11163);f().extend(c()),f().extend(v()),f().extend(w());let OwnershipTransferSinglePage=()=>{let{query:e,locale:n}=(0,P.useRouter)(),{t}=(0,x.$G)(),{ownershipTransfer:r,loading:l,error:_}=(0,o.OM)({transaction_identifier:null==e?void 0:e.transaction_identifier,language:n,request_view_type:"detail"}),{data:f,isLoading:g,error:c}=(0,d.UE)();return l||g?(0,i.jsx)(u.Z,{text:t("common:text-loading")}):_||c?(0,i.jsx)(s.Z,{message:(null==_?void 0:_.message)||(null==c?void 0:c.message)}):(0,i.jsx)(a.P,{data:r,userId:null==f?void 0:f.id})};OwnershipTransferSinglePage.authenticate={permissions:l.Zk},OwnershipTransferSinglePage.Layout=r.default;var O=!0;n.default=OwnershipTransferSinglePage}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9709,2787,9494,5535,8186,1285,8504,2801,919,4724,9774,2888,179],function(){return e(e.s=38693)}),_N_E=e.O()}]);