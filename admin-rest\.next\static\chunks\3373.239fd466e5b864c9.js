(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3373],{1989:function(t,e,o){var n=o(51789),i=o(80401),r=o(57667),a=o(21327),s=o(81866);function Hash(t){var e=-1,o=null==t?0:t.length;for(this.clear();++e<o;){var n=t[e];this.set(n[0],n[1])}}Hash.prototype.clear=n,Hash.prototype.delete=i,Hash.prototype.get=r,Hash.prototype.has=a,Hash.prototype.set=s,t.exports=Hash},38407:function(t,e,o){var n=o(27040),i=o(14125),r=o(82117),a=o(67518),s=o(54705);function ListCache(t){var e=-1,o=null==t?0:t.length;for(this.clear();++e<o;){var n=t[e];this.set(n[0],n[1])}}ListCache.prototype.clear=n,ListCache.prototype.delete=i,ListCache.prototype.get=r,ListCache.prototype.has=a,ListCache.prototype.set=s,t.exports=ListCache},83369:function(t,e,o){var n=o(24785),i=o(11285),r=o(96e3),a=o(49916),s=o(95265);function MapCache(t){var e=-1,o=null==t?0:t.length;for(this.clear();++e<o;){var n=t[e];this.set(n[0],n[1])}}MapCache.prototype.clear=n,MapCache.prototype.delete=i,MapCache.prototype.get=r,MapCache.prototype.has=a,MapCache.prototype.set=s,t.exports=MapCache},62488:function(t){t.exports=function(t,e){for(var o=-1,n=e.length,i=t.length;++o<n;)t[i+o]=e[o];return t}},18470:function(t,e,o){var n=o(77813);t.exports=function(t,e){for(var o=t.length;o--;)if(n(t[o][0],e))return o;return -1}},45050:function(t,e,o){var n=o(37019);t.exports=function(t,e){var o=t.__data__;return n(e)?o["string"==typeof e?"string":"hash"]:o.map}},51789:function(t,e,o){var n=o(94536);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},80401:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},57667:function(t,e,o){var n=o(94536),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var o=e[t];return"__lodash_hash_undefined__"===o?void 0:o}return i.call(e,t)?e[t]:void 0}},21327:function(t,e,o){var n=o(94536),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},81866:function(t,e,o){var n=o(94536);t.exports=function(t,e){var o=this.__data__;return this.size+=this.has(t)?0:1,o[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},65776:function(t){var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,o){var n=typeof t;return!!(o=null==o?9007199254740991:o)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<o}},37019:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},27040:function(t){t.exports=function(){this.__data__=[],this.size=0}},14125:function(t,e,o){var n=o(18470),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,o=n(e,t);return!(o<0)&&(o==e.length-1?e.pop():i.call(e,o,1),--this.size,!0)}},82117:function(t,e,o){var n=o(18470);t.exports=function(t){var e=this.__data__,o=n(e,t);return o<0?void 0:e[o][1]}},67518:function(t,e,o){var n=o(18470);t.exports=function(t){return n(this.__data__,t)>-1}},54705:function(t,e,o){var n=o(18470);t.exports=function(t,e){var o=this.__data__,i=n(o,t);return i<0?(++this.size,o.push([t,e])):o[i][1]=e,this}},24785:function(t,e,o){var n=o(1989),i=o(38407),r=o(57071);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(r||i),string:new n}}},11285:function(t,e,o){var n=o(45050);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},96e3:function(t,e,o){var n=o(45050);t.exports=function(t){return n(this,t).get(t)}},49916:function(t,e,o){var n=o(45050);t.exports=function(t){return n(this,t).has(t)}},95265:function(t,e,o){var n=o(45050);t.exports=function(t,e){var o=n(this,t),i=o.size;return o.set(t,e),this.size+=o.size==i?0:1,this}},94536:function(t,e,o){var n=o(10852)(Object,"create");t.exports=n},77813:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},18446:function(t,e,o){var n=o(90939);t.exports=function(t,e){return n(t,e)}},66271:function(t,e,o){"use strict";var n=o(85893),i=o(93967),r=o.n(i),a=o(98388);e.Z=t=>{let{message:e,className:o}=t;return(0,n.jsx)("p",{className:(0,a.m6)(r()("my-2 text-xs text-start text-red-500",o)),children:e})}},97148:function(t,e,o){"use strict";o.r(e);var n=o(85893),i=o(93967),r=o.n(i),a=o(67294),s=o(87536),l=o(71167),u=o.n(l),c=o(66271),d=o(60779),h=o(5233),p=o(84178),f=o(12755),g=o.n(f);o(83371),o(37676);var v=o(98388),m=o(55846),y=o(71611);e.default=t=>{let{title:e,placeholder:o,control:i,className:f,editorClassName:_,name:E,required:b,disabled:x,error:C,toolTipText:R,...Q}=t,{t:w}=(0,h.$G)(),j=l.Quill.import("formats/font"),[k,S]=(0,a.useState)(!1);j.whitelist=["Roboto","Raleway","Lato","Rubik","OpenSans"],l.Quill.register({"formats/emoji":g().EmojiBlot,"modules/emoji-toolbar":g().ToolbarEmoji,"modules/emoji-textarea":g().TextAreaEmoji,"modules/emoji-shortname":g().ShortNameEmoji,Font:j,"themes/snow-quill-color-picker-enhance":p.c},!0);let O=(0,a.useRef)(),ImageHandler=()=>{let t=document.createElement("input");t.setAttribute("type","file"),t.setAttribute("accept","image/*"),t.click(),t.onchange=async()=>{let e=t.files?t.files[0]:null,o=new FormData;if(e){o.append("file",e),o.append("resource_type","raw"),S(!0);let t=await (null===d.t||void 0===d.t?void 0:d.t.upload(o));S(!1);let n=new FileReader;n.onload=()=>{var e,o;let n=null==O?void 0:null===(e=O.current)||void 0===e?void 0:e.getEditor(),i=null==n?void 0:n.getSelection(!0);n.insertEmbed(i.index,"image",null===(o=t[0])||void 0===o?void 0:o.original,"user")},n.readAsDataURL(e)}}},M=(0,a.useMemo)(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]},{font:j.whitelist}],["bold","italic","underline","strike","blockquote"],[{list:"ordered"},{list:"bullet"},{indent:"-1"},{indent:"+1"}],[{color:[]}],[{background:[]}],[{align:[]}],["code-block"],["link","image","video"],["emoji"],[{script:"sub"},{script:"super"}],["clean"]],handlers:{image:ImageHandler}},"emoji-toolbar":!0,"emoji-textarea":!0,"emoji-shortname":!0}),[]),T=["header","font","bold","italic","underline","strike","blockquote","list","bullet","indent","align","link","color","background","script","code-block","image","video","emoji"];return(0,n.jsxs)("div",{className:(0,v.m6)(r()("react-quill-description",f)),children:[e?(0,n.jsx)(y.Z,{htmlFor:E,toolTipText:R,label:e,required:b}):"",k?(0,n.jsxs)("div",{className:"flex items-center my-2 gap-2",children:[(0,n.jsx)(m.Z,{simple:!0,className:"h-6 w-6"}),(0,n.jsxs)("p",{className:"font-semibold text-body italic",children:[w("text-image-uploading-message"),"..."]})]}):"",(0,n.jsx)(s.Qr,{name:E,control:i,render:t=>{let{field:i}=t;return(0,n.jsx)(u(),{id:E,modules:M,formats:T,theme:"snow-quill-color-picker-enhance",...i,placeholder:e||o,onChange:t=>{null==i||i.onChange(t)},className:(0,v.m6)(r()("relative mb-5 rounded border border-border-base",_,x?"select-none bg-[#EEF1F4] cursor-not-allowed disabled-editor":"")),ref:O,readOnly:x})},...Q}),C?(0,n.jsx)(c.Z,{message:w(C)}):""]})}},60779:function(t,e,o){"use strict";o.d(e,{t:function(){return r}});var n=o(3737),i=o(47869);let r={upload:async t=>{let e=new FormData;return t.forEach(t=>{e.append("attachment[]",t)}),n.eN.post(i.P.ATTACHMENTS,e,{headers:{"Content-Type":"multipart/form-data"}})}}},83371:function(){},37676:function(){},71167:function(t,e,o){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)}),r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var i in e=arguments[o])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__spreadArrays||function(){for(var t=0,e=0,o=arguments.length;e<o;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<o;e++)for(var r=arguments[e],a=0,s=r.length;a<s;a++,i++)n[i]=r[a];return n},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},l=s(o(67294)),u=s(o(73935)),c=s(o(18446)),d=s(o(76095)),h=function(t){function ReactQuill(e){var o=t.call(this,e)||this;o.dirtyProps=["modules","formats","bounds","theme","children"],o.cleanProps=["id","className","style","placeholder","tabIndex","onChange","onChangeSelection","onFocus","onBlur","onKeyPress","onKeyDown","onKeyUp"],o.state={generation:0},o.selection=null,o.onEditorChange=function(t,e,n,i){var r,a;"text-change"===t?null===(r=o.onEditorChangeText)||void 0===r||r.call(o,o.editor.root.innerHTML,e,i,o.unprivilegedEditor):"selection-change"===t&&(null===(a=o.onEditorChangeSelection)||void 0===a||a.call(o,e,i,o.unprivilegedEditor))};var n=o.isControlled()?e.value:e.defaultValue;return o.value=null!=n?n:"",o}return i(ReactQuill,t),ReactQuill.prototype.validateProps=function(t){if(l.default.Children.count(t.children)>1)throw Error("The Quill editing area can only be composed of a single React element.");if(l.default.Children.count(t.children)){var e=l.default.Children.only(t.children);if((null==e?void 0:e.type)==="textarea")throw Error("Quill does not support editing on a <textarea>. Use a <div> instead.")}if(this.lastDeltaChangeSet&&t.value===this.lastDeltaChangeSet)throw Error("You are passing the `delta` object from the `onChange` event back as `value`. You most probably want `editor.getContents()` instead. See: https://github.com/zenoamaro/react-quill#using-deltas")},ReactQuill.prototype.shouldComponentUpdate=function(t,e){var o,n=this;if(this.validateProps(t),!this.editor||this.state.generation!==e.generation)return!0;if("value"in t){var i=this.getEditorContents(),r=null!=(o=t.value)?o:"";this.isEqualValue(r,i)||this.setEditorContents(this.editor,r)}return t.readOnly!==this.props.readOnly&&this.setEditorReadOnly(this.editor,t.readOnly),a(this.cleanProps,this.dirtyProps).some(function(e){return!c.default(t[e],n.props[e])})},ReactQuill.prototype.shouldComponentRegenerate=function(t){var e=this;return this.dirtyProps.some(function(o){return!c.default(t[o],e.props[o])})},ReactQuill.prototype.componentDidMount=function(){this.instantiateEditor(),this.setEditorContents(this.editor,this.getEditorContents())},ReactQuill.prototype.componentWillUnmount=function(){this.destroyEditor()},ReactQuill.prototype.componentDidUpdate=function(t,e){var o=this;if(this.editor&&this.shouldComponentRegenerate(t)){var n=this.editor.getContents(),i=this.editor.getSelection();this.regenerationSnapshot={delta:n,selection:i},this.setState({generation:this.state.generation+1}),this.destroyEditor()}if(this.state.generation!==e.generation){var r=this.regenerationSnapshot,n=r.delta,a=r.selection;delete this.regenerationSnapshot,this.instantiateEditor();var s=this.editor;s.setContents(n),postpone(function(){return o.setEditorSelection(s,a)})}},ReactQuill.prototype.instantiateEditor=function(){this.editor?this.hookEditor(this.editor):this.editor=this.createEditor(this.getEditingArea(),this.getEditorConfig())},ReactQuill.prototype.destroyEditor=function(){this.editor&&this.unhookEditor(this.editor)},ReactQuill.prototype.isControlled=function(){return"value"in this.props},ReactQuill.prototype.getEditorConfig=function(){return{bounds:this.props.bounds,formats:this.props.formats,modules:this.props.modules,placeholder:this.props.placeholder,readOnly:this.props.readOnly,scrollingContainer:this.props.scrollingContainer,tabIndex:this.props.tabIndex,theme:this.props.theme}},ReactQuill.prototype.getEditor=function(){if(!this.editor)throw Error("Accessing non-instantiated editor");return this.editor},ReactQuill.prototype.createEditor=function(t,e){var o=new d.default(t,e);return null!=e.tabIndex&&this.setEditorTabIndex(o,e.tabIndex),this.hookEditor(o),o},ReactQuill.prototype.hookEditor=function(t){this.unprivilegedEditor=this.makeUnprivilegedEditor(t),t.on("editor-change",this.onEditorChange)},ReactQuill.prototype.unhookEditor=function(t){t.off("editor-change",this.onEditorChange)},ReactQuill.prototype.getEditorContents=function(){return this.value},ReactQuill.prototype.getEditorSelection=function(){return this.selection},ReactQuill.prototype.isDelta=function(t){return t&&t.ops},ReactQuill.prototype.isEqualValue=function(t,e){return this.isDelta(t)&&this.isDelta(e)?c.default(t.ops,e.ops):c.default(t,e)},ReactQuill.prototype.setEditorContents=function(t,e){var o=this;this.value=e;var n=this.getEditorSelection();"string"==typeof e?t.setContents(t.clipboard.convert(e)):t.setContents(e),postpone(function(){return o.setEditorSelection(t,n)})},ReactQuill.prototype.setEditorSelection=function(t,e){if(this.selection=e,e){var o=t.getLength();e.index=Math.max(0,Math.min(e.index,o-1)),e.length=Math.max(0,Math.min(e.length,o-1-e.index)),t.setSelection(e)}},ReactQuill.prototype.setEditorTabIndex=function(t,e){var o;(null===(o=null==t?void 0:t.scroll)||void 0===o?void 0:o.domNode)&&(t.scroll.domNode.tabIndex=e)},ReactQuill.prototype.setEditorReadOnly=function(t,e){e?t.disable():t.enable()},ReactQuill.prototype.makeUnprivilegedEditor=function(t){return{getHTML:function(){return t.root.innerHTML},getLength:t.getLength.bind(t),getText:t.getText.bind(t),getContents:t.getContents.bind(t),getSelection:t.getSelection.bind(t),getBounds:t.getBounds.bind(t)}},ReactQuill.prototype.getEditingArea=function(){if(!this.editingArea)throw Error("Instantiating on missing editing area");var t=u.default.findDOMNode(this.editingArea);if(!t)throw Error("Cannot find element for editing area");if(3===t.nodeType)throw Error("Editing area cannot be a text node");return t},ReactQuill.prototype.renderEditingArea=function(){var t=this,e=this.props,o=e.children,n=e.preserveWhitespace,i={key:this.state.generation,ref:function(e){t.editingArea=e}};return l.default.Children.count(o)?l.default.cloneElement(l.default.Children.only(o),i):n?l.default.createElement("pre",r({},i)):l.default.createElement("div",r({},i))},ReactQuill.prototype.render=function(){var t;return l.default.createElement("div",{id:this.props.id,style:this.props.style,key:this.state.generation,className:"quill "+(null!=(t=this.props.className)?t:""),onKeyPress:this.props.onKeyPress,onKeyDown:this.props.onKeyDown,onKeyUp:this.props.onKeyUp},this.renderEditingArea())},ReactQuill.prototype.onEditorChangeText=function(t,e,o,n){if(this.editor){var i,r,a=this.isDelta(this.value)?n.getContents():n.getHTML();a!==this.getEditorContents()&&(this.lastDeltaChangeSet=e,this.value=a,null===(r=(i=this.props).onChange)||void 0===r||r.call(i,t,e,o,n))}},ReactQuill.prototype.onEditorChangeSelection=function(t,e,o){if(this.editor){var n,i,r,a,s,l,u=this.getEditorSelection(),d=!u&&t,h=u&&!t;!c.default(t,u)&&(this.selection=t,null===(i=(n=this.props).onChangeSelection)||void 0===i||i.call(n,t,e,o),d?null===(a=(r=this.props).onFocus)||void 0===a||a.call(r,t,e,o):h&&(null===(l=(s=this.props).onBlur)||void 0===l||l.call(s,u,e,o)))}},ReactQuill.prototype.focus=function(){this.editor&&this.editor.focus()},ReactQuill.prototype.blur=function(){this.editor&&(this.selection=null,this.editor.blur())},ReactQuill.displayName="React Quill",ReactQuill.Quill=d.default,ReactQuill.defaultProps={theme:"snow",modules:{},readOnly:!1},ReactQuill}(l.default.Component);function postpone(t){Promise.resolve().then(t)}t.exports=h}}]);