http:
  middlewares:
    cors:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
          - PATCH
        accessControlAllowOriginList:
          - "http://shop.localhost"
          - "http://admin.localhost"
          - "http://api.localhost"
          - "http://localhost:3002"
          - "http://localhost:3003"
        accessControlAllowHeaders:
          - "*"
        accessControlAllowCredentials: true
        accessControlMaxAge: 100
        addVaryHeader: true

    secure-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000

    gzip:
      compress: {}

  routers:
    api-router:
      rule: "Host(`api.localhost`)"
      service: api-service
      middlewares:
        - cors
        - gzip

    admin-router:
      rule: "Host(`admin.localhost`)"
      service: admin-service
      middlewares:
        - gzip

    shop-router:
      rule: "Host(`shop.localhost`)"
      service: shop-service
      middlewares:
        - gzip

  services:
    api-service:
      loadBalancer:
        servers:
          - url: "http://api-rest:5000"

    admin-service:
      loadBalancer:
        servers:
          - url: "http://admin-rest:3002"

    shop-service:
      loadBalancer:
        servers:
          - url: "http://shop:3003"
