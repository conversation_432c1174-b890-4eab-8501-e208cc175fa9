"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_cart_cart-counter-icon-button_tsx";
exports.ids = ["src_components_cart_cart-counter-icon-button_tsx"];
exports.modules = {

/***/ "./src/components/cart/cart-counter-icon-button.tsx":
/*!**********************************************************!*\
  !*** ./src/components/cart/cart-counter-icon-button.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/cart-outlined */ \"./src/components/icons/cart-outlined.tsx\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__, _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_5__, tailwind_merge__WEBPACK_IMPORTED_MODULE_6__]);\n([_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__, _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__, jotai__WEBPACK_IMPORTED_MODULE_5__, tailwind_merge__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst CartCounterIconButton = ({ className, ...rest })=>{\n    const { totalUniqueItems } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    const [_, setDisplayCart] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_2__.drawerAtom);\n    function handleCartSidebar() {\n        setDisplayCart({\n            display: true,\n            view: \"cart\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_6__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"hidden product-cart lg:flex relative\", className)),\n        onClick: handleCartSidebar,\n        ...rest,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_cart_outlined__WEBPACK_IMPORTED_MODULE_1__.CartOutlinedIcon, {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            totalUniqueItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"min-w-[20px] h-5 flex items-center justify-center rounded-full bg-accent text-light text-[10px] absolute ltr:-right-1/2 rtl:-left-1/2 -top-1/2\",\n                children: totalUniqueItems\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-icon-button.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CartCounterIconButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/cart/cart-counter-icon-button.tsx\n");

/***/ }),

/***/ "./src/components/icons/cart-outlined.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/cart-outlined.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartOutlinedIcon: () => (/* binding */ CartOutlinedIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CartOutlinedIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 17.6 19.6\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            \"data-name\": \"Path 12\",\n            d: \"M12.8 8.8v-4a4 4 0 00-8 0v4m-3-2h14l1 12H.8z\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: \"1.6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-outlined.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jYXJ0LW91dGxpbmVkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsbUJBQXNELENBQUNDLHNCQUNsRSw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsU0FBUTtRQUFpQixHQUFHSCxLQUFLO2tCQUN2RSw0RUFBQ0k7WUFDQ0MsYUFBVTtZQUNWQyxHQUFFO1lBQ0ZDLE1BQUs7WUFDTEMsUUFBTztZQUNQQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQVk7Ozs7Ozs7Ozs7a0JBR2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NhcnQtb3V0bGluZWQudHN4P2E5NmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENhcnRPdXRsaW5lZEljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxyXG4gIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIHZpZXdCb3g9XCIwIDAgMTcuNiAxOS42XCIgey4uLnByb3BzfT5cclxuICAgIDxwYXRoXHJcbiAgICAgIGRhdGEtbmFtZT1cIlBhdGggMTJcIlxyXG4gICAgICBkPVwiTTEyLjggOC44di00YTQgNCAwIDAwLTggMHY0bS0zLTJoMTRsMSAxMkguOHpcIlxyXG4gICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICBzdHJva2VXaWR0aD1cIjEuNlwiXHJcbiAgICAvPlxyXG4gIDwvc3ZnPlxyXG4pO1xyXG4iXSwibmFtZXMiOlsiQ2FydE91dGxpbmVkSWNvbiIsInByb3BzIiwic3ZnIiwieG1sbnMiLCJ2aWV3Qm94IiwicGF0aCIsImRhdGEtbmFtZSIsImQiLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/cart-outlined.tsx\n");

/***/ })

};
;