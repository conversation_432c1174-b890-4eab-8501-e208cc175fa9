(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7852,2007,2036],{22105:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/notify-logs",function(){return r(13206)}])},92072:function(e,t,r){"use strict";var o=r(85893),n=r(93967),i=r.n(n),l=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,o.jsx)("div",{className:(0,l.m6)(i()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},35484:function(e,t,r){"use strict";var o=r(85893),n=r(93967),i=r.n(n),l=r(98388);t.Z=e=>{let{title:t,className:r,...n}=e;return(0,o.jsx)("h2",{className:(0,l.m6)(i()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",r)),...n,children:t})}},97670:function(e,t,r){"use strict";r.r(t);var o=r(85893),n=r(78985),i=r(79362),l=r(8144),a=r(74673),s=r(99494),c=r(5233),d=r(1631),u=r(11163),f=r(48583),p=r(93967),m=r.n(p),b=r(30824),g=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,c.$G)(),[n,l]=(0,f.KO)(i.Hf),{childMenu:a}=t,{width:s}=(0,g.Z)();return(0,o.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:t,label:l,icon:a,childMenu:c}=e;return(0,o.jsx)(d.Z,{href:t,label:r(l),icon:a,childMenu:c,miniSidebar:n&&s>=i.h2},l)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[r,n]=(0,f.KO)(i.Hf),l=null===s.siteSettings||void 0===s.siteSettings?void 0:null===(e=s.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(l),{width:d}=(0,g.Z)();return(0,o.jsx)(o.Fragment,{children:null==a?void 0:a.map((e,n)=>{var a;return(0,o.jsxs)("div",{className:m()("flex flex-col px-5",r&&d>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,o.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&d>=i.h2?"hidden":""),children:t(null===(a=l[e])||void 0===a?void 0:a.label)}),(0,o.jsx)(SidebarItemMap,{menuItems:l[e]})]},n)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,u.useRouter)(),[s,c]=(0,f.KO)(i.Hf),[d]=(0,f.KO)(i.GH),[p]=(0,f.KO)(i.W4),{width:v}=(0,g.Z)();return(0,o.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,o.jsx)(n.Z,{}),(0,o.jsx)(a.Z,{children:(0,o.jsx)(SideBarGroup,{})}),(0,o.jsxs)("div",{className:"flex flex-1",children:[(0,o.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",v>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-20",s&&v>=i.h2?"lg:w-24":"lg:w-76"),children:(0,o.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,o.jsx)(b.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,o.jsx)(SideBarGroup,{})})})}),(0,o.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",v>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",s&&v>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,o.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,o.jsx)(l.Z,{})]})]})]})}},831:function(e,t,r){"use strict";var o=r(85893),n=r(93967),i=r.n(n),l=r(5233),a=r(25675),s=r.n(a),c=r(98388);t.Z=e=>{let{className:t,imageParentClassName:r,text:n,image:a="/no-result.svg"}=e,{t:d}=(0,l.$G)("common");return(0,o.jsxs)("div",{className:(0,c.m6)(i()("flex flex-col items-center",t)),children:[(0,o.jsx)("div",{className:(0,c.m6)(i()("relative flex h-full min-h-[380px] w-full items-center justify-center md:min-h-[450px]",r)),children:(0,o.jsx)(s(),{src:a,alt:d(n||"text-no-result-found"),className:"h-full w-full object-contain",fill:!0,sizes:"(max-width: 768px) 100vw"})}),n&&(0,o.jsx)("h3",{className:"my-7 w-full text-center text-base font-semibold text-heading/80 lg:text-xl",children:d(n)})]})}},87077:function(e,t,r){"use strict";r.d(t,{W:function(){return n},X:function(){return o}});let o={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,r){"use strict";var o=r(85893),n=r(76518),i=r(67294),l=r(23157),a=r(87077);let s=i.forwardRef((e,t)=>{let{isRTL:r}=(0,n.S)();return(0,o.jsx)(l.ZP,{ref:t,styles:a.X,isRtl:r,...e})});s.displayName="Select",t.Z=s},13206:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return R},default:function(){return NotifyLogsPage}});var o=r(85893),n=r(92072),i=r(79828),l=r(97670),a=r(67294),s=r(45957),c=r(97514),d=r(55846),u=r(5233),f=r(16203),p=r(11163),m=r(27484),b=r.n(m),g=r(73626),v=r(84110),x=r.n(v),h=r(70178),y=r.n(h),j=r(29387),w=r.n(j),_=r(2705),O=r(99930),N=r(71816),S=r(93967),Z=r.n(S),F=r(10265),C=r(831),D=r(35484);b().extend(x()),b().extend(y()),b().extend(w());let NotifyLogItem=e=>{var t,r,n,i,l;let{item:a}=e,{t:s}=(0,u.$G)(),d=(0,p.useRouter)(),{permissions:m}=(0,f.WA)(),{locale:v}=(0,p.useRouter)(),{mutate:x,isLoading:h}=(0,_.gc)(),{order:y}=(0,g.OT)({id:null==a?void 0:a.notify_tracker,language:v}),j="";switch(null==a?void 0:a.notify_type){case"order":j="text-created-new-order";break;case"message":j="text-sent-new-message"}let handleClickOnNotification=e=>{var t,r,o,n;let i;switch(x({input:e}),null==e?void 0:e.notify_type){case"order":let l=null==y?void 0:null===(t=y.shop)||void 0===t?void 0:t.slug;i=(null==m?void 0:m.includes("super_admin"))?null===c.Z||void 0===c.Z?void 0:null===(r=c.Z.order)||void 0===r?void 0:r.details(null==e?void 0:e.notify_tracker):l+(null===c.Z||void 0===c.Z?void 0:null===(o=c.Z.order)||void 0===o?void 0:o.details(null==e?void 0:e.notify_tracker)),d.push("/"+i);break;case"message":i=null===c.Z||void 0===c.Z?void 0:null===(n=c.Z.message)||void 0===n?void 0:n.details(null==e?void 0:e.notify_tracker),d.push("/"+i)}};return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("div",{className:Z()("relative flex cursor-pointer rounded-lg bg-white p-4 border-s-4 before:absolute before:top-1/2 before:h-2.5 before:w-2.5 before:-translate-y-1/2 before:rounded-full before:bg-accent before:opacity-0 before:content-[''] before:end-4 md:before:end-7 xl:p-7",(null==a?void 0:a.is_read)?"border-gray-300":"border-accent before:opacity-100"),onClick:()=>handleClickOnNotification(a),children:[(0,o.jsx)(N.Z,{name:null==a?void 0:null===(t=a.user)||void 0===t?void 0:t.name,size:"lg",src:null==a?void 0:null===(i=a.sender_user)||void 0===i?void 0:null===(n=i.profile)||void 0===n?void 0:null===(r=n.avatar)||void 0===r?void 0:r.thumbnail}),(0,o.jsxs)("div",{className:"ps-4",children:[(0,o.jsxs)("div",{className:"mb-0.5 gap-2 text-[15px]",children:[(0,o.jsx)("span",{className:"font-semibold text-heading",children:null==a?void 0:null===(l=a.user)||void 0===l?void 0:l.name})," ",s(j),(null==a?void 0:a.notify_type)!=="message"?(0,o.jsxs)("span",{className:Z()("inline-block font-medium text-accent hover:text-accent",(null==a?void 0:a.is_read)?"text-gray-500/80":"text-accent"),children:["#",null==a?void 0:a.notify_tracker]}):""]}),(0,o.jsxs)("span",{className:Z()("text-sm font-medium",(null==a?void 0:a.is_read)?"text-gray-500/80":"text-accent"),children:[b()(null==a?void 0:a.created_at).format("MMM DD, YYYY")," at"," ",b()(null==a?void 0:a.created_at).format("hh:mm A")]})]})]})})};function NotificationFilter(e){let{showTargetFilter:t,showReadStatusFilter:r,onReadStatusFilter:n,onTargetFilter:l,className:a}=e,{t:s}=(0,u.$G)();return(0,o.jsxs)("div",{className:Z()("flex w-full",a),children:[t&&(0,o.jsx)("div",{className:"w-full pr-3",children:(0,o.jsx)(i.Z,{options:[{name:"All",value:""},{name:"Order",value:"order"},{name:"Messages",value:"message"}],getOptionLabel:e=>e.name,getOptionValue:e=>e.value,placeholder:s("common:filter-by-notification-type"),onChange:l,isClearable:!0,defaultValue:[{name:"All",value:""}]})}),r&&(0,o.jsx)("div",{className:"w-full",children:(0,o.jsx)(i.Z,{options:[{name:"All",value:""},{name:"Unread",value:"0"},{name:"Read",value:"1"}],getOptionLabel:e=>e.name,getOptionValue:e=>e.value,placeholder:s("common:filter-by-notification-type"),onChange:n,isClearable:!0,defaultValue:[{name:"All",value:""}]})})]})}var R=!0;function NotifyLogsPage(){let{t:e}=(0,u.$G)(),{locale:t}=(0,p.useRouter)(),[r,i]=(0,a.useState)(),[l,c]=(0,a.useState)(1),[f,m]=(0,a.useState)(),{data:b,isLoading:g,error:v}=(0,O.UE)(),{notifyLogs:x,loading:h,paginatorInfo:y,error:j}=(0,_.z6)({receiver:null==b?void 0:b.id,notify_type:r,language:t,limit:30,orderBy:"created_at",sortedBy:F.As.Desc,page:l});return h?(0,o.jsx)(d.Z,{text:e("common:text-loading")}):v?(0,o.jsx)(s.Z,{message:v.message}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(n.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,o.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,o.jsx)(D.Z,{title:e("form:form-title-all-notifications")})}),(0,o.jsx)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:w-2/3 md:flex-row md:space-y-0 xl:w-3/4 2xl:w-2/5",children:(0,o.jsx)(NotificationFilter,{className:"md:ms-6",onTargetFilter:e=>{i(null==e?void 0:e.value),c(1)},onReadStatusFilter:e=>{m(null==e?void 0:e.value),c(1)},showTargetFilter:!0})})]}),(0,o.jsx)("div",{className:"space-y-3.5",children:x?null==x?void 0:x.map(e=>(0,o.jsx)(NotifyLogItem,{item:e},e.id)):(0,o.jsx)(C.Z,{text:"text-no-log-found",className:"mx-auto w-7/12"})})]})}NotifyLogsPage.authenticate={permissions:f.M$},NotifyLogsPage.Layout=l.default},23157:function(e,t,r){"use strict";r.d(t,{ZP:function(){return a}});var o=r(65342),n=r(87462),i=r(67294),l=r(76416);r(48711),r(73935),r(73469);var a=(0,i.forwardRef)(function(e,t){var r=(0,o.u)(e);return i.createElement(l.S,(0,n.Z)({ref:t},r))})},97326:function(e,t,r){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}r.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,r){"use strict";r.d(t,{Z:function(){return _createClass}});var o=r(83997);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,o.Z)(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,r){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}r.d(t,{Z:function(){return _createSuper}});var o=r(71002),n=r(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var r,i=_getPrototypeOf(e);if(t){var l=_getPrototypeOf(this).constructor;r=Reflect.construct(i,arguments,l)}else r=i.apply(this,arguments);return function(e,t){if(t&&("object"==(0,o.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,r)}}},60136:function(e,t,r){"use strict";r.d(t,{Z:function(){return _inherits}});var o=r(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,o.Z)(e,t)}},1413:function(e,t,r){"use strict";r.d(t,{Z:function(){return _objectSpread2}});var o=r(4942);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){(0,o.Z)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,6994,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=22105)}),_N_E=e.O()}]);