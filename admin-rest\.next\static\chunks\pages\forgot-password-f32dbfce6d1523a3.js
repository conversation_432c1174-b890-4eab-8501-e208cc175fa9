(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9742,9930],{27627:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/forgot-password",function(){return s(32856)}])},29160:function(e,t,s){"use strict";s.d(t,{Z:function(){return AuthPageLayout}});var n=s(85893),o=s(51237);function AuthPageLayout(e){let{children:t}=e;return(0,n.jsx)("div",{className:"flex h-screen items-center justify-center bg-light sm:bg-gray-100",children:(0,n.jsxs)("div",{className:"m-auto w-full max-w-[420px] rounded bg-light p-5 sm:p-8 sm:shadow",children:[(0,n.jsx)("div",{className:"mb-2 flex justify-center",children:(0,n.jsx)(o.Z,{})}),t]})})}s(67294)},21587:function(e,t,s){"use strict";var n=s(85893),o=s(93967),r=s.n(o),u=s(5114),i=s(98388);let a={info:"bg-blue-100 text-blue-600",warning:"bg-yellow-100 text-yellow-600",error:"bg-red-100 text-red-500",success:"bg-green-100 text-accent",infoOutline:"border border-blue-200 text-blue-600",warningOutline:"border border-yellow-200 text-yellow-600",errorOutline:"border border-red-200 text-red-600",successOutline:"border border-green-200 text-green-600"};t.Z=e=>{let{message:t="",closeable:s=!1,variant:o="info",className:l,onClose:c,children:d,childClassName:f}=e;return(0,n.jsxs)("div",{className:(0,i.m6)(r()("relative flex items-center justify-between rounded py-4 px-5 shadow-sm",a[o],l)),role:"alert",children:[(0,n.jsxs)("div",{className:(0,i.m6)(r()(f)),children:[(0,n.jsx)("p",{className:"text-sm",children:t}),d]}),s&&(0,n.jsx)("button",{"data-dismiss":"alert","aria-label":"Close",onClick:c,title:"Close alert",className:"absolute top-1/2 -mt-3 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full text-red-500 transition-colors duration-200 -me-0.5 end-2 hover:bg-gray-300 hover:bg-opacity-25 focus:bg-gray-300 focus:bg-opacity-25 focus:outline-none",children:(0,n.jsx)("span",{"aria-hidden":"true",children:(0,n.jsx)(u.T,{className:"h-3 w-3"})})})]})}},51237:function(e,t,s){"use strict";var n=s(85893),o=s(8152),r=s(93967),u=s.n(r),i=s(99494),a=s(48583),l=s(79362),c=s(62964),d=s(25675),f=s.n(d),m=s(11163),S=s(90573);t.Z=e=>{var t,s,r,d,g,v,P,h,p,E,y;let{className:M,...A}=e,{locale:N}=(0,m.useRouter)(),{settings:b}=(0,S.n)({language:N}),[w,Q]=(0,a.KO)(l.Hf),{width:_}=(0,c.Z)();return(0,n.jsx)(o.Z,{href:null===i.siteSettings||void 0===i.siteSettings?void 0:null===(t=i.siteSettings.logo)||void 0===t?void 0:t.href,className:u()("inline-flex items-center gap-3",M),children:w&&_>=l.h2?(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:i.siteSettings.collapseLogo.width,height:i.siteSettings.collapseLogo.height},children:(0,n.jsx)(f(),{src:null!==(h=null==b?void 0:null===(r=b.options)||void 0===r?void 0:null===(s=r.collapseLogo)||void 0===s?void 0:s.original)&&void 0!==h?h:i.siteSettings.collapseLogo.url,alt:null!==(p=null==b?void 0:null===(d=b.options)||void 0===d?void 0:d.siteTitle)&&void 0!==p?p:i.siteSettings.collapseLogo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})}):(0,n.jsx)("span",{className:"relative overflow-hidden ",style:{width:i.siteSettings.logo.width,height:i.siteSettings.logo.height},children:(0,n.jsx)(f(),{src:null!==(E=null==b?void 0:null===(v=b.options)||void 0===v?void 0:null===(g=v.logo)||void 0===g?void 0:g.original)&&void 0!==E?E:i.siteSettings.logo.url,alt:null!==(y=null==b?void 0:null===(P=b.options)||void 0===P?void 0:P.siteTitle)&&void 0!==y?y:i.siteSettings.logo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})})})}},44498:function(e,t,s){"use strict";s.d(t,{B:function(){return r}});var n=s(47869),o=s(3737);let r={me:()=>o.eN.get(n.P.ME),login:e=>o.eN.post(n.P.TOKEN,e),logout:()=>o.eN.post(n.P.LOGOUT,{}),register:e=>o.eN.post(n.P.REGISTER,e),update:e=>{let{id:t,input:s}=e;return o.eN.put("".concat(n.P.USERS,"/").concat(t),s)},changePassword:e=>o.eN.post(n.P.CHANGE_PASSWORD,e),forgetPassword:e=>o.eN.post(n.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>o.eN.post(n.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>o.eN.post(n.P.RESET_PASSWORD,e),makeAdmin:e=>o.eN.post(n.P.MAKE_ADMIN,e),block:e=>o.eN.post(n.P.BLOCK_USER,e),unblock:e=>o.eN.post(n.P.UNBLOCK_USER,e),addWalletPoints:e=>o.eN.post(n.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>o.eN.post(n.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...s}=e;return o.eN.get(n.P.USERS,{searchJoin:"and",with:"wallet",...s,search:o.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return o.eN.get(n.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return o.eN.get("".concat(n.P.USERS,"/").concat(t))},resendVerificationEmail:()=>o.eN.post(n.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return o.eN.post(n.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...s}=e;return o.eN.get(n.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...s})},fetchCustomers:e=>{let{...t}=e;return o.eN.get(n.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:s,name:r,...u}=e;return o.eN.get(n.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...u,search:o.eN.formatSearchParams({name:r,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:s,...r}=e;return o.eN.get(n.P.ALL_STAFFS,{searchJoin:"and",...r,search:o.eN.formatSearchParams({name:s,is_active:t})})}}},99930:function(e,t,s){"use strict";s.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var n=s(79362),o=s(97514),r=s(31955),u=s(5233),i=s(11163),a=s(88767),l=s(22920),c=s(47869),d=s(44498),f=s(28597),m=s(87066),S=s(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,i.useRouter)();return(0,a.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===o.Z.verifyLicense&&t.replace(o.Z.dashboard),t.pathname===o.Z.verifyEmail&&((0,S.Fu)(!0),t.replace(o.Z.dashboard))},onError:s=>{if(m.Z.isAxiosError(s)){var n,r;if((null===(n=s.response)||void 0===n?void 0:n.status)===417){t.replace(o.Z.verifyLicense);return}if((null===(r=s.response)||void 0===r?void 0:r.status)===409){(0,S.Fu)(!1),t.replace(o.Z.verifyEmail);return}e.clear(),t.replace(o.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,i.useRouter)(),{t}=(0,u.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{r.Z.remove(n.E$),e.replace(o.Z.login),l.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,u.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{l.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};l.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,u.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{l.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,l.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,u.$G)();(0,a.useQueryClient)();let t=(0,i.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{l.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{l.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,u.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{l.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,u.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{l.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,u.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{l.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}},useAdminsQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}},useVendorsQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}},useCustomersQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}},useMyStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}},useAllStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:o}=(0,a.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,f.Q)(s),loading:n,error:o}}},32856:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return v},default:function(){return ForgotPasswordPage}});var n=s(85893),o=s(67294),r=s(21587),u=s(99930),i=s(5152),a=s.n(i),l=s(11163),c=s.n(l),d=s(5233);let f=a()(()=>Promise.all([s.e(6342),s.e(4750),s.e(5921),s.e(7536),s.e(2216),s.e(7393)]).then(s.bind(s,47393)),{loadableGenerated:{webpack:()=>[47393]}}),m=a()(()=>Promise.all([s.e(6342),s.e(4750),s.e(5921),s.e(7536),s.e(2216),s.e(4452)]).then(s.bind(s,84452)),{loadableGenerated:{webpack:()=>[84452]}}),S=a()(()=>Promise.all([s.e(7536),s.e(2216),s.e(7677)]).then(s.bind(s,87677)),{loadableGenerated:{webpack:()=>[87677]}});var forget_password=()=>{let{t:e}=(0,d.$G)(),{mutate:t,isLoading:s}=(0,u.xy)(),{mutate:i,isLoading:a}=(0,u.AV)(),{mutate:l,isLoading:g}=(0,u.gL)(),[v,P]=(0,o.useState)(""),[h,p]=(0,o.useState)(""),[E,y]=(0,o.useState)("");return(0,n.jsxs)(n.Fragment,{children:[v&&(0,n.jsx)(r.Z,{variant:"error",message:e("common:".concat(v)),className:"mb-6",closeable:!0,onClose:()=>P("")}),!h&&(0,n.jsx)(f,{loading:s,onSubmit:function(e){let{email:s}=e;t({email:s},{onSuccess:e=>{(null==e?void 0:e.success)?p(s):P(null==e?void 0:e.message)}})}}),h&&!E&&(0,n.jsx)(m,{loading:a,onSubmit:function(e){let{token:t}=e;i({email:h,token:t},{onSuccess:e=>{(null==e?void 0:e.success)?y(t):P(null==e?void 0:e.message)}})}}),h&&E&&(0,n.jsx)(S,{loading:g,onSubmit:function(e){let{password:t}=e;l({email:h,token:E,password:t},{onSuccess:e=>{(null==e?void 0:e.success)?c().push("/"):P(null==e?void 0:e.message)}})}})]})},g=s(29160),v=!0;function ForgotPasswordPage(){let{t:e}=(0,d.$G)();return(0,n.jsxs)(g.Z,{children:[(0,n.jsx)("h3",{className:"mb-6 mt-4 text-center text-base italic text-body",children:e("form:form-title-forgot-password")}),(0,n.jsx)(forget_password,{})]})}}},function(e){e.O(0,[2964,9494,9774,2888,179],function(){return e(e.s=27627)}),_N_E=e.O()}]);