(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6736],{21742:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/groups",function(){return n(56086)}])},35484:function(e,t,n){"use strict";var r=n(85893),l=n(93967),o=n.n(l),a=n(98388);t.Z=e=>{let{title:t,className:n,...l}=e;return(0,r.jsx)("h2",{className:(0,a.m6)(o()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...l,children:t})}},37912:function(e,t,n){"use strict";var r=n(85893),l=n(5114),o=n(80287),a=n(93967),u=n.n(a),s=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:m,...v}=e,{register:b,handleSubmit:y,watch:P,reset:h,formState:{errors:g}}=(0,i.cI)({defaultValues:{searchText:""}}),x=P("searchText"),{t:S}=(0,c.$G)();(0,s.useEffect)(()=>{x||n({searchText:""})},[x]);let E=u()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(u()("relative flex w-full items-center",t)),onSubmit:y(n),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(o.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(E),placeholder:null!=m?m:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),g.searchText&&(0,r.jsx)("p",{children:g.searchText.message}),!!x&&(0,r.jsx)("button",{type:"button",onClick:function(){h(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(l.T,{className:"h-5 w-5"})})]})}},16720:function(e,t,n){"use strict";n.d(t,{jT:function(){return useCreateTypeMutation},e7:function(){return useDeleteTypeMutation},F2:function(){return useTypeQuery},qs:function(){return useTypesQuery},oy:function(){return useUpdateTypeMutation}});var r=n(11163),l=n.n(r),o=n(88767),a=n(22920),u=n(5233),s=n(97514),i=n(47869),c=n(55191),d=n(3737);let p={...(0,c.h)(i.P.TYPES),all:e=>{let{name:t,...n}=e;return d.eN.get(i.P.TYPES,{searchJoin:"and",...n,search:d.eN.formatSearchParams({name:t})})}};var f=n(93345);let useCreateTypeMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.create,{onSuccess:()=>{l().push(s.Z.type.list,void 0,{locale:f.Config.defaultLanguage}),a.Am.success(e("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(i.P.TYPES)}})},useDeleteTypeMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,u.$G)();return(0,o.useMutation)(p.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.TYPES)}})},useUpdateTypeMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,r.useRouter)(),n=(0,o.useQueryClient)();return(0,o.useMutation)(p.update,{onSuccess:async n=>{let r=t.query.shop?"/".concat(t.query.shop).concat(s.Z.type.list):s.Z.type.list;await t.push("".concat(r,"/").concat(null==n?void 0:n.slug,"/edit"),void 0,{locale:f.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(i.P.TYPES)}})},useTypeQuery=e=>{let{slug:t,language:n}=e;return(0,o.useQuery)([i.P.TYPES,{slug:t,language:n}],()=>p.get({slug:t,language:n}))},useTypesQuery=e=>{let{data:t,isLoading:n,error:r}=(0,o.useQuery)([i.P.TYPES,e],e=>{let{queryKey:t,pageParam:n}=e;return p.all(Object.assign({},t[1],n))},{keepPreviousData:!0});return{types:null!=t?t:[],loading:n,error:r}}},56086:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return I},default:function(){return TypesPage}});var r=n(85893),l=n(92072),o=n(35484),a=n(37912),u=n(27899),s=n(10265),i=n(47559),c=n(5766),d=n(5233),p=n(76518),f=n(67294),m=n(78998),v=n(97514),b=n(34927),y=n(77556),group_list=e=>{let{types:t,onSort:n,onOrder:l}=e,{t:o}=(0,d.$G)(),{alignLeft:a,alignRight:P}=(0,p.S)(),[h,g]=(0,f.useState)({sort:s.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{n(e=>e===s.As.Desc?s.As.Asc:s.As.Desc),l(e),g({sort:h.sort===s.As.Desc?s.As.Asc:s.As.Desc,column:e})}}),x=[{title:(0,r.jsx)(m.Z,{title:o("table:table-item-id"),ascending:h.sort===s.As.Asc&&"id"===h.column,isActive:"id"===h.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:a,width:150,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(o("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(m.Z,{title:o("table:table-homepage-name"),ascending:h.sort===s.As.Asc&&"name"===h.column,isActive:"name"===h.column}),width:220,className:"cursor-pointer",dataIndex:"name",key:"name",align:a,onHeaderCell:()=>onHeaderClick("name"),render:e=>(0,r.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:o("table:table-item-slug"),dataIndex:"slug",key:"slug",align:"center",width:220,ellipsis:!0},{title:o("table:table-item-icon"),dataIndex:"icon",key:"slug",align:"center",width:150,render:e=>e?(0,r.jsx)("span",{className:"flex items-center justify-center",children:(0,i.q)({iconList:c,iconName:e,className:"w-5 h-5 max-h-full max-w-full"})}):null},{title:o("table:table-item-actions"),dataIndex:"slug",key:"actions",align:P,width:120,render:(e,t)=>(0,r.jsx)(b.Z,{slug:e,record:t,deleteModalView:"DELETE_TYPE",routes:null===v.Z||void 0===v.Z?void 0:v.Z.type})}];return(0,r.jsx)("div",{className:"mb-8 overflow-hidden rounded shadow",children:(0,r.jsx)(u.i,{columns:x,emptyText:()=>(0,r.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,r.jsx)(y.m,{className:"w-52"}),(0,r.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:o("table:empty-table-data")}),(0,r.jsx)("p",{className:"text-[13px]",children:o("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:380}})})},P=n(97670),h=n(45957),g=n(61616),x=n(55846),S=n(93345),E=n(16720),T=n(16203),N=n(11163),I=!0;function TypesPage(){let{locale:e}=(0,N.useRouter)(),{t}=(0,d.$G)(),[n,u]=(0,f.useState)("created_at"),[i,c]=(0,f.useState)(s.As.Desc),[p,m]=(0,f.useState)(""),{types:b,loading:y,error:P}=(0,E.qs)({name:p,language:e,orderBy:n,sortedBy:i});return y?(0,r.jsx)(x.Z,{text:t("common:text-loading")}):P?(0,r.jsx)(h.Z,{message:P.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,r.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,r.jsx)(o.Z,{title:t("common:sidebar-nav-item-groups")})}),(0,r.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-1/2",children:[(0,r.jsx)(a.Z,{onSearch:function(e){let{searchText:t}=e;m(t)},placeholderText:t("form:input-placeholder-search-name")}),e===S.Config.defaultLanguage&&(0,r.jsxs)(g.Z,{href:v.Z.type.create,className:"h-12 w-full md:w-auto md:ms-6",children:[(0,r.jsxs)("span",{className:"block md:hidden xl:block",children:["+ ",t("form:button-label-add-group")]}),(0,r.jsxs)("span",{className:"hidden md:block xl:hidden",children:["+ ",t("form:button-label-add")]})]})]})]}),(0,r.jsx)(group_list,{types:b,onOrder:u,onSort:c})]})}TypesPage.authenticate={permissions:T.M$},TypesPage.Layout=P.default},28368:function(e,t,n){"use strict";n.d(t,{p:function(){return I}});var r,l,o,a=n(67294),u=n(32984),s=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),m=n(14157),v=n(15466),b=n(73781);let y=null!=(o=a.startTransition)?o:function(e){e()};var P=((r=P||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),h=((l=h||{})[l.ToggleDisclosure=0]="ToggleDisclosure",l[l.CloseDisclosure=1]="CloseDisclosure",l[l.SetButtonId=2]="SetButtonId",l[l.SetPanelId=3]="SetPanelId",l[l.LinkPanel=4]="LinkPanel",l[l.UnlinkPanel=5]="UnlinkPanel",l);let g={0:e=>({...e,disclosureState:(0,u.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},x=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(x);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}x.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let E=(0,a.createContext)(null);function Y(e,t){return(0,u.E)(t.type,g,e,t)}E.displayName="DisclosurePanelContext";let T=a.Fragment,N=s.AN.RenderStrategy|s.AN.Static,I=Object.assign((0,s.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,l=(0,a.useRef)(null),o=(0,i.T)(t,(0,i.h)(e=>{l.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:y},P]=p,h=(0,b.z)(e=>{P({type:1});let t=(0,v.r)(l);if(!t||!y)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(y):t.getElementById(y);null==n||n.focus()}),g=(0,a.useMemo)(()=>({close:h}),[h]),E=(0,a.useMemo)(()=>({open:0===m,close:h}),[m,h]);return a.createElement(x.Provider,{value:p},a.createElement(S.Provider,{value:g},a.createElement(f.up,{value:(0,u.E)(m,{0:f.ZM.Open,1:f.ZM.Closed})},(0,s.sY)({ourProps:{ref:o},theirProps:r,slot:E,defaultTag:T,name:"Disclosure"}))))}),{Button:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-button-${n}`,...l}=e,[o,u]=M("Disclosure.Button"),f=(0,a.useContext)(E),v=null!==f&&f===o.panelId,y=(0,a.useRef)(null),P=(0,i.T)(y,t,v?null:o.buttonRef);(0,a.useEffect)(()=>{if(!v)return u({type:2,buttonId:r}),()=>{u({type:2,buttonId:null})}},[r,u,v]);let h=(0,b.z)(e=>{var t;if(v){if(1===o.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0})}}),g=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),x=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(v?(u({type:0}),null==(n=o.buttonRef.current)||n.focus()):u({type:0}))}),S=(0,a.useMemo)(()=>({open:0===o.disclosureState}),[o]),T=(0,m.f)(e,y),N=v?{ref:P,type:T,onKeyDown:h,onClick:x}:{ref:P,id:r,type:T,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:h,onKeyUp:g,onClick:x};return(0,s.sY)({ourProps:N,theirProps:l,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-panel-${n}`,...l}=e,[o,u]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,o.panelRef,e=>{y(()=>u({type:e?4:5}))});(0,a.useEffect)(()=>(u({type:3,panelId:r}),()=>{u({type:3,panelId:null})}),[r,u]);let m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===o.disclosureState,b=(0,a.useMemo)(()=>({open:0===o.disclosureState,close:d}),[o,d]);return a.createElement(E.Provider,{value:o.panelId},(0,s.sY)({ourProps:{ref:p,id:r},theirProps:l,slot:b,defaultTag:"div",features:N,visible:v,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){"use strict";n.d(t,{J:function(){return Z}});var r,l,o=n(67294),a=n(32984),u=n(12351),s=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),m=n(14157),v=n(39650),b=n(15466),y=n(51074),P=n(14007),h=n(46045),g=n(73781),x=n(45662),S=n(3855),E=n(16723),T=n(65958),N=n(2740),I=((r=I||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),C=((l=C||{})[l.TogglePopover=0]="TogglePopover",l[l.ClosePopover=1]="ClosePopover",l[l.SetButton=2]="SetButton",l[l.SetButtonId=3]="SetButtonId",l[l.SetPanel=4]="SetPanel",l[l.SetPanelId=5]="SetPanelId",l);let k={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},j=(0,o.createContext)(null);function oe(e){let t=(0,o.useContext)(j);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}j.displayName="PopoverContext";let D=(0,o.createContext)(null);function fe(e){let t=(0,o.useContext)(D);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}D.displayName="PopoverAPIContext";let O=(0,o.createContext)(null);function Ee(){return(0,o.useContext)(O)}O.displayName="PopoverGroupContext";let R=(0,o.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,k,e,t)}R.displayName="PopoverPanelContext";let A=u.AN.RenderStrategy|u.AN.Static,_=u.AN.RenderStrategy|u.AN.Static,Z=Object.assign((0,u.yV)(function(e,t){var n;let{__demoMode:r=!1,...l}=e,i=(0,o.useRef)(null),c=(0,s.T)(t,(0,s.h)(e=>{i.current=e})),d=(0,o.useRef)([]),m=(0,o.useReducer)(Ne,{__demoMode:r,popoverState:r?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,o.createRef)(),afterPanelSentinel:(0,o.createRef)()}),[{popoverState:b,button:h,buttonId:x,panel:E,panelId:I,beforePanelSentinel:C,afterPanelSentinel:k},O]=m,A=(0,y.i)(null!=(n=i.current)?n:h),_=(0,o.useMemo)(()=>{if(!h||!E)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(h))^Number(null==e?void 0:e.contains(E)))return!0;let e=(0,p.GO)(),t=e.indexOf(h),n=(t+e.length-1)%e.length,r=(t+1)%e.length,l=e[n],o=e[r];return!E.contains(l)&&!E.contains(o)},[h,E]),Z=(0,S.E)(x),B=(0,S.E)(I),F=(0,o.useMemo)(()=>({buttonId:Z,panelId:B,close:()=>O({type:1})}),[Z,B,O]),z=Ee(),$=null==z?void 0:z.registerPopover,G=(0,g.z)(()=>{var e;return null!=(e=null==z?void 0:z.isFocusWithinPopoverGroup())?e:(null==A?void 0:A.activeElement)&&((null==h?void 0:h.contains(A.activeElement))||(null==E?void 0:E.contains(A.activeElement)))});(0,o.useEffect)(()=>null==$?void 0:$(F),[$,F]);let[L,Q]=(0,N.k)(),V=(0,T.v)({mainTreeNodeRef:null==z?void 0:z.mainTreeNodeRef,portals:L,defaultContainers:[h,E]});(0,P.O)(null==A?void 0:A.defaultView,"focus",e=>{var t,n,r,l;e.target!==window&&e.target instanceof HTMLElement&&0===b&&(G()||h&&E&&(V.contains(e.target)||null!=(n=null==(t=C.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(l=null==(r=k.current)?void 0:r.contains)&&l.call(r,e.target)||O({type:1})))},!0),(0,v.O)(V.resolveContainers,(e,t)=>{O({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==h||h.focus())},0===b);let H=(0,g.z)(e=>{O({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:h:h;null==t||t.focus()}),K=(0,o.useMemo)(()=>({close:H,isPortalled:_}),[H,_]),q=(0,o.useMemo)(()=>({open:0===b,close:H}),[b,H]);return o.createElement(R.Provider,{value:null},o.createElement(j.Provider,{value:m},o.createElement(D.Provider,{value:K},o.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},o.createElement(Q,null,(0,u.sY)({ourProps:{ref:c},theirProps:l,slot:q,defaultTag:"div",name:"Popover"}),o.createElement(V.MainTreeNode,null))))))}),{Button:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-button-${n}`,...l}=e,[f,v]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),P=(0,o.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,E=Ee(),T=null==E?void 0:E.closeOthers,N=null!==(0,o.useContext)(R);(0,o.useEffect)(()=>{if(!N)return v({type:3,buttonId:r}),()=>{v({type:3,buttonId:null})}},[N,r,v]);let[I]=(0,o.useState)(()=>Symbol()),C=(0,s.T)(P,t,N?null:e=>{if(e)f.buttons.current.push(I);else{let e=f.buttons.current.indexOf(I);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&v({type:2,button:e})}),k=(0,s.T)(P,t),j=(0,y.i)(P),D=(0,g.z)(e=>{var t,n,r;if(N){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),v({type:1}),null==(r=f.button)||r.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==T||T(f.buttonId)),v({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==T?void 0:T(f.buttonId);if(!P.current||null!=j&&j.activeElement&&!P.current.contains(j.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1})}}),O=(0,g.z)(e=>{N||e.key===c.R.Space&&e.preventDefault()}),A=(0,g.z)(t=>{var n,r;(0,d.P)(t.currentTarget)||e.disabled||(N?(v({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==T||T(f.buttonId)),v({type:0}),null==(r=f.button)||r.focus()))}),_=(0,g.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===f.popoverState,B=(0,o.useMemo)(()=>({open:Z}),[Z]),F=(0,m.f)(e,P),z=N?{ref:k,type:F,onKeyDown:D,onClick:A}:{ref:C,id:f.buttonId,type:F,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:D,onKeyUp:O,onClick:A,onMouseDown:_},$=(0,x.l)(),G=(0,g.z)(()=>{let e=f.panel;e&&(0,a.E)($.current,{[x.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[x.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)($.current,{[x.N.Forwards]:p.TO.Next,[x.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return o.createElement(o.Fragment,null,(0,u.sY)({ourProps:z,theirProps:l,slot:B,defaultTag:"button",name:"Popover.Button"}),Z&&!N&&b&&o.createElement(h._,{id:S,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:G}))}),Overlay:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-overlay-${n}`,...l}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,s.T)(t),m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===a,b=(0,g.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),y=(0,o.useMemo)(()=>({open:0===a}),[a]);return(0,u.sY)({ourProps:{ref:p,id:r,"aria-hidden":!0,onClick:b},theirProps:l,slot:y,defaultTag:"div",features:A,visible:v,name:"Popover.Overlay"})}),Panel:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-panel-${n}`,focus:l=!1,...d}=e,[m,v]=oe("Popover.Panel"),{close:b,isPortalled:P}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,T=`headlessui-focus-sentinel-after-${(0,i.M)()}`,N=(0,o.useRef)(null),I=(0,s.T)(N,t,e=>{v({type:4,panel:e})}),C=(0,y.i)(N);(0,E.e)(()=>(v({type:5,panelId:r}),()=>{v({type:5,panelId:null})}),[r,v]);let k=(0,f.oJ)(),j=null!==k?(k&f.ZM.Open)===f.ZM.Open:0===m.popoverState,D=(0,g.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==m.popoverState||!N.current||null!=C&&C.activeElement&&!N.current.contains(C.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1}),null==(t=m.button)||t.focus()}});(0,o.useEffect)(()=>{var t;e.static||1===m.popoverState&&(null==(t=e.unmount)||t)&&v({type:4,panel:null})},[m.popoverState,e.unmount,e.static,v]),(0,o.useEffect)(()=>{if(m.__demoMode||!l||0!==m.popoverState||!N.current)return;let e=null==C?void 0:C.activeElement;N.current.contains(e)||(0,p.jA)(N.current,p.TO.First)},[m.__demoMode,l,N,m.popoverState]);let O=(0,o.useMemo)(()=>({open:0===m.popoverState,close:b}),[m,b]),A={ref:I,id:r,onKeyDown:D,onBlur:l&&0===m.popoverState?e=>{var t,n,r,l,o;let a=e.relatedTarget;a&&N.current&&(null!=(t=N.current)&&t.contains(a)||(v({type:1}),(null!=(r=null==(n=m.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,a)||null!=(o=null==(l=m.afterPanelSentinel.current)?void 0:l.contains)&&o.call(l,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,x.l)(),B=(0,g.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[x.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=m.afterPanelSentinel.current)||t.focus())},[x.N.Backwards]:()=>{var e;null==(e=m.button)||e.focus({preventScroll:!0})}})}),F=(0,g.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[x.N.Forwards]:()=>{var e;if(!m.button)return;let t=(0,p.GO)(),n=t.indexOf(m.button),r=t.slice(0,n+1),l=[...t.slice(n+1),...r];for(let t of l.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=m.panel)&&e.contains(t)){let e=l.indexOf(t);-1!==e&&l.splice(e,1)}(0,p.jA)(l,p.TO.First,{sorted:!1})},[x.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=m.button)||t.focus())}})});return o.createElement(R.Provider,{value:r},j&&P&&o.createElement(h._,{id:S,ref:m.beforePanelSentinel,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:B}),(0,u.sY)({ourProps:A,theirProps:d,slot:O,defaultTag:"div",features:_,visible:j,name:"Popover.Panel"}),j&&P&&o.createElement(h._,{id:T,ref:m.afterPanelSentinel,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}))}),Group:(0,u.yV)(function(e,t){let n=(0,o.useRef)(null),r=(0,s.T)(n,t),[l,a]=(0,o.useState)([]),i=(0,T.H)(),c=(0,g.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,g.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,g.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||l.some(e=>{var n,l;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(l=t.getElementById(e.panelId.current))?void 0:l.contains(r))})}),f=(0,g.z)(e=>{for(let t of l)t.buttonId.current!==e&&t.close()}),m=(0,o.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),v=(0,o.useMemo)(()=>({}),[]);return o.createElement(O.Provider,{value:m},(0,u.sY)({ourProps:{ref:r},theirProps:e,slot:v,defaultTag:"div",name:"Popover.Group"}),o.createElement(i.MainTreeNode,null))})})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6775,9494,5535,8186,1285,1631,7556,8504,2713,778,9774,2888,179],function(){return e(e.s=21742)}),_N_E=e.O()}]);