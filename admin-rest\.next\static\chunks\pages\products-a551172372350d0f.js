(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7345],{74918:function(e,l,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products",function(){return t(36693)}])},97670:function(e,l,t){"use strict";t.r(l);var s=t(85893),r=t(78985),a=t(79362),i=t(8144),n=t(74673),d=t(99494),o=t(5233),c=t(1631),u=t(11163),m=t(48583),x=t(93967),f=t.n(x),p=t(30824),h=t(62964);let SidebarItemMap=e=>{let{menuItems:l}=e,{t}=(0,o.$G)(),[r,i]=(0,m.KO)(a.Hf),{childMenu:n}=l,{width:d}=(0,h.Z)();return(0,s.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:l,label:i,icon:n,childMenu:o}=e;return(0,s.jsx)(c.Z,{href:l,label:t(i),icon:n,childMenu:o,miniSidebar:r&&d>=a.h2},i)})})},SideBarGroup=()=>{var e;let{t:l}=(0,o.$G)(),[t,r]=(0,m.KO)(a.Hf),i=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(i),{width:c}=(0,h.Z)();return(0,s.jsx)(s.Fragment,{children:null==n?void 0:n.map((e,r)=>{var n;return(0,s.jsxs)("div",{className:f()("flex flex-col px-5",t&&c>=a.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",t&&c>=a.h2?"hidden":""),children:l(null===(n=i[e])||void 0===n?void 0:n.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:i[e]})]},r)})})};l.default=e=>{let{children:l}=e,{locale:t}=(0,u.useRouter)(),[d,o]=(0,m.KO)(a.Hf),[c]=(0,m.KO)(a.GH),[x]=(0,m.KO)(a.W4),{width:g}=(0,h.Z)();return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===t||"he"===t?"rtl":"ltr",children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(n.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=a.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-20",d&&g>=a.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,s.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=a.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&g>=a.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,s.jsx)(i.Z,{})]})]})]})}},36693:function(e,l,t){"use strict";t.r(l),t.d(l,{__N_SSG:function(){return N},default:function(){return ProductsPage}});var s=t(85893),r=t(92072),a=t(37912),i=t(85634),n=t(46626),d=t(97670),o=t(15724),c=t(145),u=t(45957),m=t(55846),x=t(93242),f=t(10265),p=t(16203),h=t(93967),g=t.n(h),b=t(5233),j=t(11163),v=t(67294),w=t(35484),N=!0;function ProductsPage(){let[e,l]=(0,v.useState)(""),[t,d]=(0,v.useState)(""),[p,h]=(0,v.useState)(""),[N,S]=(0,v.useState)(""),[y,_]=(0,v.useState)(1),{t:Z}=(0,b.$G)(),{locale:P}=(0,j.useRouter)(),[k,O]=(0,v.useState)("created_at"),[G,T]=(0,v.useState)(f.As.Desc),[K,C]=(0,v.useState)(!0),{products:E,loading:F,paginatorInfo:H,error:I}=(0,x.kN)({language:P,limit:20,page:y,type:t,categories:p,product_type:N,name:e,orderBy:k,sortedBy:G});return F?(0,s.jsx)(m.Z,{text:Z("common:text-loading")}):I?(0,s.jsx)(u.Z,{message:I.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(r.Z,{className:"mb-8 flex flex-col",children:[(0,s.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(w.Z,{title:Z("form:input-label-products")})}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center ms-auto md:w-2/4",children:(0,s.jsx)(a.Z,{onSearch:function(e){let{searchText:t}=e;l(t),_(1)},placeholderText:Z("form:input-placeholder-search-name")})}),(0,s.jsxs)("button",{className:"mt-5 flex items-center whitespace-nowrap text-base font-semibold text-accent md:mt-0 md:ms-5",onClick:()=>{C(e=>!e)},children:[Z("common:text-filter")," ",K?(0,s.jsx)(n.a,{className:"ms-2"}):(0,s.jsx)(i.K,{className:"ms-2"})]})]}),(0,s.jsx)("div",{className:g()("flex w-full transition",{"visible h-auto":K,"invisible h-0":!K}),children:(0,s.jsx)("div",{className:"mt-5 flex w-full flex-col border-t border-gray-200 pt-5 md:mt-8 md:flex-row md:items-center md:pt-8",children:(0,s.jsx)(o.Z,{className:"w-full",type:t,onCategoryFilter:e=>{h(null==e?void 0:e.slug),_(1)},onTypeFilter:e=>{d(null==e?void 0:e.slug),_(1)},onProductTypeFilter:e=>{S(null==e?void 0:e.slug),_(1)},enableCategory:!0,enableType:!0,enableProductType:!0})})})]}),(0,s.jsx)(c.Z,{products:E,paginatorInfo:H,onPagination:function(e){_(e)},onOrder:O,onSort:T})]})}ProductsPage.authenticate={permissions:p.M$},ProductsPage.Layout=d.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1077,193,9774,2888,179],function(){return e(e.s=74918)}),_N_E=e.O()}]);