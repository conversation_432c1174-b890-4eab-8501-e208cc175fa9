const { Sequelize } = require('sequelize');

// Database connection using the same config as the app
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: 'ep-bitter-flower-a1q5lyqy-pooler.ap-southeast-1.aws.neon.tech',
  port: 5432,
  username: 'ecommerce_owner',
  password: 'npg_aI0Dn8AMfbWj',
  database: 'ecommerce',
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  logging: false,
});

async function seedData() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully.');

    // Clear existing data
    console.log('Clearing existing data...');
    await sequelize.query('DELETE FROM product_tags');
    await sequelize.query('DELETE FROM product_categories');
    await sequelize.query('DELETE FROM products');
    await sequelize.query('DELETE FROM shops');
    await sequelize.query('DELETE FROM users');
    await sequelize.query('DELETE FROM tags');
    await sequelize.query('DELETE FROM categories');
    await sequelize.query('DELETE FROM types');

    // Reset sequences
    await sequelize.query('ALTER SEQUENCE types_id_seq RESTART WITH 1');
    await sequelize.query('ALTER SEQUENCE categories_id_seq RESTART WITH 1');
    await sequelize.query('ALTER SEQUENCE tags_id_seq RESTART WITH 1');
    await sequelize.query('ALTER SEQUENCE users_id_seq RESTART WITH 1');
    await sequelize.query('ALTER SEQUENCE shops_id_seq RESTART WITH 1');
    await sequelize.query('ALTER SEQUENCE products_id_seq RESTART WITH 1');

    // Insert Types
    console.log('Inserting types...');
    await sequelize.query(`
      INSERT INTO types (name, slug, image, icon, promotional_sliders, settings, language, created_at, updated_at, "createdAt", "updatedAt")
      VALUES 
        ('General', 'general', '{}', 'ShoppingBagIcon', '[]', '{"isHome": true, "layoutType": "classic", "productCard": "neon"}', 'en', NOW(), NOW(), NOW(), NOW()),
        ('Electronics', 'electronics', '{}', 'DeviceTabletIcon', '[]', '{"isHome": false, "layoutType": "classic", "productCard": "neon"}', 'en', NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Categories
    console.log('Inserting categories...');
    await sequelize.query(`
      INSERT INTO categories (name, slug, parent_id, type_id, details, image, icon, language, created_at, updated_at, "createdAt", "updatedAt")
      VALUES 
        ('Smartphones', 'smartphones', NULL, 2, '{}', '{}', 'DeviceMobileIcon', 'en', NOW(), NOW(), NOW(), NOW()),
        ('Laptops', 'laptops', NULL, 2, '{}', '{}', 'ComputerDesktopIcon', 'en', NOW(), NOW(), NOW(), NOW()),
        ('Clothing', 'clothing', NULL, 1, '{}', '{}', 'ShirtIcon', 'en', NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Tags
    console.log('Inserting tags...');
    await sequelize.query(`
      INSERT INTO tags (name, slug, details, image, icon, type_id, language, created_at, updated_at, "createdAt", "updatedAt")
      VALUES 
        ('Popular', 'popular', '{}', '{}', 'StarIcon', 1, 'en', NOW(), NOW(), NOW(), NOW()),
        ('New Arrival', 'new-arrival', '{}', '{}', 'SparklesIcon', 1, 'en', NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Users
    console.log('Inserting users...');
    await sequelize.query(`
      INSERT INTO users (name, email, password, is_active, created_at, updated_at, "createdAt", "updatedAt")
      VALUES ('Admin User', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', true, NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Shops
    console.log('Inserting shops...');
    await sequelize.query(`
      INSERT INTO shops (owner_id, name, slug, description, cover_image, logo, is_active, address, settings, created_at, updated_at, "createdAt", "updatedAt")
      VALUES (1, 'Demo Shop', 'demo-shop', 'A demo shop for testing purposes', '{}', '{}', true, 
              '{"street_address":"123 Demo Street","city":"Demo City","state":"Demo State","zip":"12345","country":"Demo Country"}',
              '{"contact":"<EMAIL>","website":"https://demo-shop.com"}', NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Products
    console.log('Inserting products...');
    await sequelize.query(`
      INSERT INTO products (name, slug, description, type_id, shop_id, price, sale_price, sku, quantity, in_stock, is_taxable, status, product_type, unit, height, length, width, image, gallery, language, created_at, updated_at, "createdAt", "updatedAt")
      VALUES 
        ('iPhone 15 Pro', 'iphone-15-pro', 'Latest iPhone with advanced features', 2, 1, 999.99, 899.99, 'IPH15PRO001', 50, true, true, 'publish', 'simple', 'piece', '5.77', '2.78', '0.32', 
         '{"id":1,"original":"https://via.placeholder.com/400x400/000000/FFFFFF?text=iPhone+15+Pro","thumbnail":"https://via.placeholder.com/150x150/000000/FFFFFF?text=iPhone+15+Pro"}',
         '[{"id":1,"original":"https://via.placeholder.com/400x400/000000/FFFFFF?text=iPhone+15+Pro","thumbnail":"https://via.placeholder.com/150x150/000000/FFFFFF?text=iPhone+15+Pro"}]',
         'en', NOW(), NOW(), NOW(), NOW()),
        ('MacBook Pro 16"', 'macbook-pro-16', 'Powerful laptop for professionals', 2, 1, 2499.99, 2299.99, 'MBP16001', 25, true, true, 'publish', 'simple', 'piece', '1.68', '14.01', '9.77',
         '{"id":2,"original":"https://via.placeholder.com/400x400/333333/FFFFFF?text=MacBook+Pro","thumbnail":"https://via.placeholder.com/150x150/333333/FFFFFF?text=MacBook+Pro"}',
         '[{"id":2,"original":"https://via.placeholder.com/400x400/333333/FFFFFF?text=MacBook+Pro","thumbnail":"https://via.placeholder.com/150x150/333333/FFFFFF?text=MacBook+Pro"}]',
         'en', NOW(), NOW(), NOW(), NOW()),
        ('Cotton T-Shirt', 'cotton-t-shirt', 'Comfortable cotton t-shirt for everyday wear', 1, 1, 29.99, 24.99, 'TSHIRT001', 100, true, true, 'publish', 'simple', 'piece', '0.5', '28', '20',
         '{"id":3,"original":"https://via.placeholder.com/400x400/0066CC/FFFFFF?text=T-Shirt","thumbnail":"https://via.placeholder.com/150x150/0066CC/FFFFFF?text=T-Shirt"}',
         '[{"id":3,"original":"https://via.placeholder.com/400x400/0066CC/FFFFFF?text=T-Shirt","thumbnail":"https://via.placeholder.com/150x150/0066CC/FFFFFF?text=T-Shirt"}]',
         'en', NOW(), NOW(), NOW(), NOW()),
        ('Wireless Headphones', 'wireless-headphones', 'High-quality wireless headphones with noise cancellation', 2, 1, 199.99, 149.99, 'HEADPHONES001', 75, true, true, 'publish', 'simple', 'piece', '8', '7', '6',
         '{"id":4,"original":"https://via.placeholder.com/400x400/FF6600/FFFFFF?text=Headphones","thumbnail":"https://via.placeholder.com/150x150/FF6600/FFFFFF?text=Headphones"}',
         '[{"id":4,"original":"https://via.placeholder.com/400x400/FF6600/FFFFFF?text=Headphones","thumbnail":"https://via.placeholder.com/150x150/FF6600/FFFFFF?text=Headphones"}]',
         'en', NOW(), NOW(), NOW(), NOW())
    `);

    // Insert Product-Category relationships
    console.log('Inserting product-category relationships...');
    await sequelize.query(`
      INSERT INTO product_categories (product_id, category_id)
      VALUES (1, 1), (2, 2), (3, 3), (4, 1)
    `);

    // Insert Product-Tag relationships
    console.log('Inserting product-tag relationships...');
    await sequelize.query(`
      INSERT INTO product_tags (product_id, tag_id)
      VALUES (1, 1), (1, 2), (2, 1), (4, 2)
    `);

    console.log('Sample data seeded successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    await sequelize.close();
  }
}

seedData();
