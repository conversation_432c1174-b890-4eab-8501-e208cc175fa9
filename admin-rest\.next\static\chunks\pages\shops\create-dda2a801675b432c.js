(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5258],{36970:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shops/create",function(){return r(25850)}])},25850:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return i},default:function(){return CreateShopPage}});var n=r(85893),a=r(82801),o=r(99313),s=r(16203),u=r(5233),i=!0;function CreateShopPage(){let{t:e}=(0,u.$G)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:form-title-create-shop")})}),(0,n.jsx)(o.Z,{})]})}CreateShopPage.authenticate={permissions:s.Zk},CreateShopPage.Layout=a.default}},function(e){e.O(0,[6342,1255,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,3261,9709,7755,7557,8680,9494,5535,8186,1285,5468,2801,8798,9774,2888,179],function(){return e(e.s=36970)}),_N_E=e.O()}]);