#!/bin/bash

echo "🚀 Starting E-commerce Platform with Docker Compose..."

# Create necessary directories
mkdir -p letsencrypt

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start all services
echo "🔨 Building and starting all services..."
docker-compose up --build -d

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 30

# Run database migrations and seeding
echo "🗄️ Setting up database..."
docker-compose exec api-rest npm run db:migrate
docker-compose exec api-rest node simple-seed.js

echo "✅ Setup complete!"
echo ""
echo "🌐 Access your services:"
echo "   • Shop Frontend: http://shop.localhost"
echo "   • Admin Panel: http://admin.localhost"
echo "   • API Documentation: http://api.localhost/docs"
echo "   • MinIO Console: http://minio-console.localhost"
echo "   • MinIO API: http://minio.localhost"
echo "   • Traefik Dashboard: http://traefik.localhost:8080"
echo ""
echo "📝 Default credentials:"
echo "   • Admin: <EMAIL> / password"
echo "   • MinIO: minioadmin / minioadmin123"
