(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2892],{66249:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/users",function(){return t(57886)}])},57886:function(e,s,t){"use strict";t.r(s),t.d(s,{__N_SSG:function(){return p},default:function(){return AllUsersPage}});var n=t(85893),a=t(92072),l=t(97670),r=t(37912),c=t(39816),u=t(61616),o=t(67294),i=t(45957),m=t(55846),d=t(99930),f=t(5233),x=t(97514),h=t(10265),_=t(16203),w=t(35484),p=!0;function AllUsersPage(){let[e,s]=(0,o.useState)(""),[t,l]=(0,o.useState)(1),{t:_}=(0,f.$G)(),[p,j]=(0,o.useState)("created_at"),[g,N]=(0,o.useState)(h.As.Desc),{users:b,paginatorInfo:S,loading:Z,error:P}=(0,d.xY)({limit:20,page:t,name:e,orderBy:p,sortedBy:g});return Z?(0,n.jsx)(m.Z,{text:_("common:text-loading")}):P?(0,n.jsx)(i.Z,{message:P.message}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(a.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,n.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,n.jsx)(w.Z,{title:_("form:input-label-users")})}),(0,n.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:w-3/4 md:flex-row md:space-y-0 xl:w-2/4",children:[(0,n.jsx)(r.Z,{onSearch:function(e){let{searchText:t}=e;s(t),l(1)},placeholderText:_("form:input-placeholder-search-name")}),(0,n.jsx)(u.Z,{href:"".concat(x.Z.user.create),className:"h-12 w-full md:w-auto md:ms-6",children:(0,n.jsxs)("span",{children:["+ ",_("form:button-label-add-user")]})})]})]}),Z?null:(0,n.jsx)(c.Z,{customers:b,paginatorInfo:S,onPagination:function(e){l(e)},onOrder:j,onSort:N})]})}AllUsersPage.authenticate={permissions:_.M$},AllUsersPage.Layout=l.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,8504,5217,9774,2888,179],function(){return e(e.s=66249)}),_N_E=e.O()}]);