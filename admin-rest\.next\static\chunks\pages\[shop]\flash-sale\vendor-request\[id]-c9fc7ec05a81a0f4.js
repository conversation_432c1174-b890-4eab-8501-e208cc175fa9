(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5896],{61582:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/flash-sale/vendor-request/[id]",function(){return s(41781)}])},52201:function(e,t,s){"use strict";var l=s(85893),a=s(5233),n=s(27484),u=s.n(n),o=s(93967),r=s.n(o),i=s(98388);t.Z=e=>{let{data:t,className:s,...n}=e,o=null==t?void 0:t.flash_sale,{t:d}=(0,a.$G)();return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:(0,i.m6)(r()(s)),...n,children:[(0,l.jsx)("h2",{className:"mb-8 border-b border-b-[#E5E5E5] pb-6 text-2xl font-semibold text-muted-black",children:"Flash sale request."}),(0,l.jsx)("div",{className:"relative overflow-hidden rounded-tl-[1.25rem] rounded-tr-[1.25rem] bg-white mb-5",children:(0,l.jsxs)("div",{className:"p-10",children:[(null==t?void 0:t.title)?(0,l.jsx)("h3",{className:"mb-4 text-xl font-semibold text-muted-black",children:null==t?void 0:t.title}):"",(null==o?void 0:o.description)?(0,l.jsx)("p",{className:"mb-8 text-base leading-[150%] text-[#666] lg:text-lg",children:null==o?void 0:o.description}):"",(0,l.jsxs)("ul",{className:"space-y-4 text-sm lg:text-base [&>li>p]:text-base-dark [&>li>span]:font-semibold [&>li>span]:text-muted-black [&>li]:flex [&>li]:items-center [&>li]:gap-1",children:[(null==o?void 0:o.sale_status)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-campaign-status")," : "]}),(0,l.jsx)("p",{children:(null==o?void 0:o.sale_status)?"On going":"On hold"})]}):"",(null==o?void 0:o.start_date)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("notice-active-date"),": "]}),(0,l.jsx)("p",{children:u()(null==o?void 0:o.start_date).format("DD MMM YYYY")})]}):"",(null==o?void 0:o.end_date)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("notice-expire-date"),": "]}),(0,l.jsx)("p",{children:u()(null==o?void 0:o.end_date).format("DD MMM YYYY")})]}):"",(null==o?void 0:o.type)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-campaign-type-on")," : "]}),(0,l.jsx)("p",{children:d(null==o?void 0:o.type)})]}):"",(null==o?void 0:o.rate)?(0,l.jsxs)("li",{children:[(0,l.jsxs)("span",{children:[d("text-deals-rate")," : "]}),(0,l.jsxs)("p",{children:[null==o?void 0:o.rate,(null==o?void 0:o.type)==="percentage"?"% off.":"",(null==o?void 0:o.type)==="wallet_point_gift"?" point.":"",(null==o?void 0:o.type)==="free_shipping"?" N/A":""]})]}):""]})]})}),(0,l.jsx)("div",{className:"relative overflow-hidden bg-white mb-5",children:(0,l.jsxs)("div",{className:"p-10",children:[(0,l.jsx)("h3",{className:"mb-4 text-xl font-semibold text-muted-black",children:"Request note."}),(null==t?void 0:t.note)?(0,l.jsx)("p",{className:"mb-8 text-base leading-[150%] text-[#666] lg:text-lg",children:null==t?void 0:t.note}):""]})})]})})}},60385:function(e,t,s){"use strict";s.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var l=s(11163),a=s.n(l),n=s(88767),u=s(22920),o=s(5233),r=s(28597),i=s(97514),d=s(47869),c=s(93345),S=s(55191),h=s(3737);let p={...(0,S.h)(d.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h.eN.get(d.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...s,search:h.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:s,shop_id:l}=e;return h.eN.get("".concat(d.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:s,shop_id:l,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:s,...l}=e;return h.eN.get(d.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:s,...l,search:h.eN.formatSearchParams({title:t,shop_id:s})})},approve:e=>h.eN.post(d.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>h.eN.post(d.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...s}=e;return h.eN.get(d.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...s,search:h.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:s,error:l,isLoading:a}=(0,n.useQuery)([d.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,r.Q)(s),error:l,loading:a}},useRequestedListForFlashSale=e=>{let{id:t,language:s,shop_id:l}=e,{data:a,error:u,isLoading:o}=(0,n.useQuery)([d.P.FLASH_SALE,{id:t,language:s,shop_id:l}],()=>p.get({id:t,language:s,shop_id:l}));return{flashSaleRequest:a,error:u,loading:o}},useRequestedProductsForFlashSale=e=>{var t;let{data:s,error:l,isLoading:a}=(0,n.useQuery)([d.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return p.requestedProducts(Object.assign({},t[1],s))},{keepPreviousData:!0});return{products:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,r.Q)(s),error:l,loading:a}},useCreateFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),t=(0,l.useRouter)(),{t:s}=(0,o.$G)();return(0,n.useMutation)(p.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await a().push(e,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;u.Am.error(s("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,n.useQueryClient)(),s=(0,l.useRouter)();return(0,n.useMutation)(p.update,{onSuccess:async t=>{let l=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await s.push(l,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var s;u.Am.error(e("common:".concat(null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,o.$G)();return(0,n.useMutation)(p.delete,{onSuccess:()=>{u.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var s;u.Am.error(t("common:".concat(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,n.useQueryClient)(),s=(0,l.useRouter)();return(0,n.useMutation)(p.approve,{onSuccess:async()=>{let t=s.query.shop?"/".concat(s.query.shop).concat(i.Z.vendorRequestForFlashSale.list):i.Z.vendorRequestForFlashSale.list;await a().push(t,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(p.disapprove,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.FLASH_SALE)}})}},41781:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSP:function(){return E}});var l=s(85893),a=s(52201),n=s(47133),u=s(45957),o=s(55846),r=s(60385),i=s(30042),d=s(16203),c=s(27484),S=s.n(c),h=s(84110),p=s.n(h),v=s(29387),m=s.n(v),_=s(70178),x=s.n(_),F=s(5233),g=s(11163),R=s(45517),f=s(67294);S().extend(p()),S().extend(x()),S().extend(m());let VendorRequestFlashSaleSinglePage=()=>{let{query:e,locale:t}=(0,g.useRouter)(),{t:s}=(0,F.$G)(),[n,c]=(0,f.useState)(""),[S,h]=(0,f.useState)(1),{permissions:p}=(0,d.WA)(),{data:v}=(0,i.DZ)({slug:null==e?void 0:e.shop}),{flashSaleRequest:m,loading:_,error:x}=(0,r.n1)({id:null==e?void 0:e.id,language:t,shop_id:null==v?void 0:v.id}),{products:E,loading:A,paginatorInfo:L,error:q}=(0,r.j4)({limit:5,vendor_request_id:null==e?void 0:e.id,page:S,name:n});(0,f.useCallback)(e=>{let{searchText:t}=e;c(t),h(1)},[c,h]);let P=(0,f.useCallback)(e=>{h(e)},[h]);return _?(0,l.jsx)(o.Z,{text:s("common:text-loading")}):x?(0,l.jsx)(u.Z,{message:x.message}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.Z,{data:m}),(0,l.jsx)("div",{className:"relative overflow-hidden bg-white mb-5",children:(0,l.jsxs)("div",{className:"p-10",children:[(0,l.jsx)("h3",{className:"mb-5 text-xl font-semibold text-muted-black",children:"Requested products."}),(0,l.jsx)(R.Z,{products:E,paginatorInfo:L,onPagination:P})]})})]})};VendorRequestFlashSaleSinglePage.authenticate={permissions:d.ce},VendorRequestFlashSaleSinglePage.Layout=n.Z;var E=!0;t.default=VendorRequestFlashSaleSinglePage}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,5601,9693,9494,5535,8186,1285,1631,7556,9461,9774,2888,179],function(){return e(e.s=61582)}),_N_E=e.O()}]);