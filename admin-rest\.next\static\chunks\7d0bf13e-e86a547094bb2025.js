"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1255],{37054:function(e,t,s){s.d(t,{F2:function(){return Autocomplete},Ji:function(){return useJs<PERSON><PERSON><PERSON>oader}});var n,o,r,i,a,l,p,d,c,g,h,m=s(85893),v=s(67294),f=s(73935);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _defineProperty(e,t,s){var n;return(n=function(e,t){if("object"!=_typeof(e)||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var n=s.call(e,t||"default");if("object"!=_typeof(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==_typeof(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function getDefaultExportFromCjs$1(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var y=getDefaultExportFromCjs$1(l?a:(l=1,a=function(e,t,s,n,o,r,i,a){if(!e){var l;if(void 0===t)l=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var p=[s,n,o,r,i,a],d=0;(l=Error(t.replace(/%s/g,function(){return p[d++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}})),b=(0,v.createContext)(null);function unregisterEvent(e){google.maps.event.removeListener(e)}function unregisterEvents(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e.forEach(unregisterEvent)}function applyUpdatersToPropsAndRegisterEvents(e){var t,{updaterMap:s,eventMap:n,prevProps:o,nextProps:r,instance:i}=e,a=Object.keys(n).reduce(function(e,t){var s;return s=n[t],"function"==typeof r[t]&&e.push(google.maps.event.addListener(i,s,r[t])),e},[]);return t={},function(e,t){Object.keys(e).forEach(s=>t(e[s],s))}(s,(e,s)=>{var n=r[s];n!==o[s]&&(t[s]=n,e(i,n))}),a}function asyncGeneratorStep(e,t,s,n,o,r,i){try{var a=e[r](i),l=a.value}catch(e){return void s(e)}a.done?t(l):Promise.resolve(l).then(n,o)}function _asyncToGenerator(e){return function(){var t=this,s=arguments;return new Promise(function(n,o){var r=e.apply(t,s);function _next(e){asyncGeneratorStep(r,n,o,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(r,n,o,_next,_throw,"throw",e)}_next(void 0)})}}function makeLoadScriptUrl(e){var{googleMapsApiKey:t,googleMapsClientId:s,version:n="weekly",language:o,region:r,libraries:i,channel:a,mapIds:l,authReferrerPolicy:p}=e,d=[];return y(t&&s||!(t&&s),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),t?d.push("key=".concat(t)):s&&d.push("client=".concat(s)),n&&d.push("v=".concat(n)),o&&d.push("language=".concat(o)),r&&d.push("region=".concat(r)),i&&i.length&&d.push("libraries=".concat(i.sort().join(","))),a&&d.push("channel=".concat(a)),l&&l.length&&d.push("map_ids=".concat(l.join(","))),p&&d.push("auth_referrer_policy=".concat(p)),d.push("loading=async"),d.push("callback=initMap"),"https://maps.googleapis.com/maps/api/js?".concat(d.join("&"))}(0,v.memo)(function(e){var{children:t,options:s,id:n,mapContainerStyle:o,mapContainerClassName:r,center:i,onClick:a,onDblClick:l,onDrag:p,onDragEnd:d,onDragStart:c,onMouseMove:g,onMouseOut:h,onMouseOver:f,onMouseDown:y,onMouseUp:L,onRightClick:E,onCenterChanged:C,onLoad:P,onUnmount:w}=e,[x,S]=(0,v.useState)(null),M=(0,v.useRef)(null),[k,O]=(0,v.useState)(null),[_,j]=(0,v.useState)(null),[I,D]=(0,v.useState)(null),[B,T]=(0,v.useState)(null),[A,U]=(0,v.useState)(null),[R,z]=(0,v.useState)(null),[$,V]=(0,v.useState)(null),[W,Z]=(0,v.useState)(null),[N,H]=(0,v.useState)(null),[K,G]=(0,v.useState)(null),[F,Y]=(0,v.useState)(null),[q,J]=(0,v.useState)(null);return(0,v.useEffect)(()=>{s&&null!==x&&x.setOptions(s)},[x,s]),(0,v.useEffect)(()=>{null!==x&&void 0!==i&&x.setCenter(i)},[x,i]),(0,v.useEffect)(()=>{x&&l&&(null!==_&&google.maps.event.removeListener(_),j(google.maps.event.addListener(x,"dblclick",l)))},[l]),(0,v.useEffect)(()=>{x&&d&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(x,"dragend",d)))},[d]),(0,v.useEffect)(()=>{x&&c&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(x,"dragstart",c)))},[c]),(0,v.useEffect)(()=>{x&&y&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(x,"mousedown",y)))},[y]),(0,v.useEffect)(()=>{x&&g&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(x,"mousemove",g)))},[g]),(0,v.useEffect)(()=>{x&&h&&(null!==$&&google.maps.event.removeListener($),V(google.maps.event.addListener(x,"mouseout",h)))},[h]),(0,v.useEffect)(()=>{x&&f&&(null!==W&&google.maps.event.removeListener(W),Z(google.maps.event.addListener(x,"mouseover",f)))},[f]),(0,v.useEffect)(()=>{x&&L&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(x,"mouseup",L)))},[L]),(0,v.useEffect)(()=>{x&&E&&(null!==K&&google.maps.event.removeListener(K),G(google.maps.event.addListener(x,"rightclick",E)))},[E]),(0,v.useEffect)(()=>{x&&a&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(x,"click",a)))},[a]),(0,v.useEffect)(()=>{x&&p&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(x,"drag",p)))},[p]),(0,v.useEffect)(()=>{x&&C&&(null!==k&&google.maps.event.removeListener(k),O(google.maps.event.addListener(x,"center_changed",C)))},[a]),(0,v.useEffect)(()=>{var e=null===M.current?null:new google.maps.Map(M.current,s);return S(e),null!==e&&P&&P(e),()=>{null!==e&&w&&w(e)}},[]),(0,m.jsx)("div",{id:n,ref:M,style:o,className:r,children:(0,m.jsx)(b.Provider,{value:x,children:null!==x?t:null})})});var L="undefined"!=typeof document;function injectScript(e){var{url:t,id:s,nonce:n}=e;return L?new Promise(function(e,o){var r=document.getElementById(s),i=window;if(r){var a=r.getAttribute("data-state");if(r.src===t&&"error"!==a){if("ready"===a)return e(s);var l=i.initMap,p=r.onerror;return i.initMap=function(){l&&l(),e(s)},void(r.onerror=function(e){p&&p(e),o(e)})}r.remove()}var d=document.createElement("script");d.type="text/javascript",d.src=t,d.id=s,d.async=!0,d.nonce=n||"",d.onerror=function(e){d.setAttribute("data-state","error"),o(e)},i.initMap=function(){d.setAttribute("data-state","ready"),e(s)},document.head.appendChild(d)}).catch(e=>{throw console.error("injectScript error: ",e),e}):Promise.reject(Error("document is undefined"))}function isGoogleFontStyle(e){var t=e.href;return!!t&&(0===t.indexOf("https://fonts.googleapis.com/css?family=Roboto")||0===t.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text"))||("style"===e.tagName.toLowerCase()&&e.styleSheet&&e.styleSheet.cssText&&0===e.styleSheet.cssText.replace("\r\n","").indexOf(".gm-style")?(e.styleSheet.cssText="",!0):"style"===e.tagName.toLowerCase()&&e.innerHTML&&0===e.innerHTML.replace("\r\n","").indexOf(".gm-style")?(e.innerHTML="",!0):"style"===e.tagName.toLowerCase()&&!e.styleSheet&&!e.innerHTML)}function preventGoogleFonts(){var e=document.getElementsByTagName("head")[0];if(e){var t=e.insertBefore.bind(e);e.insertBefore=function(s,n){return isGoogleFontStyle(s)||Reflect.apply(t,e,[s,n]),s};var s=e.appendChild.bind(e);e.appendChild=function(t){return isGoogleFontStyle(t)||Reflect.apply(s,e,[t]),t}}}var E=!1;function DefaultLoadingElement(){return(0,m.jsx)("div",{children:"Loading..."})}var C={id:"script-loader",version:"weekly"};let LoadScript=class LoadScript extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"check",(0,v.createRef)()),_defineProperty(this,"state",{loaded:!1}),_defineProperty(this,"cleanupCallback",()=>{delete window.google.maps,this.injectScript()}),_defineProperty(this,"isCleaningUp",_asyncToGenerator(function*(){return new Promise(function(e){if(E){if(L)var t=window.setInterval(function(){E||(window.clearInterval(t),e())},1)}else e()})})),_defineProperty(this,"cleanup",()=>{E=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter(function(e){return"string"==typeof e.src&&e.src.includes("maps.googleapis")}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)}),Array.prototype.slice.call(document.getElementsByTagName("link")).filter(function(e){return"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"===e.href}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)}),Array.prototype.slice.call(document.getElementsByTagName("style")).filter(function(e){return void 0!==e.innerText&&e.innerText.length>0&&e.innerText.includes(".gm-")}).forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)})}),_defineProperty(this,"injectScript",()=>{this.props.preventGoogleFontsLoading&&preventGoogleFonts(),y(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id),injectScript({id:this.props.id,nonce:this.props.nonce,url:makeLoadScriptUrl(this.props)}).then(()=>{this.props.onLoad&&this.props.onLoad(),this.setState(function(){return{loaded:!0}})}).catch(e=>{this.props.onError&&this.props.onError(e),console.error("\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(this.props.googleMapsApiKey||"-",") or Client ID (").concat(this.props.googleMapsClientId||"-",") to <LoadScript />\n          Otherwise it is a Network issue.\n        "))})})}componentDidMount(){if(L){if(window.google&&window.google.maps&&!E){console.error("google api is already presented");return}this.isCleaningUp().then(this.injectScript).catch(function(e){console.error("Error at injecting script after cleaning up: ",e)})}}componentDidUpdate(e){this.props.libraries!==e.libraries&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),L&&e.language!==this.props.language&&(this.cleanup(),this.setState(function(){return{loaded:!1}},this.cleanupCallback))}componentWillUnmount(){L&&(this.cleanup(),window.setTimeout(()=>{this.check.current||(delete window.google,E=!1)},1),this.props.onUnmount&&this.props.onUnmount())}render(){return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{ref:this.check}),this.state.loaded?this.props.children:this.props.loadingElement||(0,m.jsx)(DefaultLoadingElement,{})]})}};function _objectWithoutProperties(e,t){if(null==e)return{};var s,n,o=function(e,t){if(null==e)return{};var s={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.includes(n))continue;s[n]=e[n]}return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(n=0;n<r.length;n++)s=r[n],t.includes(s)||({}).propertyIsEnumerable.call(e,s)&&(o[s]=e[s])}return o}_defineProperty(LoadScript,"defaultProps",C);var P=["loadingElement","onLoad","onError","onUnmount","children"],w=(0,m.jsx)(DefaultLoadingElement,{});(0,v.memo)(function(e){var{loadingElement:t,onLoad:s,onError:n,onUnmount:o,children:r}=e,{isLoaded:i,loadError:a}=function(e){var{id:t=C.id,version:s=C.version,nonce:n,googleMapsApiKey:o,googleMapsClientId:r,language:i,region:a,libraries:l,preventGoogleFontsLoading:d,channel:c,mapIds:g,authReferrerPolicy:h}=e,m=(0,v.useRef)(!1),[f,b]=(0,v.useState)(!1),[E,P]=(0,v.useState)(void 0);(0,v.useEffect)(function(){return m.current=!0,()=>{m.current=!1}},[]),(0,v.useEffect)(function(){L&&d&&preventGoogleFonts()},[d]),(0,v.useEffect)(function(){f&&y(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")},[f]);var w=makeLoadScriptUrl({version:s,googleMapsApiKey:o,googleMapsClientId:r,language:i,region:a,libraries:l,channel:c,mapIds:g,authReferrerPolicy:h});(0,v.useEffect)(function(){if(L){if(window.google&&window.google.maps&&p===w){setLoadedIfMounted();return}injectScript({id:t,url:w,nonce:n}).then(setLoadedIfMounted).catch(function(e){m.current&&P(e),console.warn("\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(o||"-",") or Client ID (").concat(r||"-",")\n        Otherwise it is a Network issue.\n      ")),console.error(e)})}function setLoadedIfMounted(){m.current&&(b(!0),p=w)}},[t,w,n]);var x=(0,v.useRef)();return(0,v.useEffect)(function(){x.current&&l!==x.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),x.current=l},[l]),{isLoaded:f,loadError:E,url:w}}(_objectWithoutProperties(e,P));return(0,v.useEffect)(function(){i&&"function"==typeof s&&s()},[i,s]),(0,v.useEffect)(function(){a&&"function"==typeof n&&n(a)},[a,n]),(0,v.useEffect)(function(){return()=>{o&&o()}},[o]),i?r:t||w}),"function"==typeof SuppressedError&&SuppressedError;var x=(n=function equal(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){if((s=e.length)!=t.length)return!1;for(n=s;0!=n--;)if(!equal(e[n],t[n]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if((s=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=s;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,o[n]))return!1;for(n=s;0!=n--;){var s,n,o,r=o[n];if(!equal(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}).__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n,S="__googleMapsScriptId";(o=d||(d={}))[o.INITIALIZED=0]="INITIALIZED",o[o.LOADING=1]="LOADING",o[o.SUCCESS=2]="SUCCESS",o[o.FAILURE=3]="FAILURE";let Loader=class Loader{constructor(e){var{apiKey:t,authReferrerPolicy:s,channel:n,client:o,id:r=S,language:i,libraries:a=[],mapIds:l,nonce:p,region:d,retries:c=3,url:g="https://maps.googleapis.com/maps/api/js",version:h}=e;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=s,this.channel=n,this.client=o,this.id=r||S,this.language=i,this.libraries=a,this.mapIds=l,this.nonce=p,this.region=d,this.retries=c,this.url=g,this.version=h,Loader.instance){if(!x(this.options,Loader.instance.options))throw Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options)," !== ").concat(JSON.stringify(Loader.instance.options)));return Loader.instance}Loader.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?d.FAILURE:this.done?d.SUCCESS:this.loading?d.LOADING:d.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){var e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+="&key=".concat(this.apiKey)),this.channel&&(e+="&channel=".concat(this.channel)),this.client&&(e+="&client=".concat(this.client)),this.libraries.length>0&&(e+="&libraries=".concat(this.libraries.join(","))),this.language&&(e+="&language=".concat(this.language)),this.region&&(e+="&region=".concat(this.region)),this.version&&(e+="&v=".concat(this.version)),this.mapIds&&(e+="&map_ids=".concat(this.mapIds.join(","))),this.authReferrerPolicy&&(e+="&auth_referrer_policy=".concat(this.authReferrerPolicy)),e}deleteScript(){var e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((e,t)=>{this.loadCallback(s=>{s?t(s.error):e(window.google)})})}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){if(document.getElementById(this.id)){this.callback();return}var e,t,s={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(s).forEach(e=>!s[e]&&delete s[e]),(null===(t=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.maps)||void 0===t?void 0:t.importLibrary)||(e=>{var t,s,n,o="The Google Maps JavaScript API",r="google",i="importLibrary",a="__ib__",l=document,p=window,d=(p=p[r]||(p[r]={})).maps||(p.maps={}),c=new Set,g=new URLSearchParams,u=()=>t||(t=new Promise((i,p)=>{var h,m,v;return h=this,m=void 0,v=function*(){var h;for(n in yield s=l.createElement("script"),s.id=this.id,g.set("libraries",[...c]+""),e)g.set(n.replace(/[A-Z]/g,e=>"_"+e[0].toLowerCase()),e[n]);g.set("callback",r+".maps."+a),s.src=this.url+"?"+g,d[a]=i,s.onerror=()=>t=p(Error(o+" could not load.")),s.nonce=this.nonce||(null===(h=l.querySelector("script[nonce]"))||void 0===h?void 0:h.nonce)||"",l.head.append(s)},new(m||(m=Promise))(function(e,t){function fulfilled(e){try{step(v.next(e))}catch(e){t(e)}}function rejected(e){try{step(v.throw(e))}catch(e){t(e)}}function step(t){var s;t.done?e(t.value):((s=t.value)instanceof m?s:new m(function(e){e(s)})).then(fulfilled,rejected)}step((v=v.apply(h,[])).next())})}));d[i]?console.warn(o+" only loads once. Ignoring:",e):d[i]=function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),n=1;n<t;n++)s[n-1]=arguments[n];return c.add(e)&&u().then(()=>d[i](e,...s))}})(s);var n=this.libraries.map(e=>this.importLibrary(e));n.length||n.push(this.importLibrary("core")),Promise.all(n).then(()=>this.callback(),e=>{var t=new ErrorEvent("error",{error:e});this.loadErrorCallback(t)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){var t=this.errors.length*Math.pow(2,this.errors.length);console.error("Failed to load Google Maps script, retrying in ".concat(t," ms.")),setTimeout(()=>{this.deleteScript(),this.setScript()},t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(e=>{e(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading){if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version){console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),this.callback();return}this.loading=!0,this.setScript()}}}};var M=["maps"];function useJsApiLoader(e){var{id:t=C.id,version:s=C.version,nonce:n,googleMapsApiKey:o,language:r,region:i,libraries:a=M,preventGoogleFontsLoading:l,mapIds:p,authReferrerPolicy:d}=e,c=(0,v.useRef)(!1),[g,h]=(0,v.useState)(!1),[m,f]=(0,v.useState)(void 0);(0,v.useEffect)(function(){return c.current=!0,()=>{c.current=!1}},[]);var y=(0,v.useMemo)(()=>new Loader({id:t,apiKey:o,version:s,libraries:a,language:r||"en",region:i||"US",mapIds:p||[],nonce:n||"",authReferrerPolicy:d||"origin"}),[t,o,s,a,r,i,p,n,d]);(0,v.useEffect)(function(){g||y.load().then(()=>{c.current&&h(!0)}).catch(e=>{f(e)})},[]),(0,v.useEffect)(()=>{L&&l&&preventGoogleFonts()},[l]);var b=(0,v.useRef)();return(0,v.useEffect)(()=>{b.current&&a!==b.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),b.current=a},[a]),{isLoaded:g,loadError:m}}function ownKeys$f(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$f(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$f(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$f(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}var k={},O={options(e,t){e.setOptions(t)}};(0,v.memo)(function(e){var{options:t,onLoad:s,onUnmount:n}=e,o=(0,v.useContext)(b),[r,i]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==r&&r.setMap(o)},[o]),(0,v.useEffect)(()=>{t&&null!==r&&r.setOptions(t)},[r,t]),(0,v.useEffect)(()=>{var e=new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({},t),{},{map:o}));return i(e),s&&s(e),()=>{null!==r&&(n&&n(r),r.setMap(null))}},[]),null});let TrafficLayer=class TrafficLayer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"state",{trafficLayer:null}),_defineProperty(this,"setTrafficLayerCallback",()=>{null!==this.state.trafficLayer&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)}),_defineProperty(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:O,eventMap:k,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{trafficLayer:e}},this.setTrafficLayerCallback)}componentDidUpdate(e){null!==this.state.trafficLayer&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:O,eventMap:k,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){null!==this.state.trafficLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),unregisterEvents(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}};_defineProperty(TrafficLayer,"contextType",b),(0,v.memo)(function(e){var{onLoad:t,onUnmount:s}=e,n=(0,v.useContext)(b),[o,r]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==o&&o.setMap(n)},[n]),(0,v.useEffect)(()=>{var e=new google.maps.BicyclingLayer;return r(e),e.setMap(n),t&&t(e),()=>{null!==e&&(s&&s(e),e.setMap(null))}},[]),null});let BicyclingLayer=class BicyclingLayer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"state",{bicyclingLayer:null}),_defineProperty(this,"setBicyclingLayerCallback",()=>{null!==this.state.bicyclingLayer&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))})}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState(()=>({bicyclingLayer:e}),this.setBicyclingLayerCallback)}componentWillUnmount(){null!==this.state.bicyclingLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}};_defineProperty(BicyclingLayer,"contextType",b),(0,v.memo)(function(e){var{onLoad:t,onUnmount:s}=e,n=(0,v.useContext)(b),[o,r]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==o&&o.setMap(n)},[n]),(0,v.useEffect)(()=>{var e=new google.maps.TransitLayer;return r(e),e.setMap(n),t&&t(e),()=>{null!==o&&(s&&s(o),o.setMap(null))}},[]),null});let TransitLayer=class TransitLayer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"state",{transitLayer:null}),_defineProperty(this,"setTransitLayerCallback",()=>{null!==this.state.transitLayer&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))})}componentDidMount(){var e=new google.maps.TransitLayer;this.setState(function(){return{transitLayer:e}},this.setTransitLayerCallback)}componentWillUnmount(){null!==this.state.transitLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}};function ownKeys$e(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$e(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$e(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$e(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(TransitLayer,"contextType",b);var _={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},j={drawingMode(e,t){e.setDrawingMode(t)},options(e,t){e.setOptions(t)}};(0,v.memo)(function(e){var{options:t,drawingMode:s,onCircleComplete:n,onMarkerComplete:o,onOverlayComplete:r,onPolygonComplete:i,onPolylineComplete:a,onRectangleComplete:l,onLoad:p,onUnmount:d}=e,c=(0,v.useContext)(b),[g,h]=(0,v.useState)(null),[m,f]=(0,v.useState)(null),[L,E]=(0,v.useState)(null),[C,P]=(0,v.useState)(null),[w,x]=(0,v.useState)(null),[S,M]=(0,v.useState)(null),[k,O]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==g&&g.setMap(c)},[c]),(0,v.useEffect)(()=>{t&&null!==g&&g.setOptions(t)},[g,t]),(0,v.useEffect)(()=>{null!==g&&g.setDrawingMode(null!=s?s:null)},[g,s]),(0,v.useEffect)(()=>{g&&n&&(null!==m&&google.maps.event.removeListener(m),f(google.maps.event.addListener(g,"circlecomplete",n)))},[g,n]),(0,v.useEffect)(()=>{g&&o&&(null!==L&&google.maps.event.removeListener(L),E(google.maps.event.addListener(g,"markercomplete",o)))},[g,o]),(0,v.useEffect)(()=>{g&&r&&(null!==C&&google.maps.event.removeListener(C),P(google.maps.event.addListener(g,"overlaycomplete",r)))},[g,r]),(0,v.useEffect)(()=>{g&&i&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(g,"polygoncomplete",i)))},[g,i]),(0,v.useEffect)(()=>{g&&a&&(null!==S&&google.maps.event.removeListener(S),M(google.maps.event.addListener(g,"polylinecomplete",a)))},[g,a]),(0,v.useEffect)(()=>{g&&l&&(null!==k&&google.maps.event.removeListener(k),O(google.maps.event.addListener(g,"rectanglecomplete",l)))},[g,l]),(0,v.useEffect)(()=>{y(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var e=new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({},t),{},{map:c}));return s&&e.setDrawingMode(s),n&&f(google.maps.event.addListener(e,"circlecomplete",n)),o&&E(google.maps.event.addListener(e,"markercomplete",o)),r&&P(google.maps.event.addListener(e,"overlaycomplete",r)),i&&x(google.maps.event.addListener(e,"polygoncomplete",i)),a&&M(google.maps.event.addListener(e,"polylinecomplete",a)),l&&O(google.maps.event.addListener(e,"rectanglecomplete",l)),h(e),p&&p(e),()=>{null!==g&&(m&&google.maps.event.removeListener(m),L&&google.maps.event.removeListener(L),C&&google.maps.event.removeListener(C),w&&google.maps.event.removeListener(w),S&&google.maps.event.removeListener(S),k&&google.maps.event.removeListener(k),d&&d(g),g.setMap(null))}},[]),null});let DrawingManager=class DrawingManager extends v.PureComponent{constructor(e){super(e),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{drawingManager:null}),_defineProperty(this,"setDrawingManagerCallback",()=>{null!==this.state.drawingManager&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)}),y(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:j,eventMap:_,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{drawingManager:e}},this.setDrawingManagerCallback)}componentDidUpdate(e){null!==this.state.drawingManager&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:j,eventMap:_,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){null!==this.state.drawingManager&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),unregisterEvents(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}};function ownKeys$d(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$d(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$d(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$d(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(DrawingManager,"contextType",b);var I={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},D={animation(e,t){e.setAnimation(t)},clickable(e,t){e.setClickable(t)},cursor(e,t){e.setCursor(t)},draggable(e,t){e.setDraggable(t)},icon(e,t){e.setIcon(t)},label(e,t){e.setLabel(t)},map(e,t){e.setMap(t)},opacity(e,t){e.setOpacity(t)},options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},shape(e,t){e.setShape(t)},title(e,t){e.setTitle(t)},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},B={};(0,v.memo)(function(e){var{position:t,options:s,clusterer:n,noClustererRedraw:o,children:r,draggable:i,visible:a,animation:l,clickable:p,cursor:d,icon:c,label:g,opacity:h,shape:f,title:y,zIndex:L,onClick:E,onDblClick:C,onDrag:P,onDragEnd:w,onDragStart:x,onMouseOut:S,onMouseOver:M,onMouseUp:k,onMouseDown:O,onRightClick:_,onClickableChanged:j,onCursorChanged:I,onAnimationChanged:D,onDraggableChanged:T,onFlatChanged:A,onIconChanged:U,onPositionChanged:R,onShapeChanged:z,onTitleChanged:$,onVisibleChanged:V,onZindexChanged:W,onLoad:Z,onUnmount:N}=e,H=(0,v.useContext)(b),[K,G]=(0,v.useState)(null),[F,Y]=(0,v.useState)(null),[q,J]=(0,v.useState)(null),[X,Q]=(0,v.useState)(null),[ee,et]=(0,v.useState)(null),[es,en]=(0,v.useState)(null),[eo,er]=(0,v.useState)(null),[ei,ea]=(0,v.useState)(null),[el,ep]=(0,v.useState)(null),[eu,ed]=(0,v.useState)(null),[ec,eg]=(0,v.useState)(null),[eh,em]=(0,v.useState)(null),[ev,ef]=(0,v.useState)(null),[ey,eb]=(0,v.useState)(null),[eL,eE]=(0,v.useState)(null),[eC,eP]=(0,v.useState)(null),[ew,ex]=(0,v.useState)(null),[eS,eM]=(0,v.useState)(null),[ek,eO]=(0,v.useState)(null),[e_,ej]=(0,v.useState)(null),[eI,eD]=(0,v.useState)(null),[eB,eT]=(0,v.useState)(null);(0,v.useEffect)(()=>{null!==K&&K.setMap(H)},[H]),(0,v.useEffect)(()=>{void 0!==s&&null!==K&&K.setOptions(s)},[K,s]),(0,v.useEffect)(()=>{void 0!==i&&null!==K&&K.setDraggable(i)},[K,i]),(0,v.useEffect)(()=>{t&&null!==K&&K.setPosition(t)},[K,t]),(0,v.useEffect)(()=>{void 0!==a&&null!==K&&K.setVisible(a)},[K,a]),(0,v.useEffect)(()=>{null==K||K.setAnimation(l)},[K,l]),(0,v.useEffect)(()=>{K&&void 0!==p&&K.setClickable(p)},[K,p]),(0,v.useEffect)(()=>{K&&void 0!==d&&K.setCursor(d)},[K,d]),(0,v.useEffect)(()=>{K&&void 0!==c&&K.setIcon(c)},[K,c]),(0,v.useEffect)(()=>{K&&void 0!==g&&K.setLabel(g)},[K,g]),(0,v.useEffect)(()=>{K&&void 0!==h&&K.setOpacity(h)},[K,h]),(0,v.useEffect)(()=>{K&&void 0!==f&&K.setShape(f)},[K,f]),(0,v.useEffect)(()=>{K&&void 0!==y&&K.setTitle(y)},[K,y]),(0,v.useEffect)(()=>{K&&void 0!==L&&K.setZIndex(L)},[K,L]),(0,v.useEffect)(()=>{K&&C&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(K,"dblclick",C)))},[C]),(0,v.useEffect)(()=>{K&&w&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(K,"dragend",w)))},[w]),(0,v.useEffect)(()=>{K&&x&&(null!==X&&google.maps.event.removeListener(X),Q(google.maps.event.addListener(K,"dragstart",x)))},[x]),(0,v.useEffect)(()=>{K&&O&&(null!==ee&&google.maps.event.removeListener(ee),et(google.maps.event.addListener(K,"mousedown",O)))},[O]),(0,v.useEffect)(()=>{K&&S&&(null!==es&&google.maps.event.removeListener(es),en(google.maps.event.addListener(K,"mouseout",S)))},[S]),(0,v.useEffect)(()=>{K&&M&&(null!==eo&&google.maps.event.removeListener(eo),er(google.maps.event.addListener(K,"mouseover",M)))},[M]),(0,v.useEffect)(()=>{K&&k&&(null!==ei&&google.maps.event.removeListener(ei),ea(google.maps.event.addListener(K,"mouseup",k)))},[k]),(0,v.useEffect)(()=>{K&&_&&(null!==el&&google.maps.event.removeListener(el),ep(google.maps.event.addListener(K,"rightclick",_)))},[_]),(0,v.useEffect)(()=>{K&&E&&(null!==eu&&google.maps.event.removeListener(eu),ed(google.maps.event.addListener(K,"click",E)))},[E]),(0,v.useEffect)(()=>{K&&P&&(null!==ec&&google.maps.event.removeListener(ec),eg(google.maps.event.addListener(K,"drag",P)))},[P]),(0,v.useEffect)(()=>{K&&j&&(null!==eh&&google.maps.event.removeListener(eh),em(google.maps.event.addListener(K,"clickable_changed",j)))},[j]),(0,v.useEffect)(()=>{K&&I&&(null!==ev&&google.maps.event.removeListener(ev),ef(google.maps.event.addListener(K,"cursor_changed",I)))},[I]),(0,v.useEffect)(()=>{K&&D&&(null!==ey&&google.maps.event.removeListener(ey),eb(google.maps.event.addListener(K,"animation_changed",D)))},[D]),(0,v.useEffect)(()=>{K&&T&&(null!==eL&&google.maps.event.removeListener(eL),eE(google.maps.event.addListener(K,"draggable_changed",T)))},[T]),(0,v.useEffect)(()=>{K&&A&&(null!==eC&&google.maps.event.removeListener(eC),eP(google.maps.event.addListener(K,"flat_changed",A)))},[A]),(0,v.useEffect)(()=>{K&&U&&(null!==ew&&google.maps.event.removeListener(ew),ex(google.maps.event.addListener(K,"icon_changed",U)))},[U]),(0,v.useEffect)(()=>{K&&R&&(null!==eS&&google.maps.event.removeListener(eS),eM(google.maps.event.addListener(K,"position_changed",R)))},[R]),(0,v.useEffect)(()=>{K&&z&&(null!==ek&&google.maps.event.removeListener(ek),eO(google.maps.event.addListener(K,"shape_changed",z)))},[z]),(0,v.useEffect)(()=>{K&&$&&(null!==e_&&google.maps.event.removeListener(e_),ej(google.maps.event.addListener(K,"title_changed",$)))},[$]),(0,v.useEffect)(()=>{K&&V&&(null!==eI&&google.maps.event.removeListener(eI),eD(google.maps.event.addListener(K,"visible_changed",V)))},[V]),(0,v.useEffect)(()=>{K&&W&&(null!==eB&&google.maps.event.removeListener(eB),eT(google.maps.event.addListener(K,"zindex_changed",W)))},[W]),(0,v.useEffect)(()=>{var e=_objectSpread$d(_objectSpread$d(_objectSpread$d({},s||B),n?B:{map:H}),{},{position:t}),r=new google.maps.Marker(e);return n?n.addMarker(r,!!o):r.setMap(H),t&&r.setPosition(t),void 0!==a&&r.setVisible(a),void 0!==i&&r.setDraggable(i),void 0!==p&&r.setClickable(p),"string"==typeof d&&r.setCursor(d),c&&r.setIcon(c),void 0!==g&&r.setLabel(g),void 0!==h&&r.setOpacity(h),f&&r.setShape(f),"string"==typeof y&&r.setTitle(y),"number"==typeof L&&r.setZIndex(L),C&&Y(google.maps.event.addListener(r,"dblclick",C)),w&&J(google.maps.event.addListener(r,"dragend",w)),x&&Q(google.maps.event.addListener(r,"dragstart",x)),O&&et(google.maps.event.addListener(r,"mousedown",O)),S&&en(google.maps.event.addListener(r,"mouseout",S)),M&&er(google.maps.event.addListener(r,"mouseover",M)),k&&ea(google.maps.event.addListener(r,"mouseup",k)),_&&ep(google.maps.event.addListener(r,"rightclick",_)),E&&ed(google.maps.event.addListener(r,"click",E)),P&&eg(google.maps.event.addListener(r,"drag",P)),j&&em(google.maps.event.addListener(r,"clickable_changed",j)),I&&ef(google.maps.event.addListener(r,"cursor_changed",I)),D&&eb(google.maps.event.addListener(r,"animation_changed",D)),T&&eE(google.maps.event.addListener(r,"draggable_changed",T)),A&&eP(google.maps.event.addListener(r,"flat_changed",A)),U&&ex(google.maps.event.addListener(r,"icon_changed",U)),R&&eM(google.maps.event.addListener(r,"position_changed",R)),z&&eO(google.maps.event.addListener(r,"shape_changed",z)),$&&ej(google.maps.event.addListener(r,"title_changed",$)),V&&eD(google.maps.event.addListener(r,"visible_changed",V)),W&&eT(google.maps.event.addListener(r,"zindex_changed",W)),G(r),Z&&Z(r),()=>{null!==F&&google.maps.event.removeListener(F),null!==q&&google.maps.event.removeListener(q),null!==X&&google.maps.event.removeListener(X),null!==ee&&google.maps.event.removeListener(ee),null!==es&&google.maps.event.removeListener(es),null!==eo&&google.maps.event.removeListener(eo),null!==ei&&google.maps.event.removeListener(ei),null!==el&&google.maps.event.removeListener(el),null!==eu&&google.maps.event.removeListener(eu),null!==eh&&google.maps.event.removeListener(eh),null!==ev&&google.maps.event.removeListener(ev),null!==ey&&google.maps.event.removeListener(ey),null!==eL&&google.maps.event.removeListener(eL),null!==eC&&google.maps.event.removeListener(eC),null!==ew&&google.maps.event.removeListener(ew),null!==eS&&google.maps.event.removeListener(eS),null!==e_&&google.maps.event.removeListener(e_),null!==eI&&google.maps.event.removeListener(eI),null!==eB&&google.maps.event.removeListener(eB),N&&N(r),n?n.removeMarker(r,!!o):r&&r.setMap(null)}},[]);var eA=(0,v.useMemo)(()=>r?v.Children.map(r,e=>(0,v.isValidElement)(e)?(0,v.cloneElement)(e,{anchor:K}):e):null,[r,K]);return(0,m.jsx)(m.Fragment,{children:eA})||null});let Marker=class Marker extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[])}componentDidMount(){var e=this;return _asyncToGenerator(function*(){var t=_objectSpread$d(_objectSpread$d(_objectSpread$d({},e.props.options||B),e.props.clusterer?B:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(t),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:D,eventMap:I,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)})()}componentDidUpdate(e){this.marker&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:D,eventMap:I,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),unregisterEvents(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){return(this.props.children?v.Children.map(this.props.children,e=>(0,v.isValidElement)(e)?(0,v.cloneElement)(e,{anchor:this.marker}):e):null)||null}};_defineProperty(Marker,"contextType",b);var T=function(){function ClusterIcon(e,t){e.getClusterer().extend(ClusterIcon,google.maps.OverlayView),this.cluster=e,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=t,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(e.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return ClusterIcon.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},ClusterIcon.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},ClusterIcon.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var t=this.cluster.getClusterer();if(google.maps.event.trigger(t,"click",this.cluster),google.maps.event.trigger(t,"clusterclick",this.cluster),t.getZoomOnClick()){var s=t.getMaxZoom(),n=this.cluster.getBounds(),o=t.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(n),this.timeOut=window.setTimeout(function(){var e=t.getMap();if(null!==e){"fitBounds"in e&&e.fitBounds(n);var o=e.getZoom()||0;null!==s&&o>s&&e.setZoom(s+1)}},100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},ClusterIcon.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},ClusterIcon.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},ClusterIcon.prototype.onAdd=function(){this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null===(e=this.getPanes())||void 0===e||e.overlayMouseTarget.appendChild(this.div);var e,t=this.getMap();null!==t&&(this.boundsChangedListener=google.maps.event.addListener(t,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},ClusterIcon.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},ClusterIcon.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=null!==e?"".concat(e.y,"px"):"0",this.div.style.left=null!==e?"".concat(e.x,"px"):"0"}},ClusterIcon.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},ClusterIcon.prototype.show=function(){var e,t,s,n,o,r;if(this.div&&this.center){var i=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,a=this.backgroundPosition.split(" "),l=parseInt((null===(e=a[0])||void 0===e?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),p=parseInt((null===(t=a[1])||void 0===t?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),d=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==d?"".concat(d.y,"px"):"0","; left: ").concat(null!==d?"".concat(d.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var c=document.createElement("img");c.alt=i,c.src=this.url,c.width=this.width,c.height=this.height,c.setAttribute("style","position: absolute; top: ".concat(p,"px; left: ").concat(l,"px")),this.cluster.getClusterer().enableRetinaIcons||(c.style.clip="rect(-".concat(p,"px, -").concat(l+this.width,"px, -").concat(p+this.height,", -").concat(l,")"));var g=document.createElement("div");g.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null===(s=this.sums)||void 0===s?void 0:s.text)&&(g.innerText="".concat(null===(n=this.sums)||void 0===n?void 0:n.text)),(null===(o=this.sums)||void 0===o?void 0:o.html)&&(g.innerHTML="".concat(null===(r=this.sums)||void 0===r?void 0:r.html)),this.div.innerHTML="",this.div.appendChild(c),this.div.appendChild(g),this.div.title=i,this.div.style.display=""}this.visible=!0},ClusterIcon.prototype.useStyle=function(e){this.sums=e;var t=this.cluster.getClusterer().getStyles(),s=t[Math.min(t.length-1,Math.max(0,e.index-1))];s&&(this.url=s.url,this.height=s.height,this.width=s.width,s.className&&(this.className="".concat(this.clusterClassName," ").concat(s.className)),this.anchorText=s.anchorText||[0,0],this.anchorIcon=s.anchorIcon||[this.height/2,this.width/2],this.textColor=s.textColor||"black",this.textSize=s.textSize||11,this.textDecoration=s.textDecoration||"none",this.fontWeight=s.fontWeight||"bold",this.fontStyle=s.fontStyle||"normal",this.fontFamily=s.fontFamily||"Arial,sans-serif",this.backgroundPosition=s.backgroundPosition||"0 0")},ClusterIcon.prototype.setCenter=function(e){this.center=e},ClusterIcon.prototype.getPosFromLatLng=function(e){var t=this.getProjection().fromLatLngToDivPixel(e);return null!==t&&(t.x-=this.anchorIcon[1],t.y-=this.anchorIcon[0]),t},ClusterIcon}(),A=function(){function Cluster(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new T(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return Cluster.prototype.getSize=function(){return this.markers.length},Cluster.prototype.getMarkers=function(){return this.markers},Cluster.prototype.getCenter=function(){return this.center},Cluster.prototype.getMap=function(){return this.map},Cluster.prototype.getClusterer=function(){return this.markerClusterer},Cluster.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),t=this.getMarkers(),s=0;s<t.length;s++){var n=t[s].getPosition();n&&e.extend(n)}return e},Cluster.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},Cluster.prototype.addMarker=function(e){if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter){var t=e.getPosition();if(t){var s=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(s-1)+t.lat())/s,(this.center.lng()*(s-1)+t.lng())/s),this.calculateBounds()}}}else{var n,t=e.getPosition();t&&(this.center=t,this.calculateBounds())}e.isAdded=!0,this.markers.push(e);var o=this.markers.length,r=this.markerClusterer.getMaxZoom(),i=null===(n=this.map)||void 0===n?void 0:n.getZoom();if(null!==r&&void 0!==i&&i>r)e.getMap()!==this.map&&e.setMap(this.map);else if(o<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(o===this.minClusterSize)for(var a=0,l=this.markers;a<l.length;a++)l[a].setMap(null);else e.setMap(null);return!0},Cluster.prototype.isMarkerInClusterBounds=function(e){if(null!==this.bounds){var t=e.getPosition();if(t)return this.bounds.contains(t)}return!1},Cluster.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},Cluster.prototype.updateIcon=function(){var e,t=this.markers.length,s=this.markerClusterer.getMaxZoom(),n=null===(e=this.map)||void 0===e?void 0:e.getZoom();if(null!==s&&void 0!==n&&n>s||t<this.minClusterSize){this.clusterIcon.hide();return}this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show()},Cluster.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var t=0;t<this.markers.length;t++)if(e===this.markers[t])return!0;return!1},Cluster}();function CALCULATOR(e,t){var s=e.length,n=s.toString().length;return{text:s.toString(),index:Math.min(n,t),title:""}}var U=[53,56,66,78,90],R=function(){function Clusterer(e,t,s){void 0===t&&(t=[]),void 0===s&&(s={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(Clusterer,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=s.gridSize||60,this.minClusterSize=s.minimumClusterSize||2,this.maxZoom=s.maxZoom||null,this.styles=s.styles||[],this.title=s.title||"",this.zoomOnClick=!0,void 0!==s.zoomOnClick&&(this.zoomOnClick=s.zoomOnClick),this.averageCenter=!1,void 0!==s.averageCenter&&(this.averageCenter=s.averageCenter),this.ignoreHidden=!1,void 0!==s.ignoreHidden&&(this.ignoreHidden=s.ignoreHidden),this.enableRetinaIcons=!1,void 0!==s.enableRetinaIcons&&(this.enableRetinaIcons=s.enableRetinaIcons),this.imagePath=s.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=s.imageExtension||"png",this.imageSizes=s.imageSizes||U,this.calculator=s.calculator||CALCULATOR,this.batchSize=s.batchSize||2e3,this.batchSizeIE=s.batchSizeIE||500,this.clusterClass=s.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(t,!0),this.setMap(e)}return Clusterer.prototype.onZoomChanged=function(){var e,t;this.resetViewport(!1),((null===(e=this.getMap())||void 0===e?void 0:e.getZoom())===(this.get("minZoom")||0)||(null===(t=this.getMap())||void 0===t?void 0:t.getZoom())===this.get("maxZoom"))&&google.maps.event.trigger(this,"idle")},Clusterer.prototype.onIdle=function(){this.redraw()},Clusterer.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),null!==e&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},Clusterer.prototype.onRemove=function(){for(var e=0,t=this.markers;e<t.length;e++){var s=t[e];s.getMap()!==this.activeMap&&s.setMap(this.activeMap)}for(var n=0,o=this.clusters;n<o.length;n++)o[n].remove();this.clusters=[];for(var r=0,i=this.listeners;r<i.length;r++){var a=i[r];google.maps.event.removeListener(a)}this.listeners=[],this.activeMap=null,this.ready=!1},Clusterer.prototype.draw=function(){},Clusterer.prototype.getMap=function(){return null},Clusterer.prototype.getPanes=function(){return null},Clusterer.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},Clusterer.prototype.setMap=function(){},Clusterer.prototype.addListener=function(){return{remove:function(){}}},Clusterer.prototype.bindTo=function(){},Clusterer.prototype.get=function(){},Clusterer.prototype.notify=function(){},Clusterer.prototype.set=function(){},Clusterer.prototype.setValues=function(){},Clusterer.prototype.unbind=function(){},Clusterer.prototype.unbindAll=function(){},Clusterer.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},Clusterer.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),t=new google.maps.LatLngBounds,s=0;s<e.length;s++){var n=e[s].getPosition();n&&t.extend(n)}var o=this.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(t)},Clusterer.prototype.getGridSize=function(){return this.gridSize},Clusterer.prototype.setGridSize=function(e){this.gridSize=e},Clusterer.prototype.getMinimumClusterSize=function(){return this.minClusterSize},Clusterer.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},Clusterer.prototype.getMaxZoom=function(){return this.maxZoom},Clusterer.prototype.setMaxZoom=function(e){this.maxZoom=e},Clusterer.prototype.getStyles=function(){return this.styles},Clusterer.prototype.setStyles=function(e){this.styles=e},Clusterer.prototype.getTitle=function(){return this.title},Clusterer.prototype.setTitle=function(e){this.title=e},Clusterer.prototype.getZoomOnClick=function(){return this.zoomOnClick},Clusterer.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},Clusterer.prototype.getAverageCenter=function(){return this.averageCenter},Clusterer.prototype.setAverageCenter=function(e){this.averageCenter=e},Clusterer.prototype.getIgnoreHidden=function(){return this.ignoreHidden},Clusterer.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},Clusterer.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},Clusterer.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},Clusterer.prototype.getImageExtension=function(){return this.imageExtension},Clusterer.prototype.setImageExtension=function(e){this.imageExtension=e},Clusterer.prototype.getImagePath=function(){return this.imagePath},Clusterer.prototype.setImagePath=function(e){this.imagePath=e},Clusterer.prototype.getImageSizes=function(){return this.imageSizes},Clusterer.prototype.setImageSizes=function(e){this.imageSizes=e},Clusterer.prototype.getCalculator=function(){return this.calculator},Clusterer.prototype.setCalculator=function(e){this.calculator=e},Clusterer.prototype.getBatchSizeIE=function(){return this.batchSizeIE},Clusterer.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},Clusterer.prototype.getClusterClass=function(){return this.clusterClass},Clusterer.prototype.setClusterClass=function(e){this.clusterClass=e},Clusterer.prototype.getMarkers=function(){return this.markers},Clusterer.prototype.getTotalMarkers=function(){return this.markers.length},Clusterer.prototype.getClusters=function(){return this.clusters},Clusterer.prototype.getTotalClusters=function(){return this.clusters.length},Clusterer.prototype.addMarker=function(e,t){this.pushMarkerTo(e),t||this.redraw()},Clusterer.prototype.addMarkers=function(e,t){for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var n=e[s];n&&this.pushMarkerTo(n)}t||this.redraw()},Clusterer.prototype.pushMarkerTo=function(e){var t=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",function(){t.ready&&(e.isAdded=!1,t.repaint())}),e.isAdded=!1,this.markers.push(e)},Clusterer.prototype.removeMarker_=function(e){var t=-1;if(this.markers.indexOf)t=this.markers.indexOf(e);else for(var s=0;s<this.markers.length;s++)if(e===this.markers[s]){t=s;break}return -1!==t&&(e.setMap(null),this.markers.splice(t,1),!0)},Clusterer.prototype.removeMarker=function(e,t){var s=this.removeMarker_(e);return!t&&s&&this.repaint(),s},Clusterer.prototype.removeMarkers=function(e,t){for(var s=!1,n=0;n<e.length;n++){var o=e[n];s=s||this.removeMarker_(o)}return!t&&s&&this.repaint(),s},Clusterer.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},Clusterer.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout(function(){for(var t=0;t<e.length;t++)e[t].remove()},0)},Clusterer.prototype.getExtendedBounds=function(e){var t=this.getProjection(),s=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));null!==s&&(s.x+=this.gridSize,s.y-=this.gridSize);var n=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(null!==n&&(n.x-=this.gridSize,n.y+=this.gridSize),null!==s){var o=t.fromDivPixelToLatLng(s);null!==o&&e.extend(o)}if(null!==n){var r=t.fromDivPixelToLatLng(n);null!==r&&e.extend(r)}return e},Clusterer.prototype.redraw=function(){this.createClusters(0)},Clusterer.prototype.resetViewport=function(e){for(var t=0,s=this.clusters;t<s.length;t++)s[t].remove();this.clusters=[];for(var n=0,o=this.markers;n<o.length;n++){var r=o[n];r.isAdded=!1,e&&r.setMap(null)}},Clusterer.prototype.distanceBetweenPoints=function(e,t){var s=(t.lat()-e.lat())*Math.PI/180,n=(t.lng()-e.lng())*Math.PI/180,o=Math.sin(s/2)*Math.sin(s/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(t.lat()*Math.PI/180)*Math.sin(n/2)*Math.sin(n/2);return 6371*(2*Math.atan2(Math.sqrt(o),Math.sqrt(1-o)))},Clusterer.prototype.isMarkerInBounds=function(e,t){var s=e.getPosition();return!!s&&t.contains(s)},Clusterer.prototype.addToClosestCluster=function(e){for(var t,s=4e4,n=null,o=0,r=this.clusters;o<r.length;o++){var i=(t=r[o]).getCenter(),a=e.getPosition();if(i&&a){var l=this.distanceBetweenPoints(i,a);l<s&&(s=l,n=t)}}n&&n.isMarkerInClusterBounds(e)?n.addMarker(e):((t=new A(this)).addMarker(e),this.clusters.push(t))},Clusterer.prototype.createClusters=function(e){var t=this;if(this.ready){0===e&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var s=this.getMap(),n=null!==s&&("getBounds"in s)?s.getBounds():null,o=((null==s?void 0:s.getZoom())||0)>3?new google.maps.LatLngBounds(null==n?void 0:n.getSouthWest(),null==n?void 0:n.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),r=this.getExtendedBounds(o),i=Math.min(e+this.batchSize,this.markers.length),a=e;a<i;a++){var l=this.markers[a];l&&!l.isAdded&&this.isMarkerInBounds(l,r)&&(!this.ignoreHidden||this.ignoreHidden&&l.getVisible())&&this.addToClosestCluster(l)}if(i<this.markers.length)this.timerRefStatic=window.setTimeout(function(){t.createClusters(i)},0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var p=0,d=this.clusters;p<d.length;p++)d[p].updateIcon()}}},Clusterer.prototype.extend=function(e,t){return(function(e){for(var t in e.prototype)this.prototype[t]=e.prototype[t];return this}).apply(e,[t])},Clusterer}();function ownKeys$c(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}var z={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},$={averageCenter(e,t){e.setAverageCenter(t)},batchSizeIE(e,t){e.setBatchSizeIE(t)},calculator(e,t){e.setCalculator(t)},clusterClass(e,t){e.setClusterClass(t)},enableRetinaIcons(e,t){e.setEnableRetinaIcons(t)},gridSize(e,t){e.setGridSize(t)},ignoreHidden(e,t){e.setIgnoreHidden(t)},imageExtension(e,t){e.setImageExtension(t)},imagePath(e,t){e.setImagePath(t)},imageSizes(e,t){e.setImageSizes(t)},maxZoom(e,t){e.setMaxZoom(t)},minimumClusterSize(e,t){e.setMinimumClusterSize(t)},styles(e,t){e.setStyles(t)},title(e,t){e.setTitle(t)},zoomOnClick(e,t){e.setZoomOnClick(t)}},V={};(0,v.memo)(function(e){var{children:t,options:s,averageCenter:n,batchSizeIE:o,calculator:r,clusterClass:i,enableRetinaIcons:a,gridSize:l,ignoreHidden:p,imageExtension:d,imagePath:c,imageSizes:g,maxZoom:h,minimumClusterSize:m,styles:f,title:y,zoomOnClick:L,onClick:E,onClusteringBegin:C,onClusteringEnd:P,onMouseOver:w,onMouseOut:x,onLoad:S,onUnmount:M}=e,[k,O]=(0,v.useState)(null),_=(0,v.useContext)(b),[j,I]=(0,v.useState)(null),[D,B]=(0,v.useState)(null),[T,A]=(0,v.useState)(null),[U,W]=(0,v.useState)(null),[Z,N]=(0,v.useState)(null);return(0,v.useEffect)(()=>{k&&x&&(null!==U&&google.maps.event.removeListener(U),W(google.maps.event.addListener(k,z.onMouseOut,x)))},[x]),(0,v.useEffect)(()=>{k&&w&&(null!==Z&&google.maps.event.removeListener(Z),N(google.maps.event.addListener(k,z.onMouseOver,w)))},[w]),(0,v.useEffect)(()=>{k&&E&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(k,z.onClick,E)))},[E]),(0,v.useEffect)(()=>{k&&C&&(null!==D&&google.maps.event.removeListener(D),B(google.maps.event.addListener(k,z.onClusteringBegin,C)))},[C]),(0,v.useEffect)(()=>{k&&P&&(null!==T&&google.maps.event.removeListener(T),B(google.maps.event.addListener(k,z.onClusteringEnd,P)))},[P]),(0,v.useEffect)(()=>{void 0!==n&&null!==k&&$.averageCenter(k,n)},[k,n]),(0,v.useEffect)(()=>{void 0!==o&&null!==k&&$.batchSizeIE(k,o)},[k,o]),(0,v.useEffect)(()=>{void 0!==r&&null!==k&&$.calculator(k,r)},[k,r]),(0,v.useEffect)(()=>{void 0!==i&&null!==k&&$.clusterClass(k,i)},[k,i]),(0,v.useEffect)(()=>{void 0!==a&&null!==k&&$.enableRetinaIcons(k,a)},[k,a]),(0,v.useEffect)(()=>{void 0!==l&&null!==k&&$.gridSize(k,l)},[k,l]),(0,v.useEffect)(()=>{void 0!==p&&null!==k&&$.ignoreHidden(k,p)},[k,p]),(0,v.useEffect)(()=>{void 0!==d&&null!==k&&$.imageExtension(k,d)},[k,d]),(0,v.useEffect)(()=>{void 0!==c&&null!==k&&$.imagePath(k,c)},[k,c]),(0,v.useEffect)(()=>{void 0!==g&&null!==k&&$.imageSizes(k,g)},[k,g]),(0,v.useEffect)(()=>{void 0!==h&&null!==k&&$.maxZoom(k,h)},[k,h]),(0,v.useEffect)(()=>{void 0!==m&&null!==k&&$.minimumClusterSize(k,m)},[k,m]),(0,v.useEffect)(()=>{void 0!==f&&null!==k&&$.styles(k,f)},[k,f]),(0,v.useEffect)(()=>{void 0!==y&&null!==k&&$.title(k,y)},[k,y]),(0,v.useEffect)(()=>{void 0!==L&&null!==k&&$.zoomOnClick(k,L)},[k,L]),(0,v.useEffect)(()=>{if(_){var e=function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$c(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$c(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({},s||V),t=new R(_,[],e);return n&&$.averageCenter(t,n),o&&$.batchSizeIE(t,o),r&&$.calculator(t,r),i&&$.clusterClass(t,i),a&&$.enableRetinaIcons(t,a),l&&$.gridSize(t,l),p&&$.ignoreHidden(t,p),d&&$.imageExtension(t,d),c&&$.imagePath(t,c),g&&$.imageSizes(t,g),h&&$.maxZoom(t,h),m&&$.minimumClusterSize(t,m),f&&$.styles(t,f),y&&$.title(t,y),L&&$.zoomOnClick(t,L),x&&W(google.maps.event.addListener(t,z.onMouseOut,x)),w&&N(google.maps.event.addListener(t,z.onMouseOver,w)),E&&I(google.maps.event.addListener(t,z.onClick,E)),C&&B(google.maps.event.addListener(t,z.onClusteringBegin,C)),P&&A(google.maps.event.addListener(t,z.onClusteringEnd,P)),O(t),S&&S(t),()=>{null!==U&&google.maps.event.removeListener(U),null!==Z&&google.maps.event.removeListener(Z),null!==j&&google.maps.event.removeListener(j),null!==D&&google.maps.event.removeListener(D),null!==T&&google.maps.event.removeListener(T),M&&M(t)}}},[]),null!==k&&t(k)||null});let ClustererComponent=class ClustererComponent extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{markerClusterer:null}),_defineProperty(this,"setClustererCallback",()=>{null!==this.state.markerClusterer&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)})}componentDidMount(){if(this.context){var e=new R(this.context,[],this.props.options);this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:$,eventMap:z,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({markerClusterer:e}),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:$,eventMap:z,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){null!==this.state.markerClusterer&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),unregisterEvents(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return null!==this.state.markerClusterer?this.props.children(this.state.markerClusterer):null}};function cancelHandler(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}_defineProperty(ClustererComponent,"contextType",b);var W=function(){function InfoBox(e){void 0===e&&(e={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(InfoBox,google.maps.OverlayView),this.content=e.content||"",this.disableAutoPan=e.disableAutoPan||!1,this.maxWidth=e.maxWidth||0,this.pixelOffset=e.pixelOffset||new google.maps.Size(0,0),this.position=e.position||new google.maps.LatLng(0,0),this.zIndex=e.zIndex||null,this.boxClass=e.boxClass||"infoBox",this.boxStyle=e.boxStyle||{},this.closeBoxMargin=e.closeBoxMargin||"2px",this.closeBoxURL=e.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===e.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=e.infoBoxClearance||new google.maps.Size(1,1),void 0===e.visible&&(void 0===e.isHidden?e.visible=!0:e.visible=!e.isHidden),this.isHidden=!e.visible,this.alignBottom=e.alignBottom||!1,this.pane=e.pane||"floatPane",this.enableEventPropagation=e.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return InfoBox.prototype.createInfoBoxDiv=function(){var e=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var t=this.getPanes();if(null!==t&&t[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var s=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-s.left-s.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var n=0,o=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];n<o.length;n++){var r=o[n];this.eventListeners.push(google.maps.event.addListener(this.div,r,cancelHandler))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",function(){e.div&&(e.div.style.cursor="default")}))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",function(t){t.returnValue=!1,t.preventDefault&&t.preventDefault(),e.enableEventPropagation||cancelHandler(t)}),google.maps.event.trigger(this,"domready")}},InfoBox.prototype.getCloseBoxImg=function(){var e="";return""!==this.closeBoxURL&&(e='<img alt="" aria-hidden="true" src=\''+this.closeBoxURL+"' align=right style=' position: relative; cursor: pointer; margin: "+this.closeBoxMargin+";'>"),e},InfoBox.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},InfoBox.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},InfoBox.prototype.getCloseClickHandler=function(){return this.closeClickHandler},InfoBox.prototype.panBox=function(e){if(this.div&&!e){var t=this.getMap();if(t instanceof google.maps.Map){var s=0,n=0,o=t.getBounds();o&&!o.contains(this.position)&&t.setCenter(this.position);var r=t.getDiv(),i=r.offsetWidth,a=r.offsetHeight,l=this.pixelOffset.width,p=this.pixelOffset.height,d=this.div.offsetWidth,c=this.div.offsetHeight,g=this.infoBoxClearance.width,h=this.infoBoxClearance.height,m=this.getProjection().fromLatLngToContainerPixel(this.position);null!==m&&(m.x<-l+g?s=m.x+l-g:m.x+d+l+g>i&&(s=m.x+d+l+g-i),this.alignBottom?m.y<-p+h+c?n=m.y+p-h-c:m.y+p+h>a&&(n=m.y+p+h-a):m.y<-p+h?n=m.y+p-h:m.y+c+p+h>a&&(n=m.y+c+p+h-a)),0===s&&0===n||t.panBy(s,n)}}},InfoBox.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.div.style[t]=e[t]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var s=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*s+')"',this.div.style.filter="alpha(opacity="+100*s+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},InfoBox.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var t=this.div.ownerDocument,s=t&&t.defaultView?t.defaultView.getComputedStyle(this.div,""):null;s&&(e.top=parseInt(s.borderTopWidth||"",10)||0,e.bottom=parseInt(s.borderBottomWidth||"",10)||0,e.left=parseInt(s.borderLeftWidth||"",10)||0,e.right=parseInt(s.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var n=this.div.currentStyle;n&&(e.top=parseInt(n.borderTopWidth||"",10)||0,e.bottom=parseInt(n.borderBottomWidth||"",10)||0,e.left=parseInt(n.borderLeftWidth||"",10)||0,e.right=parseInt(n.borderRightWidth||"",10)||0)}return e},InfoBox.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},InfoBox.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection().fromLatLngToDivPixel(this.position);null!==e&&(this.div.style.left=e.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(e.y+this.pixelOffset.height)+"px":this.div.style.top=e.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},InfoBox.prototype.setOptions=function(e){void 0===e&&(e={}),void 0!==e.boxClass&&(this.boxClass=e.boxClass,this.setBoxStyle()),void 0!==e.boxStyle&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),void 0!==e.content&&this.setContent(e.content),void 0!==e.disableAutoPan&&(this.disableAutoPan=e.disableAutoPan),void 0!==e.maxWidth&&(this.maxWidth=e.maxWidth),void 0!==e.pixelOffset&&(this.pixelOffset=e.pixelOffset),void 0!==e.alignBottom&&(this.alignBottom=e.alignBottom),void 0!==e.position&&this.setPosition(e.position),void 0!==e.zIndex&&this.setZIndex(e.zIndex),void 0!==e.closeBoxMargin&&(this.closeBoxMargin=e.closeBoxMargin),void 0!==e.closeBoxURL&&(this.closeBoxURL=e.closeBoxURL),void 0!==e.infoBoxClearance&&(this.infoBoxClearance=e.infoBoxClearance),void 0!==e.isHidden&&(this.isHidden=e.isHidden),void 0!==e.visible&&(this.isHidden=!e.visible),void 0!==e.enableEventPropagation&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},InfoBox.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},InfoBox.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},InfoBox.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},InfoBox.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},InfoBox.prototype.getContent=function(){return this.content},InfoBox.prototype.getPosition=function(){return this.position},InfoBox.prototype.getZIndex=function(){return this.zIndex},InfoBox.prototype.getVisible=function(){return null!=this.getMap()&&!this.isHidden},InfoBox.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},InfoBox.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},InfoBox.prototype.open=function(e,t){var s=this;t&&(this.position=t.getPosition(),this.moveListener=google.maps.event.addListener(t,"position_changed",function(){var e=t.getPosition();s.setPosition(e)}),this.mapListener=google.maps.event.addListener(t,"map_changed",function(){s.setMap(t.map)})),this.setMap(e),this.div&&this.panBox()},InfoBox.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,t=this.eventListeners;e<t.length;e++){var s=t[e];google.maps.event.removeListener(s)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},InfoBox.prototype.extend=function(e,t){return(function(e){for(var t in e.prototype)Object.prototype.hasOwnProperty.call(this,t)||(this.prototype[t]=e.prototype[t]);return this}).apply(e,[t])},InfoBox}(),Z=["position"],N=["position"];function ownKeys$b(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$b(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$b(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$b(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}var H={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},K={options(e,t){e.setOptions(t)},position(e,t){t instanceof google.maps.LatLng?e.setPosition(t):e.setPosition(new google.maps.LatLng(t.lat,t.lng))},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},G={};(0,v.memo)(function(e){var{children:t,anchor:s,options:n,position:o,zIndex:r,onCloseClick:i,onDomReady:a,onContentChanged:l,onPositionChanged:p,onZindexChanged:d,onLoad:c,onUnmount:g}=e,h=(0,v.useContext)(b),[m,L]=(0,v.useState)(null),[E,C]=(0,v.useState)(null),[P,w]=(0,v.useState)(null),[x,S]=(0,v.useState)(null),[M,k]=(0,v.useState)(null),[O,_]=(0,v.useState)(null),j=(0,v.useRef)(null);return(0,v.useEffect)(()=>{h&&null!==m&&(m.close(),s?m.open(h,s):m.getPosition()&&m.open(h))},[h,m,s]),(0,v.useEffect)(()=>{n&&null!==m&&m.setOptions(n)},[m,n]),(0,v.useEffect)(()=>{if(o&&null!==m){var e=o instanceof google.maps.LatLng?o:new google.maps.LatLng(o.lat,o.lng);m.setPosition(e)}},[o]),(0,v.useEffect)(()=>{"number"==typeof r&&null!==m&&m.setZIndex(r)},[r]),(0,v.useEffect)(()=>{m&&i&&(null!==E&&google.maps.event.removeListener(E),C(google.maps.event.addListener(m,"closeclick",i)))},[i]),(0,v.useEffect)(()=>{m&&a&&(null!==P&&google.maps.event.removeListener(P),w(google.maps.event.addListener(m,"domready",a)))},[a]),(0,v.useEffect)(()=>{m&&l&&(null!==x&&google.maps.event.removeListener(x),S(google.maps.event.addListener(m,"content_changed",l)))},[l]),(0,v.useEffect)(()=>{m&&p&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(m,"position_changed",p)))},[p]),(0,v.useEffect)(()=>{m&&d&&(null!==O&&google.maps.event.removeListener(O),_(google.maps.event.addListener(m,"zindex_changed",d)))},[d]),(0,v.useEffect)(()=>{if(h){var e,t=n||G,{position:o}=t,r=_objectWithoutProperties(t,Z);!o||o instanceof google.maps.LatLng||(e=new google.maps.LatLng(o.lat,o.lng));var v=new W(_objectSpread$b(_objectSpread$b({},r),e?{position:e}:{}));j.current=document.createElement("div"),L(v),i&&C(google.maps.event.addListener(v,"closeclick",i)),a&&w(google.maps.event.addListener(v,"domready",a)),l&&S(google.maps.event.addListener(v,"content_changed",l)),p&&k(google.maps.event.addListener(v,"position_changed",p)),d&&_(google.maps.event.addListener(v,"zindex_changed",d)),v.setContent(j.current),s?v.open(h,s):v.getPosition()?v.open(h):y(!1,"You must provide either an anchor or a position prop for <InfoBox>."),c&&c(v)}return()=>{null!==m&&(E&&google.maps.event.removeListener(E),x&&google.maps.event.removeListener(x),P&&google.maps.event.removeListener(P),M&&google.maps.event.removeListener(M),O&&google.maps.event.removeListener(O),g&&g(m),m.close())}},[]),j.current?(0,f.createPortal)(v.Children.only(t),j.current):null});let InfoBoxComponent=class InfoBoxComponent extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"containerElement",null),_defineProperty(this,"state",{infoBox:null}),_defineProperty(this,"open",(e,t)=>{t?null!==this.context&&e.open(this.context,t):e.getPosition()?null!==this.context&&e.open(this.context):y(!1,"You must provide either an anchor or a position prop for <InfoBox>.")}),_defineProperty(this,"setInfoBoxCallback",()=>{null!==this.state.infoBox&&null!==this.containerElement&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))})}componentDidMount(){var e,t=this.props.options||{},{position:s}=t,n=_objectWithoutProperties(t,N);!s||s instanceof google.maps.LatLng||(e=new google.maps.LatLng(s.lat,s.lng));var o=new W(_objectSpread$b(_objectSpread$b({},n),e?{position:e}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:K,eventMap:H,prevProps:{},nextProps:this.props,instance:o}),this.setState({infoBox:o},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:t}=this.state;null!==t&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:K,eventMap:H,prevProps:e,nextProps:this.props,instance:t}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:t}=this.state;null!==t&&(e&&e(t),unregisterEvents(this.registeredEvents),t.close())}render(){return this.containerElement?(0,f.createPortal)(v.Children.only(this.props.children),this.containerElement):null}};_defineProperty(InfoBoxComponent,"contextType",b);var F=getDefaultExportFromCjs$1(g?c:(g=1,c=function equal(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){if((s=e.length)!=t.length)return!1;for(n=s;0!=n--;)if(!equal(e[n],t[n]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if((s=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=s;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,o[n]))return!1;for(n=s;0!=n--;){var s,n,o,r=o[n];if(!equal(e[r],t[r]))return!1}return!0}return e!=e&&t!=t})),Y=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];let KDBush=class KDBush{static from(e){if(!(e instanceof ArrayBuffer))throw Error("Data must be an instance of ArrayBuffer.");var[t,s]=new Uint8Array(e,0,2);if(219!==t)throw Error("Data does not appear to be in a KDBush format.");var n=s>>4;if(1!==n)throw Error("Got v".concat(n," data when expected v").concat(1,"."));var o=Y[15&s];if(!o)throw Error("Unrecognized array type.");var[r]=new Uint16Array(e,2,1),[i]=new Uint32Array(e,4,1);return new KDBush(i,r,o,e)}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Float64Array,n=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=s,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var o=Y.indexOf(this.ArrayType),r=2*e*this.ArrayType.BYTES_PER_ELEMENT,i=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-i%8)%8;if(o<0)throw Error("Unexpected typed array class: ".concat(s,"."));n&&n instanceof ArrayBuffer?(this.data=n,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+i+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+r+i+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+i+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+o]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){var s=this._pos>>1;return this.ids[s]=s,this.coords[this._pos++]=e,this.coords[this._pos++]=t,s}finish(){var e=this._pos>>1;if(e!==this.numItems)throw Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return function sort(e,t,s,n,o,r){if(!(o-n<=s)){var i=n+o>>1;(function select(e,t,s,n,o,r){for(;o>n;){if(o-n>600){var i=o-n+1,a=s-n+1,l=Math.log(i),p=.5*Math.exp(2*l/3),d=.5*Math.sqrt(l*p*(i-p)/i)*(a-i/2<0?-1:1),c=Math.max(n,Math.floor(s-a*p/i+d)),g=Math.min(o,Math.floor(s+(i-a)*p/i+d));select(e,t,s,c,g,r)}var h=t[2*s+r],m=n,v=o;for(swapItem(e,t,n,s),t[2*o+r]>h&&swapItem(e,t,n,o);m<v;){for(swapItem(e,t,m,v),m++,v--;t[2*m+r]<h;)m++;for(;t[2*v+r]>h;)v--}t[2*n+r]===h?swapItem(e,t,n,v):swapItem(e,t,++v,o),v<=s&&(n=v+1),s<=v&&(o=v-1)}})(e,t,i,n,o,r),sort(e,t,s,n,i-1,1-r),sort(e,t,s,i+1,o,1-r)}}(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,s,n){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:r,nodeSize:i}=this,a=[0,o.length-1,0],l=[];a.length;){var p=a.pop()||0,d=a.pop()||0,c=a.pop()||0;if(d-c<=i){for(var g=c;g<=d;g++){var h=r[2*g],m=r[2*g+1];h>=e&&h<=s&&m>=t&&m<=n&&l.push(o[g])}continue}var v=c+d>>1,f=r[2*v],y=r[2*v+1];f>=e&&f<=s&&y>=t&&y<=n&&l.push(o[v]),(0===p?e<=f:t<=y)&&(a.push(c),a.push(v-1),a.push(1-p)),(0===p?s>=f:n>=y)&&(a.push(v+1),a.push(d),a.push(1-p))}return l}within(e,t,s){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");for(var{ids:n,coords:o,nodeSize:r}=this,i=[0,n.length-1,0],a=[],l=s*s;i.length;){var p=i.pop()||0,d=i.pop()||0,c=i.pop()||0;if(d-c<=r){for(var g=c;g<=d;g++)sqDist(o[2*g],o[2*g+1],e,t)<=l&&a.push(n[g]);continue}var h=c+d>>1,m=o[2*h],v=o[2*h+1];sqDist(m,v,e,t)<=l&&a.push(n[h]),(0===p?e-s<=m:t-s<=v)&&(i.push(c),i.push(h-1),i.push(1-p)),(0===p?e+s>=m:t+s>=v)&&(i.push(h+1),i.push(d),i.push(1-p))}return a}};function swapItem(e,t,s,n){swap(e,s,n),swap(t,2*s,2*n),swap(t,2*s+1,2*n+1)}function swap(e,t,s){var n=e[t];e[t]=e[s],e[s]=n}function sqDist(e,t,s,n){var o=e-s,r=t-n;return o*o+r*r}var q={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},J=Math.fround||(r=new Float32Array(1),e=>(r[0]=+e,r[0]));let Supercluster=class Supercluster{constructor(e){this.options=Object.assign(Object.create(q),e),this.trees=Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:t,minZoom:s,maxZoom:n}=this.options;t&&console.time("total time");var o="prepare ".concat(e.length," points");t&&console.time(o),this.points=e;for(var r=[],i=0;i<e.length;i++){var a=e[i];if(a.geometry){var[l,p]=a.geometry.coordinates,d=J(lngX(l)),c=J(latY(p));r.push(d,c,1/0,i,-1,1),this.options.reduce&&r.push(0)}}var g=this.trees[n+1]=this._createTree(r);t&&console.timeEnd(o);for(var h=n;h>=s;h--){var m=+Date.now();g=this.trees[h]=this._createTree(this._cluster(g,h)),t&&console.log("z%d: %d clusters in %dms",h,g.numItems,+Date.now()-m)}return t&&console.timeEnd("total time"),this}getClusters(e,t){var s=((e[0]+180)%360+360)%360-180,n=Math.max(-90,Math.min(90,e[1])),o=180===e[2]?180:((e[2]+180)%360+360)%360-180,r=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)s=-180,o=180;else if(s>o){var i=this.getClusters([s,n,180,r],t),a=this.getClusters([-180,n,o,r],t);return i.concat(a)}var l=this.trees[this._limitZoom(t)],p=l.range(lngX(s),latY(r),lngX(o),latY(n)),d=l.data,c=[];for(var g of p){var h=this.stride*g;c.push(d[h+5]>1?getClusterJSON(d,h,this.clusterProps):this.points[d[h+3]])}return c}getChildren(e){var t=this._getOriginId(e),s=this._getOriginZoom(e),n="No cluster with the specified id.",o=this.trees[s];if(!o)throw Error(n);var r=o.data;if(t*this.stride>=r.length)throw Error(n);var i=this.options.radius/(this.options.extent*Math.pow(2,s-1)),a=r[t*this.stride],l=r[t*this.stride+1],p=o.within(a,l,i),d=[];for(var c of p){var g=c*this.stride;r[g+4]===e&&d.push(r[g+5]>1?getClusterJSON(r,g,this.clusterProps):this.points[r[g+3]])}if(0===d.length)throw Error(n);return d}getLeaves(e,t,s){t=t||10,s=s||0;var n=[];return this._appendLeaves(n,e,t,s,0),n}getTile(e,t,s){var n=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:r,radius:i}=this.options,a=i/r,l=(s-a)/o,p=(s+1+a)/o,d={features:[]};return this._addTileFeatures(n.range((t-a)/o,l,(t+1+a)/o,p),n.data,t,s,o,d),0===t&&this._addTileFeatures(n.range(1-a/o,l,1,p),n.data,o,s,o,d),t===o-1&&this._addTileFeatures(n.range(0,l,a/o,p),n.data,-1,s,o,d),d.features.length?d:null}getClusterExpansionZoom(e){for(var t=this._getOriginZoom(e)-1;t<=this.options.maxZoom;){var s=this.getChildren(e);if(t++,1!==s.length)break;e=s[0].properties.cluster_id}return t}_appendLeaves(e,t,s,n,o){for(var r of this.getChildren(t)){var i=r.properties;if(i&&i.cluster?o+i.point_count<=n?o+=i.point_count:o=this._appendLeaves(e,i.cluster_id,s,n,o):o<n?o++:e.push(r),e.length===s)break}return o}_createTree(e){for(var t=new KDBush(e.length/this.stride|0,this.options.nodeSize,Float32Array),s=0;s<e.length;s+=this.stride)t.add(e[s],e[s+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,s,n,o,r){for(var i of e){var a=i*this.stride,l=t[a+5]>1,p=void 0,d=void 0,c=void 0;if(l)p=getClusterProperties(t,a,this.clusterProps),d=t[a],c=t[a+1];else{var g=this.points[t[a+3]];p=g.properties;var[h,m]=g.geometry.coordinates;d=lngX(h),c=latY(m)}var v={type:1,geometry:[[Math.round(this.options.extent*(d*o-s)),Math.round(this.options.extent*(c*o-n))]],tags:p},f=void 0;void 0!==(f=l||this.options.generateId?t[a+3]:this.points[t[a+3]].id)&&(v.id=f),r.features.push(v)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){for(var{radius:s,extent:n,reduce:o,minPoints:r}=this.options,i=s/(n*Math.pow(2,t)),a=e.data,l=[],p=this.stride,d=0;d<a.length;d+=p)if(!(a[d+2]<=t)){a[d+2]=t;var c=a[d],g=a[d+1],h=e.within(a[d],a[d+1],i),m=a[d+5],v=m;for(var f of h){var y=f*p;a[y+2]>t&&(v+=a[y+5])}if(v>m&&v>=r){var b=c*m,L=g*m,E=void 0,C=-1,P=((d/p|0)<<5)+(t+1)+this.points.length;for(var w of h){var x=w*p;if(!(a[x+2]<=t)){a[x+2]=t;var S=a[x+5];b+=a[x]*S,L+=a[x+1]*S,a[x+4]=P,o&&(E||(E=this._map(a,d,!0),C=this.clusterProps.length,this.clusterProps.push(E)),o(E,this._map(a,x)))}}a[d+4]=P,l.push(b/v,L/v,1/0,P,-1,v),o&&l.push(C)}else{for(var M=0;M<p;M++)l.push(a[d+M]);if(v>1)for(var k of h){var O=k*p;if(!(a[O+2]<=t)){a[O+2]=t;for(var _=0;_<p;_++)l.push(a[O+_])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,s){if(e[t+5]>1){var n=this.clusterProps[e[t+6]];return s?Object.assign({},n):n}var o=this.points[e[t+3]].properties,r=this.options.map(o);return s&&r===o?Object.assign({},r):r}};function getClusterJSON(e,t,s){return{type:"Feature",id:e[t+3],properties:getClusterProperties(e,t,s),geometry:{type:"Point",coordinates:[(e[t]-.5)*360,360*Math.atan(Math.exp((180-360*e[t+1])*Math.PI/180))/Math.PI-90]}}}function getClusterProperties(e,t,s){var n=e[t+5],o=e[t+6];return Object.assign(-1===o?{}:Object.assign({},s[o]),{cluster:!0,cluster_id:e[t+3],point_count:n,point_count_abbreviated:n>=1e4?"".concat(Math.round(n/1e3),"k"):n>=1e3?"".concat(Math.round(n/100)/10,"k"):n})}function lngX(e){return e/360+.5}function latY(e){var t=Math.sin(e*Math.PI/180),s=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return s<0?0:s>1?1:s}let MarkerUtils=class MarkerUtils{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}};let Cluster=class Cluster{constructor(e){var{markers:t,position:s}=e;this.markers=t,s&&(s instanceof google.maps.LatLng?this._position=s:this._position=new google.maps.LatLng(s))}get bounds(){if(0!==this.markers.length||this._position){var e=new google.maps.LatLngBounds(this._position,this._position);for(var t of this.markers)e.extend(MarkerUtils.getPosition(t));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter(e=>MarkerUtils.getVisible(e)).length}push(e){this.markers.push(e)}delete(){this.marker&&(MarkerUtils.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}};var extendBoundsToPaddedViewport=(e,t,s)=>{var{northEast:n,southWest:o}=latLngBoundsToPixelBounds(e,t);return pixelBoundsToLatLngBounds(extendPixelBounds({northEast:n,southWest:o},s),t)},latLngBoundsToPixelBounds=(e,t)=>({northEast:t.fromLatLngToDivPixel(e.getNorthEast()),southWest:t.fromLatLngToDivPixel(e.getSouthWest())}),extendPixelBounds=(e,t)=>{var{northEast:s,southWest:n}=e;return s.x+=t,s.y-=t,n.x-=t,n.y+=t,{northEast:s,southWest:n}},pixelBoundsToLatLngBounds=(e,t)=>{var{northEast:s,southWest:n}=e,o=t.fromDivPixelToLatLng(n),r=t.fromDivPixelToLatLng(s);return new google.maps.LatLngBounds(o,r)};let AbstractAlgorithm=class AbstractAlgorithm{constructor(e){var{maxZoom:t=16}=e;this.maxZoom=t}noop(e){var{markers:t}=e;return noop$1(t)}};var noop$1=e=>e.map(e=>new Cluster({position:MarkerUtils.getPosition(e),markers:[e]}));let SuperClusterAlgorithm=class SuperClusterAlgorithm extends AbstractAlgorithm{constructor(e){var{maxZoom:t,radius:s=60}=e,n=/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function(e,t){var s={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(s[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(s[n[o]]=e[n[o]]);return s}(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new Supercluster(Object.assign({maxZoom:this.maxZoom,radius:s},n))}calculate(e){var t=!1,s={zoom:e.map.getZoom()};if(!F(e.markers,this.markers)){t=!0,this.markers=[...e.markers];var n=this.markers.map(e=>{var t=MarkerUtils.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}});this.superCluster.load(n)}return!t&&(this.state.zoom<=this.maxZoom||s.zoom<=this.maxZoom)&&(t=!F(this.state,s)),this.state=s,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster(e){var{map:t}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map(e=>this.transformCluster(e))}transformCluster(e){var{geometry:{coordinates:[t,s]},properties:n}=e;if(n.cluster)return new Cluster({markers:this.superCluster.getLeaves(n.cluster_id,1/0).map(e=>e.properties.marker),position:{lat:s,lng:t}});var o=n.marker;return new Cluster({markers:[o],position:MarkerUtils.getPosition(o)})}};let ClusterStats=class ClusterStats{constructor(e,t){this.markers={sum:e.length};var s=t.map(e=>e.count),n=s.reduce((e,t)=>e+t,0);this.clusters={count:t.length,markers:{mean:n/t.length,sum:n,min:Math.min(...s),max:Math.max(...s)}}}};let DefaultRenderer=class DefaultRenderer{render(e,t,s){var{count:n,position:o}=e,r=n>Math.max(10,t.clusters.markers.mean)?"#ff0000":"#0000ff",i='<svg fill="'.concat(r,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">').concat(n,"</text>\n</svg>"),a="Cluster of ".concat(n," markers"),l=Number(google.maps.Marker.MAX_ZINDEX)+n;if(MarkerUtils.isAdvancedMarkerAvailable(s)){var p=new DOMParser().parseFromString(i,"image/svg+xml").documentElement;return p.setAttribute("transform","translate(0 25)"),new google.maps.marker.AdvancedMarkerElement({map:s,position:o,zIndex:l,title:a,content:p})}var d={position:o,zIndex:l,title:a,icon:{url:"data:image/svg+xml;base64,".concat(btoa(i)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(d)}};let OverlayViewSafe=class OverlayViewSafe{constructor(){!function(e,t){for(var s in t.prototype)e.prototype[s]=t.prototype[s]}(OverlayViewSafe,google.maps.OverlayView)}};(i=h||(h={})).CLUSTERING_BEGIN="clusteringbegin",i.CLUSTERING_END="clusteringend",i.CLUSTER_CLICK="click";var defaultOnClusterClickHandler=(e,t,s)=>{s.fitBounds(t.bounds)};let MarkerClusterer=class MarkerClusterer extends OverlayViewSafe{constructor(e){var{map:t,markers:s=[],algorithmOptions:n={},algorithm:o=new SuperClusterAlgorithm(n),renderer:r=new DefaultRenderer,onClusterClick:i=defaultOnClusterClickHandler}=e;super(),this.markers=[...s],this.clusters=[],this.algorithm=o,this.renderer=r,this.onClusterClick=i,t&&this.setMap(t)}addMarker(e,t){!this.markers.includes(e)&&(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach(e=>{this.addMarker(e,!0)}),t||this.render()}removeMarker(e,t){var s=this.markers.indexOf(e);return -1!==s&&(MarkerUtils.setMap(e,null),this.markers.splice(s,1),t||this.render(),!0)}removeMarkers(e,t){var s=!1;return e.forEach(e=>{s=this.removeMarker(e,!0)||s}),s&&!t&&this.render(),s}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,h.CLUSTERING_BEGIN,this);var{clusters:t,changed:s}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(s||void 0==s){var n=new Set;for(var o of t)1==o.markers.length&&n.add(o.markers[0]);var r=[];for(var i of this.clusters)null!=i.marker&&(1==i.markers.length?n.has(i.marker)||MarkerUtils.setMap(i.marker,null):r.push(i.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame(()=>r.forEach(e=>MarkerUtils.setMap(e,null)))}google.maps.event.trigger(this,h.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach(e=>MarkerUtils.setMap(e,null)),this.clusters.forEach(e=>e.delete()),this.clusters=[]}renderClusters(){var e=new ClusterStats(this.markers,this.clusters),t=this.getMap();this.clusters.forEach(s=>{1===s.markers.length?s.marker=s.markers[0]:(s.marker=this.renderer.render(s,e,t),s.markers.forEach(e=>MarkerUtils.setMap(e,null)),this.onClusterClick&&s.marker.addListener("click",e=>{google.maps.event.trigger(this,h.CLUSTER_CLICK,s),this.onClusterClick(e,s,t)})),MarkerUtils.setMap(s.marker,t)})}};function ownKeys$a(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$a(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$a(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$a(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}(0,v.memo)(function(e){var{children:t,options:s}=e,n=function(e){var t,s=(y(!!v.useContext,"useGoogleMap is React hook and requires React version 16.8+"),y(!!(t=(0,v.useContext)(b)),"useGoogleMap needs a GoogleMap available up in the tree"),t),[n,o]=(0,v.useState)(null);return(0,v.useEffect)(()=>{s&&null===n&&o(new MarkerClusterer(_objectSpread$a(_objectSpread$a({},e),{},{map:s})))},[s]),n}(s);return null!==n?t(n):null});var X={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Q={options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},zIndex(e,t){e.setZIndex(t)}};(0,v.memo)(function(e){var{children:t,anchor:s,options:n,position:o,zIndex:r,onCloseClick:i,onDomReady:a,onContentChanged:l,onPositionChanged:p,onZindexChanged:d,onLoad:c,onUnmount:g}=e,h=(0,v.useContext)(b),[m,L]=(0,v.useState)(null),[E,C]=(0,v.useState)(null),[P,w]=(0,v.useState)(null),[x,S]=(0,v.useState)(null),[M,k]=(0,v.useState)(null),[O,_]=(0,v.useState)(null),j=(0,v.useRef)(null);return(0,v.useEffect)(()=>{null!==m&&(m.close(),s?m.open(h,s):m.getPosition()&&m.open(h))},[h,m,s]),(0,v.useEffect)(()=>{n&&null!==m&&m.setOptions(n)},[m,n]),(0,v.useEffect)(()=>{o&&null!==m&&m.setPosition(o)},[o]),(0,v.useEffect)(()=>{"number"==typeof r&&null!==m&&m.setZIndex(r)},[r]),(0,v.useEffect)(()=>{m&&i&&(null!==E&&google.maps.event.removeListener(E),C(google.maps.event.addListener(m,"closeclick",i)))},[i]),(0,v.useEffect)(()=>{m&&a&&(null!==P&&google.maps.event.removeListener(P),w(google.maps.event.addListener(m,"domready",a)))},[a]),(0,v.useEffect)(()=>{m&&l&&(null!==x&&google.maps.event.removeListener(x),S(google.maps.event.addListener(m,"content_changed",l)))},[l]),(0,v.useEffect)(()=>{m&&p&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(m,"position_changed",p)))},[p]),(0,v.useEffect)(()=>{m&&d&&(null!==O&&google.maps.event.removeListener(O),_(google.maps.event.addListener(m,"zindex_changed",d)))},[d]),(0,v.useEffect)(()=>{var e=new google.maps.InfoWindow(n);return L(e),j.current=document.createElement("div"),i&&C(google.maps.event.addListener(e,"closeclick",i)),a&&w(google.maps.event.addListener(e,"domready",a)),l&&S(google.maps.event.addListener(e,"content_changed",l)),p&&k(google.maps.event.addListener(e,"position_changed",p)),d&&_(google.maps.event.addListener(e,"zindex_changed",d)),e.setContent(j.current),o&&e.setPosition(o),r&&e.setZIndex(r),s?e.open(h,s):e.getPosition()?e.open(h):y(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),c&&c(e),()=>{E&&google.maps.event.removeListener(E),x&&google.maps.event.removeListener(x),P&&google.maps.event.removeListener(P),M&&google.maps.event.removeListener(M),O&&google.maps.event.removeListener(O),g&&g(e),e.close()}},[]),j.current?(0,f.createPortal)(v.Children.only(t),j.current):null});let InfoWindow=class InfoWindow extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"containerElement",null),_defineProperty(this,"state",{infoWindow:null}),_defineProperty(this,"open",(e,t)=>{t?e.open(this.context,t):e.getPosition()?e.open(this.context):y(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")}),_defineProperty(this,"setInfoWindowCallback",()=>{null!==this.state.infoWindow&&null!==this.containerElement&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))})}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:Q,eventMap:X,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({infoWindow:e}),this.setInfoWindowCallback)}componentDidUpdate(e){null!==this.state.infoWindow&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:Q,eventMap:X,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){null!==this.state.infoWindow&&(unregisterEvents(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?(0,f.createPortal)(v.Children.only(this.props.children),this.containerElement):null}};function ownKeys$9(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$9(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$9(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$9(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(InfoWindow,"contextType",b);var ee={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},et={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},visible(e,t){e.setVisible(t)}},es={};(0,v.memo)(function(e){var{options:t,draggable:s,editable:n,visible:o,path:r,onDblClick:i,onDragEnd:a,onDragStart:l,onMouseDown:p,onMouseMove:d,onMouseOut:c,onMouseOver:g,onMouseUp:h,onRightClick:m,onClick:f,onDrag:y,onLoad:L,onUnmount:E}=e,C=(0,v.useContext)(b),[P,w]=(0,v.useState)(null),[x,S]=(0,v.useState)(null),[M,k]=(0,v.useState)(null),[O,_]=(0,v.useState)(null),[j,I]=(0,v.useState)(null),[D,B]=(0,v.useState)(null),[T,A]=(0,v.useState)(null),[U,R]=(0,v.useState)(null),[z,$]=(0,v.useState)(null),[V,W]=(0,v.useState)(null),[Z,N]=(0,v.useState)(null),[H,K]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==P&&P.setMap(C)},[C]),(0,v.useEffect)(()=>{void 0!==t&&null!==P&&P.setOptions(t)},[P,t]),(0,v.useEffect)(()=>{void 0!==s&&null!==P&&P.setDraggable(s)},[P,s]),(0,v.useEffect)(()=>{void 0!==n&&null!==P&&P.setEditable(n)},[P,n]),(0,v.useEffect)(()=>{void 0!==o&&null!==P&&P.setVisible(o)},[P,o]),(0,v.useEffect)(()=>{void 0!==r&&null!==P&&P.setPath(r)},[P,r]),(0,v.useEffect)(()=>{P&&i&&(null!==x&&google.maps.event.removeListener(x),S(google.maps.event.addListener(P,"dblclick",i)))},[i]),(0,v.useEffect)(()=>{P&&a&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(P,"dragend",a)))},[a]),(0,v.useEffect)(()=>{P&&l&&(null!==O&&google.maps.event.removeListener(O),_(google.maps.event.addListener(P,"dragstart",l)))},[l]),(0,v.useEffect)(()=>{P&&p&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(P,"mousedown",p)))},[p]),(0,v.useEffect)(()=>{P&&d&&(null!==D&&google.maps.event.removeListener(D),B(google.maps.event.addListener(P,"mousemove",d)))},[d]),(0,v.useEffect)(()=>{P&&c&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(P,"mouseout",c)))},[c]),(0,v.useEffect)(()=>{P&&g&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(P,"mouseover",g)))},[g]),(0,v.useEffect)(()=>{P&&h&&(null!==z&&google.maps.event.removeListener(z),$(google.maps.event.addListener(P,"mouseup",h)))},[h]),(0,v.useEffect)(()=>{P&&m&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(P,"rightclick",m)))},[m]),(0,v.useEffect)(()=>{P&&f&&(null!==Z&&google.maps.event.removeListener(Z),N(google.maps.event.addListener(P,"click",f)))},[f]),(0,v.useEffect)(()=>{P&&y&&(null!==H&&google.maps.event.removeListener(H),K(google.maps.event.addListener(P,"drag",y)))},[y]),(0,v.useEffect)(()=>{var e=new google.maps.Polyline(_objectSpread$9(_objectSpread$9({},t||es),{},{map:C}));return r&&e.setPath(r),void 0!==o&&e.setVisible(o),void 0!==n&&e.setEditable(n),void 0!==s&&e.setDraggable(s),i&&S(google.maps.event.addListener(e,"dblclick",i)),a&&k(google.maps.event.addListener(e,"dragend",a)),l&&_(google.maps.event.addListener(e,"dragstart",l)),p&&I(google.maps.event.addListener(e,"mousedown",p)),d&&B(google.maps.event.addListener(e,"mousemove",d)),c&&A(google.maps.event.addListener(e,"mouseout",c)),g&&R(google.maps.event.addListener(e,"mouseover",g)),h&&$(google.maps.event.addListener(e,"mouseup",h)),m&&W(google.maps.event.addListener(e,"rightclick",m)),f&&N(google.maps.event.addListener(e,"click",f)),y&&K(google.maps.event.addListener(e,"drag",y)),w(e),L&&L(e),()=>{null!==x&&google.maps.event.removeListener(x),null!==M&&google.maps.event.removeListener(M),null!==O&&google.maps.event.removeListener(O),null!==j&&google.maps.event.removeListener(j),null!==D&&google.maps.event.removeListener(D),null!==T&&google.maps.event.removeListener(T),null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==V&&google.maps.event.removeListener(V),null!==Z&&google.maps.event.removeListener(Z),E&&E(e),e.setMap(null)}},[]),null});let Polyline=class Polyline extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{polyline:null}),_defineProperty(this,"setPolylineCallback",()=>{null!==this.state.polyline&&this.props.onLoad&&this.props.onLoad(this.state.polyline)})}componentDidMount(){var e=new google.maps.Polyline(_objectSpread$9(_objectSpread$9({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:et,eventMap:ee,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{polyline:e}},this.setPolylineCallback)}componentDidUpdate(e){null!==this.state.polyline&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:et,eventMap:ee,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){null!==this.state.polyline&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),unregisterEvents(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}};function ownKeys$8(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$8(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$8(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$8(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(Polyline,"contextType",b);var en={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},eo={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},paths(e,t){e.setPaths(t)},visible(e,t){e.setVisible(t)}};(0,v.memo)(function(e){var{options:t,draggable:s,editable:n,visible:o,path:r,paths:i,onDblClick:a,onDragEnd:l,onDragStart:p,onMouseDown:d,onMouseMove:c,onMouseOut:g,onMouseOver:h,onMouseUp:m,onRightClick:f,onClick:y,onDrag:L,onLoad:E,onUnmount:C,onEdit:P}=e,w=(0,v.useContext)(b),[x,S]=(0,v.useState)(null),[M,k]=(0,v.useState)(null),[O,_]=(0,v.useState)(null),[j,I]=(0,v.useState)(null),[D,B]=(0,v.useState)(null),[T,A]=(0,v.useState)(null),[U,R]=(0,v.useState)(null),[z,$]=(0,v.useState)(null),[V,W]=(0,v.useState)(null),[Z,N]=(0,v.useState)(null),[H,K]=(0,v.useState)(null),[G,F]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==x&&x.setMap(w)},[w]),(0,v.useEffect)(()=>{void 0!==t&&null!==x&&x.setOptions(t)},[x,t]),(0,v.useEffect)(()=>{void 0!==s&&null!==x&&x.setDraggable(s)},[x,s]),(0,v.useEffect)(()=>{void 0!==n&&null!==x&&x.setEditable(n)},[x,n]),(0,v.useEffect)(()=>{void 0!==o&&null!==x&&x.setVisible(o)},[x,o]),(0,v.useEffect)(()=>{void 0!==r&&null!==x&&x.setPath(r)},[x,r]),(0,v.useEffect)(()=>{void 0!==i&&null!==x&&x.setPaths(i)},[x,i]),(0,v.useEffect)(()=>{x&&"function"==typeof a&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(x,"dblclick",a)))},[a]),(0,v.useEffect)(()=>{x&&(google.maps.event.addListener(x.getPath(),"insert_at",()=>{null==P||P(x)}),google.maps.event.addListener(x.getPath(),"set_at",()=>{null==P||P(x)}),google.maps.event.addListener(x.getPath(),"remove_at",()=>{null==P||P(x)}))},[x,P]),(0,v.useEffect)(()=>{x&&"function"==typeof l&&(null!==O&&google.maps.event.removeListener(O),_(google.maps.event.addListener(x,"dragend",l)))},[l]),(0,v.useEffect)(()=>{x&&"function"==typeof p&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(x,"dragstart",p)))},[p]),(0,v.useEffect)(()=>{x&&"function"==typeof d&&(null!==D&&google.maps.event.removeListener(D),B(google.maps.event.addListener(x,"mousedown",d)))},[d]),(0,v.useEffect)(()=>{x&&"function"==typeof c&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(x,"mousemove",c)))},[c]),(0,v.useEffect)(()=>{x&&"function"==typeof g&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(x,"mouseout",g)))},[g]),(0,v.useEffect)(()=>{x&&"function"==typeof h&&(null!==z&&google.maps.event.removeListener(z),$(google.maps.event.addListener(x,"mouseover",h)))},[h]),(0,v.useEffect)(()=>{x&&"function"==typeof m&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(x,"mouseup",m)))},[m]),(0,v.useEffect)(()=>{x&&"function"==typeof f&&(null!==Z&&google.maps.event.removeListener(Z),N(google.maps.event.addListener(x,"rightclick",f)))},[f]),(0,v.useEffect)(()=>{x&&"function"==typeof y&&(null!==H&&google.maps.event.removeListener(H),K(google.maps.event.addListener(x,"click",y)))},[y]),(0,v.useEffect)(()=>{x&&"function"==typeof L&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(x,"drag",L)))},[L]),(0,v.useEffect)(()=>{var e=new google.maps.Polygon(_objectSpread$8(_objectSpread$8({},t),{},{map:w}));return r&&e.setPath(r),i&&e.setPaths(i),void 0!==o&&e.setVisible(o),void 0!==n&&e.setEditable(n),void 0!==s&&e.setDraggable(s),a&&k(google.maps.event.addListener(e,"dblclick",a)),l&&_(google.maps.event.addListener(e,"dragend",l)),p&&I(google.maps.event.addListener(e,"dragstart",p)),d&&B(google.maps.event.addListener(e,"mousedown",d)),c&&A(google.maps.event.addListener(e,"mousemove",c)),g&&R(google.maps.event.addListener(e,"mouseout",g)),h&&$(google.maps.event.addListener(e,"mouseover",h)),m&&W(google.maps.event.addListener(e,"mouseup",m)),f&&N(google.maps.event.addListener(e,"rightclick",f)),y&&K(google.maps.event.addListener(e,"click",y)),L&&F(google.maps.event.addListener(e,"drag",L)),S(e),E&&E(e),()=>{null!==M&&google.maps.event.removeListener(M),null!==O&&google.maps.event.removeListener(O),null!==j&&google.maps.event.removeListener(j),null!==D&&google.maps.event.removeListener(D),null!==T&&google.maps.event.removeListener(T),null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==V&&google.maps.event.removeListener(V),null!==Z&&google.maps.event.removeListener(Z),null!==H&&google.maps.event.removeListener(H),C&&C(e),e.setMap(null)}},[]),null});let Polygon=class Polygon extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eo,eventMap:en,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eo,eventMap:en,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),unregisterEvents(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}};function ownKeys$7(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$7(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$7(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$7(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(Polygon,"contextType",b);var er={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},ei={bounds(e,t){e.setBounds(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},visible(e,t){e.setVisible(t)}};(0,v.memo)(function(e){var{options:t,bounds:s,draggable:n,editable:o,visible:r,onDblClick:i,onDragEnd:a,onDragStart:l,onMouseDown:p,onMouseMove:d,onMouseOut:c,onMouseOver:g,onMouseUp:h,onRightClick:m,onClick:f,onDrag:y,onBoundsChanged:L,onLoad:E,onUnmount:C}=e,P=(0,v.useContext)(b),[w,x]=(0,v.useState)(null),[S,M]=(0,v.useState)(null),[k,O]=(0,v.useState)(null),[_,j]=(0,v.useState)(null),[I,D]=(0,v.useState)(null),[B,T]=(0,v.useState)(null),[A,U]=(0,v.useState)(null),[R,z]=(0,v.useState)(null),[$,V]=(0,v.useState)(null),[W,Z]=(0,v.useState)(null),[N,H]=(0,v.useState)(null),[K,G]=(0,v.useState)(null),[F,Y]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==w&&w.setMap(P)},[P]),(0,v.useEffect)(()=>{void 0!==t&&null!==w&&w.setOptions(t)},[w,t]),(0,v.useEffect)(()=>{void 0!==n&&null!==w&&w.setDraggable(n)},[w,n]),(0,v.useEffect)(()=>{void 0!==o&&null!==w&&w.setEditable(o)},[w,o]),(0,v.useEffect)(()=>{void 0!==r&&null!==w&&w.setVisible(r)},[w,r]),(0,v.useEffect)(()=>{void 0!==s&&null!==w&&w.setBounds(s)},[w,s]),(0,v.useEffect)(()=>{w&&i&&(null!==S&&google.maps.event.removeListener(S),M(google.maps.event.addListener(w,"dblclick",i)))},[i]),(0,v.useEffect)(()=>{w&&a&&(null!==k&&google.maps.event.removeListener(k),O(google.maps.event.addListener(w,"dragend",a)))},[a]),(0,v.useEffect)(()=>{w&&l&&(null!==_&&google.maps.event.removeListener(_),j(google.maps.event.addListener(w,"dragstart",l)))},[l]),(0,v.useEffect)(()=>{w&&p&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(w,"mousedown",p)))},[p]),(0,v.useEffect)(()=>{w&&d&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(w,"mousemove",d)))},[d]),(0,v.useEffect)(()=>{w&&c&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(w,"mouseout",c)))},[c]),(0,v.useEffect)(()=>{w&&g&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(w,"mouseover",g)))},[g]),(0,v.useEffect)(()=>{w&&h&&(null!==$&&google.maps.event.removeListener($),V(google.maps.event.addListener(w,"mouseup",h)))},[h]),(0,v.useEffect)(()=>{w&&m&&(null!==W&&google.maps.event.removeListener(W),Z(google.maps.event.addListener(w,"rightclick",m)))},[m]),(0,v.useEffect)(()=>{w&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(w,"click",f)))},[f]),(0,v.useEffect)(()=>{w&&y&&(null!==K&&google.maps.event.removeListener(K),G(google.maps.event.addListener(w,"drag",y)))},[y]),(0,v.useEffect)(()=>{w&&L&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(w,"bounds_changed",L)))},[L]),(0,v.useEffect)(()=>{var e=new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({},t),{},{map:P}));return void 0!==r&&e.setVisible(r),void 0!==o&&e.setEditable(o),void 0!==n&&e.setDraggable(n),void 0!==s&&e.setBounds(s),i&&M(google.maps.event.addListener(e,"dblclick",i)),a&&O(google.maps.event.addListener(e,"dragend",a)),l&&j(google.maps.event.addListener(e,"dragstart",l)),p&&D(google.maps.event.addListener(e,"mousedown",p)),d&&T(google.maps.event.addListener(e,"mousemove",d)),c&&U(google.maps.event.addListener(e,"mouseout",c)),g&&z(google.maps.event.addListener(e,"mouseover",g)),h&&V(google.maps.event.addListener(e,"mouseup",h)),m&&Z(google.maps.event.addListener(e,"rightclick",m)),f&&H(google.maps.event.addListener(e,"click",f)),y&&G(google.maps.event.addListener(e,"drag",y)),L&&Y(google.maps.event.addListener(e,"bounds_changed",L)),x(e),E&&E(e),()=>{null!==S&&google.maps.event.removeListener(S),null!==k&&google.maps.event.removeListener(k),null!==_&&google.maps.event.removeListener(_),null!==I&&google.maps.event.removeListener(I),null!==B&&google.maps.event.removeListener(B),null!==A&&google.maps.event.removeListener(A),null!==R&&google.maps.event.removeListener(R),null!==$&&google.maps.event.removeListener($),null!==W&&google.maps.event.removeListener(W),null!==N&&google.maps.event.removeListener(N),null!==K&&google.maps.event.removeListener(K),null!==F&&google.maps.event.removeListener(F),C&&C(e),e.setMap(null)}},[]),null});let Rectangle=class Rectangle extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{rectangle:null}),_defineProperty(this,"setRectangleCallback",()=>{null!==this.state.rectangle&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)})}componentDidMount(){var e=new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ei,eventMap:er,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{rectangle:e}},this.setRectangleCallback)}componentDidUpdate(e){null!==this.state.rectangle&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ei,eventMap:er,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){null!==this.state.rectangle&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),unregisterEvents(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}};function ownKeys$6(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$6(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$6(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$6(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(Rectangle,"contextType",b);var ea={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},el={center(e,t){e.setCenter(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},radius(e,t){e.setRadius(t)},visible(e,t){e.setVisible(t)}},ep={};(0,v.memo)(function(e){var{options:t,center:s,radius:n,draggable:o,editable:r,visible:i,onDblClick:a,onDragEnd:l,onDragStart:p,onMouseDown:d,onMouseMove:c,onMouseOut:g,onMouseOver:h,onMouseUp:m,onRightClick:f,onClick:y,onDrag:L,onCenterChanged:E,onRadiusChanged:C,onLoad:P,onUnmount:w}=e,x=(0,v.useContext)(b),[S,M]=(0,v.useState)(null),[k,O]=(0,v.useState)(null),[_,j]=(0,v.useState)(null),[I,D]=(0,v.useState)(null),[B,T]=(0,v.useState)(null),[A,U]=(0,v.useState)(null),[R,z]=(0,v.useState)(null),[$,V]=(0,v.useState)(null),[W,Z]=(0,v.useState)(null),[N,H]=(0,v.useState)(null),[K,G]=(0,v.useState)(null),[F,Y]=(0,v.useState)(null),[q,J]=(0,v.useState)(null),[X,Q]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==S&&S.setMap(x)},[x]),(0,v.useEffect)(()=>{void 0!==t&&null!==S&&S.setOptions(t)},[S,t]),(0,v.useEffect)(()=>{void 0!==o&&null!==S&&S.setDraggable(o)},[S,o]),(0,v.useEffect)(()=>{void 0!==r&&null!==S&&S.setEditable(r)},[S,r]),(0,v.useEffect)(()=>{void 0!==i&&null!==S&&S.setVisible(i)},[S,i]),(0,v.useEffect)(()=>{"number"==typeof n&&null!==S&&S.setRadius(n)},[S,n]),(0,v.useEffect)(()=>{void 0!==s&&null!==S&&S.setCenter(s)},[S,s]),(0,v.useEffect)(()=>{S&&a&&(null!==k&&google.maps.event.removeListener(k),O(google.maps.event.addListener(S,"dblclick",a)))},[a]),(0,v.useEffect)(()=>{S&&l&&(null!==_&&google.maps.event.removeListener(_),j(google.maps.event.addListener(S,"dragend",l)))},[l]),(0,v.useEffect)(()=>{S&&p&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(S,"dragstart",p)))},[p]),(0,v.useEffect)(()=>{S&&d&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(S,"mousedown",d)))},[d]),(0,v.useEffect)(()=>{S&&c&&(null!==A&&google.maps.event.removeListener(A),U(google.maps.event.addListener(S,"mousemove",c)))},[c]),(0,v.useEffect)(()=>{S&&g&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(S,"mouseout",g)))},[g]),(0,v.useEffect)(()=>{S&&h&&(null!==$&&google.maps.event.removeListener($),V(google.maps.event.addListener(S,"mouseover",h)))},[h]),(0,v.useEffect)(()=>{S&&m&&(null!==W&&google.maps.event.removeListener(W),Z(google.maps.event.addListener(S,"mouseup",m)))},[m]),(0,v.useEffect)(()=>{S&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(S,"rightclick",f)))},[f]),(0,v.useEffect)(()=>{S&&y&&(null!==K&&google.maps.event.removeListener(K),G(google.maps.event.addListener(S,"click",y)))},[y]),(0,v.useEffect)(()=>{S&&L&&(null!==F&&google.maps.event.removeListener(F),Y(google.maps.event.addListener(S,"drag",L)))},[L]),(0,v.useEffect)(()=>{S&&E&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(S,"center_changed",E)))},[y]),(0,v.useEffect)(()=>{S&&C&&(null!==X&&google.maps.event.removeListener(X),Q(google.maps.event.addListener(S,"radius_changed",C)))},[C]),(0,v.useEffect)(()=>{var e=new google.maps.Circle(_objectSpread$6(_objectSpread$6({},t||ep),{},{map:x}));return"number"==typeof n&&e.setRadius(n),void 0!==s&&e.setCenter(s),"number"==typeof n&&e.setRadius(n),void 0!==i&&e.setVisible(i),void 0!==r&&e.setEditable(r),void 0!==o&&e.setDraggable(o),a&&O(google.maps.event.addListener(e,"dblclick",a)),l&&j(google.maps.event.addListener(e,"dragend",l)),p&&D(google.maps.event.addListener(e,"dragstart",p)),d&&T(google.maps.event.addListener(e,"mousedown",d)),c&&U(google.maps.event.addListener(e,"mousemove",c)),g&&z(google.maps.event.addListener(e,"mouseout",g)),h&&V(google.maps.event.addListener(e,"mouseover",h)),m&&Z(google.maps.event.addListener(e,"mouseup",m)),f&&H(google.maps.event.addListener(e,"rightclick",f)),y&&G(google.maps.event.addListener(e,"click",y)),L&&Y(google.maps.event.addListener(e,"drag",L)),E&&J(google.maps.event.addListener(e,"center_changed",E)),C&&Q(google.maps.event.addListener(e,"radius_changed",C)),M(e),P&&P(e),()=>{null!==k&&google.maps.event.removeListener(k),null!==_&&google.maps.event.removeListener(_),null!==I&&google.maps.event.removeListener(I),null!==B&&google.maps.event.removeListener(B),null!==A&&google.maps.event.removeListener(A),null!==R&&google.maps.event.removeListener(R),null!==$&&google.maps.event.removeListener($),null!==W&&google.maps.event.removeListener(W),null!==N&&google.maps.event.removeListener(N),null!==K&&google.maps.event.removeListener(K),null!==q&&google.maps.event.removeListener(q),null!==X&&google.maps.event.removeListener(X),w&&w(e),e.setMap(null)}},[]),null});let Circle=class Circle extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{circle:null}),_defineProperty(this,"setCircleCallback",()=>{null!==this.state.circle&&this.props.onLoad&&this.props.onLoad(this.state.circle)})}componentDidMount(){var e=new google.maps.Circle(_objectSpread$6(_objectSpread$6({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:el,eventMap:ea,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{circle:e}},this.setCircleCallback)}componentDidUpdate(e){null!==this.state.circle&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:el,eventMap:ea,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){if(null!==this.state.circle){var e;this.props.onUnmount&&this.props.onUnmount(this.state.circle),unregisterEvents(this.registeredEvents),null===(e=this.state.circle)||void 0===e||e.setMap(null)}}render(){return null}};function ownKeys$5(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$5(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$5(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$5(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(Circle,"contextType",b);var eu={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},ed={add(e,t){e.add(t)},addgeojson(e,t,s){e.addGeoJson(t,s)},contains(e,t){e.contains(t)},foreach(e,t){e.forEach(t)},loadgeojson(e,t,s,n){e.loadGeoJson(t,s,n)},overridestyle(e,t,s){e.overrideStyle(t,s)},remove(e,t){e.remove(t)},revertstyle(e,t){e.revertStyle(t)},controlposition(e,t){e.setControlPosition(t)},controls(e,t){e.setControls(t)},drawingmode(e,t){e.setDrawingMode(t)},map(e,t){e.setMap(t)},style(e,t){e.setStyle(t)},togeojson(e,t){e.toGeoJson(t)}};(0,v.memo)(function(e){var{options:t,onClick:s,onDblClick:n,onMouseDown:o,onMouseMove:r,onMouseOut:i,onMouseOver:a,onMouseUp:l,onRightClick:p,onAddFeature:d,onRemoveFeature:c,onRemoveProperty:g,onSetGeometry:h,onSetProperty:m,onLoad:f,onUnmount:y}=e,L=(0,v.useContext)(b),[E,C]=(0,v.useState)(null),[P,w]=(0,v.useState)(null),[x,S]=(0,v.useState)(null),[M,k]=(0,v.useState)(null),[O,_]=(0,v.useState)(null),[j,I]=(0,v.useState)(null),[D,B]=(0,v.useState)(null),[T,A]=(0,v.useState)(null),[U,R]=(0,v.useState)(null),[z,$]=(0,v.useState)(null),[V,W]=(0,v.useState)(null),[Z,N]=(0,v.useState)(null),[H,K]=(0,v.useState)(null),[G,F]=(0,v.useState)(null);return(0,v.useEffect)(()=>{null!==E&&E.setMap(L)},[L]),(0,v.useEffect)(()=>{E&&n&&(null!==P&&google.maps.event.removeListener(P),w(google.maps.event.addListener(E,"dblclick",n)))},[n]),(0,v.useEffect)(()=>{E&&o&&(null!==x&&google.maps.event.removeListener(x),S(google.maps.event.addListener(E,"mousedown",o)))},[o]),(0,v.useEffect)(()=>{E&&r&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(E,"mousemove",r)))},[r]),(0,v.useEffect)(()=>{E&&i&&(null!==O&&google.maps.event.removeListener(O),_(google.maps.event.addListener(E,"mouseout",i)))},[i]),(0,v.useEffect)(()=>{E&&a&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(E,"mouseover",a)))},[a]),(0,v.useEffect)(()=>{E&&l&&(null!==D&&google.maps.event.removeListener(D),B(google.maps.event.addListener(E,"mouseup",l)))},[l]),(0,v.useEffect)(()=>{E&&p&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(E,"rightclick",p)))},[p]),(0,v.useEffect)(()=>{E&&s&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(E,"click",s)))},[s]),(0,v.useEffect)(()=>{E&&d&&(null!==z&&google.maps.event.removeListener(z),$(google.maps.event.addListener(E,"addfeature",d)))},[d]),(0,v.useEffect)(()=>{E&&c&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(E,"removefeature",c)))},[c]),(0,v.useEffect)(()=>{E&&g&&(null!==Z&&google.maps.event.removeListener(Z),N(google.maps.event.addListener(E,"removeproperty",g)))},[g]),(0,v.useEffect)(()=>{E&&h&&(null!==H&&google.maps.event.removeListener(H),K(google.maps.event.addListener(E,"setgeometry",h)))},[h]),(0,v.useEffect)(()=>{E&&m&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(E,"setproperty",m)))},[m]),(0,v.useEffect)(()=>{if(null!==L){var e=new google.maps.Data(_objectSpread$5(_objectSpread$5({},t),{},{map:L}));n&&w(google.maps.event.addListener(e,"dblclick",n)),o&&S(google.maps.event.addListener(e,"mousedown",o)),r&&k(google.maps.event.addListener(e,"mousemove",r)),i&&_(google.maps.event.addListener(e,"mouseout",i)),a&&I(google.maps.event.addListener(e,"mouseover",a)),l&&B(google.maps.event.addListener(e,"mouseup",l)),p&&A(google.maps.event.addListener(e,"rightclick",p)),s&&R(google.maps.event.addListener(e,"click",s)),d&&$(google.maps.event.addListener(e,"addfeature",d)),c&&W(google.maps.event.addListener(e,"removefeature",c)),g&&N(google.maps.event.addListener(e,"removeproperty",g)),h&&K(google.maps.event.addListener(e,"setgeometry",h)),m&&F(google.maps.event.addListener(e,"setproperty",m)),C(e),f&&f(e)}return()=>{E&&(null!==P&&google.maps.event.removeListener(P),null!==x&&google.maps.event.removeListener(x),null!==M&&google.maps.event.removeListener(M),null!==O&&google.maps.event.removeListener(O),null!==j&&google.maps.event.removeListener(j),null!==D&&google.maps.event.removeListener(D),null!==T&&google.maps.event.removeListener(T),null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==V&&google.maps.event.removeListener(V),null!==Z&&google.maps.event.removeListener(Z),null!==H&&google.maps.event.removeListener(H),null!==G&&google.maps.event.removeListener(G),y&&y(E),E.setMap(null))}},[]),null});let Data=class Data extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{data:null}),_defineProperty(this,"setDataCallback",()=>{null!==this.state.data&&this.props.onLoad&&this.props.onLoad(this.state.data)})}componentDidMount(){if(null!==this.context){var e=new google.maps.Data(_objectSpread$5(_objectSpread$5({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ed,eventMap:eu,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({data:e}),this.setDataCallback)}}componentDidUpdate(e){null!==this.state.data&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ed,eventMap:eu,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){null!==this.state.data&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),unregisterEvents(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}};function ownKeys$4(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$4(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$4(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$4(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(Data,"contextType",b);var ec={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},eg={options(e,t){e.setOptions(t)},url(e,t){e.setUrl(t)},zIndex(e,t){e.setZIndex(t)}};let KmlLayer=class KmlLayer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{kmlLayer:null}),_defineProperty(this,"setKmlLayerCallback",()=>{null!==this.state.kmlLayer&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)})}componentDidMount(){var e=new google.maps.KmlLayer(_objectSpread$4(_objectSpread$4({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eg,eventMap:ec,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{kmlLayer:e}},this.setKmlLayerCallback)}componentDidUpdate(e){null!==this.state.kmlLayer&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eg,eventMap:ec,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){null!==this.state.kmlLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),unregisterEvents(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}};function getOffsetOverride(e,t){return"function"==typeof t?t(e.offsetWidth,e.offsetHeight):{x:0,y:0}}function getLayoutStyles(e,t,s,n){var o,r,i,a,l,p,d;return void 0!==s?(r=s instanceof(o=google.maps.LatLngBounds)?s:(p=s,new o(new google.maps.LatLng(p.ne.lat,p.ne.lng),new google.maps.LatLng(p.sw.lat,p.sw.lng))),i=e&&e.fromLatLngToDivPixel(r.getNorthEast()),a=e&&e.fromLatLngToDivPixel(r.getSouthWest()),i&&a?{left:"".concat(a.x+t.x,"px"),top:"".concat(i.y+t.y,"px"),width:"".concat(i.x-a.x-t.x,"px"),height:"".concat(a.y-i.y-t.y,"px")}:{left:"-9999px",top:"-9999px"}):function(e,t,s){var n=e&&e.fromLatLngToDivPixel(s);if(n){var{x:o,y:r}=n;return{left:"".concat(o+t.x,"px"),top:"".concat(r+t.y,"px")}}return{left:"-9999px",top:"-9999px"}}(e,t,n instanceof(l=google.maps.LatLng)?n:(d=n,new l(d.lat,d.lng)))}function ownKeys$3(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function ownKeys$2(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function convertToLatLngString(e){return e?(e instanceof google.maps.LatLng?e:new google.maps.LatLng(e.lat,e.lng))+"":""}function convertToLatLngBoundsString(e){return e?(e instanceof google.maps.LatLngBounds?e:new google.maps.LatLngBounds(new google.maps.LatLng(e.south,e.east),new google.maps.LatLng(e.north,e.west)))+"":""}_defineProperty(KmlLayer,"contextType",b),(0,v.memo)(function(e){var{position:t,bounds:s,mapPaneName:n,zIndex:o,onLoad:r,onUnmount:i,getPixelPositionOffset:a,children:l}=e,p=(0,v.useContext)(b),d=(0,v.useMemo)(()=>{var e=document.createElement("div");return e.style.position="absolute",e},[]),c=(0,v.useMemo)(()=>(function(e,t,s,n,o){let Overlay=class Overlay extends google.maps.OverlayView{constructor(e,t,s,n){super(),this.container=e,this.pane=t,this.position=s,this.bounds=n}onAdd(){var e,t=null===(e=this.getPanes())||void 0===e?void 0:e[this.pane];null==t||t.appendChild(this.container)}draw(){for(var[e,t]of Object.entries(getLayoutStyles(this.getProjection(),function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$3(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$3(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({},this.container?getOffsetOverride(this.container,o):{x:0,y:0}),this.bounds,this.position)))this.container.style[e]=t}onRemove(){null!==this.container.parentNode&&this.container.parentNode.removeChild(this.container)}};return new Overlay(e,t,s,n)})(d,n,t,s,a),[d,n,t,s]);return(0,v.useEffect)(()=>(null==r||r(c),null==c||c.setMap(p),()=>{null==i||i(c),null==c||c.setMap(null)}),[p,c]),(0,v.useEffect)(()=>{d.style.zIndex="".concat(o)},[o,d]),f.createPortal(l,d)});let OverlayView=class OverlayView extends v.PureComponent{constructor(e){super(e),_defineProperty(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),_defineProperty(this,"updatePane",()=>{var e=this.props.mapPaneName,t=this.overlayView.getPanes();y(!!e,"OverlayView requires props.mapPaneName but got %s",e),t?this.setState({paneEl:t[e]}):this.setState({paneEl:null})}),_defineProperty(this,"onAdd",()=>{var e,t;this.updatePane(),null===(e=(t=this.props).onLoad)||void 0===e||e.call(t,this.overlayView)}),_defineProperty(this,"onPositionElement",()=>{var e,t,s,n,o,r=getLayoutStyles(this.overlayView.getProjection(),function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$2(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$2(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}({x:0,y:0},this.containerRef.current?getOffsetOverride(this.containerRef.current,this.props.getPixelPositionOffset):{}),this.props.bounds,this.props.position);o={left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height},(r.left!==o.left||r.top!==o.top||r.width!==o.height||r.height!==o.height)&&this.setState({containerStyle:{top:null!==(e=r.top)&&void 0!==e?e:0,left:null!==(t=r.left)&&void 0!==t?t:0,width:null!==(s=r.width)&&void 0!==s?s:0,height:null!==(n=r.height)&&void 0!==n?n:0,position:"absolute"}})}),_defineProperty(this,"draw",()=>{this.onPositionElement()}),_defineProperty(this,"onRemove",()=>{var e,t;this.setState(()=>({paneEl:null})),null===(e=(t=this.props).onUnmount)||void 0===e||e.call(t,this.overlayView)}),this.containerRef=(0,v.createRef)();var t=new google.maps.OverlayView;t.onAdd=this.onAdd,t.draw=this.draw,t.onRemove=this.onRemove,this.overlayView=t}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var t=convertToLatLngString(e.position),s=convertToLatLngString(this.props.position),n=convertToLatLngBoundsString(e.bounds),o=convertToLatLngBoundsString(this.props.bounds);(t!==s||n!==o)&&this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var e=this.state.paneEl;return e?f.createPortal((0,m.jsx)("div",{ref:this.containerRef,style:this.state.containerStyle,children:v.Children.only(this.props.children)}),e):null}};function ownKeys$1(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread$1(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys$1(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys$1(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(OverlayView,"FLOAT_PANE","floatPane"),_defineProperty(OverlayView,"MAP_PANE","mapPane"),_defineProperty(OverlayView,"MARKER_LAYER","markerLayer"),_defineProperty(OverlayView,"OVERLAY_LAYER","overlayLayer"),_defineProperty(OverlayView,"OVERLAY_MOUSE_TARGET","overlayMouseTarget"),_defineProperty(OverlayView,"contextType",b);var eh={onDblClick:"dblclick",onClick:"click"},em={opacity(e,t){e.setOpacity(t)}};(0,v.memo)(function(e){var{url:t,bounds:s,options:n,visible:o}=e,r=(0,v.useContext)(b),i=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east)),a=(0,v.useMemo)(()=>new google.maps.GroundOverlay(t,i,n),[]);return(0,v.useEffect)(()=>{null!==a&&a.setMap(r)},[r]),(0,v.useEffect)(()=>{void 0!==t&&null!==a&&(a.set("url",t),a.setMap(r))},[a,t]),(0,v.useEffect)(()=>{void 0!==o&&null!==a&&a.setOpacity(o?1:0)},[a,o]),(0,v.useEffect)(()=>{var e=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east));void 0!==s&&null!==a&&(a.set("bounds",e),a.setMap(r))},[a,s]),null});let GroundOverlay=class GroundOverlay extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{groundOverlay:null}),_defineProperty(this,"setGroundOverlayCallback",()=>{null!==this.state.groundOverlay&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)})}componentDidMount(){y(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,_objectSpread$1(_objectSpread$1({},this.props.options),{},{map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:em,eventMap:eh,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{groundOverlay:e}},this.setGroundOverlayCallback)}componentDidUpdate(e){null!==this.state.groundOverlay&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:em,eventMap:eh,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}};function ownKeys(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),s.push.apply(s,n)}return s}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(s),!0).forEach(function(t){_defineProperty(e,t,s[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(s,t))})}return e}_defineProperty(GroundOverlay,"defaultProps",{onLoad:function(){}}),_defineProperty(GroundOverlay,"contextType",b);var ev={},ef={data(e,t){e.setData(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)}};(0,v.memo)(function(e){var{data:t,onLoad:s,onUnmount:n,options:o}=e,r=(0,v.useContext)(b),[i,a]=(0,v.useState)(null);return(0,v.useEffect)(()=>{google.maps.visualization||y(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)},[]),(0,v.useEffect)(()=>{y(!!t,"data property is required in HeatmapLayer %s",t)},[t]),(0,v.useEffect)(()=>{null!==i&&i.setMap(r)},[r]),(0,v.useEffect)(()=>{o&&null!==i&&i.setOptions(o)},[i,o]),(0,v.useEffect)(()=>{var e=new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({},o),{},{data:t,map:r}));return a(e),s&&s(e),()=>{null!==i&&(n&&n(i),i.setMap(null))}},[]),null});let HeatmapLayer=class HeatmapLayer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{heatmapLayer:null}),_defineProperty(this,"setHeatmapLayerCallback",()=>{null!==this.state.heatmapLayer&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)})}componentDidMount(){y(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),y(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ef,eventMap:ev,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{heatmapLayer:e}},this.setHeatmapLayerCallback)}componentDidUpdate(e){unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ef,eventMap:ev,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){null!==this.state.heatmapLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),unregisterEvents(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}};_defineProperty(HeatmapLayer,"contextType",b);var ey={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},eb={register(e,t,s){e.registerPanoProvider(t,s)},links(e,t){e.setLinks(t)},motionTracking(e,t){e.setMotionTracking(t)},options(e,t){e.setOptions(t)},pano(e,t){e.setPano(t)},position(e,t){e.setPosition(t)},pov(e,t){e.setPov(t)},visible(e,t){e.setVisible(t)},zoom(e,t){e.setZoom(t)}};let StreetViewPanorama=class StreetViewPanorama extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{streetViewPanorama:null}),_defineProperty(this,"setStreetViewPanoramaCallback",()=>{null!==this.state.streetViewPanorama&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)})}componentDidMount(){var e,t,s=null!==(e=null===(t=this.context)||void 0===t?void 0:t.getStreetView())&&void 0!==e?e:null;this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eb,eventMap:ey,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({streetViewPanorama:s}),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){null!==this.state.streetViewPanorama&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eb,eventMap:ey,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){null!==this.state.streetViewPanorama&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),unregisterEvents(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}};_defineProperty(StreetViewPanorama,"contextType",b);let StreetViewService=class StreetViewService extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"state",{streetViewService:null}),_defineProperty(this,"setStreetViewServiceCallback",()=>{null!==this.state.streetViewService&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)})}componentDidMount(){var e=new google.maps.StreetViewService;this.setState(function(){return{streetViewService:e}},this.setStreetViewServiceCallback)}componentWillUnmount(){null!==this.state.streetViewService&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}};_defineProperty(StreetViewService,"contextType",b);var eL={onDirectionsChanged:"directions_changed"},eE={directions(e,t){e.setDirections(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},panel(e,t){e.setPanel(t)},routeIndex(e,t){e.setRouteIndex(t)}};let DirectionsRenderer=class DirectionsRenderer extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"state",{directionsRenderer:null}),_defineProperty(this,"setDirectionsRendererCallback",()=>{null!==this.state.directionsRenderer&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))})}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eE,eventMap:eL,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{directionsRenderer:e}},this.setDirectionsRendererCallback)}componentDidUpdate(e){null!==this.state.directionsRenderer&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eE,eventMap:eL,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){null!==this.state.directionsRenderer&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),unregisterEvents(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}};_defineProperty(DirectionsRenderer,"contextType",b);var eC={onPlacesChanged:"places_changed"},eP={bounds(e,t){e.setBounds(t)}};let StandaloneSearchBox=class StandaloneSearchBox extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"containerElement",(0,v.createRef)()),_defineProperty(this,"state",{searchBox:null}),_defineProperty(this,"setSearchBoxCallback",()=>{null!==this.state.searchBox&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)})}componentDidMount(){if(y(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),null!==this.containerElement&&null!==this.containerElement.current){var e=this.containerElement.current.querySelector("input");if(null!==e){var t=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eP,eventMap:eC,prevProps:{},nextProps:this.props,instance:t}),this.setState(function(){return{searchBox:t}},this.setSearchBoxCallback)}}}componentDidUpdate(e){null!==this.state.searchBox&&(unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:eP,eventMap:eC,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){null!==this.state.searchBox&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),unregisterEvents(this.registeredEvents))}render(){return(0,m.jsx)("div",{ref:this.containerElement,children:v.Children.only(this.props.children)})}};_defineProperty(StandaloneSearchBox,"contextType",b);var ew={onPlaceChanged:"place_changed"},ex={bounds(e,t){e.setBounds(t)},restrictions(e,t){e.setComponentRestrictions(t)},fields(e,t){e.setFields(t)},options(e,t){e.setOptions(t)},types(e,t){e.setTypes(t)}};let Autocomplete=class Autocomplete extends v.PureComponent{constructor(){super(...arguments),_defineProperty(this,"registeredEvents",[]),_defineProperty(this,"containerElement",(0,v.createRef)()),_defineProperty(this,"state",{autocomplete:null}),_defineProperty(this,"setAutocompleteCallback",()=>{null!==this.state.autocomplete&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)})}componentDidMount(){y(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var e,t=null===(e=this.containerElement.current)||void 0===e?void 0:e.querySelector("input");if(t){var s=new google.maps.places.Autocomplete(t,this.props.options);this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ex,eventMap:ew,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({autocomplete:s}),this.setAutocompleteCallback)}}componentDidUpdate(e){unregisterEvents(this.registeredEvents),this.registeredEvents=applyUpdatersToPropsAndRegisterEvents({updaterMap:ex,eventMap:ew,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){null!==this.state.autocomplete&&unregisterEvents(this.registeredEvents)}render(){return(0,m.jsx)("div",{ref:this.containerElement,className:this.props.className,children:v.Children.only(this.props.children)})}};_defineProperty(Autocomplete,"defaultProps",{className:""}),_defineProperty(Autocomplete,"contextType",b)}}]);