"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2223],{22223:function(e,t,u){u.r(t);var n=u(85893),r=u(71421),s=u(75814),a=u(89664);t.default=()=>{let{mutate:e,isLoading:t}=(0,a.Oi)(),{data:u}=(0,s.X9)(),{closeModal:i}=(0,s.SO)();async function handleDelete(){e({id:u}),i()}return(0,n.jsx)(r.Z,{onCancel:i,onDelete:handleDelete,deleteBtnLoading:t})}},89664:function(e,t,u){u.d(t,{I1:function(){return useAttributeQuery},OO:function(){return useAttributesQuery},gL:function(){return useCreateAttributeMutation},Oi:function(){return useDeleteAttributeMutation},uT:function(){return useUpdateAttributeMutation}});var n=u(11163),r=u.n(n),s=u(88767),a=u(22920),i=u(5233),l=u(97514),c=u(47869),o=u(55191),d=u(3737);let T={...(0,o.h)(c.P.ATTRIBUTES),paginated:e=>{let{type:t,name:u,shop_id:n,...r}=e;return d.eN.get(c.P.ATTRIBUTES,{searchJoin:"and",...r,search:d.eN.formatSearchParams({type:t,name:u,shop_id:n})})},all:e=>{let{type:t,name:u,shop_id:n,...r}=e;return d.eN.get(c.P.ATTRIBUTES,{searchJoin:"and",...r,search:d.eN.formatSearchParams({type:t,name:u,shop_id:n})})}};var A=u(93345);let useCreateAttributeMutation=()=>{let e=(0,n.useRouter)(),t=(0,s.useQueryClient)(),{t:u}=(0,i.$G)();return(0,s.useMutation)(T.create,{onSuccess:()=>{let t=e.query.shop?"/".concat(e.query.shop).concat(l.Z.attribute.list):l.Z.attribute.list;r().push(t,void 0,{locale:A.Config.defaultLanguage}),a.Am.success(u("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(c.P.ATTRIBUTES)}})},useUpdateAttributeMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(T.update,{onSuccess:()=>{a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ATTRIBUTES)}})},useDeleteAttributeMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,i.$G)();return(0,s.useMutation)(T.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.ATTRIBUTES)}})},useAttributeQuery=e=>{let{slug:t,language:u}=e;return(0,s.useQuery)([c.P.ATTRIBUTES,{slug:t,language:u}],()=>T.get({slug:t,language:u}))},useAttributesQuery=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:u,error:n,isLoading:r}=(0,s.useQuery)([c.P.ATTRIBUTES,e],e=>{let{queryKey:t,pageParam:u}=e;return T.all(Object.assign({},t[1],u))},{keepPreviousData:!0,...t});return{attributes:null!=u?u:[],error:n,loading:r}}}}]);