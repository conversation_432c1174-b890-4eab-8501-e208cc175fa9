"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6312],{35484:function(e,t,n){var o=n(85893),l=n(93967),r=n.n(l),a=n(98388);t.Z=e=>{let{title:t,className:n,...l}=e;return(0,o.jsx)("h2",{className:(0,a.m6)(r()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...l,children:t})}},37912:function(e,t,n){var o=n(85893),l=n(5114),r=n(80287),a=n(93967),s=n.n(a),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:v,...m}=e,{register:b,handleSubmit:h,watch:y,reset:g,formState:{errors:P}}=(0,i.cI)({defaultValues:{searchText:""}}),x=y("searchText"),{t:S}=(0,c.$G)();(0,u.useEffect)(()=>{x||n({searchText:""})},[x]);let E=s()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,o.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(s()("relative flex w-full items-center",t)),onSubmit:h(n),children:[(0,o.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,o.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,o.jsx)(r.W,{className:"h-5 w-5"})}),(0,o.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(E),placeholder:null!=v?v:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...m}),P.searchText&&(0,o.jsx)("p",{children:P.searchText.message}),!!x&&(0,o.jsx)("button",{type:"button",onClick:function(){g(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,o.jsx)(l.T,{className:"h-5 w-5"})})]})}},59134:function(e,t,n){var o=n(85893),l=n(804),r=n(77556),a=n(34927),s=n(18230),u=n(27899),i=n(78998),c=n(97514),d=n(10265),p=n(76518),f=n(27484),v=n.n(f),m=n(84110),b=n.n(m),h=n(29387),y=n.n(h),g=n(70178),P=n.n(g),x=n(5233),S=n(11163),E=n(67294);v().extend(b()),v().extend(P()),v().extend(y()),t.Z=e=>{let{faqs:t,paginatorInfo:n,onPagination:f,onSort:v,onOrder:m}=e,{t:b}=(0,x.$G)(),h=(0,S.useRouter)(),{query:{shop:y}}=h,{alignLeft:g,alignRight:P}=(0,p.S)(),[A,I]=(0,E.useState)({sort:null===d.As||void 0===d.As?void 0:d.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{v(e=>e===(null===d.As||void 0===d.As?void 0:d.As.Desc)?null===d.As||void 0===d.As?void 0:d.As.Asc:null===d.As||void 0===d.As?void 0:d.As.Desc),m(e),I({sort:(null==A?void 0:A.sort)===(null===d.As||void 0===d.As?void 0:d.As.Desc)?null===d.As||void 0===d.As?void 0:d.As.Asc:null===d.As||void 0===d.As?void 0:d.As.Desc,column:e})}}),N=[{title:(0,o.jsx)(i.Z,{title:b("table:table-item-id"),ascending:A.sort===d.As.Asc&&"id"===A.column,isActive:"id"===A.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:g,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(b("table:table-item-id"),": ").concat(e)},{title:(0,o.jsx)(i.Z,{title:b("table:table-item-title-title"),ascending:(null==A?void 0:A.sort)===(null===d.As||void 0===d.As?void 0:d.As.Asc)&&(null==A?void 0:A.column)==="faq_title",isActive:(null==A?void 0:A.column)==="faq_title"}),className:"cursor-pointer",dataIndex:"faq_title",key:"faq_title",align:g,ellipsis:!0,width:200,onHeaderCell:()=>onHeaderClick("faq_title"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(i.Z,{title:b("table:table-item-description"),ascending:(null==A?void 0:A.sort)===(null===d.As||void 0===d.As?void 0:d.As.Asc)&&(null==A?void 0:A.column)==="faq_description",isActive:(null==A?void 0:A.column)==="faq_description"}),className:"cursor-pointer",dataIndex:"faq_description",key:"faq_description",align:g,width:300,ellipsis:!0,onHeaderCell:()=>onHeaderClick("faq_description"),render:e=>(0,o.jsx)("span",{dangerouslySetInnerHTML:{__html:(null==e?void 0:e.length)<140?e:(null==e?void 0:e.substring(0,140))+"..."}})},{title:(0,o.jsx)(i.Z,{title:b("table:table-item-type"),ascending:(null==A?void 0:A.sort)===(null===d.As||void 0===d.As?void 0:d.As.Asc)&&(null==A?void 0:A.column)==="faq_type",isActive:(null==A?void 0:A.column)==="faq_type"}),className:"cursor-pointer",dataIndex:"faq_type",key:"faq_type",align:"center",width:100,onHeaderCell:()=>onHeaderClick("faq_type"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(i.Z,{title:b("table:table-item-issued-by"),ascending:(null==A?void 0:A.sort)===(null===d.As||void 0===d.As?void 0:d.As.Asc)&&(null==A?void 0:A.column)==="issued_by",isActive:(null==A?void 0:A.column)==="issued_by"}),className:"cursor-pointer",dataIndex:"issued_by",key:"issued_by",align:"center",width:100,onHeaderCell:()=>onHeaderClick("issued_by"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:b("table:table-item-actions"),key:"actions",align:P,width:150,render:e=>(null==h?void 0:h.asPath)!=="/"?(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(a.Z,{slug:null==e?void 0:e.id,record:e,deleteModalView:"DELETE_FAQ",routes:null===c.Z||void 0===c.Z?void 0:c.Z.faqs,isShop:!!y,shopSlug:null!=y?y:""})}):(0,o.jsx)(l.Z,{id:null==e?void 0:e.id,detailsUrl:"".concat(h.asPath,"/").concat(null==e?void 0:e.id),customLocale:null==h?void 0:h.locale})}];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,o.jsx)(u.i,{columns:N,emptyText:()=>(0,o.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,o.jsx)(r.m,{className:"w-52"}),(0,o.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:b("table:empty-table-data")}),(0,o.jsx)("p",{className:"text-[13px]",children:b("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==n?void 0:n.total)&&(0,o.jsx)("div",{className:"flex items-center justify-end",children:(0,o.jsx)(s.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:f})})]})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var o=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,o.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,o.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var o=n(85893),l=n(55891),r=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,o.jsx)(l.Z,{nextIcon:(0,o.jsx)(r.T,{}),prevIcon:(0,o.jsx)(ArrowPrev,{}),...e})},92001:function(e,t,n){n.d(t,{Mf:function(){return useCreateFaqsMutation},V7:function(){return useDeleteFaqsMutation},cb:function(){return useFaqQuery},uo:function(){return useFaqsQuery},Zg:function(){return useUpdateFaqsMutation}});var o=n(11163),l=n.n(o),r=n(88767),a=n(22920),s=n(5233),u=n(28597),i=n(97514),c=n(47869),d=n(93345),p=n(55191),f=n(3737);let v={...(0,p.h)(c.P.FAQS),all:function(){let{faq_title:e,shop_id:t,...n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return f.eN.get(c.P.FAQS,{searchJoin:"and",shop_id:t,...n,search:f.eN.formatSearchParams({faq_title:e,shop_id:t})})},get(e){let{id:t,language:n}=e;return f.eN.get("".concat(c.P.FAQS,"/").concat(t),{language:n})},paginated:e=>{let{faq_title:t,shop_id:n,...o}=e;return f.eN.get(c.P.FAQS,{searchJoin:"and",shop_id:n,...o,search:f.eN.formatSearchParams({faq_title:t,shop_id:n})})}},useFaqQuery=e=>{let{id:t,language:n}=e,{data:o,error:l,isLoading:a}=(0,r.useQuery)([c.P.FAQS,{id:t,language:n}],()=>v.get({id:t,language:n}));return{faqs:o,error:l,loading:a}},useFaqsQuery=e=>{var t;let{data:n,error:o,isLoading:l}=(0,r.useQuery)([c.P.FAQS,e],e=>{let{queryKey:t,pageParam:n}=e;return v.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{faqs:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(n),error:o,loading:l}},useCreateFaqsMutation=()=>{let e=(0,r.useQueryClient)(),t=(0,o.useRouter)(),{t:n}=(0,s.$G)();return(0,r.useMutation)(v.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.faqs.list):i.Z.faqs.list;await l().push(e,void 0,{locale:d.Config.defaultLanguage}),a.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.FAQS)},onError:e=>{var t;a.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFaqsMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,r.useQueryClient)(),n=(0,o.useRouter)();return(0,r.useMutation)(v.update,{onSuccess:async t=>{let o=n.query.shop?"/".concat(n.query.shop).concat(i.Z.faqs.list):i.Z.faqs.list;await n.push(o,void 0,{locale:d.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FAQS)},onError:t=>{var n;a.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteFaqsMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,s.$G)();return(0,r.useMutation)(v.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.FAQS)},onError:e=>{var n;a.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},28368:function(e,t,n){n.d(t,{p:function(){return N}});var o,l,r,a=n(67294),s=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),v=n(14157),m=n(15466),b=n(73781);let h=null!=(r=a.startTransition)?r:function(e){e()};var y=((o=y||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),g=((l=g||{})[l.ToggleDisclosure=0]="ToggleDisclosure",l[l.CloseDisclosure=1]="CloseDisclosure",l[l.SetButtonId=2]="SetButtonId",l[l.SetPanelId=3]="SetPanelId",l[l.LinkPanel=4]="LinkPanel",l[l.UnlinkPanel=5]="UnlinkPanel",l);let P={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},x=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(x);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}x.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let E=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,P,e,t)}E.displayName="DisclosurePanelContext";let A=a.Fragment,I=u.AN.RenderStrategy|u.AN.Static,N=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...o}=e,l=(0,a.useRef)(null),r=(0,i.T)(t,(0,i.h)(e=>{l.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:v,buttonId:h},y]=p,g=(0,b.z)(e=>{y({type:1});let t=(0,m.r)(l);if(!t||!h)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(h):t.getElementById(h);null==n||n.focus()}),P=(0,a.useMemo)(()=>({close:g}),[g]),E=(0,a.useMemo)(()=>({open:0===v,close:g}),[v,g]);return a.createElement(x.Provider,{value:p},a.createElement(S.Provider,{value:P},a.createElement(f.up,{value:(0,s.E)(v,{0:f.ZM.Open,1:f.ZM.Closed})},(0,u.sY)({ourProps:{ref:r},theirProps:o,slot:E,defaultTag:A,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-button-${n}`,...l}=e,[r,s]=M("Disclosure.Button"),f=(0,a.useContext)(E),m=null!==f&&f===r.panelId,h=(0,a.useRef)(null),y=(0,i.T)(h,t,m?null:r.buttonRef);(0,a.useEffect)(()=>{if(!m)return s({type:2,buttonId:o}),()=>{s({type:2,buttonId:null})}},[o,s,m]);let g=(0,b.z)(e=>{var t;if(m){if(1===r.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=r.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),P=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),x=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(m?(s({type:0}),null==(n=r.buttonRef.current)||n.focus()):s({type:0}))}),S=(0,a.useMemo)(()=>({open:0===r.disclosureState}),[r]),A=(0,v.f)(e,h),I=m?{ref:y,type:A,onKeyDown:g,onClick:x}:{ref:y,id:o,type:A,"aria-expanded":0===r.disclosureState,"aria-controls":r.linkedPanel?r.panelId:void 0,onKeyDown:g,onKeyUp:P,onClick:x};return(0,u.sY)({ourProps:I,theirProps:l,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-panel-${n}`,...l}=e,[r,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,r.panelRef,e=>{h(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:o}),()=>{s({type:3,panelId:null})}),[o,s]);let v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===r.disclosureState,b=(0,a.useMemo)(()=>({open:0===r.disclosureState,close:d}),[r,d]);return a.createElement(E.Provider,{value:r.panelId},(0,u.sY)({ourProps:{ref:p,id:o},theirProps:l,slot:b,defaultTag:"div",features:I,visible:m,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){n.d(t,{J:function(){return O}});var o,l,r=n(67294),a=n(32984),s=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),v=n(14157),m=n(39650),b=n(15466),h=n(51074),y=n(14007),g=n(46045),P=n(73781),x=n(45662),S=n(3855),E=n(16723),A=n(65958),I=n(2740),N=((o=N||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),T=((l=T||{})[l.TogglePopover=0]="TogglePopover",l[l.ClosePopover=1]="ClosePopover",l[l.SetButton=2]="SetButton",l[l.SetButtonId=3]="SetButtonId",l[l.SetPanel=4]="SetPanel",l[l.SetPanelId=5]="SetPanelId",l);let C={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},k=(0,r.createContext)(null);function oe(e){let t=(0,r.useContext)(k);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}k.displayName="PopoverContext";let j=(0,r.createContext)(null);function fe(e){let t=(0,r.useContext)(j);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}j.displayName="PopoverAPIContext";let F=(0,r.createContext)(null);function Ee(){return(0,r.useContext)(F)}F.displayName="PopoverGroupContext";let D=(0,r.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,C,e,t)}D.displayName="PopoverPanelContext";let R=s.AN.RenderStrategy|s.AN.Static,_=s.AN.RenderStrategy|s.AN.Static,O=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:o=!1,...l}=e,i=(0,r.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,r.useRef)([]),v=(0,r.useReducer)(Ne,{__demoMode:o,popoverState:o?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,r.createRef)(),afterPanelSentinel:(0,r.createRef)()}),[{popoverState:b,button:g,buttonId:x,panel:E,panelId:N,beforePanelSentinel:T,afterPanelSentinel:C},F]=v,R=(0,h.i)(null!=(n=i.current)?n:g),_=(0,r.useMemo)(()=>{if(!g||!E)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(g))^Number(null==e?void 0:e.contains(E)))return!0;let e=(0,p.GO)(),t=e.indexOf(g),n=(t+e.length-1)%e.length,o=(t+1)%e.length,l=e[n],r=e[o];return!E.contains(l)&&!E.contains(r)},[g,E]),O=(0,S.E)(x),q=(0,S.E)(N),Z=(0,r.useMemo)(()=>({buttonId:O,panelId:q,close:()=>F({type:1})}),[O,q,F]),B=Ee(),z=null==B?void 0:B.registerPopover,Q=(0,P.z)(()=>{var e;return null!=(e=null==B?void 0:B.isFocusWithinPopoverGroup())?e:(null==R?void 0:R.activeElement)&&((null==g?void 0:g.contains(R.activeElement))||(null==E?void 0:E.contains(R.activeElement)))});(0,r.useEffect)(()=>null==z?void 0:z(Z),[z,Z]);let[L,H]=(0,I.k)(),$=(0,A.v)({mainTreeNodeRef:null==B?void 0:B.mainTreeNodeRef,portals:L,defaultContainers:[g,E]});(0,y.O)(null==R?void 0:R.defaultView,"focus",e=>{var t,n,o,l;e.target!==window&&e.target instanceof HTMLElement&&0===b&&(Q()||g&&E&&($.contains(e.target)||null!=(n=null==(t=T.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(l=null==(o=C.current)?void 0:o.contains)&&l.call(o,e.target)||F({type:1})))},!0),(0,m.O)($.resolveContainers,(e,t)=>{F({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==g||g.focus())},0===b);let G=(0,P.z)(e=>{F({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:g:g;null==t||t.focus()}),V=(0,r.useMemo)(()=>({close:G,isPortalled:_}),[G,_]),K=(0,r.useMemo)(()=>({open:0===b,close:G}),[b,G]);return r.createElement(D.Provider,{value:null},r.createElement(k.Provider,{value:v},r.createElement(j.Provider,{value:V},r.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},r.createElement(H,null,(0,s.sY)({ourProps:{ref:c},theirProps:l,slot:K,defaultTag:"div",name:"Popover"}),r.createElement($.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-button-${n}`,...l}=e,[f,m]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),y=(0,r.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,E=Ee(),A=null==E?void 0:E.closeOthers,I=null!==(0,r.useContext)(D);(0,r.useEffect)(()=>{if(!I)return m({type:3,buttonId:o}),()=>{m({type:3,buttonId:null})}},[I,o,m]);let[N]=(0,r.useState)(()=>Symbol()),T=(0,u.T)(y,t,I?null:e=>{if(e)f.buttons.current.push(N);else{let e=f.buttons.current.indexOf(N);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&m({type:2,button:e})}),C=(0,u.T)(y,t),k=(0,h.i)(y),j=(0,P.z)(e=>{var t,n,o;if(I){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),m({type:1}),null==(o=f.button)||o.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==A||A(f.buttonId)),m({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==A?void 0:A(f.buttonId);if(!y.current||null!=k&&k.activeElement&&!y.current.contains(k.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1})}}),F=(0,P.z)(e=>{I||e.key===c.R.Space&&e.preventDefault()}),R=(0,P.z)(t=>{var n,o;(0,d.P)(t.currentTarget)||e.disabled||(I?(m({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==A||A(f.buttonId)),m({type:0}),null==(o=f.button)||o.focus()))}),_=(0,P.z)(e=>{e.preventDefault(),e.stopPropagation()}),O=0===f.popoverState,q=(0,r.useMemo)(()=>({open:O}),[O]),Z=(0,v.f)(e,y),B=I?{ref:C,type:Z,onKeyDown:j,onClick:R}:{ref:T,id:f.buttonId,type:Z,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:j,onKeyUp:F,onClick:R,onMouseDown:_},z=(0,x.l)(),Q=(0,P.z)(()=>{let e=f.panel;e&&(0,a.E)(z.current,{[x.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[x.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(z.current,{[x.N.Forwards]:p.TO.Next,[x.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return r.createElement(r.Fragment,null,(0,s.sY)({ourProps:B,theirProps:l,slot:q,defaultTag:"button",name:"Popover.Button"}),O&&!I&&b&&r.createElement(g._,{id:S,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:Q}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-overlay-${n}`,...l}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,u.T)(t),v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===a,b=(0,P.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),h=(0,r.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:p,id:o,"aria-hidden":!0,onClick:b},theirProps:l,slot:h,defaultTag:"div",features:R,visible:m,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-panel-${n}`,focus:l=!1,...d}=e,[v,m]=oe("Popover.Panel"),{close:b,isPortalled:y}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,A=`headlessui-focus-sentinel-after-${(0,i.M)()}`,I=(0,r.useRef)(null),N=(0,u.T)(I,t,e=>{m({type:4,panel:e})}),T=(0,h.i)(I);(0,E.e)(()=>(m({type:5,panelId:o}),()=>{m({type:5,panelId:null})}),[o,m]);let C=(0,f.oJ)(),k=null!==C?(C&f.ZM.Open)===f.ZM.Open:0===v.popoverState,j=(0,P.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==v.popoverState||!I.current||null!=T&&T.activeElement&&!I.current.contains(T.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1}),null==(t=v.button)||t.focus()}});(0,r.useEffect)(()=>{var t;e.static||1===v.popoverState&&(null==(t=e.unmount)||t)&&m({type:4,panel:null})},[v.popoverState,e.unmount,e.static,m]),(0,r.useEffect)(()=>{if(v.__demoMode||!l||0!==v.popoverState||!I.current)return;let e=null==T?void 0:T.activeElement;I.current.contains(e)||(0,p.jA)(I.current,p.TO.First)},[v.__demoMode,l,I,v.popoverState]);let F=(0,r.useMemo)(()=>({open:0===v.popoverState,close:b}),[v,b]),R={ref:N,id:o,onKeyDown:j,onBlur:l&&0===v.popoverState?e=>{var t,n,o,l,r;let a=e.relatedTarget;a&&I.current&&(null!=(t=I.current)&&t.contains(a)||(m({type:1}),(null!=(o=null==(n=v.beforePanelSentinel.current)?void 0:n.contains)&&o.call(n,a)||null!=(r=null==(l=v.afterPanelSentinel.current)?void 0:l.contains)&&r.call(l,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},O=(0,x.l)(),q=(0,P.z)(()=>{let e=I.current;e&&(0,a.E)(O.current,{[x.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=v.afterPanelSentinel.current)||t.focus())},[x.N.Backwards]:()=>{var e;null==(e=v.button)||e.focus({preventScroll:!0})}})}),Z=(0,P.z)(()=>{let e=I.current;e&&(0,a.E)(O.current,{[x.N.Forwards]:()=>{var e;if(!v.button)return;let t=(0,p.GO)(),n=t.indexOf(v.button),o=t.slice(0,n+1),l=[...t.slice(n+1),...o];for(let t of l.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=v.panel)&&e.contains(t)){let e=l.indexOf(t);-1!==e&&l.splice(e,1)}(0,p.jA)(l,p.TO.First,{sorted:!1})},[x.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=v.button)||t.focus())}})});return r.createElement(D.Provider,{value:o},k&&y&&r.createElement(g._,{id:S,ref:v.beforePanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:q}),(0,s.sY)({ourProps:R,theirProps:d,slot:F,defaultTag:"div",features:_,visible:k,name:"Popover.Panel"}),k&&y&&r.createElement(g._,{id:A,ref:v.afterPanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:Z}))}),Group:(0,s.yV)(function(e,t){let n=(0,r.useRef)(null),o=(0,u.T)(n,t),[l,a]=(0,r.useState)([]),i=(0,A.H)(),c=(0,P.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,P.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,P.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let o=t.activeElement;return!!(null!=(e=n.current)&&e.contains(o))||l.some(e=>{var n,l;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(o))||(null==(l=t.getElementById(e.panelId.current))?void 0:l.contains(o))})}),f=(0,P.z)(e=>{for(let t of l)t.buttonId.current!==e&&t.close()}),v=(0,r.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),m=(0,r.useMemo)(()=>({}),[]);return r.createElement(F.Provider,{value:v},(0,s.sY)({ourProps:{ref:o},theirProps:e,slot:m,defaultTag:"div",name:"Popover.Group"}),r.createElement(i.MainTreeNode,null))})})}}]);