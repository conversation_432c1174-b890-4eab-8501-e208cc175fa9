(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3703],{46816:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/refund-reasons",function(){return n(26088)}])},37912:function(e,t,n){"use strict";var r=n(85893),o=n(5114),l=n(80287),a=n(93967),s=n.n(a),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:m,...v}=e,{register:b,handleSubmit:h,watch:g,reset:x,formState:{errors:y}}=(0,i.cI)({defaultValues:{searchText:""}}),S=g("searchText"),{t:P}=(0,c.$G)();(0,u.useEffect)(()=>{S||n({searchText:""})},[S]);let E=s()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(s()("relative flex w-full items-center",t)),onSubmit:h(n),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:P("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(l.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(E),placeholder:null!=m?m:P("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),y.searchText&&(0,r.jsx)("p",{children:y.searchText.message}),!!S&&(0,r.jsx)("button",{type:"button",onClick:function(){x(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(o.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){"use strict";n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},97670:function(e,t,n){"use strict";n.r(t);var r=n(85893),o=n(78985),l=n(79362),a=n(8144),s=n(74673),u=n(99494),i=n(5233),c=n(1631),d=n(11163),p=n(48583),f=n(93967),m=n.n(f),v=n(30824),b=n(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:n}=(0,i.$G)(),[o,a]=(0,p.KO)(l.Hf),{childMenu:s}=t,{width:u}=(0,b.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:a,icon:s,childMenu:i}=e;return(0,r.jsx)(c.Z,{href:t,label:n(a),icon:s,childMenu:i,miniSidebar:o&&u>=l.h2},a)})})},SideBarGroup=()=>{var e;let{t}=(0,i.$G)(),[n,o]=(0,p.KO)(l.Hf),a=null===u.siteSettings||void 0===u.siteSettings?void 0:null===(e=u.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(a),{width:c}=(0,b.Z)();return(0,r.jsx)(r.Fragment,{children:null==s?void 0:s.map((e,o)=>{var s;return(0,r.jsxs)("div",{className:m()("flex flex-col px-5",n&&c>=l.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",n&&c>=l.h2?"hidden":""),children:t(null===(s=a[e])||void 0===s?void 0:s.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},o)})})};t.default=e=>{let{children:t}=e,{locale:n}=(0,d.useRouter)(),[u,i]=(0,p.KO)(l.Hf),[c]=(0,p.KO)(l.GH),[f]=(0,p.KO)(l.W4),{width:h}=(0,b.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===n||"he"===n?"rtl":"ltr",children:[(0,r.jsx)(o.Z,{}),(0,r.jsx)(s.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=l.h2&&(c||f)?"lg:pt-[8.75rem]":"pt-20",u&&h>=l.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(v.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=l.h2&&(c||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",u&&h>=l.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(a.Z,{})]})]})]})}},18230:function(e,t,n){"use strict";n.d(t,{Z:function(){return pagination}});var r=n(85893),o=n(55891),l=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,r.jsx)(o.Z,{nextIcon:(0,r.jsx)(l.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},14388:function(e,t,n){"use strict";n.d(t,{ng:function(){return useCreateRefunReasonMutation},YL:function(){return useDeleteRefundReasonMutation},Rz:function(){return useRefundReasonQuery},Ff:function(){return useRefundReasonsQuery},o_:function(){return useUpdateRefundReasonMutation}});var r=n(11163),o=n.n(r),l=n(88767),a=n(22920),s=n(5233),u=n(97514),i=n(47869),c=n(28597),d=n(55191),p=n(3737);let f={...(0,d.h)(i.P.REFUND_REASONS),paginated:e=>{let{name:t,...n}=e;return p.eN.get(i.P.REFUND_REASONS,{searchJoin:"and",...n,search:p.eN.formatSearchParams({name:t})})}};var m=n(93345);let useCreateRefunReasonMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,s.$G)(),n=(0,r.useRouter)();return(0,l.useMutation)(f.create,{onSuccess:async()=>{let e=n.query.shop?"/".concat(n.query.shop).concat(u.Z.refundReasons.list):u.Z.refundReasons.list;await o().push(e,void 0,{locale:m.Config.defaultLanguage}),a.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.REFUND_REASONS)},onError:e=>{var n;a.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})},useDeleteRefundReasonMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,s.$G)();return(0,l.useMutation)(f.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.REFUND_REASONS)},onError:e=>{var n;a.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})},useUpdateRefundReasonMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,r.useRouter)(),n=(0,l.useQueryClient)();return(0,l.useMutation)(f.update,{onSuccess:async n=>{let r=t.query.shop?"/".concat(t.query.shop).concat(u.Z.refundReasons.list):u.Z.refundReasons.list;await t.push("".concat(r,"/").concat(null==n?void 0:n.slug,"/edit"),void 0,{locale:m.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(i.P.REFUND_REASONS)},onError:t=>{var n;a.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useRefundReasonQuery=e=>{let{slug:t,language:n}=e,{data:r,error:o,isLoading:a}=(0,l.useQuery)([i.P.REFUND_REASONS,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{refundReason:r,error:o,loading:a}},useRefundReasonsQuery=e=>{var t;let{data:n,error:r,isLoading:o}=(0,l.useQuery)([i.P.REFUND_REASONS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{refundReasons:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(n),error:r,loading:o}}},26088:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return R},default:function(){return RefundReasons}});var r=n(85893),o=n(92072),l=n(37912),a=n(97670),s=n(34927),u=n(18230),i=n(27899),c=n(78998),d=n(97514),p=n(10265),f=n(76518),m=n(5233),v=n(11163),b=n(67294),refund_reason_list=e=>{let{refundReasons:t,paginatorInfo:n,onPagination:o,onSort:l,onOrder:a}=e,{t:h}=(0,m.$G)();(0,v.useRouter)();let{alignLeft:g}=(0,f.S)(),[x,y]=(0,b.useState)({sort:p.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{l(e=>e===p.As.Desc?p.As.Asc:p.As.Desc),a(e),y({sort:x.sort===p.As.Desc?p.As.Asc:p.As.Desc,column:e})}}),S=[{title:(0,r.jsx)(c.Z,{title:h("table:table-item-id"),ascending:x.sort===p.As.Asc&&"id"===x.column,isActive:"id"===x.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:g,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(h("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(c.Z,{title:h("table:table-item-title"),ascending:x.sort===p.As.Asc&&"name"===x.column,isActive:"name"===x.column}),className:"cursor-pointer",dataIndex:"name",key:"name",width:400,ellipsis:!0,align:g,render:e=>(0,r.jsx)("span",{className:"font-medium",children:e}),onHeaderCell:()=>onHeaderClick("name")},{title:(0,r.jsx)(c.Z,{title:h("table:table-item-slug"),ascending:x.sort===p.As.Asc&&"slug"===x.column,isActive:"slug"===x.column}),className:"cursor-pointer",dataIndex:"slug",key:"slug",width:400,ellipsis:!0,align:g,onHeaderCell:()=>onHeaderClick("slug")},{title:h("table:table-item-actions"),dataIndex:"slug",key:"actions",width:120,align:"right",render:(e,t)=>(0,r.jsx)(s.Z,{slug:e,record:t,deleteModalView:"DELETE_REFUND_REASON",routes:null===d.Z||void 0===d.Z?void 0:d.Z.refundReasons})}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,r.jsx)(i.i,{columns:S,emptyText:h("table:empty-table-data"),data:t,rowKey:"id",scroll:{x:900}})}),!!(null==n?void 0:n.total)&&(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(u.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:o})})]})},h=n(45957),g=n(61616),x=n(55846),y=n(93345),S=n(14388),P=n(16203),E=n(79362),R=!0;function RefundReasons(){let{t:e}=(0,m.$G)(),{locale:t}=(0,v.useRouter)(),[n,a]=(0,b.useState)(""),[s,u]=(0,b.useState)(1),[i,c]=(0,b.useState)("created_at"),[f,P]=(0,b.useState)(p.As.Desc),{refundReasons:R,loading:N,paginatorInfo:I,error:T}=(0,S.Ff)({limit:E.VZ,name:n.replace(/-/g," "),page:s,orderBy:i,sortedBy:f,language:t});return N?(0,r.jsx)(x.Z,{text:e("common:text-loading")}):T?(0,r.jsx)(h.Z,{message:T.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o.Z,{className:"mb-8 flex flex-col items-center xl:flex-row",children:[(0,r.jsx)("div",{className:"mb-4 md:w-1/4 xl:mb-0",children:(0,r.jsx)("h1",{className:"before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 xl:before:w-1",children:e("common:text-refund-reasons")})}),(0,r.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-2/3 2xl:w-2/4",children:[(0,r.jsx)(l.Z,{onSearch:function(e){let{searchText:t}=e;a(t)},placeholderText:e("form:input-placeholder-search-name")}),t===y.Config.defaultLanguage&&(0,r.jsx)(g.Z,{href:"".concat(d.Z.refundReasons.create),className:"h-12 w-full md:w-auto md:ms-6",children:(0,r.jsxs)("span",{children:["+ ",e("form:button-label-add-refund-reason")]})})]})]}),(0,r.jsx)(refund_reason_list,{refundReasons:R,paginatorInfo:I,onPagination:function(e){u(e)},onOrder:c,onSort:P})]})}RefundReasons.authenticate={permissions:P.M$},RefundReasons.Layout=a.default},28368:function(e,t,n){"use strict";n.d(t,{p:function(){return I}});var r,o,l,a=n(67294),s=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),m=n(14157),v=n(15466),b=n(73781);let h=null!=(l=a.startTransition)?l:function(e){e()};var g=((r=g||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),x=((o=x||{})[o.ToggleDisclosure=0]="ToggleDisclosure",o[o.CloseDisclosure=1]="CloseDisclosure",o[o.SetButtonId=2]="SetButtonId",o[o.SetPanelId=3]="SetPanelId",o[o.LinkPanel=4]="LinkPanel",o[o.UnlinkPanel=5]="UnlinkPanel",o);let y={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},S=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}S.displayName="DisclosureContext";let P=(0,a.createContext)(null);P.displayName="DisclosureAPIContext";let E=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,y,e,t)}E.displayName="DisclosurePanelContext";let R=a.Fragment,N=u.AN.RenderStrategy|u.AN.Static,I=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,o=(0,a.useRef)(null),l=(0,i.T)(t,(0,i.h)(e=>{o.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:h},g]=p,x=(0,b.z)(e=>{g({type:1});let t=(0,v.r)(o);if(!t||!h)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(h):t.getElementById(h);null==n||n.focus()}),y=(0,a.useMemo)(()=>({close:x}),[x]),E=(0,a.useMemo)(()=>({open:0===m,close:x}),[m,x]);return a.createElement(S.Provider,{value:p},a.createElement(P.Provider,{value:y},a.createElement(f.up,{value:(0,s.E)(m,{0:f.ZM.Open,1:f.ZM.Closed})},(0,u.sY)({ourProps:{ref:l},theirProps:r,slot:E,defaultTag:R,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-button-${n}`,...o}=e,[l,s]=M("Disclosure.Button"),f=(0,a.useContext)(E),v=null!==f&&f===l.panelId,h=(0,a.useRef)(null),g=(0,i.T)(h,t,v?null:l.buttonRef);(0,a.useEffect)(()=>{if(!v)return s({type:2,buttonId:r}),()=>{s({type:2,buttonId:null})}},[r,s,v]);let x=(0,b.z)(e=>{var t;if(v){if(1===l.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=l.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),y=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),S=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(v?(s({type:0}),null==(n=l.buttonRef.current)||n.focus()):s({type:0}))}),P=(0,a.useMemo)(()=>({open:0===l.disclosureState}),[l]),R=(0,m.f)(e,h),N=v?{ref:g,type:R,onKeyDown:x,onClick:S}:{ref:g,id:r,type:R,"aria-expanded":0===l.disclosureState,"aria-controls":l.linkedPanel?l.panelId:void 0,onKeyDown:x,onKeyUp:y,onClick:S};return(0,u.sY)({ourProps:N,theirProps:o,slot:P,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-panel-${n}`,...o}=e,[l,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(P);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,l.panelRef,e=>{h(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:r}),()=>{s({type:3,panelId:null})}),[r,s]);let m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===l.disclosureState,b=(0,a.useMemo)(()=>({open:0===l.disclosureState,close:d}),[l,d]);return a.createElement(E.Provider,{value:l.panelId},(0,u.sY)({ourProps:{ref:p,id:r},theirProps:o,slot:b,defaultTag:"div",features:N,visible:v,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){"use strict";n.d(t,{J:function(){return Z}});var r,o,l=n(67294),a=n(32984),s=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),m=n(14157),v=n(39650),b=n(15466),h=n(51074),g=n(14007),x=n(46045),y=n(73781),S=n(45662),P=n(3855),E=n(16723),R=n(65958),N=n(2740),I=((r=I||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),T=((o=T||{})[o.TogglePopover=0]="TogglePopover",o[o.ClosePopover=1]="ClosePopover",o[o.SetButton=2]="SetButton",o[o.SetButtonId=3]="SetButtonId",o[o.SetPanel=4]="SetPanel",o[o.SetPanelId=5]="SetPanelId",o);let j={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},C=(0,l.createContext)(null);function oe(e){let t=(0,l.useContext)(C);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}C.displayName="PopoverContext";let k=(0,l.createContext)(null);function fe(e){let t=(0,l.useContext)(k);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}k.displayName="PopoverAPIContext";let A=(0,l.createContext)(null);function Ee(){return(0,l.useContext)(A)}A.displayName="PopoverGroupContext";let O=(0,l.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,j,e,t)}O.displayName="PopoverPanelContext";let D=s.AN.RenderStrategy|s.AN.Static,_=s.AN.RenderStrategy|s.AN.Static,Z=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:r=!1,...o}=e,i=(0,l.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,l.useRef)([]),m=(0,l.useReducer)(Ne,{__demoMode:r,popoverState:r?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,l.createRef)(),afterPanelSentinel:(0,l.createRef)()}),[{popoverState:b,button:x,buttonId:S,panel:E,panelId:I,beforePanelSentinel:T,afterPanelSentinel:j},A]=m,D=(0,h.i)(null!=(n=i.current)?n:x),_=(0,l.useMemo)(()=>{if(!x||!E)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(x))^Number(null==e?void 0:e.contains(E)))return!0;let e=(0,p.GO)(),t=e.indexOf(x),n=(t+e.length-1)%e.length,r=(t+1)%e.length,o=e[n],l=e[r];return!E.contains(o)&&!E.contains(l)},[x,E]),Z=(0,P.E)(S),F=(0,P.E)(I),B=(0,l.useMemo)(()=>({buttonId:Z,panelId:F,close:()=>A({type:1})}),[Z,F,A]),z=Ee(),G=null==z?void 0:z.registerPopover,L=(0,y.z)(()=>{var e;return null!=(e=null==z?void 0:z.isFocusWithinPopoverGroup())?e:(null==D?void 0:D.activeElement)&&((null==x?void 0:x.contains(D.activeElement))||(null==E?void 0:E.contains(D.activeElement)))});(0,l.useEffect)(()=>null==G?void 0:G(B),[G,B]);let[$,H]=(0,N.k)(),U=(0,R.v)({mainTreeNodeRef:null==z?void 0:z.mainTreeNodeRef,portals:$,defaultContainers:[x,E]});(0,g.O)(null==D?void 0:D.defaultView,"focus",e=>{var t,n,r,o;e.target!==window&&e.target instanceof HTMLElement&&0===b&&(L()||x&&E&&(U.contains(e.target)||null!=(n=null==(t=T.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(o=null==(r=j.current)?void 0:r.contains)&&o.call(r,e.target)||A({type:1})))},!0),(0,v.O)(U.resolveContainers,(e,t)=>{A({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==x||x.focus())},0===b);let K=(0,y.z)(e=>{A({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:x:x;null==t||t.focus()}),Q=(0,l.useMemo)(()=>({close:K,isPortalled:_}),[K,_]),V=(0,l.useMemo)(()=>({open:0===b,close:K}),[b,K]);return l.createElement(O.Provider,{value:null},l.createElement(C.Provider,{value:m},l.createElement(k.Provider,{value:Q},l.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},l.createElement(H,null,(0,s.sY)({ourProps:{ref:c},theirProps:o,slot:V,defaultTag:"div",name:"Popover"}),l.createElement(U.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-button-${n}`,...o}=e,[f,v]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),g=(0,l.useRef)(null),P=`headlessui-focus-sentinel-${(0,i.M)()}`,E=Ee(),R=null==E?void 0:E.closeOthers,N=null!==(0,l.useContext)(O);(0,l.useEffect)(()=>{if(!N)return v({type:3,buttonId:r}),()=>{v({type:3,buttonId:null})}},[N,r,v]);let[I]=(0,l.useState)(()=>Symbol()),T=(0,u.T)(g,t,N?null:e=>{if(e)f.buttons.current.push(I);else{let e=f.buttons.current.indexOf(I);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&v({type:2,button:e})}),j=(0,u.T)(g,t),C=(0,h.i)(g),k=(0,y.z)(e=>{var t,n,r;if(N){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),v({type:1}),null==(r=f.button)||r.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==R||R(f.buttonId)),v({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==R?void 0:R(f.buttonId);if(!g.current||null!=C&&C.activeElement&&!g.current.contains(C.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1})}}),A=(0,y.z)(e=>{N||e.key===c.R.Space&&e.preventDefault()}),D=(0,y.z)(t=>{var n,r;(0,d.P)(t.currentTarget)||e.disabled||(N?(v({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==R||R(f.buttonId)),v({type:0}),null==(r=f.button)||r.focus()))}),_=(0,y.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===f.popoverState,F=(0,l.useMemo)(()=>({open:Z}),[Z]),B=(0,m.f)(e,g),z=N?{ref:j,type:B,onKeyDown:k,onClick:D}:{ref:T,id:f.buttonId,type:B,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:k,onKeyUp:A,onClick:D,onMouseDown:_},G=(0,S.l)(),L=(0,y.z)(()=>{let e=f.panel;e&&(0,a.E)(G.current,{[S.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[S.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(G.current,{[S.N.Forwards]:p.TO.Next,[S.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return l.createElement(l.Fragment,null,(0,s.sY)({ourProps:z,theirProps:o,slot:F,defaultTag:"button",name:"Popover.Button"}),Z&&!N&&b&&l.createElement(x._,{id:P,features:x.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:L}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-overlay-${n}`,...o}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,u.T)(t),m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===a,b=(0,y.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),h=(0,l.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:p,id:r,"aria-hidden":!0,onClick:b},theirProps:o,slot:h,defaultTag:"div",features:D,visible:v,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-panel-${n}`,focus:o=!1,...d}=e,[m,v]=oe("Popover.Panel"),{close:b,isPortalled:g}=fe("Popover.Panel"),P=`headlessui-focus-sentinel-before-${(0,i.M)()}`,R=`headlessui-focus-sentinel-after-${(0,i.M)()}`,N=(0,l.useRef)(null),I=(0,u.T)(N,t,e=>{v({type:4,panel:e})}),T=(0,h.i)(N);(0,E.e)(()=>(v({type:5,panelId:r}),()=>{v({type:5,panelId:null})}),[r,v]);let j=(0,f.oJ)(),C=null!==j?(j&f.ZM.Open)===f.ZM.Open:0===m.popoverState,k=(0,y.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==m.popoverState||!N.current||null!=T&&T.activeElement&&!N.current.contains(T.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1}),null==(t=m.button)||t.focus()}});(0,l.useEffect)(()=>{var t;e.static||1===m.popoverState&&(null==(t=e.unmount)||t)&&v({type:4,panel:null})},[m.popoverState,e.unmount,e.static,v]),(0,l.useEffect)(()=>{if(m.__demoMode||!o||0!==m.popoverState||!N.current)return;let e=null==T?void 0:T.activeElement;N.current.contains(e)||(0,p.jA)(N.current,p.TO.First)},[m.__demoMode,o,N,m.popoverState]);let A=(0,l.useMemo)(()=>({open:0===m.popoverState,close:b}),[m,b]),D={ref:I,id:r,onKeyDown:k,onBlur:o&&0===m.popoverState?e=>{var t,n,r,o,l;let a=e.relatedTarget;a&&N.current&&(null!=(t=N.current)&&t.contains(a)||(v({type:1}),(null!=(r=null==(n=m.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,a)||null!=(l=null==(o=m.afterPanelSentinel.current)?void 0:o.contains)&&l.call(o,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,S.l)(),F=(0,y.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[S.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=m.afterPanelSentinel.current)||t.focus())},[S.N.Backwards]:()=>{var e;null==(e=m.button)||e.focus({preventScroll:!0})}})}),B=(0,y.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[S.N.Forwards]:()=>{var e;if(!m.button)return;let t=(0,p.GO)(),n=t.indexOf(m.button),r=t.slice(0,n+1),o=[...t.slice(n+1),...r];for(let t of o.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=m.panel)&&e.contains(t)){let e=o.indexOf(t);-1!==e&&o.splice(e,1)}(0,p.jA)(o,p.TO.First,{sorted:!1})},[S.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=m.button)||t.focus())}})});return l.createElement(O.Provider,{value:r},C&&g&&l.createElement(x._,{id:P,ref:m.beforePanelSentinel,features:x.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}),(0,s.sY)({ourProps:D,theirProps:d,slot:A,defaultTag:"div",features:_,visible:C,name:"Popover.Panel"}),C&&g&&l.createElement(x._,{id:R,ref:m.afterPanelSentinel,features:x.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:B}))}),Group:(0,s.yV)(function(e,t){let n=(0,l.useRef)(null),r=(0,u.T)(n,t),[o,a]=(0,l.useState)([]),i=(0,R.H)(),c=(0,y.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,y.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,y.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||o.some(e=>{var n,o;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(o=t.getElementById(e.panelId.current))?void 0:o.contains(r))})}),f=(0,y.z)(e=>{for(let t of o)t.buttonId.current!==e&&t.close()}),m=(0,l.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),v=(0,l.useMemo)(()=>({}),[]);return l.createElement(A.Provider,{value:m},(0,s.sY)({ourProps:{ref:r},theirProps:e,slot:v,defaultTag:"div",name:"Popover.Group"}),l.createElement(i.MainTreeNode,null))})})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,8504,2713,9774,2888,179],function(){return e(e.s=46816)}),_N_E=e.O()}]);