'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Seed Products
    await queryInterface.bulkInsert('products', [
      {
        name: 'iPhone 15 Pro',
        slug: 'iphone-15-pro',
        description: 'Latest iPhone with advanced features',
        type_id: 2, // Electronics
        shop_id: 1,
        price: 999.99,
        sale_price: 899.99,
        sku: 'IPH15PRO001',
        quantity: 50,
        in_stock: true,
        is_taxable: true,
        status: 'publish',
        product_type: 'simple',
        unit: 'piece',
        height: '5.77',
        length: '2.78',
        width: '0.32',
        weight: '0.187',
        image: JSON.stringify({
          id: 1,
          original: 'https://via.placeholder.com/400x400/000000/FFFFFF?text=iPhone+15+Pro',
          thumbnail: 'https://via.placeholder.com/150x150/000000/FFFFFF?text=iPhone+15+Pro'
        }),
        gallery: JSON.stringify([
          {
            id: 1,
            original: 'https://via.placeholder.com/400x400/000000/FFFFFF?text=iPhone+15+Pro',
            thumbnail: 'https://via.placeholder.com/150x150/000000/FFFFFF?text=iPhone+15+Pro'
          }
        ]),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'MacBook Pro 16"',
        slug: 'macbook-pro-16',
        description: 'Powerful laptop for professionals',
        type_id: 2, // Electronics
        shop_id: 1,
        price: 2499.99,
        sale_price: 2299.99,
        sku: 'MBP16001',
        quantity: 25,
        in_stock: true,
        is_taxable: true,
        status: 'publish',
        product_type: 'simple',
        unit: 'piece',
        height: '1.68',
        length: '14.01',
        width: '9.77',
        weight: '4.7',
        image: JSON.stringify({
          id: 2,
          original: 'https://via.placeholder.com/400x400/333333/FFFFFF?text=MacBook+Pro',
          thumbnail: 'https://via.placeholder.com/150x150/333333/FFFFFF?text=MacBook+Pro'
        }),
        gallery: JSON.stringify([
          {
            id: 2,
            original: 'https://via.placeholder.com/400x400/333333/FFFFFF?text=MacBook+Pro',
            thumbnail: 'https://via.placeholder.com/150x150/333333/FFFFFF?text=MacBook+Pro'
          }
        ]),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Cotton T-Shirt',
        slug: 'cotton-t-shirt',
        description: 'Comfortable cotton t-shirt for everyday wear',
        type_id: 1, // General
        shop_id: 1,
        price: 29.99,
        sale_price: 24.99,
        sku: 'TSHIRT001',
        quantity: 100,
        in_stock: true,
        is_taxable: true,
        status: 'publish',
        product_type: 'simple',
        unit: 'piece',
        height: '0.5',
        length: '28',
        width: '20',
        weight: '0.2',
        image: JSON.stringify({
          id: 3,
          original: 'https://via.placeholder.com/400x400/0066CC/FFFFFF?text=T-Shirt',
          thumbnail: 'https://via.placeholder.com/150x150/0066CC/FFFFFF?text=T-Shirt'
        }),
        gallery: JSON.stringify([
          {
            id: 3,
            original: 'https://via.placeholder.com/400x400/0066CC/FFFFFF?text=T-Shirt',
            thumbnail: 'https://via.placeholder.com/150x150/0066CC/FFFFFF?text=T-Shirt'
          }
        ]),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Wireless Headphones',
        slug: 'wireless-headphones',
        description: 'High-quality wireless headphones with noise cancellation',
        type_id: 2, // Electronics
        shop_id: 1,
        price: 199.99,
        sale_price: 149.99,
        sku: 'HEADPHONES001',
        quantity: 75,
        in_stock: true,
        is_taxable: true,
        status: 'publish',
        product_type: 'simple',
        unit: 'piece',
        height: '8',
        length: '7',
        width: '6',
        weight: '0.3',
        image: JSON.stringify({
          id: 4,
          original: 'https://via.placeholder.com/400x400/FF6600/FFFFFF?text=Headphones',
          thumbnail: 'https://via.placeholder.com/150x150/FF6600/FFFFFF?text=Headphones'
        }),
        gallery: JSON.stringify([
          {
            id: 4,
            original: 'https://via.placeholder.com/400x400/FF6600/FFFFFF?text=Headphones',
            thumbnail: 'https://via.placeholder.com/150x150/FF6600/FFFFFF?text=Headphones'
          }
        ]),
        language: 'en',
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {
      ignoreDuplicates: true
    });

    // Seed Product-Category relationships
    await queryInterface.bulkInsert('product_categories', [
      { product_id: 1, category_id: 1 }, // iPhone -> Smartphones
      { product_id: 2, category_id: 2 }, // MacBook -> Laptops
      { product_id: 3, category_id: 3 }, // T-Shirt -> Clothing
      { product_id: 4, category_id: 1 }  // Headphones -> Smartphones (electronics)
    ], {
      ignoreDuplicates: true
    });

    // Seed Product-Tag relationships
    await queryInterface.bulkInsert('product_tags', [
      { product_id: 1, tag_id: 1 }, // iPhone -> Popular
      { product_id: 1, tag_id: 2 }, // iPhone -> New Arrival
      { product_id: 2, tag_id: 1 }, // MacBook -> Popular
      { product_id: 4, tag_id: 2 }  // Headphones -> New Arrival
    ], {
      ignoreDuplicates: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('product_tags', null, {});
    await queryInterface.bulkDelete('product_categories', null, {});
    await queryInterface.bulkDelete('products', null, {});
  }
};
