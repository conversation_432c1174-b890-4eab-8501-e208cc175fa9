"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8690],{48690:function(e,t,s){s.r(t),s.d(t,{default:function(){return contact_grid}});var c=s(85893),r=s(67294),a=s(48583),n=s(8657),l=s(75814),d=s(93967),i=s.n(d),contact_card=e=>{let{checked:t,number:s}=e;return(0,c.jsx)("div",{className:i()("relative p-4 h-full rounded border cursor-pointer group hover:border-accent",{"border-accent shadow-sm bg-light":t,"bg-gray-100 border-transparent":!t}),children:(0,c.jsx)("p",{className:"text-sm text-heading font-semibold capitalize",children:s})})},o=s(85031),x=s(5233),contact_grid=e=>{let{contact:t,label:s,count:d,className:i}=e,[u,m]=(0,a.KO)(n.lu),{openModal:h}=(0,l.SO)(),{t:b}=(0,x.$G)("common");return(0,r.useEffect)(()=>{t&&m(t)},[t,m]),(0,c.jsxs)("div",{className:i,children:[(0,c.jsxs)("div",{className:"mb-5 flex items-center justify-between md:mb-8",children:[(0,c.jsxs)("div",{className:"space-s-3 md:space-s-4 flex items-center",children:[d&&(0,c.jsx)("span",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-accent text-base text-light lg:text-xl",children:d}),(0,c.jsx)("p",{className:"text-lg capitalize text-heading lg:text-xl",children:s})]}),(0,c.jsxs)("button",{className:"flex items-center text-sm font-semibold text-accent transition-colors duration-200 hover:text-accent-hover focus:text-accent-hover focus:outline-none",onClick:function(){h("ADD_OR_UPDATE_CHECKOUT_CONTACT")},children:[(0,c.jsx)(o.p,{className:"me-0.5 h-4 w-4 stroke-2"}),b(u?"text-update":"text-add")]})]}),(0,c.jsx)("div",{className:"grid grid-cols-1 gap-4",children:u?(0,c.jsx)(contact_card,{checked:!!u,number:u}):(0,c.jsx)("span",{className:"relative rounded border border-border-200 bg-gray-100 px-5 py-6 text-center text-base",children:b("text-no-contact")})})]})}}}]);