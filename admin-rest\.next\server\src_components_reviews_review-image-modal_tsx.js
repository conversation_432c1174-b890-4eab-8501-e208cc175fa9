"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_reviews_review-image-modal_tsx";
exports.ids = ["src_components_reviews_review-image-modal_tsx"];
exports.modules = {

/***/ "./src/components/icons/chevron-left.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/chevron-left.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeft: () => (/* binding */ ChevronLeft)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ChevronLeft = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGV2cm9uLWxlZnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxjQUFpRCxDQUFDQyxzQkFDN0QsOERBQUNDO1FBQUlDLE1BQUs7UUFBT0MsU0FBUTtRQUFZQyxRQUFPO1FBQWdCLEdBQUdKLEtBQUs7a0JBQ2xFLDRFQUFDSztZQUNDQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQWE7WUFDYkMsR0FBRTs7Ozs7Ozs7OztrQkFHTiIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NoZXZyb24tbGVmdC50c3g/ZDA1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQ2hldnJvbkxlZnQ6IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxyXG4gIDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgey4uLnByb3BzfT5cclxuICAgIDxwYXRoXHJcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICBzdHJva2VXaWR0aD17Mn1cclxuICAgICAgZD1cIk0xNSAxOWwtNy03IDctN1wiXHJcbiAgICAvPlxyXG4gIDwvc3ZnPlxyXG4pO1xyXG4iXSwibmFtZXMiOlsiQ2hldnJvbkxlZnQiLCJwcm9wcyIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/chevron-left.tsx\n");

/***/ }),

/***/ "./src/components/icons/chevron-right.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/chevron-right.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronRight: () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ChevronRight = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 5l7 7-7 7\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGV2cm9uLXJpZ2h0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsZUFBa0QsQ0FBQ0Msc0JBQzlELDhEQUFDQztRQUFJQyxNQUFLO1FBQU9DLFNBQVE7UUFBWUMsUUFBTztRQUFnQixHQUFHSixLQUFLO2tCQUNsRSw0RUFBQ0s7WUFDQ0MsZUFBYztZQUNkQyxnQkFBZTtZQUNmQyxhQUFhO1lBQ2JDLEdBQUU7Ozs7Ozs7Ozs7a0JBR04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGV2cm9uLXJpZ2h0LnRzeD8wNWZiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBDaGV2cm9uUmlnaHQ6IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxyXG4gIDxzdmcgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgey4uLnByb3BzfT5cclxuICAgIDxwYXRoXHJcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICBzdHJva2VXaWR0aD17Mn1cclxuICAgICAgZD1cIk05IDVsNyA3LTcgN1wiXHJcbiAgICAvPlxyXG4gIDwvc3ZnPlxyXG4pO1xyXG4iXSwibmFtZXMiOlsiQ2hldnJvblJpZ2h0IiwicHJvcHMiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/chevron-right.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/CNFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/CNFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CNFlag: () => (/* binding */ CNFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CNFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: width,\n        height: height,\n        viewBox: \"0 0 512 512\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mask\", {\n                id: \"a\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"256\",\n                    cy: \"256\",\n                    r: \"256\",\n                    fill: \"#fff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                mask: \"url(#a)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"M0 0h512v512H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ffda44\",\n                        d: \"m140.1 155.8 22.1 68h71.5l-57.8 42.1 22.1 68-57.9-42-57.9 42 22.2-68-57.9-42.1H118zm163.4 240.7-16.9-20.8-25 9.7 14.5-22.5-16.9-20.9 25.9 6.9 14.6-22.5 1.4 26.8 26 6.9-25.1 9.6zm33.6-61 8-25.6-21.9-15.5 26.8-.4 7.9-25.6 8.7 25.4 26.8-.3-21.5 16 8.6 25.4-21.9-15.5zm45.3-147.6L370.6 212l19.2 18.7-26.5-3.8-11.8 24-4.6-26.4-26.6-3.8 23.8-12.5-4.6-26.5 19.2 18.7zm-78.2-73-2 26.7 24.9 10.1-26.1 6.4-1.9 26.8-14.1-22.8-26.1 6.4 17.3-20.5-14.2-22.7 24.9 10.1z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\CNFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/flags/CNFlag.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/DEFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/DEFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFlag: () => (/* binding */ DEFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst DEFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: width,\n        height: height,\n        viewBox: \"0 0 512 512\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mask\", {\n                id: \"a\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"256\",\n                    cy: \"256\",\n                    r: \"256\",\n                    fill: \"#fff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                mask: \"url(#a)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ffda44\",\n                        d: \"m0 345 256.7-25.5L512 345v167H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"m0 167 255-23 257 23v178H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#333\",\n                        d: \"M0 0h512v167H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\DEFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9mbGFncy9ERUZsYWcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxTQUFTLENBQUMsRUFBRUMsUUFBUSxPQUFPLEVBQUVDLFNBQVMsT0FBTyxFQUFFO0lBQzFELHFCQUNFLDhEQUFDQztRQUFJQyxPQUFNO1FBQTZCSCxPQUFPQTtRQUFPQyxRQUFRQTtRQUFRRyxTQUFROzswQkFDNUUsOERBQUNDO2dCQUFLQyxJQUFHOzBCQUNQLDRFQUFDQztvQkFBT0MsSUFBRztvQkFBTUMsSUFBRztvQkFBTUMsR0FBRTtvQkFBTUMsTUFBSzs7Ozs7Ozs7Ozs7MEJBRXpDLDhEQUFDQztnQkFBRVAsTUFBSzs7a0NBQ04sOERBQUNRO3dCQUFLRixNQUFLO3dCQUFVRyxHQUFFOzs7Ozs7a0NBQ3ZCLDhEQUFDRDt3QkFBS0YsTUFBSzt3QkFBVUcsR0FBRTs7Ozs7O2tDQUN2Qiw4REFBQ0Q7d0JBQUtGLE1BQUs7d0JBQU9HLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUk1QixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vc3JjL2NvbXBvbmVudHMvaWNvbnMvZmxhZ3MvREVGbGFnLnRzeD81OTkxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBERUZsYWcgPSAoeyB3aWR0aCA9ICc2NDBweCcsIGhlaWdodCA9ICc0ODBweCcgfSkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiB3aWR0aD17d2lkdGh9IGhlaWdodD17aGVpZ2h0fSB2aWV3Qm94PVwiMCAwIDUxMiA1MTJcIj5cclxuICAgICAgPG1hc2sgaWQ9XCJhXCI+XHJcbiAgICAgICAgPGNpcmNsZSBjeD1cIjI1NlwiIGN5PVwiMjU2XCIgcj1cIjI1NlwiIGZpbGw9XCIjZmZmXCIgLz5cclxuICAgICAgPC9tYXNrPlxyXG4gICAgICA8ZyBtYXNrPVwidXJsKCNhKVwiPlxyXG4gICAgICAgIDxwYXRoIGZpbGw9XCIjZmZkYTQ0XCIgZD1cIm0wIDM0NSAyNTYuNy0yNS41TDUxMiAzNDV2MTY3SDB6XCIgLz5cclxuICAgICAgICA8cGF0aCBmaWxsPVwiI2Q4MDAyN1wiIGQ9XCJtMCAxNjcgMjU1LTIzIDI1NyAyM3YxNzhIMHpcIiAvPlxyXG4gICAgICAgIDxwYXRoIGZpbGw9XCIjMzMzXCIgZD1cIk0wIDBoNTEydjE2N0gwelwiIC8+XHJcbiAgICAgIDwvZz5cclxuICAgIDwvc3ZnPlxyXG4gICk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJERUZsYWciLCJ3aWR0aCIsImhlaWdodCIsInN2ZyIsInhtbG5zIiwidmlld0JveCIsIm1hc2siLCJpZCIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwiZmlsbCIsImciLCJwYXRoIiwiZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/flags/DEFlag.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/ESFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/ESFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ESFlag: () => (/* binding */ ESFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ESFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: width,\n        height: height,\n        viewBox: \"0 0 512 512\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mask\", {\n                id: \"a\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"256\",\n                    cy: \"256\",\n                    r: \"256\",\n                    fill: \"#fff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                mask: \"url(#a)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ffda44\",\n                        d: \"m0 128 256-32 256 32v256l-256 32L0 384Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"M0 0h512v128H0zm0 384h512v128H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        fill: \"#eee\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M144 304h-16v-80h16zm128 0h16v-80h-16z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                cx: \"208\",\n                                cy: \"296\",\n                                rx: \"48\",\n                                ry: \"32\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        fill: \"#d80027\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                width: \"16\",\n                                height: \"24\",\n                                x: \"128\",\n                                y: \"192\",\n                                rx: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                width: \"16\",\n                                height: \"24\",\n                                x: \"272\",\n                                y: \"192\",\n                                rx: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M208 272v24a24 24 0 0 0 24 24 24 24 0 0 0 24-24v-24h-24z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"32\",\n                        height: \"16\",\n                        x: \"120\",\n                        y: \"208\",\n                        fill: \"#ff9811\",\n                        ry: \"8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"32\",\n                        height: \"16\",\n                        x: \"264\",\n                        y: \"208\",\n                        fill: \"#ff9811\",\n                        ry: \"8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"32\",\n                        height: \"16\",\n                        x: \"120\",\n                        y: \"304\",\n                        fill: \"#ff9811\",\n                        rx: \"8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"32\",\n                        height: \"16\",\n                        x: \"264\",\n                        y: \"304\",\n                        fill: \"#ff9811\",\n                        rx: \"8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ff9811\",\n                        d: \"M160 272v24c0 8 4 14 9 19l5-6 5 10a21 21 0 0 0 10 0l5-10 5 6c6-5 9-11 9-19v-24h-9l-5 8-5-8h-10l-5 8-5-8z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M122 252h172m-172 24h28m116 0h28\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"M122 248a4 4 0 0 0-4 4 4 4 0 0 0 4 4h172a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm0 24a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm144 0a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#eee\",\n                        d: \"M196 168c-7 0-13 5-15 11l-5-1c-9 0-16 7-16 16s7 16 16 16c7 0 13-4 15-11a16 16 0 0 0 17-4 16 16 0 0 0 17 4 16 16 0 1 0 10-20 16 16 0 0 0-27-5c-3-4-7-6-12-6zm0 8c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm24 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm-44 10 4 1 4 8c0 4-4 7-8 7s-8-3-8-8c0-4 4-8 8-8zm64 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-7l4-8z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"none\",\n                        d: \"M220 284v12c0 7 5 12 12 12s12-5 12-12v-12z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ff9811\",\n                        d: \"M200 160h16v32h-16z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#eee\",\n                        d: \"M208 224h48v48h-48z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"m248 208-8 8h-64l-8-8c0-13 18-24 40-24s40 11 40 24zm-88 16h48v48h-48z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"20\",\n                        height: \"32\",\n                        x: \"222\",\n                        y: \"232\",\n                        fill: \"#d80027\",\n                        rx: \"10\",\n                        ry: \"10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#ff9811\",\n                        d: \"M168 232v8h8v16h-8v8h32v-8h-8v-16h8v-8zm8-16h64v8h-64z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        fill: \"#ffda44\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"186\",\n                                cy: \"202\",\n                                r: \"6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"208\",\n                                cy: \"202\",\n                                r: \"6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"230\",\n                                cy: \"202\",\n                                r: \"6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"M169 272v43a24 24 0 0 0 10 4v-47h-10zm20 0v47a24 24 0 0 0 10-4v-43h-10z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        fill: \"#338af3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"208\",\n                                cy: \"272\",\n                                r: \"16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                width: \"32\",\n                                height: \"16\",\n                                x: \"264\",\n                                y: \"320\",\n                                ry: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                width: \"32\",\n                                height: \"16\",\n                                x: \"120\",\n                                y: \"320\",\n                                ry: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ESFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/flags/ESFlag.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/ILFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/ILFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ILFlag: () => (/* binding */ ILFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ILFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 640 480\",\n        width: width,\n        height: height,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"clipPath\", {\n                    id: \"il-a\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillOpacity: \".7\",\n                        d: \"M-87.6 0H595v512H-87.6z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                fillRule: \"evenodd\",\n                clipPath: \"url(#il-a)\",\n                transform: \"translate(82.1) scale(.94)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M619.4 512H-112V0h731.4z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#00c\",\n                        d: \"M619.4 115.2H-112V48h731.4zm0 350.5H-112v-67.2h731.4zm-483-275l110.1 191.6L359 191.6l-222.6-.8z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M225.8 317.8l20.9 35.5 21.4-35.3-42.4-.2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#00c\",\n                        d: \"M136 320.6L246.2 129l112.4 190.8-222.6.8z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#fff\",\n                        d: \"M225.8 191.6l20.9-35.5 21.4 35.4-42.4.1zM182 271.1l-21.7 36 41-.1-19.3-36zm-21.3-66.5l41.2.3-19.8 36.3-21.4-36.6zm151.2 67l20.9 35.5-41.7-.5 20.8-35zm20.5-67l-41.2.3 19.8 36.3 21.4-36.6zm-114.3 0L189.7 256l28.8 50.3 52.8 1.2 32-51.5-29.6-52-55.6.5z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\ILFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/flags/ILFlag.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/SAFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/SAFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SAFlag: () => (/* binding */ SAFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst SAFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: width,\n        height: height,\n        viewBox: \"0 0 512 512\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mask\", {\n                id: \"a\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"256\",\n                    cy: \"256\",\n                    r: \"256\",\n                    fill: \"#fff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                mask: \"url(#a)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#496e2d\",\n                        d: \"M0 0h512v512H0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        fill: \"#eee\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M144.7 306c0 18.5 15 33.5 33.4 33.5h100.2a27.8 27.8 0 0 0 27.8 27.8h33.4a27.8 27.8 0 0 0 27.8-27.8V306zm225.4-161.3v78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9H370zm-239.3 78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9h-33.4z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M320 144.7h33.4v78H320zm-50 44.5a5.6 5.6 0 0 1-11.2 0v-44.5h-33.4v44.5a5.6 5.6 0 0 1-11.1 0v-44.5h-33.4v44.5a39 39 0 0 0 39 39 38.7 38.7 0 0 0 22.2-7 38.7 38.7 0 0 0 22.2 7c1.7 0 3.4-.1 5-.3a22.3 22.3 0 0 1-21.6 17v33.4c30.6 0 55.6-25 55.6-55.7v-77.9H270z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M180.9 244.9h50v33.4h-50z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\SAFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/flags/SAFlag.tsx\n");

/***/ }),

/***/ "./src/components/icons/flags/USFlag.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/flags/USFlag.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   USFlag: () => (/* binding */ USFlag)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst USFlag = ({ width = \"640px\", height = \"480px\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: width,\n        height: height,\n        viewBox: \"0 0 512 512\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mask\", {\n                id: \"a\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"256\",\n                    cy: \"256\",\n                    r: \"256\",\n                    fill: \"#fff\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                mask: \"url(#a)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#eee\",\n                        d: \"M256 0h256v64l-32 32 32 32v64l-32 32 32 32v64l-32 32 32 32v64l-256 32L0 448v-64l32-32-32-32v-64z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#d80027\",\n                        d: \"M224 64h288v64H224Zm0 128h288v64H256ZM0 320h512v64H0Zm0 128h512v64H0Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#0052b4\",\n                        d: \"M0 0h256v256H0Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fill: \"#eee\",\n                        d: \"m187 243 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67zm162-81 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Zm162-82 57-41h-70l57 41-22-67Zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\icons\\\\flags\\\\USFlag.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/flags/USFlag.tsx\n");

/***/ }),

/***/ "./src/components/reviews/review-image-modal.tsx":
/*!*******************************************************!*\
  !*** ./src/components/reviews/review-image-modal.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _utils_locals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/locals */ \"./src/utils/locals.tsx\");\n/* harmony import */ var _components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/chevron-left */ \"./src/components/icons/chevron-left.tsx\");\n/* harmony import */ var _components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/chevron-right */ \"./src/components/icons/chevron-right.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/slider */ \"./src/components/ui/slider.tsx\");\n/* harmony import */ var _utils_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/use-swiper-ref */ \"./src/utils/use-swiper-ref.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__]);\n_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nconst ReviewImageModal = ()=>{\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { isRTL } = (0,_utils_locals__WEBPACK_IMPORTED_MODULE_3__.useIsRTL)();\n    const [nextEl, nextRef] = (0,_utils_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const [prevEl, prevRef] = (0,_utils_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"m-auto block w-full max-w-[680px] rounded bg-light p-3\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Swiper, {\n                    id: \"review-gallery\",\n                    modules: [\n                        _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Navigation\n                    ],\n                    initialSlide: data?.initSlide ?? 0,\n                    // onSwiper={(swiper) => {\n                    //   setTimeout(() => {\n                    //     swiper.navigation.init();\n                    //   }, 100);\n                    // }}\n                    onInit: (swiper)=>{\n                        swiper.navigation.init();\n                        swiper.navigation.update();\n                    },\n                    loop: data?.images?.length > 1,\n                    navigation: {\n                        prevEl,\n                        nextEl\n                    },\n                    spaceBetween: 0,\n                    slidesPerView: 1,\n                    children: data?.images?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.SwiperSlide, {\n                            className: \"relative flex items-center justify-center selection:bg-transparent\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: item?.original ?? \"/product-placeholder-borderless.svg\",\n                                alt: `Review gallery ${item.id}`,\n                                width: 600,\n                                height: 600,\n                                className: \"object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, undefined)\n                        }, `review-gallery-${item.id}`, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined),\n                data?.images?.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: prevRef,\n                            className: \"absolute z-10 flex items-center justify-center w-8 h-8 -mt-4 transition-all duration-200 border rounded-full shadow-xl cursor-pointer review-gallery-prev top-2/4 border-border-200 border-opacity-70 bg-light text-heading start-2 hover:bg-gray-100 md:-mt-5 md:h-9 md:w-9 md:start-3\",\n                            children: isRTL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: nextRef,\n                            className: \"absolute z-10 flex items-center justify-center w-8 h-8 -mt-4 transition-all duration-200 border rounded-full shadow-xl cursor-pointer review-gallery-next top-2/4 border-border-200 border-opacity-70 bg-light text-heading end-2 hover:bg-gray-100 md:-mt-5 md:h-9 md:w-9 md:end-3\",\n                            children: isRTL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewImageModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/reviews/review-image-modal.tsx\n");

/***/ }),

/***/ "./src/components/ui/slider.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/slider.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A11y: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_4__.A11y),\n/* harmony export */   Navigation: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Navigation),\n/* harmony export */   Pagination: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Pagination),\n/* harmony export */   Scrollbar: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Scrollbar),\n/* harmony export */   Swiper: () => (/* reexport safe */ swiper_react__WEBPACK_IMPORTED_MODULE_5__.Swiper),\n/* harmony export */   SwiperSlide: () => (/* reexport safe */ swiper_react__WEBPACK_IMPORTED_MODULE_5__.SwiperSlide)\n/* harmony export */ });\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swiper/css */ \"./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(swiper_css__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper/css/navigation */ \"./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/css/pagination */ \"./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(swiper_css_pagination__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/css/thumbs */ \"./node_modules/swiper/modules/thumbs.css\");\n/* harmony import */ var swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_modules__WEBPACK_IMPORTED_MODULE_4__, swiper_react__WEBPACK_IMPORTED_MODULE_5__]);\n([swiper_modules__WEBPACK_IMPORTED_MODULE_4__, swiper_react__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9zbGlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0I7QUFDVztBQUNBO0FBQ0o7QUFDOEM7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvY29tcG9uZW50cy91aS9zbGlkZXIudHN4PzE3YzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdzd2lwZXIvY3NzJztcclxuaW1wb3J0ICdzd2lwZXIvY3NzL25hdmlnYXRpb24nO1xyXG5pbXBvcnQgJ3N3aXBlci9jc3MvcGFnaW5hdGlvbic7XHJcbmltcG9ydCAnc3dpcGVyL2Nzcy90aHVtYnMnO1xyXG5leHBvcnQgeyBOYXZpZ2F0aW9uLCBQYWdpbmF0aW9uLCBTY3JvbGxiYXIsIEExMXkgfSBmcm9tICdzd2lwZXIvbW9kdWxlcyc7XHJcbmV4cG9ydCB7IFN3aXBlciwgU3dpcGVyU2xpZGUgfSBmcm9tICdzd2lwZXIvcmVhY3QnO1xyXG4iXSwibmFtZXMiOlsiTmF2aWdhdGlvbiIsIlBhZ2luYXRpb24iLCJTY3JvbGxiYXIiLCJBMTF5IiwiU3dpcGVyIiwiU3dpcGVyU2xpZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/slider.tsx\n");

/***/ }),

/***/ "./src/utils/locals.tsx":
/*!******************************!*\
  !*** ./src/utils/locals.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   languageMenu: () => (/* binding */ languageMenu),\n/* harmony export */   useIsRTL: () => (/* binding */ useIsRTL)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_icons_flags_SAFlag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/flags/SAFlag */ \"./src/components/icons/flags/SAFlag.tsx\");\n/* harmony import */ var _components_icons_flags_CNFlag__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/flags/CNFlag */ \"./src/components/icons/flags/CNFlag.tsx\");\n/* harmony import */ var _components_icons_flags_USFlag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/flags/USFlag */ \"./src/components/icons/flags/USFlag.tsx\");\n/* harmony import */ var _components_icons_flags_DEFlag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/flags/DEFlag */ \"./src/components/icons/flags/DEFlag.tsx\");\n/* harmony import */ var _components_icons_flags_ILFlag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/icons/flags/ILFlag */ \"./src/components/icons/flags/ILFlag.tsx\");\n/* harmony import */ var _components_icons_flags_ESFlag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/icons/flags/ESFlag */ \"./src/components/icons/flags/ESFlag.tsx\");\n\n\n\n\n\n\n\n\nconst localeRTLList = [\n    \"ar\",\n    \"he\"\n];\nfunction useIsRTL() {\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    if (locale && localeRTLList.includes(locale)) {\n        return {\n            isRTL: true,\n            alignLeft: \"right\",\n            alignRight: \"left\"\n        };\n    }\n    return {\n        isRTL: false,\n        alignLeft: \"left\",\n        alignRight: \"right\"\n    };\n}\nlet languageMenu = [\n    {\n        id: \"ar\",\n        name: \"عربى\",\n        value: \"ar\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_SAFlag__WEBPACK_IMPORTED_MODULE_2__.SAFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 23,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        id: \"zh\",\n        name: \"中国人\",\n        value: \"zh\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_CNFlag__WEBPACK_IMPORTED_MODULE_3__.CNFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 29,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        id: \"en\",\n        name: \"English\",\n        value: \"en\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_USFlag__WEBPACK_IMPORTED_MODULE_4__.USFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 35,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        id: \"de\",\n        name: \"Deutsch\",\n        value: \"de\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_DEFlag__WEBPACK_IMPORTED_MODULE_5__.DEFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 41,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        id: \"he\",\n        name: \"rעברית\",\n        value: \"he\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_ILFlag__WEBPACK_IMPORTED_MODULE_6__.ILFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 47,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        id: \"es\",\n        name: \"Espa\\xf1ol\",\n        value: \"es\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_flags_ESFlag__WEBPACK_IMPORTED_MODULE_7__.ESFlag, {\n            width: \"28px\",\n            height: \"28px\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\utils\\\\locals.tsx\",\n            lineNumber: 53,\n            columnNumber: 11\n        }, undefined)\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvbG9jYWxzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ2lCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUV6RCxNQUFNTyxnQkFBZ0I7SUFBQztJQUFNO0NBQUs7QUFDM0IsU0FBU0M7SUFDZCxNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHVCxzREFBU0E7SUFDNUIsSUFBSVMsVUFBVUYsY0FBY0csUUFBUSxDQUFDRCxTQUFTO1FBQzVDLE9BQU87WUFBRUUsT0FBTztZQUFNQyxXQUFXO1lBQVNDLFlBQVk7UUFBTztJQUMvRDtJQUNBLE9BQU87UUFBRUYsT0FBTztRQUFPQyxXQUFXO1FBQVFDLFlBQVk7SUFBUTtBQUNoRTtBQUVPLElBQUlDLGVBQWU7SUFDeEI7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsb0JBQU0sOERBQUNqQixrRUFBTUE7WUFBQ2tCLE9BQU07WUFBT0MsUUFBTzs7Ozs7O0lBQ3BDO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsb0JBQU0sOERBQUNoQixrRUFBTUE7WUFBQ2lCLE9BQU07WUFBT0MsUUFBTzs7Ozs7O0lBQ3BDO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsb0JBQU0sOERBQUNmLGtFQUFNQTtZQUFDZ0IsT0FBTTtZQUFPQyxRQUFPOzs7Ozs7SUFDcEM7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxvQkFBTSw4REFBQ2Qsa0VBQU1BO1lBQUNlLE9BQU07WUFBT0MsUUFBTzs7Ozs7O0lBQ3BDO0lBQ0E7UUFDRUwsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsb0JBQU0sOERBQUNiLGtFQUFNQTtZQUFDYyxPQUFNO1lBQU9DLFFBQU87Ozs7OztJQUNwQztJQUNBO1FBQ0VMLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLG9CQUFNLDhEQUFDWixrRUFBTUE7WUFBQ2EsT0FBTTtZQUFPQyxRQUFPOzs7Ozs7SUFDcEM7Q0FDRCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vc3JjL3V0aWxzL2xvY2Fscy50c3g/YThmNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCB7IFNBRmxhZyB9IGZyb20gJ0AvY29tcG9uZW50cy9pY29ucy9mbGFncy9TQUZsYWcnO1xyXG5pbXBvcnQgeyBDTkZsYWcgfSBmcm9tICdAL2NvbXBvbmVudHMvaWNvbnMvZmxhZ3MvQ05GbGFnJztcclxuaW1wb3J0IHsgVVNGbGFnIH0gZnJvbSAnQC9jb21wb25lbnRzL2ljb25zL2ZsYWdzL1VTRmxhZyc7XHJcbmltcG9ydCB7IERFRmxhZyB9IGZyb20gJ0AvY29tcG9uZW50cy9pY29ucy9mbGFncy9ERUZsYWcnO1xyXG5pbXBvcnQgeyBJTEZsYWcgfSBmcm9tICdAL2NvbXBvbmVudHMvaWNvbnMvZmxhZ3MvSUxGbGFnJztcclxuaW1wb3J0IHsgRVNGbGFnIH0gZnJvbSAnQC9jb21wb25lbnRzL2ljb25zL2ZsYWdzL0VTRmxhZyc7XHJcblxyXG5jb25zdCBsb2NhbGVSVExMaXN0ID0gWydhcicsICdoZSddO1xyXG5leHBvcnQgZnVuY3Rpb24gdXNlSXNSVEwoKSB7XHJcbiAgY29uc3QgeyBsb2NhbGUgfSA9IHVzZVJvdXRlcigpO1xyXG4gIGlmIChsb2NhbGUgJiYgbG9jYWxlUlRMTGlzdC5pbmNsdWRlcyhsb2NhbGUpKSB7XHJcbiAgICByZXR1cm4geyBpc1JUTDogdHJ1ZSwgYWxpZ25MZWZ0OiAncmlnaHQnLCBhbGlnblJpZ2h0OiAnbGVmdCcgfTtcclxuICB9XHJcbiAgcmV0dXJuIHsgaXNSVEw6IGZhbHNlLCBhbGlnbkxlZnQ6ICdsZWZ0JywgYWxpZ25SaWdodDogJ3JpZ2h0JyB9O1xyXG59XHJcblxyXG5leHBvcnQgbGV0IGxhbmd1YWdlTWVudSA9IFtcclxuICB7XHJcbiAgICBpZDogJ2FyJyxcclxuICAgIG5hbWU6ICfYudix2KjZiScsXHJcbiAgICB2YWx1ZTogJ2FyJyxcclxuICAgIGljb246IDxTQUZsYWcgd2lkdGg9XCIyOHB4XCIgaGVpZ2h0PVwiMjhweFwiIC8+LFxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICd6aCcsXHJcbiAgICBuYW1lOiAn5Lit5Zu95Lq6JyxcclxuICAgIHZhbHVlOiAnemgnLFxyXG4gICAgaWNvbjogPENORmxhZyB3aWR0aD1cIjI4cHhcIiBoZWlnaHQ9XCIyOHB4XCIgLz4sXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2VuJyxcclxuICAgIG5hbWU6ICdFbmdsaXNoJyxcclxuICAgIHZhbHVlOiAnZW4nLFxyXG4gICAgaWNvbjogPFVTRmxhZyB3aWR0aD1cIjI4cHhcIiBoZWlnaHQ9XCIyOHB4XCIgLz4sXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2RlJyxcclxuICAgIG5hbWU6ICdEZXV0c2NoJyxcclxuICAgIHZhbHVlOiAnZGUnLFxyXG4gICAgaWNvbjogPERFRmxhZyB3aWR0aD1cIjI4cHhcIiBoZWlnaHQ9XCIyOHB4XCIgLz4sXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2hlJyxcclxuICAgIG5hbWU6ICdy16LXkdeo15nXqicsXHJcbiAgICB2YWx1ZTogJ2hlJyxcclxuICAgIGljb246IDxJTEZsYWcgd2lkdGg9XCIyOHB4XCIgaGVpZ2h0PVwiMjhweFwiIC8+LFxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdlcycsXHJcbiAgICBuYW1lOiAnRXNwYcOxb2wnLFxyXG4gICAgdmFsdWU6ICdlcycsXHJcbiAgICBpY29uOiA8RVNGbGFnIHdpZHRoPVwiMjhweFwiIGhlaWdodD1cIjI4cHhcIiAvPixcclxuICB9LFxyXG5dO1xyXG4iXSwibmFtZXMiOlsidXNlUm91dGVyIiwiU0FGbGFnIiwiQ05GbGFnIiwiVVNGbGFnIiwiREVGbGFnIiwiSUxGbGFnIiwiRVNGbGFnIiwibG9jYWxlUlRMTGlzdCIsInVzZUlzUlRMIiwibG9jYWxlIiwiaW5jbHVkZXMiLCJpc1JUTCIsImFsaWduTGVmdCIsImFsaWduUmlnaHQiLCJsYW5ndWFnZU1lbnUiLCJpZCIsIm5hbWUiLCJ2YWx1ZSIsImljb24iLCJ3aWR0aCIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/utils/locals.tsx\n");

/***/ }),

/***/ "./src/utils/use-swiper-ref.ts":
/*!*************************************!*\
  !*** ./src/utils/use-swiper-ref.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useSwiperRef = ()=>{\n    const [wrapper, setWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (ref.current) {\n            setWrapper(ref.current);\n        }\n    }, []);\n    return [\n        wrapper,\n        ref\n    ];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSwiperRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvdXNlLXN3aXBlci1yZWYudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBRXBELE1BQU1HLGVBQWU7SUFJbkIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdMLCtDQUFRQTtJQUN0QyxNQUFNTSxNQUFNTCw2Q0FBTUEsQ0FBSTtJQUV0QkMsZ0RBQVNBLENBQUM7UUFDUixJQUFJSSxJQUFJQyxPQUFPLEVBQUU7WUFDZkYsV0FBV0MsSUFBSUMsT0FBTztRQUN4QjtJQUNGLEdBQUcsRUFBRTtJQUVMLE9BQU87UUFBQ0g7UUFBU0U7S0FBSTtBQUN2QjtBQUVBLGlFQUFlSCxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vc3JjL3V0aWxzL3VzZS1zd2lwZXItcmVmLnRzP2MwOGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5cclxuY29uc3QgdXNlU3dpcGVyUmVmID0gPFQgZXh0ZW5kcyBIVE1MRWxlbWVudD4oKTogW1xyXG4gIFQgfCB1bmRlZmluZWQsXHJcbiAgUmVhY3QuUmVmPFQ+XHJcbl0gPT4ge1xyXG4gIGNvbnN0IFt3cmFwcGVyLCBzZXRXcmFwcGVyXSA9IHVzZVN0YXRlPFQ+KCk7XHJcbiAgY29uc3QgcmVmID0gdXNlUmVmPFQ+KG51bGwpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHJlZi5jdXJyZW50KSB7XHJcbiAgICAgIHNldFdyYXBwZXIocmVmLmN1cnJlbnQpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIFt3cmFwcGVyLCByZWZdO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgdXNlU3dpcGVyUmVmO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VTd2lwZXJSZWYiLCJ3cmFwcGVyIiwic2V0V3JhcHBlciIsInJlZiIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/utils/use-swiper-ref.ts\n");

/***/ })

};
;