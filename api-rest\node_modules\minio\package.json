{"name": "minio", "version": "8.0.5", "description": "S3 Compatible Cloud Storage client", "main": "./dist/main/minio.js", "module": "./dist/esm/minio.mjs", "scripts": {"prepare": "husky install", "tsc": "tsc", "type-check": "tsc --noEmit --emitDeclarationOnly false", "build": "node build.mjs", "test": "mocha", "lint": "eslint --ext js,mjs,cjs,ts ./", "lint-fix": "eslint --ext js,mjs,cjs,ts ./ --fix", "prepublishOnly": "npm run build", "functional": "mocha tests/functional/functional-tests.js", "format": "prettier -w .", "format-check": "prettier --list-different .", "lint-staged": "lint-staged"}, "exports": {".": {"require": "./dist/main/minio.js", "types": "./types/esm/minio.d.ts", "default": "./dist/esm/minio.mjs"}, "./dist/main/internal/*": null, "./dist/main/*": {"require": "./dist/main/*", "default": null}, "./dist/esm/internal/*": null, "./dist/esm/*": {"import": "./dist/esm/*", "default": null}, "./package.json": "./package.json"}, "files": ["package.json", "./dist/", "./src/", "./types/", "LICENSE", "README.md", "README_zh_CN.md", "MAINTAINERS.md"], "prettier": {"printWidth": 120, "singleQuote": true, "endOfLine": "lf", "trailingComma": "all", "semi": false}, "lint-staged": {"*.json": ["prettier --write"], "*.{js,cjs,mjs,ts}": ["eslint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/minio/minio-js.git"}, "author": {"name": "MinIO, Inc.", "url": "https://min.io"}, "engines": {"node": "^16 || ^18 || >=20"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/minio/minio-js/issues", "mail": ""}, "homepage": "https://github.com/minio/minio-js#readme", "dependencies": {"async": "^3.2.4", "block-stream2": "^2.1.0", "browser-or-node": "^2.1.1", "buffer-crc32": "^1.0.0", "eventemitter3": "^5.0.1", "fast-xml-parser": "^4.4.1", "ipaddr.js": "^2.0.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "query-string": "^7.1.3", "stream-json": "^1.8.0", "through2": "^4.0.2", "web-encoding": "^1.1.5", "xml2js": "^0.5.0 || ^0.6.2"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/plugin-transform-modules-commonjs": "^7.21.5", "@babel/preset-env": "^7.21.5", "@babel/preset-typescript": "^7.21.5", "@babel/register": "^7.21.0", "@nodelib/fs.walk": "^1.2.8", "@types/async": "^3.2.20", "@types/block-stream2": "^2.1.2", "@types/chai": "^4.3.11", "@types/chai-as-promised": "^7.1.8", "@types/lodash": "^4.14.194", "@types/mime-types": "^2.1.1", "@types/node": "^20.1.0", "@types/stream-json": "^1.7.5", "@types/through2": "^2.0.38", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "@upleveled/babel-plugin-remove-node-prefix": "^1.0.5", "babel-plugin-replace-import-extension": "^1.1.3", "babel-plugin-transform-replace-expressions": "^0.2.0", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "dotenv": "^16.0.3", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "mocha": "^10.2.0", "mocha-steps": "^1.3.0", "nock": "^13.3.1", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "split-file": "^2.3.0", "superagent": "^8.0.1", "typescript": "^5.0.4", "uuid": "^9.0.0"}, "keywords": ["api", "amazon", "minio", "cloud", "s3", "storage"]}