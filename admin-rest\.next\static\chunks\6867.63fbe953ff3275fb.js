"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6867],{96867:function(e,n,t){t.r(n);var s=t(85893),o=t(71421),u=t(75814),a=t(9140),r=t(14388);n.default=()=>{let{mutate:e,isLoading:n}=(0,r.YL)(),{data:t}=(0,u.X9)(),{closeModal:i}=(0,u.SO)();return(0,s.jsx)(o.Z,{onCancel:i,onDelete:function(){try{e({id:t}),i()}catch(e){i(),(0,a.e)(e)}},deleteBtnLoading:n})}},14388:function(e,n,t){t.d(n,{ng:function(){return useCreateRefunReasonMutation},YL:function(){return useDeleteRefundReasonMutation},Rz:function(){return useRefundReasonQuery},Ff:function(){return useRefundReasonsQuery},o_:function(){return useUpdateRefundReasonMutation}});var s=t(11163),o=t.n(s),u=t(88767),a=t(22920),r=t(5233),i=t(97514),l=t(47869),c=t(28597),d=t(55191),f=t(3737);let R={...(0,d.h)(l.P.REFUND_REASONS),paginated:e=>{let{name:n,...t}=e;return f.eN.get(l.P.REFUND_REASONS,{searchJoin:"and",...t,search:f.eN.formatSearchParams({name:n})})}};var g=t(93345);let useCreateRefunReasonMutation=()=>{let e=(0,u.useQueryClient)(),{t:n}=(0,r.$G)(),t=(0,s.useRouter)();return(0,u.useMutation)(R.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.refundReasons.list):i.Z.refundReasons.list;await o().push(e,void 0,{locale:g.Config.defaultLanguage}),a.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.REFUND_REASONS)},onError:e=>{var t;a.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useDeleteRefundReasonMutation=()=>{let e=(0,u.useQueryClient)(),{t:n}=(0,r.$G)();return(0,u.useMutation)(R.delete,{onSuccess:()=>{a.Am.success(n("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.REFUND_REASONS)},onError:e=>{var t;a.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateRefundReasonMutation=()=>{let{t:e}=(0,r.$G)(),n=(0,s.useRouter)(),t=(0,u.useQueryClient)();return(0,u.useMutation)(R.update,{onSuccess:async t=>{let s=n.query.shop?"/".concat(n.query.shop).concat(i.Z.refundReasons.list):i.Z.refundReasons.list;await n.push("".concat(s,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:g.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.REFUND_REASONS)},onError:n=>{var t;a.Am.error(e("common:".concat(null==n?void 0:null===(t=n.response)||void 0===t?void 0:t.data.message)))}})},useRefundReasonQuery=e=>{let{slug:n,language:t}=e,{data:s,error:o,isLoading:a}=(0,u.useQuery)([l.P.REFUND_REASONS,{slug:n,language:t}],()=>R.get({slug:n,language:t}));return{refundReason:s,error:o,loading:a}},useRefundReasonsQuery=e=>{var n;let{data:t,error:s,isLoading:o}=(0,u.useQuery)([l.P.REFUND_REASONS,e],e=>{let{queryKey:n,pageParam:t}=e;return R.paginated(Object.assign({},n[1],t))},{keepPreviousData:!0});return{refundReasons:null!==(n=null==t?void 0:t.data)&&void 0!==n?n:[],paginatorInfo:(0,c.Q)(t),error:s,loading:o}}},9140:function(e,n,t){t.d(n,{e:function(){return getErrorMessage}});var s=t(11163),o=t.n(s),u=t(31955);function getErrorMessage(e){let n={message:"",validation:[]};if(e.graphQLErrors)for(let t of e.graphQLErrors){if(t.extensions&&"validation"===t.extensions.category)return n.message=t.message,n.validation=t.extensions.validation,n;t.extensions&&"authorization"===t.extensions.category&&(u.Z.remove("auth_token"),u.Z.remove("auth_permissions"),o().push("/"))}return n.message=e.message,n}}}]);