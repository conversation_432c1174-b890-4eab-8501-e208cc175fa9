services:
  # Development overrides
  api-rest:
    environment:
      NODE_ENV: development
    volumes:
      - ./api-rest:/app
      - /app/node_modules
    command: npm run start:dev

  admin-rest:
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_REST_API_ENDPOINT: http://localhost/api
    volumes:
      - ./admin-rest:/app
      - /app/node_modules
      - /app/.next
    command: yarn dev

  shop:
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_REST_API_ENDPOINT: http://localhost/api
    volumes:
      - ./shop:/app
      - /app/node_modules
      - /app/.next
    command: yarn dev:rest
