"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_banners_banner-without-slider_tsx"],{

/***/ "./src/components/banners/banner-without-slider.tsx":
/*!**********************************************************!*\
  !*** ./src/components/banners/banner-without-slider.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _components_ui_search_search_with_suggestion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/search/search-with-suggestion */ \"./src/components/ui/search/search-with-suggestion.tsx\");\n\n\n\n\n\nconst BannerWithoutSlider = (param)=>{\n    let { banners, layout } = param;\n    var _banners__image, _banners_, _banners_1, _banners_2, _banners_3;\n    var _banners__image_original, _banners__title;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"relative hidden lg:block\", {\n            \"!block\": layout === \"minimal\"\n        }),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"relative h-screen w-full\", {\n                \"max-h-140\": layout === \"standard\",\n                \"max-h-[320px] md:max-h-[680px]\": layout === \"minimal\"\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                    className: \"h-full min-h-140 w-full object-cover\",\n                    src: (_banners__image_original = (_banners_ = banners[0]) === null || _banners_ === void 0 ? void 0 : (_banners__image = _banners_.image) === null || _banners__image === void 0 ? void 0 : _banners__image.original) !== null && _banners__image_original !== void 0 ? _banners__image_original : _lib_placeholders__WEBPACK_IMPORTED_MODULE_3__.productPlaceholder,\n                    alt: (_banners__title = (_banners_1 = banners[0]) === null || _banners_1 === void 0 ? void 0 : _banners_1.title) !== null && _banners__title !== void 0 ? _banners__title : \"\",\n                    fill: true,\n                    sizes: \"(max-width: 768px) 100vw\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"absolute inset-0 flex w-full flex-col items-center justify-center p-5 text-center md:px-20 lg:space-y-10\", {\n                        \"mt-24 space-y-5 md:mt-0 md:!space-y-8\": layout === \"minimal\"\n                    }),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text-2xl font-bold tracking-tight text-heading md:text-3xl lg:text-4xl xl:text-5xl\", {\n                                \"!text-accent\": layout === \"minimal\"\n                            }),\n                            children: (_banners_2 = banners[0]) === null || _banners_2 === void 0 ? void 0 : _banners_2.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-body md:text-base xl:text-lg\",\n                            children: (_banners_3 = banners[0]) === null || _banners_3 === void 0 ? void 0 : _banners_3.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-3xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_search_search_with_suggestion__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                label: \"search\",\n                                className: \"hidden lg:block\",\n                                variant: \"with-shadow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\banners\\\\banner-without-slider.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = BannerWithoutSlider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BannerWithoutSlider);\nvar _c;\n$RefreshReg$(_c, \"BannerWithoutSlider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/banners/banner-without-slider.tsx\n"));

/***/ })

}]);