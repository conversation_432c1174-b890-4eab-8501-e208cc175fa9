(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1681,2036],{77238:function(e,n,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shippings/edit/[id]",function(){return i(84368)}])},84368:function(e,n,i){"use strict";i.r(n),i.d(n,{__N_SSP:function(){return l},default:function(){return UpdateShippingPage}});var t=i(85893),s=i(97670),d=i(11163),r=i(92941),a=i(45957),u=i(55846),o=i(64179),p=i(5233),l=!0;function UpdateShippingPage(){let{query:e}=(0,d.useRouter)(),{t:n}=(0,p.$G)(),{data:i,isLoading:s,error:l}=(0,o.f1)(e.id);return s?(0,t.jsx)(u.Z,{text:n("common:text-loading")}):l?(0,t.jsx)(a.Z,{message:l.message}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsxs)("h1",{className:"text-lg font-semibold text-heading",children:[n("form:form-title-update-shipping")," #",null==i?void 0:i.id]})}),(0,t.jsx)(r.Z,{initialValues:i})]})}UpdateShippingPage.Layout=s.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,9494,5535,8186,1285,1631,8118,9774,2888,179],function(){return e(e.s=77238)}),_N_E=e.O()}]);