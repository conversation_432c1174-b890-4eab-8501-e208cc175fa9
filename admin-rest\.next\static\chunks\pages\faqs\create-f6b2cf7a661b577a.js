(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2632,2007,2036],{38020:function(e,l,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/faqs/create",function(){return r(40584)}])},97670:function(e,l,r){"use strict";r.r(l);var t=r(85893),s=r(78985),i=r(79362),a=r(8144),n=r(74673),d=r(99494),o=r(5233),u=r(1631),c=r(11163),h=r(48583),x=r(93967),f=r.n(x),p=r(30824),b=r(62964);let SidebarItemMap=e=>{let{menuItems:l}=e,{t:r}=(0,o.$G)(),[s,a]=(0,h.KO)(i.Hf),{childMenu:n}=l,{width:d}=(0,b.Z)();return(0,t.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:l,label:a,icon:n,childMenu:o}=e;return(0,t.jsx)(u.Z,{href:l,label:r(a),icon:n,childMenu:o,miniSidebar:s&&d>=i.h2},a)})})},SideBarGroup=()=>{var e;let{t:l}=(0,o.$G)(),[r,s]=(0,h.KO)(i.Hf),a=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(a),{width:u}=(0,b.Z)();return(0,t.jsx)(t.Fragment,{children:null==n?void 0:n.map((e,s)=>{var n;return(0,t.jsxs)("div",{className:f()("flex flex-col px-5",r&&u>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&u>=i.h2?"hidden":""),children:l(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:a[e]})]},s)})})};l.default=e=>{let{children:l}=e,{locale:r}=(0,c.useRouter)(),[d,o]=(0,h.KO)(i.Hf),[u]=(0,h.KO)(i.GH),[x]=(0,h.KO)(i.W4),{width:m}=(0,b.Z)();return(0,t.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,t.jsx)(s.Z,{}),(0,t.jsx)(n.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",m>=i.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-20",d&&m>=i.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,t.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",m>=i.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&m>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,t.jsx)(a.Z,{})]})]})]})}},40584:function(e,l,r){"use strict";r.r(l),r.d(l,{__N_SSG:function(){return d},default:function(){return CreateFAQsPage}});var t=r(85893),s=r(40386),i=r(97670),a=r(5233),n=r(16203),d=!0;function CreateFAQsPage(){let{t:e}=(0,a.$G)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsxs)("h1",{className:"text-lg font-semibold text-heading",children:[e("text-non-translated-title")," ",e("text-faq")]})}),(0,t.jsx)(s.Z,{})]})}CreateFAQsPage.authenticate={permissions:n.M$},CreateFAQsPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,9494,5535,8186,1285,1631,386,9774,2888,179],function(){return e(e.s=38020)}),_N_E=e.O()}]);