(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2937],{76095:function(t,e,r){var n=r(48764).lW;/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */"undefined"!=typeof self&&self,t.exports=function(t){var e={};function __nested_webpack_require_697__(r){if(e[r])return e[r].exports;var n=e[r]={i:r,l:!1,exports:{}};return t[r].call(n.exports,n,n.exports,__nested_webpack_require_697__),n.l=!0,n.exports}return __nested_webpack_require_697__.m=t,__nested_webpack_require_697__.c=e,__nested_webpack_require_697__.d=function(t,e,r){__nested_webpack_require_697__.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},__nested_webpack_require_697__.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return __nested_webpack_require_697__.d(e,"a",e),e},__nested_webpack_require_697__.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},__nested_webpack_require_697__.p="",__nested_webpack_require_697__(__nested_webpack_require_697__.s=109)}([function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(17),o=r(18),i=r(19),l=r(45),a=r(46),s=r(47),u=r(48),f=r(49),p=r(12),d=r(32),h=r(33),y=r(31),b=r(1),v={Scope:b.Scope,create:b.create,find:b.find,query:b.query,register:b.register,Container:n.default,Format:o.default,Leaf:i.default,Embed:u.default,Scroll:l.default,Block:s.default,Inline:a.default,Text:f.default,Attributor:{Attribute:p.default,Class:d.default,Style:h.default,Store:y.default}};e.default=v},function(t,e,r){"use strict";var n,o,i,l=this&&this.__extends||(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var a=function(t){function ParchmentError(e){var r=this;return e="[Parchment] "+e,(r=t.call(this,e)||this).message=e,r.name=r.constructor.name,r}return l(ParchmentError,t),ParchmentError}(Error);e.ParchmentError=a;var s={},u={},f={},p={};function query(t,e){if(void 0===e&&(e=i.ANY),"string"==typeof t)r=p[t]||s[t];else if(t instanceof Text||t.nodeType===Node.TEXT_NODE)r=p.text;else if("number"==typeof t)t&i.LEVEL&i.BLOCK?r=p.block:t&i.LEVEL&i.INLINE&&(r=p.inline);else if(t instanceof HTMLElement){var r,n=(t.getAttribute("class")||"").split(/\s+/);for(var o in n)if(r=u[n[o]])break;r=r||f[t.tagName]}return null==r?null:e&i.LEVEL&r.scope&&e&i.TYPE&r.scope?r:null}e.DATA_KEY="__blot",(n=i=e.Scope||(e.Scope={}))[n.TYPE=3]="TYPE",n[n.LEVEL=12]="LEVEL",n[n.ATTRIBUTE=13]="ATTRIBUTE",n[n.BLOT=14]="BLOT",n[n.INLINE=7]="INLINE",n[n.BLOCK=11]="BLOCK",n[n.BLOCK_BLOT=10]="BLOCK_BLOT",n[n.INLINE_BLOT=6]="INLINE_BLOT",n[n.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",n[n.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",n[n.ANY=15]="ANY",e.create=function(t,e){var r=query(t);if(null==r)throw new a("Unable to create "+t+" blot");var n=t instanceof Node||t.nodeType===Node.TEXT_NODE?t:r.create(e);return new r(n,e)},e.find=function find(t,r){return(void 0===r&&(r=!1),null==t)?null:null!=t[e.DATA_KEY]?t[e.DATA_KEY].blot:r?find(t.parentNode,r):null},e.query=query,e.register=function register(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(t.length>1)return t.map(function(t){return register(t)});var r=t[0];if("string"!=typeof r.blotName&&"string"!=typeof r.attrName)throw new a("Invalid definition");if("abstract"===r.blotName)throw new a("Cannot register abstract class");return p[r.blotName||r.attrName]=r,"string"==typeof r.keyName?s[r.keyName]=r:(null!=r.className&&(u[r.className]=r),null!=r.tagName&&(Array.isArray(r.tagName)?r.tagName=r.tagName.map(function(t){return t.toUpperCase()}):r.tagName=r.tagName.toUpperCase(),(Array.isArray(r.tagName)?r.tagName:[r.tagName]).forEach(function(t){(null==f[t]||null==r.className)&&(f[t]=r)}))),r}},function(t,e,r){var n=r(51),o=r(11),i=r(3),l=r(20),Delta=function(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]};Delta.prototype.insert=function(t,e){var r={};return 0===t.length?this:(r.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r))},Delta.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},Delta.prototype.retain=function(t,e){if(t<=0)return this;var r={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r)},Delta.prototype.push=function(t){var e=this.ops.length,r=this.ops[e-1];if(t=i(!0,{},t),"object"==typeof r){if("number"==typeof t.delete&&"number"==typeof r.delete)return this.ops[e-1]={delete:r.delete+t.delete},this;if("number"==typeof r.delete&&null!=t.insert&&(e-=1,"object"!=typeof(r=this.ops[e-1])))return this.ops.unshift(t),this;if(o(t.attributes,r.attributes)){if("string"==typeof t.insert&&"string"==typeof r.insert)return this.ops[e-1]={insert:r.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof r.retain)return this.ops[e-1]={retain:r.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},Delta.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},Delta.prototype.filter=function(t){return this.ops.filter(t)},Delta.prototype.forEach=function(t){this.ops.forEach(t)},Delta.prototype.map=function(t){return this.ops.map(t)},Delta.prototype.partition=function(t){var e=[],r=[];return this.forEach(function(n){(t(n)?e:r).push(n)}),[e,r]},Delta.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},Delta.prototype.changeLength=function(){return this.reduce(function(t,e){return e.insert?t+l.length(e):e.delete?t-e.delete:t},0)},Delta.prototype.length=function(){return this.reduce(function(t,e){return t+l.length(e)},0)},Delta.prototype.slice=function(t,e){t=t||0,"number"!=typeof e&&(e=1/0);for(var r,n=[],o=l.iterator(this.ops),i=0;i<e&&o.hasNext();)i<t?r=o.next(t-i):(r=o.next(e-i),n.push(r)),i+=l.length(r);return new Delta(n)},Delta.prototype.compose=function(t){var e=l.iterator(this.ops),r=l.iterator(t.ops),n=[],i=r.peek();if(null!=i&&"number"==typeof i.retain&&null==i.attributes){for(var a=i.retain;"insert"===e.peekType()&&e.peekLength()<=a;)a-=e.peekLength(),n.push(e.next());i.retain-a>0&&r.next(i.retain-a)}for(var s=new Delta(n);e.hasNext()||r.hasNext();)if("insert"===r.peekType())s.push(r.next());else if("delete"===e.peekType())s.push(e.next());else{var u=Math.min(e.peekLength(),r.peekLength()),f=e.next(u),p=r.next(u);if("number"==typeof p.retain){var d={};"number"==typeof f.retain?d.retain=u:d.insert=f.insert;var h=l.attributes.compose(f.attributes,p.attributes,"number"==typeof f.retain);if(h&&(d.attributes=h),s.push(d),!r.hasNext()&&o(s.ops[s.ops.length-1],d)){var y=new Delta(e.rest());return s.concat(y).chop()}}else"number"==typeof p.delete&&"number"==typeof f.retain&&s.push(p)}return s.chop()},Delta.prototype.concat=function(t){var e=new Delta(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e},Delta.prototype.diff=function(t,e){if(this.ops===t.ops)return new Delta;var r=[this,t].map(function(e){return e.map(function(r){if(null!=r.insert)return"string"==typeof r.insert?r.insert:"\x00";throw Error("diff() called "+(e===t?"on":"with")+" non-document")}).join("")}),i=new Delta,a=n(r[0],r[1],e),s=l.iterator(this.ops),u=l.iterator(t.ops);return a.forEach(function(t){for(var e=t[1].length;e>0;){var r=0;switch(t[0]){case n.INSERT:r=Math.min(u.peekLength(),e),i.push(u.next(r));break;case n.DELETE:r=Math.min(e,s.peekLength()),s.next(r),i.delete(r);break;case n.EQUAL:r=Math.min(s.peekLength(),u.peekLength(),e);var a=s.next(r),f=u.next(r);o(a.insert,f.insert)?i.retain(r,l.attributes.diff(a.attributes,f.attributes)):i.push(f).delete(r)}e-=r}}),i.chop()},Delta.prototype.eachLine=function(t,e){e=e||"\n";for(var r=l.iterator(this.ops),n=new Delta,o=0;r.hasNext();){if("insert"!==r.peekType())return;var i=r.peek(),a=l.length(i)-r.peekLength(),s="string"==typeof i.insert?i.insert.indexOf(e,a)-a:-1;if(s<0)n.push(r.next());else if(s>0)n.push(r.next(s));else{if(!1===t(n,r.next(1).attributes||{},o))return;o+=1,n=new Delta}}n.length()>0&&t(n,{},o)},Delta.prototype.transform=function(t,e){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);for(var r=l.iterator(this.ops),n=l.iterator(t.ops),o=new Delta;r.hasNext()||n.hasNext();)if("insert"===r.peekType()&&(e||"insert"!==n.peekType()))o.retain(l.length(r.next()));else if("insert"===n.peekType())o.push(n.next());else{var i=Math.min(r.peekLength(),n.peekLength()),a=r.next(i),s=n.next(i);if(a.delete)continue;s.delete?o.push(s):o.retain(i,l.attributes.transform(a.attributes,s.attributes,e))}return o.chop()},Delta.prototype.transformPosition=function(t,e){e=!!e;for(var r=l.iterator(this.ops),n=0;r.hasNext()&&n<=t;){var o=r.peekLength(),i=r.peekType();if(r.next(),"delete"===i){t-=Math.min(o,t-n);continue}"insert"===i&&(n<t||!e)&&(t+=o),n+=o}return t},t.exports=Delta},function(t,e){"use strict";var r=Object.prototype.hasOwnProperty,n=Object.prototype.toString,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,isArray=function(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===n.call(t)},isPlainObject=function(t){if(!t||"[object Object]"!==n.call(t))return!1;var e,o=r.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&r.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!o&&!i)return!1;for(e in t);return void 0===e||r.call(t,e)},setProperty=function(t,e){o&&"__proto__"===e.name?o(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},getProperty=function(t,e){if("__proto__"===e){if(!r.call(t,e))return;if(i)return i(t,e).value}return t[e]};t.exports=function extend(){var t,e,r,n,o,i,l=arguments[0],a=1,s=arguments.length,u=!1;for("boolean"==typeof l&&(u=l,l=arguments[1]||{},a=2),(null==l||"object"!=typeof l&&"function"!=typeof l)&&(l={});a<s;++a)if(t=arguments[a],null!=t)for(e in t)r=getProperty(l,e),l!==(n=getProperty(t,e))&&(u&&n&&(isPlainObject(n)||(o=isArray(n)))?(o?(o=!1,i=r&&isArray(r)?r:[]):i=r&&isPlainObject(r)?r:{},setProperty(l,{name:e,newValue:extend(u,i,n)})):void 0!==n&&setProperty(l,{name:e,newValue:n}));return l}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BlockEmbed=e.bubbleFormats=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(3)),i=_interopRequireDefault(r(2)),l=_interopRequireDefault(r(0)),a=_interopRequireDefault(r(16)),s=_interopRequireDefault(r(6)),u=_interopRequireDefault(r(7));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function BlockEmbed(){return _classCallCheck(this,BlockEmbed),_possibleConstructorReturn(this,(BlockEmbed.__proto__||Object.getPrototypeOf(BlockEmbed)).apply(this,arguments))}return _inherits(BlockEmbed,t),n(BlockEmbed,[{key:"attach",value:function(){_get(BlockEmbed.prototype.__proto__||Object.getPrototypeOf(BlockEmbed.prototype),"attach",this).call(this),this.attributes=new l.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new i.default().insert(this.value(),(0,o.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(t,e){var r=l.default.query(t,l.default.Scope.BLOCK_ATTRIBUTE);null!=r&&this.attributes.attribute(r,e)}},{key:"formatAt",value:function(t,e,r,n){this.format(r,n)}},{key:"insertAt",value:function(t,e,r){if("string"==typeof e&&e.endsWith("\n")){var n=l.default.create(p.blotName);this.parent.insertBefore(n,0===t?this:this.next),n.insertAt(0,e.slice(0,-1))}else _get(BlockEmbed.prototype.__proto__||Object.getPrototypeOf(BlockEmbed.prototype),"insertAt",this).call(this,t,e,r)}}]),BlockEmbed}(l.default.Embed);f.scope=l.default.Scope.BLOCK_BLOT;var p=function(t){function Block(t){_classCallCheck(this,Block);var e=_possibleConstructorReturn(this,(Block.__proto__||Object.getPrototypeOf(Block)).call(this,t));return e.cache={},e}return _inherits(Block,t),n(Block,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(l.default.Leaf).reduce(function(t,e){return 0===e.length()?t:t.insert(e.value(),bubbleFormats(e))},new i.default).insert("\n",bubbleFormats(this))),this.cache.delta}},{key:"deleteAt",value:function(t,e){_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"deleteAt",this).call(this,t,e),this.cache={}}},{key:"formatAt",value:function(t,e,r,n){e<=0||(l.default.query(r,l.default.Scope.BLOCK)?t+e===this.length()&&this.format(r,n):_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"formatAt",this).call(this,t,Math.min(e,this.length()-t-1),r,n),this.cache={})}},{key:"insertAt",value:function(t,e,r){if(null!=r)return _get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"insertAt",this).call(this,t,e,r);if(0!==e.length){var n=e.split("\n"),o=n.shift();o.length>0&&(t<this.length()-1||null==this.children.tail?_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"insertAt",this).call(this,Math.min(t,this.length()-1),o):this.children.tail.insertAt(this.children.tail.length(),o),this.cache={});var i=this;n.reduce(function(t,e){return(i=i.split(t,!0)).insertAt(0,e),e.length},t+o.length)}}},{key:"insertBefore",value:function(t,e){var r=this.children.head;_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"insertBefore",this).call(this,t,e),r instanceof a.default&&r.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(t,e){_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"moveChildren",this).call(this,t,e),this.cache={}}},{key:"optimize",value:function(t){_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"optimize",this).call(this,t),this.cache={}}},{key:"path",value:function(t){return _get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"path",this).call(this,t,!0)}},{key:"removeChild",value:function(t){_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"removeChild",this).call(this,t),this.cache={}}},{key:"split",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){var r=this.clone();return 0===t?(this.parent.insertBefore(r,this),this):(this.parent.insertBefore(r,this.next),r)}var n=_get(Block.prototype.__proto__||Object.getPrototypeOf(Block.prototype),"split",this).call(this,t,e);return this.cache={},n}}]),Block}(l.default.Block);function bubbleFormats(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return null==t?e:("function"==typeof t.formats&&(e=(0,o.default)(e,t.formats())),null==t.parent||"scroll"==t.parent.blotName||t.parent.statics.scope!==t.statics.scope)?e:bubbleFormats(t.parent,e)}p.blotName="block",p.tagName="P",p.defaultChild="break",p.allowedChildren=[s.default,l.default.Embed,u.default],e.bubbleFormats=bubbleFormats,e.BlockEmbed=f,e.default=p},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.overload=e.expandConfig=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}();r(50);var i=_interopRequireDefault(r(2)),l=_interopRequireDefault(r(14)),a=_interopRequireDefault(r(8)),s=_interopRequireDefault(r(9)),u=_interopRequireDefault(r(0)),f=r(15),p=_interopRequireDefault(f),d=_interopRequireDefault(r(3)),h=_interopRequireDefault(r(10)),y=_interopRequireDefault(r(34));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var b=(0,h.default)("quill"),v=function(){function Quill(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Quill),this.options=expandConfig(t,r),this.container=this.options.container,null==this.container)return b.error("Invalid Quill container",t);this.options.debug&&Quill.debug(this.options.debug);var n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new a.default,this.scroll=u.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new l.default(this.scroll),this.selection=new p.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(a.default.events.EDITOR_CHANGE,function(t){t===a.default.events.TEXT_CHANGE&&e.root.classList.toggle("ql-blank",e.editor.isBlank())}),this.emitter.on(a.default.events.SCROLL_UPDATE,function(t,r){var n=e.selection.lastRange,o=n&&0===n.length?n.index:void 0;modify.call(e,function(){return e.editor.update(null,r,o)},t)});var o=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>");this.setContents(o),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return o(Quill,null,[{key:"debug",value:function(t){!0===t&&(t="log"),h.default.level(t)}},{key:"find",value:function(t){return t.__quill||u.default.find(t)}},{key:"import",value:function(t){return null==this.imports[t]&&b.error("Cannot import "+t+". Are you sure it was registered?"),this.imports[t]}},{key:"register",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof t){var o=t.attrName||t.blotName;"string"==typeof o?this.register("formats/"+o,t,e):Object.keys(t).forEach(function(n){r.register(n,t[n],e)})}else null==this.imports[t]||n||b.warn("Overwriting "+t+" with",e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&"abstract"!==e.blotName?u.default.register(e):t.startsWith("modules")&&"function"==typeof e.register&&e.register()}}]),o(Quill,[{key:"addContainer",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){var r=t;(t=document.createElement("div")).classList.add(r)}return this.container.insertBefore(t,e),t}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(t,e,r){var n=this,o=_slicedToArray(overload(t,e,r),4);return t=o[0],e=o[1],modify.call(this,function(){return n.editor.deleteText(t,e)},r=o[3],t,-1*e)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}},{key:"focus",value:function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()}},{key:"format",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a.default.sources.API;return modify.call(this,function(){var n=r.getSelection(!0),o=new i.default;if(null==n)return o;if(u.default.query(t,u.default.Scope.BLOCK))o=r.editor.formatLine(n.index,n.length,_defineProperty({},t,e));else{if(0===n.length)return r.selection.format(t,e),o;o=r.editor.formatText(n.index,n.length,_defineProperty({},t,e))}return r.setSelection(n,a.default.sources.SILENT),o},n)}},{key:"formatLine",value:function(t,e,r,n,o){var i=this,l=void 0,a=_slicedToArray(overload(t,e,r,n,o),4);return t=a[0],e=a[1],l=a[2],modify.call(this,function(){return i.editor.formatLine(t,e,l)},o=a[3],t,0)}},{key:"formatText",value:function(t,e,r,n,o){var i=this,l=void 0,a=_slicedToArray(overload(t,e,r,n,o),4);return t=a[0],e=a[1],l=a[2],modify.call(this,function(){return i.editor.formatText(t,e,l)},o=a[3],t,0)}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=void 0;r="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length);var n=this.container.getBoundingClientRect();return{bottom:r.bottom-n.top,height:r.height,left:r.left-n.left,right:r.right-n.left,top:r.top-n.top,width:r.width}}},{key:"getContents",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,r=_slicedToArray(overload(t,e),2);return t=r[0],e=r[1],this.editor.getContents(t,e)}},{key:"getFormat",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}},{key:"getIndex",value:function(t){return t.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(t){return this.scroll.leaf(t)}},{key:"getLine",value:function(t){return this.scroll.line(t)}},{key:"getLines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}},{key:"getModule",value:function(t){return this.theme.modules[t]}},{key:"getSelection",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,r=_slicedToArray(overload(t,e),2);return t=r[0],e=r[1],this.editor.getText(t,e)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(t,e,r){var n=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Quill.sources.API;return modify.call(this,function(){return n.editor.insertEmbed(t,e,r)},o,t)}},{key:"insertText",value:function(t,e,r,n,o){var i=this,l=void 0,a=_slicedToArray(overload(t,0,r,n,o),4);return t=a[0],l=a[2],modify.call(this,function(){return i.editor.insertText(t,e,l)},o=a[3],t,e.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(t,e,r){this.clipboard.dangerouslyPasteHTML(t,e,r)}},{key:"removeFormat",value:function(t,e,r){var n=this,o=_slicedToArray(overload(t,e,r),4);return t=o[0],e=o[1],modify.call(this,function(){return n.editor.removeFormat(t,e)},r=o[3],t)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.default.sources.API;return modify.call(this,function(){t=new i.default(t);var r=e.getLength(),n=e.editor.deleteText(0,r),o=e.editor.applyDelta(t),l=o.ops[o.ops.length-1];return null!=l&&"string"==typeof l.insert&&"\n"===l.insert[l.insert.length-1]&&(e.editor.deleteText(e.getLength()-1,1),o.delete(1)),n.compose(o)},r)}},{key:"setSelection",value:function(t,e,r){if(null==t)this.selection.setRange(null,e||Quill.sources.API);else{var n=_slicedToArray(overload(t,e,r),4);t=n[0],e=n[1],r=n[3],this.selection.setRange(new f.Range(t,e),r),r!==a.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.default.sources.API,r=new i.default().insert(t);return this.setContents(r,e)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.default.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}},{key:"updateContents",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.default.sources.API;return modify.call(this,function(){return t=new i.default(t),e.editor.applyDelta(t,r)},r,!0)}}]),Quill}();function expandConfig(t,e){if((e=(0,d.default)(!0,{container:t,modules:{clipboard:!0,keyboard:!0,history:!0}},e)).theme&&e.theme!==v.DEFAULTS.theme){if(e.theme=v.import("themes/"+e.theme),null==e.theme)throw Error("Invalid theme "+e.theme+". Did you register it?")}else e.theme=y.default;var r=(0,d.default)(!0,{},e.theme.DEFAULTS);[r,e].forEach(function(t){t.modules=t.modules||{},Object.keys(t.modules).forEach(function(e){!0===t.modules[e]&&(t.modules[e]={})})});var n=Object.keys(r.modules).concat(Object.keys(e.modules)).reduce(function(t,e){var r=v.import("modules/"+e);return null==r?b.error("Cannot load "+e+" module. Are you sure you registered it?"):t[e]=r.DEFAULTS||{},t},{});return null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar}),e=(0,d.default)(!0,{},v.DEFAULTS,{modules:n},r,e),["bounds","container","scrollingContainer"].forEach(function(t){"string"==typeof e[t]&&(e[t]=document.querySelector(e[t]))}),e.modules=Object.keys(e.modules).reduce(function(t,r){return e.modules[r]&&(t[r]=e.modules[r]),t},{}),e}function modify(t,e,r,n){if(this.options.strict&&!this.isEnabled()&&e===a.default.sources.USER)return new i.default;var o=null==r?null:this.getSelection(),l=this.editor.delta,s=t();if(null!=o&&(!0===r&&(r=o.index),null==n?o=shiftRange(o,s,e):0!==n&&(o=shiftRange(o,r,n,e)),this.setSelection(o,a.default.sources.SILENT)),s.length()>0){var u,f,p=[a.default.events.TEXT_CHANGE,s,l,e];(u=this.emitter).emit.apply(u,[a.default.events.EDITOR_CHANGE].concat(p)),e!==a.default.sources.SILENT&&(f=this.emitter).emit.apply(f,p)}return s}function overload(t,e,r,o,i){var l={};return"number"==typeof t.index&&"number"==typeof t.length?("number"!=typeof e&&(i=o,o=r,r=e),e=t.length,t=t.index):"number"!=typeof e&&(i=o,o=r,r=e,e=0),(void 0===r?"undefined":n(r))==="object"?(l=r,i=o):"string"==typeof r&&(null!=o?l[r]=o:i=r),[t,e,l,i=i||a.default.sources.API]}function shiftRange(t,e,r,n){if(null==t)return null;var o=void 0,l=void 0;if(e instanceof i.default){var s=_slicedToArray([t.index,t.index+t.length].map(function(t){return e.transformPosition(t,n!==a.default.sources.USER)}),2);o=s[0],l=s[1]}else{var u=_slicedToArray([t.index,t.index+t.length].map(function(t){return t<e||t===e&&n===a.default.sources.USER?t:r>=0?t+r:Math.max(e,t+r)}),2);o=u[0],l=u[1]}return new f.Range(o,l-o)}v.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},v.events=a.default.events,v.sources=a.default.sources,v.version="1.3.7",v.imports={delta:i.default,parchment:u.default,"core/module":s.default,"core/theme":y.default},e.expandConfig=expandConfig,e.overload=overload,e.default=v},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(7)),i=_interopRequireDefault(r(0));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function Inline(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Inline),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Inline.__proto__||Object.getPrototypeOf(Inline)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Inline,t),n(Inline,[{key:"formatAt",value:function(t,e,r,n){if(0>Inline.compare(this.statics.blotName,r)&&i.default.query(r,i.default.Scope.BLOT)){var o=this.isolate(t,e);n&&o.wrap(r,n)}else _get(Inline.prototype.__proto__||Object.getPrototypeOf(Inline.prototype),"formatAt",this).call(this,t,e,r,n)}},{key:"optimize",value:function(t){if(_get(Inline.prototype.__proto__||Object.getPrototypeOf(Inline.prototype),"optimize",this).call(this,t),this.parent instanceof Inline&&Inline.compare(this.statics.blotName,this.parent.statics.blotName)>0){var e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}}],[{key:"compare",value:function(t,e){var r=Inline.order.indexOf(t),n=Inline.order.indexOf(e);return r>=0||n>=0?r-n:t===e?0:t<e?-1:1}}]),Inline}(i.default.Inline);l.allowedChildren=[l,i.default.Embed,o.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(t){function TextBlot(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,TextBlot),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(TextBlot.__proto__||Object.getPrototypeOf(TextBlot)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(TextBlot,t),TextBlot}(((n=r(0))&&n.__esModule?n:{default:n}).default.Text);e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(54));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var i=(0,_interopRequireDefault(r(10)).default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(t){document.addEventListener(t,function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(t){if(t.__quill&&t.__quill.emitter){var r;(r=t.__quill.emitter).handleDOM.apply(r,e)}})})});var l=function(t){function Emitter(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Emitter);var t=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Emitter.__proto__||Object.getPrototypeOf(Emitter)).call(this));return t.listeners={},t.on("error",i.error),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Emitter,t),n(Emitter,[{key:"emit",value:function(){i.log.apply(i,arguments),_get(Emitter.prototype.__proto__||Object.getPrototypeOf(Emitter.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];(this.listeners[t.type]||[]).forEach(function(e){var n=e.node,o=e.handler;(t.target===n||n.contains(t.target))&&o.apply(void 0,[t].concat(r))})}},{key:"listenDOM",value:function(t,e,r){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:r})}}]),Emitter}(o.default);l.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},l.sources={API:"api",SILENT:"silent",USER:"user"},e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var Module=function Module(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,Module),this.quill=t,this.options=e};Module.DEFAULTS={},e.default=Module},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=["error","warn","log","info"],o="warn";function debug(t){if(n.indexOf(t)<=n.indexOf(o)){for(var e,r=arguments.length,i=Array(r>1?r-1:0),l=1;l<r;l++)i[l-1]=arguments[l];(e=console)[t].apply(e,i)}}function namespace(t){return n.reduce(function(e,r){return e[r]=debug.bind(console,r,t),e},{})}debug.level=namespace.level=function(t){o=t},e.default=namespace},function(t,e,r){var n=Array.prototype.slice,o=r(52),i=r(53),l=t.exports=function(t,e,r){return r||(r={}),t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():t&&e&&("object"==typeof t||"object"==typeof e)?function(t,e,r){if(null==t||null==e||t.prototype!==e.prototype)return!1;if(i(t))return!!i(e)&&l(t=n.call(t),e=n.call(e),r);if(isBuffer(t)){if(!isBuffer(e)||t.length!==e.length)return!1;for(a=0;a<t.length;a++)if(t[a]!==e[a])return!1;return!0}try{var a,s,u=o(t),f=o(e)}catch(t){return!1}if(u.length!=f.length)return!1;for(u.sort(),f.sort(),a=u.length-1;a>=0;a--)if(u[a]!=f[a])return!1;for(a=u.length-1;a>=0;a--)if(!l(t[s=u[a]],e[s],r))return!1;return typeof t==typeof e}(t,e,r):r.strict?t===e:t==e)};function isBuffer(t){return!!t&&"object"==typeof t&&"number"==typeof t.length&&"function"==typeof t.copy&&"function"==typeof t.slice&&(!(t.length>0)||"number"==typeof t[0])}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),o=function(){function Attributor(t,e,r){void 0===r&&(r={}),this.attrName=t,this.keyName=e;var o=n.Scope.TYPE&n.Scope.ATTRIBUTE;null!=r.scope?this.scope=r.scope&n.Scope.LEVEL|o:this.scope=n.Scope.ATTRIBUTE,null!=r.whitelist&&(this.whitelist=r.whitelist)}return Attributor.keys=function(t){return[].map.call(t.attributes,function(t){return t.name})},Attributor.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},Attributor.prototype.canAdd=function(t,e){return null!=n.query(t,n.Scope.BLOT&(this.scope|n.Scope.TYPE))&&(null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1))},Attributor.prototype.remove=function(t){t.removeAttribute(this.keyName)},Attributor.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},Attributor}();e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Code=void 0;var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(2)),i=_interopRequireDefault(r(0)),l=_interopRequireDefault(r(4)),a=_interopRequireDefault(r(6)),s=_interopRequireDefault(r(7));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var u=function(t){function Code(){return _classCallCheck(this,Code),_possibleConstructorReturn(this,(Code.__proto__||Object.getPrototypeOf(Code)).apply(this,arguments))}return _inherits(Code,t),Code}(a.default);u.blotName="code",u.tagName="CODE";var f=function(t){function CodeBlock(){return _classCallCheck(this,CodeBlock),_possibleConstructorReturn(this,(CodeBlock.__proto__||Object.getPrototypeOf(CodeBlock)).apply(this,arguments))}return _inherits(CodeBlock,t),n(CodeBlock,[{key:"delta",value:function(){var t=this,e=this.domNode.textContent;return e.endsWith("\n")&&(e=e.slice(0,-1)),e.split("\n").reduce(function(e,r){return e.insert(r).insert("\n",t.formats())},new o.default)}},{key:"format",value:function(t,e){if(t!==this.statics.blotName||!e){var r=_slicedToArray(this.descendant(s.default,this.length()-1),1)[0];null!=r&&r.deleteAt(r.length()-1,1),_get(CodeBlock.prototype.__proto__||Object.getPrototypeOf(CodeBlock.prototype),"format",this).call(this,t,e)}}},{key:"formatAt",value:function(t,e,r,n){if(0!==e&&null!=i.default.query(r,i.default.Scope.BLOCK)&&(r!==this.statics.blotName||n!==this.statics.formats(this.domNode))){var o=this.newlineIndex(t);if(!(o<0)&&!(o>=t+e)){var l=this.newlineIndex(t,!0)+1,a=o-l+1,s=this.isolate(l,a),u=s.next;s.format(r,n),u instanceof CodeBlock&&u.formatAt(0,t-l+e-a,r,n)}}}},{key:"insertAt",value:function(t,e,r){if(null==r){var n=_slicedToArray(this.descendant(s.default,t),2),o=n[0],i=n[1];o.insertAt(i,e)}}},{key:"length",value:function(){var t=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?t:t+1}},{key:"newlineIndex",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this.domNode.textContent.slice(0,t).lastIndexOf("\n");var r=this.domNode.textContent.slice(t).indexOf("\n");return r>-1?t+r:-1}},{key:"optimize",value:function(t){this.domNode.textContent.endsWith("\n")||this.appendChild(i.default.create("text","\n")),_get(CodeBlock.prototype.__proto__||Object.getPrototypeOf(CodeBlock.prototype),"optimize",this).call(this,t);var e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===e.statics.formats(e.domNode)&&(e.optimize(t),e.moveChildren(this),e.remove())}},{key:"replace",value:function(t){_get(CodeBlock.prototype.__proto__||Object.getPrototypeOf(CodeBlock.prototype),"replace",this).call(this,t),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(t){var e=i.default.find(t);null==e?t.parentNode.removeChild(t):e instanceof i.default.Embed?e.remove():e.unwrap()})}}],[{key:"create",value:function(t){var e=_get(CodeBlock.__proto__||Object.getPrototypeOf(CodeBlock),"create",this).call(this,t);return e.setAttribute("spellcheck",!1),e}},{key:"formats",value:function(){return!0}}]),CodeBlock}(l.default);f.blotName="code-block",f.tagName="PRE",f.TAB="  ",e.Code=u,e.default=f},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),i=_interopRequireDefault(r(2)),l=_interopRequireDefault(r(20)),a=_interopRequireDefault(r(0)),s=_interopRequireDefault(r(13)),u=_interopRequireDefault(r(24)),f=r(4),p=_interopRequireDefault(f),d=_interopRequireDefault(r(16)),h=_interopRequireDefault(r(21)),y=_interopRequireDefault(r(11)),b=_interopRequireDefault(r(3));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var v=/^[ -~]*$/,g=function(){function Editor(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,Editor),this.scroll=t,this.delta=this.getDelta()}return o(Editor,[{key:"applyDelta",value:function(t){var e=this,r=!1;this.scroll.update();var o=this.scroll.length();return this.scroll.batchStart(),(t=t.reduce(function(t,e){if(1===e.insert){var r=(0,h.default)(e.attributes);return delete r.image,t.insert({image:e.attributes.image},r)}if(null!=e.attributes&&(!0===e.attributes.list||!0===e.attributes.bullet)&&((e=(0,h.default)(e)).attributes.list?e.attributes.list="ordered":(e.attributes.list="bullet",delete e.attributes.bullet)),"string"==typeof e.insert){var n=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(n,e.attributes)}return t.push(e)},new i.default)).reduce(function(t,i){var s=i.retain||i.delete||i.insert.length||1,u=i.attributes||{};if(null!=i.insert){if("string"==typeof i.insert){var d=i.insert;d.endsWith("\n")&&r&&(r=!1,d=d.slice(0,-1)),t>=o&&!d.endsWith("\n")&&(r=!0),e.scroll.insertAt(t,d);var h=_slicedToArray(e.scroll.line(t),2),y=h[0],v=h[1],g=(0,b.default)({},(0,f.bubbleFormats)(y));if(y instanceof p.default){var m=_slicedToArray(y.descendant(a.default.Leaf,v),1)[0];g=(0,b.default)(g,(0,f.bubbleFormats)(m))}u=l.default.attributes.diff(g,u)||{}}else if("object"===n(i.insert)){var _=Object.keys(i.insert)[0];if(null==_)return t;e.scroll.insertAt(t,_,i.insert[_])}o+=s}return Object.keys(u).forEach(function(r){e.scroll.formatAt(t,s,r,u[r])}),t+s},0),t.reduce(function(t,r){return"number"==typeof r.delete?(e.scroll.deleteAt(t,r.delete),t):t+(r.retain||r.insert.length||1)},0),this.scroll.batchEnd(),this.update(t)}},{key:"deleteText",value:function(t,e){return this.scroll.deleteAt(t,e),this.update(new i.default().retain(t).delete(e))}},{key:"formatLine",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(n).forEach(function(o){if(null==r.scroll.whitelist||r.scroll.whitelist[o]){var i=r.scroll.lines(t,Math.max(e,1)),l=e;i.forEach(function(e){var i=e.length();if(e instanceof s.default){var a=t-e.offset(r.scroll),u=e.newlineIndex(a+l)-a+1;e.formatAt(a,u,o,n[o])}else e.format(o,n[o]);l-=i})}}),this.scroll.optimize(),this.update(new i.default().retain(t).retain(e,(0,h.default)(n)))}},{key:"formatText",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(n).forEach(function(o){r.scroll.formatAt(t,e,o,n[o])}),this.update(new i.default().retain(t).retain(e,(0,h.default)(n)))}},{key:"getContents",value:function(t,e){return this.delta.slice(t,t+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(t,e){return t.concat(e.delta())},new i.default)}},{key:"getFormat",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],n=[];0===e?this.scroll.path(t).forEach(function(t){var e=_slicedToArray(t,1)[0];e instanceof p.default?r.push(e):e instanceof a.default.Leaf&&n.push(e)}):(r=this.scroll.lines(t,e),n=this.scroll.descendants(a.default.Leaf,t,e));var o=[r,n].map(function(t){if(0===t.length)return{};for(var e=(0,f.bubbleFormats)(t.shift());Object.keys(e).length>0;){var r=t.shift();if(null==r)break;e=function(t,e){return Object.keys(e).reduce(function(r,n){return null==t[n]||(e[n]===t[n]?r[n]=e[n]:Array.isArray(e[n])?0>e[n].indexOf(t[n])&&(r[n]=e[n].concat([t[n]])):r[n]=[e[n],t[n]]),r},{})}((0,f.bubbleFormats)(r),e)}return e});return b.default.apply(b.default,o)}},{key:"getText",value:function(t,e){return this.getContents(t,e).filter(function(t){return"string"==typeof t.insert}).map(function(t){return t.insert}).join("")}},{key:"insertEmbed",value:function(t,e,r){var n;return this.scroll.insertAt(t,e,r),this.update(new i.default().retain(t).insert((e in(n={})?Object.defineProperty(n,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[e]=r,n)))}},{key:"insertText",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(n).forEach(function(o){r.scroll.formatAt(t,e.length,o,n[o])}),this.update(new i.default().retain(t).insert(e,(0,h.default)(n)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var t=this.scroll.children.head;return t.statics.blotName===p.default.blotName&&!(t.children.length>1)&&t.children.head instanceof d.default}},{key:"removeFormat",value:function(t,e){var r=this.getText(t,e),n=_slicedToArray(this.scroll.line(t+e),2),o=n[0],l=n[1],a=0,u=new i.default;null!=o&&(a=o instanceof s.default?o.newlineIndex(l)-l+1:o.length()-l,u=o.delta().slice(l,l+a-1).insert("\n"));var f=this.getContents(t,e+a).diff(new i.default().insert(r).concat(u)),p=new i.default().retain(t).concat(f);return this.applyDelta(p)}},{key:"update",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(v)&&a.default.find(e[0].target)){var o=a.default.find(e[0].target),l=(0,f.bubbleFormats)(o),s=o.offset(this.scroll),p=e[0].oldValue.replace(u.default.CONTENTS,""),d=new i.default().insert(p),h=new i.default().insert(o.value());t=new i.default().retain(s).concat(d.diff(h,r)).reduce(function(t,e){return e.insert?t.insert(e.insert,l):t.push(e)},new i.default),this.delta=n.compose(t)}else this.delta=this.getDelta(),t&&(0,y.default)(n.compose(t),this.delta)||(t=n.diff(this.delta,r));return t}}]),Editor}();e.default=g},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Range=void 0;var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(21)),l=_interopRequireDefault(r(11)),a=_interopRequireDefault(r(8));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _toConsumableArray(t){if(!Array.isArray(t))return Array.from(t);for(var e=0,r=Array(t.length);e<t.length;e++)r[e]=t[e];return r}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}var s=(0,_interopRequireDefault(r(10)).default)("quill:selection"),Range=function Range(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;_classCallCheck(this,Range),this.index=t,this.length=e},u=function(){function Selection(t,e){var r=this;_classCallCheck(this,Selection),this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=o.default.create("cursor",this),this.lastRange=this.savedRange=new Range(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){r.mouseDown||setTimeout(r.update.bind(r,a.default.sources.USER),1)}),this.emitter.on(a.default.events.EDITOR_CHANGE,function(t,e){t===a.default.events.TEXT_CHANGE&&e.length()>0&&r.update(a.default.sources.SILENT)}),this.emitter.on(a.default.events.SCROLL_BEFORE_UPDATE,function(){if(r.hasFocus()){var t=r.getNativeRange();null!=t&&t.start.node!==r.cursor.textNode&&r.emitter.once(a.default.events.SCROLL_UPDATE,function(){try{r.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset)}catch(t){}})}}),this.emitter.on(a.default.events.SCROLL_OPTIMIZE,function(t,e){if(e.range){var n=e.range,o=n.startNode,i=n.startOffset,l=n.endNode,a=n.endOffset;r.setNativeRange(o,i,l,a)}}),this.update(a.default.sources.SILENT)}return n(Selection,[{key:"handleComposition",value:function(){var t=this;this.root.addEventListener("compositionstart",function(){t.composing=!0}),this.root.addEventListener("compositionend",function(){if(t.composing=!1,t.cursor.parent){var e=t.cursor.restore();e&&setTimeout(function(){t.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)},1)}})}},{key:"handleDragging",value:function(){var t=this;this.emitter.listenDOM("mousedown",document.body,function(){t.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){t.mouseDown=!1,t.update(a.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(t,e){if(null==this.scroll.whitelist||this.scroll.whitelist[t]){this.scroll.update();var r=this.getNativeRange();if(!(null==r||!r.native.collapsed||o.default.query(t,o.default.Scope.BLOCK))){if(r.start.node!==this.cursor.textNode){var n=o.default.find(r.start.node,!1);if(null==n)return;if(n instanceof o.default.Leaf){var i=n.split(r.start.offset);n.parent.insertBefore(this.cursor,i)}else n.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.scroll.length();e=Math.min((t=Math.min(t,r-1))+e,r-1)-t;var n=void 0,o=_slicedToArray(this.scroll.leaf(t),2),i=o[0],l=o[1];if(null==i)return null;var a=_slicedToArray(i.position(l,!0),2);n=a[0],l=a[1];var s=document.createRange();if(e>0){s.setStart(n,l);var u=_slicedToArray(this.scroll.leaf(t+e),2);if(i=u[0],l=u[1],null==i)return null;var f=_slicedToArray(i.position(l,!0),2);return n=f[0],l=f[1],s.setEnd(n,l),s.getBoundingClientRect()}var p="left",d=void 0;return n instanceof Text?(l<n.data.length?(s.setStart(n,l),s.setEnd(n,l+1)):(s.setStart(n,l-1),s.setEnd(n,l),p="right"),d=s.getBoundingClientRect()):(d=i.domNode.getBoundingClientRect(),l>0&&(p="right")),{bottom:d.top+d.height,height:d.height,left:d[p],right:d[p],top:d.top,width:0}}},{key:"getNativeRange",value:function(){var t=document.getSelection();if(null==t||t.rangeCount<=0)return null;var e=t.getRangeAt(0);if(null==e)return null;var r=this.normalizeNative(e);return s.info("getNativeRange",r),r}},{key:"getRange",value:function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(t){var e=this,r=[[t.start.node,t.start.offset]];t.native.collapsed||r.push([t.end.node,t.end.offset]);var n=r.map(function(t){var r=_slicedToArray(t,2),n=r[0],i=r[1],l=o.default.find(n,!0),a=l.offset(e.scroll);return 0===i?a:l instanceof o.default.Container?a+l.length():a+l.index(n,i)}),i=Math.min(Math.max.apply(Math,_toConsumableArray(n)),this.scroll.length()-1),l=Math.min.apply(Math,[i].concat(_toConsumableArray(n)));return new Range(l,i-l)}},{key:"normalizeNative",value:function(t){if(!contains(this.root,t.startContainer)||!t.collapsed&&!contains(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(function(t){for(var e=t.node,r=t.offset;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>r)e=e.childNodes[r],r=0;else if(e.childNodes.length===r)r=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length+1;else break;t.node=e,t.offset=r}),e}},{key:"rangeToNative",value:function(t){var e=this,r=t.collapsed?[t.index]:[t.index,t.index+t.length],n=[],o=this.scroll.length();return r.forEach(function(t,r){t=Math.min(o-1,t);var i=void 0,l=_slicedToArray(e.scroll.leaf(t),2),a=l[0],s=l[1],u=_slicedToArray(a.position(s,0!==r),2);i=u[0],s=u[1],n.push(i,s)}),n.length<2&&(n=n.concat(n)),n}},{key:"scrollIntoView",value:function(t){var e=this.lastRange;if(null!=e){var r=this.getBounds(e.index,e.length);if(null!=r){var n=this.scroll.length()-1,o=_slicedToArray(this.scroll.line(Math.min(e.index,n)),1)[0],i=o;if(e.length>0&&(i=_slicedToArray(this.scroll.line(Math.min(e.index+e.length,n)),1)[0]),null!=o&&null!=i){var l=t.getBoundingClientRect();r.top<l.top?t.scrollTop-=l.top-r.top:r.bottom>l.bottom&&(t.scrollTop+=r.bottom-l.bottom)}}}}},{key:"setNativeRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(s.info("setNativeRange",t,e,r,n),null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=r.parentNode){var i=document.getSelection();if(null!=i){if(null!=t){this.hasFocus()||this.root.focus();var l=(this.getNativeRange()||{}).native;if(null==l||o||t!==l.startContainer||e!==l.startOffset||r!==l.endContainer||n!==l.endOffset){"BR"==t.tagName&&(e=[].indexOf.call(t.parentNode.childNodes,t),t=t.parentNode),"BR"==r.tagName&&(n=[].indexOf.call(r.parentNode.childNodes,r),r=r.parentNode);var a=document.createRange();a.setStart(t,e),a.setEnd(r,n),i.removeAllRanges(),i.addRange(a)}}else i.removeAllRanges(),this.root.blur(),document.body.focus()}}}},{key:"setRange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a.default.sources.API;if("string"==typeof e&&(r=e,e=!1),s.info("setRange",t),null!=t){var n=this.rangeToNative(t);this.setNativeRange.apply(this,_toConsumableArray(n).concat([e]))}else this.setNativeRange(null);this.update(r)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.default.sources.USER,e=this.lastRange,r=_slicedToArray(this.getRange(),2),n=r[0],o=r[1];if(this.lastRange=n,null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,l.default)(e,this.lastRange)){!this.composing&&null!=o&&o.native.collapsed&&o.start.node!==this.cursor.textNode&&this.cursor.restore();var s,u,f=[a.default.events.SELECTION_CHANGE,(0,i.default)(this.lastRange),(0,i.default)(e),t];(s=this.emitter).emit.apply(s,[a.default.events.EDITOR_CHANGE].concat(f)),t!==a.default.sources.SILENT&&(u=this.emitter).emit.apply(u,f)}}}]),Selection}();function contains(t,e){try{e.parentNode}catch(t){return!1}return e instanceof Text&&(e=e.parentNode),t.contains(e)}e.Range=Range,e.default=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function Break(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Break),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Break.__proto__||Object.getPrototypeOf(Break)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Break,t),o(Break,[{key:"insertInto",value:function(t,e){0===t.children.length?_get(Break.prototype.__proto__||Object.getPrototypeOf(Break.prototype),"insertInto",this).call(this,t,e):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),Break}(((n=r(0))&&n.__esModule?n:{default:n}).default.Embed);i.blotName="break",i.tagName="BR",e.default=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(44),l=r(30),a=r(1),s=function(t){function ContainerBlot(e){var r=t.call(this,e)||this;return r.build(),r}return o(ContainerBlot,t),ContainerBlot.prototype.appendChild=function(t){this.insertBefore(t)},ContainerBlot.prototype.attach=function(){t.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},ContainerBlot.prototype.build=function(){var t=this;this.children=new i.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(e){try{var r=makeBlot(e);t.insertBefore(r,t.children.head||void 0)}catch(t){if(t instanceof a.ParchmentError)return;throw t}})},ContainerBlot.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,function(t,e,r){t.deleteAt(e,r)})},ContainerBlot.prototype.descendant=function(t,e){var r=this.children.find(e),n=r[0],o=r[1];return null==t.blotName&&t(n)||null!=t.blotName&&n instanceof t?[n,o]:n instanceof ContainerBlot?n.descendant(t,o):[null,-1]},ContainerBlot.prototype.descendants=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=Number.MAX_VALUE);var n=[],o=r;return this.children.forEachAt(e,r,function(e,r,i){(null==t.blotName&&t(e)||null!=t.blotName&&e instanceof t)&&n.push(e),e instanceof ContainerBlot&&(n=n.concat(e.descendants(t,r,o))),o-=i}),n},ContainerBlot.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),t.prototype.detach.call(this)},ContainerBlot.prototype.formatAt=function(t,e,r,n){this.children.forEachAt(t,e,function(t,e,o){t.formatAt(e,o,r,n)})},ContainerBlot.prototype.insertAt=function(t,e,r){var n=this.children.find(t),o=n[0],i=n[1];if(o)o.insertAt(i,e,r);else{var l=null==r?a.create("text",e):a.create(e,r);this.appendChild(l)}},ContainerBlot.prototype.insertBefore=function(t,e){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(e){return t instanceof e}))throw new a.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,e)},ContainerBlot.prototype.length=function(){return this.children.reduce(function(t,e){return t+e.length()},0)},ContainerBlot.prototype.moveChildren=function(t,e){this.children.forEach(function(r){t.insertBefore(r,e)})},ContainerBlot.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),0===this.children.length){if(null!=this.statics.defaultChild){var r=a.create(this.statics.defaultChild);this.appendChild(r),r.optimize(e)}else this.remove()}},ContainerBlot.prototype.path=function(t,e){void 0===e&&(e=!1);var r=this.children.find(t,e),n=r[0],o=r[1],i=[[this,t]];return n instanceof ContainerBlot?i.concat(n.path(o,e)):(null!=n&&i.push([n,o]),i)},ContainerBlot.prototype.removeChild=function(t){this.children.remove(t)},ContainerBlot.prototype.replace=function(e){e instanceof ContainerBlot&&e.moveChildren(this),t.prototype.replace.call(this,e)},ContainerBlot.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var r=this.clone();return this.parent.insertBefore(r,this.next),this.children.forEachAt(t,this.length(),function(t,n,o){t=t.split(n,e),r.appendChild(t)}),r},ContainerBlot.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},ContainerBlot.prototype.update=function(t,e){var r=this,n=[],o=[];t.forEach(function(t){t.target===r.domNode&&"childList"===t.type&&(n.push.apply(n,t.addedNodes),o.push.apply(o,t.removedNodes))}),o.forEach(function(t){if(!(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var e=a.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===r.domNode)&&e.detach()}}),n.filter(function(t){return t.parentNode==r.domNode}).sort(function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(t){var e=null;null!=t.nextSibling&&(e=a.find(t.nextSibling));var n=makeBlot(t);(n.next!=e||null==n.next)&&(null!=n.parent&&n.parent.removeChild(r),r.insertBefore(n,e||void 0))})},ContainerBlot}(l.default);function makeBlot(t){var e=a.find(t);if(null==e)try{e=a.create(t)}catch(r){e=a.create(a.Scope.INLINE),[].slice.call(t.childNodes).forEach(function(t){e.domNode.appendChild(t)}),t.parentNode&&t.parentNode.replaceChild(e.domNode,t),e.attach()}return e}e.default=s},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(12),l=r(31),a=r(17),s=r(1),u=function(t){function FormatBlot(e){var r=t.call(this,e)||this;return r.attributes=new l.default(r.domNode),r}return o(FormatBlot,t),FormatBlot.formats=function(t){return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},FormatBlot.prototype.format=function(t,e){var r=s.query(t);r instanceof i.default?this.attributes.attribute(r,e):e&&null!=r&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)},FormatBlot.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode);return null!=e&&(t[this.statics.blotName]=e),t},FormatBlot.prototype.replaceWith=function(e,r){var n=t.prototype.replaceWith.call(this,e,r);return this.attributes.copy(n),n},FormatBlot.prototype.update=function(e,r){var n=this;t.prototype.update.call(this,e,r),e.some(function(t){return t.target===n.domNode&&"attributes"===t.type})&&this.attributes.build()},FormatBlot.prototype.wrap=function(e,r){var n=t.prototype.wrap.call(this,e,r);return n instanceof FormatBlot&&n.statics.scope===this.statics.scope&&this.attributes.move(n),n},FormatBlot}(a.default);e.default=u},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(30),l=r(1),a=function(t){function LeafBlot(){return null!==t&&t.apply(this,arguments)||this}return o(LeafBlot,t),LeafBlot.value=function(t){return!0},LeafBlot.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},LeafBlot.prototype.position=function(t,e){var r=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return t>0&&(r+=1),[this.parent.domNode,r]},LeafBlot.prototype.value=function(){var t;return(t={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},LeafBlot.scope=l.Scope.INLINE_BLOT,LeafBlot}(i.default);e.default=a},function(t,e,r){var n=r(11),o=r(3),i={attributes:{compose:function(t,e,r){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var n=o(!0,{},e);for(var i in r||(n=Object.keys(n).reduce(function(t,e){return null!=n[e]&&(t[e]=n[e]),t},{})),t)void 0!==t[i]&&void 0===e[i]&&(n[i]=t[i]);return Object.keys(n).length>0?n:void 0},diff:function(t,e){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var r=Object.keys(t).concat(Object.keys(e)).reduce(function(r,o){return n(t[o],e[o])||(r[o]=void 0===e[o]?null:e[o]),r},{});return Object.keys(r).length>0?r:void 0},transform:function(t,e,r){if("object"!=typeof t)return e;if("object"==typeof e){if(!r)return e;var n=Object.keys(e).reduce(function(r,n){return void 0===t[n]&&(r[n]=e[n]),r},{});return Object.keys(n).length>0?n:void 0}}},iterator:function(t){return new Iterator(t)},length:function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"string"==typeof t.insert?t.insert.length:1}};function Iterator(t){this.ops=t,this.index=0,this.offset=0}Iterator.prototype.hasNext=function(){return this.peekLength()<1/0},Iterator.prototype.next=function(t){t||(t=1/0);var e=this.ops[this.index];if(!e)return{retain:1/0};var r=this.offset,n=i.length(e);if(t>=n-r?(t=n-r,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};var o={};return e.attributes&&(o.attributes=e.attributes),"number"==typeof e.retain?o.retain=t:"string"==typeof e.insert?o.insert=e.insert.substr(r,t):o.insert=e.insert,o},Iterator.prototype.peek=function(){return this.ops[this.index]},Iterator.prototype.peekLength=function(){return this.ops[this.index]?i.length(this.ops[this.index])-this.offset:1/0},Iterator.prototype.peekType=function(){if(this.ops[this.index]){if("number"==typeof this.ops[this.index].delete)return"delete";if("number"!=typeof this.ops[this.index].retain)return"insert"}return"retain"},Iterator.prototype.rest=function(){if(!this.hasNext())return[];if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,r=this.next(),n=this.ops.slice(this.index);return this.offset=t,this.index=e,[r].concat(n)},t.exports=i},function(t,e){var r=function(){"use strict";var t,e,r;function _instanceof(t,e){return null!=e&&t instanceof e}try{t=Map}catch(e){t=function(){}}try{e=Set}catch(t){e=function(){}}try{r=Promise}catch(t){r=function(){}}function clone(o,i,l,a,s){"object"==typeof i&&(l=i.depth,a=i.prototype,s=i.includeNonEnumerable,i=i.circular);var u=[],f=[],p=void 0!==n;return void 0===i&&(i=!0),void 0===l&&(l=1/0),function _clone(o,l){if(null===o)return null;if(0===l||"object"!=typeof o)return o;if(_instanceof(o,t))d=new t;else if(_instanceof(o,e))d=new e;else if(_instanceof(o,r))d=new r(function(t,e){o.then(function(e){t(_clone(e,l-1))},function(t){e(_clone(t,l-1))})});else if(clone.__isArray(o))d=[];else if(clone.__isRegExp(o))d=new RegExp(o.source,__getRegExpFlags(o)),o.lastIndex&&(d.lastIndex=o.lastIndex);else if(clone.__isDate(o))d=new Date(o.getTime());else{if(p&&n.isBuffer(o))return d=n.allocUnsafe?n.allocUnsafe(o.length):new n(o.length),o.copy(d),d;_instanceof(o,Error)?d=Object.create(o):void 0===a?d=Object.create(h=Object.getPrototypeOf(o)):(d=Object.create(a),h=a)}if(i){var d,h,y,b=u.indexOf(o);if(-1!=b)return f[b];u.push(o),f.push(d)}for(var v in _instanceof(o,t)&&o.forEach(function(t,e){var r=_clone(e,l-1),n=_clone(t,l-1);d.set(r,n)}),_instanceof(o,e)&&o.forEach(function(t){var e=_clone(t,l-1);d.add(e)}),o)h&&(y=Object.getOwnPropertyDescriptor(h,v)),y&&null==y.set||(d[v]=_clone(o[v],l-1));if(Object.getOwnPropertySymbols)for(var g=Object.getOwnPropertySymbols(o),v=0;v<g.length;v++){var m=g[v],_=Object.getOwnPropertyDescriptor(o,m);(!_||_.enumerable||s)&&(d[m]=_clone(o[m],l-1),_.enumerable||Object.defineProperty(d,m,{enumerable:!1}))}if(s)for(var k=Object.getOwnPropertyNames(o),v=0;v<k.length;v++){var O=k[v],_=Object.getOwnPropertyDescriptor(o,O);_&&_.enumerable||(d[O]=_clone(o[O],l-1),Object.defineProperty(d,O,{enumerable:!1}))}return d}(o,l)}function __objToStr(t){return Object.prototype.toString.call(t)}function __getRegExpFlags(t){var e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),e}return clone.clonePrototype=function(t){if(null===t)return null;var c=function(){};return c.prototype=t,new c},clone.__objToStr=__objToStr,clone.__isDate=function(t){return"object"==typeof t&&"[object Date]"===__objToStr(t)},clone.__isArray=function(t){return"object"==typeof t&&"[object Array]"===__objToStr(t)},clone.__isRegExp=function(t){return"object"==typeof t&&"[object RegExp]"===__objToStr(t)},clone.__getRegExpFlags=__getRegExpFlags,clone}();"object"==typeof t&&t.exports&&(t.exports=r)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(8)),l=r(4),a=_interopRequireDefault(l),s=_interopRequireDefault(r(16)),u=_interopRequireDefault(r(13)),f=_interopRequireDefault(r(25));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function isLine(t){return t instanceof a.default||t instanceof l.BlockEmbed}var p=function(t){function Scroll(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Scroll);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Scroll.__proto__||Object.getPrototypeOf(Scroll)).call(this,t));return r.emitter=e.emitter,Array.isArray(e.whitelist)&&(r.whitelist=e.whitelist.reduce(function(t,e){return t[e]=!0,t},{})),r.domNode.addEventListener("DOMNodeInserted",function(){}),r.optimize(),r.enable(),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Scroll,t),n(Scroll,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(t,e){var r=_slicedToArray(this.line(t),2),n=r[0],o=r[1],i=_slicedToArray(this.line(t+e),1)[0];if(_get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"deleteAt",this).call(this,t,e),null!=i&&n!==i&&o>0){if(n instanceof l.BlockEmbed||i instanceof l.BlockEmbed){this.optimize();return}if(n instanceof u.default){var a=n.newlineIndex(n.length(),!0);if(a>-1&&(n=n.split(a+1))===i){this.optimize();return}}else if(i instanceof u.default){var f=i.newlineIndex(0);f>-1&&i.split(f+1)}var p=i.children.head instanceof s.default?null:i.children.head;n.moveChildren(i,p),n.remove()}this.optimize()}},{key:"enable",value:function(){var t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.domNode.setAttribute("contenteditable",t)}},{key:"formatAt",value:function(t,e,r,n){(null==this.whitelist||this.whitelist[r])&&(_get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"formatAt",this).call(this,t,e,r,n),this.optimize())}},{key:"insertAt",value:function(t,e,r){if(null==r||null==this.whitelist||this.whitelist[e]){if(t>=this.length()){if(null==r||null==o.default.query(e,o.default.Scope.BLOCK)){var n=o.default.create(this.statics.defaultChild);this.appendChild(n),null==r&&e.endsWith("\n")&&(e=e.slice(0,-1)),n.insertAt(0,e,r)}else{var i=o.default.create(e,r);this.appendChild(i)}}else _get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"insertAt",this).call(this,t,e,r);this.optimize()}}},{key:"insertBefore",value:function(t,e){if(t.statics.scope===o.default.Scope.INLINE_BLOT){var r=o.default.create(this.statics.defaultChild);r.appendChild(t),t=r}_get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"insertBefore",this).call(this,t,e)}},{key:"leaf",value:function(t){return this.path(t).pop()||[null,-1]}},{key:"line",value:function(t){return t===this.length()?this.line(t-1):this.descendant(isLine,t)}},{key:"lines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return function getLines(t,e,r){var n=[],i=r;return t.children.forEachAt(e,r,function(t,e,r){isLine(t)?n.push(t):t instanceof o.default.Container&&(n=n.concat(getLines(t,e,i))),i-=r}),n}(this,t,e)}},{key:"optimize",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(_get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"optimize",this).call(this,t,e),t.length>0&&this.emitter.emit(i.default.events.SCROLL_OPTIMIZE,t,e))}},{key:"path",value:function(t){return _get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"path",this).call(this,t).slice(1)}},{key:"update",value:function(t){if(!0!==this.batch){var e=i.default.sources.USER;"string"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t.length>0&&this.emitter.emit(i.default.events.SCROLL_BEFORE_UPDATE,e,t),_get(Scroll.prototype.__proto__||Object.getPrototypeOf(Scroll.prototype),"update",this).call(this,t.concat([])),t.length>0&&this.emitter.emit(i.default.events.SCROLL_UPDATE,e,t)}}}]),Scroll}(o.default.Scroll);p.blotName="scroll",p.className="ql-editor",p.tagName="DIV",p.defaultChild="block",p.allowedChildren=[a.default,l.BlockEmbed,f.default],e.default=p},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SHORTKEY=e.default=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),i=_interopRequireDefault(r(21)),l=_interopRequireDefault(r(11)),a=_interopRequireDefault(r(3)),s=_interopRequireDefault(r(2)),u=_interopRequireDefault(r(20)),f=_interopRequireDefault(r(0)),p=_interopRequireDefault(r(5)),d=_interopRequireDefault(r(10)),h=_interopRequireDefault(r(9));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var y=(0,d.default)("quill:keyboard"),b=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",v=function(t){function Keyboard(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Keyboard);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Keyboard.__proto__||Object.getPrototypeOf(Keyboard)).call(this,t,e));return r.bindings={},Object.keys(r.options.bindings).forEach(function(e){("list autofill"!==e||null==t.scroll.whitelist||t.scroll.whitelist.list)&&r.options.bindings[e]&&r.addBinding(r.options.bindings[e])}),r.addBinding({key:Keyboard.keys.ENTER,shiftKey:null},handleEnter),r.addBinding({key:Keyboard.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(r.addBinding({key:Keyboard.keys.BACKSPACE},{collapsed:!0},handleBackspace),r.addBinding({key:Keyboard.keys.DELETE},{collapsed:!0},handleDelete)):(r.addBinding({key:Keyboard.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},handleBackspace),r.addBinding({key:Keyboard.keys.DELETE},{collapsed:!0,suffix:/^.?$/},handleDelete)),r.addBinding({key:Keyboard.keys.BACKSPACE},{collapsed:!1},handleDeleteRange),r.addBinding({key:Keyboard.keys.DELETE},{collapsed:!1},handleDeleteRange),r.addBinding({key:Keyboard.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},handleBackspace),r.listen(),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Keyboard,t),o(Keyboard,null,[{key:"match",value:function(t,e){return e=normalize(e),!["altKey","ctrlKey","metaKey","shiftKey"].some(function(r){return!!e[r]!==t[r]&&null!==e[r]})&&e.key===(t.which||t.keyCode)}}]),o(Keyboard,[{key:"addBinding",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=normalize(t);if(null==n||null==n.key)return y.warn("Attempted to add invalid keyboard binding",n);"function"==typeof e&&(e={handler:e}),"function"==typeof r&&(r={handler:r}),n=(0,a.default)(n,e,r),this.bindings[n.key]=this.bindings[n.key]||[],this.bindings[n.key].push(n)}},{key:"listen",value:function(){var t=this;this.quill.root.addEventListener("keydown",function(e){if(!e.defaultPrevented){var r=e.which||e.keyCode,o=(t.bindings[r]||[]).filter(function(t){return Keyboard.match(e,t)});if(0!==o.length){var i=t.quill.getSelection();if(null!=i&&t.quill.hasFocus()){var a=_slicedToArray(t.quill.getLine(i.index),2),s=a[0],u=a[1],p=_slicedToArray(t.quill.getLeaf(i.index),2),d=p[0],h=p[1],y=_slicedToArray(0===i.length?[d,h]:t.quill.getLeaf(i.index+i.length),2),b=y[0],v=y[1],g=d instanceof f.default.Text?d.value().slice(0,h):"",m=b instanceof f.default.Text?b.value().slice(v):"",_={collapsed:0===i.length,empty:0===i.length&&1>=s.length(),format:t.quill.getFormat(i),offset:u,prefix:g,suffix:m};o.some(function(e){if(null!=e.collapsed&&e.collapsed!==_.collapsed||null!=e.empty&&e.empty!==_.empty||null!=e.offset&&e.offset!==_.offset)return!1;if(Array.isArray(e.format)){if(e.format.every(function(t){return null==_.format[t]}))return!1}else if("object"===n(e.format)&&!Object.keys(e.format).every(function(t){return!0===e.format[t]?null!=_.format[t]:!1===e.format[t]?null==_.format[t]:(0,l.default)(e.format[t],_.format[t])}))return!1;return!!((null==e.prefix||e.prefix.test(_.prefix))&&(null==e.suffix||e.suffix.test(_.suffix)))&&!0!==e.handler.call(t,i,_)})&&e.preventDefault()}}}})}}]),Keyboard}(h.default);function makeEmbedArrowHandler(t,e){var r,n=t===v.keys.LEFT?"prefix":"suffix";return _defineProperty(r={key:t,shiftKey:e,altKey:null},n,/^$/),_defineProperty(r,"handler",function(r){var n=r.index;return t===v.keys.RIGHT&&(n+=r.length+1),!(_slicedToArray(this.quill.getLeaf(n),1)[0]instanceof f.default.Embed)||(t===v.keys.LEFT?e?this.quill.setSelection(r.index-1,r.length+1,p.default.sources.USER):this.quill.setSelection(r.index-1,p.default.sources.USER):e?this.quill.setSelection(r.index,r.length+1,p.default.sources.USER):this.quill.setSelection(r.index+r.length+1,p.default.sources.USER),!1)}),r}function handleBackspace(t,e){if(!(0===t.index||1>=this.quill.getLength())){var r=_slicedToArray(this.quill.getLine(t.index),1)[0],n={};if(0===e.offset){var o=_slicedToArray(this.quill.getLine(t.index-1),1)[0];if(null!=o&&o.length()>1){var i=r.formats(),l=this.quill.getFormat(t.index-1,1);n=u.default.attributes.diff(i,l)||{}}}var a=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;this.quill.deleteText(t.index-a,a,p.default.sources.USER),Object.keys(n).length>0&&this.quill.formatLine(t.index-a,a,n,p.default.sources.USER),this.quill.focus()}}function handleDelete(t,e){var r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(t.index>=this.quill.getLength()-r)){var n={},o=0,i=_slicedToArray(this.quill.getLine(t.index),1)[0];if(e.offset>=i.length()-1){var l=_slicedToArray(this.quill.getLine(t.index+1),1)[0];if(l){var a=i.formats(),s=this.quill.getFormat(t.index,1);n=u.default.attributes.diff(a,s)||{},o=l.length()}}this.quill.deleteText(t.index,r,p.default.sources.USER),Object.keys(n).length>0&&this.quill.formatLine(t.index+o-1,r,n,p.default.sources.USER)}}function handleDeleteRange(t){var e=this.quill.getLines(t),r={};if(e.length>1){var n=e[0].formats(),o=e[e.length-1].formats();r=u.default.attributes.diff(o,n)||{}}this.quill.deleteText(t,p.default.sources.USER),Object.keys(r).length>0&&this.quill.formatLine(t.index,1,r,p.default.sources.USER),this.quill.setSelection(t.index,p.default.sources.SILENT),this.quill.focus()}function handleEnter(t,e){var r=this;t.length>0&&this.quill.scroll.deleteAt(t.index,t.length);var n=Object.keys(e.format).reduce(function(t,r){return f.default.query(r,f.default.Scope.BLOCK)&&!Array.isArray(e.format[r])&&(t[r]=e.format[r]),t},{});this.quill.insertText(t.index,"\n",n,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.focus(),Object.keys(e.format).forEach(function(t){null!=n[t]||Array.isArray(e.format[t])||"link"===t||r.quill.format(t,e.format[t],p.default.sources.USER)})}function makeCodeBlockHandler(t){return{key:v.keys.TAB,shiftKey:!t,format:{"code-block":!0},handler:function(e){var r=f.default.query("code-block"),n=e.index,o=e.length,i=_slicedToArray(this.quill.scroll.descendant(r,n),2),l=i[0],a=i[1];if(null!=l){var s=this.quill.getIndex(l),u=l.newlineIndex(a,!0)+1,d=l.newlineIndex(s+a+o),h=l.domNode.textContent.slice(u,d).split("\n");a=0,h.forEach(function(e,i){t?(l.insertAt(u+a,r.TAB),a+=r.TAB.length,0===i?n+=r.TAB.length:o+=r.TAB.length):e.startsWith(r.TAB)&&(l.deleteAt(u+a,r.TAB.length),a-=r.TAB.length,0===i?n-=r.TAB.length:o-=r.TAB.length),a+=e.length+1}),this.quill.update(p.default.sources.USER),this.quill.setSelection(n,o,p.default.sources.SILENT)}}}}function makeFormatHandler(t){return{key:t[0].toUpperCase(),shortKey:!0,handler:function(e,r){this.quill.format(t,!r.format[t],p.default.sources.USER)}}}function normalize(t){if("string"==typeof t||"number"==typeof t)return normalize({key:t});if((void 0===t?"undefined":n(t))==="object"&&(t=(0,i.default)(t,!1)),"string"==typeof t.key){if(null!=v.keys[t.key.toUpperCase()])t.key=v.keys[t.key.toUpperCase()];else{if(1!==t.key.length)return null;t.key=t.key.toUpperCase().charCodeAt(0)}}return t.shortKey&&(t[b]=t.shortKey,delete t.shortKey),t}v.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},v.DEFAULTS={bindings:{bold:makeFormatHandler("bold"),italic:makeFormatHandler("italic"),underline:makeFormatHandler("underline"),indent:{key:v.keys.TAB,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","+1",p.default.sources.USER)}},outdent:{key:v.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","-1",p.default.sources.USER)}},"outdent backspace":{key:v.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){null!=e.format.indent?this.quill.format("indent","-1",p.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,p.default.sources.USER)}},"indent code-block":makeCodeBlockHandler(!0),"outdent code-block":makeCodeBlockHandler(!1),"remove tab":{key:v.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,p.default.sources.USER)}},tab:{key:v.keys.TAB,handler:function(t){this.quill.history.cutoff();var e=new s.default().retain(t.index).delete(t.length).insert("	");this.quill.updateContents(e,p.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,p.default.sources.SILENT)}},"list empty enter":{key:v.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(t,e){this.quill.format("list",!1,p.default.sources.USER),e.format.indent&&this.quill.format("indent",!1,p.default.sources.USER)}},"checklist enter":{key:v.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(t){var e=_slicedToArray(this.quill.getLine(t.index),2),r=e[0],n=e[1],o=(0,a.default)({},r.formats(),{list:"checked"}),i=new s.default().retain(t.index).insert("\n",o).retain(r.length()-n-1).retain(1,{list:"unchecked"});this.quill.updateContents(i,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:v.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var r=_slicedToArray(this.quill.getLine(t.index),2),n=r[0],o=r[1],i=new s.default().retain(t.index).insert("\n",e.format).retain(n.length()-o-1).retain(1,{header:null});this.quill.updateContents(i,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){var r=e.prefix.length,n=_slicedToArray(this.quill.getLine(t.index),2),o=n[0],i=n[1];if(i>r)return!0;var l=void 0;switch(e.prefix.trim()){case"[]":case"[ ]":l="unchecked";break;case"[x]":l="checked";break;case"-":case"*":l="bullet";break;default:l="ordered"}this.quill.insertText(t.index," ",p.default.sources.USER),this.quill.history.cutoff();var a=new s.default().retain(t.index-i).delete(r+1).retain(o.length()-2-i).retain(1,{list:l});this.quill.updateContents(a,p.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-r,p.default.sources.SILENT)}},"code exit":{key:v.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(t){var e=_slicedToArray(this.quill.getLine(t.index),2),r=e[0],n=e[1],o=new s.default().retain(t.index+r.length()-n-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(o,p.default.sources.USER)}},"embed left":makeEmbedArrowHandler(v.keys.LEFT,!1),"embed left shift":makeEmbedArrowHandler(v.keys.LEFT,!0),"embed right":makeEmbedArrowHandler(v.keys.RIGHT,!1),"embed right shift":makeEmbedArrowHandler(v.keys.RIGHT,!0)}},e.default=v,e.SHORTKEY=b},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(7));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function Cursor(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Cursor);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Cursor.__proto__||Object.getPrototypeOf(Cursor)).call(this,t));return r.selection=e,r.textNode=document.createTextNode(Cursor.CONTENTS),r.domNode.appendChild(r.textNode),r._length=0,r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Cursor,t),n(Cursor,null,[{key:"value",value:function(){}}]),n(Cursor,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(t,e){if(0!==this._length)return _get(Cursor.prototype.__proto__||Object.getPrototypeOf(Cursor.prototype),"format",this).call(this,t,e);for(var r=this,n=0;null!=r&&r.statics.scope!==o.default.Scope.BLOCK_BLOT;)n+=r.offset(r.parent),r=r.parent;null!=r&&(this._length=Cursor.CONTENTS.length,r.optimize(),r.formatAt(n,Cursor.CONTENTS.length,t,e),this._length=0)}},{key:"index",value:function(t,e){return t===this.textNode?0:_get(Cursor.prototype.__proto__||Object.getPrototypeOf(Cursor.prototype),"index",this).call(this,t,e)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){_get(Cursor.prototype.__proto__||Object.getPrototypeOf(Cursor.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var t=this.textNode,e=this.selection.getNativeRange(),r=void 0,n=void 0,l=void 0;if(null!=e&&e.start.node===t&&e.end.node===t){var a=[t,e.start.offset,e.end.offset];r=a[0],n=a[1],l=a[2]}for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==Cursor.CONTENTS){var s=this.textNode.data.split(Cursor.CONTENTS).join("");this.next instanceof i.default?(r=this.next.domNode,this.next.insertAt(0,s),this.textNode.data=Cursor.CONTENTS):(this.textNode.data=s,this.parent.insertBefore(o.default.create(this.textNode),this),this.textNode=document.createTextNode(Cursor.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),null!=n){var u=_slicedToArray([n,l].map(function(t){return Math.max(0,Math.min(r.data.length,t-1))}),2);return n=u[0],l=u[1],{startNode:r,startOffset:n,endNode:r,endOffset:l}}}}},{key:"update",value:function(t,e){var r=this;if(t.some(function(t){return"characterData"===t.type&&t.target===r.textNode})){var n=this.restore();n&&(e.range=n)}}},{key:"value",value:function(){return""}}]),Cursor}(o.default.Embed);l.blotName="cursor",l.className="ql-cursor",l.tagName="span",l.CONTENTS="\uFEFF",e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=_interopRequireDefault(r(0)),o=r(4),i=_interopRequireDefault(o);function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function Container(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Container),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Container.__proto__||Object.getPrototypeOf(Container)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Container,t),Container}(n.default.Container);l.allowedChildren=[i.default,o.BlockEmbed,l],e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorStyle=e.ColorClass=e.ColorAttributor=void 0;var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=(n=r(0))&&n.__esModule?n:{default:n},l=function(t){function ColorAttributor(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,ColorAttributor),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(ColorAttributor.__proto__||Object.getPrototypeOf(ColorAttributor)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(ColorAttributor,t),o(ColorAttributor,[{key:"value",value:function(t){var e=_get(ColorAttributor.prototype.__proto__||Object.getPrototypeOf(ColorAttributor.prototype),"value",this).call(this,t);return e.startsWith("rgb(")?"#"+(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map(function(t){return("00"+parseInt(t).toString(16)).slice(-2)}).join(""):e}}]),ColorAttributor}(i.default.Attributor.Style),a=new i.default.Attributor.Class("color","ql-color",{scope:i.default.Scope.INLINE}),s=new l("color","color",{scope:i.default.Scope.INLINE});e.ColorAttributor=l,e.ColorClass=a,e.ColorStyle=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sanitize=e.default=void 0;var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function Link(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Link),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Link.__proto__||Object.getPrototypeOf(Link)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Link,t),o(Link,[{key:"format",value:function(t,e){if(t!==this.statics.blotName||!e)return _get(Link.prototype.__proto__||Object.getPrototypeOf(Link.prototype),"format",this).call(this,t,e);e=this.constructor.sanitize(e),this.domNode.setAttribute("href",e)}}],[{key:"create",value:function(t){var e=_get(Link.__proto__||Object.getPrototypeOf(Link),"create",this).call(this,t);return t=this.sanitize(t),e.setAttribute("href",t),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}},{key:"formats",value:function(t){return t.getAttribute("href")}},{key:"sanitize",value:function(t){return _sanitize(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}}]),Link}(((n=r(6))&&n.__esModule?n:{default:n}).default);function _sanitize(t,e){var r=document.createElement("a");r.href=t;var n=r.href.slice(0,r.href.indexOf(":"));return e.indexOf(n)>-1}i.blotName="link",i.tagName="A",i.SANITIZED_URL="about:blank",i.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e.default=i,e.sanitize=_sanitize},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),i=_interopRequireDefault(r(23)),l=_interopRequireDefault(r(107));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var a=0;function toggleAriaAttribute(t,e){t.setAttribute(e,"true"!==t.getAttribute(e))}var s=function(){function Picker(t){var e=this;(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,Picker),this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){e.togglePicker()}),this.label.addEventListener("keydown",function(t){switch(t.keyCode){case i.default.keys.ENTER:e.togglePicker();break;case i.default.keys.ESCAPE:e.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}return o(Picker,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),toggleAriaAttribute(this.label,"aria-expanded"),toggleAriaAttribute(this.options,"aria-hidden")}},{key:"buildItem",value:function(t){var e=this,r=document.createElement("span");return r.tabIndex="0",r.setAttribute("role","button"),r.classList.add("ql-picker-item"),t.hasAttribute("value")&&r.setAttribute("data-value",t.getAttribute("value")),t.textContent&&r.setAttribute("data-label",t.textContent),r.addEventListener("click",function(){e.selectItem(r,!0)}),r.addEventListener("keydown",function(t){switch(t.keyCode){case i.default.keys.ENTER:e.selectItem(r,!0),t.preventDefault();break;case i.default.keys.ESCAPE:e.escape(),t.preventDefault()}}),r}},{key:"buildLabel",value:function(){var t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=l.default,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}},{key:"buildOptions",value:function(){var t=this,e=document.createElement("span");e.classList.add("ql-picker-options"),e.setAttribute("aria-hidden","true"),e.tabIndex="-1",e.id="ql-picker-options-"+a,a+=1,this.label.setAttribute("aria-controls",e.id),this.options=e,[].slice.call(this.select.options).forEach(function(r){var n=t.buildItem(r);e.appendChild(n),!0===r.selected&&t.selectItem(n)}),this.container.appendChild(e)}},{key:"buildPicker",value:function(){var t=this;[].slice.call(this.select.attributes).forEach(function(e){t.container.setAttribute(e.name,e.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var t=this;this.close(),setTimeout(function(){return t.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.container.querySelector(".ql-selected");if(t!==r&&(null!=r&&r.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(t.parentNode.children,t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e))){if("function"==typeof Event)this.select.dispatchEvent(new Event("change"));else if(("undefined"==typeof Event?"undefined":n(Event))==="object"){var o=document.createEvent("Event");o.initEvent("change",!0,!0),this.select.dispatchEvent(o)}this.close()}}},{key:"update",value:function(){var t=void 0;if(this.select.selectedIndex>-1){var e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);var r=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",r)}}]),Picker}();e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=_interopRequireDefault(r(0)),o=_interopRequireDefault(r(5)),i=r(4),l=_interopRequireDefault(i),a=_interopRequireDefault(r(16)),s=_interopRequireDefault(r(25)),u=_interopRequireDefault(r(24)),f=_interopRequireDefault(r(35)),p=_interopRequireDefault(r(6)),d=_interopRequireDefault(r(22)),h=_interopRequireDefault(r(7)),y=_interopRequireDefault(r(55)),b=_interopRequireDefault(r(42)),v=_interopRequireDefault(r(23));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}o.default.register({"blots/block":l.default,"blots/block/embed":i.BlockEmbed,"blots/break":a.default,"blots/container":s.default,"blots/cursor":u.default,"blots/embed":f.default,"blots/inline":p.default,"blots/scroll":d.default,"blots/text":h.default,"modules/clipboard":y.default,"modules/history":b.default,"modules/keyboard":v.default}),n.default.register(l.default,a.default,u.default,p.default,d.default,h.default),e.default=o.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(1),o=function(){function ShadowBlot(t){this.domNode=t,this.domNode[n.DATA_KEY]={blot:this}}return Object.defineProperty(ShadowBlot.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),ShadowBlot.create=function(t){var e;if(null==this.tagName)throw new n.ParchmentError("Blot definition missing tagName");return Array.isArray(this.tagName)?("string"==typeof t&&parseInt(t=t.toUpperCase()).toString()===t&&(t=parseInt(t)),e="number"==typeof t?document.createElement(this.tagName[t-1]):this.tagName.indexOf(t)>-1?document.createElement(t):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e},ShadowBlot.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},ShadowBlot.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return n.create(t)},ShadowBlot.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[n.DATA_KEY]},ShadowBlot.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},ShadowBlot.prototype.formatAt=function(t,e,r,o){var i=this.isolate(t,e);if(null!=n.query(r,n.Scope.BLOT)&&o)i.wrap(r,o);else if(null!=n.query(r,n.Scope.ATTRIBUTE)){var l=n.create(this.statics.scope);i.wrap(l),l.format(r,o)}},ShadowBlot.prototype.insertAt=function(t,e,r){var o=null==r?n.create("text",e):n.create(e,r),i=this.split(t);this.parent.insertBefore(o,i)},ShadowBlot.prototype.insertInto=function(t,e){void 0===e&&(e=null),null!=this.parent&&this.parent.children.remove(this);var r=null;t.children.insertBefore(this,e),null!=e&&(r=e.domNode),(this.domNode.parentNode!=t.domNode||this.domNode.nextSibling!=r)&&t.domNode.insertBefore(this.domNode,r),this.parent=t,this.attach()},ShadowBlot.prototype.isolate=function(t,e){var r=this.split(t);return r.split(e),r},ShadowBlot.prototype.length=function(){return 1},ShadowBlot.prototype.offset=function(t){return(void 0===t&&(t=this.parent),null==this.parent||this==t)?0:this.parent.children.offset(this)+this.parent.offset(t)},ShadowBlot.prototype.optimize=function(t){null!=this.domNode[n.DATA_KEY]&&delete this.domNode[n.DATA_KEY].mutations},ShadowBlot.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},ShadowBlot.prototype.replace=function(t){null!=t.parent&&(t.parent.insertBefore(this,t.next),t.remove())},ShadowBlot.prototype.replaceWith=function(t,e){var r="string"==typeof t?n.create(t,e):t;return r.replace(this),r},ShadowBlot.prototype.split=function(t,e){return 0===t?this:this.next},ShadowBlot.prototype.update=function(t,e){},ShadowBlot.prototype.wrap=function(t,e){var r="string"==typeof t?n.create(t,e):t;return null!=this.parent&&this.parent.insertBefore(r,this.next),r.appendChild(this),r},ShadowBlot.blotName="abstract",ShadowBlot}();e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(12),o=r(32),i=r(33),l=r(1),a=function(){function AttributorStore(t){this.attributes={},this.domNode=t,this.build()}return AttributorStore.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},AttributorStore.prototype.build=function(){var t=this;this.attributes={};var e=n.default.keys(this.domNode),r=o.default.keys(this.domNode),a=i.default.keys(this.domNode);e.concat(r).concat(a).forEach(function(e){var r=l.query(e,l.Scope.ATTRIBUTE);r instanceof n.default&&(t.attributes[r.attrName]=r)})},AttributorStore.prototype.copy=function(t){var e=this;Object.keys(this.attributes).forEach(function(r){var n=e.attributes[r].value(e.domNode);t.format(r,n)})},AttributorStore.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},AttributorStore.prototype.values=function(){var t=this;return Object.keys(this.attributes).reduce(function(e,r){return e[r]=t.attributes[r].value(t.domNode),e},{})},AttributorStore}();e.default=a},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});function match(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter(function(t){return 0===t.indexOf(e+"-")})}Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function ClassAttributor(){return null!==t&&t.apply(this,arguments)||this}return o(ClassAttributor,t),ClassAttributor.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map(function(t){return t.split("-").slice(0,-1).join("-")})},ClassAttributor.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(this.keyName+"-"+e),!0)},ClassAttributor.prototype.remove=function(t){match(t,this.keyName).forEach(function(e){t.classList.remove(e)}),0===t.classList.length&&t.removeAttribute("class")},ClassAttributor.prototype.value=function(t){var e=(match(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},ClassAttributor}(r(12).default);e.default=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});function camelize(t){var e=t.split("-"),r=e.slice(1).map(function(t){return t[0].toUpperCase()+t.slice(1)}).join("");return e[0]+r}Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function StyleAttributor(){return null!==t&&t.apply(this,arguments)||this}return o(StyleAttributor,t),StyleAttributor.keys=function(t){return(t.getAttribute("style")||"").split(";").map(function(t){return t.split(":")[0].trim()})},StyleAttributor.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[camelize(this.keyName)]=e,!0)},StyleAttributor.prototype.remove=function(t){t.style[camelize(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},StyleAttributor.prototype.value=function(t){var e=t.style[camelize(this.keyName)];return this.canAdd(t,e)?e:""},StyleAttributor}(r(12).default);e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=function(){function Theme(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,Theme),this.quill=t,this.options=e,this.modules={}}return n(Theme,[{key:"init",value:function(){var t=this;Object.keys(this.options.modules).forEach(function(e){null==t.modules[e]&&t.addModule(e)})}},{key:"addModule",value:function(t){var e=this.quill.constructor.import("modules/"+t);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}]),Theme}();o.DEFAULTS={modules:{}},o.themes={default:o},e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(7));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function Embed(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Embed);var e=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Embed.__proto__||Object.getPrototypeOf(Embed)).call(this,t));return e.contentNode=document.createElement("span"),e.contentNode.setAttribute("contenteditable",!1),[].slice.call(e.domNode.childNodes).forEach(function(t){e.contentNode.appendChild(t)}),e.leftGuard=document.createTextNode("\uFEFF"),e.rightGuard=document.createTextNode("\uFEFF"),e.domNode.appendChild(e.leftGuard),e.domNode.appendChild(e.contentNode),e.domNode.appendChild(e.rightGuard),e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Embed,t),n(Embed,[{key:"index",value:function(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:_get(Embed.prototype.__proto__||Object.getPrototypeOf(Embed.prototype),"index",this).call(this,t,e)}},{key:"restore",value:function(t){var e=void 0,r=void 0,n=t.data.split("\uFEFF").join("");if(t===this.leftGuard){if(this.prev instanceof i.default){var l=this.prev.length();this.prev.insertAt(l,n),e={startNode:this.prev.domNode,startOffset:l+n.length}}else r=document.createTextNode(n),this.parent.insertBefore(o.default.create(r),this),e={startNode:r,startOffset:n.length}}else t===this.rightGuard&&(this.next instanceof i.default?(this.next.insertAt(0,n),e={startNode:this.next.domNode,startOffset:n.length}):(r=document.createTextNode(n),this.parent.insertBefore(o.default.create(r),this.next),e={startNode:r,startOffset:n.length}));return t.data="\uFEFF",e}},{key:"update",value:function(t,e){var r=this;t.forEach(function(t){if("characterData"===t.type&&(t.target===r.leftGuard||t.target===r.rightGuard)){var n=r.restore(t.target);n&&(e.range=n)}})}}]),Embed}(o.default.Embed);e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignStyle=e.AlignClass=e.AlignAttribute=void 0;var n,o=(n=r(0))&&n.__esModule?n:{default:n},i={scope:o.default.Scope.BLOCK,whitelist:["right","center","justify"]},l=new o.default.Attributor.Attribute("align","align",i),a=new o.default.Attributor.Class("align","ql-align",i),s=new o.default.Attributor.Style("align","text-align",i);e.AlignAttribute=l,e.AlignClass=a,e.AlignStyle=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BackgroundStyle=e.BackgroundClass=void 0;var n,o=(n=r(0))&&n.__esModule?n:{default:n},i=r(26),l=new o.default.Attributor.Class("background","ql-bg",{scope:o.default.Scope.INLINE}),a=new i.ColorAttributor("background","background-color",{scope:o.default.Scope.INLINE});e.BackgroundClass=l,e.BackgroundStyle=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DirectionStyle=e.DirectionClass=e.DirectionAttribute=void 0;var n,o=(n=r(0))&&n.__esModule?n:{default:n},i={scope:o.default.Scope.BLOCK,whitelist:["rtl"]},l=new o.default.Attributor.Attribute("direction","dir",i),a=new o.default.Attributor.Class("direction","ql-direction",i),s=new o.default.Attributor.Style("direction","direction",i);e.DirectionAttribute=l,e.DirectionClass=a,e.DirectionStyle=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FontClass=e.FontStyle=void 0;var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=(n=r(0))&&n.__esModule?n:{default:n},l={scope:i.default.Scope.INLINE,whitelist:["serif","monospace"]},a=new i.default.Attributor.Class("font","ql-font",l),s=new(function(t){function FontStyleAttributor(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,FontStyleAttributor),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(FontStyleAttributor.__proto__||Object.getPrototypeOf(FontStyleAttributor)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(FontStyleAttributor,t),o(FontStyleAttributor,[{key:"value",value:function(t){return _get(FontStyleAttributor.prototype.__proto__||Object.getPrototypeOf(FontStyleAttributor.prototype),"value",this).call(this,t).replace(/["']/g,"")}}]),FontStyleAttributor}(i.default.Attributor.Style))("font","font-family",l);e.FontStyle=s,e.FontClass=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SizeStyle=e.SizeClass=void 0;var n,o=(n=r(0))&&n.__esModule?n:{default:n},i=new o.default.Attributor.Class("size","ql-size",{scope:o.default.Scope.INLINE,whitelist:["small","large","huge"]}),l=new o.default.Attributor.Style("size","font-size",{scope:o.default.Scope.INLINE,whitelist:["10px","18px","32px"]});e.SizeClass=i,e.SizeStyle=l},function(t,e,r){"use strict";t.exports={align:{"":r(76),center:r(77),right:r(78),justify:r(79)},background:r(80),blockquote:r(81),bold:r(82),clean:r(83),code:r(58),"code-block":r(58),color:r(84),direction:{"":r(85),rtl:r(86)},float:{center:r(87),full:r(88),left:r(89),right:r(90)},formula:r(91),header:{1:r(92),2:r(93)},italic:r(94),image:r(95),indent:{"+1":r(96),"-1":r(97)},link:r(98),list:{ordered:r(99),bullet:r(100),check:r(101)},script:{sub:r(102),super:r(103)},strike:r(104),underline:r(105),video:r(106)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getLastChangeIndex=e.default=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(5));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function History(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,History);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(History.__proto__||Object.getPrototypeOf(History)).call(this,t,e));return r.lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(i.default.events.EDITOR_CHANGE,function(t,e,n,o){t!==i.default.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&o!==i.default.sources.USER?r.transform(e):r.record(e,n))}),r.quill.keyboard.addBinding({key:"Z",shortKey:!0},r.undo.bind(r)),r.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},r.redo.bind(r)),/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"Y",shortKey:!0},r.redo.bind(r)),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(History,t),n(History,[{key:"change",value:function(t,e){if(0!==this.stack[t].length){var r=this.stack[t].pop();this.stack[e].push(r),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r[t],i.default.sources.USER),this.ignoreChange=!1;var n=getLastChangeIndex(r[t]);this.quill.setSelection(n)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(t,e){if(0!==t.ops.length){this.stack.redo=[];var r=this.quill.getContents().diff(e),n=Date.now();if(this.lastRecorded+this.options.delay>n&&this.stack.undo.length>0){var o=this.stack.undo.pop();r=r.compose(o.undo),t=o.redo.compose(t)}else this.lastRecorded=n;this.stack.undo.push({redo:t,undo:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(t){this.stack.undo.forEach(function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)}),this.stack.redo.forEach(function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),History}(_interopRequireDefault(r(9)).default);function getLastChangeIndex(t){var e,r=t.reduce(function(t,e){return t+(e.delete||0)},0),n=t.length()-r;return null!=(e=t.ops[t.ops.length-1])&&(null!=e.insert?"string"==typeof e.insert&&e.insert.endsWith("\n"):null!=e.attributes&&Object.keys(e.attributes).some(function(t){return null!=o.default.query(t,o.default.Scope.BLOCK)}))&&(n-=1),n}l.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},e.default=l,e.getLastChangeIndex=getLastChangeIndex},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BaseTooltip=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(3)),i=_interopRequireDefault(r(2)),l=_interopRequireDefault(r(8)),a=_interopRequireDefault(r(23)),s=_interopRequireDefault(r(34)),u=_interopRequireDefault(r(59)),f=_interopRequireDefault(r(60)),p=_interopRequireDefault(r(28)),d=_interopRequireDefault(r(61));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var h=[!1,"center","right","justify"],y=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],b=[!1,"serif","monospace"],v=["1","2","3",!1],g=["small",!1,"large","huge"],m=function(t){function BaseTheme(t,e){_classCallCheck(this,BaseTheme);var r=_possibleConstructorReturn(this,(BaseTheme.__proto__||Object.getPrototypeOf(BaseTheme)).call(this,t,e));return t.emitter.listenDOM("click",document.body,function listener(e){if(!document.body.contains(t.root))return document.body.removeEventListener("click",listener);null==r.tooltip||r.tooltip.root.contains(e.target)||document.activeElement===r.tooltip.textbox||r.quill.hasFocus()||r.tooltip.hide(),null!=r.pickers&&r.pickers.forEach(function(t){t.container.contains(e.target)||t.close()})}),r}return _inherits(BaseTheme,t),n(BaseTheme,[{key:"addModule",value:function(t){var e=_get(BaseTheme.prototype.__proto__||Object.getPrototypeOf(BaseTheme.prototype),"addModule",this).call(this,t);return"toolbar"===t&&this.extendToolbar(e),e}},{key:"buildButtons",value:function(t,e){t.forEach(function(t){(t.getAttribute("class")||"").split(/\s+/).forEach(function(r){if(r.startsWith("ql-")&&null!=e[r=r.slice(3)]){if("direction"===r)t.innerHTML=e[r][""]+e[r].rtl;else if("string"==typeof e[r])t.innerHTML=e[r];else{var n=t.value||"";null!=n&&e[r][n]&&(t.innerHTML=e[r][n])}}})})}},{key:"buildPickers",value:function(t,e){var r=this;this.pickers=t.map(function(t){if(t.classList.contains("ql-align"))return null==t.querySelector("option")&&fillSelect(t,h),new f.default(t,e.align);if(!(t.classList.contains("ql-background")||t.classList.contains("ql-color")))return null==t.querySelector("option")&&(t.classList.contains("ql-font")?fillSelect(t,b):t.classList.contains("ql-header")?fillSelect(t,v):t.classList.contains("ql-size")&&fillSelect(t,g)),new p.default(t);var r=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&fillSelect(t,y,"background"===r?"#ffffff":"#000000"),new u.default(t,e[r])}),this.quill.on(l.default.events.EDITOR_CHANGE,function(){r.pickers.forEach(function(t){t.update()})})}}]),BaseTheme}(s.default);m.DEFAULTS=(0,o.default)(!0,{},s.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var t=this,e=this.container.querySelector("input.ql-image[type=file]");null==e&&((e=document.createElement("input")).setAttribute("type","file"),e.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),e.classList.add("ql-image"),e.addEventListener("change",function(){if(null!=e.files&&null!=e.files[0]){var r=new FileReader;r.onload=function(r){var n=t.quill.getSelection(!0);t.quill.updateContents(new i.default().retain(n.index).delete(n.length).insert({image:r.target.result}),l.default.sources.USER),t.quill.setSelection(n.index+1,l.default.sources.SILENT),e.value=""},r.readAsDataURL(e.files[0])}}),this.container.appendChild(e)),e.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var _=function(t){function BaseTooltip(t,e){_classCallCheck(this,BaseTooltip);var r=_possibleConstructorReturn(this,(BaseTooltip.__proto__||Object.getPrototypeOf(BaseTooltip)).call(this,t,e));return r.textbox=r.root.querySelector('input[type="text"]'),r.listen(),r}return _inherits(BaseTooltip,t),n(BaseTooltip,[{key:"listen",value:function(){var t=this;this.textbox.addEventListener("keydown",function(e){a.default.match(e,"enter")?(t.save(),e.preventDefault()):a.default.match(e,"escape")&&(t.cancel(),e.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+t)||""),this.root.setAttribute("data-mode",t)}},{key:"restoreFocus",value:function(){var t=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=t}},{key:"save",value:function(){var t,e,r=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":var n=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",r,l.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",r,l.default.sources.USER)),this.quill.root.scrollTop=n;break;case"video":r=(e=(t=r).match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/))?(e[1]||"https")+"://www.youtube.com/embed/"+e[2]+"?showinfo=0":(e=t.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(e[1]||"https")+"://player.vimeo.com/video/"+e[2]+"/":t;case"formula":if(!r)break;var o=this.quill.getSelection(!0);if(null!=o){var i=o.index+o.length;this.quill.insertEmbed(i,this.root.getAttribute("data-mode"),r,l.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(i+1," ",l.default.sources.USER),this.quill.setSelection(i+2,l.default.sources.USER)}}this.textbox.value="",this.hide()}}]),BaseTooltip}(d.default);function fillSelect(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach(function(e){var n=document.createElement("option");e===r?n.setAttribute("selected","selected"):n.setAttribute("value",e),t.appendChild(n)})}e.BaseTooltip=_,e.default=m},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function LinkedList(){this.head=this.tail=null,this.length=0}return LinkedList.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.insertBefore(t[0],null),t.length>1&&this.append.apply(this,t.slice(1))},LinkedList.prototype.contains=function(t){for(var e,r=this.iterator();e=r();)if(e===t)return!0;return!1},LinkedList.prototype.insertBefore=function(t,e){t&&(t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},LinkedList.prototype.offset=function(t){for(var e=0,r=this.head;null!=r;){if(r===t)return e;e+=r.length(),r=r.next}return -1},LinkedList.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)},LinkedList.prototype.iterator=function(t){return void 0===t&&(t=this.head),function(){var e=t;return null!=t&&(t=t.next),e}},LinkedList.prototype.find=function(t,e){void 0===e&&(e=!1);for(var r,n=this.iterator();r=n();){var o=r.length();if(t<o||e&&t===o&&(null==r.next||0!==r.next.length()))return[r,t];t-=o}return[null,0]},LinkedList.prototype.forEach=function(t){for(var e,r=this.iterator();e=r();)t(e)},LinkedList.prototype.forEachAt=function(t,e,r){if(!(e<=0))for(var n,o=this.find(t),i=o[0],l=t-o[1],a=this.iterator(i);(n=a())&&l<t+e;){var s=n.length();t>l?r(n,t-l,Math.min(e,l+s-t)):r(n,0,Math.min(s,t+e-l)),l+=s}},LinkedList.prototype.map=function(t){return this.reduce(function(e,r){return e.push(t(r)),e},[])},LinkedList.prototype.reduce=function(t,e){for(var r,n=this.iterator();r=n();)e=t(e,r);return e},LinkedList}();e.default=n},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(17),l=r(1),a={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},s=function(t){function ScrollBlot(e){var r=t.call(this,e)||this;return r.scroll=r,r.observer=new MutationObserver(function(t){r.update(t)}),r.observer.observe(r.domNode,a),r.attach(),r}return o(ScrollBlot,t),ScrollBlot.prototype.detach=function(){t.prototype.detach.call(this),this.observer.disconnect()},ScrollBlot.prototype.deleteAt=function(e,r){this.update(),0===e&&r===this.length()?this.children.forEach(function(t){t.remove()}):t.prototype.deleteAt.call(this,e,r)},ScrollBlot.prototype.formatAt=function(e,r,n,o){this.update(),t.prototype.formatAt.call(this,e,r,n,o)},ScrollBlot.prototype.insertAt=function(e,r,n){this.update(),t.prototype.insertAt.call(this,e,r,n)},ScrollBlot.prototype.optimize=function(e,r){var n=this;void 0===e&&(e=[]),void 0===r&&(r={}),t.prototype.optimize.call(this,r);for(var o=[].slice.call(this.observer.takeRecords());o.length>0;)e.push(o.pop());for(var mark=function(t,e){void 0===e&&(e=!0),null!=t&&t!==n&&null!=t.domNode.parentNode&&(null==t.domNode[l.DATA_KEY].mutations&&(t.domNode[l.DATA_KEY].mutations=[]),e&&mark(t.parent))},optimize=function(t){null!=t.domNode[l.DATA_KEY]&&null!=t.domNode[l.DATA_KEY].mutations&&(t instanceof i.default&&t.children.forEach(optimize),t.optimize(r))},a=e,s=0;a.length>0;s+=1){if(s>=100)throw Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(function(t){var e=l.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(mark(l.find(t.previousSibling,!1)),[].forEach.call(t.addedNodes,function(t){var e=l.find(t,!1);mark(e,!1),e instanceof i.default&&e.children.forEach(function(t){mark(t,!1)})})):"attributes"===t.type&&mark(e.prev)),mark(e))}),this.children.forEach(optimize),o=(a=[].slice.call(this.observer.takeRecords())).slice();o.length>0;)e.push(o.pop())}},ScrollBlot.prototype.update=function(e,r){var n=this;void 0===r&&(r={}),(e=e||this.observer.takeRecords()).map(function(t){var e=l.find(t.target,!0);return null==e?null:null==e.domNode[l.DATA_KEY].mutations?(e.domNode[l.DATA_KEY].mutations=[t],e):(e.domNode[l.DATA_KEY].mutations.push(t),null)}).forEach(function(t){null!=t&&t!==n&&null!=t.domNode[l.DATA_KEY]&&t.update(t.domNode[l.DATA_KEY].mutations||[],r)}),null!=this.domNode[l.DATA_KEY].mutations&&t.prototype.update.call(this,this.domNode[l.DATA_KEY].mutations,r),this.optimize(e,r)},ScrollBlot.blotName="scroll",ScrollBlot.defaultChild="block",ScrollBlot.scope=l.Scope.BLOCK_BLOT,ScrollBlot.tagName="DIV",ScrollBlot}(i.default);e.default=s},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(18),l=r(1),a=function(t){function InlineBlot(){return null!==t&&t.apply(this,arguments)||this}return o(InlineBlot,t),InlineBlot.formats=function(e){if(e.tagName!==InlineBlot.tagName)return t.formats.call(this,e)},InlineBlot.prototype.format=function(e,r){var n=this;e!==this.statics.blotName||r?t.prototype.format.call(this,e,r):(this.children.forEach(function(t){t instanceof i.default||(t=t.wrap(InlineBlot.blotName,!0)),n.attributes.copy(t)}),this.unwrap())},InlineBlot.prototype.formatAt=function(e,r,n,o){null!=this.formats()[n]||l.query(n,l.Scope.ATTRIBUTE)?this.isolate(e,r).format(n,o):t.prototype.formatAt.call(this,e,r,n,o)},InlineBlot.prototype.optimize=function(e){t.prototype.optimize.call(this,e);var r=this.formats();if(0===Object.keys(r).length)return this.unwrap();var n=this.next;n instanceof InlineBlot&&n.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var r in t)if(t[r]!==e[r])return!1;return!0}(r,n.formats())&&(n.moveChildren(this),n.remove())},InlineBlot.blotName="inline",InlineBlot.scope=l.Scope.INLINE_BLOT,InlineBlot.tagName="SPAN",InlineBlot}(i.default);e.default=a},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(18),l=r(1),a=function(t){function BlockBlot(){return null!==t&&t.apply(this,arguments)||this}return o(BlockBlot,t),BlockBlot.formats=function(e){var r=l.query(BlockBlot.blotName).tagName;if(e.tagName!==r)return t.formats.call(this,e)},BlockBlot.prototype.format=function(e,r){null!=l.query(e,l.Scope.BLOCK)&&(e!==this.statics.blotName||r?t.prototype.format.call(this,e,r):this.replaceWith(BlockBlot.blotName))},BlockBlot.prototype.formatAt=function(e,r,n,o){null!=l.query(n,l.Scope.BLOCK)?this.format(n,o):t.prototype.formatAt.call(this,e,r,n,o)},BlockBlot.prototype.insertAt=function(e,r,n){if(null==n||null!=l.query(r,l.Scope.INLINE))t.prototype.insertAt.call(this,e,r,n);else{var o=this.split(e),i=l.create(r,n);o.parent.insertBefore(i,o)}},BlockBlot.prototype.update=function(e,r){navigator.userAgent.match(/Trident/)?this.build():t.prototype.update.call(this,e,r)},BlockBlot.blotName="block",BlockBlot.scope=l.Scope.BLOCK_BLOT,BlockBlot.tagName="P",BlockBlot}(i.default);e.default=a},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function EmbedBlot(){return null!==t&&t.apply(this,arguments)||this}return o(EmbedBlot,t),EmbedBlot.formats=function(t){},EmbedBlot.prototype.format=function(e,r){t.prototype.formatAt.call(this,0,this.length(),e,r)},EmbedBlot.prototype.formatAt=function(e,r,n,o){0===e&&r===this.length()?this.format(n,o):t.prototype.formatAt.call(this,e,r,n,o)},EmbedBlot.prototype.formats=function(){return this.statics.formats(this.domNode)},EmbedBlot}(r(19).default);e.default=i},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function __(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)});Object.defineProperty(e,"__esModule",{value:!0});var i=r(19),l=r(1),a=function(t){function TextBlot(e){var r=t.call(this,e)||this;return r.text=r.statics.value(r.domNode),r}return o(TextBlot,t),TextBlot.create=function(t){return document.createTextNode(t)},TextBlot.value=function(t){var e=t.data;return e.normalize&&(e=e.normalize()),e},TextBlot.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},TextBlot.prototype.index=function(t,e){return this.domNode===t?e:-1},TextBlot.prototype.insertAt=function(e,r,n){null==n?(this.text=this.text.slice(0,e)+r+this.text.slice(e),this.domNode.data=this.text):t.prototype.insertAt.call(this,e,r,n)},TextBlot.prototype.length=function(){return this.text.length},TextBlot.prototype.optimize=function(e){t.prototype.optimize.call(this,e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof TextBlot&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},TextBlot.prototype.position=function(t,e){return void 0===e&&(e=!1),[this.domNode,t]},TextBlot.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var r=l.create(this.domNode.splitText(t));return this.parent.insertBefore(r,this.next),this.text=this.statics.value(this.domNode),r},TextBlot.prototype.update=function(t,e){var r=this;t.some(function(t){return"characterData"===t.type&&t.target===r.domNode})&&(this.text=this.statics.value(this.domNode))},TextBlot.prototype.value=function(){return this.text},TextBlot.blotName="text",TextBlot.scope=l.Scope.INLINE_BLOT,TextBlot}(i.default);e.default=a},function(t,e,r){"use strict";var n=document.createElement("div");if(n.classList.toggle("test-class",!1),n.classList.contains("test-class")){var o=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(t,e){return arguments.length>1&&!this.contains(t)==!e?e:o.call(this,t)}}String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.substr(e,t.length)===t}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var r=this.toString();("number"!=typeof e||!isFinite(e)||Math.floor(e)!==e||e>r.length)&&(e=r.length),e-=t.length;var n=r.indexOf(t,e);return -1!==n&&n===e}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(this===null)throw TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof t)throw TypeError("predicate must be a function");for(var e,r=Object(this),n=r.length>>>0,o=arguments[1],i=0;i<n;i++)if(e=r[i],t.call(o,e,i,r))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(t,e){function diff_main(t,e,r){if(t==e)return t?[[0,t]]:[];(r<0||t.length<r)&&(r=null);var n=diff_commonPrefix(t,e),o=t.substring(0,n);n=diff_commonSuffix(t=t.substring(n),e=e.substring(n));var i=t.substring(t.length-n),l=function(t,e){if(!t)return[[1,e]];if(!e)return[[-1,t]];var r,n=t.length>e.length?t:e,o=t.length>e.length?e:t,i=n.indexOf(o);if(-1!=i)return r=[[1,n.substring(0,i)],[0,o],[1,n.substring(i+o.length)]],t.length>e.length&&(r[0][0]=r[2][0]=-1),r;if(1==o.length)return[[-1,t],[1,e]];var l=function(t,e){var r,n,o,i,l,a=t.length>e.length?t:e,s=t.length>e.length?e:t;if(a.length<4||2*s.length<a.length)return null;function diff_halfMatchI_(t,e,r){for(var n,o,i,l,a=t.substring(r,r+Math.floor(t.length/4)),s=-1,u="";-1!=(s=e.indexOf(a,s+1));){var f=diff_commonPrefix(t.substring(r),e.substring(s)),p=diff_commonSuffix(t.substring(0,r),e.substring(0,s));u.length<p+f&&(u=e.substring(s-p,s)+e.substring(s,s+f),n=t.substring(0,r-p),o=t.substring(r+f),i=e.substring(0,s-p),l=e.substring(s+f))}return 2*u.length>=t.length?[n,o,i,l,u]:null}var u=diff_halfMatchI_(a,s,Math.ceil(a.length/4)),f=diff_halfMatchI_(a,s,Math.ceil(a.length/2));return u||f?(r=f?u&&u[4].length>f[4].length?u:f:u,t.length>e.length?(n=r[0],o=r[1],i=r[2],l=r[3]):(i=r[0],l=r[1],n=r[2],o=r[3]),[n,o,i,l,r[4]]):null}(t,e);if(l){var a=l[0],s=l[1],u=l[2],f=l[3],p=l[4],d=diff_main(a,u),h=diff_main(s,f);return d.concat([[0,p]],h)}return function(t,e){for(var r=t.length,n=e.length,o=Math.ceil((r+n)/2),i=2*o,l=Array(i),a=Array(i),s=0;s<i;s++)l[s]=-1,a[s]=-1;l[o+1]=0,a[o+1]=0;for(var u=r-n,f=u%2!=0,p=0,d=0,h=0,y=0,b=0;b<o;b++){for(var v=-b+p;v<=b-d;v+=2){for(var g,m=o+v,_=(g=v==-b||v!=b&&l[m-1]<l[m+1]?l[m+1]:l[m-1]+1)-v;g<r&&_<n&&t.charAt(g)==e.charAt(_);)g++,_++;if(l[m]=g,g>r)d+=2;else if(_>n)p+=2;else if(f){var k=o+u-v;if(k>=0&&k<i&&-1!=a[k]){var O=r-a[k];if(g>=O)return diff_bisectSplit_(t,e,g,_)}}}for(var E=-b+h;E<=b-y;E+=2){for(var O,k=o+E,x=(O=E==-b||E!=b&&a[k-1]<a[k+1]?a[k+1]:a[k-1]+1)-E;O<r&&x<n&&t.charAt(r-O-1)==e.charAt(n-x-1);)O++,x++;if(a[k]=O,O>r)y+=2;else if(x>n)h+=2;else if(!f){var m=o+u-E;if(m>=0&&m<i&&-1!=l[m]){var g=l[m],_=o+g-m;if(g>=(O=r-O))return diff_bisectSplit_(t,e,g,_)}}}}return[[-1,t],[1,e]]}(t,e)}(t=t.substring(0,t.length-n),e=e.substring(0,e.length-n));return o&&l.unshift([0,o]),i&&l.push([0,i]),function diff_cleanupMerge(t){t.push([0,""]);for(var e,r=0,n=0,o=0,i="",l="";r<t.length;)switch(t[r][0]){case 1:o++,l+=t[r][1],r++;break;case -1:n++,i+=t[r][1],r++;break;case 0:n+o>1?(0!==n&&0!==o&&(0!==(e=diff_commonPrefix(l,i))&&(r-n-o>0&&0==t[r-n-o-1][0]?t[r-n-o-1][1]+=l.substring(0,e):(t.splice(0,0,[0,l.substring(0,e)]),r++),l=l.substring(e),i=i.substring(e)),0!==(e=diff_commonSuffix(l,i))&&(t[r][1]=l.substring(l.length-e)+t[r][1],l=l.substring(0,l.length-e),i=i.substring(0,i.length-e))),0===n?t.splice(r-o,n+o,[1,l]):0===o?t.splice(r-n,n+o,[-1,i]):t.splice(r-n-o,n+o,[-1,i],[1,l]),r=r-n-o+(n?1:0)+(o?1:0)+1):0!==r&&0==t[r-1][0]?(t[r-1][1]+=t[r][1],t.splice(r,1)):r++,o=0,n=0,i="",l=""}""===t[t.length-1][1]&&t.pop();var a=!1;for(r=1;r<t.length-1;)0==t[r-1][0]&&0==t[r+1][0]&&(t[r][1].substring(t[r][1].length-t[r-1][1].length)==t[r-1][1]?(t[r][1]=t[r-1][1]+t[r][1].substring(0,t[r][1].length-t[r-1][1].length),t[r+1][1]=t[r-1][1]+t[r+1][1],t.splice(r-1,1),a=!0):t[r][1].substring(0,t[r+1][1].length)==t[r+1][1]&&(t[r-1][1]+=t[r+1][1],t[r][1]=t[r][1].substring(t[r+1][1].length)+t[r+1][1],t.splice(r+1,1),a=!0)),r++;a&&diff_cleanupMerge(t)}(l),null!=r&&(l=function(t,e){var r=function(t,e){if(0===e)return[0,t];for(var r=0,n=0;n<t.length;n++){var o=t[n];if(-1===o[0]||0===o[0]){var i=r+o[1].length;if(e===i)return[n+1,t];if(e<i){t=t.slice();var l=e-r,a=[o[0],o[1].slice(0,l)],s=[o[0],o[1].slice(l)];return t.splice(n,1,a,s),[n+1,t]}r=i}}throw Error("cursor_pos is out of bounds!")}(t,e),n=r[1],o=r[0],i=n[o],l=n[o+1];if(null==i||0!==i[0])return t;if(null!=l&&i[1]+l[1]===l[1]+i[1])return n.splice(o,2,l,i),merge_tuples(n,o,2);if(null==l||0!==l[1].indexOf(i[1]))return t;n.splice(o,2,[l[0],i[1]],[0,i[1]]);var a=l[1].slice(i[1].length);return a.length>0&&n.splice(o+2,0,[l[0],a]),merge_tuples(n,o,3)}(l,r)),l=function(t){for(var e,r=!1,starts_with_pair_end=function(t){return t.charCodeAt(0)>=56320&&57343>=t.charCodeAt(0)},n=2;n<t.length;n+=1)0===t[n-2][0]&&(e=t[n-2][1]).charCodeAt(e.length-1)>=55296&&56319>=e.charCodeAt(e.length-1)&&-1===t[n-1][0]&&starts_with_pair_end(t[n-1][1])&&1===t[n][0]&&starts_with_pair_end(t[n][1])&&(r=!0,t[n-1][1]=t[n-2][1].slice(-1)+t[n-1][1],t[n][1]=t[n-2][1].slice(-1)+t[n][1],t[n-2][1]=t[n-2][1].slice(0,-1));if(!r)return t;for(var o=[],n=0;n<t.length;n+=1)t[n][1].length>0&&o.push(t[n]);return o}(l)}function diff_bisectSplit_(t,e,r,n){var o=t.substring(0,r),i=e.substring(0,n),l=t.substring(r),a=e.substring(n),s=diff_main(o,i),u=diff_main(l,a);return s.concat(u)}function diff_commonPrefix(t,e){if(!t||!e||t.charAt(0)!=e.charAt(0))return 0;for(var r=0,n=Math.min(t.length,e.length),o=n,i=0;r<o;)t.substring(i,o)==e.substring(i,o)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return o}function diff_commonSuffix(t,e){if(!t||!e||t.charAt(t.length-1)!=e.charAt(e.length-1))return 0;for(var r=0,n=Math.min(t.length,e.length),o=n,i=0;r<o;)t.substring(t.length-o,t.length-i)==e.substring(e.length-o,e.length-i)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return o}function merge_tuples(t,e,r){for(var n=e+r-1;n>=0&&n>=e-1;n--)if(n+1<t.length){var o=t[n],i=t[n+1];o[0]===i[1]&&t.splice(n,2,[o[0],o[1]+i[1]])}return t}diff_main.INSERT=1,diff_main.DELETE=-1,diff_main.EQUAL=0,t.exports=diff_main},function(t,e){(t.exports="function"==typeof Object.keys?Object.keys:shim).shim=shim;function shim(t){var e=[];for(var r in t)e.push(r);return e}},function(t,e){var r="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();function supported(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function unsupported(t){return t&&"object"==typeof t&&"number"==typeof t.length&&Object.prototype.hasOwnProperty.call(t,"callee")&&!Object.prototype.propertyIsEnumerable.call(t,"callee")||!1}(e=t.exports=r?supported:unsupported).supported=supported,e.unsupported=unsupported},function(t,e){"use strict";var r=Object.prototype.hasOwnProperty,n="~";function Events(){}function EE(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),new Events().__proto__||(n=!1)),EventEmitter.prototype.eventNames=function(){var t,e,o=[];if(0===this._eventsCount)return o;for(e in t=this._events)r.call(t,e)&&o.push(n?e.slice(1):e);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},EventEmitter.prototype.listeners=function(t,e){var r=n?n+t:t,o=this._events[r];if(e)return!!o;if(!o)return[];if(o.fn)return[o.fn];for(var i=0,l=o.length,a=Array(l);i<l;i++)a[i]=o[i].fn;return a},EventEmitter.prototype.emit=function(t,e,r,o,i,l){var a=n?n+t:t;if(!this._events[a])return!1;var s,u,f=this._events[a],p=arguments.length;if(f.fn){switch(f.once&&this.removeListener(t,f.fn,void 0,!0),p){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,e),!0;case 3:return f.fn.call(f.context,e,r),!0;case 4:return f.fn.call(f.context,e,r,o),!0;case 5:return f.fn.call(f.context,e,r,o,i),!0;case 6:return f.fn.call(f.context,e,r,o,i,l),!0}for(u=1,s=Array(p-1);u<p;u++)s[u-1]=arguments[u];f.fn.apply(f.context,s)}else{var d,h=f.length;for(u=0;u<h;u++)switch(f[u].once&&this.removeListener(t,f[u].fn,void 0,!0),p){case 1:f[u].fn.call(f[u].context);break;case 2:f[u].fn.call(f[u].context,e);break;case 3:f[u].fn.call(f[u].context,e,r);break;case 4:f[u].fn.call(f[u].context,e,r,o);break;default:if(!s)for(d=1,s=Array(p-1);d<p;d++)s[d-1]=arguments[d];f[u].fn.apply(f[u].context,s)}}return!0},EventEmitter.prototype.on=function(t,e,r){var o=new EE(e,r||this),i=n?n+t:t;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],o]:this._events[i].push(o):(this._events[i]=o,this._eventsCount++),this},EventEmitter.prototype.once=function(t,e,r){var o=new EE(e,r||this,!0),i=n?n+t:t;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],o]:this._events[i].push(o):(this._events[i]=o,this._eventsCount++),this},EventEmitter.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return 0==--this._eventsCount?this._events=new Events:delete this._events[i],this;var l=this._events[i];if(l.fn)l.fn!==e||o&&!l.once||r&&l.context!==r||(0==--this._eventsCount?this._events=new Events:delete this._events[i]);else{for(var a=0,s=[],u=l.length;a<u;a++)(l[a].fn!==e||o&&!l[a].once||r&&l[a].context!==r)&&s.push(l[a]);s.length?this._events[i]=1===s.length?s[0]:s:0==--this._eventsCount?this._events=new Events:delete this._events[i]}return this},EventEmitter.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&(0==--this._eventsCount?this._events=new Events:delete this._events[e])):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prototype.setMaxListeners=function(){return this},EventEmitter.prefixed=n,EventEmitter.EventEmitter=EventEmitter,void 0!==t&&(t.exports=EventEmitter)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.matchText=e.matchSpacing=e.matchNewline=e.matchBlot=e.matchAttributor=e.default=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),i=_interopRequireDefault(r(3)),l=_interopRequireDefault(r(2)),a=_interopRequireDefault(r(0)),s=_interopRequireDefault(r(5)),u=_interopRequireDefault(r(10)),f=_interopRequireDefault(r(9)),p=r(36),d=r(37),h=_interopRequireDefault(r(13)),y=r(26),b=r(38),v=r(39),g=r(40);function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var m=(0,u.default)("quill:clipboard"),_="__ql-matcher",k=[[Node.TEXT_NODE,matchText],[Node.TEXT_NODE,matchNewline],["br",function(t,e){return deltaEndsWith(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,matchNewline],[Node.ELEMENT_NODE,matchBlot],[Node.ELEMENT_NODE,matchSpacing],[Node.ELEMENT_NODE,matchAttributor],[Node.ELEMENT_NODE,function(t,e){var r={},n=t.style||{};return n.fontStyle&&"italic"===computeStyle(t).fontStyle&&(r.italic=!0),n.fontWeight&&(computeStyle(t).fontWeight.startsWith("bold")||parseInt(computeStyle(t).fontWeight)>=700)&&(r.bold=!0),Object.keys(r).length>0&&(e=applyFormat(e,r)),parseFloat(n.textIndent||0)>0&&(e=new l.default().insert("	").concat(e)),e}],["li",function(t,e){var r=a.default.query(t);if(null==r||"list-item"!==r.blotName||!deltaEndsWith(e,"\n"))return e;for(var n=-1,o=t.parentNode;!o.classList.contains("ql-clipboard");)"list"===(a.default.query(o)||{}).blotName&&(n+=1),o=o.parentNode;return n<=0?e:e.compose(new l.default().retain(e.length()-1).retain(1,{indent:n}))}],["b",matchAlias.bind(matchAlias,"bold")],["i",matchAlias.bind(matchAlias,"italic")],["style",function(){return new l.default}]],O=[p.AlignAttribute,b.DirectionAttribute].reduce(function(t,e){return t[e.keyName]=e,t},{}),E=[p.AlignStyle,d.BackgroundStyle,y.ColorStyle,b.DirectionStyle,v.FontStyle,g.SizeStyle].reduce(function(t,e){return t[e.keyName]=e,t},{}),x=function(t){function Clipboard(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Clipboard);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Clipboard.__proto__||Object.getPrototypeOf(Clipboard)).call(this,t,e));return r.quill.root.addEventListener("paste",r.onPaste.bind(r)),r.container=r.quill.addContainer("ql-clipboard"),r.container.setAttribute("contenteditable",!0),r.container.setAttribute("tabindex",-1),r.matchers=[],k.concat(r.options.matchers).forEach(function(t){var n=_slicedToArray(t,2),o=n[0],i=n[1];(e.matchVisual||i!==matchSpacing)&&r.addMatcher(o,i)}),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Clipboard,t),o(Clipboard,[{key:"addMatcher",value:function(t,e){this.matchers.push([t,e])}},{key:"convert",value:function(t){if("string"==typeof t)return this.container.innerHTML=t.replace(/\>\r?\n +\</g,"><"),this.convert();var e=this.quill.getFormat(this.quill.selection.savedRange.index);if(e[h.default.blotName]){var r=this.container.innerText;return this.container.innerHTML="",new l.default().insert(r,_defineProperty({},h.default.blotName,e[h.default.blotName]))}var n=_slicedToArray(this.prepareMatching(),2),o=n[0],i=n[1],a=function traverse(t,e,r){return t.nodeType===t.TEXT_NODE?r.reduce(function(e,r){return r(t,e)},new l.default):t.nodeType===t.ELEMENT_NODE?[].reduce.call(t.childNodes||[],function(n,o){var i=traverse(o,e,r);return o.nodeType===t.ELEMENT_NODE&&(i=e.reduce(function(t,e){return e(o,t)},i),i=(o[_]||[]).reduce(function(t,e){return e(o,t)},i)),n.concat(i)},new l.default):new l.default}(this.container,o,i);return deltaEndsWith(a,"\n")&&null==a.ops[a.ops.length-1].attributes&&(a=a.compose(new l.default().retain(a.length()-1).delete(1))),m.log("convert",this.container.innerHTML,a),this.container.innerHTML="",a}},{key:"dangerouslyPasteHTML",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.default.sources.API;if("string"==typeof t)this.quill.setContents(this.convert(t),e),this.quill.setSelection(0,s.default.sources.SILENT);else{var n=this.convert(e);this.quill.updateContents(new l.default().retain(t).concat(n),r),this.quill.setSelection(t+n.length(),s.default.sources.SILENT)}}},{key:"onPaste",value:function(t){var e=this;if(!t.defaultPrevented&&this.quill.isEnabled()){var r=this.quill.getSelection(),n=new l.default().retain(r.index),o=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(s.default.sources.SILENT),setTimeout(function(){n=n.concat(e.convert()).delete(r.length),e.quill.updateContents(n,s.default.sources.USER),e.quill.setSelection(n.length()-r.length,s.default.sources.SILENT),e.quill.scrollingContainer.scrollTop=o,e.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var t=this,e=[],r=[];return this.matchers.forEach(function(n){var o=_slicedToArray(n,2),i=o[0],l=o[1];switch(i){case Node.TEXT_NODE:r.push(l);break;case Node.ELEMENT_NODE:e.push(l);break;default:[].forEach.call(t.container.querySelectorAll(i),function(t){t[_]=t[_]||[],t[_].push(l)})}}),[e,r]}}]),Clipboard}(f.default);function applyFormat(t,e,r){return(void 0===e?"undefined":n(e))==="object"?Object.keys(e).reduce(function(t,r){return applyFormat(t,r,e[r])},t):t.reduce(function(t,n){return n.attributes&&n.attributes[e]?t.push(n):t.insert(n.insert,(0,i.default)({},_defineProperty({},e,r),n.attributes))},new l.default)}function computeStyle(t){if(t.nodeType!==Node.ELEMENT_NODE)return{};var e="__ql-computed-style";return t[e]||(t[e]=window.getComputedStyle(t))}function deltaEndsWith(t,e){for(var r="",n=t.ops.length-1;n>=0&&r.length<e.length;--n){var o=t.ops[n];if("string"!=typeof o.insert)break;r=o.insert+r}return r.slice(-1*e.length)===e}function isLine(t){return 0!==t.childNodes.length&&["block","list-item"].indexOf(computeStyle(t).display)>-1}function matchAlias(t,e,r){return applyFormat(r,t,!0)}function matchAttributor(t,e){var r=a.default.Attributor.Attribute.keys(t),n=a.default.Attributor.Class.keys(t),o=a.default.Attributor.Style.keys(t),i={};return r.concat(n).concat(o).forEach(function(e){var r=a.default.query(e,a.default.Scope.ATTRIBUTE);null!=r&&(i[r.attrName]=r.value(t),i[r.attrName])||(null!=(r=O[e])&&(r.attrName===e||r.keyName===e)&&(i[r.attrName]=r.value(t)||void 0),null!=(r=E[e])&&(r.attrName===e||r.keyName===e)&&(i[(r=E[e]).attrName]=r.value(t)||void 0))}),Object.keys(i).length>0&&(e=applyFormat(e,i)),e}function matchBlot(t,e){var r=a.default.query(t);if(null==r)return e;if(r.prototype instanceof a.default.Embed){var n={},o=r.value(t);null!=o&&(n[r.blotName]=o,e=new l.default().insert(n,r.formats(t)))}else"function"==typeof r.formats&&(e=applyFormat(e,r.blotName,r.formats(t)));return e}function matchNewline(t,e){return!deltaEndsWith(e,"\n")&&(isLine(t)||e.length()>0&&t.nextSibling&&isLine(t.nextSibling))&&e.insert("\n"),e}function matchSpacing(t,e){if(isLine(t)&&null!=t.nextElementSibling&&!deltaEndsWith(e,"\n\n")){var r=t.offsetHeight+parseFloat(computeStyle(t).marginTop)+parseFloat(computeStyle(t).marginBottom);t.nextElementSibling.offsetTop>t.offsetTop*****r&&e.insert("\n")}return e}function matchText(t,e){var r=t.data;if("O:P"===t.parentNode.tagName)return e.insert(r.trim());if(0===r.trim().length&&t.parentNode.classList.contains("ql-clipboard"))return e;if(!computeStyle(t.parentNode).whiteSpace.startsWith("pre")){var replacer=function(t,e){return(e=e.replace(/[^\u00a0]/g,"")).length<1&&t?" ":e};r=(r=r.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,replacer.bind(replacer,!0)),(null==t.previousSibling&&isLine(t.parentNode)||null!=t.previousSibling&&isLine(t.previousSibling))&&(r=r.replace(/^\s+/,replacer.bind(replacer,!1))),(null==t.nextSibling&&isLine(t.parentNode)||null!=t.nextSibling&&isLine(t.nextSibling))&&(r=r.replace(/\s+$/,replacer.bind(replacer,!1)))}return e.insert(r)}x.DEFAULTS={matchers:[],matchVisual:!0},e.default=x,e.matchAttributor=matchAttributor,e.matchBlot=matchBlot,e.matchNewline=matchNewline,e.matchSpacing=matchSpacing,e.matchText=matchText},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function Bold(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Bold),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Bold.__proto__||Object.getPrototypeOf(Bold)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Bold,t),o(Bold,[{key:"optimize",value:function(t){_get(Bold.prototype.__proto__||Object.getPrototypeOf(Bold.prototype),"optimize",this).call(this,t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return _get(Bold.__proto__||Object.getPrototypeOf(Bold),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),Bold}(((n=r(6))&&n.__esModule?n:{default:n}).default);i.blotName="bold",i.tagName=["STRONG","B"],e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addControls=e.default=void 0;var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(2)),i=_interopRequireDefault(r(0)),l=_interopRequireDefault(r(5)),a=_interopRequireDefault(r(10)),s=_interopRequireDefault(r(9));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}var u=(0,a.default)("quill:toolbar"),f=function(t){function Toolbar(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Toolbar);var r,n=_possibleConstructorReturn(this,(Toolbar.__proto__||Object.getPrototypeOf(Toolbar)).call(this,t,e));if(Array.isArray(n.options.container)){var o=document.createElement("div");addControls(o,n.options.container),t.container.parentNode.insertBefore(o,t.container),n.container=o}else"string"==typeof n.options.container?n.container=document.querySelector(n.options.container):n.container=n.options.container;return n.container instanceof HTMLElement?(n.container.classList.add("ql-toolbar"),n.controls=[],n.handlers={},Object.keys(n.options.handlers).forEach(function(t){n.addHandler(t,n.options.handlers[t])}),[].forEach.call(n.container.querySelectorAll("button, select"),function(t){n.attach(t)}),n.quill.on(l.default.events.EDITOR_CHANGE,function(t,e){t===l.default.events.SELECTION_CHANGE&&n.update(e)}),n.quill.on(l.default.events.SCROLL_OPTIMIZE,function(){var t=_slicedToArray(n.quill.selection.getRange(),1)[0];n.update(t)}),n):(r=u.error("Container required for toolbar",n.options),_possibleConstructorReturn(n,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Toolbar,t),n(Toolbar,[{key:"addHandler",value:function(t,e){this.handlers[t]=e}},{key:"attach",value:function(t){var e=this,r=[].find.call(t.classList,function(t){return 0===t.indexOf("ql-")});if(r){if(r=r.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[r]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[r]){u.warn("ignoring attaching to disabled format",r,t);return}if(null==i.default.query(r)){u.warn("ignoring attaching to nonexistent format",r,t);return}}var n="SELECT"===t.tagName?"change":"click";t.addEventListener(n,function(n){var a,s,u,f=void 0;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;var p=t.options[t.selectedIndex];f=!p.hasAttribute("selected")&&(p.value||!1)}else f=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),n.preventDefault();e.quill.focus();var d=_slicedToArray(e.quill.selection.getRange(),1)[0];if(null!=e.handlers[r])e.handlers[r].call(e,f);else if(i.default.query(r).prototype instanceof i.default.Embed){if(!(f=prompt("Enter "+r)))return;e.quill.updateContents(new o.default().retain(d.index).delete(d.length).insert((a={},s=r,u=f,s in a?Object.defineProperty(a,s,{value:u,enumerable:!0,configurable:!0,writable:!0}):a[s]=u,a)),l.default.sources.USER)}else e.quill.format(r,f,l.default.sources.USER);e.update(d)}),this.controls.push([r,t])}}},{key:"update",value:function(t){var e=null==t?{}:this.quill.getFormat(t);this.controls.forEach(function(r){var n=_slicedToArray(r,2),o=n[0],i=n[1];if("SELECT"===i.tagName){var l=void 0;if(null==t)l=null;else if(null==e[o])l=i.querySelector("option[selected]");else if(!Array.isArray(e[o])){var a=e[o];"string"==typeof a&&(a=a.replace(/\"/g,'\\"')),l=i.querySelector('option[value="'+a+'"]')}null==l?(i.value="",i.selectedIndex=-1):l.selected=!0}else if(null==t)i.classList.remove("ql-active");else if(i.hasAttribute("value")){var s=e[o]===i.getAttribute("value")||null!=e[o]&&e[o].toString()===i.getAttribute("value")||null==e[o]&&!i.getAttribute("value");i.classList.toggle("ql-active",s)}else i.classList.toggle("ql-active",null!=e[o])})}}]),Toolbar}(s.default);function addButton(t,e,r){var n=document.createElement("button");n.setAttribute("type","button"),n.classList.add("ql-"+e),null!=r&&(n.value=r),t.appendChild(n)}function addControls(t,e){Array.isArray(e[0])||(e=[e]),e.forEach(function(e){var r=document.createElement("span");r.classList.add("ql-formats"),e.forEach(function(t){if("string"==typeof t)addButton(r,t);else{var e,n=Object.keys(t)[0],o=t[n];Array.isArray(o)?((e=document.createElement("select")).classList.add("ql-"+n),o.forEach(function(t){var r=document.createElement("option");!1!==t?r.setAttribute("value",t):r.setAttribute("selected","selected"),e.appendChild(r)}),r.appendChild(e)):addButton(r,n,o)}}),t.appendChild(r)})}f.DEFAULTS={},f.DEFAULTS={container:null,handlers:{clean:function(){var t=this,e=this.quill.getSelection();null!=e&&(0==e.length?Object.keys(this.quill.getFormat()).forEach(function(e){null!=i.default.query(e,i.default.Scope.INLINE)&&t.quill.format(e,!1)}):this.quill.removeFormat(e,l.default.sources.USER))},direction:function(t){var e=this.quill.getFormat().align;"rtl"===t&&null==e?this.quill.format("align","right",l.default.sources.USER):t||"right"!==e||this.quill.format("align",!1,l.default.sources.USER),this.quill.format("direction",t,l.default.sources.USER)},indent:function(t){var e=this.quill.getSelection(),r=this.quill.getFormat(e),n=parseInt(r.indent||0);if("+1"===t||"-1"===t){var o="+1"===t?1:-1;"rtl"===r.direction&&(o*=-1),this.quill.format("indent",n+o,l.default.sources.USER)}},link:function(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,l.default.sources.USER)},list:function(t){var e=this.quill.getSelection(),r=this.quill.getFormat(e);"check"===t?"checked"===r.list||"unchecked"===r.list?this.quill.format("list",!1,l.default.sources.USER):this.quill.format("list","unchecked",l.default.sources.USER):this.quill.format("list",t,l.default.sources.USER)}}},e.default=f,e.addControls=addControls},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function ColorPicker(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,ColorPicker);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(ColorPicker.__proto__||Object.getPrototypeOf(ColorPicker)).call(this,t));return r.label.innerHTML=e,r.container.classList.add("ql-color-picker"),[].slice.call(r.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(t){t.classList.add("ql-primary")}),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(ColorPicker,t),o(ColorPicker,[{key:"buildItem",value:function(t){var e=_get(ColorPicker.prototype.__proto__||Object.getPrototypeOf(ColorPicker.prototype),"buildItem",this).call(this,t);return e.style.backgroundColor=t.getAttribute("value")||"",e}},{key:"selectItem",value:function(t,e){_get(ColorPicker.prototype.__proto__||Object.getPrototypeOf(ColorPicker.prototype),"selectItem",this).call(this,t,e);var r=this.label.querySelector(".ql-color-label"),n=t&&t.getAttribute("data-value")||"";r&&("line"===r.tagName?r.style.stroke=n:r.style.fill=n)}}]),ColorPicker}(((n=r(28))&&n.__esModule?n:{default:n}).default);e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function IconPicker(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,IconPicker);var r=function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(IconPicker.__proto__||Object.getPrototypeOf(IconPicker)).call(this,t));return r.container.classList.add("ql-icon-picker"),[].forEach.call(r.container.querySelectorAll(".ql-picker-item"),function(t){t.innerHTML=e[t.getAttribute("data-value")||""]}),r.defaultItem=r.container.querySelector(".ql-selected"),r.selectItem(r.defaultItem),r}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(IconPicker,t),o(IconPicker,[{key:"selectItem",value:function(t,e){_get(IconPicker.prototype.__proto__||Object.getPrototypeOf(IconPicker.prototype),"selectItem",this).call(this,t,e),t=t||this.defaultItem,this.label.innerHTML=t.innerHTML}}]),IconPicker}(((n=r(28))&&n.__esModule?n:{default:n}).default);e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=function(){function Tooltip(t,e){var r=this;(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,Tooltip),this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){r.root.style.marginTop=-1*r.quill.root.scrollTop+"px"}),this.hide()}return n(Tooltip,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(t){var e=t.left+t.width/2-this.root.offsetWidth/2,r=t.bottom+this.quill.root.scrollTop;this.root.style.left=e+"px",this.root.style.top=r+"px",this.root.classList.remove("ql-flip");var n=this.boundsContainer.getBoundingClientRect(),o=this.root.getBoundingClientRect(),i=0;if(o.right>n.right&&(i=n.right-o.right,this.root.style.left=e+i+"px"),o.left<n.left&&(i=n.left-o.left,this.root.style.left=e+i+"px"),o.bottom>n.bottom){var l=o.bottom-o.top,a=t.bottom-t.top+l;this.root.style.top=r-a+"px",this.root.classList.add("ql-flip")}return i}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),Tooltip}();e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var _slicedToArray=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var l,a=t[Symbol.iterator]();!(n=(l=a.next()).done)&&(r.push(l.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{!n&&a.return&&a.return()}finally{if(o)throw i}}return r}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(3)),i=_interopRequireDefault(r(8)),l=r(43),a=_interopRequireDefault(l),s=_interopRequireDefault(r(27)),u=r(15),f=_interopRequireDefault(r(41));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var p=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],d=function(t){function SnowTheme(t,e){_classCallCheck(this,SnowTheme),null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=p);var r=_possibleConstructorReturn(this,(SnowTheme.__proto__||Object.getPrototypeOf(SnowTheme)).call(this,t,e));return r.quill.container.classList.add("ql-snow"),r}return _inherits(SnowTheme,t),n(SnowTheme,[{key:"extendToolbar",value:function(t){t.container.classList.add("ql-snow"),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),f.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),f.default),this.tooltip=new h(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(e,r){t.handlers.link.call(t,!r.format.link)})}}]),SnowTheme}(a.default);d.DEFAULTS=(0,o.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){if(t){var e=this.quill.getSelection();if(null!=e&&0!=e.length){var r=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(r)&&0!==r.indexOf("mailto:")&&(r="mailto:"+r),this.quill.theme.tooltip.edit("link",r)}}else this.quill.format("link",!1)}}}}});var h=function(t){function SnowTooltip(t,e){_classCallCheck(this,SnowTooltip);var r=_possibleConstructorReturn(this,(SnowTooltip.__proto__||Object.getPrototypeOf(SnowTooltip)).call(this,t,e));return r.preview=r.root.querySelector("a.ql-preview"),r}return _inherits(SnowTooltip,t),n(SnowTooltip,[{key:"listen",value:function(){var t=this;_get(SnowTooltip.prototype.__proto__||Object.getPrototypeOf(SnowTooltip.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(e){t.root.classList.contains("ql-editing")?t.save():t.edit("link",t.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(e){if(null!=t.linkRange){var r=t.linkRange;t.restoreFocus(),t.quill.formatText(r,"link",!1,i.default.sources.USER),delete t.linkRange}e.preventDefault(),t.hide()}),this.quill.on(i.default.events.SELECTION_CHANGE,function(e,r,n){if(null!=e){if(0===e.length&&n===i.default.sources.USER){var o=_slicedToArray(t.quill.scroll.descendant(s.default,e.index),2),l=o[0],a=o[1];if(null!=l){t.linkRange=new u.Range(e.index-a,l.length());var f=s.default.formats(l.domNode);t.preview.textContent=f,t.preview.setAttribute("href",f),t.show(),t.position(t.quill.getBounds(t.linkRange));return}}else delete t.linkRange;t.hide()}})}},{key:"show",value:function(){_get(SnowTooltip.prototype.__proto__||Object.getPrototypeOf(SnowTooltip.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),SnowTooltip}(l.BaseTooltip);h.TEMPLATE='<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-action"></a><a class="ql-remove"></a>',e.default=d},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=_interopRequireDefault(r(29)),o=r(36),i=r(38),l=r(64),a=_interopRequireDefault(r(65)),s=_interopRequireDefault(r(66)),u=r(67),f=_interopRequireDefault(u),p=r(37),d=r(26),h=r(39),y=r(40),b=_interopRequireDefault(r(56)),v=_interopRequireDefault(r(68)),g=_interopRequireDefault(r(27)),m=_interopRequireDefault(r(69)),_=_interopRequireDefault(r(70)),k=_interopRequireDefault(r(71)),O=_interopRequireDefault(r(72)),E=_interopRequireDefault(r(73)),x=r(13),A=_interopRequireDefault(x),w=_interopRequireDefault(r(74)),q=_interopRequireDefault(r(75)),T=_interopRequireDefault(r(57)),S=_interopRequireDefault(r(41)),P=_interopRequireDefault(r(28)),C=_interopRequireDefault(r(59)),N=_interopRequireDefault(r(60)),j=_interopRequireDefault(r(61)),B=_interopRequireDefault(r(108)),L=_interopRequireDefault(r(62));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}n.default.register({"attributors/attribute/direction":i.DirectionAttribute,"attributors/class/align":o.AlignClass,"attributors/class/background":p.BackgroundClass,"attributors/class/color":d.ColorClass,"attributors/class/direction":i.DirectionClass,"attributors/class/font":h.FontClass,"attributors/class/size":y.SizeClass,"attributors/style/align":o.AlignStyle,"attributors/style/background":p.BackgroundStyle,"attributors/style/color":d.ColorStyle,"attributors/style/direction":i.DirectionStyle,"attributors/style/font":h.FontStyle,"attributors/style/size":y.SizeStyle},!0),n.default.register({"formats/align":o.AlignClass,"formats/direction":i.DirectionClass,"formats/indent":l.IndentClass,"formats/background":p.BackgroundStyle,"formats/color":d.ColorStyle,"formats/font":h.FontClass,"formats/size":y.SizeClass,"formats/blockquote":a.default,"formats/code-block":A.default,"formats/header":s.default,"formats/list":f.default,"formats/bold":b.default,"formats/code":x.Code,"formats/italic":v.default,"formats/link":g.default,"formats/script":m.default,"formats/strike":_.default,"formats/underline":k.default,"formats/image":O.default,"formats/video":E.default,"formats/list/item":u.ListItem,"modules/formula":w.default,"modules/syntax":q.default,"modules/toolbar":T.default,"themes/bubble":B.default,"themes/snow":L.default,"ui/icons":S.default,"ui/picker":P.default,"ui/icon-picker":N.default,"ui/color-picker":C.default,"ui/tooltip":j.default},!0),e.default=n.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IndentClass=void 0;var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=(n=r(0))&&n.__esModule?n:{default:n},l=new(function(t){function IdentAttributor(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,IdentAttributor),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(IdentAttributor.__proto__||Object.getPrototypeOf(IdentAttributor)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(IdentAttributor,t),o(IdentAttributor,[{key:"add",value:function(t,e){if("+1"===e||"-1"===e){var r=this.value(t)||0;e="+1"===e?r+1:r-1}return 0===e?(this.remove(t),!0):_get(IdentAttributor.prototype.__proto__||Object.getPrototypeOf(IdentAttributor.prototype),"add",this).call(this,t,e)}},{key:"canAdd",value:function(t,e){return _get(IdentAttributor.prototype.__proto__||Object.getPrototypeOf(IdentAttributor.prototype),"canAdd",this).call(this,t,e)||_get(IdentAttributor.prototype.__proto__||Object.getPrototypeOf(IdentAttributor.prototype),"canAdd",this).call(this,t,parseInt(e))}},{key:"value",value:function(t){return parseInt(_get(IdentAttributor.prototype.__proto__||Object.getPrototypeOf(IdentAttributor.prototype),"value",this).call(this,t))||void 0}}]),IdentAttributor}(i.default.Attributor.Class))("indent","ql-indent",{scope:i.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});e.IndentClass=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(t){function Blockquote(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Blockquote),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Blockquote.__proto__||Object.getPrototypeOf(Blockquote)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Blockquote,t),Blockquote}(((n=r(4))&&n.__esModule?n:{default:n}).default);o.blotName="blockquote",o.tagName="blockquote",e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),i=function(t){function Header(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Header),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Header.__proto__||Object.getPrototypeOf(Header)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Header,t),o(Header,null,[{key:"formats",value:function(t){return this.tagName.indexOf(t.tagName)+1}}]),Header}(((n=r(4))&&n.__esModule?n:{default:n}).default);i.blotName="header",i.tagName=["H1","H2","H3","H4","H5","H6"],e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.ListItem=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(4)),l=_interopRequireDefault(r(25));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var a=function(t){function ListItem(){return _classCallCheck(this,ListItem),_possibleConstructorReturn(this,(ListItem.__proto__||Object.getPrototypeOf(ListItem)).apply(this,arguments))}return _inherits(ListItem,t),n(ListItem,[{key:"format",value:function(t,e){t!==s.blotName||e?_get(ListItem.prototype.__proto__||Object.getPrototypeOf(ListItem.prototype),"format",this).call(this,t,e):this.replaceWith(o.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():_get(ListItem.prototype.__proto__||Object.getPrototypeOf(ListItem.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(t,e){return(this.parent.isolate(this.offset(this.parent),this.length()),t===this.parent.statics.blotName)?(this.parent.replaceWith(t,e),this):(this.parent.unwrap(),_get(ListItem.prototype.__proto__||Object.getPrototypeOf(ListItem.prototype),"replaceWith",this).call(this,t,e))}}],[{key:"formats",value:function(t){return t.tagName===this.tagName?void 0:_get(ListItem.__proto__||Object.getPrototypeOf(ListItem),"formats",this).call(this,t)}}]),ListItem}(i.default);a.blotName="list-item",a.tagName="LI";var s=function(t){function List(t){_classCallCheck(this,List);var e=_possibleConstructorReturn(this,(List.__proto__||Object.getPrototypeOf(List)).call(this,t)),listEventHandler=function(r){if(r.target.parentNode===t){var n=e.statics.formats(t),i=o.default.find(r.target);"checked"===n?i.format("list","unchecked"):"unchecked"===n&&i.format("list","checked")}};return t.addEventListener("touchstart",listEventHandler),t.addEventListener("mousedown",listEventHandler),e}return _inherits(List,t),n(List,null,[{key:"create",value:function(t){var e="ordered"===t?"OL":"UL",r=_get(List.__proto__||Object.getPrototypeOf(List),"create",this).call(this,e);return("checked"===t||"unchecked"===t)&&r.setAttribute("data-checked","checked"===t),r}},{key:"formats",value:function(t){return"OL"===t.tagName?"ordered":"UL"===t.tagName?t.hasAttribute("data-checked")?"true"===t.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),n(List,[{key:"format",value:function(t,e){this.children.length>0&&this.children.tail.format(t,e)}},{key:"formats",value:function(){var t,e,r;return t={},e=this.statics.blotName,r=this.statics.formats(this.domNode),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}},{key:"insertBefore",value:function(t,e){if(t instanceof a)_get(List.prototype.__proto__||Object.getPrototypeOf(List.prototype),"insertBefore",this).call(this,t,e);else{var r=null==e?this.length():e.offset(this),n=this.split(r);n.parent.insertBefore(t,n)}}},{key:"optimize",value:function(t){_get(List.prototype.__proto__||Object.getPrototypeOf(List.prototype),"optimize",this).call(this,t);var e=this.next;null!=e&&e.prev===this&&e.statics.blotName===this.statics.blotName&&e.domNode.tagName===this.domNode.tagName&&e.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(e.moveChildren(this),e.remove())}},{key:"replace",value:function(t){if(t.statics.blotName!==this.statics.blotName){var e=o.default.create(this.statics.defaultChild);t.moveChildren(e),this.appendChild(e)}_get(List.prototype.__proto__||Object.getPrototypeOf(List.prototype),"replace",this).call(this,t)}}]),List}(l.default);s.blotName="list",s.scope=o.default.Scope.BLOCK_BLOT,s.tagName=["OL","UL"],s.defaultChild="list-item",s.allowedChildren=[a],e.ListItem=a,e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(t){function Italic(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Italic),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Italic.__proto__||Object.getPrototypeOf(Italic)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Italic,t),Italic}(((n=r(56))&&n.__esModule?n:{default:n}).default);o.blotName="italic",o.tagName=["EM","I"],e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=function(t){function Script(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Script),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Script.__proto__||Object.getPrototypeOf(Script)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Script,t),o(Script,null,[{key:"create",value:function(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):_get(Script.__proto__||Object.getPrototypeOf(Script),"create",this).call(this,t)}},{key:"formats",value:function(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}]),Script}(((n=r(6))&&n.__esModule?n:{default:n}).default);i.blotName="script",i.tagName=["SUB","SUP"],e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(t){function Strike(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Strike),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Strike.__proto__||Object.getPrototypeOf(Strike)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Strike,t),Strike}(((n=r(6))&&n.__esModule?n:{default:n}).default);o.blotName="strike",o.tagName="S",e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(t){function Underline(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Underline),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Underline.__proto__||Object.getPrototypeOf(Underline)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Underline,t),Underline}(((n=r(6))&&n.__esModule?n:{default:n}).default);o.blotName="underline",o.tagName="U",e.default=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=(n=r(0))&&n.__esModule?n:{default:n},l=r(27),a=["alt","height","width"],s=function(t){function Image(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Image),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Image.__proto__||Object.getPrototypeOf(Image)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Image,t),o(Image,[{key:"format",value:function(t,e){a.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):_get(Image.prototype.__proto__||Object.getPrototypeOf(Image.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=_get(Image.__proto__||Object.getPrototypeOf(Image),"create",this).call(this,t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(t){return a.reduce(function(e,r){return t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e},{})}},{key:"match",value:function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}},{key:"sanitize",value:function(t){return(0,l.sanitize)(t,["http","https","data"])?t:"//:0"}},{key:"value",value:function(t){return t.getAttribute("src")}}]),Image}(i.default.Embed);s.blotName="image",s.tagName="IMG",e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,o=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},i=r(4),l=(n=r(27))&&n.__esModule?n:{default:n},a=["height","width"],s=function(t){function Video(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,Video),function(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}(this,(Video.__proto__||Object.getPrototypeOf(Video)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(Video,t),o(Video,[{key:"format",value:function(t,e){a.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):_get(Video.prototype.__proto__||Object.getPrototypeOf(Video.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=_get(Video.__proto__||Object.getPrototypeOf(Video),"create",this).call(this,t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen",!0),e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(t){return a.reduce(function(e,r){return t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e},{})}},{key:"sanitize",value:function(t){return l.default.sanitize(t)}},{key:"value",value:function(t){return t.getAttribute("src")}}]),Video}(i.BlockEmbed);s.blotName="video",s.className="ql-video",s.tagName="IFRAME",e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.FormulaBlot=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(35)),i=_interopRequireDefault(r(5)),l=_interopRequireDefault(r(9));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var a=function(t){function FormulaBlot(){return _classCallCheck(this,FormulaBlot),_possibleConstructorReturn(this,(FormulaBlot.__proto__||Object.getPrototypeOf(FormulaBlot)).apply(this,arguments))}return _inherits(FormulaBlot,t),n(FormulaBlot,null,[{key:"create",value:function(t){var e=_get(FormulaBlot.__proto__||Object.getPrototypeOf(FormulaBlot),"create",this).call(this,t);return"string"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}},{key:"value",value:function(t){return t.getAttribute("data-value")}}]),FormulaBlot}(o.default);a.blotName="formula",a.className="ql-formula",a.tagName="SPAN";var s=function(t){function Formula(){_classCallCheck(this,Formula);var t=_possibleConstructorReturn(this,(Formula.__proto__||Object.getPrototypeOf(Formula)).call(this));if(null==window.katex)throw Error("Formula module requires KaTeX.");return t}return _inherits(Formula,t),n(Formula,null,[{key:"register",value:function(){i.default.register(a,!0)}}]),Formula}(l.default);e.FormulaBlot=a,e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.CodeToken=e.CodeBlock=void 0;var n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),_get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},o=_interopRequireDefault(r(0)),i=_interopRequireDefault(r(5)),l=_interopRequireDefault(r(9));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var a=function(t){function SyntaxCodeBlock(){return _classCallCheck(this,SyntaxCodeBlock),_possibleConstructorReturn(this,(SyntaxCodeBlock.__proto__||Object.getPrototypeOf(SyntaxCodeBlock)).apply(this,arguments))}return _inherits(SyntaxCodeBlock,t),n(SyntaxCodeBlock,[{key:"replaceWith",value:function(t){this.domNode.textContent=this.domNode.textContent,this.attach(),_get(SyntaxCodeBlock.prototype.__proto__||Object.getPrototypeOf(SyntaxCodeBlock.prototype),"replaceWith",this).call(this,t)}},{key:"highlight",value:function(t){var e=this.domNode.textContent;this.cachedText!==e&&((e.trim().length>0||null==this.cachedText)&&(this.domNode.innerHTML=t(e),this.domNode.normalize(),this.attach()),this.cachedText=e)}}]),SyntaxCodeBlock}(_interopRequireDefault(r(13)).default);a.className="ql-syntax";var s=new o.default.Attributor.Class("token","hljs",{scope:o.default.Scope.INLINE}),u=function(t){function Syntax(t,e){_classCallCheck(this,Syntax);var r=_possibleConstructorReturn(this,(Syntax.__proto__||Object.getPrototypeOf(Syntax)).call(this,t,e));if("function"!=typeof r.options.highlight)throw Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var n=null;return r.quill.on(i.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(n),n=setTimeout(function(){r.highlight(),n=null},r.options.interval)}),r.highlight(),r}return _inherits(Syntax,t),n(Syntax,null,[{key:"register",value:function(){i.default.register(s,!0),i.default.register(a,!0)}}]),n(Syntax,[{key:"highlight",value:function(){var t=this;if(!this.quill.selection.composing){this.quill.update(i.default.sources.USER);var e=this.quill.getSelection();this.quill.scroll.descendants(a).forEach(function(e){e.highlight(t.options.highlight)}),this.quill.update(i.default.sources.SILENT),null!=e&&this.quill.setSelection(e,i.default.sources.SILENT)}}}]),Syntax}(l.default);u.DEFAULTS={highlight:null==window.hljs?null:function(t){return window.hljs.highlightAuto(t).value},interval:1e3},e.CodeBlock=a,e.CodeToken=s,e.default=u},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BubbleTooltip=void 0;var _get=function get(t,e,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,e);if(void 0===n){var o=Object.getPrototypeOf(t);if(null===o)return;return get(o,e,r)}if("value"in n)return n.value;var i=n.get;if(void 0!==i)return i.call(r)},n=function(){function defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(t,e,r){return e&&defineProperties(t.prototype,e),r&&defineProperties(t,r),t}}(),o=_interopRequireDefault(r(3)),i=_interopRequireDefault(r(8)),l=r(43),a=_interopRequireDefault(l),s=r(15),u=_interopRequireDefault(r(41));function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],p=function(t){function BubbleTheme(t,e){_classCallCheck(this,BubbleTheme),null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=f);var r=_possibleConstructorReturn(this,(BubbleTheme.__proto__||Object.getPrototypeOf(BubbleTheme)).call(this,t,e));return r.quill.container.classList.add("ql-bubble"),r}return _inherits(BubbleTheme,t),n(BubbleTheme,[{key:"extendToolbar",value:function(t){this.tooltip=new d(this.quill,this.options.bounds),this.tooltip.root.appendChild(t.container),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),u.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),u.default)}}]),BubbleTheme}(a.default);p.DEFAULTS=(0,o.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var d=function(t){function BubbleTooltip(t,e){_classCallCheck(this,BubbleTooltip);var r=_possibleConstructorReturn(this,(BubbleTooltip.__proto__||Object.getPrototypeOf(BubbleTooltip)).call(this,t,e));return r.quill.on(i.default.events.EDITOR_CHANGE,function(t,e,n,o){if(t===i.default.events.SELECTION_CHANGE){if(null!=e&&e.length>0&&o===i.default.sources.USER){r.show(),r.root.style.left="0px",r.root.style.width="",r.root.style.width=r.root.offsetWidth+"px";var l=r.quill.getLines(e.index,e.length);if(1===l.length)r.position(r.quill.getBounds(e));else{var a=l[l.length-1],u=r.quill.getIndex(a),f=Math.min(a.length()-1,e.index+e.length-u),p=r.quill.getBounds(new s.Range(u,f));r.position(p)}}else document.activeElement!==r.textbox&&r.quill.hasFocus()&&r.hide()}}),r}return _inherits(BubbleTooltip,t),n(BubbleTooltip,[{key:"listen",value:function(){var t=this;_get(BubbleTooltip.prototype.__proto__||Object.getPrototypeOf(BubbleTooltip.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){t.root.classList.remove("ql-editing")}),this.quill.on(i.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!t.root.classList.contains("ql-hidden")){var e=t.quill.getSelection();null!=e&&t.position(t.quill.getBounds(e))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(t){var e=_get(BubbleTooltip.prototype.__proto__||Object.getPrototypeOf(BubbleTooltip.prototype),"position",this).call(this,t),r=this.root.querySelector(".ql-tooltip-arrow");if(r.style.marginLeft="",0===e)return e;r.style.marginLeft=-1*e-r.offsetWidth/2+"px"}}]),BubbleTooltip}(l.BaseTooltip);d.TEMPLATE='<span class="ql-tooltip-arrow"></span><div class="ql-tooltip-editor"><input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL"><a class="ql-close"></a></div>',e.BubbleTooltip=d,e.default=p},function(t,e,r){t.exports=r(63)}]).default}}]);