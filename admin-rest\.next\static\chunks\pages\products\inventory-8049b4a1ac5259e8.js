(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7773],{88485:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products/inventory",function(){return l(33952)}])},97670:function(e,t,l){"use strict";l.r(t);var s=l(85893),r=l(78985),n=l(79362),i=l(8144),a=l(74673),o=l(99494),d=l(5233),c=l(1631),u=l(11163),m=l(48583),x=l(93967),f=l.n(x),h=l(30824),p=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,d.$G)(),[r,i]=(0,m.KO)(n.Hf),{childMenu:a}=t,{width:o}=(0,p.Z)();return(0,s.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:t,label:i,icon:a,childMenu:d}=e;return(0,s.jsx)(c.Z,{href:t,label:l(i),icon:a,childMenu:d,miniSidebar:r&&o>=n.h2},i)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[l,r]=(0,m.KO)(n.Hf),i=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(i),{width:c}=(0,p.Z)();return(0,s.jsx)(s.Fragment,{children:null==a?void 0:a.map((e,r)=>{var a;return(0,s.jsxs)("div",{className:f()("flex flex-col px-5",l&&c>=n.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&c>=n.h2?"hidden":""),children:t(null===(a=i[e])||void 0===a?void 0:a.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:i[e]})]},r)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,u.useRouter)(),[o,d]=(0,m.KO)(n.Hf),[c]=(0,m.KO)(n.GH),[x]=(0,m.KO)(n.W4),{width:g}=(0,p.Z)();return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(a.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=n.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-20",o&&g>=n.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,s.jsx)(h.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=n.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",o&&g>=n.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,s.jsx)(i.Z,{})]})]})]})}},33952:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSG:function(){return N},default:function(){return ProductInventoryPage}});var s=l(85893),r=l(92072),n=l(97670),i=l(45957),a=l(55846),o=l(5233),d=l(67294),c=l(37912),u=l(16203),m=l(10265),x=l(93242),f=l(11163),h=l(15724),p=l(93967),g=l.n(p),b=l(85634),v=l(46626),j=l(65366),w=l(35484),N=!0;function ProductInventoryPage(){let{t:e}=(0,o.$G)(),[t,l]=(0,d.useState)(""),[n,u]=(0,d.useState)(1),[p,N]=(0,d.useState)("created_at"),[y,S]=(0,d.useState)(m.As.Desc),[_,Z]=(0,d.useState)(""),[P,k]=(0,d.useState)(""),[O,G]=(0,d.useState)(""),{locale:I}=(0,f.useRouter)(),[K,T]=(0,d.useState)(!0),{products:C,paginatorInfo:E,loading:F,error:H}=(0,x.kN)({language:I,name:t,limit:20,page:n,orderBy:p,sortedBy:y,categories:P,product_type:O,type:_});return F?(0,s.jsx)(a.Z,{text:e("common:text-loading")}):H?(0,s.jsx)(i.Z,{message:H.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(r.Z,{className:"mb-8 flex flex-col",children:[(0,s.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(w.Z,{title:e("form:input-label-inventory")})}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center ms-auto md:w-2/4",children:(0,s.jsx)(c.Z,{onSearch:function(e){let{searchText:t}=e;l(t),u(1)}})}),(0,s.jsxs)("button",{className:"mt-5 flex items-center whitespace-nowrap text-base font-semibold text-accent md:mt-0 md:ms-5",onClick:()=>{T(e=>!e)},children:[e("common:text-filter")," ",K?(0,s.jsx)(v.a,{className:"ms-2"}):(0,s.jsx)(b.K,{className:"ms-2"})]})]}),(0,s.jsx)("div",{className:g()("flex w-full transition",{"visible h-auto":K,"invisible h-0":!K}),children:(0,s.jsx)("div",{className:"mt-5 flex w-full flex-col border-t border-gray-200 pt-5 md:mt-8 md:flex-row md:items-center md:pt-8",children:(0,s.jsx)(h.Z,{className:"w-full",type:_,onCategoryFilter:e=>{k(null==e?void 0:e.slug),u(1)},onTypeFilter:e=>{Z(null==e?void 0:e.slug),u(1)},onProductTypeFilter:e=>{G(null==e?void 0:e.slug),u(1)},enableCategory:!0,enableType:!0,enableProductType:!0})})})]}),(0,s.jsx)(j.Z,{products:C,paginatorInfo:E,onPagination:function(e){u(e)},onOrder:N,onSort:S})]})}ProductInventoryPage.authenticate={permissions:u.M$},ProductInventoryPage.Layout=n.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1077,685,9774,2888,179],function(){return e(e.s=88485)}),_N_E=e.O()}]);