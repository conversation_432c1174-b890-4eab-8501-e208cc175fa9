"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6678],{35484:function(e,t,n){var r=n(85893),a=n(93967),s=n.n(a),l=n(98388);t.Z=e=>{let{title:t,className:n,...a}=e;return(0,r.jsx)("h2",{className:(0,l.m6)(s()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...a,children:t})}},37912:function(e,t,n){var r=n(85893),a=n(5114),s=n(80287),l=n(93967),o=n.n(l),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:l="outline",shadow:m=!1,inputClassName:f,placeholderText:h,...x}=e,{register:p,handleSubmit:b,watch:g,reset:v,formState:{errors:w}}=(0,i.cI)({defaultValues:{searchText:""}}),y=g("searchText"),{t:N}=(0,c.$G)();(0,u.useEffect)(()=>{y||n({searchText:""})},[y]);let A=o()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===l,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===l,"border border-border-base focus:border-accent":"outline"===l},{"focus:shadow":m},f);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(o()("relative flex w-full items-center",t)),onSubmit:b(n),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:N("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(s.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...p("searchText"),className:(0,d.m6)(A),placeholder:null!=h?h:N("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...x}),w.searchText&&(0,r.jsx)("p",{children:w.searchText.message}),!!y&&(0,r.jsx)("button",{type:"button",onClick:function(){v(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(a.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},65034:function(e,t,n){var r=n(85893),a=n(67294),s=n(18230),l=n(25675),o=n.n(l),u=n(27899),i=n(99494),c=n(5233),d=n(78998),m=n(77768),f=n(11163),h=n(77556),x=n(10265),p=n(97514),b=n(76518),g=n(61571),v=n(34927);t.Z=e=>{var t;let{manufacturers:n,paginatorInfo:l,onPagination:w,onSort:y,onOrder:N}=e,{t:A}=(0,c.$G)(),j=(0,f.useRouter)(),{alignLeft:M}=(0,b.S)(),[C,S]=(0,a.useState)({sort:x.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{y(e=>e===x.As.Desc?x.As.Asc:x.As.Desc),N(e),S({sort:C.sort===x.As.Desc?x.As.Asc:x.As.Desc,column:e})}}),k=[{title:(0,r.jsx)(d.Z,{title:A("table:table-item-id"),ascending:C.sort===x.As.Asc&&"id"===C.column,isActive:"id"===C.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:M,width:160,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(A("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(d.Z,{title:A("table:table-item-title"),ascending:C.sort===x.As.Asc&&"name"===C.column,isActive:"name"===C.column}),dataIndex:"name",key:"name",width:220,align:M,className:"cursor-pointer",onHeaderCell:()=>onHeaderClick("name"),render:(e,t)=>{var n,a;let{image:s}=t;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative aspect-square h-10 w-10 shrink-0 overflow-hidden rounded border border-border-200/80 bg-gray-100 me-2.5",children:(0,r.jsx)(o(),{src:null!==(a=null==s?void 0:s.thumbnail)&&void 0!==a?a:null===i.siteSettings||void 0===i.siteSettings?void 0:null===(n=i.siteSettings.product)||void 0===n?void 0:n.placeholder,alt:e,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw"})}),(0,r.jsx)("span",{className:"truncate font-medium",children:e})]})}},{title:A("table:table-item-products"),dataIndex:"products_count",key:"products_count",width:120,align:"center"},{title:A("table:table-item-approval-action"),dataIndex:"is_approved",key:"approve",align:"center",width:150,render:function(e,t){let{locale:n}=(0,f.useRouter)(),{mutate:a,isLoading:s}=(0,g.TN)();return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(m.r,{checked:e,onChange:function(){a({id:null==t?void 0:t.id,name:null==t?void 0:t.name,is_approved:!e,type_id:null==t?void 0:t.type.id,language:n})},className:"".concat(e?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none"),dir:"ltr",children:[(0,r.jsx)("span",{className:"sr-only",children:"Enable"}),(0,r.jsx)("span",{className:"".concat(e?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light")})]})})}},{title:A("table:table-item-slug"),dataIndex:"slug",key:"slug",align:"center",width:200},{title:A("table:table-item-actions"),dataIndex:"slug",key:"actions",align:"right",width:120,render:(e,t)=>(0,r.jsx)(v.Z,{slug:e,record:t,deleteModalView:"DELETE_MANUFACTURER",routes:null===p.Z||void 0===p.Z?void 0:p.Z.manufacturer})}];return(null==j?void 0:null===(t=j.query)||void 0===t?void 0:t.shop)&&(k=null==k?void 0:k.filter(e=>(null==e?void 0:e.key)!=="approve"&&(null==e?void 0:e.key)!=="actions")),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,r.jsx)(u.i,{columns:k,emptyText:()=>(0,r.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,r.jsx)(h.m,{className:"w-52"}),(0,r.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:A("table:empty-table-data")}),(0,r.jsx)("p",{className:"text-[13px]",children:A("table:empty-table-sorry-text")})]}),data:n,rowKey:"id",scroll:{x:900}})}),!!(null==l?void 0:l.total)&&(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(s.Z,{total:l.total,current:l.currentPage,pageSize:l.perPage,onChange:w})})]})}},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var r=n(85893),a=n(55891),s=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,r.jsx)(a.Z,{nextIcon:(0,r.jsx)(s.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},61571:function(e,t,n){n.d(t,{M5:function(){return useCreateManufacturerMutation},ty:function(){return useDeleteManufacturerMutation},a7:function(){return useManufacturerQuery},ML:function(){return useManufacturersQuery},pN:function(){return useUpdateManufacturerMutation},TN:function(){return useUpdateManufacturerMutationInList}});var r=n(11163),a=n.n(r),s=n(88767),l=n(22920),o=n(5233),u=n(97514),i=n(47869),c=n(28597),d=n(55191),m=n(3737);let f={...(0,d.h)(i.P.MANUFACTURERS),paginated:e=>{let{name:t,shop_id:n,...r}=e;return m.eN.get(i.P.MANUFACTURERS,{searchJoin:"and",...r,search:m.eN.formatSearchParams({name:t,shop_id:n})})}};var h=n(93345);let useCreateManufacturerMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)(),n=(0,r.useRouter)();return(0,s.useMutation)(f.create,{onSuccess:async()=>{let e=n.query.shop?"/".concat(n.query.shop).concat(u.Z.manufacturer.list):u.Z.manufacturer.list;await a().push(e,void 0,{locale:h.Config.defaultLanguage}),l.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useDeleteManufacturerMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,o.$G)();return(0,s.useMutation)(f.delete,{onSuccess:()=>{l.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,r.useRouter)(),n=(0,s.useQueryClient)();return(0,s.useMutation)(f.update,{onSuccess:async n=>{let r=t.query.shop?"/".concat(t.query.shop).concat(u.Z.manufacturer.list):u.Z.manufacturer.list;await t.push("".concat(r,"/").concat(null==n?void 0:n.slug,"/edit"),void 0,{locale:h.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutationInList=()=>{let{t:e}=(0,o.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(f.update,{onSuccess:async()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.MANUFACTURERS)}})},useManufacturerQuery=e=>{let{slug:t,language:n}=e,{data:r,error:a,isLoading:l}=(0,s.useQuery)([i.P.MANUFACTURERS,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{manufacturer:r,error:a,loading:l}},useManufacturersQuery=e=>{var t;let{data:n,error:r,isLoading:a}=(0,s.useQuery)([i.P.MANUFACTURERS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{manufacturers:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(n),error:r,loading:a}}}}]);