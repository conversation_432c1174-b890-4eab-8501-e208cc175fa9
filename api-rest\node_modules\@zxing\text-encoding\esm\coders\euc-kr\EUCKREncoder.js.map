{"version": 3, "file": "EUCKREncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/euc-kr/EUCKREncoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE7E;;;;GAIG;AACH;IAIE,sBAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,aAAa;YAC9B,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,OAAO,UAAU,CAAC;QAEpB,8DAA8D;QAC9D,UAAU;QACV,IAAM,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAa,CAAC,CAAC;QAEzE,uDAAuD;QACvD,IAAI,OAAO,KAAK,IAAI;YAClB,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;QAElC,mDAAmD;QACnD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAE9C,wCAAwC;QACxC,IAAM,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAErC,uDAAuD;QACvD,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,mBAAC;AAAD,CAAC,AAxCD,IAwCC"}