(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5428,2007,2036],{51011:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/attributes/[attributeId]/[action]",function(){return l(64327)}])},97670:function(e,t,l){"use strict";l.r(t);var r=l(85893),i=l(78985),s=l(79362),a=l(8144),n=l(74673),d=l(99494),o=l(5233),u=l(1631),c=l(11163),x=l(48583),h=l(93967),f=l.n(h),p=l(30824),b=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,o.$G)(),[i,a]=(0,x.KO)(s.Hf),{childMenu:n}=t,{width:d}=(0,b.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:t,label:a,icon:n,childMenu:o}=e;return(0,r.jsx)(u.Z,{href:t,label:l(a),icon:n,childMenu:o,miniSidebar:i&&d>=s.h2},a)})})},SideBarGroup=()=>{var e;let{t}=(0,o.$G)(),[l,i]=(0,x.KO)(s.Hf),a=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(a),{width:u}=(0,b.Z)();return(0,r.jsx)(r.Fragment,{children:null==n?void 0:n.map((e,i)=>{var n;return(0,r.jsxs)("div",{className:f()("flex flex-col px-5",l&&u>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&u>=s.h2?"hidden":""),children:t(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},i)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,c.useRouter)(),[d,o]=(0,x.KO)(s.Hf),[u]=(0,x.KO)(s.GH),[h]=(0,x.KO)(s.W4),{width:g}=(0,b.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(n.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=s.h2&&(u||h)?"lg:pt-[8.75rem]":"pt-20",d&&g>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=s.h2&&(u||h)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&g>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(a.Z,{})]})]})]})}},64327:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSG:function(){return x},default:function(){return UpdateAttributePage}});var r=l(85893),i=l(97670),s=l(11163),a=l(45957),n=l(55846),d=l(802),o=l(5233),u=l(89664),c=l(93345),x=!0;function UpdateAttributePage(){let{t:e}=(0,o.$G)(),{query:t,locale:l}=(0,s.useRouter)(),{data:i,isLoading:x,error:h}=(0,u.I1)({slug:t.attributeId,language:"edit"===t.action.toString()?l:c.Config.defaultLanguage});return x?(0,r.jsx)(n.Z,{text:e("common:text-loading")}):h?(0,r.jsx)(a.Z,{message:h.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:edit-attribute")})}),(0,r.jsx)(d.Z,{initialValues:i})]})}UpdateAttributePage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6261,9494,5535,8186,1285,1631,802,9774,2888,179],function(){return e(e.s=51011)}),_N_E=e.O()}]);