"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3572],{35484:function(e,t,n){var o=n(85893),r=n(93967),l=n.n(r),a=n(98388);t.Z=e=>{let{title:t,className:n,...r}=e;return(0,o.jsx)("h2",{className:(0,a.m6)(l()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...r,children:t})}},37912:function(e,t,n){var o=n(85893),r=n(5114),l=n(80287),a=n(93967),u=n.n(a),s=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:m,...v}=e,{register:b,handleSubmit:P,watch:g,reset:y,formState:{errors:x}}=(0,i.cI)({defaultValues:{searchText:""}}),h=g("searchText"),{t:S}=(0,c.$G)();(0,s.useEffect)(()=>{h||n({searchText:""})},[h]);let C=u()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,o.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(u()("relative flex w-full items-center",t)),onSubmit:P(n),children:[(0,o.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,o.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,o.jsx)(l.W,{className:"h-5 w-5"})}),(0,o.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(C),placeholder:null!=m?m:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),x.searchText&&(0,o.jsx)("p",{children:x.searchText.message}),!!h&&(0,o.jsx)("button",{type:"button",onClick:function(){y(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,o.jsx)(r.T,{className:"h-5 w-5"})})]})}},60044:function(e,t,n){var o=n(85893),r=n(18230),l=n(25675),a=n.n(l),u=n(27899),s=n(10265),i=n(99494),c=n(60942),d=n(27484),p=n.n(d),f=n(84110),m=n.n(f),v=n(70178),b=n.n(v),P=n(29387),g=n.n(P),y=n(5233),x=n(67294),h=n(78998),S=n(97514),C=n(34927),E=n(77556),N=n(76518),I=n(8953),A=n(11163);p().extend(m()),p().extend(b()),p().extend(g()),t.Z=e=>{let{coupons:t,paginatorInfo:n,onPagination:l,onSort:d,onOrder:f}=e,{t:m}=(0,y.$G)(),v=(0,A.useRouter)(),{query:{shop:b}}=v,{alignLeft:P}=(0,N.S)(),[g,O]=(0,x.useState)({sort:s.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{d(e=>e===s.As.Desc?s.As.Asc:s.As.Desc),f(e),O({sort:g.sort===s.As.Desc?s.As.Asc:s.As.Desc,column:e})}}),T=[{title:(0,o.jsx)(h.Z,{title:m("table:table-item-id"),ascending:g.sort===s.As.Asc&&"id"===g.column,isActive:"id"===g.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:P,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(m("table:table-item-id"),": ").concat(e)},{title:m("table:table-item-banner"),dataIndex:"image",key:"image",width:74,render:e=>{var t;return(0,o.jsx)(a(),{src:null!==(t=null==e?void 0:e.thumbnail)&&void 0!==t?t:i.siteSettings.product.placeholder,alt:"coupon banner",width:42,height:42,className:"overflow-hidden rounded"})}},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-code"),ascending:g.sort===s.As.Asc&&"code"===g.column,isActive:"code"===g.column}),className:"cursor-pointer",dataIndex:"code",key:"code",align:"center",onHeaderCell:()=>onHeaderClick("code"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-coupon-amount"),ascending:g.sort===s.As.Asc&&"amount"===g.column,isActive:"amount"===g.column}),className:"cursor-pointer",dataIndex:"amount",key:"amount",align:"center",width:150,onHeaderCell:()=>onHeaderClick("amount"),render:function(e,t){let{price:n}=(0,c.ZP)({amount:e});return"PERCENTAGE_COUPON"===t.type?(0,o.jsxs)("span",{children:[e,"%"]}):(0,o.jsx)("span",{children:n})}},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-minimum-cart-amount"),ascending:g.sort===s.As.Asc&&"minimum_cart_amount"===g.column,isActive:"minimum_cart_amount"===g.column}),className:"cursor-pointer",dataIndex:"minimum_cart_amount",key:"minimum_cart_amount",align:"center",width:150,onHeaderCell:()=>onHeaderClick("minimum_cart_amount"),render:function(e){let{price:t}=(0,c.ZP)({amount:e});return(0,o.jsx)("span",{children:t})}},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-active"),ascending:g.sort===s.As.Asc&&"active_from"===g.column,isActive:"active_from"===g.column}),className:"cursor-pointer",dataIndex:"active_from",key:"active_from",align:"center",onHeaderCell:()=>onHeaderClick("active_from"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:p()().to(p().utc(e).tz(p().tz.guess()))})},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-expired"),ascending:g.sort===s.As.Asc&&"expire_at"===g.column,isActive:"expire_at"===g.column}),className:"cursor-pointer",dataIndex:"expire_at",key:"expire_at",align:"center",onHeaderCell:()=>onHeaderClick("expire_at"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:p()().to(p().utc(e).tz(p().tz.guess()))})},{title:(0,o.jsx)(h.Z,{title:m("table:table-item-status"),ascending:g.sort===s.As.Asc&&"is_approve"===g.column,isActive:"is_approve"===g.column}),className:"cursor-pointer",dataIndex:"is_approve",key:"is_approve",align:"center",width:150,onHeaderCell:()=>onHeaderClick("is_approve"),render:e=>(0,o.jsx)(I.Z,{textKey:e?"Approved":"Disapprove",color:e?"bg-accent/10 !text-accent":"bg-status-failed/10 text-status-failed"})},{title:m("table:table-item-actions"),dataIndex:"code",key:"actions",align:"right",width:260,render:(e,t)=>(0,o.jsx)(C.Z,{slug:e,record:t,deleteModalView:"DELETE_COUPON",routes:null===S.Z||void 0===S.Z?void 0:S.Z.coupon,isShop:!!b,shopSlug:null!=b?b:"",couponApproveButton:!0,isCouponApprove:null==t?void 0:t.is_approve})}];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,o.jsx)(u.i,{columns:T,emptyText:()=>(0,o.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,o.jsx)(E.m,{className:"w-52"}),(0,o.jsx)("div",{className:"pt-6 mb-1 text-base font-semibold text-heading",children:m("table:empty-table-data")}),(0,o.jsx)("p",{className:"text-[13px]",children:m("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:900}})}),!!(null==n?void 0:n.total)&&(0,o.jsx)("div",{className:"flex items-center justify-end",children:(0,o.jsx)(r.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:l})})]})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var o=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,o.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,o.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},8953:function(e,t,n){var o=n(85893),r=n(93967),l=n.n(r),a=n(5233),u=n(98388);t.Z=e=>{let{t}=(0,a.$G)(),{className:n,color:r,textColor:s,text:i,textKey:c,animate:d=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("span",{className:(0,u.m6)(l()("inline-block",p.root,{[p.default]:!r,[p.text]:!s,[p.animate]:d},r,s,n)),children:c?t(c):i})})}},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var o=n(85893),r=n(55891),l=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,o.jsx)(r.Z,{nextIcon:(0,o.jsx)(l.T,{}),prevIcon:(0,o.jsx)(ArrowPrev,{}),...e})},92717:function(e,t,n){n.d(t,{OR:function(){return useApproveCouponMutation},Bo:function(){return useCouponQuery},ID:function(){return useCouponsQuery},wr:function(){return useCreateCouponMutation},kN:function(){return useDeleteCouponMutation},l9:function(){return useDisApproveCouponMutation},w3:function(){return useUpdateCouponMutation},Mu:function(){return useVerifyCouponMutation}});var o=n(11163),r=n.n(o),l=n(88767),a=n(22920),u=n(5233),s=n(28597),i=n(47869),c=n(55191),d=n(3737);let p={...(0,c.h)(i.P.COUPONS),get(e){let{code:t,language:n}=e;return d.eN.get("".concat(i.P.COUPONS,"/").concat(t),{language:n})},paginated:e=>{let{code:t,...n}=e;return d.eN.get(i.P.COUPONS,{searchJoin:"and",...n,search:d.eN.formatSearchParams({code:t})})},verify:e=>d.eN.post(i.P.VERIFY_COUPONS,e),approve:e=>d.eN.post(i.P.APPROVE_COUPON,e),disapprove:e=>d.eN.post(i.P.DISAPPROVE_COUPON,e)};var f=n(97514),m=n(93345);let useCreateCouponMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,u.$G)(),n=(0,o.useRouter)();return(0,l.useMutation)(p.create,{onSuccess:async()=>{let e=n.query.shop?"/".concat(n.query.shop).concat(f.Z.coupon.list):f.Z.coupon.list;await r().push(e,void 0,{locale:m.Config.defaultLanguage}),a.Am.success(t("common:successfully-created"))},onError:e=>{var n;a.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))},onSettled:()=>{e.invalidateQueries(i.P.COUPONS)}})},useDeleteCouponMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,u.$G)();return(0,l.useMutation)(p.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.COUPONS)}})},useUpdateCouponMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,l.useQueryClient)(),n=(0,o.useRouter)();return(0,l.useMutation)(p.update,{onSuccess:async t=>{let o=n.query.shop?"/".concat(n.query.shop).concat(f.Z.coupon.list):f.Z.coupon.list;await n.push(o,void 0,{locale:m.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.COUPONS)},onError:t=>{var n;a.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useVerifyCouponMutation=()=>(0,l.useMutation)(p.verify),useCouponQuery=e=>{let{code:t,language:n}=e,{data:o,error:r,isLoading:a}=(0,l.useQuery)([i.P.COUPONS,{code:t,language:n}],()=>p.get({code:t,language:n}));return{coupon:o,error:r,loading:a}},useCouponsQuery=e=>{var t;let{data:n,error:o,isLoading:r}=(0,l.useQuery)([i.P.COUPONS,e],e=>{let{queryKey:t,pageParam:n}=e;return p.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{coupons:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,s.Q)(n),error:o,loading:r}},useApproveCouponMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(p.approve,{onSuccess:()=>{a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.COUPONS)}})},useDisApproveCouponMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(p.disapprove,{onSuccess:()=>{a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.COUPONS)}})}},60942:function(e,t,n){n.d(t,{T4:function(){return formatPrice},ZP:function(){return usePrice}});var o=n(67294),r=n(99494),l=n(73263);function formatPrice(e){let{amount:t,currencyCode:n,locale:o,fractions:r=2}=e,l=new Intl.NumberFormat(o,{style:"currency",currency:n,maximumFractionDigits:r>20||r<0||!r?2:r});return l.format(t)}function usePrice(e){let{currency:t,currencyOptions:n}=(0,l.rV)(),{formation:a,fractions:u}=n,{amount:s,baseAmount:i,currencyCode:c=t}=null!=e?e:{},d=null!=a?a:r.siteSettings.defaultLanguage,p=(0,o.useMemo)(()=>"number"==typeof s&&c?i?function(e){let{amount:t,baseAmount:n,currencyCode:o,locale:r,fractions:l=2}=e,a=n<t,u=new Intl.NumberFormat(r,{style:"percent"}),s=a?u.format((t-n)/t):null,i=formatPrice({amount:t,currencyCode:o,locale:r,fractions:l}),c=a?formatPrice({amount:n,currencyCode:o,locale:r,fractions:l}):null;return{price:i,basePrice:c,discount:s}}({amount:s,baseAmount:i,currencyCode:c,locale:d,fractions:u}):formatPrice({amount:s,currencyCode:c,locale:d,fractions:u}):"",[s,i,c]);return"string"==typeof p?{price:p,basePrice:null,discount:null}:p}},28368:function(e,t,n){n.d(t,{p:function(){return I}});var o,r,l,a=n(67294),u=n(32984),s=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),m=n(14157),v=n(15466),b=n(73781);let P=null!=(l=a.startTransition)?l:function(e){e()};var g=((o=g||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),y=((r=y||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let x={0:e=>({...e,disclosureState:(0,u.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},h=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(h);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}h.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let C=(0,a.createContext)(null);function Y(e,t){return(0,u.E)(t.type,x,e,t)}C.displayName="DisclosurePanelContext";let E=a.Fragment,N=s.AN.RenderStrategy|s.AN.Static,I=Object.assign((0,s.yV)(function(e,t){let{defaultOpen:n=!1,...o}=e,r=(0,a.useRef)(null),l=(0,i.T)(t,(0,i.h)(e=>{r.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:P},g]=p,y=(0,b.z)(e=>{g({type:1});let t=(0,v.r)(r);if(!t||!P)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(P):t.getElementById(P);null==n||n.focus()}),x=(0,a.useMemo)(()=>({close:y}),[y]),C=(0,a.useMemo)(()=>({open:0===m,close:y}),[m,y]);return a.createElement(h.Provider,{value:p},a.createElement(S.Provider,{value:x},a.createElement(f.up,{value:(0,u.E)(m,{0:f.ZM.Open,1:f.ZM.Closed})},(0,s.sY)({ourProps:{ref:l},theirProps:o,slot:C,defaultTag:E,name:"Disclosure"}))))}),{Button:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-button-${n}`,...r}=e,[l,u]=M("Disclosure.Button"),f=(0,a.useContext)(C),v=null!==f&&f===l.panelId,P=(0,a.useRef)(null),g=(0,i.T)(P,t,v?null:l.buttonRef);(0,a.useEffect)(()=>{if(!v)return u({type:2,buttonId:o}),()=>{u({type:2,buttonId:null})}},[o,u,v]);let y=(0,b.z)(e=>{var t;if(v){if(1===l.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0}),null==(t=l.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),u({type:0})}}),x=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),h=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(v?(u({type:0}),null==(n=l.buttonRef.current)||n.focus()):u({type:0}))}),S=(0,a.useMemo)(()=>({open:0===l.disclosureState}),[l]),E=(0,m.f)(e,P),N=v?{ref:g,type:E,onKeyDown:y,onClick:h}:{ref:g,id:o,type:E,"aria-expanded":0===l.disclosureState,"aria-controls":l.linkedPanel?l.panelId:void 0,onKeyDown:y,onKeyUp:x,onClick:h};return(0,s.sY)({ourProps:N,theirProps:r,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-panel-${n}`,...r}=e,[l,u]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,l.panelRef,e=>{P(()=>u({type:e?4:5}))});(0,a.useEffect)(()=>(u({type:3,panelId:o}),()=>{u({type:3,panelId:null})}),[o,u]);let m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===l.disclosureState,b=(0,a.useMemo)(()=>({open:0===l.disclosureState,close:d}),[l,d]);return a.createElement(C.Provider,{value:l.panelId},(0,s.sY)({ourProps:{ref:p,id:o},theirProps:r,slot:b,defaultTag:"div",features:N,visible:v,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){n.d(t,{J:function(){return Z}});var o,r,l=n(67294),a=n(32984),u=n(12351),s=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),m=n(14157),v=n(39650),b=n(15466),P=n(51074),g=n(14007),y=n(46045),x=n(73781),h=n(45662),S=n(3855),C=n(16723),E=n(65958),N=n(2740),I=((o=I||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),A=((r=A||{})[r.TogglePopover=0]="TogglePopover",r[r.ClosePopover=1]="ClosePopover",r[r.SetButton=2]="SetButton",r[r.SetButtonId=3]="SetButtonId",r[r.SetPanel=4]="SetPanel",r[r.SetPanelId=5]="SetPanelId",r);let O={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},T=(0,l.createContext)(null);function oe(e){let t=(0,l.useContext)(T);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}T.displayName="PopoverContext";let k=(0,l.createContext)(null);function fe(e){let t=(0,l.useContext)(k);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}k.displayName="PopoverAPIContext";let j=(0,l.createContext)(null);function Ee(){return(0,l.useContext)(j)}j.displayName="PopoverGroupContext";let D=(0,l.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,O,e,t)}D.displayName="PopoverPanelContext";let _=u.AN.RenderStrategy|u.AN.Static,R=u.AN.RenderStrategy|u.AN.Static,Z=Object.assign((0,u.yV)(function(e,t){var n;let{__demoMode:o=!1,...r}=e,i=(0,l.useRef)(null),c=(0,s.T)(t,(0,s.h)(e=>{i.current=e})),d=(0,l.useRef)([]),m=(0,l.useReducer)(Ne,{__demoMode:o,popoverState:o?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,l.createRef)(),afterPanelSentinel:(0,l.createRef)()}),[{popoverState:b,button:y,buttonId:h,panel:C,panelId:I,beforePanelSentinel:A,afterPanelSentinel:O},j]=m,_=(0,P.i)(null!=(n=i.current)?n:y),R=(0,l.useMemo)(()=>{if(!y||!C)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(y))^Number(null==e?void 0:e.contains(C)))return!0;let e=(0,p.GO)(),t=e.indexOf(y),n=(t+e.length-1)%e.length,o=(t+1)%e.length,r=e[n],l=e[o];return!C.contains(r)&&!C.contains(l)},[y,C]),Z=(0,S.E)(h),B=(0,S.E)(I),F=(0,l.useMemo)(()=>({buttonId:Z,panelId:B,close:()=>j({type:1})}),[Z,B,j]),z=Ee(),H=null==z?void 0:z.registerPopover,U=(0,x.z)(()=>{var e;return null!=(e=null==z?void 0:z.isFocusWithinPopoverGroup())?e:(null==_?void 0:_.activeElement)&&((null==y?void 0:y.contains(_.activeElement))||(null==C?void 0:C.contains(_.activeElement)))});(0,l.useEffect)(()=>null==H?void 0:H(F),[H,F]);let[L,$]=(0,N.k)(),G=(0,E.v)({mainTreeNodeRef:null==z?void 0:z.mainTreeNodeRef,portals:L,defaultContainers:[y,C]});(0,g.O)(null==_?void 0:_.defaultView,"focus",e=>{var t,n,o,r;e.target!==window&&e.target instanceof HTMLElement&&0===b&&(U()||y&&C&&(G.contains(e.target)||null!=(n=null==(t=A.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(r=null==(o=O.current)?void 0:o.contains)&&r.call(o,e.target)||j({type:1})))},!0),(0,v.O)(G.resolveContainers,(e,t)=>{j({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==y||y.focus())},0===b);let V=(0,x.z)(e=>{j({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:y:y;null==t||t.focus()}),Q=(0,l.useMemo)(()=>({close:V,isPortalled:R}),[V,R]),K=(0,l.useMemo)(()=>({open:0===b,close:V}),[b,V]);return l.createElement(D.Provider,{value:null},l.createElement(T.Provider,{value:m},l.createElement(k.Provider,{value:Q},l.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},l.createElement($,null,(0,u.sY)({ourProps:{ref:c},theirProps:r,slot:K,defaultTag:"div",name:"Popover"}),l.createElement(G.MainTreeNode,null))))))}),{Button:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-button-${n}`,...r}=e,[f,v]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),g=(0,l.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,C=Ee(),E=null==C?void 0:C.closeOthers,N=null!==(0,l.useContext)(D);(0,l.useEffect)(()=>{if(!N)return v({type:3,buttonId:o}),()=>{v({type:3,buttonId:null})}},[N,o,v]);let[I]=(0,l.useState)(()=>Symbol()),A=(0,s.T)(g,t,N?null:e=>{if(e)f.buttons.current.push(I);else{let e=f.buttons.current.indexOf(I);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&v({type:2,button:e})}),O=(0,s.T)(g,t),T=(0,P.i)(g),k=(0,x.z)(e=>{var t,n,o;if(N){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),v({type:1}),null==(o=f.button)||o.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==E||E(f.buttonId)),v({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==E?void 0:E(f.buttonId);if(!g.current||null!=T&&T.activeElement&&!g.current.contains(T.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1})}}),j=(0,x.z)(e=>{N||e.key===c.R.Space&&e.preventDefault()}),_=(0,x.z)(t=>{var n,o;(0,d.P)(t.currentTarget)||e.disabled||(N?(v({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==E||E(f.buttonId)),v({type:0}),null==(o=f.button)||o.focus()))}),R=(0,x.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===f.popoverState,B=(0,l.useMemo)(()=>({open:Z}),[Z]),F=(0,m.f)(e,g),z=N?{ref:O,type:F,onKeyDown:k,onClick:_}:{ref:A,id:f.buttonId,type:F,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:k,onKeyUp:j,onClick:_,onMouseDown:R},H=(0,h.l)(),U=(0,x.z)(()=>{let e=f.panel;e&&(0,a.E)(H.current,{[h.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[h.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(H.current,{[h.N.Forwards]:p.TO.Next,[h.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return l.createElement(l.Fragment,null,(0,u.sY)({ourProps:z,theirProps:r,slot:B,defaultTag:"button",name:"Popover.Button"}),Z&&!N&&b&&l.createElement(y._,{id:S,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:U}))}),Overlay:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-overlay-${n}`,...r}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,s.T)(t),m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===a,b=(0,x.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),P=(0,l.useMemo)(()=>({open:0===a}),[a]);return(0,u.sY)({ourProps:{ref:p,id:o,"aria-hidden":!0,onClick:b},theirProps:r,slot:P,defaultTag:"div",features:_,visible:v,name:"Popover.Overlay"})}),Panel:(0,u.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-panel-${n}`,focus:r=!1,...d}=e,[m,v]=oe("Popover.Panel"),{close:b,isPortalled:g}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,E=`headlessui-focus-sentinel-after-${(0,i.M)()}`,N=(0,l.useRef)(null),I=(0,s.T)(N,t,e=>{v({type:4,panel:e})}),A=(0,P.i)(N);(0,C.e)(()=>(v({type:5,panelId:o}),()=>{v({type:5,panelId:null})}),[o,v]);let O=(0,f.oJ)(),T=null!==O?(O&f.ZM.Open)===f.ZM.Open:0===m.popoverState,k=(0,x.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==m.popoverState||!N.current||null!=A&&A.activeElement&&!N.current.contains(A.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1}),null==(t=m.button)||t.focus()}});(0,l.useEffect)(()=>{var t;e.static||1===m.popoverState&&(null==(t=e.unmount)||t)&&v({type:4,panel:null})},[m.popoverState,e.unmount,e.static,v]),(0,l.useEffect)(()=>{if(m.__demoMode||!r||0!==m.popoverState||!N.current)return;let e=null==A?void 0:A.activeElement;N.current.contains(e)||(0,p.jA)(N.current,p.TO.First)},[m.__demoMode,r,N,m.popoverState]);let j=(0,l.useMemo)(()=>({open:0===m.popoverState,close:b}),[m,b]),_={ref:I,id:o,onKeyDown:k,onBlur:r&&0===m.popoverState?e=>{var t,n,o,r,l;let a=e.relatedTarget;a&&N.current&&(null!=(t=N.current)&&t.contains(a)||(v({type:1}),(null!=(o=null==(n=m.beforePanelSentinel.current)?void 0:n.contains)&&o.call(n,a)||null!=(l=null==(r=m.afterPanelSentinel.current)?void 0:r.contains)&&l.call(r,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,h.l)(),B=(0,x.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[h.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=m.afterPanelSentinel.current)||t.focus())},[h.N.Backwards]:()=>{var e;null==(e=m.button)||e.focus({preventScroll:!0})}})}),F=(0,x.z)(()=>{let e=N.current;e&&(0,a.E)(Z.current,{[h.N.Forwards]:()=>{var e;if(!m.button)return;let t=(0,p.GO)(),n=t.indexOf(m.button),o=t.slice(0,n+1),r=[...t.slice(n+1),...o];for(let t of r.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=m.panel)&&e.contains(t)){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}(0,p.jA)(r,p.TO.First,{sorted:!1})},[h.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=m.button)||t.focus())}})});return l.createElement(D.Provider,{value:o},T&&g&&l.createElement(y._,{id:S,ref:m.beforePanelSentinel,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:B}),(0,u.sY)({ourProps:_,theirProps:d,slot:j,defaultTag:"div",features:R,visible:T,name:"Popover.Panel"}),T&&g&&l.createElement(y._,{id:E,ref:m.afterPanelSentinel,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}))}),Group:(0,u.yV)(function(e,t){let n=(0,l.useRef)(null),o=(0,s.T)(n,t),[r,a]=(0,l.useState)([]),i=(0,E.H)(),c=(0,x.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,x.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,x.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let o=t.activeElement;return!!(null!=(e=n.current)&&e.contains(o))||r.some(e=>{var n,r;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(o))||(null==(r=t.getElementById(e.panelId.current))?void 0:r.contains(o))})}),f=(0,x.z)(e=>{for(let t of r)t.buttonId.current!==e&&t.close()}),m=(0,l.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),v=(0,l.useMemo)(()=>({}),[]);return l.createElement(j.Provider,{value:m},(0,u.sY)({ourProps:{ref:o},theirProps:e,slot:v,defaultTag:"div",name:"Popover.Group"}),l.createElement(i.MainTreeNode,null))})})}}]);