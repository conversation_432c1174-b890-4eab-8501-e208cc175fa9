(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1960,2036],{4378:function(e,l,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/products/[productSlug]/[action]",function(){return i(11330)}])},47133:function(e,l,i){"use strict";var t=i(85893),s=i(78985),r=i(79362),n=i(11163),d=i(16203),o=i(1631),a=i(99494),u=i(5233),c=i(74673),p=i(8144),h=i(90573),f=i(48583),g=i(93967),m=i.n(g),v=i(30824),x=i(62964);let SidebarItemMap=e=>{var l,i;let s,a,{menuItems:c}=e,{locale:p}=(0,n.useRouter)(),{t:g}=(0,u.$G)(),{settings:m}=(0,h.n)({language:p}),{childMenu:v}=c,b=null==m?void 0:null===(l=m.options)||void 0===l?void 0:l.enableTerms,j=null==m?void 0:null===(i=m.options)||void 0===i?void 0:i.enableCoupons,{permissions:N}=(0,d.WA)(),[S,w]=(0,f.KO)(r.Hf),{width:_}=(0,x.Z)(),{query:{shop:Z}}=(0,n.useRouter)();return!b&&(s=null==c?void 0:c.childMenu.find(e=>"Terms And Conditions"===e.label))&&(s.permissions=d.M$),!j&&(a=null==c?void 0:c.childMenu.find(e=>"Coupons"===e.label))&&(a.permissions=d.M$),(0,t.jsx)("div",{className:"space-y-2",children:null==v?void 0:v.map(e=>{let{href:l,label:i,icon:s,permissions:n,childMenu:a}=e;return a||(0,d.Ft)(n,N)?(0,t.jsx)(o.Z,{href:l(null==Z?void 0:Z.toString()),label:g(i),icon:s,childMenu:a,miniSidebar:S&&_>=r.h2},i):null})})},SideBarGroup=()=>{var e,l;let[i,s]=(0,f.KO)(r.Hf),{role:n}=(0,d.WA)(),o="staff"===n?null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===a.siteSettings||void 0===a.siteSettings?void 0:null===(l=a.siteSettings.sidebarLinks)||void 0===l?void 0:l.shop,c=Object.keys(o),{width:p}=(0,x.Z)(),{t:h}=(0,u.$G)();return(0,t.jsx)(t.Fragment,{children:null==c?void 0:c.map((e,l)=>{var s;return(0,t.jsxs)("div",{className:m()("flex flex-col px-5",i&&p>=r.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",i&&p>=r.h2?"hidden":""),children:h(null===(s=o[e])||void 0===s?void 0:s.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:o[e]})]},l)})})};l.Z=e=>{let{children:l}=e,[i,d]=(0,f.KO)(r.Hf),{locale:o}=(0,n.useRouter)(),{width:a}=(0,x.Z)(),[u]=(0,f.KO)(r.GH),[h]=(0,f.KO)(r.W4);return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===o||"he"===o?"rtl":"ltr",children:[(0,t.jsx)(s.Z,{}),(0,t.jsx)(c.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",a>=r.h2&&(u||h)?"pt-[8.75rem]":"pt-20",i&&a>=r.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,t.jsx)(v.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",a>=r.h2&&(u||h)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",i&&a>=r.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,t.jsx)(p.Z,{})]})]})]})}},11330:function(e,l,i){"use strict";i.r(l),i.d(l,{__N_SSP:function(){return m},default:function(){return UpdateProductPage}});var t=i(85893),s=i(81696),r=i(45957),n=i(55846),d=i(11163),o=i(5233),a=i(47133),u=i(16203),c=i(93242),p=i(93345),h=i(97514),f=i(30042),g=i(99930),m=!0;function UpdateProductPage(){var e,l;let{query:i,locale:a}=(0,d.useRouter)(),{t:m}=(0,o.$G)(),v=(0,d.useRouter)(),{permissions:x}=(0,u.WA)(),{data:b}=(0,g.UE)(),{data:j}=(0,f.DZ)({slug:null==i?void 0:i.shop}),N=null==j?void 0:j.id,{product:S,isLoading:w,error:_}=(0,c.FA)({slug:i.productSlug,language:"edit"===i.action.toString()?a:p.Config.defaultLanguage});return w?(0,t.jsx)(n.Z,{text:m("common:text-loading")}):_?(0,t.jsx)(r.Z,{message:_.message}):((0,u.Ft)(u.M$,x)||(null==b?void 0:null===(e=b.shops)||void 0===e?void 0:e.map(e=>e.id).includes(N))||(null==b?void 0:null===(l=b.managed_shop)||void 0===l?void 0:l.id)==N||v.replace(h.Z.dashboard),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-heading",children:m("form:form-title-edit-product")})}),(0,t.jsx)(s.Z,{initialValues:S})]}))}UpdateProductPage.authenticate={permissions:u.ce},UpdateProductPage.Layout=a.Z}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,7755,939,7557,3316,9494,5535,8186,1285,1631,5468,6256,1696,9774,2888,179],function(){return e(e.s=4378)}),_N_E=e.O()}]);