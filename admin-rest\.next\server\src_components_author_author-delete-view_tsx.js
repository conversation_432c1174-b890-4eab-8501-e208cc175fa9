"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_author_author-delete-view_tsx";
exports.ids = ["src_components_author_author-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/author/author-delete-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/author/author-delete-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\n/* harmony import */ var _data_author__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/author */ \"./src/data/author.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _utils_form_error__WEBPACK_IMPORTED_MODULE_3__, _data_author__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _utils_form_error__WEBPACK_IMPORTED_MODULE_3__, _data_author__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthorDeleteView = ()=>{\n    const { mutate: deleteAuthor, isLoading: loading } = (0,_data_author__WEBPACK_IMPORTED_MODULE_4__.useDeleteAuthorMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        try {\n            deleteAuthor({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_3__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\author\\\\author-delete-view.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthorDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/author/author-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/author.ts":
/*!****************************!*\
  !*** ./src/data/author.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthorQuery: () => (/* binding */ useAuthorQuery),\n/* harmony export */   useAuthorsQuery: () => (/* binding */ useAuthorsQuery),\n/* harmony export */   useCreateAuthorMutation: () => (/* binding */ useCreateAuthorMutation),\n/* harmony export */   useDeleteAuthorMutation: () => (/* binding */ useDeleteAuthorMutation),\n/* harmony export */   useUpdateAuthorMutation: () => (/* binding */ useUpdateAuthorMutation),\n/* harmony export */   useUpdateAuthorMutationInList: () => (/* binding */ useUpdateAuthorMutationInList)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_author__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./client/author */ \"./src/data/client/author.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _client_author__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _client_author__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateAuthorMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.author.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.author.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useDeleteAuthorMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useUpdateAuthorMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.author.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.author.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useUpdateAuthorMutationInList = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.update, {\n        onSuccess: async ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\nconst useAuthorQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS,\n        {\n            slug,\n            language\n        }\n    ], ()=>_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.get({\n            slug,\n            language\n        }));\n    return {\n        author: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useAuthorsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.AUTHORS,\n        options\n    ], ({ queryKey, pageParam })=>_client_author__WEBPACK_IMPORTED_MODULE_7__.AuthorClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        authors: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/author.ts\n");

/***/ }),

/***/ "./src/data/client/author.ts":
/*!***********************************!*\
  !*** ./src/data/client/author.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthorClient: () => (/* binding */ AuthorClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst AuthorClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTHORS),\n    paginated: ({ type, name, is_approved, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTHORS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                is_approved\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvYXV0aG9yLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFPZ0Q7QUFDSDtBQUNGO0FBRXBDLE1BQU1HLGVBQWU7SUFDMUIsR0FBR0YsMERBQVdBLENBQ1pELHlEQUFhQSxDQUFDSSxPQUFPLENBQ3RCO0lBQ0RDLFdBQVcsQ0FBQyxFQUNWQyxJQUFJLEVBQ0pDLElBQUksRUFDSkMsV0FBVyxFQUNYLEdBQUdDLFFBQ3lCO1FBQzVCLE9BQU9QLG9EQUFVQSxDQUFDUSxHQUFHLENBQWtCVix5REFBYUEsQ0FBQ0ksT0FBTyxFQUFFO1lBQzVETyxZQUFZO1lBQ1osR0FBR0YsTUFBTTtZQUNURyxRQUFRVixvREFBVUEsQ0FBQ1csa0JBQWtCLENBQUM7Z0JBQUVQO2dCQUFNQztnQkFBTUM7WUFBWTtRQUNsRTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9kYXRhL2NsaWVudC9hdXRob3IudHM/M2UxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xyXG4gIEF1dGhvcixcclxuICBDcmVhdGVBdXRob3JJbnB1dCxcclxuICBBdXRob3JRdWVyeU9wdGlvbnMsXHJcbiAgQXV0aG9yUGFnaW5hdG9yLFxyXG4gIFF1ZXJ5T3B0aW9ucyxcclxufSBmcm9tICdAL3R5cGVzJztcclxuaW1wb3J0IHsgQVBJX0VORFBPSU5UUyB9IGZyb20gJy4vYXBpLWVuZHBvaW50cyc7XHJcbmltcG9ydCB7IGNydWRGYWN0b3J5IH0gZnJvbSAnLi9jdXJkLWZhY3RvcnknO1xyXG5pbXBvcnQgeyBIdHRwQ2xpZW50IH0gZnJvbSAnLi9odHRwLWNsaWVudCc7XHJcblxyXG5leHBvcnQgY29uc3QgQXV0aG9yQ2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PEF1dGhvciwgUXVlcnlPcHRpb25zLCBDcmVhdGVBdXRob3JJbnB1dD4oXHJcbiAgICBBUElfRU5EUE9JTlRTLkFVVEhPUlNcclxuICApLFxyXG4gIHBhZ2luYXRlZDogKHtcclxuICAgIHR5cGUsXHJcbiAgICBuYW1lLFxyXG4gICAgaXNfYXBwcm92ZWQsXHJcbiAgICAuLi5wYXJhbXNcclxuICB9OiBQYXJ0aWFsPEF1dGhvclF1ZXJ5T3B0aW9ucz4pID0+IHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxBdXRob3JQYWdpbmF0b3I+KEFQSV9FTkRQT0lOVFMuQVVUSE9SUywge1xyXG4gICAgICBzZWFyY2hKb2luOiAnYW5kJyxcclxuICAgICAgLi4ucGFyYW1zLFxyXG4gICAgICBzZWFyY2g6IEh0dHBDbGllbnQuZm9ybWF0U2VhcmNoUGFyYW1zKHsgdHlwZSwgbmFtZSwgaXNfYXBwcm92ZWQgfSksXHJcbiAgICB9KTtcclxuICB9LFxyXG59O1xyXG4iXSwibmFtZXMiOlsiQVBJX0VORFBPSU5UUyIsImNydWRGYWN0b3J5IiwiSHR0cENsaWVudCIsIkF1dGhvckNsaWVudCIsIkFVVEhPUlMiLCJwYWdpbmF0ZWQiLCJ0eXBlIiwibmFtZSIsImlzX2FwcHJvdmVkIiwicGFyYW1zIiwiZ2V0Iiwic2VhcmNoSm9pbiIsInNlYXJjaCIsImZvcm1hdFNlYXJjaFBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/client/author.ts\n");

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvZm9ybS1lcnJvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFpQztBQUNEO0FBRXpCLFNBQVNFLGdCQUFnQkMsS0FBVTtJQUN4QyxJQUFJQyxpQkFBaUI7UUFDbkJDLFNBQVM7UUFDVEMsWUFBWSxFQUFFO0lBQ2hCO0lBRUEsSUFBSUgsTUFBTUksYUFBYSxFQUFFO1FBQ3ZCLEtBQUssTUFBTUMsZ0JBQWdCTCxNQUFNSSxhQUFhLENBQUU7WUFDOUMsSUFDRUMsYUFBYUMsVUFBVSxJQUN2QkQsYUFBYUMsVUFBVSxDQUFDQyxRQUFRLEtBQUssY0FDckM7Z0JBQ0FOLGNBQWMsQ0FBQyxVQUFVLEdBQUdJLGFBQWFILE9BQU87Z0JBQ2hERCxjQUFjLENBQUMsYUFBYSxHQUFHSSxhQUFhQyxVQUFVLENBQUNILFVBQVU7Z0JBQ2pFLE9BQU9GO1lBQ1QsT0FBTyxJQUNMSSxhQUFhQyxVQUFVLElBQ3ZCRCxhQUFhQyxVQUFVLENBQUNDLFFBQVEsS0FBSyxpQkFDckM7Z0JBQ0FULHdEQUFjLENBQUM7Z0JBQ2ZBLHdEQUFjLENBQUM7Z0JBQ2ZELHVEQUFXLENBQUM7WUFDZDtRQUNGO0lBQ0Y7SUFDQUksY0FBYyxDQUFDLFVBQVUsR0FBR0QsTUFBTUUsT0FBTztJQUN6QyxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vc3JjL3V0aWxzL2Zvcm0tZXJyb3IudHN4PzkyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJvdXRlciBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCBDb29raWVzIGZyb20gJ2pzLWNvb2tpZSc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZ2V0RXJyb3JNZXNzYWdlKGVycm9yOiBhbnkpIHtcclxuICBsZXQgcHJvY2Vzc2VkRXJyb3IgPSB7XHJcbiAgICBtZXNzYWdlOiAnJyxcclxuICAgIHZhbGlkYXRpb246IFtdLFxyXG4gIH07XHJcblxyXG4gIGlmIChlcnJvci5ncmFwaFFMRXJyb3JzKSB7XHJcbiAgICBmb3IgKGNvbnN0IGdyYXBoUUxFcnJvciBvZiBlcnJvci5ncmFwaFFMRXJyb3JzKSB7XHJcbiAgICAgIGlmIChcclxuICAgICAgICBncmFwaFFMRXJyb3IuZXh0ZW5zaW9ucyAmJlxyXG4gICAgICAgIGdyYXBoUUxFcnJvci5leHRlbnNpb25zLmNhdGVnb3J5ID09PSAndmFsaWRhdGlvbidcclxuICAgICAgKSB7XHJcbiAgICAgICAgcHJvY2Vzc2VkRXJyb3JbJ21lc3NhZ2UnXSA9IGdyYXBoUUxFcnJvci5tZXNzYWdlO1xyXG4gICAgICAgIHByb2Nlc3NlZEVycm9yWyd2YWxpZGF0aW9uJ10gPSBncmFwaFFMRXJyb3IuZXh0ZW5zaW9ucy52YWxpZGF0aW9uO1xyXG4gICAgICAgIHJldHVybiBwcm9jZXNzZWRFcnJvcjtcclxuICAgICAgfSBlbHNlIGlmIChcclxuICAgICAgICBncmFwaFFMRXJyb3IuZXh0ZW5zaW9ucyAmJlxyXG4gICAgICAgIGdyYXBoUUxFcnJvci5leHRlbnNpb25zLmNhdGVnb3J5ID09PSAnYXV0aG9yaXphdGlvbidcclxuICAgICAgKSB7XHJcbiAgICAgICAgQ29va2llcy5yZW1vdmUoJ2F1dGhfdG9rZW4nKTtcclxuICAgICAgICBDb29raWVzLnJlbW92ZSgnYXV0aF9wZXJtaXNzaW9ucycpO1xyXG4gICAgICAgIFJvdXRlci5wdXNoKCcvJyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbiAgcHJvY2Vzc2VkRXJyb3JbJ21lc3NhZ2UnXSA9IGVycm9yLm1lc3NhZ2U7XHJcbiAgcmV0dXJuIHByb2Nlc3NlZEVycm9yO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJSb3V0ZXIiLCJDb29raWVzIiwiZ2V0RXJyb3JNZXNzYWdlIiwiZXJyb3IiLCJwcm9jZXNzZWRFcnJvciIsIm1lc3NhZ2UiLCJ2YWxpZGF0aW9uIiwiZ3JhcGhRTEVycm9ycyIsImdyYXBoUUxFcnJvciIsImV4dGVuc2lvbnMiLCJjYXRlZ29yeSIsInJlbW92ZSIsInB1c2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n");

/***/ })

};
;