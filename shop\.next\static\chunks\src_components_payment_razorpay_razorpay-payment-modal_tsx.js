"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_payment_razorpay_razorpay-payment-modal_tsx"],{

/***/ "./src/components/payment/razorpay/razorpay-payment-modal.tsx":
/*!********************************************************************!*\
  !*** ./src/components/payment/razorpay/razorpay-payment-modal.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/use-razorpay */ \"./src/lib/use-razorpay.ts\");\n/* harmony import */ var _lib_format_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/format-address */ \"./src/lib/format-address.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _framework_order__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/order */ \"./src/framework/rest/order.ts\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst RazorpayPaymentModal = (param)=>{\n    let { trackingNumber, paymentIntentInfo, paymentGateway } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    const { loadRazorpayScript, checkScriptLoaded } = (0,_lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { settings, isLoading: isSettingsLoading } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const { order, isLoading, refetch } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder)({\n        tracking_number: trackingNumber\n    });\n    const { createOrderPayment } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment)();\n    // @ts-ignore\n    const { customer_name, customer_contact, customer, billing_address } = order !== null && order !== void 0 ? order : {};\n    const paymentHandle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        var _settings_logo;\n        if (!checkScriptLoaded()) {\n            await loadRazorpayScript();\n        }\n        const options = {\n            key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\n            amount: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.amount,\n            currency: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.currency,\n            name: customer_name,\n            description: \"\".concat(t(\"text-order\"), \"#\").concat(trackingNumber),\n            image: settings === null || settings === void 0 ? void 0 : (_settings_logo = settings.logo) === null || _settings_logo === void 0 ? void 0 : _settings_logo.original,\n            order_id: paymentIntentInfo === null || paymentIntentInfo === void 0 ? void 0 : paymentIntentInfo.payment_id,\n            handler: async ()=>{\n                closeModal();\n                createOrderPayment({\n                    tracking_number: trackingNumber,\n                    payment_gateway: \"razorpay\"\n                });\n            },\n            prefill: {\n                ...customer_name && {\n                    name: customer_name\n                },\n                ...customer_contact && {\n                    contact: \"+\".concat(customer_contact)\n                },\n                ...(customer === null || customer === void 0 ? void 0 : customer.email) && {\n                    email: customer === null || customer === void 0 ? void 0 : customer.email\n                }\n            },\n            notes: {\n                address: (0,_lib_format_address__WEBPACK_IMPORTED_MODULE_3__.formatAddress)(billing_address)\n            },\n            modal: {\n                ondismiss: async ()=>{\n                    closeModal();\n                    await refetch();\n                }\n            }\n        };\n        const razorpay = window.Razorpay(options);\n        return razorpay.open();\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isSettingsLoading) {\n            (async ()=>{\n                await paymentHandle();\n            })();\n        }\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    if (isLoading || isSettingsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            showText: false\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\razorpay\\\\razorpay-payment-modal.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, undefined);\n    }\n    return null;\n};\n_s(RazorpayPaymentModal, \"hcUl2sNbUAcfvhIaTW5i7B6+MrI=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction,\n        _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        _framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings,\n        _framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder,\n        _framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment\n    ];\n});\n_c = RazorpayPaymentModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RazorpayPaymentModal);\nvar _c;\n$RefreshReg$(_c, \"RazorpayPaymentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wYXltZW50L3Jhem9ycGF5L3Jhem9ycGF5LXBheW1lbnQtbW9kYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUErQztBQUNtQjtBQUNiO0FBRVA7QUFDdUI7QUFDbEI7QUFDVztBQUNBO0FBUTlELE1BQU1VLHVCQUF3QztRQUFDLEVBQzdDQyxjQUFjLEVBQ2RDLGlCQUFpQixFQUNqQkMsY0FBYyxFQUNmOztJQUNDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdWLDREQUFjQTtJQUM1QixNQUFNLEVBQUVXLFVBQVUsRUFBRSxHQUFHVixrRkFBY0E7SUFDckMsTUFBTSxFQUFFVyxrQkFBa0IsRUFBRUMsaUJBQWlCLEVBQUUsR0FBR2YsNkRBQVdBO0lBQzdELE1BQU0sRUFBRWdCLFFBQVEsRUFBRUMsV0FBV0MsaUJBQWlCLEVBQUUsR0FBR2QsZ0VBQVdBO0lBQzlELE1BQU0sRUFBRWUsS0FBSyxFQUFFRixTQUFTLEVBQUVHLE9BQU8sRUFBRSxHQUFHZiwwREFBUUEsQ0FBQztRQUM3Q2dCLGlCQUFpQlo7SUFDbkI7SUFDQSxNQUFNLEVBQUVhLGtCQUFrQixFQUFFLEdBQUdoQixpRUFBZUE7SUFFOUMsYUFBYTtJQUNiLE1BQU0sRUFBRWlCLGFBQWEsRUFBRUMsZ0JBQWdCLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFLEdBQ2xFUCxrQkFBQUEsbUJBQUFBLFFBQVMsQ0FBQztJQUVaLE1BQU1RLGdCQUFnQjdCLGtEQUFXQSxDQUFDO1lBVXZCa0I7UUFUVCxJQUFJLENBQUNELHFCQUFxQjtZQUN4QixNQUFNRDtRQUNSO1FBQ0EsTUFBTWMsVUFBMkI7WUFDL0JDLEtBQUtDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsMkJBQTJCO1lBQzVDQyxNQUFNLEVBQUV2Qiw4QkFBQUEsd0NBQUFBLGtCQUFtQnVCLE1BQU07WUFDakNDLFFBQVEsRUFBRXhCLDhCQUFBQSx3Q0FBQUEsa0JBQW1Cd0IsUUFBUTtZQUNyQ0MsTUFBTVo7WUFDTmEsYUFBYSxHQUFzQjNCLE9BQW5CRyxFQUFFLGVBQWMsS0FBa0IsT0FBZkg7WUFDbkM0QixLQUFLLEVBQUVyQixxQkFBQUEsZ0NBQUFBLGlCQUFBQSxTQUFVc0IsSUFBSSxjQUFkdEIscUNBQUFBLGVBQWdCdUIsUUFBUTtZQUMvQkMsUUFBUSxFQUFFOUIsOEJBQUFBLHdDQUFBQSxrQkFBbUIrQixVQUFVO1lBQ3ZDQyxTQUFTO2dCQUNQN0I7Z0JBQ0FTLG1CQUFtQjtvQkFDakJELGlCQUFpQlo7b0JBQ2pCa0MsaUJBQWlCO2dCQUNuQjtZQUNGO1lBQ0FDLFNBQVM7Z0JBQ1AsR0FBSXJCLGlCQUFpQjtvQkFBRVksTUFBTVo7Z0JBQWMsQ0FBQztnQkFDNUMsR0FBSUMsb0JBQW9CO29CQUFFcUIsU0FBUyxJQUFxQixPQUFqQnJCO2dCQUFtQixDQUFDO2dCQUMzRCxHQUFJQyxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVxQixLQUFLLEtBQUk7b0JBQUVBLEtBQUssRUFBRXJCLHFCQUFBQSwrQkFBQUEsU0FBVXFCLEtBQUs7Z0JBQUMsQ0FBQztZQUNuRDtZQUNBQyxPQUFPO2dCQUNMQyxTQUFTL0Msa0VBQWFBLENBQUN5QjtZQUN6QjtZQUNBdUIsT0FBTztnQkFDTEMsV0FBVztvQkFDVHJDO29CQUNBLE1BQU1PO2dCQUNSO1lBQ0Y7UUFDRjtRQUNBLE1BQU0rQixXQUFXLE9BQWdCRSxRQUFRLENBQUN6QjtRQUMxQyxPQUFPdUIsU0FBU0csSUFBSTtJQUN0QixHQUFHO1FBQUNyQztRQUFXQztLQUFrQjtJQUVqQ25CLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDa0IsYUFBYSxDQUFDQyxtQkFBbUI7WUFDbkM7Z0JBQ0MsTUFBTVM7WUFDUjtRQUNGO0lBQ0YsR0FBRztRQUFDVjtRQUFXQztLQUFrQjtJQUVqQyxJQUFJRCxhQUFhQyxtQkFBbUI7UUFDbEMscUJBQU8sOERBQUNYLDhFQUFPQTtZQUFDZ0QsVUFBVTs7Ozs7O0lBQzVCO0lBRUEsT0FBTztBQUNUO0dBckVNL0M7O1FBS1VOLHdEQUFjQTtRQUNMQyw4RUFBY0E7UUFDYUgseURBQVdBO1FBQ1ZJLDREQUFXQTtRQUN4QkMsc0RBQVFBO1FBR2ZDLDZEQUFlQTs7O0tBWjFDRTtBQXVFTiwrREFBZUEsb0JBQW9CQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3BheW1lbnQvcmF6b3JwYXkvcmF6b3JwYXktcGF5bWVudC1tb2RhbC50c3g/YWJjZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgdXNlUmF6b3JwYXksIHsgUmF6b3JwYXlPcHRpb25zIH0gZnJvbSAnQC9saWIvdXNlLXJhem9ycGF5JztcclxuaW1wb3J0IHsgZm9ybWF0QWRkcmVzcyB9IGZyb20gJ0AvbGliL2Zvcm1hdC1hZGRyZXNzJztcclxuaW1wb3J0IHsgUGF5bWVudEdhdGV3YXksIFBheW1lbnRJbnRlbnRJbmZvIH0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcclxuaW1wb3J0IHsgdXNlTW9kYWxBY3Rpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbW9kYWwvbW9kYWwuY29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVNldHRpbmdzIH0gZnJvbSAnQC9mcmFtZXdvcmsvc2V0dGluZ3MnO1xyXG5pbXBvcnQgeyB1c2VPcmRlciwgdXNlT3JkZXJQYXltZW50IH0gZnJvbSAnQC9mcmFtZXdvcmsvb3JkZXInO1xyXG5pbXBvcnQgU3Bpbm5lciBmcm9tICdAL2NvbXBvbmVudHMvdWkvbG9hZGVycy9zcGlubmVyL3NwaW5uZXInO1xyXG5cclxuaW50ZXJmYWNlIFByb3BzIHtcclxuICBwYXltZW50SW50ZW50SW5mbzogUGF5bWVudEludGVudEluZm87XHJcbiAgdHJhY2tpbmdOdW1iZXI6IHN0cmluZztcclxuICBwYXltZW50R2F0ZXdheTogUGF5bWVudEdhdGV3YXk7XHJcbn1cclxuXHJcbmNvbnN0IFJhem9ycGF5UGF5bWVudE1vZGFsOiBSZWFjdC5GQzxQcm9wcz4gPSAoe1xyXG4gIHRyYWNraW5nTnVtYmVyLFxyXG4gIHBheW1lbnRJbnRlbnRJbmZvLFxyXG4gIHBheW1lbnRHYXRld2F5LFxyXG59KSA9PiB7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IHsgY2xvc2VNb2RhbCB9ID0gdXNlTW9kYWxBY3Rpb24oKTtcclxuICBjb25zdCB7IGxvYWRSYXpvcnBheVNjcmlwdCwgY2hlY2tTY3JpcHRMb2FkZWQgfSA9IHVzZVJhem9ycGF5KCk7XHJcbiAgY29uc3QgeyBzZXR0aW5ncywgaXNMb2FkaW5nOiBpc1NldHRpbmdzTG9hZGluZyB9ID0gdXNlU2V0dGluZ3MoKTtcclxuICBjb25zdCB7IG9yZGVyLCBpc0xvYWRpbmcsIHJlZmV0Y2ggfSA9IHVzZU9yZGVyKHtcclxuICAgIHRyYWNraW5nX251bWJlcjogdHJhY2tpbmdOdW1iZXIsXHJcbiAgfSk7XHJcbiAgY29uc3QgeyBjcmVhdGVPcmRlclBheW1lbnQgfSA9IHVzZU9yZGVyUGF5bWVudCgpO1xyXG5cclxuICAvLyBAdHMtaWdub3JlXHJcbiAgY29uc3QgeyBjdXN0b21lcl9uYW1lLCBjdXN0b21lcl9jb250YWN0LCBjdXN0b21lciwgYmlsbGluZ19hZGRyZXNzIH0gPVxyXG4gICAgb3JkZXIgPz8ge307XHJcblxyXG4gIGNvbnN0IHBheW1lbnRIYW5kbGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIWNoZWNrU2NyaXB0TG9hZGVkKCkpIHtcclxuICAgICAgYXdhaXQgbG9hZFJhem9ycGF5U2NyaXB0KCk7XHJcbiAgICB9XHJcbiAgICBjb25zdCBvcHRpb25zOiBSYXpvcnBheU9wdGlvbnMgPSB7XHJcbiAgICAgIGtleTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUkFaT1JQQVlfS0VZX0lELFxyXG4gICAgICBhbW91bnQ6IHBheW1lbnRJbnRlbnRJbmZvPy5hbW91bnQhLFxyXG4gICAgICBjdXJyZW5jeTogcGF5bWVudEludGVudEluZm8/LmN1cnJlbmN5ISxcclxuICAgICAgbmFtZTogY3VzdG9tZXJfbmFtZSEsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBgJHt0KCd0ZXh0LW9yZGVyJyl9IyR7dHJhY2tpbmdOdW1iZXJ9YCxcclxuICAgICAgaW1hZ2U6IHNldHRpbmdzPy5sb2dvPy5vcmlnaW5hbCEsXHJcbiAgICAgIG9yZGVyX2lkOiBwYXltZW50SW50ZW50SW5mbz8ucGF5bWVudF9pZCEsXHJcbiAgICAgIGhhbmRsZXI6IGFzeW5jICgpID0+IHtcclxuICAgICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICAgICAgY3JlYXRlT3JkZXJQYXltZW50KHtcclxuICAgICAgICAgIHRyYWNraW5nX251bWJlcjogdHJhY2tpbmdOdW1iZXIhLFxyXG4gICAgICAgICAgcGF5bWVudF9nYXRld2F5OiAncmF6b3JwYXknIGFzIHN0cmluZyxcclxuICAgICAgICB9KTtcclxuICAgICAgfSxcclxuICAgICAgcHJlZmlsbDoge1xyXG4gICAgICAgIC4uLihjdXN0b21lcl9uYW1lICYmIHsgbmFtZTogY3VzdG9tZXJfbmFtZSB9KSxcclxuICAgICAgICAuLi4oY3VzdG9tZXJfY29udGFjdCAmJiB7IGNvbnRhY3Q6IGArJHtjdXN0b21lcl9jb250YWN0fWAgfSksXHJcbiAgICAgICAgLi4uKGN1c3RvbWVyPy5lbWFpbCAmJiB7IGVtYWlsOiBjdXN0b21lcj8uZW1haWwgfSksXHJcbiAgICAgIH0sXHJcbiAgICAgIG5vdGVzOiB7XHJcbiAgICAgICAgYWRkcmVzczogZm9ybWF0QWRkcmVzcyhiaWxsaW5nX2FkZHJlc3MgYXMgYW55KSxcclxuICAgICAgfSxcclxuICAgICAgbW9kYWw6IHtcclxuICAgICAgICBvbmRpc21pc3M6IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIGNsb3NlTW9kYWwoKTtcclxuICAgICAgICAgIGF3YWl0IHJlZmV0Y2goKTtcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfTtcclxuICAgIGNvbnN0IHJhem9ycGF5ID0gKHdpbmRvdyBhcyBhbnkpLlJhem9ycGF5KG9wdGlvbnMpO1xyXG4gICAgcmV0dXJuIHJhem9ycGF5Lm9wZW4oKTtcclxuICB9LCBbaXNMb2FkaW5nLCBpc1NldHRpbmdzTG9hZGluZ10pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFpc0xvYWRpbmcgJiYgIWlzU2V0dGluZ3NMb2FkaW5nKSB7XHJcbiAgICAgIChhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgYXdhaXQgcGF5bWVudEhhbmRsZSgpO1xyXG4gICAgICB9KSgpO1xyXG4gICAgfVxyXG4gIH0sIFtpc0xvYWRpbmcsIGlzU2V0dGluZ3NMb2FkaW5nXSk7XHJcblxyXG4gIGlmIChpc0xvYWRpbmcgfHwgaXNTZXR0aW5nc0xvYWRpbmcpIHtcclxuICAgIHJldHVybiA8U3Bpbm5lciBzaG93VGV4dD17ZmFsc2V9IC8+O1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIG51bGw7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBSYXpvcnBheVBheW1lbnRNb2RhbDtcclxuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwidXNlUmF6b3JwYXkiLCJmb3JtYXRBZGRyZXNzIiwidXNlVHJhbnNsYXRpb24iLCJ1c2VNb2RhbEFjdGlvbiIsInVzZVNldHRpbmdzIiwidXNlT3JkZXIiLCJ1c2VPcmRlclBheW1lbnQiLCJTcGlubmVyIiwiUmF6b3JwYXlQYXltZW50TW9kYWwiLCJ0cmFja2luZ051bWJlciIsInBheW1lbnRJbnRlbnRJbmZvIiwicGF5bWVudEdhdGV3YXkiLCJ0IiwiY2xvc2VNb2RhbCIsImxvYWRSYXpvcnBheVNjcmlwdCIsImNoZWNrU2NyaXB0TG9hZGVkIiwic2V0dGluZ3MiLCJpc0xvYWRpbmciLCJpc1NldHRpbmdzTG9hZGluZyIsIm9yZGVyIiwicmVmZXRjaCIsInRyYWNraW5nX251bWJlciIsImNyZWF0ZU9yZGVyUGF5bWVudCIsImN1c3RvbWVyX25hbWUiLCJjdXN0b21lcl9jb250YWN0IiwiY3VzdG9tZXIiLCJiaWxsaW5nX2FkZHJlc3MiLCJwYXltZW50SGFuZGxlIiwib3B0aW9ucyIsImtleSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19SQVpPUlBBWV9LRVlfSUQiLCJhbW91bnQiLCJjdXJyZW5jeSIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImltYWdlIiwibG9nbyIsIm9yaWdpbmFsIiwib3JkZXJfaWQiLCJwYXltZW50X2lkIiwiaGFuZGxlciIsInBheW1lbnRfZ2F0ZXdheSIsInByZWZpbGwiLCJjb250YWN0IiwiZW1haWwiLCJub3RlcyIsImFkZHJlc3MiLCJtb2RhbCIsIm9uZGlzbWlzcyIsInJhem9ycGF5Iiwid2luZG93IiwiUmF6b3JwYXkiLCJvcGVuIiwic2hvd1RleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/payment/razorpay/razorpay-payment-modal.tsx\n"));

/***/ }),

/***/ "./src/lib/format-address.ts":
/*!***********************************!*\
  !*** ./src/lib/format-address.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAddress: function() { return /* binding */ formatAddress; }\n/* harmony export */ });\nfunction removeFalsy(obj) {\n    return Object.fromEntries(Object.entries(obj).filter((param)=>{\n        let [_, v] = param;\n        return Boolean(v);\n    }));\n}\nfunction formatAddress(address) {\n    if (!address) return;\n    const temp = [\n        \"street_address\",\n        \"state\",\n        \"city\",\n        \"zip\",\n        \"country\"\n    ].reduce((acc, k)=>({\n            ...acc,\n            [k]: address[k]\n        }), {});\n    const formattedAddress = removeFalsy(temp);\n    return Object.values(formattedAddress).join(\", \");\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxTQUFTQSxZQUFZQyxHQUFRO0lBQzNCLE9BQU9DLE9BQU9DLFdBQVcsQ0FBQ0QsT0FBT0UsT0FBTyxDQUFDSCxLQUFLSSxNQUFNLENBQUM7WUFBQyxDQUFDQyxHQUFHQyxFQUFFO2VBQUtDLFFBQVFEO0lBQUM7QUFDNUU7QUFFTyxTQUFTRSxjQUFjQyxPQUFvQjtJQUNoRCxJQUFJLENBQUNBLFNBQVM7SUFDZCxNQUFNQyxPQUFPO1FBQUM7UUFBa0I7UUFBUztRQUFRO1FBQU87S0FBVSxDQUFDQyxNQUFNLENBQ3ZFLENBQUNDLEtBQUtDLElBQU87WUFBRSxHQUFHRCxHQUFHO1lBQUUsQ0FBQ0MsRUFBRSxFQUFFLE9BQWdCLENBQUNBLEVBQUU7UUFBQyxJQUNoRCxDQUFDO0lBRUgsTUFBTUMsbUJBQW1CZixZQUFZVztJQUNyQyxPQUFPVCxPQUFPYyxNQUFNLENBQUNELGtCQUFrQkUsSUFBSSxDQUFDO0FBQzlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvZm9ybWF0LWFkZHJlc3MudHM/Y2IyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBVc2VyQWRkcmVzcyB9IGZyb20gJ0AvdHlwZXMnO1xyXG5mdW5jdGlvbiByZW1vdmVGYWxzeShvYmo6IGFueSkge1xyXG4gIHJldHVybiBPYmplY3QuZnJvbUVudHJpZXMoT2JqZWN0LmVudHJpZXMob2JqKS5maWx0ZXIoKFtfLCB2XSkgPT4gQm9vbGVhbih2KSkpO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0QWRkcmVzcyhhZGRyZXNzOiBVc2VyQWRkcmVzcykge1xyXG4gIGlmICghYWRkcmVzcykgcmV0dXJuO1xyXG4gIGNvbnN0IHRlbXAgPSBbJ3N0cmVldF9hZGRyZXNzJywgJ3N0YXRlJywgJ2NpdHknLCAnemlwJywgJ2NvdW50cnknXS5yZWR1Y2UoXHJcbiAgICAoYWNjLCBrKSA9PiAoeyAuLi5hY2MsIFtrXTogKGFkZHJlc3MgYXMgYW55KVtrXSB9KSxcclxuICAgIHt9XHJcbiAgKTtcclxuICBjb25zdCBmb3JtYXR0ZWRBZGRyZXNzID0gcmVtb3ZlRmFsc3kodGVtcCk7XHJcbiAgcmV0dXJuIE9iamVjdC52YWx1ZXMoZm9ybWF0dGVkQWRkcmVzcykuam9pbignLCAnKTtcclxufVxyXG4iXSwibmFtZXMiOlsicmVtb3ZlRmFsc3kiLCJvYmoiLCJPYmplY3QiLCJmcm9tRW50cmllcyIsImVudHJpZXMiLCJmaWx0ZXIiLCJfIiwidiIsIkJvb2xlYW4iLCJmb3JtYXRBZGRyZXNzIiwiYWRkcmVzcyIsInRlbXAiLCJyZWR1Y2UiLCJhY2MiLCJrIiwiZm9ybWF0dGVkQWRkcmVzcyIsInZhbHVlcyIsImpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/format-address.ts\n"));

/***/ }),

/***/ "./src/lib/use-razorpay.ts":
/*!*********************************!*\
  !*** ./src/lib/use-razorpay.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useRazorpay = ()=>{\n    /* Constants */ const RAZORPAY_SCRIPT = \"https://checkout.razorpay.com/v1/checkout.js\";\n    const isClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>\"object\" !== \"undefined\", []);\n    const checkScriptLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient || !(\"Razorpay\" in window)) return false;\n        return true;\n    }, []);\n    const loadRazorpayScript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient) return; // Don't execute this function if it's rendering on server side\n        return new Promise((resolve, reject)=>{\n            const scriptTag = document.createElement(\"script\");\n            scriptTag.src = RAZORPAY_SCRIPT;\n            scriptTag.onload = (ev)=>resolve(ev);\n            scriptTag.onerror = (err)=>reject(err);\n            document.body.appendChild(scriptTag);\n        });\n    }, []);\n    return {\n        checkScriptLoaded,\n        loadRazorpayScript\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useRazorpay);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-razorpay.ts\n"));

/***/ })

}]);