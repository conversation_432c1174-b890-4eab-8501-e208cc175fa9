"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_ui_auto-suggestion_tsx";
exports.ids = ["src_components_ui_auto-suggestion_tsx"];
exports.modules = {

/***/ "__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Transition: () => (/* reexport safe */ C_GithubProjects_LOLgorithm_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__.Transition)
/* harmony export */ });
/* harmony import */ var C_GithubProjects_LOLgorithm_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ "./node_modules/@headlessui/react/dist/components/transitions/transition.js");



/***/ }),

/***/ "./src/components/ui/auto-suggestion.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/auto-suggestion.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scrollbar */ \"./src/components/ui/scrollbar.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__]);\n_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\nconst AutoSuggestion = ({ className, suggestions, visible, notFound, showLoaders, seeMore, seeMoreLink })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleClick = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__.Transition, {\n        show: visible,\n        enter: \"transition-opacity duration-75\",\n        enterFrom: \"opacity-0\",\n        enterTo: \"opacity-100\",\n        leave: \"transition-opacity duration-150\",\n        leaveFrom: \"opacity-100\",\n        leaveTo: \"opacity-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(\"absolute top-11 left-0 mt-2 w-full lg:top-16 lg:mt-1\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full w-full rounded-lg bg-white py-2 shadow-downfall-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-full w-full\",\n                        children: [\n                            notFound && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"flex h-full w-full items-center justify-center py-10 font-semibold text-gray-400\",\n                                children: t(\"text-no-products\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined),\n                            showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-full w-full items-center justify-center py-14\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    simple: true,\n                                    className: \"h-9 w-9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined),\n                            !notFound && !showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-52\",\n                                children: suggestions?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>handleClick(_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product(item?.slug)),\n                                        className: \"flex w-full cursor-pointer items-center border-b border-border-100 px-5 py-2 transition-colors last:border-b-0 hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-8 w-8 overflow-hidden rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                                    className: \"h-full w-full\",\n                                                    src: item?.image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__.productPlaceholder,\n                                                    alt: item?.name ?? \"\",\n                                                    width: 100,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-heading ltr:ml-3 rtl:mr-3\",\n                                                children: item?.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, item?.slug, true, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    seeMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full py-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: seeMoreLink,\n                            className: \"text-sm font-semibold text-accent transition-colors hover:text-accent-hover\",\n                            children: t(\"text-see-more\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoSuggestion);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/auto-suggestion.tsx\n");

/***/ })

};
;