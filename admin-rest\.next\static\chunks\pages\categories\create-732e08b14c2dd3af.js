(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7164,2036],{92996:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/create",function(){return t(58307)}])},58307:function(e,r,t){"use strict";t.r(r),t.d(r,{__N_SSG:function(){return i},default:function(){return CreateCategoriesPage}});var n=t(85893),a=t(97670),o=t(65343),s=t(5233),i=!0;function CreateCategoriesPage(){let{t:e}=(0,s.$G)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:form-title-create-category")})}),(0,n.jsx)(o.Z,{})]})}CreateCategoriesPage.Layout=a.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,9494,5535,8186,1285,1631,5468,1737,5054,9774,2888,179],function(){return e(e.s=92996)}),_N_E=e.O()}]);