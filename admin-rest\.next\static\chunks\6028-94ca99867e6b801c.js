"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6028,9297],{28368:function(e,t,n){n.d(t,{p:function(){return O}});var r,l,o,a=n(67294),s=n(32984),i=n(12351),c=n(23784),p=n(19946),d=n(61363),f=n(64103),v=n(16567),m=n(14157),P=n(15466),b=n(73781);let y=null!=(o=a.startTransition)?o:function(e){e()};var E=((r=E||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),S=((l=S||{})[l.ToggleDisclosure=0]="ToggleDisclosure",l[l.CloseDisclosure=1]="CloseDisclosure",l[l.SetButtonId=2]="SetButtonId",l[l.SetPanelId=3]="SetPanelId",l[l.LinkPanel=4]="LinkPanel",l[l.UnlinkPanel=5]="UnlinkPanel",l);let g={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},h=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(h);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}h.displayName="DisclosureContext";let T=(0,a.createContext)(null);T.displayName="DisclosureAPIContext";let I=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,g,e,t)}I.displayName="DisclosurePanelContext";let k=a.Fragment,C=i.AN.RenderStrategy|i.AN.Static,O=Object.assign((0,i.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,l=(0,a.useRef)(null),o=(0,c.T)(t,(0,c.h)(e=>{l.current=e},void 0===e.as||e.as===a.Fragment)),p=(0,a.useRef)(null),d=(0,a.useRef)(null),f=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:p,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:y},E]=f,S=(0,b.z)(e=>{E({type:1});let t=(0,P.r)(l);if(!t||!y)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(y):t.getElementById(y);null==n||n.focus()}),g=(0,a.useMemo)(()=>({close:S}),[S]),I=(0,a.useMemo)(()=>({open:0===m,close:S}),[m,S]);return a.createElement(h.Provider,{value:f},a.createElement(T.Provider,{value:g},a.createElement(v.up,{value:(0,s.E)(m,{0:v.ZM.Open,1:v.ZM.Closed})},(0,i.sY)({ourProps:{ref:o},theirProps:r,slot:I,defaultTag:k,name:"Disclosure"}))))}),{Button:(0,i.yV)(function(e,t){let n=(0,p.M)(),{id:r=`headlessui-disclosure-button-${n}`,...l}=e,[o,s]=M("Disclosure.Button"),v=(0,a.useContext)(I),P=null!==v&&v===o.panelId,y=(0,a.useRef)(null),E=(0,c.T)(y,t,P?null:o.buttonRef);(0,a.useEffect)(()=>{if(!P)return s({type:2,buttonId:r}),()=>{s({type:2,buttonId:null})}},[r,s,P]);let S=(0,b.z)(e=>{var t;if(P){if(1===o.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),g=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),h=(0,b.z)(t=>{var n;(0,f.P)(t.currentTarget)||e.disabled||(P?(s({type:0}),null==(n=o.buttonRef.current)||n.focus()):s({type:0}))}),T=(0,a.useMemo)(()=>({open:0===o.disclosureState}),[o]),k=(0,m.f)(e,y),C=P?{ref:E,type:k,onKeyDown:S,onClick:h}:{ref:E,id:r,type:k,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:S,onKeyUp:g,onClick:h};return(0,i.sY)({ourProps:C,theirProps:l,slot:T,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,i.yV)(function(e,t){let n=(0,p.M)(),{id:r=`headlessui-disclosure-panel-${n}`,...l}=e,[o,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(T);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),f=(0,c.T)(t,o.panelRef,e=>{y(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:r}),()=>{s({type:3,panelId:null})}),[r,s]);let m=(0,v.oJ)(),P=null!==m?(m&v.ZM.Open)===v.ZM.Open:0===o.disclosureState,b=(0,a.useMemo)(()=>({open:0===o.disclosureState,close:d}),[o,d]);return a.createElement(I.Provider,{value:o.panelId},(0,i.sY)({ourProps:{ref:f,id:r},theirProps:l,slot:b,defaultTag:"div",features:C,visible:P,name:"Disclosure.Panel"}))})})},95389:function(e,t,n){n.d(t,{_:function(){return p},b:function(){return H}});var r=n(67294),l=n(19946),o=n(12351),a=n(16723),s=n(23784),i=n(73781);let c=(0,r.createContext)(null);function H(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=(0,i.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),l=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return r.createElement(c.Provider,{value:l},e.children)},[t])]}let p=Object.assign((0,o.yV)(function(e,t){let n=(0,l.M)(),{id:i=`headlessui-label-${n}`,passive:p=!1,...d}=e,f=function u(){let e=(0,r.useContext)(c);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),v=(0,s.T)(t);(0,a.e)(()=>f.register(i),[i,f.register]);let m={ref:v,...f.props,id:i};return p&&("onClick"in m&&(delete m.htmlFor,delete m.onClick),"onClick"in d&&delete d.onClick),(0,o.sY)({ourProps:m,theirProps:d,slot:f.slot||{},defaultTag:"label",name:f.name||"Label"})}),{})},86215:function(e,t,n){n.d(t,{J:function(){return _}});var r,l,o=n(67294),a=n(32984),s=n(12351),i=n(23784),c=n(19946),p=n(61363),d=n(64103),f=n(84575),v=n(16567),m=n(14157),P=n(39650),b=n(15466),y=n(51074),E=n(14007),S=n(46045),g=n(73781),h=n(45662),T=n(3855),I=n(16723),k=n(65958),C=n(2740),O=((r=O||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),R=((l=R||{})[l.TogglePopover=0]="TogglePopover",l[l.ClosePopover=1]="ClosePopover",l[l.SetButton=2]="SetButton",l[l.SetButtonId=3]="SetButtonId",l[l.SetPanel=4]="SetPanel",l[l.SetPanelId=5]="SetPanelId",l);let D={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},x=(0,o.createContext)(null);function oe(e){let t=(0,o.useContext)(x);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}x.displayName="PopoverContext";let N=(0,o.createContext)(null);function fe(e){let t=(0,o.useContext)(N);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}N.displayName="PopoverAPIContext";let B=(0,o.createContext)(null);function Ee(){return(0,o.useContext)(B)}B.displayName="PopoverGroupContext";let F=(0,o.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,D,e,t)}F.displayName="PopoverPanelContext";let z=s.AN.RenderStrategy|s.AN.Static,A=s.AN.RenderStrategy|s.AN.Static,_=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:r=!1,...l}=e,c=(0,o.useRef)(null),p=(0,i.T)(t,(0,i.h)(e=>{c.current=e})),d=(0,o.useRef)([]),m=(0,o.useReducer)(Ne,{__demoMode:r,popoverState:r?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,o.createRef)(),afterPanelSentinel:(0,o.createRef)()}),[{popoverState:b,button:S,buttonId:h,panel:I,panelId:O,beforePanelSentinel:R,afterPanelSentinel:D},B]=m,z=(0,y.i)(null!=(n=c.current)?n:S),A=(0,o.useMemo)(()=>{if(!S||!I)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(S))^Number(null==e?void 0:e.contains(I)))return!0;let e=(0,f.GO)(),t=e.indexOf(S),n=(t+e.length-1)%e.length,r=(t+1)%e.length,l=e[n],o=e[r];return!I.contains(l)&&!I.contains(o)},[S,I]),_=(0,T.E)(h),L=(0,T.E)(O),G=(0,o.useMemo)(()=>({buttonId:_,panelId:L,close:()=>B({type:1})}),[_,L,B]),$=Ee(),j=null==$?void 0:$.registerPopover,V=(0,g.z)(()=>{var e;return null!=(e=null==$?void 0:$.isFocusWithinPopoverGroup())?e:(null==z?void 0:z.activeElement)&&((null==S?void 0:S.contains(z.activeElement))||(null==I?void 0:I.contains(z.activeElement)))});(0,o.useEffect)(()=>null==j?void 0:j(G),[j,G]);let[Z,K]=(0,C.k)(),J=(0,k.v)({mainTreeNodeRef:null==$?void 0:$.mainTreeNodeRef,portals:Z,defaultContainers:[S,I]});(0,E.O)(null==z?void 0:z.defaultView,"focus",e=>{var t,n,r,l;e.target!==window&&e.target instanceof HTMLElement&&0===b&&(V()||S&&I&&(J.contains(e.target)||null!=(n=null==(t=R.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(l=null==(r=D.current)?void 0:r.contains)&&l.call(r,e.target)||B({type:1})))},!0),(0,P.O)(J.resolveContainers,(e,t)=>{B({type:1}),(0,f.sP)(t,f.tJ.Loose)||(e.preventDefault(),null==S||S.focus())},0===b);let U=(0,g.z)(e=>{B({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:S:S;null==t||t.focus()}),q=(0,o.useMemo)(()=>({close:U,isPortalled:A}),[U,A]),W=(0,o.useMemo)(()=>({open:0===b,close:U}),[b,U]);return o.createElement(F.Provider,{value:null},o.createElement(x.Provider,{value:m},o.createElement(N.Provider,{value:q},o.createElement(v.up,{value:(0,a.E)(b,{0:v.ZM.Open,1:v.ZM.Closed})},o.createElement(K,null,(0,s.sY)({ourProps:{ref:p},theirProps:l,slot:W,defaultTag:"div",name:"Popover"}),o.createElement(J.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-popover-button-${n}`,...l}=e,[v,P]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),E=(0,o.useRef)(null),T=`headlessui-focus-sentinel-${(0,c.M)()}`,I=Ee(),k=null==I?void 0:I.closeOthers,C=null!==(0,o.useContext)(F);(0,o.useEffect)(()=>{if(!C)return P({type:3,buttonId:r}),()=>{P({type:3,buttonId:null})}},[C,r,P]);let[O]=(0,o.useState)(()=>Symbol()),R=(0,i.T)(E,t,C?null:e=>{if(e)v.buttons.current.push(O);else{let e=v.buttons.current.indexOf(O);-1!==e&&v.buttons.current.splice(e,1)}v.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&P({type:2,button:e})}),D=(0,i.T)(E,t),x=(0,y.i)(E),N=(0,g.z)(e=>{var t,n,r;if(C){if(1===v.popoverState)return;switch(e.key){case p.R.Space:case p.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),P({type:1}),null==(r=v.button)||r.focus()}}else switch(e.key){case p.R.Space:case p.R.Enter:e.preventDefault(),e.stopPropagation(),1===v.popoverState&&(null==k||k(v.buttonId)),P({type:0});break;case p.R.Escape:if(0!==v.popoverState)return null==k?void 0:k(v.buttonId);if(!E.current||null!=x&&x.activeElement&&!E.current.contains(x.activeElement))return;e.preventDefault(),e.stopPropagation(),P({type:1})}}),B=(0,g.z)(e=>{C||e.key===p.R.Space&&e.preventDefault()}),z=(0,g.z)(t=>{var n,r;(0,d.P)(t.currentTarget)||e.disabled||(C?(P({type:1}),null==(n=v.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===v.popoverState&&(null==k||k(v.buttonId)),P({type:0}),null==(r=v.button)||r.focus()))}),A=(0,g.z)(e=>{e.preventDefault(),e.stopPropagation()}),_=0===v.popoverState,L=(0,o.useMemo)(()=>({open:_}),[_]),G=(0,m.f)(e,E),$=C?{ref:D,type:G,onKeyDown:N,onClick:z}:{ref:R,id:v.buttonId,type:G,"aria-expanded":0===v.popoverState,"aria-controls":v.panel?v.panelId:void 0,onKeyDown:N,onKeyUp:B,onClick:z,onMouseDown:A},j=(0,h.l)(),V=(0,g.z)(()=>{let e=v.panel;e&&(0,a.E)(j.current,{[h.N.Forwards]:()=>(0,f.jA)(e,f.TO.First),[h.N.Backwards]:()=>(0,f.jA)(e,f.TO.Last)})===f.fE.Error&&(0,f.jA)((0,f.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(j.current,{[h.N.Forwards]:f.TO.Next,[h.N.Backwards]:f.TO.Previous}),{relativeTo:v.button})});return o.createElement(o.Fragment,null,(0,s.sY)({ourProps:$,theirProps:l,slot:L,defaultTag:"button",name:"Popover.Button"}),_&&!C&&b&&o.createElement(S._,{id:T,features:S.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:V}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-popover-overlay-${n}`,...l}=e,[{popoverState:a},p]=oe("Popover.Overlay"),f=(0,i.T)(t),m=(0,v.oJ)(),P=null!==m?(m&v.ZM.Open)===v.ZM.Open:0===a,b=(0,g.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();p({type:1})}),y=(0,o.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:f,id:r,"aria-hidden":!0,onClick:b},theirProps:l,slot:y,defaultTag:"div",features:z,visible:P,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-popover-panel-${n}`,focus:l=!1,...d}=e,[m,P]=oe("Popover.Panel"),{close:b,isPortalled:E}=fe("Popover.Panel"),T=`headlessui-focus-sentinel-before-${(0,c.M)()}`,k=`headlessui-focus-sentinel-after-${(0,c.M)()}`,C=(0,o.useRef)(null),O=(0,i.T)(C,t,e=>{P({type:4,panel:e})}),R=(0,y.i)(C);(0,I.e)(()=>(P({type:5,panelId:r}),()=>{P({type:5,panelId:null})}),[r,P]);let D=(0,v.oJ)(),x=null!==D?(D&v.ZM.Open)===v.ZM.Open:0===m.popoverState,N=(0,g.z)(e=>{var t;if(e.key===p.R.Escape){if(0!==m.popoverState||!C.current||null!=R&&R.activeElement&&!C.current.contains(R.activeElement))return;e.preventDefault(),e.stopPropagation(),P({type:1}),null==(t=m.button)||t.focus()}});(0,o.useEffect)(()=>{var t;e.static||1===m.popoverState&&(null==(t=e.unmount)||t)&&P({type:4,panel:null})},[m.popoverState,e.unmount,e.static,P]),(0,o.useEffect)(()=>{if(m.__demoMode||!l||0!==m.popoverState||!C.current)return;let e=null==R?void 0:R.activeElement;C.current.contains(e)||(0,f.jA)(C.current,f.TO.First)},[m.__demoMode,l,C,m.popoverState]);let B=(0,o.useMemo)(()=>({open:0===m.popoverState,close:b}),[m,b]),z={ref:O,id:r,onKeyDown:N,onBlur:l&&0===m.popoverState?e=>{var t,n,r,l,o;let a=e.relatedTarget;a&&C.current&&(null!=(t=C.current)&&t.contains(a)||(P({type:1}),(null!=(r=null==(n=m.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,a)||null!=(o=null==(l=m.afterPanelSentinel.current)?void 0:l.contains)&&o.call(l,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},_=(0,h.l)(),L=(0,g.z)(()=>{let e=C.current;e&&(0,a.E)(_.current,{[h.N.Forwards]:()=>{var t;(0,f.jA)(e,f.TO.First)===f.fE.Error&&(null==(t=m.afterPanelSentinel.current)||t.focus())},[h.N.Backwards]:()=>{var e;null==(e=m.button)||e.focus({preventScroll:!0})}})}),G=(0,g.z)(()=>{let e=C.current;e&&(0,a.E)(_.current,{[h.N.Forwards]:()=>{var e;if(!m.button)return;let t=(0,f.GO)(),n=t.indexOf(m.button),r=t.slice(0,n+1),l=[...t.slice(n+1),...r];for(let t of l.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=m.panel)&&e.contains(t)){let e=l.indexOf(t);-1!==e&&l.splice(e,1)}(0,f.jA)(l,f.TO.First,{sorted:!1})},[h.N.Backwards]:()=>{var t;(0,f.jA)(e,f.TO.Previous)===f.fE.Error&&(null==(t=m.button)||t.focus())}})});return o.createElement(F.Provider,{value:r},x&&E&&o.createElement(S._,{id:T,ref:m.beforePanelSentinel,features:S.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:L}),(0,s.sY)({ourProps:z,theirProps:d,slot:B,defaultTag:"div",features:A,visible:x,name:"Popover.Panel"}),x&&E&&o.createElement(S._,{id:k,ref:m.afterPanelSentinel,features:S.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:G}))}),Group:(0,s.yV)(function(e,t){let n=(0,o.useRef)(null),r=(0,i.T)(n,t),[l,a]=(0,o.useState)([]),c=(0,k.H)(),p=(0,g.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,g.z)(e=>(a(t=>[...t,e]),()=>p(e))),f=(0,g.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||l.some(e=>{var n,l;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(l=t.getElementById(e.panelId.current))?void 0:l.contains(r))})}),v=(0,g.z)(e=>{for(let t of l)t.buttonId.current!==e&&t.close()}),m=(0,o.useMemo)(()=>({registerPopover:d,unregisterPopover:p,isFocusWithinPopoverGroup:f,closeOthers:v,mainTreeNodeRef:c.mainTreeNodeRef}),[d,p,f,v,c.mainTreeNodeRef]),P=(0,o.useMemo)(()=>({}),[]);return o.createElement(B.Provider,{value:m},(0,s.sY)({ourProps:{ref:r},theirProps:e,slot:P,defaultTag:"div",name:"Popover.Group"}),o.createElement(c.MainTreeNode,null))})})},77768:function(e,t,n){n.d(t,{r:function(){return S}});var r=n(67294),l=n(12351),o=n(19946),a=n(61363),s=n(64103),i=n(95389),c=n(39516),p=n(14157),d=n(23784),f=n(46045),v=n(18689),m=n(73781),P=n(31147),b=n(94192);let y=(0,r.createContext)(null);y.displayName="GroupContext";let E=r.Fragment,S=Object.assign((0,l.yV)(function(e,t){let n=(0,o.M)(),{id:i=`headlessui-switch-${n}`,checked:c,defaultChecked:E=!1,onChange:S,name:g,value:h,form:T,...I}=e,k=(0,r.useContext)(y),C=(0,r.useRef)(null),O=(0,d.T)(C,t,null===k?null:k.setSwitch),[R,D]=(0,P.q)(c,S,E),x=(0,m.z)(()=>null==D?void 0:D(!R)),N=(0,m.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),x()}),B=(0,m.z)(e=>{e.key===a.R.Space?(e.preventDefault(),x()):e.key===a.R.Enter&&(0,v.g)(e.currentTarget)}),F=(0,m.z)(e=>e.preventDefault()),z=(0,r.useMemo)(()=>({checked:R}),[R]),A={id:i,ref:O,role:"switch",type:(0,p.f)(e,C),tabIndex:0,"aria-checked":R,"aria-labelledby":null==k?void 0:k.labelledby,"aria-describedby":null==k?void 0:k.describedby,onClick:N,onKeyUp:B,onKeyPress:F},_=(0,b.G)();return(0,r.useEffect)(()=>{var e;let t=null==(e=C.current)?void 0:e.closest("form");t&&void 0!==E&&_.addEventListener(t,"reset",()=>{D(E)})},[C,D]),r.createElement(r.Fragment,null,null!=g&&R&&r.createElement(f._,{features:f.A.Hidden,...(0,l.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:T,checked:R,name:g,value:h})}),(0,l.sY)({ourProps:A,theirProps:I,slot:z,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[n,o]=(0,r.useState)(null),[a,s]=(0,i.b)(),[p,d]=(0,c.f)(),f=(0,r.useMemo)(()=>({switch:n,setSwitch:o,labelledby:a,describedby:p}),[n,o,a,p]);return r.createElement(d,{name:"Switch.Description"},r.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(t=f.switch)?void 0:t.id,onClick(e){n&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},r.createElement(y.Provider,{value:f},(0,l.sY)({ourProps:{},theirProps:e,defaultTag:E,name:"Switch.Group"}))))},Label:i._,Description:c.d})}}]);