"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_category_category-delete-view_tsx";
exports.ids = ["src_components_category_category-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/category/category-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/category/category-delete-view.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_category__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/category */ \"./src/data/category.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_category__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_category__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst CategoryDeleteView = ()=>{\n    const { mutate: deleteCategory, isLoading: loading } = (0,_data_category__WEBPACK_IMPORTED_MODULE_3__.useDeleteCategoryMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteCategory({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\category\\\\category-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/category/category-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/category.ts":
/*!******************************!*\
  !*** ./src/data/category.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCategoriesQuery: () => (/* binding */ useCategoriesQuery),\n/* harmony export */   useCategoryQuery: () => (/* binding */ useCategoryQuery),\n/* harmony export */   useCreateCategoryMutation: () => (/* binding */ useCreateCategoryMutation),\n/* harmony export */   useDeleteCategoryMutation: () => (/* binding */ useDeleteCategoryMutation),\n/* harmony export */   useUpdateCategoryMutation: () => (/* binding */ useUpdateCategoryMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_category__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./client/category */ \"./src/data/client/category.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _client_category__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__, _client_category__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateCategoryMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_category__WEBPACK_IMPORTED_MODULE_7__.categoryClient.create, {\n        onSuccess: ()=>{\n            next_router__WEBPACK_IMPORTED_MODULE_0___default().push(_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.category.list, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.CATEGORIES);\n        }\n    });\n};\nconst useDeleteCategoryMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_category__WEBPACK_IMPORTED_MODULE_7__.categoryClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.CATEGORIES);\n        }\n    });\n};\nconst useUpdateCategoryMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_client_category__WEBPACK_IMPORTED_MODULE_7__.categoryClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.category.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.category.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.CATEGORIES);\n        }\n    });\n};\nconst useCategoryQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.CATEGORIES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_client_category__WEBPACK_IMPORTED_MODULE_7__.categoryClient.get({\n            slug,\n            language\n        }));\n    return {\n        category: data,\n        error,\n        isLoading\n    };\n};\nconst useCategoriesQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.CATEGORIES,\n        options\n    ], ({ queryKey, pageParam })=>_client_category__WEBPACK_IMPORTED_MODULE_7__.categoryClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        categories: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/category.ts\n");

/***/ }),

/***/ "./src/data/client/category.ts":
/*!*************************************!*\
  !*** ./src/data/client/category.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoryClient: () => (/* binding */ categoryClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst categoryClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CATEGORIES),\n    paginated: ({ type, name, self, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CATEGORIES, {\n            searchJoin: \"and\",\n            self,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvY2F0ZWdvcnkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQU9nRDtBQUNIO0FBQ0Y7QUFFcEMsTUFBTUcsaUJBQWlCO0lBQzVCLEdBQUdGLDBEQUFXQSxDQUNaRCx5REFBYUEsQ0FBQ0ksVUFBVSxDQUN6QjtJQUNEQyxXQUFXLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQUVDLElBQUksRUFBRSxHQUFHQyxRQUF1QztRQUN4RSxPQUFPUCxvREFBVUEsQ0FBQ1EsR0FBRyxDQUFvQlYseURBQWFBLENBQUNJLFVBQVUsRUFBRTtZQUNqRU8sWUFBWTtZQUNaSDtZQUNBLEdBQUdDLE1BQU07WUFDVEcsUUFBUVYsb0RBQVVBLENBQUNXLGtCQUFrQixDQUFDO2dCQUFFUDtnQkFBTUM7WUFBSztRQUNyRDtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9kYXRhL2NsaWVudC9jYXRlZ29yeS50cz9mMWFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XHJcbiAgQ2F0ZWdvcnksXHJcbiAgQ2F0ZWdvcnlQYWdpbmF0b3IsXHJcbiAgQ2F0ZWdvcnlRdWVyeU9wdGlvbnMsXHJcbiAgQ3JlYXRlQ2F0ZWdvcnlJbnB1dCxcclxuICBRdWVyeU9wdGlvbnMsXHJcbn0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCB7IEFQSV9FTkRQT0lOVFMgfSBmcm9tICcuL2FwaS1lbmRwb2ludHMnO1xyXG5pbXBvcnQgeyBjcnVkRmFjdG9yeSB9IGZyb20gJy4vY3VyZC1mYWN0b3J5JztcclxuaW1wb3J0IHsgSHR0cENsaWVudCB9IGZyb20gJy4vaHR0cC1jbGllbnQnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGNhdGVnb3J5Q2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PENhdGVnb3J5LCBRdWVyeU9wdGlvbnMsIENyZWF0ZUNhdGVnb3J5SW5wdXQ+KFxyXG4gICAgQVBJX0VORFBPSU5UUy5DQVRFR09SSUVTXHJcbiAgKSxcclxuICBwYWdpbmF0ZWQ6ICh7IHR5cGUsIG5hbWUsIHNlbGYsIC4uLnBhcmFtcyB9OiBQYXJ0aWFsPENhdGVnb3J5UXVlcnlPcHRpb25zPikgPT4ge1xyXG4gICAgcmV0dXJuIEh0dHBDbGllbnQuZ2V0PENhdGVnb3J5UGFnaW5hdG9yPihBUElfRU5EUE9JTlRTLkNBVEVHT1JJRVMsIHtcclxuICAgICAgc2VhcmNoSm9pbjogJ2FuZCcsXHJcbiAgICAgIHNlbGYsXHJcbiAgICAgIC4uLnBhcmFtcyxcclxuICAgICAgc2VhcmNoOiBIdHRwQ2xpZW50LmZvcm1hdFNlYXJjaFBhcmFtcyh7IHR5cGUsIG5hbWUgfSksXHJcbiAgICB9KTtcclxuICB9LFxyXG59O1xyXG4iXSwibmFtZXMiOlsiQVBJX0VORFBPSU5UUyIsImNydWRGYWN0b3J5IiwiSHR0cENsaWVudCIsImNhdGVnb3J5Q2xpZW50IiwiQ0FURUdPUklFUyIsInBhZ2luYXRlZCIsInR5cGUiLCJuYW1lIiwic2VsZiIsInBhcmFtcyIsImdldCIsInNlYXJjaEpvaW4iLCJzZWFyY2giLCJmb3JtYXRTZWFyY2hQYXJhbXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/data/client/category.ts\n");

/***/ })

};
;