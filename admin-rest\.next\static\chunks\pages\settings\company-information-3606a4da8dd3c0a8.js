(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3098,2007,2036],{46384:function(e,t,o){var r=o(38407),n=o(37465),l=o(63779),i=o(67599),a=o(44758),s=o(34309);function Stack(e){var t=this.__data__=new r(e);this.size=t.size}Stack.prototype.clear=n,Stack.prototype.delete=l,Stack.prototype.get=i,Stack.prototype.has=a,Stack.prototype.set=s,e.exports=Stack},11149:function(e,t,o){var r=o(55639).Uint8Array;e.exports=r},34963:function(e){e.exports=function(e,t){for(var o=-1,r=null==e?0:e.length,n=0,l=[];++o<r;){var i=e[o];t(i,o,e)&&(l[n++]=i)}return l}},14636:function(e,t,o){var r=o(22545),n=o(35694),l=o(1469),i=o(44144),a=o(65776),s=o(36719),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var o=l(e),d=!o&&n(e),u=!o&&!d&&i(e),f=!o&&!d&&!u&&s(e),p=o||d||u||f,m=p?r(e.length,String):[],x=m.length;for(var v in e)(t||c.call(e,v))&&!(p&&("length"==v||u&&("offset"==v||"parent"==v)||f&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||a(v,x)))&&m.push(v);return m}},68866:function(e,t,o){var r=o(62488),n=o(1469);e.exports=function(e,t,o){var l=t(e);return n(e)?l:r(l,o(e))}},22545:function(e){e.exports=function(e,t){for(var o=-1,r=Array(e);++o<e;)r[o]=t(o);return r}},58234:function(e,t,o){var r=o(68866),n=o(99551),l=o(3674);e.exports=function(e){return r(e,l,n)}},99551:function(e,t,o){var r=o(34963),n=o(70479),l=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(e){return null==e?[]:r(i(e=Object(e)),function(t){return l.call(e,t)})}:n;e.exports=a},37465:function(e,t,o){var r=o(38407);e.exports=function(){this.__data__=new r,this.size=0}},63779:function(e){e.exports=function(e){var t=this.__data__,o=t.delete(e);return this.size=t.size,o}},67599:function(e){e.exports=function(e){return this.__data__.get(e)}},44758:function(e){e.exports=function(e){return this.__data__.has(e)}},34309:function(e,t,o){var r=o(38407),n=o(57071),l=o(83369);e.exports=function(e,t){var o=this.__data__;if(o instanceof r){var i=o.__data__;if(!n||i.length<199)return i.push([e,t]),this.size=++o.size,this;o=this.__data__=new l(i)}return o.set(e,t),this.size=o.size,this}},3674:function(e,t,o){var r=o(14636),n=o(280),l=o(98612);e.exports=function(e){return l(e)?r(e):n(e)}},70479:function(e){e.exports=function(){return[]}},95009:function(e,t,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/company-information",function(){return o(87284)}])},92072:function(e,t,o){"use strict";var r=o(85893),n=o(93967),l=o.n(n),i=o(98388);t.Z=e=>{let{className:t,...o}=e;return(0,r.jsx)("div",{className:(0,i.m6)(l()("rounded bg-light p-5 shadow md:p-8",t)),...o})}},13718:function(e,t,o){"use strict";o.d(t,{Z:function(){return GooglePlacesAutocomplete}});var r=o(85893),n=o(37054),l=o(67294),i=o(5233),a=o(55846),s=o(8366),c=o(23322);function GooglePlacesAutocomplete(e){let{onChange:t,onChangeCurrentLocation:o,data:d,disabled:u=!1,icon:f=!1}=e,{t:p}=(0,i.$G)(),[m,x]=(0,l.useState)(""),[v,b,h,g,y,w]=(0,c.ZP)({onChange:t,onChangeCurrentLocation:o,setInputValue:x});return((0,l.useEffect)(()=>{let e=null==d?void 0:d.formattedAddress;x(e)},[d]),w)?(0,r.jsx)("div",{children:p("common:text-map-cant-load")}):y?(0,r.jsxs)("div",{className:"relative",children:[f&&(0,r.jsx)("div",{className:"absolute top-0 left-0 flex h-12 w-10 items-center justify-center text-gray-400",children:(0,r.jsx)(s.$t,{className:"w-[18px]"})}),(0,r.jsx)(n.F2,{onLoad:v,onPlaceChanged:h,onUnmount:b,fields:["address_components","geometry.location","formatted_address"],types:["address"],children:(0,r.jsx)("input",{type:"text",placeholder:p("form:placeholder-search-location"),value:m,onChange:e=>x(e.target.value),className:"flex h-12 w-full appearance-none items-center rounded border border-border-base text-sm text-heading transition duration-300 ease-in-out  focus:border-accent focus:outline-none focus:ring-0 ".concat(u?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":""," ").concat(f?"pe-4 ps-9":"px-4"),disabled:u})})]}):(0,r.jsx)(a.Z,{simple:!0,className:"h-6 w-6"})}},62003:function(e,t,o){"use strict";o.d(t,{s:function(){return ChevronLeft}});var r=o(85893);let ChevronLeft=e=>(0,r.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},86779:function(e,t,o){"use strict";o.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var r=o(85893);let InfoIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,r.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},8366:function(e,t,o){"use strict";o.d(t,{$t:function(){return MapPin},f_:function(){return MapPinIconWithPlatform},tE:function(){return MapPinIcon}});var r=o(85893);let MapPin=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",...t,children:(0,r.jsx)("path",{d:"M256 0C161.896 0 85.333 76.563 85.333 170.667c0 28.25 7.063 56.26 20.49 81.104L246.667 506.5c1.875 3.396 5.448 5.5 9.333 5.5s7.458-2.104 9.333-5.5l140.896-254.813c13.375-24.76 20.438-52.771 20.438-81.021C426.667 76.563 350.104 0 256 0zm0 256c-47.052 0-85.333-38.281-85.333-85.333S208.948 85.334 256 85.334s85.333 38.281 85.333 85.333S303.052 256 256 256z",fill:"currentColor"})})},MapPinIcon=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("path",{opacity:.2,d:"M8 1.5a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M8 4a2.5 2.5 0 100 5 2.5 2.5 0 000-5zm0 4a1.5 1.5 0 110-3 1.5 1.5 0 010 3zm0-7a5.506 5.506 0 00-5.5 5.5c0 1.963.907 4.043 2.625 6.016.772.891 1.64 1.694 2.59 2.393a.5.5 0 00.574 0c.948-.7 1.816-1.502 2.586-2.393C12.591 10.543 13.5 8.463 13.5 6.5A5.506 5.506 0 008 1zm0 12.875c-1.033-.813-4.5-3.797-4.5-7.375a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375z",fill:"currentColor"})]}),MapPinIconWithPlatform=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("path",{opacity:.2,d:"M8 2a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M12.5 14.5H9.409c.519-.464 1.009-.96 1.466-1.484C12.591 11.043 13.5 8.963 13.5 7a5.5 5.5 0 00-11 0c0 1.963.907 4.043 2.625 6.016.457.525.947 1.02 1.466 1.484H3.5a.5.5 0 000 1h9a.5.5 0 000-1zM3.5 7a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375C6.967 13.562 3.5 10.577 3.5 7zm7 0a2.5 2.5 0 10-5 0 2.5 2.5 0 005 0zm-4 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z",fill:"currentColor"})]})},12032:function(e,t,o){"use strict";o.d(t,{N:function(){return SaveIcon}});var r=o(85893);let SaveIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,r.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},25774:function(e,t,o){"use strict";o.r(t),o.d(t,{FacebookIcon:function(){return FacebookIcon},InstagramIcon:function(){return InstagramIcon},TwitterIcon:function(){return TwitterIcon},YouTubeIcon:function(){return YouTubeIcon}});var r=o(85893);let FacebookIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 12",...e,children:(0,r.jsx)("path",{"data-name":"_ionicons_svg_logo-facebook (6)",d:"M11.338 0H.662A.663.663 0 000 .663v10.674a.663.663 0 00.662.662H6V7.25H4.566V5.5H6V4.206a2.28 2.28 0 012.459-2.394c.662 0 1.375.05 1.541.072V3.5H8.9c-.753 0-.9.356-.9.881V5.5h1.794L9.56 7.25H8V12h3.338a.663.663 0 00.662-.663V.662A.663.663 0 0011.338 0z",fill:"currentColor"})}),InstagramIcon=e=>(0,r.jsxs)("svg",{"data-name":"Group 96",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 12",...e,children:[(0,r.jsx)("path",{"data-name":"Path 1",d:"M8.5 1A2.507 2.507 0 0111 3.5v5A2.507 2.507 0 018.5 11h-5A2.507 2.507 0 011 8.5v-5A2.507 2.507 0 013.5 1h5m0-1h-5A3.51 3.51 0 000 3.5v5A3.51 3.51 0 003.5 12h5A3.51 3.51 0 0012 8.5v-5A3.51 3.51 0 008.5 0z",fill:"currentColor"}),(0,r.jsx)("path",{"data-name":"Path 2",d:"M9.25 3.5a.75.75 0 11.75-.75.748.748 0 01-.75.75zM6 4a2 2 0 11-2 2 2 2 0 012-2m0-1a3 3 0 103 3 3 3 0 00-3-3z",fill:"currentColor"})]}),TwitterIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14.747 12",...e,children:(0,r.jsx)("path",{"data-name":"_ionicons_svg_logo-twitter (5)",d:"M14.747 1.422a6.117 6.117 0 01-1.737.478A3.036 3.036 0 0014.341.225a6.012 6.012 0 01-1.922.734 3.025 3.025 0 00-5.234 2.069 2.962 2.962 0 00.078.691A8.574 8.574 0 011.026.553a3.032 3.032 0 00.941 4.044 2.955 2.955 0 01-1.375-.378v.037A3.028 3.028 0 003.02 7.225a3.046 3.046 0 01-.8.106 2.854 2.854 0 01-.569-.056 3.03 3.03 0 002.828 2.1 6.066 6.066 0 01-3.759 1.3 6.135 6.135 0 01-.722-.044A8.457 8.457 0 004.631 12a8.557 8.557 0 008.616-8.619c0-.131 0-.262-.009-.391a6.159 6.159 0 001.509-1.568z",fill:"currentColor"})}),YouTubeIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15.997 12",...e,children:(0,r.jsx)("path",{d:"M15.893 2.65A2.429 2.429 0 0013.581.113c-1.731-.081-3.5-.112-5.3-.112h-.563c-1.8 0-3.569.031-5.3.112A2.434 2.434 0 00.106 2.656C.028 3.768-.006 4.881-.003 5.993s.031 2.225.106 3.34a2.437 2.437 0 002.309 2.547c1.822.085 3.688.12 5.584.12s3.759-.031 5.581-.119a2.438 2.438 0 002.312-2.547c.075-1.116.109-2.228.106-3.344s-.027-2.225-.102-3.34zM6.468 9.059v-6.14l4.531 3.069z",fill:"currentColor"})})},97670:function(e,t,o){"use strict";o.r(t);var r=o(85893),n=o(78985),l=o(79362),i=o(8144),a=o(74673),s=o(99494),c=o(5233),d=o(1631),u=o(11163),f=o(48583),p=o(93967),m=o.n(p),x=o(30824),v=o(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:o}=(0,c.$G)(),[n,i]=(0,f.KO)(l.Hf),{childMenu:a}=t,{width:s}=(0,v.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:t,label:i,icon:a,childMenu:c}=e;return(0,r.jsx)(d.Z,{href:t,label:o(i),icon:a,childMenu:c,miniSidebar:n&&s>=l.h2},i)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[o,n]=(0,f.KO)(l.Hf),i=null===s.siteSettings||void 0===s.siteSettings?void 0:null===(e=s.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(i),{width:d}=(0,v.Z)();return(0,r.jsx)(r.Fragment,{children:null==a?void 0:a.map((e,n)=>{var a;return(0,r.jsxs)("div",{className:m()("flex flex-col px-5",o&&d>=l.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:m()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",o&&d>=l.h2?"hidden":""),children:t(null===(a=i[e])||void 0===a?void 0:a.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:i[e]})]},n)})})};t.default=e=>{let{children:t}=e,{locale:o}=(0,u.useRouter)(),[s,c]=(0,f.KO)(l.Hf),[d]=(0,f.KO)(l.GH),[p]=(0,f.KO)(l.W4),{width:b}=(0,v.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===o||"he"===o?"rtl":"ltr",children:[(0,r.jsx)(n.Z,{}),(0,r.jsx)(a.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:m()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",b>=l.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-20",s&&b>=l.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:m()("relative flex w-full flex-col justify-start transition-[padding] duration-300",b>=l.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",s&&b>=l.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(i.Z,{})]})]})]})}},59122:function(e,t,o){"use strict";o.d(t,{Z:function(){return SettingsPageHeader}});var r=o(85893),n=o(11163),l=o(99494),i=o(8152),a=o(93967),s=o.n(a),c=o(5233),d=o(67294),u=o(86998),f=o(62003);function SettingsPageHeader(e){var t,o,a,p;let{pageTitle:m}=e,{t:x}=(0,c.$G)(),v=(0,n.useRouter)(),{sliderEl:b,sliderPrevBtn:h,sliderNextBtn:g,scrollToTheRight:y,scrollToTheLeft:w}=function(){let e=(0,d.useRef)(null),t=(0,d.useRef)(null),o=(0,d.useRef)(null);return(0,d.useEffect)(()=>{let r=e.current,n=t.current,l=o.current,i=r.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=r.offsetWidth;r.scrollWidth>e?(null==l||l.classList.remove("opacity-0","invisible"),i&&(null==r||r.classList.add("!-mb-[43px]"))):(null==l||l.classList.add("opacity-0","invisible"),i&&(null==r||r.classList.remove("!-mb-[43px]"))),null==n||n.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==r?void 0:r.scrollLeft,t=null==r?void 0:r.offsetWidth;(null==r?void 0:r.scrollWidth)-e==t?(null==l||l.classList.add("opacity-0","invisible"),null==n||n.classList.remove("opacity-0","invisible")):null==l||l.classList.remove("opacity-0","invisible"),0===e?(null==n||n.classList.add("opacity-0","invisible"),null==l||l.classList.remove("opacity-0","invisible")):null==n||n.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),r.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),r.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:o,scrollToTheRight:function(){let o=e.current.offsetWidth;e.current.scrollLeft+=o/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,o.current.classList.remove("opacity-0","invisible")}}}(),j=null===l.siteSettings||void 0===l.siteSettings?void 0:null===(p=l.siteSettings.sidebarLinks)||void 0===p?void 0:null===(a=p.admin)||void 0===a?void 0:null===(o=a.settings)||void 0===o?void 0:null===(t=o.childMenu[0])||void 0===t?void 0:t.childMenu,_=v.asPath.split("#")[0].split("?")[0];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:x(m)})}),(0,r.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,r.jsx)("button",{title:"Prev",ref:h,onClick:()=>w(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(f.s,{className:"h-[18px] w-[18px]"})}),(0,r.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,r.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:b,children:null==j?void 0:j.map((e,t)=>(0,r.jsx)(i.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:s()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",_===e.href?"text-heading before:w-full":null),children:x(e.label)},t))})}),(0,r.jsx)("button",{title:"Next",ref:g,onClick:()=>y(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(u._,{className:"h-[18px] w-[18px]"})})]})]})}},80602:function(e,t,o){"use strict";var r=o(85893);t.Z=e=>{let{title:t,details:o,className:n,...l}=e;return(0,r.jsxs)("div",{className:n,...l,children:[t&&(0,r.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),o&&(0,r.jsx)("p",{className:"text-sm text-body",children:o})]})}},33e3:function(e,t,o){"use strict";var r=o(85893),n=o(71611),l=o(93967),i=o.n(l),a=o(67294),s=o(98388);let c={small:"text-sm h-10",medium:"h-12",big:"h-14"},d=a.forwardRef((e,t)=>{let{className:o,label:l,note:a,name:d,error:u,children:f,variant:p="normal",dimension:m="medium",shadow:x=!1,type:v="text",inputClassName:b,disabled:h,showLabel:g=!0,required:y,toolTipText:w,labelClassName:j,..._}=e,N=i()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===p,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===p,"border border-border-base focus:border-accent":"outline"===p},{"focus:shadow":x},c[m],b),C="number"===v&&h?"number-disable":"";return(0,r.jsxs)("div",{className:(0,s.m6)(o),children:[g||l?(0,r.jsx)(n.Z,{htmlFor:d,toolTipText:w,label:l,required:y,className:j}):"",(0,r.jsx)("input",{id:d,name:d,type:v,ref:t,className:(0,s.m6)(i()(h?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(C," select-none"):"",N)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:h,"aria-invalid":u?"true":"false",..._}),a&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:a}),u&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:u})]})});d.displayName="Input",t.Z=d},23091:function(e,t,o){"use strict";var r=o(85893),n=o(93967),l=o.n(n),i=o(98388);t.Z=e=>{let{className:t,...o}=e;return(0,r.jsx)("label",{className:(0,i.m6)(l()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...o})}},92732:function(e,t,o){"use strict";var r=o(85893),n=o(87536),l=o(93967),i=o.n(l);o(67294);var a=o(67555),s=o.n(a);o(48705);var c=o(98388),d=o(71611);t.Z=e=>{let{label:t,required:o,showLabel:l=!0,error:a,className:u,inputClassName:f,toolTipText:p,disabled:m,note:x,name:v,control:b,...h}=e;return(0,r.jsxs)("div",{className:(0,c.m6)(i()("mb-5",u)),children:[(0,r.jsx)(n.Qr,{render:e=>{let{field:{onChange:n,value:u}}=e;return(0,r.jsxs)(r.Fragment,{children:[l?(0,r.jsx)(d.Z,{htmlFor:v,toolTipText:p,label:t,required:o}):"",(0,r.jsx)(s(),{value:u,onChange:n,inputClass:(0,c.m6)(i()("!p-0 !pe-4 !ps-14 !flex !items-center !w-full !appearance-none !transition !duration-300 !ease-in-out !text-heading !text-sm focus:!outline-none focus:!ring-0 !border !border-border-base !rounded focus:!border-accent !h-12",m?"cursor-not-allowed !border-[#D4D8DD] !bg-[#EEF1F4] select-none":"",f)),dropdownClass:"focus:!ring-0 !border !border-border-base !shadow-350","aria-invalid":a?"true":"false",disabled:m})]})},id:v,name:v,control:b,...h}),x&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:x}),a&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:a})]})}},28454:function(e,t,o){"use strict";var r=o(85893),n=o(79828),l=o(71611),i=o(87536);t.Z=e=>{let{control:t,options:o,name:a,rules:s,getOptionLabel:c,getOptionValue:d,disabled:u,isMulti:f,isClearable:p,isLoading:m,placeholder:x,label:v,required:b,toolTipText:h,error:g,...y}=e;return(0,r.jsxs)(r.Fragment,{children:[v?(0,r.jsx)(l.Z,{htmlFor:a,toolTipText:h,label:v,required:b}):"",(0,r.jsx)(i.Qr,{control:t,name:a,rules:s,...y,render:e=>{let{field:t}=e;return(0,r.jsx)(n.Z,{...t,getOptionLabel:c,getOptionValue:d,placeholder:x,isMulti:f,isClearable:p,isLoading:m,options:o,isDisabled:u})}}),g&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:g})]})}},87077:function(e,t,o){"use strict";o.d(t,{W:function(){return n},X:function(){return r}});let r={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:t.isSelected?"#E5E7EB":t.isFocused?"#F9FAFB":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,t)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:t.isSelected?"#EEF1F4":t.isFocused?"#EEF1F4":"#ffffff"}),control:(e,t)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==t?void 0:t.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==t?void 0:t.isDisabled)?"#D4D8DD":t.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:t.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,t)=>({...e,color:t.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,t)=>({...e,paddingLeft:16}),singleValue:(e,t)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,t)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,t)=>({...e,paddingLeft:t.isRtl?0:12,paddingRight:t.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,t)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,t)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,t,o){"use strict";var r=o(85893),n=o(76518),l=o(67294),i=o(23157),a=o(87077);let s=l.forwardRef((e,t)=>{let{isRTL:o}=(0,n.S)();return(0,r.jsx)(i.ZP,{ref:t,styles:a.X,isRtl:o,...e})});s.displayName="Select",t.Z=s},22220:function(e,t,o){"use strict";var r=o(85893),n=o(93967),l=o.n(n),i=o(98388);t.Z=e=>{let{children:t,className:o,...n}=e;return(0,r.jsx)("div",{className:(0,i.m6)(l()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",o)),...n,children:t})}},95414:function(e,t,o){"use strict";var r=o(85893),n=o(71611),l=o(93967),i=o.n(l),a=o(67294),s=o(98388);let c=a.forwardRef((e,t)=>{let{className:o,label:l,toolTipText:a,name:c,error:d,variant:u="normal",shadow:f=!1,inputClassName:p,disabled:m,required:x,...v}=e,b=i()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===u,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===u,"border border-border-base focus:border-accent":"outline"===u},{"focus:shadow":f},p);return(0,r.jsxs)("div",{className:(0,s.m6)(i()(o)),children:[l&&(0,r.jsx)(n.Z,{htmlFor:c,toolTipText:a,label:l,required:x}),(0,r.jsx)("textarea",{id:c,name:c,className:(0,s.m6)(i()(b,m?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:m,...v}),d&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:d})]})});c.displayName="TextArea",t.Z=c},71611:function(e,t,o){"use strict";var r=o(85893),n=o(86779),l=o(71943),i=o(23091),a=o(98388);t.Z=e=>{let{className:t,required:o,label:s,toolTipText:c,htmlFor:d}=e;return(0,r.jsxs)(i.Z,{className:(0,a.m6)(t),htmlFor:d,children:[s,o?(0,r.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",c?(0,r.jsx)(l.u,{content:c,children:(0,r.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,r.jsx)(n.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,o){"use strict";o.d(t,{u:function(){return Tooltip}});var r=o(85893),n=o(67294),l=o(93075),i=o(82364),a=o(24750),s=o(93967),c=o.n(s),d=o(67421),u=o(98388);let f={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:o,gap:s=8,animation:m="zoomIn",placement:x="top",size:v="md",rounded:b="DEFAULT",shadow:h="md",color:g="default",className:y,arrowClassName:w,showArrow:j=!0}=e,[_,N]=(0,n.useState)(!1),C=(0,n.useRef)(null),{t:D}=(0,d.$G)(),{x:Z,y:S,refs:k,strategy:z,context:T}=(0,l.YF)({placement:x,open:_,onOpenChange:N,middleware:[(0,i.x7)({element:C}),(0,i.cv)(s),(0,i.RR)(),(0,i.uY)({padding:8})],whileElementsMounted:a.Me}),{getReferenceProps:F,getFloatingProps:O}=(0,l.NI)([(0,l.XI)(T),(0,l.KK)(T),(0,l.qs)(T,{role:"tooltip"}),(0,l.bQ)(T)]),{isMounted:L,styles:E}=(0,l.Y_)(T,{duration:{open:150,close:150},...p[m]});return(0,r.jsxs)(r.Fragment,{children:[(0,n.cloneElement)(t,F({ref:k.setReference,...t.props})),(L||_)&&(0,r.jsx)(l.ll,{children:(0,r.jsxs)("div",{role:"tooltip",ref:k.setFloating,className:(0,u.m6)(c()(f.base,f.size[v],f.rounded[b],f.variant.solid.base,f.variant.solid.color[g],f.shadow[h],y)),style:{position:z,top:null!=S?S:0,left:null!=Z?Z:0,...E},...O(),children:[D("".concat(o)),j&&(0,r.jsx)(l.Y$,{ref:C,context:T,className:c()(f.arrow.color[g],w),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},87284:function(e,t,o){"use strict";o.r(t),o.d(t,{__N_SSG:function(){return A},default:function(){return CompanyInformation}});var r=o(85893),n=o(97670),l=o(59122),i=o(45957),a=o(55846),s=o(90573),c=o(16203),d=o(5233),u=o(11163),f=o(92072),p=o(13718),m=o(12032),x=o(25774),v=o(16310),b=o(79362);let h=v.Ry().shape({contactDetails:v.Ry().shape({contact:v.Z_().required("form:error-phone-number-required").matches(b.Oj,"form:error-phone-number-valid-required"),website:v.Z_().required("form:error-website-required").matches(b.F7,"form:error-url-valid-required"),emailAddress:v.Z_().email("form:error-email-format").required("form:error-email-required"),socials:v.IX().of(v.Ry().shape({url:v.Z_().when("icon",e=>e?v.Z_().url("form:error-invalid-url").required("form:error-url-required"):v.Z_().nullable())}))})});var g=o(60802),y=o(80602),w=o(33e3),j=o(23091),_=o(92732),N=o(28454),C=o(22220),D=o(95414),Z=o(99494),S=o(3986),k=o(35841),z=o(47559),T=o(47533),F=o(57557),O=o.n(F),L=o(87536);let E=Z.e.map(e=>(e.label=(0,r.jsxs)("div",{className:"flex items-center text-body space-s-4",children:[(0,r.jsx)("span",{className:"flex items-center justify-center w-4 h-4",children:(0,z.q)({iconList:x,iconName:e.value,className:"w-4 h-4"})}),(0,r.jsx)("span",{children:e.label})]}),e));function CompanyInfoForm(e){var t,o,n,l,i,a,c,x,v,b,Z,z,F,A,I,P,R,B,M,V,H,G;let{settings:q}=e,{mutate:K,isLoading:W}=(0,s.B)(),{t:$}=(0,d.$G)(),{options:Y}=null!=q?q:{},X=null==Y?void 0:Y.useGoogleMap,{locale:U}=(0,u.useRouter)(),{register:Q,handleSubmit:J,control:ee,getValues:et,reset:eo,formState:{errors:er,dirtyFields:en}}=(0,L.cI)({shouldUnregister:!0,resolver:(0,T.X)(h),defaultValues:{...Y,contactDetails:{...null==Y?void 0:Y.contactDetails,socials:(null==Y?void 0:null===(t=Y.contactDetails)||void 0===t?void 0:t.socials)?null==Y?void 0:null===(o=Y.contactDetails)||void 0===o?void 0:o.socials.map(e=>({icon:null==E?void 0:E.find(t=>(null==t?void 0:t.value)===(null==e?void 0:e.icon)),url:null==e?void 0:e.url})):[]}}}),{fields:el,append:ei,remove:ea}=(0,L.Dq)({control:ee,name:"contactDetails.socials"});async function onSubmit(e){var t,o,r,n,l,i;let a={...null==e?void 0:e.contactDetails,location:X?{...O()(null==e?void 0:null===(t=e.contactDetails)||void 0===t?void 0:t.location,"__typename")}:{...null==e?void 0:null===(o=e.contactDetails)||void 0===o?void 0:o.location,formattedAddress:(0,k.T)(null==e?void 0:null===(r=e.contactDetails)||void 0===r?void 0:r.location)},socials:(null==e?void 0:null===(n=e.contactDetails)||void 0===n?void 0:n.socials)?null==e?void 0:null===(i=e.contactDetails)||void 0===i?void 0:null===(l=i.socials)||void 0===l?void 0:l.map(e=>{var t;return{icon:null==e?void 0:null===(t=e.icon)||void 0===t?void 0:t.value,url:null==e?void 0:e.url}}):[]};K({language:U,options:{...Y,...e,contactDetails:a}}),eo(e,{keepValues:!0})}let es=Object.keys(en).length>0;return(0,S.H)({isDirty:es}),(0,r.jsxs)("form",{onSubmit:J(onSubmit),children:[(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-gray-300 border-dashed sm:my-8 sm:mt-8 sm:mb-3",children:[(0,r.jsx)(y.Z,{title:$("form:footer-address"),details:$("form:footer-address-helper-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(f.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[X?(0,r.jsxs)("div",{className:"mb-5",children:[(0,r.jsx)(j.Z,{children:$("form:input-label-autocomplete")}),(0,r.jsx)(L.Qr,{control:ee,name:"contactDetails.location",render:e=>{let{field:{onChange:t}}=e;return(0,r.jsx)(p.Z,{onChange:t,data:et("contactDetails.location")})}})]}):(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-5",children:[(0,r.jsx)(w.Z,{label:$("text-city"),toolTipText:$("form:input-tooltip-company-city"),...Q("contactDetails.location.city"),error:$(null===(l=er.contactDetails)||void 0===l?void 0:null===(n=l.location)||void 0===n?void 0:n.city),variant:"outline"}),(0,r.jsx)(w.Z,{label:$("text-country"),toolTipText:$("form:input-tooltip-company-country"),...Q("contactDetails.location.country"),error:$(null===(a=er.contactDetails)||void 0===a?void 0:null===(i=a.location)||void 0===i?void 0:i.country),variant:"outline"}),(0,r.jsx)(w.Z,{label:$("text-state"),toolTipText:$("form:input-tooltip-company-state"),...Q("contactDetails.location.state"),error:$(null===(x=er.contactDetails)||void 0===x?void 0:null===(c=x.location)||void 0===c?void 0:c.state),variant:"outline"}),(0,r.jsx)(w.Z,{label:$("text-zip"),toolTipText:$("form:input-tooltip-company-zip"),...Q("contactDetails.location.zip"),error:$(null===(b=er.contactDetails)||void 0===b?void 0:null===(v=b.location)||void 0===v?void 0:v.zip),variant:"outline"}),(0,r.jsx)(D.Z,{label:$("text-street-address"),toolTipText:$("form:input-tooltip-company-street-address"),...Q("contactDetails.location.street_address"),error:$(null===(z=er.contactDetails)||void 0===z?void 0:null===(Z=z.location)||void 0===Z?void 0:Z.street_address),variant:"outline",className:"col-span-full"})]}),(0,r.jsx)(_.Z,{label:$("form:input-label-contact"),toolTipText:$("form:input-tooltip-company-contact-number"),...Q("contactDetails.contact"),control:ee,error:$(null===(A=er.contactDetails)||void 0===A?void 0:null===(F=A.contact)||void 0===F?void 0:F.message)}),(0,r.jsx)(w.Z,{label:$("form:input-label-website"),toolTipText:$("form:input-tooltip-company-website"),...Q("contactDetails.website"),variant:"outline",className:"mb-5",error:$(null===(P=er.contactDetails)||void 0===P?void 0:null===(I=P.website)||void 0===I?void 0:I.message)}),(0,r.jsx)(w.Z,{label:$("form:input-label-email"),toolTipText:$("form:input-tooltip-company-email"),...Q("contactDetails.emailAddress"),variant:"outline",className:"mb-5",error:$(null===(B=er.contactDetails)||void 0===B?void 0:null===(R=B.emailAddress)||void 0===R?void 0:R.message)}),(0,r.jsx)("div",{children:null==el?void 0:el.map((e,t)=>{var o,n,l,i;return(0,r.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 first:mt-5 first:border-t last:border-b-0 md:py-8 md:first:mt-10",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,r.jsx)("div",{className:"sm:col-span-2",children:(0,r.jsx)(N.Z,{name:"contactDetails.socials.".concat(t,".icon"),control:ee,options:E,isClearable:!0,defaultValue:null==e?void 0:e.icon,label:$("form:input-label-select-platform"),toolTipText:$("form:input-tooltip-company-social-platform")})}),(0,r.jsx)(w.Z,{className:"sm:col-span-2",label:$("form:input-label-social-url"),toolTipText:$("form:input-tooltip-company-profile-url"),variant:"outline",...Q("contactDetails.socials.".concat(t,".url")),defaultValue:e.url,error:$(null==er?void 0:null===(i=er.contactDetails)||void 0===i?void 0:null===(l=i.socials)||void 0===l?void 0:null===(n=l[t])||void 0===n?void 0:null===(o=n.url)||void 0===o?void 0:o.message)}),(0,r.jsx)("button",{onClick:()=>{ea(t)},type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none sm:col-span-1 sm:mt-4",children:$("form:button-label-remove")})]})},e.id)})}),(0,r.jsx)(g.Z,{type:"button",onClick:()=>ei({icon:"",url:""}),className:"w-full sm:w-auto",children:$("form:button-label-add-social")})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base",children:[(0,r.jsx)(y.Z,{title:$("form:form-title-footer-information"),details:$("form:site-info-footer-description"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(f.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(w.Z,{label:$("form:input-label-site-link"),toolTipText:$("form:input-tooltip-company-site-link"),...Q("siteLink"),error:$(null==er?void 0:null===(M=er.siteLink)||void 0===M?void 0:M.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(w.Z,{label:$("form:input-label-copyright-text"),toolTipText:$("form:input-tooltip-company-copyright-text"),...Q("copyrightText"),error:$(null==er?void 0:null===(V=er.copyrightText)||void 0===V?void 0:V.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(w.Z,{label:$("form:input-label-external-text"),toolTipText:$("form:input-tooltip-company-external-text"),...Q("externalText"),error:$(null==er?void 0:null===(H=er.externalText)||void 0===H?void 0:H.message),variant:"outline",className:"mb-5"}),(0,r.jsx)(w.Z,{label:$("form:input-label-external-link"),toolTipText:$("form:input-tooltip-company-external-link"),...Q("externalLink"),error:$(null==er?void 0:null===(G=er.externalLink)||void 0===G?void 0:G.message),variant:"outline",className:"mb-5"})]})]}),(0,r.jsx)(C.Z,{className:"z-0",children:(0,r.jsxs)(g.Z,{loading:W,disabled:W||!es,className:"text-sm md:text-base",children:[(0,r.jsx)(m.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),$("form:button-label-save-settings")]})})]})}var A=!0;function CompanyInformation(){let{t:e}=(0,d.$G)(),{locale:t}=(0,u.useRouter)(),{settings:o,loading:n,error:c}=(0,s.n)({language:t});return n?(0,r.jsx)(a.Z,{text:e("common:text-loading")}):c?(0,r.jsx)(i.Z,{message:c.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.Z,{pageTitle:"form:form-title-settings"}),(0,r.jsx)(CompanyInfoForm,{settings:o})]})}CompanyInformation.authenticate={permissions:c.M$},CompanyInformation.Layout=n.default},3986:function(e,t,o){"use strict";o.d(t,{H:function(){return useConfirmRedirectIfDirty}});var r=o(67294),n=o(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:o="You have unsaved changes - are you sure you wish to leave this page?"}=e,l=(0,n.useRouter)(),i=(0,r.useRef)(t),a=(0,r.useRef)(o);(0,r.useEffect)(()=>{i.current=t},[t]),(0,r.useEffect)(()=>{a.current=o},[o]);let s=(0,r.useCallback)(e=>{if(i.current)return e.preventDefault(),e.returnValue=a.current},[]),c=(0,r.useCallback)(()=>{if(i.current&&!window.confirm(a.current))throw l.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,r.useEffect)(()=>(window.addEventListener("beforeunload",s),l.events.on("routeChangeStart",c),()=>{window.removeEventListener("beforeunload",s),l.events.off("routeChangeStart",c)}),[s,c])}},35841:function(e,t,o){"use strict";function formatAddress(e){if(!e)return;let t=["street_address","city","state","zip","country"].reduce((t,o)=>({...t,[o]:e[o]}),{}),o=Object.fromEntries(Object.entries(t).filter(e=>{let[t,o]=e;return!!o}));return Object.values(o).join(", ")}o.d(t,{T:function(){return formatAddress}})},23322:function(e,t,o){"use strict";o.d(t,{ZP:function(){return useLocation},lH:function(){return a}});var r=o(37054),n=o(67294),l=o(67421),i=o(15103);let a=(0,i.cn)(null),s=["places"];function useLocation(e){let{onChange:t,onChangeCurrentLocation:o,setInputValue:i}=e,{t:a}=(0,l.$G)(),[c,d]=(0,n.useState)(null),{isLoaded:u,loadError:f}=(0,r.Ji)({id:"google_map_autocomplete",googleMapsApiKey:"",libraries:s}),p=(0,n.useCallback)(e=>{d(e)},[]),m=(0,n.useCallback)(()=>{d(!0)},[]);return[p,m,()=>{var e;let t=null==c?void 0:c.getPlace();null==t||null===(e=t.geometry)||void 0===e||e.location},()=>{var e,t;(null===(e=navigator)||void 0===e?void 0:e.geolocation)?null===(t=navigator)||void 0===t||t.geolocation.getCurrentPosition(async e=>{let{latitude:t,longitude:r}=e.coords,n=new google.maps.Geocoder;n.geocode({location:{lat:t,lng:r}},(e,t)=>{if("OK"===t&&(null==e?void 0:e[0])){let t=function(e){var t,o,r,n;let l={lat:null==e?void 0:null===(t=e.geometry)||void 0===t?void 0:t.location.lat(),lng:null==e?void 0:null===(o=e.geometry)||void 0===o?void 0:o.location.lng(),formattedAddress:e.formatted_address},i={postal_code:"zip",postal_code_suffix:"zip",state_name:"street_address",route:"street_address",sublocality_level_1:"street_address",locality:"city",administrative_area_level_1:"state",country:"country"};for(let t of null==e?void 0:e.address_components){let[e]=t.types,{long_name:o,short_name:a}=t;i[e]&&(null!==(n=l[r=i[e]])&&void 0!==n||(l[r]=o),"postal_code_suffix"===e&&(l.zip="".concat(null==l?void 0:l.zip,"-").concat(o)),"administrative_area_level_1"===e&&(l.state=a))}return l}(null==e?void 0:e[0]);null==o||o(t)}})},e=>{console.error("Error getting current location:",e)}):console.error("Geolocation is not supported by this browser.")},u,f&&a(f)]}(0,i.cn)(e=>{let t=e(a);return t?"".concat(t.street_address,", ").concat(t.city,", ").concat(t.state,", ").concat(t.zip,", ").concat(t.country):""})},23157:function(e,t,o){"use strict";o.d(t,{ZP:function(){return a}});var r=o(65342),n=o(87462),l=o(67294),i=o(76416);o(48711),o(73935),o(73469);var a=(0,l.forwardRef)(function(e,t){var o=(0,r.u)(e);return l.createElement(i.S,(0,n.Z)({ref:t},o))})},97326:function(e,t,o){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}o.d(t,{Z:function(){return _assertThisInitialized}})},15671:function(e,t,o){"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}o.d(t,{Z:function(){return _classCallCheck}})},43144:function(e,t,o){"use strict";o.d(t,{Z:function(){return _createClass}});var r=o(83997);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,r.Z)(n.key),n)}}function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,o){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}o.d(t,{Z:function(){return _createSuper}});var r=o(71002),n=o(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var o,l=_getPrototypeOf(e);if(t){var i=_getPrototypeOf(this).constructor;o=Reflect.construct(l,arguments,i)}else o=l.apply(this,arguments);return function(e,t){if(t&&("object"==(0,r.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,o)}}},60136:function(e,t,o){"use strict";o.d(t,{Z:function(){return _inherits}});var r=o(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.Z)(e,t)}},1413:function(e,t,o){"use strict";o.d(t,{Z:function(){return _objectSpread2}});var r=o(4942);function ownKeys(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,r)}return o}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(o),!0).forEach(function(t){(0,r.Z)(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}}},function(e){e.O(0,[6342,1255,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,7755,7557,8680,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=95009)}),_N_E=e.O()}]);