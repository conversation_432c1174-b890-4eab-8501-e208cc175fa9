"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_ui_auto-suggestion_tsx"],{

/***/ "__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \******************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Transition: function() { return /* reexport safe */ C_GithubProjects_LOLgorithm_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__.Transition; }
/* harmony export */ });
/* harmony import */ var C_GithubProjects_LOLgorithm_logorithm_e_site_shop_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ "./node_modules/@headlessui/react/dist/components/transitions/transition.js");



/***/ }),

/***/ "./src/components/ui/auto-suggestion.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/auto-suggestion.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scrollbar */ \"./src/components/ui/scrollbar.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AutoSuggestion = (param)=>{\n    let { className, suggestions, visible, notFound, showLoaders, seeMore, seeMoreLink } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleClick = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_9__.Transition, {\n        show: visible,\n        enter: \"transition-opacity duration-75\",\n        enterFrom: \"opacity-0\",\n        enterTo: \"opacity-100\",\n        leave: \"transition-opacity duration-150\",\n        leaveFrom: \"opacity-100\",\n        leaveTo: \"opacity-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_2___default()(\"absolute top-11 left-0 mt-2 w-full lg:top-16 lg:mt-1\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full w-full rounded-lg bg-white py-2 shadow-downfall-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scrollbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-full w-full\",\n                        children: [\n                            notFound && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"flex h-full w-full items-center justify-center py-10 font-semibold text-gray-400\",\n                                children: t(\"text-no-products\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined),\n                            showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-full w-full items-center justify-center py-14\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    simple: true,\n                                    className: \"h-9 w-9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined),\n                            !notFound && !showLoaders && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-52\",\n                                children: suggestions === null || suggestions === void 0 ? void 0 : suggestions.map((item)=>/*#__PURE__*/ {\n                                    var _item_image;\n                                    var _item_image_original, _item_name;\n                                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>handleClick(_config_routes__WEBPACK_IMPORTED_MODULE_7__.Routes.product(item === null || item === void 0 ? void 0 : item.slug)),\n                                        className: \"flex w-full cursor-pointer items-center border-b border-border-100 px-5 py-2 transition-colors last:border-b-0 hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-8 w-8 overflow-hidden rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                                    className: \"h-full w-full\",\n                                                    src: (_item_image_original = item === null || item === void 0 ? void 0 : (_item_image = item.image) === null || _item_image === void 0 ? void 0 : _item_image.original) !== null && _item_image_original !== void 0 ? _item_image_original : _lib_placeholders__WEBPACK_IMPORTED_MODULE_6__.productPlaceholder,\n                                                    alt: (_item_name = item === null || item === void 0 ? void 0 : item.name) !== null && _item_name !== void 0 ? _item_name : \"\",\n                                                    width: 100,\n                                                    height: 100\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-heading ltr:ml-3 rtl:mr-3\",\n                                                children: item === null || item === void 0 ? void 0 : item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, item === null || item === void 0 ? void 0 : item.slug, true, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined),\n                    seeMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full py-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: seeMoreLink,\n                            className: \"text-sm font-semibold text-accent transition-colors hover:text-accent-hover\",\n                            children: t(\"text-see-more\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\auto-suggestion.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AutoSuggestion, \"4RnRNJiHpB9q7GSIHCO6Xnv5sUA=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = AutoSuggestion;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AutoSuggestion);\nvar _c;\n$RefreshReg$(_c, \"AutoSuggestion\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/auto-suggestion.tsx\n"));

/***/ })

}]);