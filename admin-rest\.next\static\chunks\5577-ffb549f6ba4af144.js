(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5577],{23350:function(e,l,t){"use strict";t.d(l,{Z:function(){return CreateOrUpdateTypeForm}});var i=t(85893),n=t(33e3),r=t(87536),o=t(60802),s=t(80602),a=t(92072),d=t(11163),c=t(47559),m=t(23091),v=t(5766);let p=[{value:"FruitsVegetable",label:"Fruits and Vegetable"},{value:"FacialCare",label:"Facial Care"},{value:"Handbag",label:"Hand Bag"},{value:"DressIcon",label:"Dress Icon"},{value:"FurnitureIcon",label:"Furniture Icon"},{value:"BookIcon",label:"Book Icon"},{value:"MedicineIcon",label:"Medicine Icon"},{value:"Restaurant",label:"Restaurant"},{value:"Bakery",label:"Bakery"},{value:"BabyCare",label:"Baby Care"},{value:"Gadgets",label:"Gadgets"},{value:"Plant",label:"Plant"},{value:"HomeAppliance",label:"Home Appliance"},{value:"MicroGreens",label:"Micro Greens"}];var b=t(5233),g=t(47533),h=t(16310);let f=h.Ry().shape({name:h.Z_().required("form:error-name-required"),banners:h.IX().min(1,"form:error-min-one-banner").when("settings.layoutType",{is:e=>!e.includes("compact"),then:()=>h.IX().of(h.Ry().shape({title:h.Z_().required("form:error-title-required")})),otherwise:()=>h.IX().of(h.Ry().shape({image:h.Ry().test("check-digital-file","form:error-banner-file-input-required",e=>e&&(null==e?void 0:e.original))}))}),settings:h.Ry().shape({bestSelling:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("Best selling title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})}),popularProducts:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("Popular products title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})}),category:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("Category title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})}),handpickedProducts:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({products:h.IX().when("enable",{is:e=>e,then:()=>h.IX().when("enableSlider",{is:e=>!e,then:()=>h.IX().required("Hand picked products is required.").min(1,"Minimum 1 products is required.").max(3,"You entered only maximum 3 items."),otherwise:()=>h.IX().required("Hand picked products is required.").min(1,"Minimum 1 products is required.")})})})}),newArrival:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("New arrival products title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})}),authors:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("Authors title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})}),manufactures:h.Ry().when("layoutType",{is:e=>e.includes("compact"),then:()=>h.Ry().shape({title:h.Z_().when("enable",{is:e=>e,then:()=>h.Z_().required("Manufactures title is required.")})}),otherwise:()=>h.Ry().shape({title:h.Z_().notRequired().nullable()})})})});var x=t(28454),y=t(66272),j=t(59565),w=t(21587),N=t(95414),Z=t(67294),_=t(14895),k=t.n(_);let P=Z.forwardRef((e,l)=>{let{className:t,label:n,labelKey:r,name:o,id:s,src:a,error:d,errorKey:c,...m}=e,{t:v}=(0,b.$G)();return(0,i.jsxs)("div",{className:t,children:[(0,i.jsxs)("div",{className:"flex w-full h-full",children:[(0,i.jsx)("input",{id:s,name:o,type:"radio",ref:l,className:k().radio_input,...m}),(0,i.jsxs)("label",{htmlFor:s,className:"w-full flex flex-col rounded cursor-pointer border border-gray-200",children:[(0,i.jsx)("div",{className:"p-3 pb-0 w-full max-h-72 flex items-center justify-center overflow-hidden",children:(0,i.jsx)("img",{src:null!=a?a:"/product-placeholder-borderless.svg",alt:v(r||n),className:"w-auto h-full object-contain"})}),(0,i.jsx)("h3",{className:"text-body text-sm text-center font-semibold p-5 mt-auto",children:(0,i.jsx)("span",{children:v(r||n)})})]})]}),c&&(0,i.jsx)("p",{className:"my-2 text-xs text-start text-red-500",children:v(c)})]})});P.displayName="RadioCard";var C=t(47457),q=t(16720),S=t(71261),R=t(93345),T=t(24504),E=t(22220),O=t(66271),F=t(93967),I=t.n(F),L=t(15724),M=t(77180),z=t(93242);let A=p.map(e=>(e.label=(0,i.jsxs)("div",{className:"flex items-center space-s-5",children:[(0,i.jsx)("span",{className:"flex items-center justify-center w-5 h-5",children:(0,c.q)({iconList:v,iconName:e.value,className:"max-h-full max-w-full"})}),(0,i.jsx)("span",{children:e.label})]}),e)),D=[{label:"Classic",value:"classic",img:"/image/layout-classic.png"},{label:"Compact",value:"compact",img:"/image/layout-compact.png"},{label:"Minimal",value:"minimal",img:"/image/layout-minimal.png"},{label:"Modern",value:"modern",img:"/image/layout-modern.png"},{label:"Standard",value:"standard",img:"/image/layout-standard.png"}],B=[{label:"Helium",value:"helium",img:"/image/card-helium.png"},{label:"Neon",value:"neon",img:"/image/card-neon.png"},{label:"Argon",value:"argon",img:"/image/card-argon.png"},{label:"Krypton",value:"krypton",img:"/image/card-krypton.png"},{label:"Xenon",value:"xenon",img:"/image/card-xenon.png"},{label:"Radon",value:"radon",img:"/image/card-radon.png"}];function CreateOrUpdateTypeForm(e){var l,t,c,v,h,_,k,F,V,G,X,K,Y,$,U,Q,W,J,ee,el,et,ei,en,er,eo,es,ea,ed,eu,ec,em,ev,ep,eb,eg,eh,ef,ex,ey,ej,ew,eN,eZ,e_,ek,eP,eC,eq,eS,eR,eT,eE,eO,eF,eI,eL,eM,ez,eA,eH,eD,eB,eV,eG,eX,eK,eY,e$,eU,eQ;let{initialValues:eW}=e,eJ=(0,d.useRouter)(),{t:e0}=(0,b.$G)(),[e1,e5]=(0,Z.useState)(""),[e2,e4]=(0,Z.useState)(""),[e3,e7]=(0,Z.useState)(!0),e9=(null==eJ?void 0:null===(l=eJ.query)||void 0===l?void 0:l.action)==="edit"&&(null==eJ?void 0:eJ.locale)===R.Config.defaultLanguage,{register:e8,control:e6,handleSubmit:le,watch:ll,formState:{errors:lt}}=(0,r.cI)({shouldUnregister:!0,resolver:(0,g.X)(f),defaultValues:{...eW,settings:{...null==eW?void 0:eW.settings,layoutType:(null==eW?void 0:null===(t=eW.settings)||void 0===t?void 0:t.layoutType)?null==eW?void 0:null===(c=eW.settings)||void 0===c?void 0:c.layoutType:D[0].value,productCard:(null==eW?void 0:null===(v=eW.settings)||void 0===v?void 0:v.productCard)?null==eW?void 0:null===(h=eW.settings)||void 0===h?void 0:h.productCard:B[0].value,bestSelling:{enable:null==eW?void 0:null===(k=eW.settings)||void 0===k?void 0:null===(_=k.bestSelling)||void 0===_?void 0:_.enable,title:null==eW?void 0:null===(V=eW.settings)||void 0===V?void 0:null===(F=V.bestSelling)||void 0===F?void 0:F.title},popularProducts:{enable:null==eW?void 0:null===(X=eW.settings)||void 0===X?void 0:null===(G=X.popularProducts)||void 0===G?void 0:G.enable,title:null==eW?void 0:null===(Y=eW.settings)||void 0===Y?void 0:null===(K=Y.popularProducts)||void 0===K?void 0:K.title},category:{enable:null==eW?void 0:null===(U=eW.settings)||void 0===U?void 0:null===($=U.category)||void 0===$?void 0:$.enable,title:null==eW?void 0:null===(W=eW.settings)||void 0===W?void 0:null===(Q=W.category)||void 0===Q?void 0:Q.title},handpickedProducts:{enable:null==eW?void 0:null===(ee=eW.settings)||void 0===ee?void 0:null===(J=ee.handpickedProducts)||void 0===J?void 0:J.enable,enableSlider:null==eW?void 0:null===(et=eW.settings)||void 0===et?void 0:null===(el=et.handpickedProducts)||void 0===el?void 0:el.enableSlider,title:null==eW?void 0:null===(en=eW.settings)||void 0===en?void 0:null===(ei=en.handpickedProducts)||void 0===ei?void 0:ei.title,products:(null==eW?void 0:null===(eo=eW.settings)||void 0===eo?void 0:null===(er=eo.handpickedProducts)||void 0===er?void 0:er.products)?null==eW?void 0:null===(ed=eW.settings)||void 0===ed?void 0:null===(ea=ed.handpickedProducts)||void 0===ea?void 0:null===(es=ea.products)||void 0===es?void 0:es.map(e=>e):[]},newArrival:{enable:null==eW?void 0:null===(ec=eW.settings)||void 0===ec?void 0:null===(eu=ec.newArrival)||void 0===eu?void 0:eu.enable,title:null==eW?void 0:null===(ev=eW.settings)||void 0===ev?void 0:null===(em=ev.newArrival)||void 0===em?void 0:em.title},authors:{enable:null==eW?void 0:null===(eb=eW.settings)||void 0===eb?void 0:null===(ep=eb.authors)||void 0===ep?void 0:ep.enable,title:null==eW?void 0:null===(eh=eW.settings)||void 0===eh?void 0:null===(eg=eh.authors)||void 0===eg?void 0:eg.title},manufactures:{enable:null==eW?void 0:null===(ex=eW.settings)||void 0===ex?void 0:null===(ef=ex.manufactures)||void 0===ef?void 0:ef.enable,title:null==eW?void 0:null===(ej=eW.settings)||void 0===ej?void 0:null===(ey=ej.manufactures)||void 0===ey?void 0:ey.title}},icon:(null==eW?void 0:eW.icon)?p.find(e=>e.value===(null==eW?void 0:eW.icon)):""}}),{fields:li,append:ln,remove:lr}=(0,r.Dq)({control:e6,name:"banners"}),lo=(0,r.qo)({control:e6,name:"settings.layoutType"}),ls=(0,r.qo)({control:e6,name:"settings.bestSelling.enable"}),la=(0,r.qo)({control:e6,name:"settings.popularProducts.enable"}),ld=(0,r.qo)({control:e6,name:"settings.category.enable"}),lu=(0,r.qo)({control:e6,name:"settings.handpickedProducts.enable"}),lc=(0,r.qo)({control:e6,name:"settings.newArrival.enable"}),lm=(0,r.qo)({control:e6,name:"settings.authors.enable"}),lv=(0,r.qo)({control:e6,name:"settings.manufactures.enable"}),{mutate:lp,isLoading:lb}=(0,q.jT)(),{mutate:lg,isLoading:lh}=(0,q.oy)(),lf=(0,T.g)(ll("name")),{products:lx,loading:ly}=(0,z.kN)({limit:999,language:null==eJ?void 0:eJ.locale,type:e1,categories:e2,status:"publish"});return(0,i.jsxs)("form",{onSubmit:le(e=>{var l,t,i,n,r,o,s,a,d,c,m,v,p,b,g,h,f,x,y,j,w,N,Z,_,k,P,C,q,S,R,T,E,O,F,I,L,M,z,A;let D={language:eJ.locale,name:e.name,slug:e.slug,icon:null===(l=e.icon)||void 0===l?void 0:l.value,settings:{isHome:null==e?void 0:null===(t=e.settings)||void 0===t?void 0:t.isHome,productCard:null==e?void 0:null===(i=e.settings)||void 0===i?void 0:i.productCard,layoutType:null==e?void 0:null===(n=e.settings)||void 0===n?void 0:n.layoutType,bestSelling:{enable:null==e?void 0:null===(o=e.settings)||void 0===o?void 0:null===(r=o.bestSelling)||void 0===r?void 0:r.enable,title:null==e?void 0:null===(a=e.settings)||void 0===a?void 0:null===(s=a.bestSelling)||void 0===s?void 0:s.title},popularProducts:{enable:null==e?void 0:null===(c=e.settings)||void 0===c?void 0:null===(d=c.popularProducts)||void 0===d?void 0:d.enable,title:null==e?void 0:null===(v=e.settings)||void 0===v?void 0:null===(m=v.popularProducts)||void 0===m?void 0:m.title},category:{enable:null==e?void 0:null===(b=e.settings)||void 0===b?void 0:null===(p=b.category)||void 0===p?void 0:p.enable,title:null==e?void 0:null===(h=e.settings)||void 0===h?void 0:null===(g=h.category)||void 0===g?void 0:g.title},handpickedProducts:{enable:null==e?void 0:null===(x=e.settings)||void 0===x?void 0:null===(f=x.handpickedProducts)||void 0===f?void 0:f.enable,enableSlider:null==e?void 0:null===(j=e.settings)||void 0===j?void 0:null===(y=j.handpickedProducts)||void 0===y?void 0:y.enableSlider,title:null==e?void 0:null===(N=e.settings)||void 0===N?void 0:null===(w=N.handpickedProducts)||void 0===w?void 0:w.title,products:null==e?void 0:null===(k=e.settings)||void 0===k?void 0:null===(_=k.handpickedProducts)||void 0===_?void 0:null===(Z=_.products)||void 0===Z?void 0:Z.map(l=>{var t,i,n,r;return{id:null==l?void 0:l.id,name:null==l?void 0:l.name,slug:null==l?void 0:l.slug,regular_price:null==l?void 0:l.regular_price,sale_price:null==l?void 0:l.sale_price,min_price:null==l?void 0:l.min_price,max_price:null==l?void 0:l.max_price,product_type:null==l?void 0:l.product_type,quantity:null==l?void 0:l.quantity,is_external:null==l?void 0:l.is_external,unit:null==l?void 0:l.unit,price:null==l?void 0:l.price,external_product_url:null==l?void 0:l.external_product_url,status:null==l?void 0:l.status,image:{id:null==l?void 0:null===(t=l.image)||void 0===t?void 0:t.id,thumbnail:null==l?void 0:null===(i=l.image)||void 0===i?void 0:i.thumbnail,original:null==l?void 0:null===(n=l.image)||void 0===n?void 0:n.original},type:{settings:{productCard:null==e?void 0:null===(r=e.settings)||void 0===r?void 0:r.productCard}}}})},newArrival:{enable:null==e?void 0:null===(C=e.settings)||void 0===C?void 0:null===(P=C.newArrival)||void 0===P?void 0:P.enable,title:null==e?void 0:null===(S=e.settings)||void 0===S?void 0:null===(q=S.newArrival)||void 0===q?void 0:q.title},authors:{enable:null==e?void 0:null===(T=e.settings)||void 0===T?void 0:null===(R=T.authors)||void 0===R?void 0:R.enable,title:null==e?void 0:null===(O=e.settings)||void 0===O?void 0:null===(E=O.authors)||void 0===E?void 0:E.title},manufactures:{enable:null==e?void 0:null===(I=e.settings)||void 0===I?void 0:null===(F=I.manufactures)||void 0===F?void 0:F.enable,title:null==e?void 0:null===(M=e.settings)||void 0===M?void 0:null===(L=M.manufactures)||void 0===L?void 0:L.title}},promotional_sliders:null===(z=e.promotional_sliders)||void 0===z?void 0:z.map(e=>{let{thumbnail:l,original:t,id:i}=e;return{thumbnail:l,original:t,id:i}}),banners:null==e?void 0:null===(A=e.banners)||void 0===A?void 0:A.map(e=>{var l,t,i;return{...e,image:{id:null==e?void 0:null===(l=e.image)||void 0===l?void 0:l.id,thumbnail:null==e?void 0:null===(t=e.image)||void 0===t?void 0:t.thumbnail,original:null==e?void 0:null===(i=e.image)||void 0===i?void 0:i.original}}})};eW&&eW.translated_languages.includes(eJ.locale)?lg({...D,id:eW.id}):lp({...D,...(null==eW?void 0:eW.slug)&&{slug:eW.slug}})}),children:[(0,i.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,i.jsx)(s.Z,{title:e0("form:item-description"),details:"".concat(eW?e0("form:item-description-update"):e0("form:item-description-add")," ").concat(e0("form:group-description-help-text")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsxs)(a.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,i.jsx)(n.Z,{label:e0("form:input-label-name"),...e8("name"),error:e0(null===(ew=lt.name)||void 0===ew?void 0:ew.message),variant:"outline",className:"mb-5"}),e9?(0,i.jsxs)("div",{className:"relative mb-5",children:[(0,i.jsx)(n.Z,{label:e0("form:input-label-slug"),...e8("slug"),error:e0(null===(eN=lt.slug)||void 0===eN?void 0:eN.message),variant:"outline",disabled:e3}),(0,i.jsx)("button",{className:"absolute top-[27px] right-px z-0 flex h-[46px] w-11 items-center justify-center rounded-tr rounded-br border-l border-solid border-border-base bg-white px-2 text-body transition duration-200 hover:text-heading focus:outline-none",type:"button",title:e0("common:text-edit"),onClick:()=>e7(!1),children:(0,i.jsx)(S.dY,{width:14})})]}):(0,i.jsx)(n.Z,{label:e0("form:input-label-slug"),...e8("slug"),value:lf,variant:"outline",className:"mb-5",disabled:!0}),(0,i.jsxs)("div",{className:"mb-5",children:[(0,i.jsx)(m.Z,{children:e0("form:input-label-select-icon")}),(0,i.jsx)(x.Z,{name:"icon",control:e6,options:A,isClearable:!0,placeholder:"Select Icon"})]})]})]}),(0,i.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,i.jsx)(s.Z,{title:e0("form:group-settings"),details:e0("form:group-settings-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsxs)(a.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,i.jsx)(C.Z,{...e8("settings.isHome"),error:e0(null===(e_=lt.settings)||void 0===e_?void 0:null===(eZ=e_.isHome)||void 0===eZ?void 0:eZ.message),label:e0("form:input-label-is-home"),className:"mb-5"}),(0,i.jsxs)("div",{className:"mb-10",children:[(0,i.jsx)(m.Z,{className:"mb-5",children:e0("form:input-label-layout-type")}),(0,i.jsx)("div",{className:"grid grid-cols-3 gap-5",children:null==D?void 0:D.map((e,l)=>(0,i.jsx)(P,{...e8("settings.layoutType"),label:e0(e.label),value:e.value,src:e.img,id:null==e?void 0:e.value},l))})]}),(0,i.jsxs)("div",{className:"mb-5",children:[(0,i.jsx)(m.Z,{className:"mb-5",children:e0("form:input-label-product-card-type")}),(0,i.jsx)("div",{className:"grid grid-cols-3 gap-5",children:null==B?void 0:B.map((e,l)=>(0,i.jsx)(P,{...e8("settings.productCard"),label:e0(e.label),value:e.value,src:e.img,id:"product-card-".concat(l)},"product-card-".concat(l)))})]})]})]}),"classic"===lo?(0,i.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,i.jsx)(s.Z,{title:e0("form:promotional-slider"),details:e0("form:promotional-slider-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsx)(a.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,i.jsx)(y.Z,{name:"promotional_sliders",control:e6})})]}):null,(0,i.jsxs)("div",{className:I()("compact"===lo?"my-5 flex flex-wrap border-b border-dashed border-border-base pb-8 sm:my-8":"my-5 flex flex-wrap sm:my-8"),children:[(0,i.jsx)(s.Z,{title:e0("common:text-banner"),details:e0("form:banner-slider-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsxs)(a.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:["minimal"===lo&&(null==li?void 0:li.length)>0?(0,i.jsx)(w.Z,{className:"mb-5",message:"Minimal demo will show only first item of banner."}):"",("compact"===lo||"minimal"===lo)&&(null==li?void 0:li.length)>0?(0,i.jsx)(w.Z,{className:"mb-5",message:"Disabled item will not show in shop end.",variant:"warning"}):"",(0,i.jsx)("div",{children:null==li?void 0:li.map((e,l)=>{var t,r,o,s,a,d;return(0,i.jsxs)("div",{className:"py-5 border-b border-dashed border-border-200 first:pt-0 last:border-0 md:py-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-5",children:[(0,i.jsxs)(j.Z,{className:"mb-0",children:[e0("common:text-banner")," ",l+1]}),(0,i.jsx)("button",{onClick:()=>{lr(l)},type:"button",className:I()("text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none sm:col-span-1 sm:mt-4","minimal"===lo&&0!==l&&l>0?"pointer-events-none cursor-not-allowed text-opacity-80":""),disabled:"minimal"===lo&&0!==l&&l>0,children:e0("form:button-label-remove")})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-5",children:[(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("banners.".concat(l,".title")),defaultValue:null==e?void 0:e.title,error:e0(null===(o=lt.banners)||void 0===o?void 0:null===(r=o[l])||void 0===r?void 0:null===(t=r.title)||void 0===t?void 0:t.message),disabled:"compact"===lo||"minimal"===lo&&0!==l&&l>0}),(0,i.jsx)(N.Z,{label:e0("form:input-description"),variant:"outline",...e8("banners.".concat(l,".description")),defaultValue:e.description,disabled:"compact"===lo||"minimal"===lo&&0!==l&&l>0})]}),(0,i.jsxs)("div",{className:"mt-5 w-full",children:[(0,i.jsxs)(j.Z,{children:[e0("form:input-gallery"),"compact"===lo?(0,i.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):""]}),(0,i.jsx)(y.Z,{name:"banners.".concat(l,".image"),control:e6,multiple:!1,disabled:"minimal"===lo&&0!==l&&l>0}),(0,i.jsx)(O.Z,{message:e0(null==lt?void 0:null===(d=lt.banners)||void 0===d?void 0:null===(a=d[l])||void 0===a?void 0:null===(s=a.image)||void 0===s?void 0:s.message)})]})]},e.id)})}),(0,i.jsx)(o.Z,{type:"button",onClick:()=>ln({title:"",description:"",image:{}}),className:"w-full sm:w-auto",disabled:"minimal"===lo&&(null==li?void 0:li.length)>0,children:e0("form:button-label-add-banner")}),(null==lt?void 0:null===(ek=lt.banners)||void 0===ek?void 0:ek.message)?(0,i.jsx)(w.Z,{message:e0(null==lt?void 0:null===(eP=lt.banners)||void 0===eP?void 0:eP.message),variant:"error",className:"mt-5"}):null]})]}),"compact"===lo?(0,i.jsxs)("div",{className:"my-5 flex flex-wrap sm:my-8",children:[(0,i.jsx)(s.Z,{title:"Layout Content Settings.",details:"Please set your layout content here.",className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsx)(a.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,i.jsxs)("div",{className:"grid gap-5",children:[(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.bestSelling.enable",control:e6}),(0,i.jsx)(m.Z,{htmlFor:"settings.bestSelling.enable",className:"mb-0 cursor-pointer",children:"Enable Best Selling Products?"})]}),ls?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.bestSelling.title"),error:e0(null==lt?void 0:null===(eS=lt.settings)||void 0===eS?void 0:null===(eq=eS.bestSelling)||void 0===eq?void 0:null===(eC=eq.title)||void 0===eC?void 0:eC.message),required:!0}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.popularProducts.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.popularProducts.enable",children:"Enable Popular Products?"})]}),la?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.popularProducts.title"),error:e0(null==lt?void 0:null===(eE=lt.settings)||void 0===eE?void 0:null===(eT=eE.popularProducts)||void 0===eT?void 0:null===(eR=eT.title)||void 0===eR?void 0:eR.message),required:!0}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.category.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.category.enable",children:"Enable Category?"})]}),ld?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.category.title"),error:e0(null==lt?void 0:null===(eI=lt.settings)||void 0===eI?void 0:null===(eF=eI.category)||void 0===eF?void 0:null===(eO=eF.title)||void 0===eO?void 0:eO.message),required:!0}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.handpickedProducts.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.handpickedProducts.enable",children:"Enable Handpicked Products?"})]}),lu?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.handpickedProducts.title"),error:e0(null==lt?void 0:null===(ez=lt.settings)||void 0===ez?void 0:null===(eM=ez.handpickedProducts)||void 0===eM?void 0:null===(eL=eM.title)||void 0===eL?void 0:eL.message)}),(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.handpickedProducts.enableSlider",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.handpickedProducts.enableSlider",children:"Enable Slider?"})]}),(0,i.jsxs)("div",{className:"grid gap-5",children:[(0,i.jsx)(L.Z,{className:"w-full",type:e1,enableCategory:!0,enableType:!0,onCategoryFilter:e=>{e4(null==e?void 0:e.slug)},onTypeFilter:e=>{e5(null==e?void 0:e.slug)}}),(0,i.jsxs)("div",{children:[(0,i.jsxs)(m.Z,{children:["Products ",(0,i.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"})]}),(0,i.jsx)(x.Z,{name:"settings.handpickedProducts.products",control:e6,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:lx,isClearable:!0,isLoading:ly,isMulti:!0}),(0,i.jsx)(O.Z,{message:e0(null==lt?void 0:null===(eD=lt.settings)||void 0===eD?void 0:null===(eH=eD.handpickedProducts)||void 0===eH?void 0:null===(eA=eH.products)||void 0===eA?void 0:eA.message)})]})]})]}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.newArrival.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.newArrival.enable",children:"Enable New Arrival?"})]}),lc?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.newArrival.title"),error:e0(null==lt?void 0:null===(eG=lt.settings)||void 0===eG?void 0:null===(eV=eG.newArrival)||void 0===eV?void 0:null===(eB=eV.title)||void 0===eB?void 0:eB.message),required:!0}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.authors.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.authors.enable",children:"Enable Authors?"})]}),lm?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.authors.title"),error:e0(null==lt?void 0:null===(eY=lt.settings)||void 0===eY?void 0:null===(eK=eY.authors)||void 0===eK?void 0:null===(eX=eK.title)||void 0===eX?void 0:eX.message),required:!0}):"",(0,i.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,i.jsx)(M.Z,{name:"settings.manufactures.enable",control:e6}),(0,i.jsx)(m.Z,{className:"mb-0 cursor-pointer",htmlFor:"settings.manufactures.enable",children:"Enable Manufactures?"})]}),lv?(0,i.jsx)(n.Z,{label:e0("form:input-title"),variant:"outline",...e8("settings.manufactures.title"),error:e0(null==lt?void 0:null===(eQ=lt.settings)||void 0===eQ?void 0:null===(eU=eQ.manufactures)||void 0===eU?void 0:null===(e$=eU.title)||void 0===e$?void 0:e$.message),required:!0}):""]})})]}):"",(0,i.jsx)(E.Z,{className:"z-0",children:(0,i.jsxs)("div",{className:"text-end",children:[eW&&(0,i.jsx)(o.Z,{variant:"outline",onClick:eJ.back,className:"text-sm me-4 md:text-base",type:"button",children:e0("form:button-label-back")}),(0,i.jsx)(o.Z,{loading:lb||lh,disabled:lb||lh,className:"text-sm md:text-base",children:eW?e0("form:button-label-update-group"):e0("form:button-label-add-group")})]})})]})}},71261:function(e,l,t){"use strict";t.d(l,{Iy:function(){return EditFillIcon},Iz:function(){return EditGhostIcon},dK:function(){return ComposeEditIcon},dY:function(){return EditIcon}});var i=t(85893);let EditIcon=e=>(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20.547 20.299",fill:"currentColor",...e,children:(0,i.jsxs)("g",{stroke:"currentColor",strokeWidth:".4",children:[(0,i.jsx)("path",{"data-name":"Path 78",d:"M18.659 12.688a.5.5 0 00-.5.5v4.423a1.5 1.5 0 01-1.494 1.494H2.691A1.5 1.5 0 011.2 17.609V4.629a1.5 1.5 0 011.494-1.494h4.419a.5.5 0 100-1H2.691A2.493 2.493 0 00.2 4.629v12.98A2.493 2.493 0 002.691 20.1h13.976a2.493 2.493 0 002.491-2.491v-4.423a.5.5 0 00-.5-.5zm0 0"}),(0,i.jsx)("path",{"data-name":"Path 79",d:"M18.96.856a2.241 2.241 0 00-3.17 0L6.899 9.739a.5.5 0 00-.128.219l-1.169 4.219a.5.5 0 00.613.613l4.219-1.169a.5.5 0 00.219-.128l8.886-8.887a2.244 2.244 0 000-3.17zm-10.971 9.21l7.273-7.273 2.346 2.346-7.273 7.273zm-.469.94l1.879 1.875-2.592.718zm11.32-7.1l-.528.528-2.346-2.345.528-.528a1.245 1.245 0 011.761 0l.585.584a1.247 1.247 0 010 1.761zm0 0"})]})}),EditFillIcon=e=>(0,i.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,i.jsx)("path",{d:"M4.671 7.87l4.546-4.546 1.459 1.459-4.547 4.546h0a2.563 2.563 0 01-1.08.645s0 0 0 0l-1.456.433.434-1.455c.121-.409.343-.78.644-1.081h0zm-1.189 2.57s0 0 0 0h0zm8.112-9.065a1.031 1.031 0 01.729 1.76l-.321.322-1.459-1.459.322-.32a1.03 1.03 0 01.729-.303z",fill:"currentColor",stroke:"currentColor"}),(0,i.jsx)("path",{d:"M3.063 3.063a1.75 1.75 0 00-1.75 1.75v6.125a1.75 1.75 0 001.75 1.75h6.124a1.75 1.75 0 001.75-1.75V7.874a.438.438 0 00-.874 0v3.063a.875.875 0 01-.876.874H3.064a.875.875 0 01-.876-.874V4.811a.875.875 0 01.876-.875h3.062a.437.437 0 100-.874H3.062z",fill:"currentColor"})]}),EditGhostIcon=e=>(0,i.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,i.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,i.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h2.793a.992.992 0 00.707-.293l5.23-5.229.217.869-2.3 2.3a.5.5 0 00.707.707l2.5-2.5a.5.5 0 00.132-.475l-.432-1.726L14.207 6a.999.999 0 000-1.414zM3 13v-1.793L4.793 13H3zm3-.207L3.207 10 8.5 4.707 11.293 7.5 6 12.793zm6-6L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]}),ComposeEditIcon=e=>(0,i.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,i.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,i.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h10.5a.5.5 0 000-1H7.208l7-7a.999.999 0 000-1.414zM3 10.206l5.5-5.5L11.293 7.5l-5.5 5.5H3v-2.793zm9-3.413L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]})},47457:function(e,l,t){"use strict";var i=t(85893),n=t(67294);let r=n.forwardRef((e,l)=>{let{className:t,label:n,name:r,error:o,...s}=e;return(0,i.jsxs)("div",{className:t,children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{id:r,name:r,type:"checkbox",ref:l,className:"pb-checkbox",...s}),(0,i.jsx)("label",{htmlFor:r,className:"text-sm text-body",children:n})]}),o&&(0,i.jsx)("p",{className:"my-2 text-xs text-red-500 text-end",children:o})]})});r.displayName="Checkbox",l.Z=r},28454:function(e,l,t){"use strict";var i=t(85893),n=t(79828),r=t(71611),o=t(87536);l.Z=e=>{let{control:l,options:t,name:s,rules:a,getOptionLabel:d,getOptionValue:c,disabled:m,isMulti:v,isClearable:p,isLoading:b,placeholder:g,label:h,required:f,toolTipText:x,error:y,...j}=e;return(0,i.jsxs)(i.Fragment,{children:[h?(0,i.jsx)(r.Z,{htmlFor:s,toolTipText:x,label:h,required:f}):"",(0,i.jsx)(o.Qr,{control:l,name:s,rules:a,...j,render:e=>{let{field:l}=e;return(0,i.jsx)(n.Z,{...l,getOptionLabel:d,getOptionValue:c,placeholder:g,isMulti:v,isClearable:p,isLoading:b,options:t,isDisabled:m})}}),y&&(0,i.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:y})]})}},22220:function(e,l,t){"use strict";var i=t(85893),n=t(93967),r=t.n(n),o=t(98388);l.Z=e=>{let{children:l,className:t,...n}=e;return(0,i.jsx)("div",{className:(0,o.m6)(r()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",t)),...n,children:l})}},77180:function(e,l,t){"use strict";var i=t(85893),n=t(66271),r=t(71611),o=t(77768),s=t(93967),a=t.n(s),d=t(5233),c=t(87536),m=t(98388);l.Z=e=>{let{control:l,label:t,name:s,error:v,disabled:p,required:b,toolTipText:g,className:h,labelClassName:f,...x}=e,{t:y}=(0,d.$G)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:(0,m.m6)(a()("flex items-center gap-x-4",h)),children:[(0,i.jsx)(c.Qr,{name:s,control:l,...x,render:e=>{let{field:{onChange:l,value:n}}=e;return(0,i.jsxs)(o.r,{checked:n,onChange:l,disabled:p,className:"".concat(n?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(p?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,i.jsxs)("span",{className:"sr-only",children:["Enable ",t]}),(0,i.jsx)("span",{className:"".concat(n?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),t?(0,i.jsx)(r.Z,{htmlFor:s,className:a()("mb-0",f),toolTipText:g,label:t,required:b}):""]}),v?(0,i.jsx)(n.Z,{message:v}):""]})}},95414:function(e,l,t){"use strict";var i=t(85893),n=t(71611),r=t(93967),o=t.n(r),s=t(67294),a=t(98388);let d=s.forwardRef((e,l)=>{let{className:t,label:r,toolTipText:s,name:d,error:c,variant:m="normal",shadow:v=!1,inputClassName:p,disabled:b,required:g,...h}=e,f=o()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===m,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===m,"border border-border-base focus:border-accent":"outline"===m},{"focus:shadow":v},p);return(0,i.jsxs)("div",{className:(0,a.m6)(o()(t)),children:[r&&(0,i.jsx)(n.Z,{htmlFor:d,toolTipText:s,label:r,required:g}),(0,i.jsx)("textarea",{id:d,name:d,className:(0,a.m6)(o()(f,b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:l,disabled:b,...h}),c&&(0,i.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});d.displayName="TextArea",l.Z=d},59565:function(e,l,t){"use strict";var i=t(85893),n=t(93967),r=t.n(n),o=t(98388);l.Z=e=>{let{className:l="mb-3",...t}=e;return(0,i.jsx)("span",{className:(0,o.m6)(r()("block text-body-dark font-semibold text-sm leading-none",l)),...t})}},24504:function(e,l,t){"use strict";function formatSlug(e){if(!e)return"";let l=e.replace(/\s+/g,"-").toLowerCase(),t=l.replace(/-+$/,"");return t}t.d(l,{g:function(){return formatSlug}})},14895:function(e){e.exports={radio_input:"radio-card_radio_input__ynTqM"}},97326:function(e,l,t){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}t.d(l,{Z:function(){return _assertThisInitialized}})},15671:function(e,l,t){"use strict";function _classCallCheck(e,l){if(!(e instanceof l))throw TypeError("Cannot call a class as a function")}t.d(l,{Z:function(){return _classCallCheck}})},43144:function(e,l,t){"use strict";t.d(l,{Z:function(){return _createClass}});var i=t(83997);function _defineProperties(e,l){for(var t=0;t<l.length;t++){var n=l[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,i.Z)(n.key),n)}}function _createClass(e,l,t){return l&&_defineProperties(e.prototype,l),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,l,t){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}t.d(l,{Z:function(){return _createSuper}});var i=t(71002),n=t(97326);function _createSuper(e){var l=_isNativeReflectConstruct();return function(){var t,r=_getPrototypeOf(e);if(l){var o=_getPrototypeOf(this).constructor;t=Reflect.construct(r,arguments,o)}else t=r.apply(this,arguments);return function(e,l){if(l&&("object"==(0,i.Z)(l)||"function"==typeof l))return l;if(void 0!==l)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,t)}}},60136:function(e,l,t){"use strict";t.d(l,{Z:function(){return _inherits}});var i=t(89611);function _inherits(e,l){if("function"!=typeof l&&null!==l)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(l&&l.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),l&&(0,i.Z)(e,l)}},1413:function(e,l,t){"use strict";t.d(l,{Z:function(){return _objectSpread2}});var i=t(4942);function ownKeys(e,l){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);l&&(i=i.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,i)}return t}function _objectSpread2(e){for(var l=1;l<arguments.length;l++){var t=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(t),!0).forEach(function(l){(0,i.Z)(e,l,t[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(t,l))})}return e}},95389:function(e,l,t){"use strict";t.d(l,{_:function(){return c},b:function(){return H}});var i=t(67294),n=t(19946),r=t(12351),o=t(16723),s=t(23784),a=t(73781);let d=(0,i.createContext)(null);function H(){let[e,l]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)(()=>function(e){let t=(0,a.z)(e=>(l(l=>[...l,e]),()=>l(l=>{let t=l.slice(),i=t.indexOf(e);return -1!==i&&t.splice(i,1),t}))),n=(0,i.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props}),[t,e.slot,e.name,e.props]);return i.createElement(d.Provider,{value:n},e.children)},[l])]}let c=Object.assign((0,r.yV)(function(e,l){let t=(0,n.M)(),{id:a=`headlessui-label-${t}`,passive:c=!1,...m}=e,v=function u(){let e=(0,i.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),p=(0,s.T)(l);(0,o.e)(()=>v.register(a),[a,v.register]);let b={ref:p,...v.props,id:a};return c&&("onClick"in b&&(delete b.htmlFor,delete b.onClick),"onClick"in m&&delete m.onClick),(0,r.sY)({ourProps:b,theirProps:m,slot:v.slot||{},defaultTag:"label",name:v.name||"Label"})}),{})},77768:function(e,l,t){"use strict";t.d(l,{r:function(){return y}});var i=t(67294),n=t(12351),r=t(19946),o=t(61363),s=t(64103),a=t(95389),d=t(39516),c=t(14157),m=t(23784),v=t(46045),p=t(18689),b=t(73781),g=t(31147),h=t(94192);let f=(0,i.createContext)(null);f.displayName="GroupContext";let x=i.Fragment,y=Object.assign((0,n.yV)(function(e,l){let t=(0,r.M)(),{id:a=`headlessui-switch-${t}`,checked:d,defaultChecked:x=!1,onChange:y,name:j,value:w,form:N,...Z}=e,_=(0,i.useContext)(f),k=(0,i.useRef)(null),P=(0,m.T)(k,l,null===_?null:_.setSwitch),[C,q]=(0,g.q)(d,y,x),S=(0,b.z)(()=>null==q?void 0:q(!C)),R=(0,b.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),S()}),T=(0,b.z)(e=>{e.key===o.R.Space?(e.preventDefault(),S()):e.key===o.R.Enter&&(0,p.g)(e.currentTarget)}),E=(0,b.z)(e=>e.preventDefault()),O=(0,i.useMemo)(()=>({checked:C}),[C]),F={id:a,ref:P,role:"switch",type:(0,c.f)(e,k),tabIndex:0,"aria-checked":C,"aria-labelledby":null==_?void 0:_.labelledby,"aria-describedby":null==_?void 0:_.describedby,onClick:R,onKeyUp:T,onKeyPress:E},I=(0,h.G)();return(0,i.useEffect)(()=>{var e;let l=null==(e=k.current)?void 0:e.closest("form");l&&void 0!==x&&I.addEventListener(l,"reset",()=>{q(x)})},[k,q]),i.createElement(i.Fragment,null,null!=j&&C&&i.createElement(v._,{features:v.A.Hidden,...(0,n.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:N,checked:C,name:j,value:w})}),(0,n.sY)({ourProps:F,theirProps:Z,slot:O,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var l;let[t,r]=(0,i.useState)(null),[o,s]=(0,a.b)(),[c,m]=(0,d.f)(),v=(0,i.useMemo)(()=>({switch:t,setSwitch:r,labelledby:o,describedby:c}),[t,r,o,c]);return i.createElement(m,{name:"Switch.Description"},i.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(l=v.switch)?void 0:l.id,onClick(e){t&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),t.click(),t.focus({preventScroll:!0}))}}},i.createElement(f.Provider,{value:v},(0,n.sY)({ourProps:{},theirProps:e,defaultTag:x,name:"Switch.Group"}))))},Label:a._,Description:d.d})}}]);