# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [default]
  pull_request:
    branches: [default]

jobs:
  test:
    name: Build & Test
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [10.x, 12.x, 14.x]
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: Cache node_modules
        id: cache-modules
        uses: actions/cache@v1
        with:
          path: node_modules
          key: ${{ matrix.node-version }}-${{ runner.OS }}-build-${{ hashFiles('package.json') }}
      - name: Install
        if: steps.cache-modules.outputs.cache-hit != 'true'
        run: npm install
      - name: Type Check
        run: npm run check --if-present
      - name: Test ES
        if: ${{!startsWith(matrix.node-version, '10.')}}
        run: npm run test:es --if-present
      - name: Test CommonJS
        run: npm run test:cjs --if-present
      - name: Test React Native
        run: npm run test:react-native --if-present
  validate-types:
    name: Check type use
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-package: ['ts', 'esm', 'cjs']
    steps:
      - uses: actions/checkout@v2
      - name: Cache node_modules
        id: cache-modules
        uses: actions/cache@v1
        with:
          path: node_modules
          key: 12.x-${{ runner.OS }}-build-${{ hashFiles('package.json') }}
      - name: Install
        if: steps.cache-modules.outputs.cache-hit != 'true'
        run: npm install
      - name: Test ${{ matrix.test-package }}
        run: npm run test:types:${{matrix.test-package}}
  publish:
    name: Publish
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/default'
    steps:
      - uses: actions/checkout@v2
      - name: Cache node_modules
        id: cache-modules
        uses: actions/cache@v1
        with:
          path: node_modules
          key: 12.x-${{ runner.OS }}-build-${{ hashFiles('package.json') }}
      - name: Install
        if: steps.cache-modules.outputs.cache-hit != 'true'
        run: npm install
      - name: Test
        run: npm test

      - name: Publish
        uses: mikeal/merge-release@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
