"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6760],{56760:function(e,t,u){u.r(t);var a=u(85893),n=u(71421),r=u(75814),s=u(47504);t.default=()=>{let{mutate:e,isLoading:t}=(0,s.l8)(),{data:u}=(0,r.X9)(),{closeModal:o}=(0,r.SO)();return(0,a.jsx)(n.Z,{onCancel:o,onDelete:function(){e({id:u}),o()},deleteBtnLoading:t})}},47504:function(e,t,u){u.d(t,{Ei:function(){return useCategoriesQuery},Im:function(){return useCategoryQuery},m7:function(){return useCreateCategoryMutation},l8:function(){return useDeleteCategoryMutation},pi:function(){return useUpdateCategoryMutation}});var a=u(11163),n=u.n(a),r=u(88767),s=u(22920),o=u(5233),i=u(97514),c=u(47869),l=u(28597),d=u(55191),g=u(3737);let y={...(0,d.h)(c.P.CATEGORIES),paginated:e=>{let{type:t,name:u,self:a,...n}=e;return g.eN.get(c.P.CATEGORIES,{searchJoin:"and",self:a,...n,search:g.eN.formatSearchParams({type:t,name:u})})}};var C=u(93345);let useCreateCategoryMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,o.$G)();return(0,r.useMutation)(y.create,{onSuccess:()=>{n().push(i.Z.category.list,void 0,{locale:C.Config.defaultLanguage}),s.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.CATEGORIES)}})},useDeleteCategoryMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,o.$G)();return(0,r.useMutation)(y.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.CATEGORIES)}})},useUpdateCategoryMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useRouter)(),u=(0,r.useQueryClient)();return(0,r.useMutation)(y.update,{onSuccess:async u=>{let a=t.query.shop?"/".concat(t.query.shop).concat(i.Z.category.list):i.Z.category.list;await t.push("".concat(a,"/").concat(null==u?void 0:u.slug,"/edit"),void 0,{locale:C.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(c.P.CATEGORIES)}})},useCategoryQuery=e=>{let{slug:t,language:u}=e,{data:a,error:n,isLoading:s}=(0,r.useQuery)([c.P.CATEGORIES,{slug:t,language:u}],()=>y.get({slug:t,language:u}));return{category:a,error:n,isLoading:s}},useCategoriesQuery=e=>{var t;let{data:u,error:a,isLoading:n}=(0,r.useQuery)([c.P.CATEGORIES,e],e=>{let{queryKey:t,pageParam:u}=e;return y.paginated(Object.assign({},t[1],u))},{keepPreviousData:!0});return{categories:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(u),error:a,loading:n}}}}]);