"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_products_details_related-products_tsx"],{

/***/ "./src/components/products/cards/card.tsx":
/*!************************************************!*\
  !*** ./src/components/products/cards/card.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Helium = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_helium_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/helium */ \"./src/components/products/cards/helium.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/helium\"\n        ]\n    }\n});\n_c1 = Helium;\nconst Neon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c2 = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_neon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/neon */ \"./src/components/products/cards/neon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/neon\"\n        ]\n    }\n}); // grocery-two\n_c3 = Neon;\nconst Argon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c4 = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_argon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/argon */ \"./src/components/products/cards/argon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/argon\"\n        ]\n    }\n}); // bakery\n_c5 = Argon;\nconst Krypton = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c6 = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_krypton_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/krypton */ \"./src/components/products/cards/krypton.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/krypton\"\n        ]\n    }\n});\n_c7 = Krypton;\nconst Xenon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c8 = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_xenon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/xenon */ \"./src/components/products/cards/xenon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/xenon\"\n        ]\n    }\n}); // furniture-two\n_c9 = Xenon;\nconst Radon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(_c10 = ()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_radon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/radon */ \"./src/components/products/cards/radon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/radon\"\n        ]\n    }\n}); // Book\n_c11 = Radon;\nconst MAP_PRODUCT_TO_CARD = {\n    neon: Neon,\n    helium: Helium,\n    argon: Argon,\n    krypton: Krypton,\n    xenon: Xenon,\n    radon: Radon\n};\nconst ProductCard = (param)=>{\n    let { product, className, ...props } = param;\n    var _product_type_settings, _product_type, _product_type_settings1, _product_type1;\n    const Component = (product === null || product === void 0 ? void 0 : (_product_type = product.type) === null || _product_type === void 0 ? void 0 : (_product_type_settings = _product_type.settings) === null || _product_type_settings === void 0 ? void 0 : _product_type_settings.productCard) ? MAP_PRODUCT_TO_CARD[product === null || product === void 0 ? void 0 : (_product_type1 = product.type) === null || _product_type1 === void 0 ? void 0 : (_product_type_settings1 = _product_type1.settings) === null || _product_type_settings1 === void 0 ? void 0 : _product_type_settings1.productCard] : Helium;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        product: product,\n        ...props,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 10\n    }, undefined);\n};\n_c12 = ProductCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductCard);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12;\n$RefreshReg$(_c, \"Helium$dynamic\");\n$RefreshReg$(_c1, \"Helium\");\n$RefreshReg$(_c2, \"Neon$dynamic\");\n$RefreshReg$(_c3, \"Neon\");\n$RefreshReg$(_c4, \"Argon$dynamic\");\n$RefreshReg$(_c5, \"Argon\");\n$RefreshReg$(_c6, \"Krypton$dynamic\");\n$RefreshReg$(_c7, \"Krypton\");\n$RefreshReg$(_c8, \"Xenon$dynamic\");\n$RefreshReg$(_c9, \"Xenon\");\n$RefreshReg$(_c10, \"Radon$dynamic\");\n$RefreshReg$(_c11, \"Radon\");\n$RefreshReg$(_c12, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/cards/card.tsx\n"));

/***/ }),

/***/ "./src/components/products/details/related-products.tsx":
/*!**************************************************************!*\
  !*** ./src/components/products/details/related-products.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _cards_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cards/card */ \"./src/components/products/cards/card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst RelatedProducts = (param)=>{\n    let { products, currentProductId, gridClassName } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-6 text-lg font-semibold tracking-tight text-heading\",\n                children: t(\"text-related-products\")\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4\", gridClassName),\n                children: products === null || products === void 0 ? void 0 : products.map((item, idx)=>{\n                    var _item_type;\n                    if (currentProductId === item.id) {\n                        return null;\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_cards_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        product: item,\n                        cardType: item === null || item === void 0 ? void 0 : (_item_type = item.type) === null || _item_type === void 0 ? void 0 : _item_type.slug\n                    }, idx, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(RelatedProducts, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = RelatedProducts;\n// <motion.div key={idx}>\n{\n/* {renderProductCard(\r\n    item,\r\n    \"!shadow-none border border-border-200 hover:!border-border-200 border-opacity-70\"\r\n  )} */ }// </motion.div>\n/* harmony default export */ __webpack_exports__[\"default\"] = (RelatedProducts);\nvar _c;\n$RefreshReg$(_c, \"RelatedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/details/related-products.tsx\n"));

/***/ })

}]);