!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).TextEncoding={})}(this,function(t){"use strict";var s="utf-8";function a(t,e){if(void 0===e&&(e=void 0),t)throw TypeError("Decoder error");return e||65533}function o(t){throw TypeError("The code point "+t+" could not be encoded.")}function u(t){var e=String(t).trim().toLowerCase();return e in i?i[e]:null}var e=[{encodings:[{labels:["unicode-1-1-utf-8","utf-8","utf8"],name:"UTF-8"}],heading:"The Encoding"},{encodings:[{labels:["866","cp866","csibm866","ibm866"],name:"IBM866"},{labels:["csisolatin2","iso-8859-2","iso-ir-101","iso8859-2","iso88592","iso_8859-2","iso_8859-2:1987","l2","latin2"],name:"ISO-8859-2"},{labels:["csisolatin3","iso-8859-3","iso-ir-109","iso8859-3","iso88593","iso_8859-3","iso_8859-3:1988","l3","latin3"],name:"ISO-8859-3"},{labels:["csisolatin4","iso-8859-4","iso-ir-110","iso8859-4","iso88594","iso_8859-4","iso_8859-4:1988","l4","latin4"],name:"ISO-8859-4"},{labels:["csisolatincyrillic","cyrillic","iso-8859-5","iso-ir-144","iso8859-5","iso88595","iso_8859-5","iso_8859-5:1988"],name:"ISO-8859-5"},{labels:["arabic","asmo-708","csiso88596e","csiso88596i","csisolatinarabic","ecma-114","iso-8859-6","iso-8859-6-e","iso-8859-6-i","iso-ir-127","iso8859-6","iso88596","iso_8859-6","iso_8859-6:1987"],name:"ISO-8859-6"},{labels:["csisolatingreek","ecma-118","elot_928","greek","greek8","iso-8859-7","iso-ir-126","iso8859-7","iso88597","iso_8859-7","iso_8859-7:1987","sun_eu_greek"],name:"ISO-8859-7"},{labels:["csiso88598e","csisolatinhebrew","hebrew","iso-8859-8","iso-8859-8-e","iso-ir-138","iso8859-8","iso88598","iso_8859-8","iso_8859-8:1988","visual"],name:"ISO-8859-8"},{labels:["csiso88598i","iso-8859-8-i","logical"],name:"ISO-8859-8-I"},{labels:["csisolatin6","iso-8859-10","iso-ir-157","iso8859-10","iso885910","l6","latin6"],name:"ISO-8859-10"},{labels:["iso-8859-13","iso8859-13","iso885913"],name:"ISO-8859-13"},{labels:["iso-8859-14","iso8859-14","iso885914"],name:"ISO-8859-14"},{labels:["csisolatin9","iso-8859-15","iso8859-15","iso885915","iso_8859-15","l9"],name:"ISO-8859-15"},{labels:["iso-8859-16"],name:"ISO-8859-16"},{labels:["cskoi8r","koi","koi8","koi8-r","koi8_r"],name:"KOI8-R"},{labels:["koi8-ru","koi8-u"],name:"KOI8-U"},{labels:["csmacintosh","mac","macintosh","x-mac-roman"],name:"macintosh"},{labels:["dos-874","iso-8859-11","iso8859-11","iso885911","tis-620","windows-874"],name:"windows-874"},{labels:["cp1250","windows-1250","x-cp1250"],name:"windows-1250"},{labels:["cp1251","windows-1251","x-cp1251"],name:"windows-1251"},{labels:["ansi_x3.4-1968","cp1252","cp819","ibm819","iso-ir-100","windows-1252","x-cp1252"],name:"windows-1252"},{labels:["ascii","us-ascii","iso-8859-1","iso8859-1","iso88591","iso_8859-1","iso_8859-1:1987","l1","latin1","csisolatin1"],name:"iso-8859-1"},{labels:["cp1253","windows-1253","x-cp1253"],name:"windows-1253"},{labels:["cp1254","csisolatin5","iso-8859-9","iso-ir-148","iso8859-9","iso88599","iso_8859-9","iso_8859-9:1989","l5","latin5","windows-1254","x-cp1254"],name:"windows-1254"},{labels:["cp1255","windows-1255","x-cp1255"],name:"windows-1255"},{labels:["cp1256","windows-1256","x-cp1256"],name:"windows-1256"},{labels:["cp1257","windows-1257","x-cp1257"],name:"windows-1257"},{labels:["cp1258","windows-1258","x-cp1258"],name:"windows-1258"},{labels:["x-mac-cyrillic","x-mac-ukrainian"],name:"x-mac-cyrillic"}],heading:"Legacy single-byte encodings"},{encodings:[{labels:["chinese","csgb2312","csiso58gb231280","gb2312","gb_2312","gb_2312-80","gbk","iso-ir-58","x-gbk"],name:"GBK"},{labels:["gb18030"],name:"gb18030"}],heading:"Legacy multi-byte Chinese (simplified) encodings"},{encodings:[{labels:["big5","big5-hkscs","cn-big5","csbig5","x-x-big5"],name:"Big5"}],heading:"Legacy multi-byte Chinese (traditional) encodings"},{encodings:[{labels:["cseucpkdfmtjapanese","euc-jp","x-euc-jp"],name:"EUC-JP"},{labels:["csiso2022jp","iso-2022-jp"],name:"ISO-2022-JP"},{labels:["csshiftjis","ms932","ms_kanji","shift-jis","shift_jis","sjis","windows-31j","x-sjis"],name:"Shift_JIS"}],heading:"Legacy multi-byte Japanese encodings"},{encodings:[{labels:["cseuckr","csksc56011987","euc-kr","iso-ir-149","korean","ks_c_5601-1987","ks_c_5601-1989","ksc5601","ksc_5601","windows-949"],name:"EUC-KR"}],heading:"Legacy multi-byte Korean encodings"},{encodings:[{labels:["csiso2022kr","hz-gb-2312","iso-2022-cn","iso-2022-cn-ext","iso-2022-kr"],name:"replacement"},{labels:["utf-16be"],name:"UTF-16BE"},{labels:["utf-16","utf-16le"],name:"UTF-16LE"},{labels:["x-user-defined"],name:"x-user-defined"}],heading:"Legacy miscellaneous encodings"}],i={};e.forEach(function(t){t.encodings.forEach(function(e){e.labels.forEach(function(t){i[t]=e})})});var n,l,f,h=-1;function d(t){return Array.isArray(t)?t:[t]}function c(t,e,i){return e<=t&&t<=i}function _(t){if(null==t)return{};if(t===Object(t))return t;throw TypeError("Could not convert argument to dictionary")}function p(){return"undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0}function r(){if(n)return n;var t=function(){if("undefined"!=typeof TextEncodingIndexes)return TextEncodingIndexes.encodingIndexes;var t=p();return t?"TextEncodingIndexes"in t?global.TextEncodingIndexes.encodingIndexes:"encoding-indexes"in t?global.encodingIndexes:null:null}();return t?n=t:null}function g(t,e){return e&&e[t]||null}function b(t,e){var i=e.indexOf(t);return-1===i?null:i}function y(t){var e=r();if(!e)throw Error("Indexes missing. Did you forget to include encoding-indexes.js first?");return e[t]}function w(t){return 0<=t&&t<=127}var m=w,j=-1,v=(S.prototype.handler=function(t,e){if(e===j&&0!==this.Big5_lead)return this.Big5_lead=0,a(this.fatal);if(e===j&&0===this.Big5_lead)return h;if(0===this.Big5_lead)return w(e)?e:c(e,129,254)?(this.Big5_lead=e,null):a(this.fatal);var i=this.Big5_lead,n=null;switch(this.Big5_lead=0,(c(e,64,126)||c(e,161,254))&&(n=157*(i-129)+(e-(e<127?64:98))),n){case 1133:return[202,772];case 1135:return[202,780];case 1164:return[234,772];case 1166:return[234,780]}var r=null===n?null:g(n,y("big5"));return null===r&&w(e)&&t.prepend(e),null===r?a(this.fatal):r},S);function S(t){this.fatal=t.fatal,this.Big5_lead=0}var I=(k.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;var i,n,r=(i=e,n=f=f||y("big5").map(function(t,e){return e<5024?null:t}),9552===i||9566===i||9569===i||9578===i||21313===i||21317===i?n.lastIndexOf(i):b(i,n));if(null===r)return o(e);var s=Math.floor(r/157)+129;if(s<161)return o(e);var a=r%157;return[s,a+(a<63?64:98)]},k);function k(t){this.fatal=t.fatal}var x=(E.prototype.handler=function(t,e){if(e===j&&0!==this.eucjp_lead)return this.eucjp_lead=0,a(this.fatal);if(e===j&&0===this.eucjp_lead)return h;if(142===this.eucjp_lead&&c(e,161,223))return this.eucjp_lead=0,65216+e;if(143===this.eucjp_lead&&c(e,161,254))return this.eucjp_jis0212_flag=!0,this.eucjp_lead=e,null;if(0===this.eucjp_lead)return w(e)?e:142===e||143===e||c(e,161,254)?(this.eucjp_lead=e,null):a(this.fatal);var i=this.eucjp_lead;this.eucjp_lead=0;var n=null;return c(i,161,254)&&c(e,161,254)&&(n=g(94*(i-161)+(e-161),y(this.eucjp_jis0212_flag?"jis0212":"jis0208"))),this.eucjp_jis0212_flag=!1,c(e,161,254)||t.prepend(e),null===n?a(this.fatal):n},E);function E(t){this.fatal=t.fatal,this.eucjp_jis0212_flag=!1,this.eucjp_lead=0}var B=(O.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;if(165===e)return 92;if(8254===e)return 126;if(c(e,65377,65439))return[142,e-65377+161];8722===e&&(e=65293);var i=b(e,y("jis0208"));return null===i?o(e):[Math.floor(i/94)+161,i%94+161]},O);function O(t){this.fatal=t.fatal}var A=(C.prototype.handler=function(t,e){if(e===j&&0!==this.euckr_lead)return this.euckr_lead=0,a(this.fatal);if(e===j&&0===this.euckr_lead)return h;if(0===this.euckr_lead)return w(e)?e:c(e,129,254)?(this.euckr_lead=e,null):a(this.fatal);var i=this.euckr_lead,n=null;this.euckr_lead=0,c(e,65,254)&&(n=190*(i-129)+(e-65));var r=null===n?null:g(n,y("euc-kr"));return null===n&&w(e)&&t.prepend(e),null===r?a(this.fatal):r},C);function C(t){this.fatal=t.fatal,this.euckr_lead=0}var T=(U.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;var i=b(e,y("euc-kr"));return null===i?o(e):[Math.floor(i/190)+129,i%190+65]},U);function U(t){this.fatal=t.fatal}var L=(M.prototype.handler=function(t,e){if(e===j&&0===this.gb18030_first&&0===this.gb18030_second&&0===this.gb18030_third)return h;var i;if(e!==j||0===this.gb18030_first&&0===this.gb18030_second&&0===this.gb18030_third||(this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0,a(this.fatal)),0!==this.gb18030_third){i=null,c(e,48,57)&&(i=function(t){if(39419<t&&t<189e3||1237575<t)return null;if(7457===t)return 59335;for(var e=0,i=0,n=y("gb18030-ranges"),r=0;r<n.length;++r){var s=d(n[r]);if(!(s[0]<=t))break;e=s[0],i=s[1]}return i+t-e}(10*(126*(10*(this.gb18030_first-129)+this.gb18030_second-48)+this.gb18030_third-129)+e-48));var n=[this.gb18030_second,this.gb18030_third,e];return this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0,null===i?(t.prepend(n),a(this.fatal)):i}if(0!==this.gb18030_second)return c(e,129,254)?(this.gb18030_third=e,null):(t.prepend([this.gb18030_second,e]),this.gb18030_first=0,this.gb18030_second=0,a(this.fatal));if(0===this.gb18030_first)return w(e)?e:128===e?8364:c(e,129,254)?(this.gb18030_first=e,null):a(this.fatal);if(c(e,48,57))return this.gb18030_second=e,null;var r=this.gb18030_first,s=null;return this.gb18030_first=0,(c(e,64,126)||c(e,128,254))&&(s=190*(r-129)+(e-(e<127?64:65))),null===(i=null===s?null:g(s,y("gb18030")))&&w(e)&&t.prepend(e),null===i?a(this.fatal):i},M);function M(t){this.fatal=t.fatal,this.gb18030_first=0,this.gb18030_second=0,this.gb18030_third=0}var J,R,K=(F.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;if(58853===e)return o(e);if(this.gbk_flag&&8364===e)return 128;var i=b(e,y("gb18030"));if(null!==i){var n=i%190;return[Math.floor(i/190)+129,n+(n<63?64:65)]}if(this.gbk_flag)return o(e);i=function(t){if(59335===t)return 7457;for(var e=0,i=0,n=y("gb18030-ranges"),r=0;r<n.length;++r){var s=d(n[r]);if(!(s[1]<=t))break;e=s[1],i=s[0]}return i+t-e}(e);var r=Math.floor(i/10/126/10);i-=10*r*126*10;var s=Math.floor(i/10/126);i-=10*s*126;var a=Math.floor(i/10);return[r+129,s+48,a+129,i-10*a+48]},F);function F(t,e){void 0===e&&(e=void 0),this.gbk_flag=e,this.fatal=t.fatal}(R=J=J||{})[R.ASCII=0]="ASCII",R[R.Roman=1]="Roman",R[R.Katakana=2]="Katakana",R[R.LeadByte=3]="LeadByte",R[R.TrailByte=4]="TrailByte",R[R.EscapeStart=5]="EscapeStart",R[R.Escape=6]="Escape";var P,D,z=(G.prototype.handler=function(t,e){switch(this.iso2022jp_decoder_state){default:case J.ASCII:return 27===e?(this.iso2022jp_decoder_state=J.EscapeStart,null):c(e,0,127)&&14!==e&&15!==e&&27!==e?(this.iso2022jp_output_flag=!1,e):e===j?h:(this.iso2022jp_output_flag=!1,a(this.fatal));case J.Roman:return 27===e?(this.iso2022jp_decoder_state=J.EscapeStart,null):92===e?(this.iso2022jp_output_flag=!1,165):126===e?(this.iso2022jp_output_flag=!1,8254):c(e,0,127)&&14!==e&&15!==e&&27!==e&&92!==e&&126!==e?(this.iso2022jp_output_flag=!1,e):e===j?h:(this.iso2022jp_output_flag=!1,a(this.fatal));case J.Katakana:return 27===e?(this.iso2022jp_decoder_state=J.EscapeStart,null):c(e,33,95)?(this.iso2022jp_output_flag=!1,65344+e):e===j?h:(this.iso2022jp_output_flag=!1,a(this.fatal));case J.LeadByte:return 27===e?(this.iso2022jp_decoder_state=J.EscapeStart,null):c(e,33,126)?(this.iso2022jp_output_flag=!1,this.iso2022jp_lead=e,this.iso2022jp_decoder_state=J.TrailByte,null):e===j?h:(this.iso2022jp_output_flag=!1,a(this.fatal));case J.TrailByte:if(27===e)return this.iso2022jp_decoder_state=J.EscapeStart,a(this.fatal);if(c(e,33,126)){this.iso2022jp_decoder_state=J.LeadByte;var i=g(94*(this.iso2022jp_lead-33)+e-33,y("jis0208"));return null===i?a(this.fatal):i}return e===j?(this.iso2022jp_decoder_state=J.LeadByte,t.prepend(e)):this.iso2022jp_decoder_state=J.LeadByte,a(this.fatal);case J.EscapeStart:return 36===e||40===e?(this.iso2022jp_lead=e,this.iso2022jp_decoder_state=J.Escape,null):(t.prepend(e),this.iso2022jp_output_flag=!1,this.iso2022jp_decoder_state=this.iso2022jp_decoder_output_state,a(this.fatal));case J.Escape:var n=this.iso2022jp_lead;this.iso2022jp_lead=0;var r=null;if(40===n&&66===e&&(r=J.ASCII),40===n&&74===e&&(r=J.Roman),40===n&&73===e&&(r=J.Katakana),36!==n||64!==e&&66!==e||(r=J.LeadByte),null===r)return t.prepend([n,e]),this.iso2022jp_output_flag=!1,this.iso2022jp_decoder_state=this.iso2022jp_decoder_output_state,a(this.fatal);this.iso2022jp_decoder_state=this.iso2022jp_decoder_state=r;var s=this.iso2022jp_output_flag;return this.iso2022jp_output_flag=!0,s?a(this.fatal):null}},G);function G(t){this.fatal=t.fatal,this.iso2022jp_decoder_state=J.ASCII,this.iso2022jp_decoder_output_state=J.ASCII,this.iso2022jp_lead=0,this.iso2022jp_output_flag=!1}(D=P=P||{})[D.ASCII=0]="ASCII",D[D.Roman=1]="Roman",D[D.jis0208=2]="jis0208";var N=(q.prototype.handler=function(t,e){if(e===j&&this.iso2022jp_state!==P.ASCII)return t.prepend(e),this.iso2022jp_state=P.ASCII,[27,40,66];if(e===j&&this.iso2022jp_state===P.ASCII)return h;if(!(this.iso2022jp_state!==P.ASCII&&this.iso2022jp_state!==P.Roman||14!==e&&15!==e&&27!==e))return o(65533);if(this.iso2022jp_state===P.ASCII&&m(e))return e;if(this.iso2022jp_state===P.Roman&&(m(e)&&92!==e&&126!==e||165==e||8254==e)){if(m(e))return e;if(165===e)return 92;if(8254===e)return 126}if(m(e)&&this.iso2022jp_state!==P.ASCII)return t.prepend(e),this.iso2022jp_state=P.ASCII,[27,40,66];if((165===e||8254===e)&&this.iso2022jp_state!==P.Roman)return t.prepend(e),this.iso2022jp_state=P.Roman,[27,40,74];8722===e&&(e=65293);var i=b(e,y("jis0208"));return null===i?o(e):this.iso2022jp_state!==P.jis0208?(t.prepend(e),this.iso2022jp_state=P.jis0208,[27,36,66]):[Math.floor(i/94)+33,i%94+33]},q);function q(t){this.fatal=t.fatal,this.iso2022jp_state=P.ASCII}var H=(Q.prototype.handler=function(t,e){if(e===j&&0!==this.Shift_JIS_lead)return this.Shift_JIS_lead=0,a(this.fatal);if(e===j&&0===this.Shift_JIS_lead)return h;if(0===this.Shift_JIS_lead)return w(e)||128===e?e:c(e,161,223)?65216+e:c(e,129,159)||c(e,224,252)?(this.Shift_JIS_lead=e,null):a(this.fatal);var i=this.Shift_JIS_lead,n=null;if(this.Shift_JIS_lead=0,(c(e,64,126)||c(e,128,252))&&(n=188*(i-(i<160?129:193))+e-(e<127?64:65)),c(n,8836,10715))return 48508+n;var r=null===n?null:g(n,y("jis0208"));return null===r&&w(e)&&t.prepend(e),null===r?a(this.fatal):r},Q);function Q(t){this.fatal=t.fatal,this.Shift_JIS_lead=0}var V=(W.prototype.handler=function(t,e){if(e===j)return h;if(m(e)||128===e)return e;if(165===e)return 92;if(8254===e)return 126;if(c(e,65377,65439))return e-65377+161;8722===e&&(e=65293);var i,n=(i=e,(l=l||y("jis0208").map(function(t,e){return c(e,8272,8835)?null:t})).indexOf(i));if(null===n)return o(e);var r=Math.floor(n/188),s=n%188;return[r+(r<31?129:193),s+(s<63?64:65)]},W);function W(t){this.fatal=t.fatal}var X=(Y.prototype.handler=function(t,e){if(e===j)return h;if(w(e))return e;var i=this.index[e-128];return i||a(this.fatal)},Y);function Y(t,e){this.index=t,this.fatal=e.fatal}var Z=($.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;var i=b(e,this.index);return null===i&&o(e),i+128},$);function $(t,e){this.index=t,this.fatal=e.fatal}function tt(t,e){var i=t>>8,n=255&t;return e?[i,n]:[n,i]}var et=(it.prototype.handler=function(t,e){if(e===j&&(null!==this.utf16_lead_byte||null!==this.utf16_lead_surrogate))return a(this.fatal);if(e===j&&null===this.utf16_lead_byte&&null===this.utf16_lead_surrogate)return h;if(null===this.utf16_lead_byte)return this.utf16_lead_byte=e,null;var i=this.utf16_be?(this.utf16_lead_byte<<8)+e:(e<<8)+this.utf16_lead_byte;if((this.utf16_lead_byte=null)===this.utf16_lead_surrogate)return c(i,55296,56319)?(this.utf16_lead_surrogate=i,null):c(i,56320,57343)?a(this.fatal):i;var n=this.utf16_lead_surrogate;return this.utf16_lead_surrogate=null,c(i,56320,57343)?65536+1024*(n-55296)+(i-56320):(t.prepend(tt(i,this.utf16_be)),a(this.fatal))},it);function it(t,e){this.utf16_be=t,this.fatal=e.fatal,this.utf16_lead_byte=null,this.utf16_lead_surrogate=null}var nt=(rt.prototype.handler=function(t,e){if(e===j)return h;if(c(e,0,65535))return tt(e,this.utf16_be);var i=tt(55296+(e-65536>>10),this.utf16_be),n=tt(56320+(e-65536&1023),this.utf16_be);return i.concat(n)},rt);function rt(t,e){this.utf16_be=t,this.fatal=e.fatal}var st=(at.prototype.handler=function(t,e){if(e===j&&0!==this.utf8_bytes_needed)return this.utf8_bytes_needed=0,a(this.fatal);if(e===j)return h;if(0===this.utf8_bytes_needed){if(c(e,0,127))return e;if(c(e,194,223))this.utf8_bytes_needed=1,this.utf8_code_point=31&e;else if(c(e,224,239))224===e&&(this.utf8_lower_boundary=160),237===e&&(this.utf8_upper_boundary=159),this.utf8_bytes_needed=2,this.utf8_code_point=15&e;else{if(!c(e,240,244))return a(this.fatal);240===e&&(this.utf8_lower_boundary=144),244===e&&(this.utf8_upper_boundary=143),this.utf8_bytes_needed=3,this.utf8_code_point=7&e}return null}if(!c(e,this.utf8_lower_boundary,this.utf8_upper_boundary))return this.utf8_code_point=this.utf8_bytes_needed=this.utf8_bytes_seen=0,this.utf8_lower_boundary=128,this.utf8_upper_boundary=191,t.prepend(e),a(this.fatal);if(this.utf8_lower_boundary=128,this.utf8_upper_boundary=191,this.utf8_code_point=this.utf8_code_point<<6|63&e,this.utf8_bytes_seen+=1,this.utf8_bytes_seen!==this.utf8_bytes_needed)return null;var i=this.utf8_code_point;return this.utf8_code_point=this.utf8_bytes_needed=this.utf8_bytes_seen=0,i},at);function at(t){this.fatal=t.fatal,this.utf8_code_point=0,this.utf8_bytes_seen=0,this.utf8_bytes_needed=0,this.utf8_lower_boundary=128,this.utf8_upper_boundary=191}var ot=(ut.prototype.handler=function(t,e){if(e===j)return h;if(m(e))return e;var i,n;c(e,128,2047)?(i=1,n=192):c(e,2048,65535)?(i=2,n=224):c(e,65536,1114111)&&(i=3,n=240);for(var r=[(e>>6*i)+n];0<i;){var s=e>>6*(i-1);r.push(128|63&s),--i}return r},ut);function ut(t){this.fatal=t.fatal}var lt=(ft.prototype.handler=function(t,e){return e===j?h:w(e)?e:63360+e-128},ft);function ft(t){this.fatal=t.fatal}var ht=(dt.prototype.handler=function(t,e){return e===j?h:m(e)?e:c(e,63360,63487)?e-63360+128:o(e)},dt);function dt(t){this.fatal=t.fatal}var ct=r(),_t={"UTF-8":function(t){return new ot(t)},GBK:function(t){return new K(t,!0)},gb18030:function(t){return new K(t)},Big5:function(t){return new I(t)},"EUC-JP":function(t){return new B(t)},"ISO-2022-JP":function(t){return new N(t)},Shift_JIS:function(t){return new V(t)},"EUC-KR":function(t){return new T(t)},"UTF-16BE":function(t){return new nt(!0,t)},"UTF-16LE":function(t){return new nt(!1,t)},"x-user-defined":function(t){return new ht(t)}},pt={"UTF-8":function(t){return new st(t)},GBK:function(t){return new L(t)},gb18030:function(t){return new L(t)},Big5:function(t){return new v(t)},"EUC-JP":function(t){return new x(t)},"ISO-2022-JP":function(t){return new z(t)},Shift_JIS:function(t){return new H(t)},"EUC-KR":function(t){return new A(t)},"UTF-16BE":function(t){return new et(!0,t)},"UTF-16LE":function(t){return new et(!1,t)},"x-user-defined":function(t){return new lt(t)}};ct&&e.forEach(function(t){"Legacy single-byte encodings"===t.heading&&t.encodings.forEach(function(t){var e=t.name,i=y(e.toLowerCase());pt[e]=function(t){return new X(i,t)},_t[e]=function(t){return new Z(i,t)}})});var gt=(bt.prototype.endOfStream=function(){return!this.tokens.length},bt.prototype.read=function(){return this.tokens.length?this.tokens.pop():j},bt.prototype.prepend=function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.push(e.pop());else this.tokens.push(t)},bt.prototype.push=function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.unshift(e.shift());else this.tokens.unshift(t)},bt);function bt(t){this.tokens=Array.from(t),this.tokens.reverse()}var yt=(Object.defineProperty(wt.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()},enumerable:!0,configurable:!0}),Object.defineProperty(wt.prototype,"fatal",{get:function(){return"fatal"===this._error_mode},enumerable:!0,configurable:!0}),Object.defineProperty(wt.prototype,"ignoreBOM",{get:function(){return this._ignoreBOM},enumerable:!0,configurable:!0}),wt.prototype.decode=function(t,e){var i=function(t){return"object"!=typeof t?new Uint8Array(0):mt(t)?new Uint8Array(t):"buffer"in t&&mt(t.buffer)?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):new Uint8Array(0)}(t),n=_(e);this._do_not_flush||(this._decoder=pt[this._encoding.name]({fatal:"fatal"===this._error_mode}),this._BOMseen=!1),this._do_not_flush=Boolean(n.stream);for(var r,s=new gt(i),a=[];;){var o=s.read();if(o===j)break;if((r=this._decoder.handler(s,o))===h)break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}if(!this._do_not_flush){do{if((r=this._decoder.handler(s,s.read()))===h)break;r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}while(!s.endOfStream());this._decoder=null}return this.serializeStream(a)},wt.prototype.serializeStream=function(t){var e,i;return e=["UTF-8","UTF-16LE","UTF-16BE"],i=this._encoding.name,-1===e.indexOf(i)||this._ignoreBOM||this._BOMseen||(0<t.length&&65279===t[0]?(this._BOMseen=!0,t.shift()):0<t.length&&(this._BOMseen=!0)),function(t){for(var e="",i=0;i<t.length;++i){var n=t[i];n<=65535?e+=String.fromCharCode(n):(n-=65536,e+=String.fromCharCode(55296+(n>>10),56320+(1023&n)))}return e}(t)},wt);function wt(t,e){t=void 0!==t?String(t):s;var i=_(e);this._encoding=null,this._decoder=null,this._ignoreBOM=!1,this._BOMseen=!1,this._error_mode="replacement",this._do_not_flush=!1;var n=u(t);if(null===n||"replacement"===n.name)throw RangeError("Unknown encoding: "+t);if(!pt[n.name])throw Error("Decoder not present. Did you forget to include encoding-indexes.js first?");this._encoding=n,Boolean(i.fatal)&&(this._error_mode="fatal"),Boolean(i.ignoreBOM)&&(this._ignoreBOM=!0)}function mt(t){try{return t instanceof ArrayBuffer}catch(t){return void console.error(t)}}var jt,vt=(Object.defineProperty(St.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()},enumerable:!0,configurable:!0}),St.prototype.encode=function(t,e){t=void 0===t?"":String(t);var i=_(e);this._do_not_flush||(this._encoder=_t[this._encoding.name]({fatal:"fatal"===this._fatal})),this._do_not_flush=Boolean(i.stream);for(var n,r=new gt(function(t){for(var e=String(t),i=e.length,n=0,r=[];n<i;){var s,a,o,u=e.charCodeAt(n);u<55296||57343<u?r.push(u):56320<=u&&u<=57343?r.push(65533):55296<=u&&u<=56319&&(n!==i-1&&56320<=(s=e.charCodeAt(n+1))&&s<=57343?(a=1023&u,o=1023&s,r.push(65536+(a<<10)+o),n+=1):r.push(65533)),n+=1}return r}(t)),s=[];;){var a=r.read();if(a===j)break;if((n=this._encoder.handler(r,a))===h)break;Array.isArray(n)?s.push.apply(s,n):s.push(n)}if(!this._do_not_flush){for(;(n=this._encoder.handler(r,r.read()))!==h;)Array.isArray(n)?s.push.apply(s,n):s.push(n);this._encoder=null}return new Uint8Array(s)},St);function St(t,e){var i=_(e);if(this._encoding=null,this._encoder=null,this._do_not_flush=!1,this._fatal=Boolean(i.fatal)?"fatal":"replacement",Boolean(i.NONSTANDARD_allowLegacyEncoding)){var n=u(t=t?String(t):s);if(null===n||"replacement"===n.name)throw RangeError("Unknown encoding: "+t);if(!_t[n.name])throw Error("Encoder not present. Did you forget to include encoding-indexes.js first?");this._encoding=n}else{this._encoding=u("utf-8");var r=p()||{};void 0!==t&&"console"in r&&console.warn("TextEncoder constructor called with encoding label, which is ignored.")}}"undefined"!=typeof window&&((jt=function(t){return!(t in window)||void 0===window[t]||null===window[t]})("TextDecoder")&&(window.TextDecoder=yt),jt("TextEncoder")&&(window.TextEncoder=vt)),t.TextDecoder=yt,t.TextEncoder=vt,Object.defineProperty(t,"__esModule",{value:!0})});