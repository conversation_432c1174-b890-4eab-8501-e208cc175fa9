"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6203],{26203:function(e,a,t){t.r(a);var n=t(85893),s=t(71421),u=t(75814),l=t(80246);a.default=()=>{let{mutate:e,isLoading:a}=(0,l.in)(),{data:t}=(0,u.X9)(),{closeModal:o}=(0,u.SO)();return(0,n.jsx)(s.Z,{onCancel:o,onDelete:function(){e({id:t}),o()},deleteBtnLoading:a})}},80246:function(e,a,t){t.d(a,{Mv:function(){return useCreateFlashSaleMutation},in:function(){return useDeleteFlashSaleMutation},gr:function(){return useFlashSaleQuery},AX:function(){return useFlashSalesQuery},Aq:function(){return useProductFlashSaleInfo},yp:function(){return useUpdateFlashSaleMutation}});var n=t(11163),s=t.n(n),u=t(88767),l=t(22920),o=t(5233),r=t(28597),i=t(97514),c=t(47869),d=t(93345),S=t(55191),h=t(3737);let f={...(0,S.h)(c.P.FLASH_SALE),all:function(){let{title:e,shop_id:a,...t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:a,...t,search:h.eN.formatSearchParams({title:e,shop_id:a})})},get(e){let{slug:a,language:t,shop_id:n}=e;return h.eN.get("".concat(c.P.FLASH_SALE,"/").concat(a),{language:t,shop_id:n,slug:a,with:"products"})},paginated:e=>{let{title:a,shop_id:t,...n}=e;return h.eN.get(c.P.FLASH_SALE,{searchJoin:"and",shop_id:t,...n,search:h.eN.formatSearchParams({title:a,shop_id:t})})},approve:e=>h.eN.post(c.P.FLASH_SALE,e),disapprove:e=>h.eN.post(c.P.FLASH_SALE,e),getFlashSaleInfoByProductID(e){let{id:a,language:t}=e;return h.eN.get(c.P.PRODUCT_FLASH_SALE_INFO,{searchJoin:"and",id:a,language:t,with:"flash_sales"})}},useFlashSaleQuery=e=>{let{slug:a,language:t,shop_id:n}=e,{data:s,error:l,isLoading:o}=(0,u.useQuery)([c.P.FLASH_SALE,{slug:a,language:t,shop_id:n}],()=>f.get({slug:a,language:t,shop_id:n}));return{flashSale:s,error:l,loading:o}},useFlashSalesQuery=e=>{var a;let{data:t,error:n,isLoading:s}=(0,u.useQuery)([c.P.FLASH_SALE,e],e=>{let{queryKey:a,pageParam:t}=e;return f.paginated(Object.assign({},a[1],t))},{keepPreviousData:!0});return{flashSale:null!==(a=null==t?void 0:t.data)&&void 0!==a?a:[],paginatorInfo:(0,r.Q)(t),error:n,loading:s}},useCreateFlashSaleMutation=()=>{let e=(0,u.useQueryClient)(),a=(0,n.useRouter)(),{t}=(0,o.$G)();return(0,u.useMutation)(f.create,{onSuccess:async()=>{let e=a.query.shop?"/".concat(a.query.shop).concat(i.Z.flashSale.list):i.Z.flashSale.list;await s().push(e,void 0,{locale:d.Config.defaultLanguage}),l.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var a;l.Am.error(t("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}})},useUpdateFlashSaleMutation=()=>{let{t:e}=(0,o.$G)(),a=(0,u.useQueryClient)(),t=(0,n.useRouter)();return(0,u.useMutation)(f.update,{onSuccess:async a=>{let n=t.query.shop?"/".concat(t.query.shop).concat(i.Z.flashSale.list):i.Z.flashSale.list;await t.push(n,void 0,{locale:d.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{a.invalidateQueries(c.P.FLASH_SALE)},onError:a=>{var t;l.Am.error(e("common:".concat(null==a?void 0:null===(t=a.response)||void 0===t?void 0:t.data.message)))}})},useDeleteFlashSaleMutation=()=>{let e=(0,u.useQueryClient)(),{t:a}=(0,o.$G)();return(0,u.useMutation)(f.delete,{onSuccess:()=>{l.Am.success(a("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.FLASH_SALE)},onError:e=>{var t;l.Am.error(a("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useProductFlashSaleInfo=e=>{let{id:a,language:t}=e,{data:n,error:s,isLoading:l}=(0,u.useQuery)([c.P.PRODUCT_FLASH_SALE_INFO,{id:a,language:t}],()=>f.getFlashSaleInfoByProductID({id:a,language:t}));return{flashSaleInfo:n,error:s,loading:l}}}}]);