"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use";
exports.ids = ["vendor-chunks/react-use"];
exports.modules = {

/***/ "./node_modules/react-use/esm/misc/util.js":
/*!*************************************************!*\
  !*** ./node_modules/react-use/esm/misc/util.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isNavigator: () => (/* binding */ isNavigator),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   off: () => (/* binding */ off),\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nvar noop = function () { };\nfunction on(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.addEventListener) {\n        obj.addEventListener.apply(obj, args);\n    }\n}\nfunction off(obj) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (obj && obj.removeEventListener) {\n        obj.removeEventListener.apply(obj, args);\n    }\n}\nvar isBrowser = typeof window !== 'undefined';\nvar isNavigator = typeof navigator !== 'undefined';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS9taXNjL3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTztBQUNBO0FBQ1A7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxxQkFBcUIsdUJBQXVCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS9taXNjL3V0aWwuanM/NmIyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIG5vb3AgPSBmdW5jdGlvbiAoKSB7IH07XG5leHBvcnQgZnVuY3Rpb24gb24ob2JqKSB7XG4gICAgdmFyIGFyZ3MgPSBbXTtcbiAgICBmb3IgKHZhciBfaSA9IDE7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICBhcmdzW19pIC0gMV0gPSBhcmd1bWVudHNbX2ldO1xuICAgIH1cbiAgICBpZiAob2JqICYmIG9iai5hZGRFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIG9iai5hZGRFdmVudExpc3RlbmVyLmFwcGx5KG9iaiwgYXJncyk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIG9mZihvYmopIHtcbiAgICB2YXIgYXJncyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMTsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIGFyZ3NbX2kgLSAxXSA9IGFyZ3VtZW50c1tfaV07XG4gICAgfVxuICAgIGlmIChvYmogJiYgb2JqLnJlbW92ZUV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgb2JqLnJlbW92ZUV2ZW50TGlzdGVuZXIuYXBwbHkob2JqLCBhcmdzKTtcbiAgICB9XG59XG5leHBvcnQgdmFyIGlzQnJvd3NlciA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnO1xuZXhwb3J0IHZhciBpc05hdmlnYXRvciA9IHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/misc/util.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useCopyToClipboard.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-use/esm/useCopyToClipboard.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! copy-to-clipboard */ \"copy-to-clipboard\");\n/* harmony import */ var copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useMountedState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useMountedState */ \"./node_modules/react-use/esm/useMountedState.js\");\n/* harmony import */ var _useSetState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useSetState */ \"./node_modules/react-use/esm/useSetState.js\");\n\n\n\n\nvar useCopyToClipboard = function () {\n    var isMounted = (0,_useMountedState__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    var _a = (0,_useSetState__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        value: undefined,\n        error: undefined,\n        noUserInteraction: true,\n    }), state = _a[0], setState = _a[1];\n    var copyToClipboard = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (value) {\n        if (!isMounted()) {\n            return;\n        }\n        var noUserInteraction;\n        var normalizedValue;\n        try {\n            // only strings and numbers casted to strings can be copied to clipboard\n            if (typeof value !== 'string' && typeof value !== 'number') {\n                var error = new Error(\"Cannot copy typeof \" + typeof value + \" to clipboard, must be a string\");\n                if (true)\n                    console.error(error);\n                setState({\n                    value: value,\n                    error: error,\n                    noUserInteraction: true,\n                });\n                return;\n            }\n            // empty strings are also considered invalid\n            else if (value === '') {\n                var error = new Error(\"Cannot copy empty string to clipboard.\");\n                if (true)\n                    console.error(error);\n                setState({\n                    value: value,\n                    error: error,\n                    noUserInteraction: true,\n                });\n                return;\n            }\n            normalizedValue = value.toString();\n            noUserInteraction = copy_to_clipboard__WEBPACK_IMPORTED_MODULE_0___default()(normalizedValue);\n            setState({\n                value: normalizedValue,\n                error: undefined,\n                noUserInteraction: noUserInteraction,\n            });\n        }\n        catch (error) {\n            setState({\n                value: normalizedValue,\n                error: error,\n                noUserInteraction: noUserInteraction,\n            });\n        }\n    }, []);\n    return [state, copyToClipboard];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCopyToClipboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VDb3B5VG9DbGlwYm9hcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQztBQUNOO0FBQ1k7QUFDUjtBQUN4QztBQUNBLG9CQUFvQiw0REFBZTtBQUNuQyxhQUFhLHdEQUFXO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCwwQkFBMEIsa0RBQVc7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLElBQXNDO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixJQUFzQztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyx3REFBUztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGlFQUFlLGtCQUFrQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vdXNlQ29weVRvQ2xpcGJvYXJkLmpzP2VhOWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHdyaXRlVGV4dCBmcm9tICdjb3B5LXRvLWNsaXBib2FyZCc7XG5pbXBvcnQgeyB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB1c2VNb3VudGVkU3RhdGUgZnJvbSAnLi91c2VNb3VudGVkU3RhdGUnO1xuaW1wb3J0IHVzZVNldFN0YXRlIGZyb20gJy4vdXNlU2V0U3RhdGUnO1xudmFyIHVzZUNvcHlUb0NsaXBib2FyZCA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgaXNNb3VudGVkID0gdXNlTW91bnRlZFN0YXRlKCk7XG4gICAgdmFyIF9hID0gdXNlU2V0U3RhdGUoe1xuICAgICAgICB2YWx1ZTogdW5kZWZpbmVkLFxuICAgICAgICBlcnJvcjogdW5kZWZpbmVkLFxuICAgICAgICBub1VzZXJJbnRlcmFjdGlvbjogdHJ1ZSxcbiAgICB9KSwgc3RhdGUgPSBfYVswXSwgc2V0U3RhdGUgPSBfYVsxXTtcbiAgICB2YXIgY29weVRvQ2xpcGJvYXJkID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKHZhbHVlKSB7XG4gICAgICAgIGlmICghaXNNb3VudGVkKCkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB2YXIgbm9Vc2VySW50ZXJhY3Rpb247XG4gICAgICAgIHZhciBub3JtYWxpemVkVmFsdWU7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBvbmx5IHN0cmluZ3MgYW5kIG51bWJlcnMgY2FzdGVkIHRvIHN0cmluZ3MgY2FuIGJlIGNvcGllZCB0byBjbGlwYm9hcmRcbiAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnICYmIHR5cGVvZiB2YWx1ZSAhPT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICB2YXIgZXJyb3IgPSBuZXcgRXJyb3IoXCJDYW5ub3QgY29weSB0eXBlb2YgXCIgKyB0eXBlb2YgdmFsdWUgKyBcIiB0byBjbGlwYm9hcmQsIG11c3QgYmUgYSBzdHJpbmdcIik7XG4gICAgICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKVxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZSh7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLFxuICAgICAgICAgICAgICAgICAgICBub1VzZXJJbnRlcmFjdGlvbjogdHJ1ZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBlbXB0eSBzdHJpbmdzIGFyZSBhbHNvIGNvbnNpZGVyZWQgaW52YWxpZFxuICAgICAgICAgICAgZWxzZSBpZiAodmFsdWUgPT09ICcnKSB7XG4gICAgICAgICAgICAgICAgdmFyIGVycm9yID0gbmV3IEVycm9yKFwiQ2Fubm90IGNvcHkgZW1wdHkgc3RyaW5nIHRvIGNsaXBib2FyZC5cIik7XG4gICAgICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKVxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZSh7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yLFxuICAgICAgICAgICAgICAgICAgICBub1VzZXJJbnRlcmFjdGlvbjogdHJ1ZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBub3JtYWxpemVkVmFsdWUgPSB2YWx1ZS50b1N0cmluZygpO1xuICAgICAgICAgICAgbm9Vc2VySW50ZXJhY3Rpb24gPSB3cml0ZVRleHQobm9ybWFsaXplZFZhbHVlKTtcbiAgICAgICAgICAgIHNldFN0YXRlKHtcbiAgICAgICAgICAgICAgICB2YWx1ZTogbm9ybWFsaXplZFZhbHVlLFxuICAgICAgICAgICAgICAgIGVycm9yOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgbm9Vc2VySW50ZXJhY3Rpb246IG5vVXNlckludGVyYWN0aW9uLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBzZXRTdGF0ZSh7XG4gICAgICAgICAgICAgICAgdmFsdWU6IG5vcm1hbGl6ZWRWYWx1ZSxcbiAgICAgICAgICAgICAgICBlcnJvcjogZXJyb3IsXG4gICAgICAgICAgICAgICAgbm9Vc2VySW50ZXJhY3Rpb246IG5vVXNlckludGVyYWN0aW9uLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9LCBbXSk7XG4gICAgcmV0dXJuIFtzdGF0ZSwgY29weVRvQ2xpcGJvYXJkXTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VDb3B5VG9DbGlwYm9hcmQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useCopyToClipboard.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useEffectOnce.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-use/esm/useEffectOnce.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar useEffectOnce = function (effect) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, []);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useEffectOnce);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VFZmZlY3RPbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUNsQztBQUNBLElBQUksZ0RBQVM7QUFDYjtBQUNBLGlFQUFlLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL25vZGVfbW9kdWxlcy9yZWFjdC11c2UvZXNtL3VzZUVmZmVjdE9uY2UuanM/MWMzNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG52YXIgdXNlRWZmZWN0T25jZSA9IGZ1bmN0aW9uIChlZmZlY3QpIHtcbiAgICB1c2VFZmZlY3QoZWZmZWN0LCBbXSk7XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlRWZmZWN0T25jZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useEffectOnce.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useMountedState.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-use/esm/useMountedState.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMountedState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useMountedState() {\n    var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var get = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return mountedRef.current; }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        mountedRef.current = true;\n        return function () {\n            mountedRef.current = false;\n        };\n    }, []);\n    return get;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VNb3VudGVkU3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVEO0FBQ3hDO0FBQ2YscUJBQXFCLDZDQUFNO0FBQzNCLGNBQWMsa0RBQVcsZUFBZSw0QkFBNEI7QUFDcEUsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VNb3VudGVkU3RhdGUuanM/ZjJiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VNb3VudGVkU3RhdGUoKSB7XG4gICAgdmFyIG1vdW50ZWRSZWYgPSB1c2VSZWYoZmFsc2UpO1xuICAgIHZhciBnZXQgPSB1c2VDYWxsYmFjayhmdW5jdGlvbiAoKSB7IHJldHVybiBtb3VudGVkUmVmLmN1cnJlbnQ7IH0sIFtdKTtcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgICAgICBtb3VudGVkUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgbW91bnRlZFJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIH07XG4gICAgfSwgW10pO1xuICAgIHJldHVybiBnZXQ7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useMountedState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useRafState.js":
/*!***************************************************!*\
  !*** ./node_modules/react-use/esm/useRafState.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useUnmount */ \"./node_modules/react-use/esm/useUnmount.js\");\n\n\nvar useRafState = function (initialState) {\n    var frame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), state = _a[0], setState = _a[1];\n    var setRafState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (value) {\n        cancelAnimationFrame(frame.current);\n        frame.current = requestAnimationFrame(function () {\n            setState(value);\n        });\n    }, []);\n    (0,_useUnmount__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n        cancelAnimationFrame(frame.current);\n    });\n    return [state, setRafState];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRafState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VSYWZTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXNEO0FBQ2hCO0FBQ3RDO0FBQ0EsZ0JBQWdCLDZDQUFNO0FBQ3RCLGFBQWEsK0NBQVE7QUFDckIsc0JBQXNCLGtEQUFXO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0wsSUFBSSx1REFBVTtBQUNkO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxpRUFBZSxXQUFXLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VSYWZTdGF0ZS5qcz8xMzM4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVVubW91bnQgZnJvbSAnLi91c2VVbm1vdW50JztcbnZhciB1c2VSYWZTdGF0ZSA9IGZ1bmN0aW9uIChpbml0aWFsU3RhdGUpIHtcbiAgICB2YXIgZnJhbWUgPSB1c2VSZWYoMCk7XG4gICAgdmFyIF9hID0gdXNlU3RhdGUoaW5pdGlhbFN0YXRlKSwgc3RhdGUgPSBfYVswXSwgc2V0U3RhdGUgPSBfYVsxXTtcbiAgICB2YXIgc2V0UmFmU3RhdGUgPSB1c2VDYWxsYmFjayhmdW5jdGlvbiAodmFsdWUpIHtcbiAgICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUoZnJhbWUuY3VycmVudCk7XG4gICAgICAgIGZyYW1lLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgc2V0U3RhdGUodmFsdWUpO1xuICAgICAgICB9KTtcbiAgICB9LCBbXSk7XG4gICAgdXNlVW5tb3VudChmdW5jdGlvbiAoKSB7XG4gICAgICAgIGNhbmNlbEFuaW1hdGlvbkZyYW1lKGZyYW1lLmN1cnJlbnQpO1xuICAgIH0pO1xuICAgIHJldHVybiBbc3RhdGUsIHNldFJhZlN0YXRlXTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VSYWZTdGF0ZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useRafState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useSetState.js":
/*!***************************************************!*\
  !*** ./node_modules/react-use/esm/useSetState.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar useSetState = function (initialState) {\n    if (initialState === void 0) { initialState = {}; }\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState), state = _a[0], set = _a[1];\n    var setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (patch) {\n        set(function (prevState) {\n            return Object.assign({}, prevState, patch instanceof Function ? patch(prevState) : patch);\n        });\n    }, []);\n    return [state, setState];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSetState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VTZXRTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFDOUM7QUFDQSxtQ0FBbUM7QUFDbkMsYUFBYSwrQ0FBUTtBQUNyQixtQkFBbUIsa0RBQVc7QUFDOUI7QUFDQSxtQ0FBbUM7QUFDbkMsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0EsaUVBQWUsV0FBVyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG1hcnZlbC9hZG1pbi1yZXN0Ly4vbm9kZV9tb2R1bGVzL3JlYWN0LXVzZS9lc20vdXNlU2V0U3RhdGUuanM/N2I5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG52YXIgdXNlU2V0U3RhdGUgPSBmdW5jdGlvbiAoaW5pdGlhbFN0YXRlKSB7XG4gICAgaWYgKGluaXRpYWxTdGF0ZSA9PT0gdm9pZCAwKSB7IGluaXRpYWxTdGF0ZSA9IHt9OyB9XG4gICAgdmFyIF9hID0gdXNlU3RhdGUoaW5pdGlhbFN0YXRlKSwgc3RhdGUgPSBfYVswXSwgc2V0ID0gX2FbMV07XG4gICAgdmFyIHNldFN0YXRlID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKHBhdGNoKSB7XG4gICAgICAgIHNldChmdW5jdGlvbiAocHJldlN0YXRlKSB7XG4gICAgICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgcHJldlN0YXRlLCBwYXRjaCBpbnN0YW5jZW9mIEZ1bmN0aW9uID8gcGF0Y2gocHJldlN0YXRlKSA6IHBhdGNoKTtcbiAgICAgICAgfSk7XG4gICAgfSwgW10pO1xuICAgIHJldHVybiBbc3RhdGUsIHNldFN0YXRlXTtcbn07XG5leHBvcnQgZGVmYXVsdCB1c2VTZXRTdGF0ZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useSetState.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useUnmount.js":
/*!**************************************************!*\
  !*** ./node_modules/react-use/esm/useUnmount.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useEffectOnce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useEffectOnce */ \"./node_modules/react-use/esm/useEffectOnce.js\");\n\n\nvar useUnmount = function (fn) {\n    var fnRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);\n    // update the ref each render so if it change the newest callback will be invoked\n    fnRef.current = fn;\n    (0,_useEffectOnce__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () { return function () { return fnRef.current(); }; });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUnmount);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VVbm1vdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0I7QUFDYTtBQUM1QztBQUNBLGdCQUFnQiw2Q0FBTTtBQUN0QjtBQUNBO0FBQ0EsSUFBSSwwREFBYSxlQUFlLHFCQUFxQiw0QkFBNEI7QUFDakY7QUFDQSxpRUFBZSxVQUFVLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9ub2RlX21vZHVsZXMvcmVhY3QtdXNlL2VzbS91c2VVbm1vdW50LmpzPzU2NzQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZUVmZmVjdE9uY2UgZnJvbSAnLi91c2VFZmZlY3RPbmNlJztcbnZhciB1c2VVbm1vdW50ID0gZnVuY3Rpb24gKGZuKSB7XG4gICAgdmFyIGZuUmVmID0gdXNlUmVmKGZuKTtcbiAgICAvLyB1cGRhdGUgdGhlIHJlZiBlYWNoIHJlbmRlciBzbyBpZiBpdCBjaGFuZ2UgdGhlIG5ld2VzdCBjYWxsYmFjayB3aWxsIGJlIGludm9rZWRcbiAgICBmblJlZi5jdXJyZW50ID0gZm47XG4gICAgdXNlRWZmZWN0T25jZShmdW5jdGlvbiAoKSB7IHJldHVybiBmdW5jdGlvbiAoKSB7IHJldHVybiBmblJlZi5jdXJyZW50KCk7IH07IH0pO1xufTtcbmV4cG9ydCBkZWZhdWx0IHVzZVVubW91bnQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useUnmount.js\n");

/***/ }),

/***/ "./node_modules/react-use/esm/useWindowSize.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-use/esm/useWindowSize.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useRafState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useRafState */ \"./node_modules/react-use/esm/useRafState.js\");\n/* harmony import */ var _misc_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./misc/util */ \"./node_modules/react-use/esm/misc/util.js\");\n\n\n\nvar useWindowSize = function (initialWidth, initialHeight) {\n    if (initialWidth === void 0) { initialWidth = Infinity; }\n    if (initialHeight === void 0) { initialHeight = Infinity; }\n    var _a = (0,_useRafState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        width: _misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser ? window.innerWidth : initialWidth,\n        height: _misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser ? window.innerHeight : initialHeight,\n    }), state = _a[0], setState = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        if (_misc_util__WEBPACK_IMPORTED_MODULE_2__.isBrowser) {\n            var handler_1 = function () {\n                setState({\n                    width: window.innerWidth,\n                    height: window.innerHeight,\n                });\n            };\n            (0,_misc_util__WEBPACK_IMPORTED_MODULE_2__.on)(window, 'resize', handler_1);\n            return function () {\n                (0,_misc_util__WEBPACK_IMPORTED_MODULE_2__.off)(window, 'resize', handler_1);\n            };\n        }\n    }, []);\n    return state;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useWindowSize);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-use/esm/useWindowSize.js\n");

/***/ })

};
;