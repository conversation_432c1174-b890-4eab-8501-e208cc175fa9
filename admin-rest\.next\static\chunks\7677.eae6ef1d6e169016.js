"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7677],{87677:function(e,r,s){s.r(r);var o=s(85893),t=s(60802),a=s(33359),n=s(87536),l=s(47533),d=s(16310),i=s(5233);let c=d.Ry().shape({password:d.Z_().required("form:error-password-required")});r.default=e=>{var r;let{onSubmit:s,loading:d}=e,{t:u}=(0,i.$G)(),{register:f,handleSubmit:m,formState:{errors:x}}=(0,n.cI)({resolver:(0,l.X)(c)});return(0,o.jsxs)("form",{onSubmit:m(s),noValidate:!0,children:[(0,o.jsx)(a.Z,{label:u("form:input-label-password"),...f("password"),error:u(null===(r=x.password)||void 0===r?void 0:r.message),variant:"outline",className:"mb-5"}),(0,o.jsx)(t.Z,{className:"h-11 w-full",loading:d,disabled:d,children:u("form:text-reset-password")})]})}},33367:function(e,r,s){s.d(r,{b:function(){return Eye}});var o=s(85893);let Eye=e=>(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:[(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})},33359:function(e,r,s){s.d(r,{Z:function(){return u}});var o=s(85893),t=s(33367);let EyeOff=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"})});var a=s(93967),n=s.n(a),l=s(67294),d=s(8152);let i={root:"ltr:pl-4 rtl:pr-4 ltr:pr-12 rtl:pl-12 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",normal:"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent",solid:"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent",outline:"border border-border-base focus:border-accent",shadow:"focus:shadow"},c=l.forwardRef((e,r)=>{let{className:s,inputClassName:a,forgotPassHelpText:c,label:u,name:f,error:m,children:x,variant:h="normal",shadow:b=!1,type:p="text",forgotPageLink:w="",required:j,...v}=e,[g,k]=(0,l.useState)(!1),N=n()(i.root,{[i.normal]:"normal"===h,[i.solid]:"solid"===h,[i.outline]:"outline"===h},!0==b&&i.shadow,a);return(0,o.jsxs)("div",{className:s,children:[(0,o.jsxs)("div",{className:"mb-3 flex items-center justify-between",children:[(0,o.jsxs)("label",{htmlFor:f,className:"text-sm font-semibold leading-none text-body-dark",children:[u,j?(0,o.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):""]}),w&&c&&(0,o.jsx)(d.Z,{href:w,className:"text-xs text-accent transition-colors duration-200 hover:text-accent-hover focus:font-semibold focus:text-accent-700 focus:outline-none",children:c})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{id:f,name:f,type:g?"text":"password",ref:r,className:N,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",...v}),(0,o.jsx)("label",{htmlFor:f,className:"absolute top-5 -mt-2 text-body end-4",onClick:()=>k(e=>!e),children:g?(0,o.jsx)(EyeOff,{className:"h-5 w-5"}):(0,o.jsx)(t.b,{className:"h-5 w-5"})})]}),m&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:m})]})});c.displayName="PasswordInput";var u=c}}]);