(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7075,2036],{29648:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/terms-and-conditions/[termSlug]",function(){return n(15230)}])},47133:function(e,t,n){"use strict";var s=n(85893),i=n(78985),o=n(79362),l=n(11163),r=n(16203),u=n(1631),a=n(99494),d=n(5233),c=n(74673),m=n(8144),p=n(90573),v=n(48583),f=n(93967),g=n.n(f),h=n(30824),A=n(62964);let SidebarItemMap=e=>{var t,n;let i,a,{menuItems:c}=e,{locale:m}=(0,l.useRouter)(),{t:f}=(0,d.$G)(),{settings:g}=(0,p.n)({language:m}),{childMenu:h}=c,N=null==g?void 0:null===(t=g.options)||void 0===t?void 0:t.enableTerms,S=null==g?void 0:null===(n=g.options)||void 0===n?void 0:n.enableCoupons,{permissions:x}=(0,r.WA)(),[C,T]=(0,v.KO)(o.Hf),{width:y}=(0,A.Z)(),{query:{shop:b}}=(0,l.useRouter)();return!N&&(i=null==c?void 0:c.childMenu.find(e=>"Terms And Conditions"===e.label))&&(i.permissions=r.M$),!S&&(a=null==c?void 0:c.childMenu.find(e=>"Coupons"===e.label))&&(a.permissions=r.M$),(0,s.jsx)("div",{className:"space-y-2",children:null==h?void 0:h.map(e=>{let{href:t,label:n,icon:i,permissions:l,childMenu:a}=e;return a||(0,r.Ft)(l,x)?(0,s.jsx)(u.Z,{href:t(null==b?void 0:b.toString()),label:f(n),icon:i,childMenu:a,miniSidebar:C&&y>=o.h2},n):null})})},SideBarGroup=()=>{var e,t;let[n,i]=(0,v.KO)(o.Hf),{role:l}=(0,r.WA)(),u="staff"===l?null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===a.siteSettings||void 0===a.siteSettings?void 0:null===(t=a.siteSettings.sidebarLinks)||void 0===t?void 0:t.shop,c=Object.keys(u),{width:m}=(0,A.Z)(),{t:p}=(0,d.$G)();return(0,s.jsx)(s.Fragment,{children:null==c?void 0:c.map((e,t)=>{var i;return(0,s.jsxs)("div",{className:g()("flex flex-col px-5",n&&m>=o.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:g()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",n&&m>=o.h2?"hidden":""),children:p(null===(i=u[e])||void 0===i?void 0:i.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:u[e]})]},t)})})};t.Z=e=>{let{children:t}=e,[n,r]=(0,v.KO)(o.Hf),{locale:u}=(0,l.useRouter)(),{width:a}=(0,A.Z)(),[d]=(0,v.KO)(o.GH),[p]=(0,v.KO)(o.W4);return(0,s.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===u||"he"===u?"rtl":"ltr",children:[(0,s.jsx)(i.Z,{}),(0,s.jsx)(c.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:g()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",a>=o.h2&&(d||p)?"pt-[8.75rem]":"pt-20",n&&a>=o.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,s.jsx)(h.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:g()("relative flex w-full flex-col justify-start transition-[padding] duration-300",a>=o.h2&&(d||p)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",n&&a>=o.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,s.jsx)(m.Z,{})]})]})]})}},63364:function(e,t,n){"use strict";var s=n(85893),i=n(3673);t.Z=e=>{let{termsAndConditions:t}=e,n=(0,i.t)({description:null==t?void 0:t.description});return(0,s.jsxs)("div",{className:"rounded bg-white px-8 py-10 shadow",children:[(null==t?void 0:t.title)?(0,s.jsx)("h3",{className:"mb-4 text-[22px] font-bold",children:null==t?void 0:t.title}):"",n?(0,s.jsx)("p",{className:"text-[15px] leading-[1.75em] text-[#5A5A5A] react-editor-description",dangerouslySetInnerHTML:{__html:n}}):""]})}},41107:function(e,t,n){"use strict";n.d(t,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var s=n(11163),i=n.n(s),o=n(88767),l=n(22920),r=n(5233),u=n(28597),a=n(97514),d=n(47869),c=n(93345),m=n(55191),p=n(3737);let v={...(0,m.h)(d.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:t,shop_id:n,...s}=e;return p.eN.get(d.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:n,...s,search:p.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>p.eN.post(d.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>p.eN.post(d.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(v.approve,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(v.disapprove,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:t,language:n}=e,{data:s,error:i,isLoading:l}=(0,o.useQuery)([d.P.TERMS_AND_CONDITIONS,{slug:t,language:n}],()=>v.get({slug:t,language:n}));return{termsAndConditions:s,error:i,loading:l}},useTermsAndConditionsQuery=e=>{var t;let{data:n,error:s,isLoading:i}=(0,o.useQuery)([d.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return v.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{termsAndConditions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(n),error:s,loading:i}},useCreateTermsAndConditionsMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,s.useRouter)(),{t:n}=(0,r.$G)();return(0,o.useMutation)(v.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(a.Z.termsAndCondition.list):a.Z.termsAndCondition.list;await i().push(e,void 0,{locale:c.Config.defaultLanguage}),l.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;l.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,r.$G)(),t=(0,o.useQueryClient)(),n=(0,s.useRouter)();return(0,o.useMutation)(v.update,{onSuccess:async t=>{let s=n.query.shop?"/".concat(n.query.shop).concat(a.Z.termsAndCondition.list):a.Z.termsAndCondition.list;await n.push(s,void 0,{locale:c.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:t=>{var n;l.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,o.useQueryClient)(),{t}=(0,r.$G)();return(0,o.useMutation)(v.delete,{onSuccess:()=>{l.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;l.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},15230:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSP:function(){return g}});var s=n(85893),i=n(47133),o=n(45957),l=n(55846),r=n(5233),u=n(11163),a=n(16203),d=n(41107),c=n(63364),m=n(90573),p=n(99930),v=n(30042),f=n(97514);let TermsAndConditionsPage=()=>{let e=(0,u.useRouter)(),{query:t,locale:n}=e,{query:{shop:i}}=e,{t:g}=(0,r.$G)(),{permissions:h}=(0,a.WA)(),{data:A}=(0,p.UE)(),{termsAndConditions:N,loading:S,error:x}=(0,d.nF)({slug:t.termSlug,language:n}),{settings:C}=(0,m.n)({language:n}),{data:T,isLoading:y}=(0,v.DZ)({slug:i}),b=null==T?void 0:T.id;if(S||y)return(0,s.jsx)(l.Z,{text:g("common:text-loading")});if(x)return(0,s.jsx)(o.Z,{message:x.message});{var _,M,O;let t=null==C?void 0:null===(_=C.options)||void 0===_?void 0:_.enableTerms,n=t?a.Zk:a.M$,s=(0,a.Ft)(a.M$,h),i=(0,a.Ft)(n,h),o=null===(O=null==A?void 0:null===(M=A.shops)||void 0===M?void 0:M.map(e=>e.id).includes(b))||void 0===O||O;i&&o||s||e.replace(f.Z.dashboard)}return(0,s.jsx)(c.Z,{termsAndConditions:N})};TermsAndConditionsPage.authenticate={permissions:a.Ho},TermsAndConditionsPage.Layout=i.Z;var g=!0;t.default=TermsAndConditionsPage},3673:function(e,t,n){"use strict";n.d(t,{t:function(){return useSanitizeContent}});var s=n(67294),i=n(91036),o=n.n(i);function useSanitizeContent(e){var t,n;let{description:i}=e,[l,r]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{r(!0)},[]),!i)return;let u=o()(i,{allowedTags:null===o()||void 0===o()?void 0:null===(n=o().defaults)||void 0===n?void 0:null===(t=n.allowedTags)||void 0===t?void 0:t.concat(["img","iframe"]),allowedAttributes:{p:["style","class"],strong:["style","class"],em:["style","class"],u:["style","class"],pre:["style","class"],sub:["style"],sup:["style"],span:["style","class"],a:["style","href","data-*","name","target","class"],img:["src","srcset","alt","title","width","height","loading","class"],li:["style","class"],iframe:["src","frameborder","allowfullscreen","class"]},allowedIframeHostnames:["www.youtube.com","player.vimeo.com"],allowedStyles:{"*":{color:[/^#(0x)?[0-9a-f]+$/i,/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/],"background-color":[/^#(0x)?[0-9a-f]+$/i,/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/],"text-align":[/^left$/,/^right$/,/^center$/],"font-size":[/^\d+(?:px|em|%)$/]}},allowedSchemesByTag:{img:["data"]}});return l?u:""}},75347:function(){},31777:function(){},34017:function(){},59905:function(){}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,1036,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=29648)}),_N_E=e.O()}]);