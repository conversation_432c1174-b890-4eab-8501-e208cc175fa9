"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_shipping_shipping-delete-view_tsx";
exports.ids = ["src_components_shipping_shipping-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/shipping/shipping-delete-view.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shipping/shipping-delete-view.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_shipping__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/shipping */ \"./src/data/shipping.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_shipping__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_shipping__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ShippingDeleteView = ()=>{\n    const { mutate: deleteShippingClass, isLoading: loading } = (0,_data_shipping__WEBPACK_IMPORTED_MODULE_3__.useDeleteShippingClassMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteShippingClass({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shipping\\\\shipping-delete-view.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShippingDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/shipping/shipping-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/shipping.ts":
/*!*************************************!*\
  !*** ./src/data/client/shipping.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shippingClient: () => (/* binding */ shippingClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst shippingClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS),\n    get ({ id }) {\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS}/${id}`);\n    },\n    paginated: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    },\n    all: ({ name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.SHIPPINGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/shipping.ts\n");

/***/ }),

/***/ "./src/data/shipping.ts":
/*!******************************!*\
  !*** ./src/data/shipping.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateShippingMutation: () => (/* binding */ useCreateShippingMutation),\n/* harmony export */   useDeleteShippingClassMutation: () => (/* binding */ useDeleteShippingClassMutation),\n/* harmony export */   useShippingClassesQuery: () => (/* binding */ useShippingClassesQuery),\n/* harmony export */   useShippingQuery: () => (/* binding */ useShippingQuery),\n/* harmony export */   useUpdateShippingMutation: () => (/* binding */ useUpdateShippingMutation)\n/* harmony export */ });\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _client_shipping__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/shipping */ \"./src/data/client/shipping.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_5__, _client_shipping__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_5__, _client_shipping__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useCreateShippingMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.create, {\n        onSuccess: ()=>{\n            router.push(_config_routes__WEBPACK_IMPORTED_MODULE_0__.Routes.shipping.list);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useDeleteShippingClassMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useUpdateShippingMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS);\n        }\n    });\n};\nconst useShippingQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        id\n    ], ()=>_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.get({\n            id\n        }));\n};\nconst useShippingClassesQuery = (options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_3__.API_ENDPOINTS.SHIPPINGS,\n        options\n    ], ({ queryKey, pageParam })=>_client_shipping__WEBPACK_IMPORTED_MODULE_6__.shippingClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        shippingClasses: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/shipping.ts\n");

/***/ })

};
;