"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6184],{26184:function(e,t,r){r.r(t);var o=r(85893),n=r(71421),a=r(75814),u=r(93242),s=r(9140);t.default=()=>{let{mutate:e,isLoading:t}=(0,u.xq)(),{data:r}=(0,a.X9)(),{closeModal:c}=(0,a.SO)();async function handleDelete(){try{e({id:r}),c()}catch(e){c(),(0,s.e)(e)}}return(0,o.jsx)(n.Z,{onCancel:c,onDelete:handleDelete,deleteBtnLoading:t})}},13202:function(e,t,r){r.d(t,{M:function(){return u}});var o=r(47869),n=r(55191),a=r(3737);let u={...(0,n.h)(o.P.PRODUCTS),get(e){let{slug:t,language:r}=e;return a.eN.get("".concat(o.P.PRODUCTS,"/").concat(t),{language:r,with:"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file"})},paginated:e=>{let{type:t,name:r,categories:n,shop_id:u,product_type:s,status:c,...i}=e;return a.eN.get(o.P.PRODUCTS,{searchJoin:"and",with:"shop;type;categories",shop_id:u,...i,search:a.eN.formatSearchParams({type:t,name:r,categories:n,shop_id:u,product_type:s,status:c})})},popular(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.POPULAR_PRODUCTS,{searchJoin:"and",with:"type;shop",...r,search:a.eN.formatSearchParams({shop_id:t})})},lowStock(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.LOW_STOCK_PRODUCTS_ANALYTICS,{searchJoin:"and",with:"type;shop",...r,search:a.eN.formatSearchParams({shop_id:t})})},generateDescription:e=>a.eN.post(o.P.GENERATE_DESCRIPTION,e),newOrInActiveProducts:e=>{let{user_id:t,shop_id:r,status:n,name:u,...s}=e;return a.eN.get(o.P.NEW_OR_INACTIVE_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:n,name:u,...s,search:a.eN.formatSearchParams({status:n,name:u})})},lowOrOutOfStockProducts:e=>{let{user_id:t,shop_id:r,status:n,categories:u,name:s,type:c,...i}=e;return a.eN.get(o.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:n,name:s,...i,search:a.eN.formatSearchParams({status:n,name:s,categories:u,type:c})})},productByCategory(e){let{limit:t,language:r}=e;return a.eN.get(o.P.CATEGORY_WISE_PRODUCTS,{limit:t,language:r})},mostSoldProductByCategory(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.CATEGORY_WISE_PRODUCTS_SALE,{searchJoin:"and",...r,search:a.eN.formatSearchParams({shop_id:t})})},getProductsByFlashSale:e=>{let{user_id:t,shop_id:r,slug:n,name:u,...s}=e;return a.eN.get(o.P.PRODUCTS_BY_FLASH_SALE,{searchJoin:"and",user_id:t,shop_id:r,slug:n,name:u,...s,search:a.eN.formatSearchParams({name:u})})},topRated(e){let{shop_id:t,...r}=e;return a.eN.get(o.P.TOP_RATED_PRODUCTS,{searchJoin:"and",...r,search:a.eN.formatSearchParams({shop_id:t})})}}},93242:function(e,t,r){r.d(t,{FA:function(){return useProductQuery},Uc:function(){return useProductsByFlashSaleQuery},YC:function(){return useInActiveProductsQuery},bJ:function(){return useGenerateDescriptionMutation},eH:function(){return useProductStockQuery},kN:function(){return useProductsQuery},qX:function(){return useCreateProductMutation},wE:function(){return useUpdateProductMutation},xq:function(){return useDeleteProductMutation}});var o=r(11163),n=r.n(o),a=r(22920),u=r(5233),s=r(88767),c=r(47869),i=r(13202),l=r(28597),d=r(97514),P=r(93345);let useCreateProductMutation=()=>{let e=(0,s.useQueryClient)(),t=(0,o.useRouter)(),{t:r}=(0,u.$G)();return(0,s.useMutation)(i.M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.product.list):d.Z.product.list;await n().push(e,void 0,{locale:P.Config.defaultLanguage}),a.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{let{data:t,status:o}=null==e?void 0:e.response;if(422===o){let e=Object.values(t).flat();a.Am.error(e[0])}else{var n;a.Am.error(r("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}}})},useUpdateProductMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,s.useQueryClient)(),r=(0,o.useRouter)();return(0,s.useMutation)(i.M.update,{onSuccess:async t=>{let o=r.query.shop?"/".concat(r.query.shop).concat(d.Z.product.list):d.Z.product.list;await r.push("".concat(o,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:P.Config.defaultLanguage}),a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.PRODUCTS)},onError:t=>{var r;a.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteProductMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,u.$G)();return(0,s.useMutation)(i.M.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{var r;a.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})},useProductQuery=e=>{let{slug:t,language:r}=e,{data:o,error:n,isLoading:a}=(0,s.useQuery)([c.P.PRODUCTS,{slug:t,language:r}],()=>i.M.get({slug:t,language:r}));return{product:o,error:n,isLoading:a}},useProductsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:o,error:n,isLoading:a}=(0,s.useQuery)([c.P.PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return i.M.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{products:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(o),error:n,loading:a}},useGenerateDescriptionMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,u.$G)("common");return(0,s.useMutation)(i.M.generateDescription,{onSuccess:()=>{a.Am.success(t("Generated..."))},onSettled:t=>{e.refetchQueries(c.P.GENERATE_DESCRIPTION)}})},useInActiveProductsQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,s.useQuery)([c.P.NEW_OR_INACTIVE_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return i.M.newOrInActiveProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:o,loading:n}},useProductStockQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,s.useQuery)([c.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return i.M.lowOrOutOfStockProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:o,loading:n}},useProductsByFlashSaleQuery=e=>{var t;let{data:r,error:o,isLoading:n}=(0,s.useQuery)([c.P.PRODUCTS_BY_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:r}=e;return i.M.getProductsByFlashSale(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:o,loading:n}}},9140:function(e,t,r){r.d(t,{e:function(){return getErrorMessage}});var o=r(11163),n=r.n(o),a=r(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let r of e.graphQLErrors){if(r.extensions&&"validation"===r.extensions.category)return t.message=r.message,t.validation=r.extensions.validation,t;r.extensions&&"authorization"===r.extensions.category&&(a.Z.remove("auth_token"),a.Z.remove("auth_permissions"),n().push("/"))}return t.message=e.message,t}}}]);