{"version": 3, "file": "SingleByteDecoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/single-byte/SingleByteDecoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAExE;;;;;GAKG;AACH;IAIE,2BAA6B,KAAoB,EAAE,OAA4B;QAAlD,UAAK,GAAL,KAAK,CAAe;QAC/C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACH,mCAAO,GAAP,UAAQ,MAAc,EAAE,IAAY;QAClC,gDAAgD;QAChD,IAAI,IAAI,KAAK,aAAa;YACxB,OAAO,QAAQ,CAAC;QAElB,+DAA+D;QAC/D,WAAW;QACX,IAAI,WAAW,CAAC,IAAI,CAAC;YACnB,OAAO,IAAI,CAAC;QAEd,+DAA+D;QAC/D,qBAAqB;QACrB,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAE3C,0CAA0C;QAC1C,IAAI,CAAC,UAAU;YACb,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElC,oDAAoD;QACpD,OAAO,UAAU,CAAC;IACpB,CAAC;IACH,wBAAC;AAAD,CAAC,AApCD,IAoCC"}