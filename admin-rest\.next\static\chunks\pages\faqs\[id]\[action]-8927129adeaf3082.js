(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4889,2007,2036],{59155:function(e,l,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/faqs/[id]/[action]",function(){return t(81261)}])},97670:function(e,l,t){"use strict";t.r(l);var r=t(85893),i=t(78985),s=t(79362),a=t(8144),n=t(74673),d=t(99494),o=t(5233),u=t(1631),c=t(11163),f=t(48583),h=t(93967),x=t.n(h),p=t(30824),g=t(62964);let SidebarItemMap=e=>{let{menuItems:l}=e,{t}=(0,o.$G)(),[i,a]=(0,f.KO)(s.Hf),{childMenu:n}=l,{width:d}=(0,g.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:l,label:a,icon:n,childMenu:o}=e;return(0,r.jsx)(u.Z,{href:l,label:t(a),icon:n,childMenu:o,miniSidebar:i&&d>=s.h2},a)})})},SideBarGroup=()=>{var e;let{t:l}=(0,o.$G)(),[t,i]=(0,f.KO)(s.Hf),a=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(a),{width:u}=(0,g.Z)();return(0,r.jsx)(r.Fragment,{children:null==n?void 0:n.map((e,i)=>{var n;return(0,r.jsxs)("div",{className:x()("flex flex-col px-5",t&&u>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:x()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",t&&u>=s.h2?"hidden":""),children:l(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},i)})})};l.default=e=>{let{children:l}=e,{locale:t}=(0,c.useRouter)(),[d,o]=(0,f.KO)(s.Hf),[u]=(0,f.KO)(s.GH),[h]=(0,f.KO)(s.W4),{width:m}=(0,g.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===t||"he"===t?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(n.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:x()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",m>=s.h2&&(u||h)?"lg:pt-[8.75rem]":"pt-20",d&&m>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:x()("relative flex w-full flex-col justify-start transition-[padding] duration-300",m>=s.h2&&(u||h)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&m>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,r.jsx)(a.Z,{})]})]})]})}},81261:function(e,l,t){"use strict";t.r(l),t.d(l,{__N_SSP:function(){return h},default:function(){return UpdateFAQsPage}});var r=t(85893),i=t(97670),s=t(45957),a=t(55846),n=t(5233),d=t(93345),o=t(16203),u=t(11163),c=t(40386),f=t(92001),h=!0;function UpdateFAQsPage(){let{query:e,locale:l}=(0,u.useRouter)(),{t}=(0,n.$G)(),{faqs:i,loading:o,error:h}=(0,f.cb)({id:e.id,language:"edit"===e.action.toString()?l:d.Config.defaultLanguage});return o?(0,r.jsx)(a.Z,{text:t("common:text-loading")}):h?(0,r.jsx)(s.Z,{message:h.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:t("form:form-title-edit-faq")})}),(0,r.jsx)(c.Z,{initialValues:i})]})}UpdateFAQsPage.authenticate={permissions:o.M$},UpdateFAQsPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,9494,5535,8186,1285,1631,386,9774,2888,179],function(){return e(e.s=59155)}),_N_E=e.O()}]);