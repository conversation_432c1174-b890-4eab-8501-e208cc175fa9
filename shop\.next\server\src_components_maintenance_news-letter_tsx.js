"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_maintenance_news-letter_tsx";
exports.ids = ["src_components_maintenance_news-letter_tsx"];
exports.modules = {

/***/ "./src/components/icons/send-icon.tsx":
/*!********************************************!*\
  !*** ./src/components/icons/send-icon.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SendIcon: () => (/* binding */ SendIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst SendIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16.045\",\n        height: \"16\",\n        viewBox: \"0 0 16.045 16\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            id: \"send\",\n            d: \"M17.633,9.293,3.284,2.079a.849.849,0,0,0-1.2,1.042l2,5.371,9.138,1.523L4.086,11.538l-2,5.371a.812.812,0,0,0,1.2.962l14.349-7.214A.762.762,0,0,0,17.633,9.293Z\",\n            transform: \"translate(-2.009 -1.994)\",\n            fill: \"currentColor\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\send-icon.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\send-icon.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9zZW5kLWljb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxXQUE4QyxDQUFDQyxzQkFDMUQsOERBQUNDO1FBQ0NDLE9BQU07UUFDTkMsT0FBTTtRQUNOQyxRQUFPO1FBQ1BDLFNBQVE7UUFDUCxHQUFHTCxLQUFLO2tCQUVULDRFQUFDTTtZQUNDQyxJQUFHO1lBQ0hDLEdBQUU7WUFDRkMsV0FBVTtZQUNWQyxNQUFLOzs7Ozs7Ozs7O2tCQUdUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2ljb25zL3NlbmQtaWNvbi50c3g/NGRiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgU2VuZEljb246IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4gKFxyXG4gIDxzdmdcclxuICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgd2lkdGg9XCIxNi4wNDVcIlxyXG4gICAgaGVpZ2h0PVwiMTZcIlxyXG4gICAgdmlld0JveD1cIjAgMCAxNi4wNDUgMTZcIlxyXG4gICAgey4uLnByb3BzfVxyXG4gID5cclxuICAgIDxwYXRoXHJcbiAgICAgIGlkPVwic2VuZFwiXHJcbiAgICAgIGQ9XCJNMTcuNjMzLDkuMjkzLDMuMjg0LDIuMDc5YS44NDkuODQ5LDAsMCwwLTEuMiwxLjA0MmwyLDUuMzcxLDkuMTM4LDEuNTIzTDQuMDg2LDExLjUzOGwtMiw1LjM3MWEuODEyLjgxMiwwLDAsMCwxLjIuOTYybDE0LjM0OS03LjIxNEEuNzYyLjc2MiwwLDAsMCwxNy42MzMsOS4yOTNaXCJcclxuICAgICAgdHJhbnNmb3JtPVwidHJhbnNsYXRlKC0yLjAwOSAtMS45OTQpXCJcclxuICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAvPlxyXG4gIDwvc3ZnPlxyXG4pO1xyXG4iXSwibmFtZXMiOlsiU2VuZEljb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwicGF0aCIsImlkIiwiZCIsInRyYW5zZm9ybSIsImZpbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/send-icon.tsx\n");

/***/ }),

/***/ "./src/components/maintenance/news-letter.tsx":
/*!****************************************************!*\
  !*** ./src/components/maintenance/news-letter.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_settings_subscribe_to_newsletter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/settings/subscribe-to-newsletter */ \"./src/components/settings/subscribe-to-newsletter.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_settings_subscribe_to_newsletter__WEBPACK_IMPORTED_MODULE_1__]);\n_components_settings_subscribe_to_newsletter__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst NewsLetter = ()=>{\n    const { data: { title, description } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-full w-full overflow-hidden rounded-[10px] bg-light p-8 md:h-auto md:min-h-0 md:max-w-2xl md:p-16 lg:w-screen lg:max-w-[56.25rem]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    src: \"/news-letter-icon.png\",\n                    alt: \"news letter icon\",\n                    width: 115,\n                    height: 125,\n                    className: \"mx-auto block\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 text-center md:mb-16\",\n                children: [\n                    title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mb-3 text-2xl font-bold text-black md:text-4xl\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, undefined) : \"\",\n                    description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mx-auto max-w-xl text-sm font-medium md:text-lg md:leading-8\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined) : \"\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_subscribe_to_newsletter__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\maintenance\\\\news-letter.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsLetter);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/maintenance/news-letter.tsx\n");

/***/ }),

/***/ "./src/components/settings/subscribe-to-newsletter.tsx":
/*!*************************************************************!*\
  !*** ./src/components/settings/subscribe-to-newsletter.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscribeToNewsletter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_settings_subscription_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/settings/subscription-form */ \"./src/components/settings/subscription-form.tsx\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_settings_subscription_form__WEBPACK_IMPORTED_MODULE_1__, _framework_settings__WEBPACK_IMPORTED_MODULE_2__]);\n([_components_settings_subscription_form__WEBPACK_IMPORTED_MODULE_1__, _framework_settings__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction SubscribeToNewsletter({ title, description }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const { mutate: subscribe, isLoading: loading, isSubscribed } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSubscription)();\n    function onSubmit({ email }) {\n        subscribe({\n            email\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col\",\n        children: [\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"mt-3 mb-7 text-xl font-semibold text-heading\",\n                children: t(title)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscribe-to-newsletter.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this) : \"\",\n            description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-7 text-sm text-heading\",\n                children: t(description)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscribe-to-newsletter.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this) : \"\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_subscription_form__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                onSubmit: onSubmit,\n                loading: loading,\n                success: isSubscribed\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscribe-to-newsletter.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscribe-to-newsletter.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/settings/subscribe-to-newsletter.tsx\n");

/***/ }),

/***/ "./src/components/settings/subscription-form.tsx":
/*!*******************************************************!*\
  !*** ./src/components/settings/subscription-form.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_icons_send_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/send-icon */ \"./src/components/icons/send-icon.tsx\");\n/* harmony import */ var _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/forms/form */ \"./src/components/ui/forms/form.tsx\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! yup */ \"yup\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(yup__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__]);\n_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst subscribeFormSchema = yup__WEBPACK_IMPORTED_MODULE_4__.object().shape({\n    email: yup__WEBPACK_IMPORTED_MODULE_4__.string().email(\"error-email-format\").required(\"error-email-required\")\n});\nfunction SubscriptionForm({ onSubmit, loading, success }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n            onSubmit: onSubmit,\n            validationSchema: subscribeFormSchema,\n            children: ({ register, formState: { errors } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full rounded border border-gray-200 bg-gray-50 ltr:pr-11 rtl:pl-11\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email_subscribe\",\n                                    ...register(\"email\"),\n                                    placeholder: t(\"common:text-enter-email\"),\n                                    className: \"h-14 w-full border-0 bg-transparent text-sm text-body outline-none focus:outline-0 ltr:pl-5 rtl:pr-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"absolute top-1/2 -mt-2 ltr:right-3 rtl:left-3\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex h-5 w-5 shrink-0 animate-spin rounded-full border-[3px] border-t-[3px] border-gray-300 text-accent ltr:ml-2 rtl:mr-2\",\n                                        style: {\n                                            borderTopColor: \"currentcolor\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_send_icon__WEBPACK_IMPORTED_MODULE_2__.SendIcon, {\n                                        className: \"text-gray-500 transition-colors hover:text-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        errors.email?.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-[13px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500\",\n                                children: t(errors.email.message)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 15\n                        }, this),\n                        !loading && success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-[13px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-accent\",\n                                children: t(\"text-subscribe-successfully\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\settings\\\\subscription-form.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/settings/subscription-form.tsx\n");

/***/ })

};
;