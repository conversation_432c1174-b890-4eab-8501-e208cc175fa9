(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3087,2036],{65283:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/[categorySlug]/[action]",function(){return n(41334)}])},41334:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSP:function(){return l},default:function(){return UpdateCategoriesPage}});var a=n(85893),r=n(97670),o=n(65343),i=n(11163),s=n(45957),u=n(55846),d=n(5233),g=n(47504),c=n(93345),l=!0;function UpdateCategoriesPage(){let{query:e,locale:t}=(0,i.useRouter)(),{t:n}=(0,d.$G)(),{category:r,isLoading:l,error:f}=(0,g.Im)({slug:e.categorySlug,language:"edit"===e.action.toString()?t:c.Config.defaultLanguage});return l?(0,a.jsx)(u.Z,{text:n("common:text-loading")}):f?(0,a.jsx)(s.Z,{message:f.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,a.jsx)("h1",{className:"text-lg font-semibold text-heading",children:n("form:form-title-edit-category")})}),(0,a.jsx)(o.Z,{initialValues:r})]})}UpdateCategoriesPage.Layout=r.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,9494,5535,8186,1285,1631,5468,1737,5054,9774,2888,179],function(){return e(e.s=65283)}),_N_E=e.O()}]);