(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7842],{7025:function(){},19360:function(){},56161:function(){},60439:function(){},99304:function(e,t,i){"use strict";i.d(t,{W_:function(){return Navigation},tl:function(){return Pagination}}),i(13433);var s=i(7185);function create_element_if_not_defined_createElementIfNotDefined(e,t,i,r){return e.params.createElements&&Object.keys(r).forEach(l=>{if(!i[l]&&!0===i.auto){let a=(0,s.e)(e.el,`.${r[l]}`)[0];a||((a=(0,s.c)("div",r[l])).className=r[l],e.el.append(a)),i[l]=a,t[l]=a}}),i}function Navigation(e){let{swiper:t,extendParams:i,on:r,emit:l}=e;function getEl(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function toggleEl(e,i){let r=t.params.navigation;(e=(0,s.m)(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...r.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](r.lockClass))})}function update(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){toggleEl(i,!1),toggleEl(e,!1);return}toggleEl(i,t.isBeginning&&!t.params.rewind),toggleEl(e,t.isEnd&&!t.params.rewind)}function onPrevClick(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),l("navigationPrev"))}function onNextClick(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),l("navigationNext"))}function init(){let e=t.params.navigation;if(t.params.navigation=create_element_if_not_defined_createElementIfNotDefined(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=getEl(e.nextEl),r=getEl(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:r}),i=(0,s.m)(i),r=(0,s.m)(r);let initButton=(i,s)=>{i&&i.addEventListener("click","next"===s?onNextClick:onPrevClick),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>initButton(e,"next")),r.forEach(e=>initButton(e,"prev"))}function destroy(){let{nextEl:e,prevEl:i}=t.navigation;e=(0,s.m)(e),i=(0,s.m)(i);let destroyButton=(e,i)=>{e.removeEventListener("click","next"===i?onNextClick:onPrevClick),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>destroyButton(e,"next")),i.forEach(e=>destroyButton(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},r("init",()=>{!1===t.params.navigation.enabled?disable():(init(),update())}),r("toEdge fromEdge lock unlock",()=>{update()}),r("destroy",()=>{destroy()}),r("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=(0,s.m)(e),i=(0,s.m)(i),t.enabled){update();return}[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),r("click",(e,i)=>{let{nextEl:r,prevEl:a}=t.navigation;r=(0,s.m)(r),a=(0,s.m)(a);let n=i.target,o=a.includes(n)||r.includes(n);if(t.isElement&&!o){let e=i.path||i.composedPath&&i.composedPath();e&&(o=e.find(e=>r.includes(e)||a.includes(e)))}if(t.params.navigation.hideOnClick&&!o){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===n||t.pagination.el.contains(n)))return;r.length?e=r[0].classList.contains(t.params.navigation.hiddenClass):a.length&&(e=a[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?l("navigationShow"):l("navigationHide"),[...r,...a].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let disable=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),destroy()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),init(),update()},disable,update,init,destroy})}function classes_to_selector_classesToSelector(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function Pagination(e){let t,{swiper:i,extendParams:r,on:l,emit:a}=e,n="swiper-pagination";r({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${n}-bullet`,bulletActiveClass:`${n}-bullet-active`,modifierClass:`${n}-`,currentClass:`${n}-current`,totalClass:`${n}-total`,hiddenClass:`${n}-hidden`,progressbarFillClass:`${n}-progressbar-fill`,progressbarOppositeClass:`${n}-progressbar-opposite`,clickableClass:`${n}-clickable`,lockClass:`${n}-lock`,horizontalClass:`${n}-horizontal`,verticalClass:`${n}-vertical`,paginationDisabledClass:`${n}-disabled`}}),i.pagination={el:null,bullets:[]};let o=0;function isPaginationDisabled(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function setSideBullets(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function onBulletClick(e){let t=e.target.closest(classes_to_selector_classesToSelector(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let r=(0,s.h)(t)*i.params.slidesPerGroup;if(i.params.loop){var l,a,n;if(i.realIndex===r)return;let e=(l=i.realIndex,a=r,(l%=n=i.slides.length,(a%=n)===l+1)?"next":a===l-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(r)}else i.slideTo(r)}function update(){let e,r;let l=i.rtl,n=i.params.pagination;if(isPaginationDisabled())return;let d=i.pagination.el;d=(0,s.m)(d);let c=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,p=i.params.loop?Math.ceil(c/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(r=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,r=i.previousSnapIndex):(r=i.previousIndex||0,e=i.activeIndex||0),"bullets"===n.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let a,c,p;let u=i.pagination.bullets;if(n.dynamicBullets&&(t=(0,s.f)(u[0],i.isHorizontal()?"width":"height",!0),d.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(n.dynamicMainBullets+4)}px`}),n.dynamicMainBullets>1&&void 0!==r&&((o+=e-(r||0))>n.dynamicMainBullets-1?o=n.dynamicMainBullets-1:o<0&&(o=0)),p=((c=(a=Math.max(e-o,0))+(Math.min(u.length,n.dynamicMainBullets)-1))+a)/2),u.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${n.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),d.length>1)u.forEach(t=>{let r=(0,s.h)(t);r===e?t.classList.add(...n.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),n.dynamicBullets&&(r>=a&&r<=c&&t.classList.add(...`${n.bulletActiveClass}-main`.split(" ")),r===a&&setSideBullets(t,"prev"),r===c&&setSideBullets(t,"next"))});else{let t=u[e];if(t&&t.classList.add(...n.bulletActiveClass.split(" ")),i.isElement&&u.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),n.dynamicBullets){let e=u[a],t=u[c];for(let e=a;e<=c;e+=1)u[e]&&u[e].classList.add(...`${n.bulletActiveClass}-main`.split(" "));setSideBullets(e,"prev"),setSideBullets(t,"next")}}if(n.dynamicBullets){let e=Math.min(u.length,n.dynamicMainBullets+4),s=(t*e-t)/2-p*t,r=l?"right":"left";u.forEach(e=>{e.style[i.isHorizontal()?r:"top"]=`${s}px`})}}d.forEach((t,s)=>{if("fraction"===n.type&&(t.querySelectorAll(classes_to_selector_classesToSelector(n.currentClass)).forEach(t=>{t.textContent=n.formatFractionCurrent(e+1)}),t.querySelectorAll(classes_to_selector_classesToSelector(n.totalClass)).forEach(e=>{e.textContent=n.formatFractionTotal(p)})),"progressbar"===n.type){let s;s=n.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let r=(e+1)/p,l=1,a=1;"horizontal"===s?l=r:a=r,t.querySelectorAll(classes_to_selector_classesToSelector(n.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${l}) scaleY(${a})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===n.type&&n.renderCustom?(t.innerHTML=n.renderCustom(i,e+1,p),0===s&&a("paginationRender",t)):(0===s&&a("paginationRender",t),a("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](n.lockClass)})}function render(){let e=i.params.pagination;if(isPaginationDisabled())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,r=i.pagination.el;r=(0,s.m)(r);let l="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?l+=e.renderBullet.call(i,t,e.bulletClass):l+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(l=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(l=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],r.forEach(t=>{"custom"!==e.type&&(t.innerHTML=l||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(classes_to_selector_classesToSelector(e.bulletClass)))}),"custom"!==e.type&&a("paginationRender",r[0])}function init(){let e;i.params.pagination=create_element_if_not_defined_createElementIfNotDefined(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.filter(e=>(0,s.a)(e,".swiper")[0]===i.el)[0]),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=(0,s.m)(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),o=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",onBulletClick),i.enabled||e.classList.add(t.lockClass)})))}function destroy(){let e=i.params.pagination;if(isPaginationDisabled())return;let t=i.pagination.el;t&&(t=(0,s.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",onBulletClick))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}l("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,s.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),l("init",()=>{!1===i.params.pagination.enabled?disable():(init(),render(),update())}),l("activeIndexChange",()=>{void 0===i.snapIndex&&update()}),l("snapIndexChange",()=>{update()}),l("snapGridLengthChange",()=>{render(),update()}),l("destroy",()=>{destroy()}),l("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),l("lock unlock",()=>{update()}),l("click",(e,t)=>{let r=t.target,l=(0,s.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&l&&l.length>0&&!r.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&r===i.navigation.nextEl||i.navigation.prevEl&&r===i.navigation.prevEl))return;let e=l[0].classList.contains(i.params.pagination.hiddenClass);!0===e?a("paginationShow"):a("paginationHide"),l.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let disable=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),destroy()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,s.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),init(),render(),update()},disable,render,update,init,destroy})}},13433:function(e,t,i){"use strict";function isObject(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function extend(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach(i=>{void 0===e[i]?e[i]=t[i]:isObject(t[i])&&isObject(e[i])&&Object.keys(t[i]).length>0&&extend(e[i],t[i])})}i.d(t,{a:function(){return getWindow},g:function(){return getDocument}});let s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function getDocument(){let e="undefined"!=typeof document?document:{};return extend(e,s),e}let r={document:s,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function getWindow(){let e="undefined"!=typeof window?window:{};return extend(e,r),e}},7185:function(e,t,i){"use strict";i.d(t,{a:function(){return elementParents},c:function(){return createElement},d:function(){return now},e:function(){return elementChildren},f:function(){return elementOuterSize},h:function(){return elementIndex},j:function(){return getTranslate},m:function(){return makeElementsArray},n:function(){return nextTick},p:function(){return elementStyle},q:function(){return elementNextAll},r:function(){return elementPrevAll},s:function(){return setCSSProperty},t:function(){return animateCSSModeScroll},u:function(){return showWarning},v:function(){return elementIsChildOf},w:function(){return function extend(){let e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){let s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(s instanceof HTMLElement):!s||1!==s.nodeType&&11!==s.nodeType)){let i=Object.keys(Object(s)).filter(e=>0>t.indexOf(e));for(let t=0,r=i.length;t<r;t+=1){let r=i[t],l=Object.getOwnPropertyDescriptor(s,r);void 0!==l&&l.enumerable&&(isObject(e[r])&&isObject(s[r])?s[r].__swiper__?e[r]=s[r]:extend(e[r],s[r]):!isObject(e[r])&&isObject(s[r])?(e[r]={},s[r].__swiper__?e[r]=s[r]:extend(e[r],s[r])):e[r]=s[r])}}}return e}},x:function(){return deleteProps}});var s=i(13433);function deleteProps(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function nextTick(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function now(){return Date.now()}function getTranslate(e,t){let i,r,l;void 0===t&&(t="x");let a=(0,s.a)(),n=function(e){let t;let i=(0,s.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((r=n.transform||n.webkitTransform).split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),l=new a.WebKitCSSMatrix("none"===r?"":r)):i=(l=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=a.WebKitCSSMatrix?l.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=a.WebKitCSSMatrix?l.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function setCSSProperty(e,t,i){e.style.setProperty(t,i)}function animateCSSModeScroll(e){let t,{swiper:i,targetPosition:r,side:l}=e,a=(0,s.a)(),n=-i.translate,o=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let c=r>n?"next":"prev",isOutOfBound=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,animate=()=>{t=new Date().getTime(),null===o&&(o=t);let e=Math.max(Math.min((t-o)/d,1),0),s=n+(.5-Math.cos(e*Math.PI)/2)*(r-n);if(isOutOfBound(s,r)&&(s=r),i.wrapperEl.scrollTo({[l]:s}),isOutOfBound(s,r)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[l]:s})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(animate)};animate()}function elementChildren(e,t){void 0===t&&(t="");let i=[...e.children];return(e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t)?i.filter(e=>e.matches(t)):i}function elementIsChildOf(e,t){let i=t.contains(e);if(!i&&t instanceof HTMLSlotElement){let i=[...t.assignedElements()];return i.includes(e)}return i}function showWarning(e){try{console.warn(e);return}catch(e){}}function createElement(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function elementPrevAll(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function elementNextAll(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function elementStyle(e,t){let i=(0,s.a)();return i.getComputedStyle(e,null).getPropertyValue(t)}function elementIndex(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function elementParents(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function elementOuterSize(e,t,i){let r=(0,s.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function makeElementsArray(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}},2261:function(e,t,i){"use strict";let s,r,l;i.d(t,{tq:function(){return m},o5:function(){return g}});var a=i(67294),n=i(13433),o=i(7185);function getSupport(){return s||(s=function(){let e=(0,n.a)(),t=(0,n.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function getDevice(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=getSupport(),s=(0,n.a)(),r=s.navigator.platform,l=t||s.navigator.userAgent,a={ios:!1,android:!1},o=s.screen.width,d=s.screen.height,c=l.match(/(Android);?[\s\/]+([\d.]+)?/),p=l.match(/(iPad).*OS\s([\d_]+)/),u=l.match(/(iPod)(.*OS\s([\d_]+))?/),h=!p&&l.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="MacIntel"===r;return!p&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${d}`)>=0&&((p=l.match(/(Version)\/([\d.]+)/))||(p=[0,1,"13_0_0"]),f=!1),c&&"Win32"!==r&&(a.os="android",a.android=!0),(p||h||u)&&(a.os="ios",a.ios=!0),a}(e)),r}let toggleSlideClasses$1=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},toggleSlideClasses=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},processLazyPreloader=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},unlazy=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},preload=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&unlazy(e,s)});return}let l=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=l+t;s+=1){let t=(s%i+i)%i;(t<r||t>l)&&unlazy(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(l+t,i-1);s+=1)s!==r&&(s>l||s<r)&&unlazy(e,s)};function transitionEmit(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:l,previousIndex:a}=t,n=s;if(n||(n=l>a?"next":l<a?"prev":"reset"),t.emit(`transition${r}`),i&&l!==a){if("reset"===n){t.emit(`slideResetTransition${r}`);return}t.emit(`slideChangeTransition${r}`),"next"===n?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`)}}function preventEdgeSwipe(e,t,i){let s=(0,n.a)(),{params:r}=e,l=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return!l||!(i<=a)&&!(i>=s.innerWidth-a)||"prevent"===l&&(t.preventDefault(),!0)}function onTouchStart(e){let t=(0,n.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type){preventEdgeSwipe(this,i,i.targetTouches[0].pageX);return}let{params:r,touches:l,enabled:a}=this;if(!a||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let d=i.target;if("wrapper"===r.touchEventsTarget&&!(0,o.v)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let c=!!r.noSwipingClass&&""!==r.noSwipingClass,p=i.composedPath?i.composedPath():i.path;c&&i.target&&i.target.shadowRoot&&p&&(d=p[0]);let u=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function __closestFrom(t){if(!t||t===(0,n.g)()||t===(0,n.a)())return null;t.assignedSlot&&(t=t.assignedSlot);let i=t.closest(e);return i||t.getRootNode?i||__closestFrom(t.getRootNode().host):null}(t)}(u,d):d.closest(u))){this.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;l.currentX=i.pageX,l.currentY=i.pageY;let f=l.currentX,m=l.currentY;if(!preventEdgeSwipe(this,i,f))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=f,l.startY=m,s.touchStartTime=(0,o.d)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let g=!0;d.matches(s.focusableElements)&&(g=!1,"SELECT"===d.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(s.focusableElements))&&t.activeElement.blur();let v=g&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||v)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function onTouchMove(e){let t,i;let s=(0,n.g)(),r=this.touchEventsData,{params:l,touches:a,rtlTranslate:d,enabled:c}=this;if(!c||!l.simulateTouch&&"mouse"===e.pointerType)return;let p=e;if(p.originalEvent&&(p=p.originalEvent),"pointermove"===p.type){if(null!==r.touchId)return;let e=p.pointerId;if(e!==r.pointerId)return}if("touchmove"===p.type){if(!(t=[...p.changedTouches].filter(e=>e.identifier===r.touchId)[0])||t.identifier!==r.touchId)return}else t=p;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",p);return}let u=t.pageX,h=t.pageY;if(p.preventedByNestedSwiper){a.startX=u,a.startY=h;return}if(!this.allowTouchMove){p.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(a,{startX:u,startY:h,currentX:u,currentY:h}),r.touchStartTime=(0,o.d)());return}if(l.touchReleaseOnEdges&&!l.loop){if(this.isVertical()){if(h<a.startY&&this.translate<=this.maxTranslate()||h>a.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(u<a.startX&&this.translate<=this.maxTranslate()||u>a.startX&&this.translate>=this.minTranslate())return}if(s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==p.target&&"mouse"!==p.pointerType&&s.activeElement.blur(),s.activeElement&&p.target===s.activeElement&&p.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",p),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=u,a.currentY=h;let f=a.currentX-a.startX,m=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(f**2+m**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?r.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,r.isScrolling=this.isHorizontal()?e>l.touchAngle:90-e>l.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",p),void 0===r.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===p.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!l.cssMode&&p.cancelable&&p.preventDefault(),l.touchMoveStopPropagation&&!l.nested&&p.stopPropagation();let g=this.isHorizontal()?f:m,v=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;l.oneWayMovement&&(g=Math.abs(g)*(d?1:-1),v=Math.abs(v)*(d?1:-1)),a.diff=g,g*=l.touchRatio,d&&(g=-g,v=-v);let w=this.touchesDirection;this.swipeDirection=g>0?"prev":"next",this.touchesDirection=v>0?"prev":"next";let b=this.params.loop&&!l.cssMode,S="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(b&&S&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,l.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",p)}if(new Date().getTime(),r.isMoved&&r.allowThresholdMove&&w!==this.touchesDirection&&b&&S&&Math.abs(g)>=1){Object.assign(a,{startX:u,startY:h,currentX:u,currentY:h,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",p),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let y=!0,E=l.resistanceRatio;if(l.touchReleaseOnEdges&&(E=0),g>0?(b&&S&&!i&&r.allowThresholdMove&&r.currentTranslate>(l.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==l.slidesPerView&&this.slides.length-l.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(y=!1,l.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+g)**E))):g<0&&(b&&S&&!i&&r.allowThresholdMove&&r.currentTranslate<(l.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==l.slidesPerView&&this.slides.length-l.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===l.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(l.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(y=!1,l.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-g)**E))),y&&(p.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),l.threshold>0){if(Math.abs(g)>l.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,r.currentTranslate=r.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{r.currentTranslate=r.startTranslate;return}}l.followFinger&&!l.cssMode&&((l.freeMode&&l.freeMode.enabled&&this.freeMode||l.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),l.freeMode&&l.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function onTouchEnd(e){let t,i;let s=this,r=s.touchEventsData,l=e;l.originalEvent&&(l=l.originalEvent);let a="touchend"===l.type||"touchcancel"===l.type;if(a){if(!(t=[...l.changedTouches].filter(e=>e.identifier===r.touchId)[0])||t.identifier!==r.touchId)return}else{if(null!==r.touchId||l.pointerId!==r.pointerId)return;t=l}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(l.type)){let e=["pointercancel","contextmenu"].includes(l.type)&&(s.browser.isSafari||s.browser.isWebView);if(!e)return}r.pointerId=null,r.touchId=null;let{params:n,touches:d,rtlTranslate:c,slidesGrid:p,enabled:u}=s;if(!u||!n.simulateTouch&&"mouse"===l.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",l),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&n.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}n.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let h=(0,o.d)(),f=h-r.touchStartTime;if(s.allowClick){let e=l.path||l.composedPath&&l.composedPath();s.updateClickedSlide(e&&e[0]||l.target,e),s.emit("tap click",l),f<300&&h-r.lastClickTime<300&&s.emit("doubleTap doubleClick",l)}if(r.lastClickTime=(0,o.d)(),(0,o.n)(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===d.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=n.followFinger?c?s.translate:-s.translate:-r.currentTranslate,n.cssMode)return;if(n.freeMode&&n.freeMode.enabled){s.freeMode.onTouchEnd({currentPos:i});return}let m=i>=-s.maxTranslate()&&!s.params.loop,g=0,v=s.slidesSizesGrid[0];for(let e=0;e<p.length;e+=e<n.slidesPerGroupSkip?1:n.slidesPerGroup){let t=e<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==p[e+t]?(m||i>=p[e]&&i<p[e+t])&&(g=e,v=p[e+t]-p[e]):(m||i>=p[e])&&(g=e,v=p[p.length-1]-p[p.length-2])}let w=null,b=null;n.rewind&&(s.isBeginning?b=n.virtual&&n.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(w=0));let S=(i-p[g])/v,y=g<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(f>n.longSwipesMs){if(!n.longSwipes){s.slideTo(s.activeIndex);return}"next"===s.swipeDirection&&(S>=n.longSwipesRatio?s.slideTo(n.rewind&&s.isEnd?w:g+y):s.slideTo(g)),"prev"===s.swipeDirection&&(S>1-n.longSwipesRatio?s.slideTo(g+y):null!==b&&S<0&&Math.abs(S)>n.longSwipesRatio?s.slideTo(b):s.slideTo(g))}else{if(!n.shortSwipes){s.slideTo(s.activeIndex);return}let e=s.navigation&&(l.target===s.navigation.nextEl||l.target===s.navigation.prevEl);e?l.target===s.navigation.nextEl?s.slideTo(g+y):s.slideTo(g):("next"===s.swipeDirection&&s.slideTo(null!==w?w:g+y),"prev"===s.swipeDirection&&s.slideTo(null!==b?b:g))}}function onResize(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:l}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let n=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&l!==e.snapGrid&&e.checkOverflow()}function onClick(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function onScroll(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function onLoad(e){processLazyPreloader(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function onDocumentTouchStart(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let events=(e,t)=>{let i=(0,n.g)(),{params:s,el:r,wrapperEl:l,device:a}=e,o=!!s.nested,d="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",e.onClick,!0),s.cssMode&&l[d]("scroll",e.onScroll),s.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",onResize,!0):e[t]("observerUpdate",onResize,!0),r[d]("load",e.onLoad,{capture:!0}))},isGridEnabled=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var d={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let c={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function onceHandler(){s.off(e,onceHandler),onceHandler.__emitterProxy&&delete onceHandler.__emitterProxy;for(var i=arguments.length,r=Array(i),l=0;l<i;l++)r[l]=arguments[l];t.apply(s,r)}return onceHandler.__emitterProxy=t,s.on(e,onceHandler,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i;let s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,l=Array(r),a=0;a<r;a++)l[a]=arguments[a];"string"==typeof l[0]||Array.isArray(l[0])?(e=l[0],t=l.slice(1,l.length),i=s):(e=l[0].events,t=l[0].data,i=l[0].context||s),t.unshift(i);let n=Array.isArray(e)?e:e.split(" ");return n.forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t;let i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,o.p)(i,"padding-left")||0,10)-parseInt((0,o.p)(i,"padding-right")||0,10),t=t-parseInt((0,o.p)(i,"padding-top")||0,10)-parseInt((0,o.p)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function getDirectionPropertyValue(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let i=t.params,{wrapperEl:s,slidesEl:r,size:l,rtlTranslate:a,wrongRTL:n}=t,d=t.virtual&&i.virtual.enabled,c=d?t.virtual.slides.length:t.slides.length,p=(0,o.e)(r,`.${t.params.slideClass}, swiper-slide`),u=d?t.virtual.slides.length:p.length,h=[],f=[],m=[],g=i.slidesOffsetBefore;"function"==typeof g&&(g=i.slidesOffsetBefore.call(t));let v=i.slidesOffsetAfter;"function"==typeof v&&(v=i.slidesOffsetAfter.call(t));let w=t.snapGrid.length,b=t.slidesGrid.length,S=i.spaceBetween,y=-g,E=0,T=0;if(void 0===l)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*l:"string"==typeof S&&(S=parseFloat(S)),t.virtualSize=-S,p.forEach(e=>{a?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),i.centeredSlides&&i.cssMode&&((0,o.s)(s,"--swiper-centered-offset-before",""),(0,o.s)(s,"--swiper-centered-offset-after",""));let x=i.grid&&i.grid.rows>1&&t.grid;x?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let C="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>void 0!==i.breakpoints[e].slidesPerView).length>0;for(let s=0;s<u;s+=1){let r;if(e=0,p[s]&&(r=p[s]),x&&t.grid.updateSlide(s,r,p),!p[s]||"none"!==(0,o.p)(r,"display")){if("auto"===i.slidesPerView){C&&(p[s].style[t.getDirectionLabel("width")]="");let l=getComputedStyle(r),a=r.style.transform,n=r.style.webkitTransform;if(a&&(r.style.transform="none"),n&&(r.style.webkitTransform="none"),i.roundLengths)e=t.isHorizontal()?(0,o.f)(r,"width",!0):(0,o.f)(r,"height",!0);else{let t=getDirectionPropertyValue(l,"width"),i=getDirectionPropertyValue(l,"padding-left"),s=getDirectionPropertyValue(l,"padding-right"),a=getDirectionPropertyValue(l,"margin-left"),n=getDirectionPropertyValue(l,"margin-right"),o=l.getPropertyValue("box-sizing");if(o&&"border-box"===o)e=t+a+n;else{let{clientWidth:l,offsetWidth:o}=r;e=t+i+s+a+n+(o-l)}}a&&(r.style.transform=a),n&&(r.style.webkitTransform=n),i.roundLengths&&(e=Math.floor(e))}else e=(l-(i.slidesPerView-1)*S)/i.slidesPerView,i.roundLengths&&(e=Math.floor(e)),p[s]&&(p[s].style[t.getDirectionLabel("width")]=`${e}px`);p[s]&&(p[s].swiperSlideSize=e),m.push(e),i.centeredSlides?(y=y+e/2+E/2+S,0===E&&0!==s&&(y=y-l/2-S),0===s&&(y=y-l/2-S),.001>Math.abs(y)&&(y=0),i.roundLengths&&(y=Math.floor(y)),T%i.slidesPerGroup==0&&h.push(y),f.push(y)):(i.roundLengths&&(y=Math.floor(y)),(T-Math.min(t.params.slidesPerGroupSkip,T))%t.params.slidesPerGroup==0&&h.push(y),f.push(y),y=y+e+S),t.virtualSize+=e+S,E=e,T+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+v,a&&n&&("slide"===i.effect||"coverflow"===i.effect)&&(s.style.width=`${t.virtualSize+S}px`),i.setWrapperSize&&(s.style[t.getDirectionLabel("width")]=`${t.virtualSize+S}px`),x&&t.grid.updateWrapperSize(e,h),!i.centeredSlides){let e=[];for(let s=0;s<h.length;s+=1){let r=h[s];i.roundLengths&&(r=Math.floor(r)),h[s]<=t.virtualSize-l&&e.push(r)}h=e,Math.floor(t.virtualSize-l)-Math.floor(h[h.length-1])>1&&h.push(t.virtualSize-l)}if(d&&i.loop){let e=m[0]+S;if(i.slidesPerGroup>1){let s=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),r=e*i.slidesPerGroup;for(let e=0;e<s;e+=1)h.push(h[h.length-1]+r)}for(let s=0;s<t.virtual.slidesBefore+t.virtual.slidesAfter;s+=1)1===i.slidesPerGroup&&h.push(h[h.length-1]+e),f.push(f[f.length-1]+e),t.virtualSize+=e}if(0===h.length&&(h=[0]),0!==S){let e=t.isHorizontal()&&a?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!i.cssMode||!!i.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${S}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;m.forEach(t=>{e+=t+(S||0)}),e-=S;let t=e>l?e-l:0;h=h.map(e=>e<=0?-g:e>t?t+v:e)}if(i.centerInsufficientSlides){let e=0;m.forEach(t=>{e+=t+(S||0)}),e-=S;let t=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(e+t<l){let i=(l-e-t)/2;h.forEach((e,t)=>{h[t]=e-i}),f.forEach((e,t)=>{f[t]=e+i})}}if(Object.assign(t,{slides:p,snapGrid:h,slidesGrid:f,slidesSizesGrid:m}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){(0,o.s)(s,"--swiper-centered-offset-before",`${-h[0]}px`),(0,o.s)(s,"--swiper-centered-offset-after",`${t.size/2-m[m.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(u!==c&&t.emit("slidesLengthChange"),h.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),f.length!==b&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!d&&!i.cssMode&&("slide"===i.effect||"fade"===i.effect)){let e=`${i.containerModifierClass}backface-hidden`,s=t.el.classList.contains(e);u<=i.maxBackfaceHiddenSlides?s||t.el.classList.add(e):s&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t;let i=this,s=[],r=i.virtual&&i.params.virtual.enabled,l=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let getSlideByIndex=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(getSlideByIndex(e))}}else s.push(getSlideByIndex(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;l=e>l?e:l}(l||0===l)&&(i.wrapperEl.style.height=`${l}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let l=-e;s&&(l=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let n=i[e],o=n.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let d=(l+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+a),c=(l-r[0]+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+a),p=-(l-o),u=p+this.slidesSizesGrid[e],h=p>=0&&p<=this.size-this.slidesSizesGrid[e],f=p>=0&&p<this.size-1||u>1&&u<=this.size||p<=0&&u>=this.size;f&&(this.visibleSlides.push(n),this.visibleSlidesIndexes.push(e)),toggleSlideClasses$1(n,f,t.slideVisibleClass),toggleSlideClasses$1(n,h,t.slideFullyVisibleClass),n.progress=s?-d:d,n.originalProgress=s?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:l,progressLoop:a}=this,n=r,o=l;if(0===i)s=0,r=!0,l=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());r=t||s<=0,l=a||s>=1,t&&(s=0),a&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],l=this.slidesGrid[this.slidesGrid.length-1],n=Math.abs(e);(a=n>=s?(n-s)/l:(n+l-r)/l)>1&&(a-=1)}Object.assign(this,{progress:s,progressLoop:a,isBeginning:r,isEnd:l}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!n&&this.emit("reachBeginning toEdge"),l&&!o&&this.emit("reachEnd toEdge"),(n&&!r||o&&!l)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i;let{slides:s,params:r,slidesEl:l,activeIndex:a}=this,n=this.virtual&&r.virtual.enabled,d=this.grid&&r.grid&&r.grid.rows>1,getFilteredSlide=e=>(0,o.e)(l,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(n){if(r.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=getFilteredSlide(`[data-swiper-slide-index="${t}"]`)}else e=getFilteredSlide(`[data-swiper-slide-index="${a}"]`)}else d?(e=s.filter(e=>e.column===a)[0],i=s.filter(e=>e.column===a+1)[0],t=s.filter(e=>e.column===a-1)[0]):e=s[a];e&&!d&&(i=(0,o.q)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=(0,o.r)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{toggleSlideClasses(s,s===e,r.slideActiveClass),toggleSlideClasses(s,s===i,r.slideNextClass),toggleSlideClasses(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i;let s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:l,params:a,activeIndex:n,realIndex:o,snapIndex:d}=s,c=e,getVirtualRealIndex=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t;let{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),l.indexOf(r)>=0)t=l.indexOf(r);else{let e=Math.min(a.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/a.slidesPerGroup)}if(t>=l.length&&(t=l.length-1),c===n&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(c===n&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=getVirtualRealIndex(c);return}let p=s.grid&&a.grid&&a.grid.rows>1;if(s.virtual&&a.virtual.enabled&&a.loop)i=getVirtualRealIndex(c);else if(p){let e=s.slides.filter(e=>e.column===c)[0],t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/a.grid.rows)}else if(s.slides[c]){let e=s.slides[c].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):c}else i=c;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:n,activeIndex:c}),s.initialized&&preload(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(o!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i;let s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let l=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){l=!0,i=e;break}}if(r&&l)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let l=(0,o.j)(r,e);return l+=this.cssOverflowAdjustment(),i&&(l=-l),l||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:l}=this,a=0,n=0;this.isHorizontal()?a=i?-e:e:n=e,s.roundLengths&&(a=Math.floor(a),n=Math.floor(n)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:n,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-n:s.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():n-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${n}px, 0px)`);let o=this.maxTranslate()-this.minTranslate();(0===o?0:(e-this.minTranslate())/o)!==l&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let l;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let a=this,{params:n,wrapperEl:d}=a;if(a.animating&&n.preventInteractionOnTransition)return!1;let c=a.minTranslate(),p=a.maxTranslate();if(l=s&&e>c?c:s&&e<p?p:e,a.updateProgress(l),n.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-l;else{if(!a.support.smoothScroll)return(0,o.t)({swiper:a,targetPosition:-l,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-l,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),transitionEmit({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),transitionEmit({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let l;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,n=e;n<0&&(n=0);let{params:d,snapGrid:c,slidesGrid:p,previousIndex:u,activeIndex:h,rtlTranslate:f,wrapperEl:m,enabled:g}=a;if(!g&&!s&&!r||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let v=Math.min(a.params.slidesPerGroupSkip,n),w=v+Math.floor((n-v)/a.params.slidesPerGroup);w>=c.length&&(w=c.length-1);let b=-c[w];if(d.normalizeSlideIndex)for(let e=0;e<p.length;e+=1){let t=-Math.floor(100*b),i=Math.floor(100*p[e]),s=Math.floor(100*p[e+1]);void 0!==p[e+1]?t>=i&&t<s-(s-i)/2?n=e:t>=i&&t<s&&(n=e+1):t>=i&&(n=e)}if(a.initialized&&n!==h&&(!a.allowSlideNext&&(f?b>a.translate&&b>a.minTranslate():b<a.translate&&b<a.minTranslate())||!a.allowSlidePrev&&b>a.translate&&b>a.maxTranslate()&&(h||0)!==n))return!1;n!==(u||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(b),l=n>h?"next":n<h?"prev":"reset";let S=a.virtual&&a.params.virtual.enabled;if(!(S&&r)&&(f&&-b===a.translate||!f&&b===a.translate))return a.updateActiveIndex(n),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(b),"reset"!==l&&(a.transitionStart(i,l),a.transitionEnd(i,l)),!1;if(d.cssMode){let e=a.isHorizontal(),i=f?b:-b;if(0===t)S&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),S&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[e?"scrollLeft":"scrollTop"]=i})):m[e?"scrollLeft":"scrollTop"]=i,S&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return(0,o.t)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;m.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}return a.setTransition(t),a.setTranslate(b),a.updateActiveIndex(n),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,s),a.transitionStart(i,l),0===t?a.transitionEnd(i,l):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,l))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){if(void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e){let t=parseInt(e,10);e=t}let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let l=r.grid&&r.params.grid&&r.params.grid.rows>1,a=e;if(r.params.loop){if(r.virtual&&r.params.virtual.enabled)a+=r.virtual.slidesBefore;else{let e;if(l){let t=a*r.params.grid.rows;e=r.slides.filter(e=>1*e.getAttribute("data-swiper-slide-index")===t)[0].column}else e=r.getSlideIndexByData(a);let t=l?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,n=r.params.slidesPerView;"auto"===n?n=r.slidesPerViewDynamic():(n=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&n%2==0&&(n+=1));let o=t-e<n;if(i&&(o=o||e<Math.ceil(n/2)),s&&i&&"auto"!==r.params.slidesPerView&&!l&&(o=!1),o){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(l){let e=a*r.params.grid.rows;a=r.slides.filter(t=>1*t.getAttribute("data-swiper-slide-index")===e)[0].column}else a=r.getSlideIndexByData(a)}}return requestAnimationFrame(()=>{r.slideTo(a,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:l,animating:a}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let n=l.slidesPerGroup;"auto"===l.slidesPerView&&1===l.slidesPerGroup&&l.slidesPerGroupAuto&&(n=Math.max(s.slidesPerViewDynamic("current",!0),1));let o=s.activeIndex<l.slidesPerGroupSkip?1:n,d=s.virtual&&l.virtual.enabled;if(l.loop){if(a&&!d&&l.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&l.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+o,e,t,i)}),!0}return l.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:l,slidesGrid:a,rtlTranslate:n,enabled:o,animating:d}=s;if(!o||s.destroyed)return s;void 0===e&&(e=s.params.speed);let c=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}let p=n?s.translate:-s.translate;function normalize(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let u=normalize(p),h=l.map(e=>normalize(e)),f=l[h.indexOf(u)-1];if(void 0===f&&r.cssMode){let e;l.forEach((t,i)=>{u>=t&&(e=i)}),void 0!==e&&(f=l[e>0?e-1:e])}let m=0;if(void 0!==f&&((m=a.indexOf(f))<0&&(m=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(m=Math.max(m=m-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(m,e,t,i)}),!0):s.slideTo(m,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,l=Math.min(this.params.slidesPerGroupSkip,r),a=l+Math.floor((r-l)/this.params.slidesPerGroup),n=this.rtlTranslate?this.translate:-this.translate;if(n>=this.snapGrid[a]){let e=this.snapGrid[a],t=this.snapGrid[a+1];n-e>(t-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1],t=this.snapGrid[a];n-e<=(t-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e;let t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,l=t.clickedIndex,a=t.isElement?"swiper-slide":`.${i.slideClass}`;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?l<t.loopedSlides-r/2||l>t.slides.length-t.loopedSlides+r/2?(t.loopFix(),l=t.getSlideIndex((0,o.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(l)})):t.slideTo(l):l>t.slides.length-r?(t.loopFix(),l=t.getSlideIndex((0,o.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(l)})):t.slideTo(l)}else t.slideTo(l)}},loop:{loopCreate:function(e){let t=this,{params:i,slidesEl:s}=t;if(!i.loop||t.virtual&&t.params.virtual.enabled)return;let initSlides=()=>{let e=(0,o.e)(s,`.${i.slideClass}, swiper-slide`);e.forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})},r=t.grid&&i.grid&&i.grid.rows>1,l=i.slidesPerGroup*(r?i.grid.rows:1),a=t.slides.length%l!=0,n=r&&t.slides.length%i.grid.rows!=0,addBlankSlides=e=>{for(let s=0;s<e;s+=1){let e=t.isElement?(0,o.c)("swiper-slide",[i.slideBlankClass]):(0,o.c)("div",[i.slideClass,i.slideBlankClass]);t.slidesEl.append(e)}};if(a){if(i.loopAddBlankSlides){let e=l-t.slides.length%l;addBlankSlides(e),t.recalcSlides(),t.updateSlides()}else(0,o.u)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");initSlides()}else if(n){if(i.loopAddBlankSlides){let e=i.grid.rows-t.slides.length%i.grid.rows;addBlankSlides(e),t.recalcSlides(),t.updateSlides()}else(0,o.u)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");initSlides()}else initSlides();t.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:l,byController:a,byMousewheel:n}=void 0===e?{}:e,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:h,params:f}=d,{centeredSlides:m}=f;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&f.virtual.enabled){i&&(f.centeredSlides||0!==d.snapIndex?f.centeredSlides&&d.snapIndex<f.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,d.emit("loopFix");return}let g=f.slidesPerView;"auto"===g?g=d.slidesPerViewDynamic():(g=Math.ceil(parseFloat(f.slidesPerView,10)),m&&g%2==0&&(g+=1));let v=f.slidesPerGroupAuto?g:f.slidesPerGroup,w=v;w%v!=0&&(w+=v-w%v),w+=f.loopAdditionalSlides,d.loopedSlides=w;let b=d.grid&&f.grid&&f.grid.rows>1;c.length<g+w?(0,o.u)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&"row"===f.grid.fill&&(0,o.u)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let S=[],y=[],E=d.activeIndex;void 0===l?l=d.getSlideIndex(c.filter(e=>e.classList.contains(f.slideActiveClass))[0]):E=l;let T="next"===s||!s,x="prev"===s||!s,C=0,P=0,M=b?Math.ceil(c.length/f.grid.rows):c.length,k=b?c[l].column:l,L=k+(m&&void 0===r?-g/2+.5:0);if(L<w){C=Math.max(w-L,v);for(let e=0;e<w-L;e+=1){let t=e-Math.floor(e/M)*M;if(b){let e=M-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&S.push(t)}else S.push(M-t-1)}}else if(L+g>M-w){P=Math.max(L-(M-2*w),v);for(let e=0;e<P;e+=1){let t=e-Math.floor(e/M)*M;b?c.forEach((e,i)=>{e.column===t&&y.push(i)}):y.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),x&&S.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.prepend(c[e]),c[e].swiperLoopMoveDOM=!1}),T&&y.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.append(c[e]),c[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===f.slidesPerView?d.updateSlides():b&&(S.length>0&&x||y.length>0&&T)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),f.watchSlidesProgress&&d.updateSlidesOffset(),i){if(S.length>0&&x){if(void 0===t){let e=d.slidesGrid[E],t=d.slidesGrid[E+C],i=t-e;n?d.setTranslate(d.translate-i):(d.slideTo(E+Math.ceil(C),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-i,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-i))}else if(r){let e=b?S.length/f.grid.rows:S.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(y.length>0&&T){if(void 0===t){let e=d.slidesGrid[E],t=d.slidesGrid[E-P],i=t-e;n?d.setTranslate(d.translate-i):(d.slideTo(E-P,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-i,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-i))}else{let e=b?y.length/f.grid.rows:y.length;d.slideTo(d.activeIndex-e,0,!1,!0)}}}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!a){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:l,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===f.slidesPerView&&i})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===f.slidesPerView&&i})}d.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{let t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;i[t]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=onTouchStart.bind(this),this.onTouchMove=onTouchMove.bind(this),this.onTouchEnd=onTouchEnd.bind(this),this.onDocumentTouchStart=onDocumentTouchStart.bind(this),e.cssMode&&(this.onScroll=onScroll.bind(this)),this.onClick=onClick.bind(this),this.onLoad=onLoad.bind(this),events(this,"on")},detachEvents:function(){events(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,l=s.breakpoints;if(!l||l&&0===Object.keys(l).length)return;let a=e.getBreakpoint(l,e.params.breakpointsBase,e.el);if(!a||e.currentBreakpoint===a)return;let n=a in l?l[a]:void 0,d=n||e.originalParams,c=isGridEnabled(e,s),p=isGridEnabled(e,d),u=e.params.grabCursor,h=d.grabCursor,f=s.enabled;c&&!p?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!c&&p&&(r.classList.add(`${s.containerModifierClass}grid`),(d.grid.fill&&"column"===d.grid.fill||!d.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),u&&!h?e.unsetGrabCursor():!u&&h&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===d[t])return;let i=s[t]&&s[t].enabled,r=d[t]&&d[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let m=d.direction&&d.direction!==s.direction,g=s.loop&&(d.slidesPerView!==s.slidesPerView||m),v=s.loop;m&&i&&e.changeDirection(),(0,o.w)(e.params,d);let w=e.params.enabled,b=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!w?e.disable():!f&&w&&e.enable(),e.currentBreakpoint=a,e.emit("_beforeBreakpoint",d),i&&(g?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!v&&b?(e.loopCreate(t),e.updateSlides()):v&&!b&&e.loopDestroy()),e.emit("breakpoint",d)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=(0,n.a)(),l="window"===t?r.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>{if("string"==typeof e&&0===e.indexOf("@")){let t=parseFloat(e.substr(1));return{value:l*t,point:e}}return{value:e,point:e}});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:l,value:n}=a[e];"window"===t?r.matchMedia(`(min-width: ${n}px)`).matches&&(s=l):n<=i.clientWidth&&(s=l)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,l=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},p={};let Swiper=class Swiper{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=(0,o.w)({},t),e&&!t.el&&(t.el=e);let a=(0,n.g)();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){let e=[];return a.querySelectorAll(t.el).forEach(i=>{let s=(0,o.w)({},t,{el:i});e.push(new Swiper(s))}),e}let c=this;c.__swiper__=!0,c.support=getSupport(),c.device=getDevice({userAgent:t.userAgent}),c.browser=(l||(l=function(){let e=(0,n.a)(),t=getDevice(),i=!1;function isSafari(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(isSafari()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let s=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=isSafari(),l=r||s&&t.ios;return{isSafari:i||r,needPerspectiveFix:i,need3dFix:l,isWebView:s}}()),l),c.eventsListeners={},c.eventsAnyListeners=[],c.modules=[...c.__modules__],t.modules&&Array.isArray(t.modules)&&c.modules.push(...t.modules);let u={};c.modules.forEach(e=>{var i;e({params:t,swiper:c,extendParams:(i=t,function(e){void 0===e&&(e={});let t=Object.keys(e)[0],s=e[t];if("object"!=typeof s||null===s||(!0===i[t]&&(i[t]={enabled:!0}),"navigation"===t&&i[t]&&i[t].enabled&&!i[t].prevEl&&!i[t].nextEl&&(i[t].auto=!0),["pagination","scrollbar"].indexOf(t)>=0&&i[t]&&i[t].enabled&&!i[t].el&&(i[t].auto=!0),!(t in i&&"enabled"in s))){(0,o.w)(u,e);return}"object"!=typeof i[t]||"enabled"in i[t]||(i[t].enabled=!0),i[t]||(i[t]={enabled:!1}),(0,o.w)(u,e)}),on:c.on.bind(c),once:c.once.bind(c),off:c.off.bind(c),emit:c.emit.bind(c)})});let h=(0,o.w)({},d,u);return c.params=(0,o.w)({},h,p,t),c.originalParams=(0,o.w)({},c.params),c.passedParams=(0,o.w)({},t),c.params&&c.params.on&&Object.keys(c.params.on).forEach(e=>{c.on(e,c.params.on[e])}),c.params&&c.params.onAny&&c.onAny(c.params.onAny),Object.assign(c,{enabled:c.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===c.params.direction,isVertical:()=>"vertical"===c.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:c.params.allowSlideNext,allowSlidePrev:c.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:c.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:c.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),c.emit("_swiper"),c.params.init&&c.init(),c}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=(0,o.e)(t,`.${i.slideClass}, swiper-slide`),r=(0,o.h)(s[0]);return(0,o.h)(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>1*t.getAttribute("data-swiper-slide-index")===e)[0])}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,o.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=this.maxTranslate(),r=(s-i)*e+i;this.translateTo(r,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:l,size:a,activeIndex:n}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[n]?Math.ceil(s[n].swiperSlideSize):0;for(let i=n+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),o+=1,t>a&&(e=!0));for(let i=n-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,o+=1,t>a&&(e=!0))}else if("current"===e)for(let e=n+1;e<s.length;e+=1){let i=t?r[e]+l[e]-r[n]<a:r[e]-r[n]<a;i&&(o+=1)}else for(let e=n-1;e>=0;e-=1){let t=r[n]-r[e]<a;t&&(o+=1)}return o}update(){let e;let t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function setTranslate(){let e=t.rtlTranslate?-1*t.translate:t.translate,i=Math.min(Math.max(e,t.maxTranslate()),t.minTranslate());t.setTranslate(i),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&processLazyPreloader(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)setTranslate(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||setTranslate()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let getWrapperSelector=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,s=(()=>{if(i&&i.shadowRoot&&i.shadowRoot.querySelector){let e=i.shadowRoot.querySelector(getWrapperSelector());return e}return(0,o.e)(i,getWrapperSelector())[0]})();return!s&&t.params.createElements&&(s=(0,o.c)("div",t.params.wrapperClass),i.append(s),(0,o.e)(i,`.${t.params.slideClass}`).forEach(e=>{s.append(e)})),Object.assign(t,{el:i,wrapperEl:s,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:s,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,o.p)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,o.p)(i,"direction")),wrongRTL:"-webkit-box"===(0,o.p)(s,"display")}),!0}init(e){let t=this;if(t.initialized)return t;let i=t.mount(e);if(!1===i)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach(e=>{e.complete?processLazyPreloader(t,e):e.addEventListener("load",e=>{processLazyPreloader(t,e.target)})}),preload(t),t.initialized=!0,preload(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:l,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),l&&l.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,o.x)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,o.w)(p,e)}static get extendedDefaults(){return p}static get defaults(){return d}static installModule(e){Swiper.prototype.__modules__||(Swiper.prototype.__modules__=[]);let t=Swiper.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>Swiper.installModule(e)):Swiper.installModule(e),Swiper}};Object.keys(c).forEach(e=>{Object.keys(c[e]).forEach(t=>{Swiper.prototype[t]=c[e][t]})}),Swiper.use([function(e){let{swiper:t,on:i,emit:s}=e,r=(0,n.a)(),l=null,a=null,resizeHandler=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},createObserver=()=>{t&&!t.destroyed&&t.initialized&&(l=new ResizeObserver(e=>{a=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,l=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:a}=e;a&&a!==t.el||(r=s?s.width:(i[0]||i).inlineSize,l=s?s.height:(i[0]||i).blockSize)}),(r!==i||l!==s)&&resizeHandler()})})).observe(t.el)},removeObserver=()=>{a&&r.cancelAnimationFrame(a),l&&l.unobserve&&t.el&&(l.unobserve(t.el),l=null)},orientationChangeHandler=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver){createObserver();return}r.addEventListener("resize",resizeHandler),r.addEventListener("orientationchange",orientationChangeHandler)}),i("destroy",()=>{removeObserver(),r.removeEventListener("resize",resizeHandler),r.removeEventListener("orientationchange",orientationChangeHandler)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,l=[],a=(0,n.a)(),attach=function(e,i){void 0===i&&(i={});let s=a.MutationObserver||a.WebkitMutationObserver,n=new s(e=>{if(t.__preventObserver__)return;if(1===e.length){r("observerUpdate",e[0]);return}let observerUpdate=function(){r("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(observerUpdate):a.setTimeout(observerUpdate,0)});n.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),l.push(n)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,o.a)(t.hostEl);for(let t=0;t<e.length;t+=1)attach(e[t])}attach(t.hostEl,{childList:t.params.observeSlideChildren}),attach(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{l.forEach(e=>{e.disconnect()}),l.splice(0,l.length)})}]);let u=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function isObject(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function extend(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:isObject(t[i])&&isObject(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:extend(e[i],t[i]):e[i]=t[i]})}function needsNavigation(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function needsPagination(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function needsScrollbar(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function uniqueClasses(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let updateOnVirtualData=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function isChildSwiperSlide(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function useIsomorphicLayoutEffect(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let h=(0,a.createContext)(null),f=(0,a.createContext)(null),m=(0,a.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:l="div",children:n,onSwiper:o,...c}=void 0===e?{}:e,p=!1,[h,m]=(0,a.useState)("swiper"),[g,v]=(0,a.useState)(null),[w,b]=(0,a.useState)(!1),S=(0,a.useRef)(!1),y=(0,a.useRef)(null),E=(0,a.useRef)(null),T=(0,a.useRef)(null),x=(0,a.useRef)(null),C=(0,a.useRef)(null),P=(0,a.useRef)(null),M=(0,a.useRef)(null),k=(0,a.useRef)(null),{params:L,passedParams:O,rest:_,events:I}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};extend(i,d),i._emitClasses=!0,i.init=!1;let l={},a=u.map(e=>e.replace(/_/,"")),n=Object.assign({},e);return Object.keys(n).forEach(n=>{void 0!==e[n]&&(a.indexOf(n)>=0?isObject(e[n])?(i[n]={},r[n]={},extend(i[n],e[n]),extend(r[n],e[n])):(i[n]=e[n],r[n]=e[n]):0===n.search(/on[A-Z]/)&&"function"==typeof e[n]?t?s[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:i.on[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:l[n]=e[n])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:l,events:s}}(c),{slides:z,slots:A}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(isChildSwiperSlide(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function processChildren(e){let t=[];return a.Children.toArray(e).forEach(e=>{isChildSwiperSlide(e)?t.push(e):e.props&&e.props.children&&processChildren(e.props.children).forEach(e=>t.push(e))}),t}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(n),onBeforeBreakpoint=()=>{b(!w)};Object.assign(L.on,{_containerClasses(e,t){m(t)}});let initSwiper=()=>{Object.assign(L.on,I),p=!0;let e={...L};if(delete e.wrapperClass,E.current=new Swiper(e),E.current.virtual&&E.current.params.virtual.enabled){E.current.virtual.slides=z;let e={cache:!1,slides:z,renderExternal:v,renderExternalUpdate:!1};extend(E.current.params.virtual,e),extend(E.current.originalParams.virtual,e)}};y.current||initSwiper(),E.current&&E.current.on("_beforeBreakpoint",onBeforeBreakpoint);let attachEvents=()=>{!p&&I&&E.current&&Object.keys(I).forEach(e=>{E.current.on(e,I[e])})},detachEvents=()=>{I&&E.current&&Object.keys(I).forEach(e=>{E.current.off(e,I[e])})};return(0,a.useEffect)(()=>()=>{E.current&&E.current.off("_beforeBreakpoint",onBeforeBreakpoint)}),(0,a.useEffect)(()=>{!S.current&&E.current&&(E.current.emitSlidesClasses(),S.current=!0)}),useIsomorphicLayoutEffect(()=>{if(t&&(t.current=y.current),y.current)return E.current.destroyed&&initSwiper(),function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:l,scrollbarEl:a,swiper:n}=e;needsNavigation(t)&&s&&r&&(n.params.navigation.nextEl=s,n.originalParams.navigation.nextEl=s,n.params.navigation.prevEl=r,n.originalParams.navigation.prevEl=r),needsPagination(t)&&l&&(n.params.pagination.el=l,n.originalParams.pagination.el=l),needsScrollbar(t)&&a&&(n.params.scrollbar.el=a,n.originalParams.scrollbar.el=a),n.init(i)}({el:y.current,nextEl:C.current,prevEl:P.current,paginationEl:M.current,scrollbarEl:k.current,swiper:E.current},L),o&&!E.current.destroyed&&o(E.current),()=>{E.current&&!E.current.destroyed&&E.current.destroy(!0,!1)}},[]),useIsomorphicLayoutEffect(()=>{attachEvents();let e=function(e,t,i,s,r){let l=[];if(!t)return l;let addKey=e=>{0>l.indexOf(e)&&l.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&addKey("children"),s.length!==i.length&&addKey("children")}let a=u.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,""));return a.forEach(i=>{if(i in e&&i in t){if(isObject(e[i])&&isObject(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?addKey(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&addKey(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&addKey(i)}))}else e[i]!==t[i]&&addKey(i)}}),l}(O,T.current,z,x.current,e=>e.key);return T.current=O,x.current=z,e.length&&E.current&&!E.current.destroyed&&function(e){let t,i,s,r,l,a,n,o,{swiper:d,slides:c,passedParams:p,changedParams:u,nextEl:h,prevEl:f,scrollbarEl:m,paginationEl:g}=e,v=u.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:w,pagination:b,navigation:S,scrollbar:y,virtual:E,thumbs:T}=d;u.includes("thumbs")&&p.thumbs&&p.thumbs.swiper&&w.thumbs&&!w.thumbs.swiper&&(t=!0),u.includes("controller")&&p.controller&&p.controller.control&&w.controller&&!w.controller.control&&(i=!0),u.includes("pagination")&&p.pagination&&(p.pagination.el||g)&&(w.pagination||!1===w.pagination)&&b&&!b.el&&(s=!0),u.includes("scrollbar")&&p.scrollbar&&(p.scrollbar.el||m)&&(w.scrollbar||!1===w.scrollbar)&&y&&!y.el&&(r=!0),u.includes("navigation")&&p.navigation&&(p.navigation.prevEl||f)&&(p.navigation.nextEl||h)&&(w.navigation||!1===w.navigation)&&S&&!S.prevEl&&!S.nextEl&&(l=!0);let destroyModule=e=>{d[e]&&(d[e].destroy(),"navigation"===e?(d.isElement&&(d[e].prevEl.remove(),d[e].nextEl.remove()),w[e].prevEl=void 0,w[e].nextEl=void 0,d[e].prevEl=void 0,d[e].nextEl=void 0):(d.isElement&&d[e].el.remove(),w[e].el=void 0,d[e].el=void 0))};if(u.includes("loop")&&d.isElement&&(w.loop&&!p.loop?a=!0:!w.loop&&p.loop?n=!0:o=!0),v.forEach(e=>{if(isObject(w[e])&&isObject(p[e]))Object.assign(w[e],p[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in p[e]&&!p[e].enabled&&destroyModule(e);else{let t=p[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&destroyModule(e):w[e]=p[e]}}),v.includes("controller")&&!i&&d.controller&&d.controller.control&&w.controller&&w.controller.control&&(d.controller.control=w.controller.control),u.includes("children")&&c&&E&&w.virtual.enabled?(E.slides=c,E.update(!0)):u.includes("virtual")&&E&&w.virtual.enabled&&(c&&(E.slides=c),E.update(!0)),u.includes("children")&&c&&w.loop&&(o=!0),t){let e=T.init();e&&T.update(!0)}i&&(d.controller.control=w.controller.control),s&&(d.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-pagination"),g.part.add("pagination"),d.el.appendChild(g)),g&&(w.pagination.el=g),b.init(),b.render(),b.update()),r&&(d.isElement&&(!m||"string"==typeof m)&&((m=document.createElement("div")).classList.add("swiper-scrollbar"),m.part.add("scrollbar"),d.el.appendChild(m)),m&&(w.scrollbar.el=m),y.init(),y.updateSize(),y.setTranslate()),l&&(d.isElement&&(h&&"string"!=typeof h||((h=document.createElement("div")).classList.add("swiper-button-next"),h.innerHTML=d.hostEl.constructor.nextButtonSvg,h.part.add("button-next"),d.el.appendChild(h)),f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-prev"),f.innerHTML=d.hostEl.constructor.prevButtonSvg,f.part.add("button-prev"),d.el.appendChild(f))),h&&(w.navigation.nextEl=h),f&&(w.navigation.prevEl=f),S.init(),S.update()),u.includes("allowSlideNext")&&(d.allowSlideNext=p.allowSlideNext),u.includes("allowSlidePrev")&&(d.allowSlidePrev=p.allowSlidePrev),u.includes("direction")&&d.changeDirection(p.direction,!1),(a||o)&&d.loopDestroy(),(n||o)&&d.loopCreate(),d.update()}({swiper:E.current,slides:z,passedParams:O,changedParams:e,nextEl:C.current,prevEl:P.current,scrollbarEl:k.current,paginationEl:M.current}),()=>{detachEvents()}}),useIsomorphicLayoutEffect(()=>{updateOnVirtualData(E.current)},[g]),a.createElement(r,_extends({ref:y,className:uniqueClasses(`${h}${s?` ${s}`:""}`)},_),a.createElement(f.Provider,{value:E.current},A["container-start"],a.createElement(l,{className:(void 0===(i=L.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},A["wrapper-start"],L.virtual?function(e,t,i){if(!i)return null;let getSlideIndex=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},s=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:r,to:l}=i,n=e.params.loop?-t.length:0,o=e.params.loop?2*t.length:t.length,d=[];for(let e=n;e<o;e+=1)e>=r&&e<=l&&d.push(t[getSlideIndex(e)]);return d.map((t,i)=>a.cloneElement(t,{swiper:e,style:s,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(E.current,z,g):z.map((e,t)=>a.cloneElement(e,{swiper:E.current,swiperSlideIndex:t})),A["wrapper-end"]),needsNavigation(L)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:P,className:"swiper-button-prev"}),a.createElement("div",{ref:C,className:"swiper-button-next"})),needsScrollbar(L)&&a.createElement("div",{ref:k,className:"swiper-scrollbar"}),needsPagination(L)&&a.createElement("div",{ref:M,className:"swiper-pagination"}),A["container-end"]))});m.displayName="Swiper";let g=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:l,zoom:n,lazy:o,virtualIndex:d,swiperSlideIndex:c,...p}=void 0===e?{}:e,u=(0,a.useRef)(null),[f,m]=(0,a.useState)("swiper-slide"),[g,v]=(0,a.useState)(!1);function updateClasses(e,t,i){t===u.current&&m(i)}useIsomorphicLayoutEffect(()=>{if(void 0!==c&&(u.current.swiperSlideIndex=c),t&&(t.current=u.current),u.current&&l){if(l.destroyed){"swiper-slide"!==f&&m("swiper-slide");return}return l.on("_slideClass",updateClasses),()=>{l&&l.off("_slideClass",updateClasses)}}}),useIsomorphicLayoutEffect(()=>{l&&u.current&&!l.destroyed&&m(l.getSlideClasses(u.current))},[l]);let w={isActive:f.indexOf("swiper-slide-active")>=0,isVisible:f.indexOf("swiper-slide-visible")>=0,isPrev:f.indexOf("swiper-slide-prev")>=0,isNext:f.indexOf("swiper-slide-next")>=0},renderChildren=()=>"function"==typeof s?s(w):s;return a.createElement(i,_extends({ref:u,className:uniqueClasses(`${f}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{v(!0)}},p),n&&a.createElement(h.Provider,{value:w},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof n?n:void 0},renderChildren(),o&&!g&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!n&&a.createElement(h.Provider,{value:w},renderChildren(),o&&!g&&a.createElement("div",{className:"swiper-lazy-preloader"})))});g.displayName="SwiperSlide"}}]);