"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_details_related-products_tsx";
exports.ids = ["src_components_products_details_related-products_tsx"];
exports.modules = {

/***/ "./src/components/products/cards/card.tsx":
/*!************************************************!*\
  !*** ./src/components/products/cards/card.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Helium = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_helium_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/helium */ \"./src/components/products/cards/helium.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/helium\"\n        ]\n    }\n});\nconst Neon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_neon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/neon */ \"./src/components/products/cards/neon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/neon\"\n        ]\n    }\n}); // grocery-two\nconst Argon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_argon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/argon */ \"./src/components/products/cards/argon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/argon\"\n        ]\n    }\n}); // bakery\nconst Krypton = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_krypton_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/krypton */ \"./src/components/products/cards/krypton.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/krypton\"\n        ]\n    }\n});\nconst Xenon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_xenon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/xenon */ \"./src/components/products/cards/xenon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/xenon\"\n        ]\n    }\n}); // furniture-two\nconst Radon = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"src_components_products_cards_radon_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/products/cards/radon */ \"./src/components/products/cards/radon.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\components\\\\products\\\\cards\\\\card.tsx -> \" + \"@/components/products/cards/radon\"\n        ]\n    }\n}); // Book\nconst MAP_PRODUCT_TO_CARD = {\n    neon: Neon,\n    helium: Helium,\n    argon: Argon,\n    krypton: Krypton,\n    xenon: Xenon,\n    radon: Radon\n};\nconst ProductCard = ({ product, className, ...props })=>{\n    const Component = product?.type?.settings?.productCard ? MAP_PRODUCT_TO_CARD[product?.type?.settings?.productCard] : Helium;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        product: product,\n        ...props,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/cards/card.tsx\n");

/***/ }),

/***/ "./src/components/products/details/related-products.tsx":
/*!**************************************************************!*\
  !*** ./src/components/products/details/related-products.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _cards_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cards/card */ \"./src/components/products/cards/card.tsx\");\n\n\n\n\nconst RelatedProducts = ({ products, currentProductId, gridClassName })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-6 text-lg font-semibold tracking-tight text-heading\",\n                children: t(\"text-related-products\")\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4\", gridClassName),\n                children: products?.map((item, idx)=>{\n                    if (currentProductId === item.id) {\n                        return null;\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_cards_card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        product: item,\n                        cardType: item?.type?.slug\n                    }, idx, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\related-products.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n// <motion.div key={idx}>\n{\n/* {renderProductCard(\r\n    item,\r\n    \"!shadow-none border border-border-200 hover:!border-border-200 border-opacity-70\"\r\n  )} */ }// </motion.div>\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RelatedProducts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/details/related-products.tsx\n");

/***/ })

};
;