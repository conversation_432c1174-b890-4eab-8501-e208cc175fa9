{"version": 3, "file": "encoding-factory.js", "sourceRoot": "", "sources": ["../../../src/encoding/encoding-factory.ts"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,uCAAuC;AAEvC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC7E,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAGpF,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAGjE,EAAE;AACF,YAAY;AACZ,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,2CAA2C;AAC3C,oCAAoC;AACpC,EAAE;AAEF,EAAE;AACF,iBAAiB;AACjB,EAAE;AAEF,mCAAmC;AAEnC,EAAE;AACF,eAAe;AACf,EAAE;AAEF,iCAAiC;AAEjC,EAAE;AACF,aAAa;AACb,EAAE;AAEF,+BAA+B;AAE/B,MAAM,eAAe,GAAG,kBAAkB,EAAE,CAAC;AAwB7C,8DAA8D;AAC9D,kEAAkE;AAClE,iCAAiC;AAEjC,kEAAkE;AAClE,iCAAiC;AAE/B,EAAE;AACF,mCAAmC;AACnC,EAAE;AAEF,2BAA2B;AAE3B,2BAA2B;AAE7B,MAAM,CAAC,MAAM,QAAQ,GAAa;IAEhC,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,OAAO,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC;IAEnE,EAAE;IACF,uDAAuD;IACvD,EAAE;IAEF,WAAW;IAEX,qBAAqB;IACrB,sCAAsC;IAEtC,qBAAqB;IACrB,4DAA4D;IAC5D,wCAAwC;IACxC,KAAK,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC;IAE1E,eAAe;IACf,yBAAyB;IAEzB,yBAAyB;IAEzB,wCAAwC;IACxC,SAAS,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IAIxE,EAAE;IACF,wDAAwD;IACxD,EAAE;IAEF,YAAY;IAEZ,sBAAsB;IAEtB,sBAAsB;IAEtB,wCAAwC;IACxC,MAAM,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC;IAGlE,EAAE;IACF,2CAA2C;IAC3C,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC;IAErE,mBAAmB;IAEnB,6BAA6B;IAE7B,6BAA6B;IAE7B,wCAAwC;IACxC,aAAa,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC;IAE9E,iBAAiB;IAEjB,2BAA2B;IAE3B,2BAA2B;IAE3B,wCAAwC;IACxC,WAAW,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC;IAE3E,EAAE;IACF,yCAAyC;IACzC,EAAE;IAEF,cAAc;IAEd,wBAAwB;IAExB,wBAAwB;IAExB,wCAAwC;IACxC,QAAQ,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC;IAGrE,EAAE;IACF,qCAAqC;IACrC,EAAE;IAEF,mBAAmB;IAEnB,qCAAqC;IAErC,uDAAuD;IAEvD,+BAA+B;IAE/B,+BAA+B;IAE/B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7E,0BAA0B;IAE1B,gBAAgB;IAChB,0BAA0B;IAC1B,wCAAwC;IACxC,UAAU,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC;IAC9E,0BAA0B;IAE1B,sBAAsB;IAEtB,gCAAgC;IAEhC,gCAAgC;IAEhC,wCAAwC;IACxC,gBAAgB,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAC;CACrF,CAAA;AAID,MAAM,CAAC,MAAM,QAAQ,GAAa;IAChC,wCAAwC;IACxC,OAAO,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC;IACnE,wCAAwC;IACxC,KAAK,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IACpE,wCAAwC;IACxC,SAAS,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IACxE,wCAAwC;IACxC,MAAM,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC;IAClE,wCAAwC;IACxC,QAAQ,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC;IACrE,wCAAwC;IACxC,aAAa,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC;IAC9E,wCAAwC;IACxC,WAAW,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC;IAC3E,wCAAwC;IACxC,QAAQ,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC;IACrE,wCAAwC;IACxC,UAAU,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7E,wCAAwC;IACxC,UAAU,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC;IAC9E,wCAAwC;IACxC,gBAAgB,EAAE,CAAC,OAA4B,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAC;CACrF,CAAA;AAGD,IAAI,eAAe,EAAE;IACnB,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;QAClC,IAAI,QAAQ,CAAC,OAAO,KAAK,8BAA8B;YACrD,OAAO;QACT,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;YAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACtC,wCAAwC;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,iBAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;YACF,wCAAwC;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,OAA4B;gBACrD,OAAO,IAAI,iBAAiB,CAAC,GAAe,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}