"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3928],{92072:function(e,t,o){var r=o(85893),n=o(93967),s=o.n(n),a=o(98388);t.Z=e=>{let{className:t,...o}=e;return(0,r.jsx)("div",{className:(0,a.m6)(s()("rounded bg-light p-5 shadow md:p-8",t)),...o})}},41312:function(e,t,o){o.d(t,{_:function(){return DownloadIcon}});var r=o(85893);let DownloadIcon=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 548.176 548.176",...t,children:(0,r.jsx)("path",{d:"M524.326 297.352c-15.896-19.89-36.21-32.782-60.959-38.684 7.81-11.8 11.704-24.934 11.704-39.399 0-20.177-7.139-37.401-21.409-51.678-14.273-14.272-31.498-21.411-51.675-21.411-18.083 0-33.879 5.901-47.39 17.703-11.225-27.41-29.171-49.393-53.817-65.95-24.646-16.562-51.818-24.842-81.514-24.842-40.349 0-74.802 14.279-103.353 42.83-28.553 28.544-42.825 62.999-42.825 103.351 0 2.474.191 6.567.571 12.275-22.459 10.469-40.349 26.171-53.676 47.106C6.661 299.594 0 322.43 0 347.179c0 35.214 12.517 65.329 37.544 90.358 25.028 25.037 55.15 37.548 90.362 37.548h310.636c30.259 0 56.096-10.711 77.512-32.12 21.413-21.409 32.121-47.246 32.121-77.516-.003-25.505-7.952-48.201-23.849-68.097zm-161.731 10.992L262.38 408.565c-1.711 1.707-3.901 2.566-6.567 2.566-2.664 0-4.854-.859-6.567-2.566L148.75 308.063c-1.713-1.711-2.568-3.901-2.568-6.567 0-2.474.9-4.616 2.708-6.423 1.812-1.808 3.949-2.711 6.423-2.711h63.954V191.865c0-2.474.905-4.616 2.712-6.427 1.809-1.805 3.949-2.708 6.423-2.708h54.823c2.478 0 4.609.9 6.427 2.708 1.804 1.811 2.707 3.953 2.707 6.427v100.497h63.954c2.665 0 4.855.855 6.563 2.566 1.714 1.711 2.562 3.901 2.562 6.567 0 2.294-.944 4.569-2.843 6.849z",fill:"currentColor"})})}},70665:function(e,t,o){o.d(t,{r:function(){return UploadIcon}});var r=o(85893);let UploadIcon=e=>{let{color:t="currentColor",width:o="41px",height:n="30px",...s}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:o,height:n,viewBox:"0 0 40.909 30",...s,children:(0,r.jsx)("g",{transform:"translate(0 -73.091)",children:(0,r.jsx)("path",{"data-name":"Path 2125",d:"M39.129,89.827A8.064,8.064,0,0,0,34.58,86.94,5.446,5.446,0,0,0,30,78.546a5.207,5.207,0,0,0-3.537,1.321,10.921,10.921,0,0,0-10.1-6.776,10.511,10.511,0,0,0-7.713,3.2A10.508,10.508,0,0,0,5.454,84q0,.277.043.916A9.528,9.528,0,0,0,0,93.546a9.193,9.193,0,0,0,2.8,6.743,9.191,9.191,0,0,0,6.744,2.8H32.728a8.172,8.172,0,0,0,6.4-13.264Zm-12.06-.575a.656.656,0,0,1-.479.2H21.818v7.5a.691.691,0,0,1-.681.681H17.045a.691.691,0,0,1-.682-.681v-7.5H11.59a.655.655,0,0,1-.681-.681.8.8,0,0,1,.213-.512L18.6,80.783a.722.722,0,0,1,.98,0l7.5,7.5a.663.663,0,0,1,.191.49A.656.656,0,0,1,27.07,89.252Z",transform:"translate(0)",fill:"currentColor"})})})}},3928:function(e,t,o){o.r(t),o.d(t,{default:function(){return import_export_modal}});var r=o(85893),n=o(92072),s=o(41312),a=o(75814),u=o(5233),i=o(11163),c=o(91297),l=o(30042),d=o(97507);function ImportProducts(){let{t:e}=(0,u.$G)("common"),{query:{shop:t}}=(0,i.useRouter)(),{data:o}=(0,l.DZ)({slug:t}),n=null==o?void 0:o.id,{mutate:s,isLoading:a}=(0,d.Lq)(),handleDrop=async e=>{e.length&&s({shop_id:n,csv:e[0]})};return(0,r.jsx)(c.Z,{onDrop:handleDrop,loading:a,title:e("text-import-products")})}function ImportVariationOptions(){let{t:e}=(0,u.$G)(),{query:{shop:t}}=(0,i.useRouter)(),{data:o}=(0,l.DZ)({slug:t}),n=null==o?void 0:o.id,{mutate:s,isLoading:a}=(0,d.o$)(),handleDrop=async e=>{e.length&&s({shop_id:n,csv:e[0]})};return(0,r.jsx)(c.Z,{onDrop:handleDrop,loading:a,title:e("text-import-product-variations")})}var p=o(83454),import_export_modal=()=>{var e,t;let{data:o}=(0,a.X9)(),{t:i}=(0,u.$G)();return(0,r.jsxs)(n.Z,{className:"flex min-h-screen flex-col md:min-h-0",children:[(0,r.jsx)("div",{className:"mb-5 w-full",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:i("common:text-export-import")})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-5 md:grid-cols-4",children:[(0,r.jsx)(ImportProducts,{}),(0,r.jsx)(ImportVariationOptions,{}),(0,r.jsxs)("a",{href:"".concat(null==p?void 0:null===(e=p.env)||void 0===e?void 0:"http://localhost:9000/api","/export-products/").concat(o),target:"_blank",className:"flex h-36 cursor-pointer flex-col items-center justify-center rounded border-2 border-dashed border-border-base p-5 focus:border-accent-400 focus:outline-none",rel:"noreferrer",children:[(0,r.jsx)(s._,{className:"w-10 text-muted-light"}),(0,r.jsx)("span",{className:"mt-4 text-center text-sm font-semibold text-accent",children:i("common:text-export-products")})]}),(0,r.jsxs)("a",{href:"".concat(null==p?void 0:null===(t=p.env)||void 0===t?void 0:"http://localhost:9000/api","/export-variation-options/").concat(o),target:"_blank",className:"flex h-36 cursor-pointer flex-col items-center justify-center rounded border-2 border-dashed border-border-base p-5 focus:border-accent-400 focus:outline-none",rel:"noreferrer",children:[(0,r.jsx)(s._,{className:"w-10 text-muted-light"}),(0,r.jsx)("span",{className:"mt-4 text-center text-sm font-semibold text-accent",children:i("common:text-export-product-variations")})]})]})]})}},91297:function(e,t,o){o.d(t,{Z:function(){return ImportCsv}});var r=o(85893),n=o(70665),s=o(32512);function ImportCsv(e){let{onDrop:t,loading:o,title:a}=e,{getRootProps:u,getInputProps:i}=(0,s.uI)({accept:".csv",multiple:!1,onDrop:t});return(0,r.jsx)("section",{className:"upload",children:(0,r.jsxs)("div",{...u({className:"border-dashed border-2 border-border-base h-36 rounded flex flex-col justify-center items-center cursor-pointer focus:border-accent-400 focus:outline-none p-5"}),children:[(0,r.jsx)("input",{...i()}),o&&(0,r.jsx)("span",{className:"ms-2 h-[30px] w-[30px] animate-spin rounded-full border-2 border-t-2 border-transparent",style:{borderTopColor:"rgb(var(--color-accent))"}}),!o&&(0,r.jsx)(n.r,{className:"text-muted-light"}),(0,r.jsx)("p",{className:"mt-4 text-center text-sm text-body",children:(0,r.jsx)("span",{className:"font-semibold text-accent",children:a})})]})})}},97507:function(e,t,o){o.d(t,{JK:function(){return useImportAttributesMutation},Lq:function(){return useImportProductsMutation},o$:function(){return useImportVariationOptionsMutation}});var r=o(88767),n=o(47869),s=o(22920),a=o(5233),u=o(3737);let i={importCsv:async(e,t)=>{let o=new FormData;o.append("csv",null==t?void 0:t.csv),o.append("shop_id",null==t?void 0:t.shop_id);let r=await u.eN.post(e,o,{headers:{"Content-Type":"multipart/form-data"}});return r.data}},useImportAttributesMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)("common");return(0,r.useMutation)(e=>i.importCsv(n.P.IMPORT_ATTRIBUTES,e),{onSuccess:()=>{s.Am.success(t("common:attribute-imported-successfully"))},onError:e=>{var o;s.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))},onSettled:()=>{e.invalidateQueries(n.P.ATTRIBUTES)}})},useImportProductsMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)("common");return(0,r.useMutation)(e=>i.importCsv(n.P.IMPORT_PRODUCTS,e),{onSuccess:()=>{s.Am.success(t("common:product-imported-successfully"))},onError:e=>{var o;s.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))},onSettled:()=>{e.invalidateQueries(n.P.PRODUCTS)}})},useImportVariationOptionsMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)("common");return(0,r.useMutation)(e=>i.importCsv(n.P.IMPORT_VARIATION_OPTIONS,e),{onSuccess:()=>{s.Am.success(t("common:variation-options-imported-successfully"))},onError:e=>{var o;s.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))},onSettled:()=>{e.invalidateQueries(n.P.PRODUCTS)}})}},30042:function(e,t,o){o.d(t,{bg:function(){return useApproveShopMutation},TC:function(){return useCreateShopMutation},mj:function(){return useDisApproveShopMutation},T3:function(){return useInActiveShopsQuery},DZ:function(){return useShopQuery},uL:function(){return useShopsQuery},_3:function(){return useTransferShopOwnershipMutation},D9:function(){return useUpdateShopMutation}});var r=o(93345),n=o(97514),s=o(47869),a=o(16203),u=o(28597),i=o(5233),c=o(11163),l=o(88767),d=o(22920),p=o(3737),m=o(55191);let h={...(0,m.h)(s.P.SHOPS),get(e){let{slug:t}=e;return p.eN.get("".concat(s.P.SHOPS,"/").concat(t))},paginated:e=>{let{name:t,...o}=e;return p.eN.get(s.P.SHOPS,{searchJoin:"and",...o,search:p.eN.formatSearchParams({name:t})})},newOrInActiveShops:e=>{let{is_active:t,name:o,...r}=e;return p.eN.get(s.P.NEW_OR_INACTIVE_SHOPS,{searchJoin:"and",is_active:t,name:o,...r,search:p.eN.formatSearchParams({is_active:t,name:o})})},approve:e=>p.eN.post(s.P.APPROVE_SHOP,e),disapprove:e=>p.eN.post(s.P.DISAPPROVE_SHOP,e),transferShopOwnership:e=>p.eN.post(s.P.TRANSFER_SHOP_OWNERSHIP,e)},useApproveShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.approve,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useDisApproveShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.disapprove,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useCreateShopMutation=()=>{let e=(0,l.useQueryClient)(),t=(0,c.useRouter)();return(0,l.useMutation)(h.create,{onSuccess:()=>{let{permissions:e}=(0,a.WA)();if((0,a.Ft)(a.M$,e))return t.push(n.Z.adminMyShops);t.push(n.Z.dashboard)},onSettled:()=>{e.invalidateQueries(s.P.SHOPS)}})},useUpdateShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,c.useRouter)(),o=(0,l.useQueryClient)();return(0,l.useMutation)(h.update,{onSuccess:async o=>{await t.push("/".concat(null==o?void 0:o.slug,"/edit"),void 0,{locale:r.Config.defaultLanguage}),d.Am.success(e("common:successfully-updated"))},onSettled:()=>{o.invalidateQueries(s.P.SHOPS)}})},useTransferShopOwnershipMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.transferShopOwnership,{onSuccess:t=>{var o;d.Am.success("".concat(e("common:successfully-transferred")).concat(null===(o=t.owner)||void 0===o?void 0:o.name))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useShopQuery=(e,t)=>{let{slug:o}=e;return(0,l.useQuery)([s.P.SHOPS,{slug:o}],()=>h.get({slug:o}),t)},useShopsQuery=e=>{var t;let{data:o,error:r,isLoading:n}=(0,l.useQuery)([s.P.SHOPS,e],e=>{let{queryKey:t,pageParam:o}=e;return h.paginated(Object.assign({},t[1],o))},{keepPreviousData:!0});return{shops:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(o),error:r,loading:n}},useInActiveShopsQuery=e=>{var t;let{data:o,error:r,isLoading:n}=(0,l.useQuery)([s.P.NEW_OR_INACTIVE_SHOPS,e],e=>{let{queryKey:t,pageParam:o}=e;return h.newOrInActiveShops(Object.assign({},t[1],o))},{keepPreviousData:!0});return{shops:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(o),error:r,loading:n}}}}]);