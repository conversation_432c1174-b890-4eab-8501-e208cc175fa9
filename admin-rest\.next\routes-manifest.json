{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/attributes/[attributeId]/[action]", "regex": "^/attributes/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPattributeId": "nxtPattributeId", "nxtPaction": "nxtPaction"}, "namedRegex": "^/attributes/(?<nxtPattributeId>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/authors/[authorSlug]/[action]", "regex": "^/authors/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPauthorSlug": "nxtPauthorSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/authors/(?<nxtPauthorSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/categories/[categorySlug]/[action]", "regex": "^/categories/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcategorySlug": "nxtPcategorySlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/categories/(?<nxtPcategorySlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/coupons/[couponSlug]/[action]", "regex": "^/coupons/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcouponSlug": "nxtPcouponSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/coupons/(?<nxtPcouponSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/faqs/[id]/[action]", "regex": "^/faqs/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/faqs/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/flash-sale/vendor-request/[id]", "regex": "^/flash\\-sale/vendor\\-request/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/flash-sale/vendor-request/[id]/[action]", "regex": "^/flash\\-sale/vendor\\-request/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/flash-sale/[slug]", "regex": "^/flash\\-sale/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/flash\\-sale/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/flash-sale/[slug]/[action]", "regex": "^/flash\\-sale/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/flash\\-sale/(?<nxtPslug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/groups/[groupSlug]/[action]", "regex": "^/groups/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPgroupSlug": "nxtPgroupSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/groups/(?<nxtPgroupSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/manufacturers/[manufacturerSlug]/[action]", "regex": "^/manufacturers/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPmanufacturerSlug": "nxtPmanufacturerSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/manufacturers/(?<nxtPmanufacturerSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/message/[id]", "regex": "^/message/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/message/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/notify-logs/user/[id]", "regex": "^/notify\\-logs/user/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/notify\\-logs/user/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/orders/[orderId]", "regex": "^/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/products/[productSlug]/[action]", "regex": "^/products/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductSlug": "nxtPproductSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/products/(?<nxtPproductSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/refund-policies/[refundPolicySlug]", "regex": "^/refund\\-policies/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrefundPolicySlug": "nxtPrefundPolicySlug"}, "namedRegex": "^/refund\\-policies/(?<nxtPrefundPolicySlug>[^/]+?)(?:/)?$"}, {"page": "/refund-policies/[refundPolicySlug]/[action]", "regex": "^/refund\\-policies/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrefundPolicySlug": "nxtPrefundPolicySlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/refund\\-policies/(?<nxtPrefundPolicySlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/refund-reasons/[refundReasonSlug]/[action]", "regex": "^/refund\\-reasons/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrefundReasonSlug": "nxtPrefundReasonSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/refund\\-reasons/(?<nxtPrefundReasonSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/refunds/[refundId]", "regex": "^/refunds/([^/]+?)(?:/)?$", "routeKeys": {"nxtPrefundId": "nxtPrefundId"}, "namedRegex": "^/refunds/(?<nxtPrefundId>[^/]+?)(?:/)?$"}, {"page": "/reviews/[reviewId]", "regex": "^/reviews/([^/]+?)(?:/)?$", "routeKeys": {"nxtPreviewId": "nxtPreviewId"}, "namedRegex": "^/reviews/(?<nxtPreviewId>[^/]+?)(?:/)?$"}, {"page": "/shippings/edit/[id]", "regex": "^/shippings/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/shippings/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/shop-message/[id]", "regex": "^/shop\\-message/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/shop\\-message/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/shop-transfer/vendor/[transaction_identifier]", "regex": "^/shop\\-transfer/vendor/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtransaction_identifier": "nxtPtransaction_identifier"}, "namedRegex": "^/shop\\-transfer/vendor/(?<nxtPtransaction_identifier>[^/]+?)(?:/)?$"}, {"page": "/shop-transfer/[transaction_identifier]", "regex": "^/shop\\-transfer/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtransaction_identifier": "nxtPtransaction_identifier"}, "namedRegex": "^/shop\\-transfer/(?<nxtPtransaction_identifier>[^/]+?)(?:/)?$"}, {"page": "/store-notices/[id]", "regex": "^/store\\-notices/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/store\\-notices/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/store-notices/[id]/[action]", "regex": "^/store\\-notices/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/store\\-notices/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/tags/[tagSlug]/[action]", "regex": "^/tags/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtagSlug": "nxtPtagSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/tags/(?<nxtPtagSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/taxes/edit/[id]", "regex": "^/taxes/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/taxes/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/terms-and-conditions/[termSlug]", "regex": "^/terms\\-and\\-conditions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtermSlug": "nxtPtermSlug"}, "namedRegex": "^/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)(?:/)?$"}, {"page": "/terms-and-conditions/[termSlug]/[action]", "regex": "^/terms\\-and\\-conditions/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtermSlug": "nxtPtermSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/withdraws/[withdrawId]", "regex": "^/withdraws/([^/]+?)(?:/)?$", "routeKeys": {"nxtPwithdrawId": "nxtPwithdrawId"}, "namedRegex": "^/withdraws/(?<nxtPwithdrawId>[^/]+?)(?:/)?$"}, {"page": "/[shop]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)(?:/)?$"}, {"page": "/[shop]/attributes", "regex": "^/([^/]+?)/attributes(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/attributes(?:/)?$"}, {"page": "/[shop]/attributes/create", "regex": "^/([^/]+?)/attributes/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/attributes/create(?:/)?$"}, {"page": "/[shop]/attributes/[attributeId]/[action]", "regex": "^/([^/]+?)/attributes/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPattributeId": "nxtPattributeId", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/attributes/(?<nxtPattributeId>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/authors", "regex": "^/([^/]+?)/authors(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/authors(?:/)?$"}, {"page": "/[shop]/authors/create", "regex": "^/([^/]+?)/authors/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/authors/create(?:/)?$"}, {"page": "/[shop]/coupons", "regex": "^/([^/]+?)/coupons(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/coupons(?:/)?$"}, {"page": "/[shop]/coupons/create", "regex": "^/([^/]+?)/coupons/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/coupons/create(?:/)?$"}, {"page": "/[shop]/coupons/[couponSlug]/[action]", "regex": "^/([^/]+?)/coupons/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPcouponSlug": "nxtPcouponSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/coupons/(?<nxtPcouponSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/edit", "regex": "^/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/edit(?:/)?$"}, {"page": "/[shop]/faqs", "regex": "^/([^/]+?)/faqs(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/faqs(?:/)?$"}, {"page": "/[shop]/faqs/create", "regex": "^/([^/]+?)/faqs/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/faqs/create(?:/)?$"}, {"page": "/[shop]/faqs/[id]/[action]", "regex": "^/([^/]+?)/faqs/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/faqs/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/flash-sale", "regex": "^/([^/]+?)/flash\\-sale(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale(?:/)?$"}, {"page": "/[shop]/flash-sale/my-products", "regex": "^/([^/]+?)/flash\\-sale/my\\-products(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/my\\-products(?:/)?$"}, {"page": "/[shop]/flash-sale/vendor-request", "regex": "^/([^/]+?)/flash\\-sale/vendor\\-request(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request(?:/)?$"}, {"page": "/[shop]/flash-sale/vendor-request/create", "regex": "^/([^/]+?)/flash\\-sale/vendor\\-request/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/create(?:/)?$"}, {"page": "/[shop]/flash-sale/vendor-request/[id]", "regex": "^/([^/]+?)/flash\\-sale/vendor\\-request/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[shop]/flash-sale/vendor-request/[id]/[action]", "regex": "^/([^/]+?)/flash\\-sale/vendor\\-request/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/flash-sale/[slug]", "regex": "^/([^/]+?)/flash\\-sale/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/flash\\-sale/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[shop]/manufacturers", "regex": "^/([^/]+?)/manufacturers(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/manufacturers(?:/)?$"}, {"page": "/[shop]/manufacturers/create", "regex": "^/([^/]+?)/manufacturers/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/manufacturers/create(?:/)?$"}, {"page": "/[shop]/orders", "regex": "^/([^/]+?)/orders(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/orders(?:/)?$"}, {"page": "/[shop]/orders/transaction", "regex": "^/([^/]+?)/orders/transaction(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/orders/transaction(?:/)?$"}, {"page": "/[shop]/orders/[orderId]", "regex": "^/([^/]+?)/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPorderId": "nxtPorderId"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/[shop]/products", "regex": "^/([^/]+?)/products(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products(?:/)?$"}, {"page": "/[shop]/products/create", "regex": "^/([^/]+?)/products/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products/create(?:/)?$"}, {"page": "/[shop]/products/draft", "regex": "^/([^/]+?)/products/draft(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products/draft(?:/)?$"}, {"page": "/[shop]/products/inventory", "regex": "^/([^/]+?)/products/inventory(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products/inventory(?:/)?$"}, {"page": "/[shop]/products/product-stock", "regex": "^/([^/]+?)/products/product\\-stock(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products/product\\-stock(?:/)?$"}, {"page": "/[shop]/products/[productSlug]/[action]", "regex": "^/([^/]+?)/products/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPproductSlug": "nxtPproductSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/products/(?<nxtPproductSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/questions", "regex": "^/([^/]+?)/questions(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/questions(?:/)?$"}, {"page": "/[shop]/refunds", "regex": "^/([^/]+?)/refunds(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/refunds(?:/)?$"}, {"page": "/[shop]/refunds/[refundId]", "regex": "^/([^/]+?)/refunds/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPrefundId": "nxtPrefundId"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/refunds/(?<nxtPrefundId>[^/]+?)(?:/)?$"}, {"page": "/[shop]/reviews", "regex": "^/([^/]+?)/reviews(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/reviews(?:/)?$"}, {"page": "/[shop]/staffs", "regex": "^/([^/]+?)/staffs(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/staffs(?:/)?$"}, {"page": "/[shop]/staffs/create", "regex": "^/([^/]+?)/staffs/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/staffs/create(?:/)?$"}, {"page": "/[shop]/store-notices", "regex": "^/([^/]+?)/store\\-notices(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/store\\-notices(?:/)?$"}, {"page": "/[shop]/store-notices/create", "regex": "^/([^/]+?)/store\\-notices/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/store\\-notices/create(?:/)?$"}, {"page": "/[shop]/store-notices/[id]", "regex": "^/([^/]+?)/store\\-notices/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/store\\-notices/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[shop]/store-notices/[id]/[action]", "regex": "^/([^/]+?)/store\\-notices/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/store\\-notices/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/terms-and-conditions", "regex": "^/([^/]+?)/terms\\-and\\-conditions(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions(?:/)?$"}, {"page": "/[shop]/terms-and-conditions/create", "regex": "^/([^/]+?)/terms\\-and\\-conditions/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/create(?:/)?$"}, {"page": "/[shop]/terms-and-conditions/[termSlug]", "regex": "^/([^/]+?)/terms\\-and\\-conditions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPtermSlug": "nxtPtermSlug"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)(?:/)?$"}, {"page": "/[shop]/terms-and-conditions/[termSlug]/[action]", "regex": "^/([^/]+?)/terms\\-and\\-conditions/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPtermSlug": "nxtPtermSlug", "nxtPaction": "nxtPaction"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)/(?<nxtPaction>[^/]+?)(?:/)?$"}, {"page": "/[shop]/transfer-ownership", "regex": "^/([^/]+?)/transfer\\-ownership(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/transfer\\-ownership(?:/)?$"}, {"page": "/[shop]/withdraws", "regex": "^/([^/]+?)/withdraws(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/withdraws(?:/)?$"}, {"page": "/[shop]/withdraws/create", "regex": "^/([^/]+?)/withdraws/create(?:/)?$", "routeKeys": {"nxtPshop": "nxtPshop"}, "namedRegex": "^/(?<nxtPshop>[^/]+?)/withdraws/create(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/attributes", "regex": "^/attributes(?:/)?$", "routeKeys": {}, "namedRegex": "^/attributes(?:/)?$"}, {"page": "/authors", "regex": "^/authors(?:/)?$", "routeKeys": {}, "namedRegex": "^/authors(?:/)?$"}, {"page": "/authors/create", "regex": "^/authors/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/authors/create(?:/)?$"}, {"page": "/become-seller", "regex": "^/become\\-seller(?:/)?$", "routeKeys": {}, "namedRegex": "^/become\\-seller(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/categories/create", "regex": "^/categories/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories/create(?:/)?$"}, {"page": "/coupons", "regex": "^/coupons(?:/)?$", "routeKeys": {}, "namedRegex": "^/coupons(?:/)?$"}, {"page": "/coupons/create", "regex": "^/coupons/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/coupons/create(?:/)?$"}, {"page": "/faqs", "regex": "^/faqs(?:/)?$", "routeKeys": {}, "namedRegex": "^/faqs(?:/)?$"}, {"page": "/faqs/create", "regex": "^/faqs/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/faqs/create(?:/)?$"}, {"page": "/flash-sale", "regex": "^/flash\\-sale(?:/)?$", "routeKeys": {}, "namedRegex": "^/flash\\-sale(?:/)?$"}, {"page": "/flash-sale/create", "regex": "^/flash\\-sale/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/flash\\-sale/create(?:/)?$"}, {"page": "/flash-sale/vendor-request", "regex": "^/flash\\-sale/vendor\\-request(?:/)?$", "routeKeys": {}, "namedRegex": "^/flash\\-sale/vendor\\-request(?:/)?$"}, {"page": "/flash-sale/vendor-request/create", "regex": "^/flash\\-sale/vendor\\-request/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/flash\\-sale/vendor\\-request/create(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/groups", "regex": "^/groups(?:/)?$", "routeKeys": {}, "namedRegex": "^/groups(?:/)?$"}, {"page": "/groups/create", "regex": "^/groups/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/groups/create(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/logout", "regex": "^/logout(?:/)?$", "routeKeys": {}, "namedRegex": "^/logout(?:/)?$"}, {"page": "/manufacturers", "regex": "^/manufacturers(?:/)?$", "routeKeys": {}, "namedRegex": "^/manufacturers(?:/)?$"}, {"page": "/manufacturers/create", "regex": "^/manufacturers/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/manufacturers/create(?:/)?$"}, {"page": "/message", "regex": "^/message(?:/)?$", "routeKeys": {}, "namedRegex": "^/message(?:/)?$"}, {"page": "/my-shop", "regex": "^/my\\-shop(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-shop(?:/)?$"}, {"page": "/my-shops", "regex": "^/my\\-shops(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-shops(?:/)?$"}, {"page": "/new-shops", "regex": "^/new\\-shops(?:/)?$", "routeKeys": {}, "namedRegex": "^/new\\-shops(?:/)?$"}, {"page": "/notice", "regex": "^/notice(?:/)?$", "routeKeys": {}, "namedRegex": "^/notice(?:/)?$"}, {"page": "/notify-logs", "regex": "^/notify\\-logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/notify\\-logs(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/orders/checkout", "regex": "^/orders/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders/checkout(?:/)?$"}, {"page": "/orders/create", "regex": "^/orders/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders/create(?:/)?$"}, {"page": "/orders/transaction", "regex": "^/orders/transaction(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders/transaction(?:/)?$"}, {"page": "/owner-message", "regex": "^/owner\\-message(?:/)?$", "routeKeys": {}, "namedRegex": "^/owner\\-message(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/products/draft", "regex": "^/products/draft(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/draft(?:/)?$"}, {"page": "/products/inventory", "regex": "^/products/inventory(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/inventory(?:/)?$"}, {"page": "/products/product-stock", "regex": "^/products/product\\-stock(?:/)?$", "routeKeys": {}, "namedRegex": "^/products/product\\-stock(?:/)?$"}, {"page": "/profile-update", "regex": "^/profile\\-update(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile\\-update(?:/)?$"}, {"page": "/questions", "regex": "^/questions(?:/)?$", "routeKeys": {}, "namedRegex": "^/questions(?:/)?$"}, {"page": "/refund-policies", "regex": "^/refund\\-policies(?:/)?$", "routeKeys": {}, "namedRegex": "^/refund\\-policies(?:/)?$"}, {"page": "/refund-policies/create", "regex": "^/refund\\-policies/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/refund\\-policies/create(?:/)?$"}, {"page": "/refund-reasons", "regex": "^/refund\\-reasons(?:/)?$", "routeKeys": {}, "namedRegex": "^/refund\\-reasons(?:/)?$"}, {"page": "/refund-reasons/create", "regex": "^/refund\\-reasons/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/refund\\-reasons/create(?:/)?$"}, {"page": "/refunds", "regex": "^/refunds(?:/)?$", "routeKeys": {}, "namedRegex": "^/refunds(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reviews", "regex": "^/reviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/reviews(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/settings/company-information", "regex": "^/settings/company\\-information(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/company\\-information(?:/)?$"}, {"page": "/settings/events", "regex": "^/settings/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/events(?:/)?$"}, {"page": "/settings/maintenance", "regex": "^/settings/maintenance(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/maintenance(?:/)?$"}, {"page": "/settings/payment", "regex": "^/settings/payment(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/payment(?:/)?$"}, {"page": "/settings/promotion-popup", "regex": "^/settings/promotion\\-popup(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/promotion\\-popup(?:/)?$"}, {"page": "/settings/seo", "regex": "^/settings/seo(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/seo(?:/)?$"}, {"page": "/settings/shop", "regex": "^/settings/shop(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/shop(?:/)?$"}, {"page": "/shippings", "regex": "^/shippings(?:/)?$", "routeKeys": {}, "namedRegex": "^/shippings(?:/)?$"}, {"page": "/shippings/create", "regex": "^/shippings/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/shippings/create(?:/)?$"}, {"page": "/shop-transfer", "regex": "^/shop\\-transfer(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop\\-transfer(?:/)?$"}, {"page": "/shop-transfer/vendor", "regex": "^/shop\\-transfer/vendor(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop\\-transfer/vendor(?:/)?$"}, {"page": "/shops", "regex": "^/shops(?:/)?$", "routeKeys": {}, "namedRegex": "^/shops(?:/)?$"}, {"page": "/shops/create", "regex": "^/shops/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/shops/create(?:/)?$"}, {"page": "/store-notices", "regex": "^/store\\-notices(?:/)?$", "routeKeys": {}, "namedRegex": "^/store\\-notices(?:/)?$"}, {"page": "/store-notices/create", "regex": "^/store\\-notices/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/store\\-notices/create(?:/)?$"}, {"page": "/tags", "regex": "^/tags(?:/)?$", "routeKeys": {}, "namedRegex": "^/tags(?:/)?$"}, {"page": "/tags/create", "regex": "^/tags/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/tags/create(?:/)?$"}, {"page": "/taxes", "regex": "^/taxes(?:/)?$", "routeKeys": {}, "namedRegex": "^/taxes(?:/)?$"}, {"page": "/taxes/create", "regex": "^/taxes/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/taxes/create(?:/)?$"}, {"page": "/terms-and-conditions", "regex": "^/terms\\-and\\-conditions(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-and\\-conditions(?:/)?$"}, {"page": "/terms-and-conditions/create", "regex": "^/terms\\-and\\-conditions/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-and\\-conditions/create(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/users/admins", "regex": "^/users/admins(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/admins(?:/)?$"}, {"page": "/users/create", "regex": "^/users/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/create(?:/)?$"}, {"page": "/users/customer", "regex": "^/users/customer(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/customer(?:/)?$"}, {"page": "/users/my-staffs", "regex": "^/users/my\\-staffs(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/my\\-staffs(?:/)?$"}, {"page": "/users/vendor-staffs", "regex": "^/users/vendor\\-staffs(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/vendor\\-staffs(?:/)?$"}, {"page": "/users/vendors", "regex": "^/users/vendors(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/vendors(?:/)?$"}, {"page": "/users/vendors/pending", "regex": "^/users/vendors/pending(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/vendors/pending(?:/)?$"}, {"page": "/verify-email", "regex": "^/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-email(?:/)?$"}, {"page": "/verify-license", "regex": "^/verify\\-license(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-license(?:/)?$"}, {"page": "/withdraws", "regex": "^/withdraws(?:/)?$", "routeKeys": {}, "namedRegex": "^/withdraws(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/index.json$"}, {"page": "/attributes", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes.json$"}, {"page": "/attributes/[attributeId]/[action]", "routeKeys": {"nxtPattributeId": "nxtPattributeId", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/attributes/(?<nxtPattributeId>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/authors", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/authors.json$"}, {"page": "/authors/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/authors/create.json$"}, {"page": "/authors/[authorSlug]/[action]", "routeKeys": {"nxtPauthorSlug": "nxtPauthorSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/authors/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/authors/(?<nxtPauthorSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/become-seller", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/become-seller.json$"}, {"page": "/categories", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/categories.json$"}, {"page": "/categories/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/categories/create.json$"}, {"page": "/categories/[categorySlug]/[action]", "routeKeys": {"nxtPcategorySlug": "nxtPcategorySlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/categories/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/categories/(?<nxtPcategorySlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/coupons", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons.json$"}, {"page": "/coupons/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons/create.json$"}, {"page": "/coupons/[couponSlug]/[action]", "routeKeys": {"nxtPcouponSlug": "nxtPcouponSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/coupons/(?<nxtPcouponSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/faqs", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs.json$"}, {"page": "/faqs/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs/create.json$"}, {"page": "/faqs/[id]/[action]", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/faqs/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/flash-sale", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale.json$"}, {"page": "/flash-sale/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale/create.json$"}, {"page": "/flash-sale/vendor-request", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale/vendor-request.json$"}, {"page": "/flash-sale/vendor-request/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash-sale/vendor-request/create.json$"}, {"page": "/flash-sale/vendor-request/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/vendor\\-request/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/flash-sale/vendor-request/[id]/[action]", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/vendor\\-request/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/flash-sale/[slug]", "routeKeys": {"nxtPslug": "nxtPslug"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/(?<nxtPslug>[^/]+?)\\.json$"}, {"page": "/flash-sale/[slug]/[action]", "routeKeys": {"nxtPslug": "nxtPslug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/flash\\-sale/(?<nxtPslug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/forgot-password", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/forgot-password.json$"}, {"page": "/groups", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/groups.json$"}, {"page": "/groups/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/groups/create.json$"}, {"page": "/groups/[groupSlug]/[action]", "routeKeys": {"nxtPgroupSlug": "nxtPgroupSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/groups/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/groups/(?<nxtPgroupSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/login", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/login.json$"}, {"page": "/logout", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/logout.json$"}, {"page": "/manufacturers", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers.json$"}, {"page": "/manufacturers/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers/create.json$"}, {"page": "/manufacturers/[manufacturerSlug]/[action]", "routeKeys": {"nxtPmanufacturerSlug": "nxtPmanufacturerSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/manufacturers/(?<nxtPmanufacturerSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/message", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/message.json$"}, {"page": "/message/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/message/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/message/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/my-shop", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/my-shop.json$"}, {"page": "/my-shops", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/my-shops.json$"}, {"page": "/new-shops", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/new-shops.json$"}, {"page": "/notice", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/notice.json$"}, {"page": "/notify-logs", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/notify-logs.json$"}, {"page": "/notify-logs/user/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/notify\\-logs/user/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/notify\\-logs/user/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/orders", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders.json$"}, {"page": "/orders/checkout", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/checkout.json$"}, {"page": "/orders/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/create.json$"}, {"page": "/orders/transaction", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/transaction.json$"}, {"page": "/orders/[orderId]", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/orders/(?<nxtPorderId>[^/]+?)\\.json$"}, {"page": "/owner-message", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/owner-message.json$"}, {"page": "/products", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products.json$"}, {"page": "/products/draft", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products/draft.json$"}, {"page": "/products/inventory", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products/inventory.json$"}, {"page": "/products/product-stock", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products/product-stock.json$"}, {"page": "/products/[productSlug]/[action]", "routeKeys": {"nxtPproductSlug": "nxtPproductSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/products/(?<nxtPproductSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/profile-update", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/profile-update.json$"}, {"page": "/questions", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/questions.json$"}, {"page": "/refund-policies", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-policies.json$"}, {"page": "/refund-policies/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-policies/create.json$"}, {"page": "/refund-policies/[refundPolicySlug]", "routeKeys": {"nxtPrefundPolicySlug": "nxtPrefundPolicySlug"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-policies/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-policies/(?<nxtPrefundPolicySlug>[^/]+?)\\.json$"}, {"page": "/refund-policies/[refundPolicySlug]/[action]", "routeKeys": {"nxtPrefundPolicySlug": "nxtPrefundPolicySlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-policies/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-policies/(?<nxtPrefundPolicySlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/refund-reasons", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-reasons.json$"}, {"page": "/refund-reasons/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund-reasons/create.json$"}, {"page": "/refund-reasons/[refundReasonSlug]/[action]", "routeKeys": {"nxtPrefundReasonSlug": "nxtPrefundReasonSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-reasons/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refund\\-reasons/(?<nxtPrefundReasonSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/refunds", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refunds.json$"}, {"page": "/refunds/[refundId]", "routeKeys": {"nxtPrefundId": "nxtPrefundId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refunds/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/refunds/(?<nxtPrefundId>[^/]+?)\\.json$"}, {"page": "/register", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/register.json$"}, {"page": "/reviews", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/reviews.json$"}, {"page": "/reviews/[reviewId]", "routeKeys": {"nxtPreviewId": "nxtPreviewId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/reviews/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/reviews/(?<nxtPreviewId>[^/]+?)\\.json$"}, {"page": "/settings", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings.json$"}, {"page": "/settings/company-information", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/company-information.json$"}, {"page": "/settings/events", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/events.json$"}, {"page": "/settings/maintenance", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/maintenance.json$"}, {"page": "/settings/payment", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/payment.json$"}, {"page": "/settings/promotion-popup", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/promotion-popup.json$"}, {"page": "/settings/seo", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/seo.json$"}, {"page": "/settings/shop", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/settings/shop.json$"}, {"page": "/shippings", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings.json$"}, {"page": "/shippings/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings/create.json$"}, {"page": "/shippings/edit/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings/edit/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shippings/edit/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/shop-message/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-message/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-message/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/shop-transfer", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop-transfer.json$"}, {"page": "/shop-transfer/vendor", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop-transfer/vendor.json$"}, {"page": "/shop-transfer/vendor/[transaction_identifier]", "routeKeys": {"nxtPtransaction_identifier": "nxtPtransaction_identifier"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-transfer/vendor/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-transfer/vendor/(?<nxtPtransaction_identifier>[^/]+?)\\.json$"}, {"page": "/shop-transfer/[transaction_identifier]", "routeKeys": {"nxtPtransaction_identifier": "nxtPtransaction_identifier"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-transfer/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shop\\-transfer/(?<nxtPtransaction_identifier>[^/]+?)\\.json$"}, {"page": "/shops", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shops.json$"}, {"page": "/shops/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/shops/create.json$"}, {"page": "/store-notices", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store-notices.json$"}, {"page": "/store-notices/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store-notices/create.json$"}, {"page": "/store-notices/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store\\-notices/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store\\-notices/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/store-notices/[id]/[action]", "routeKeys": {"nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store\\-notices/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/store\\-notices/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/tags", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/tags.json$"}, {"page": "/tags/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/tags/create.json$"}, {"page": "/tags/[tagSlug]/[action]", "routeKeys": {"nxtPtagSlug": "nxtPtagSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/tags/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/tags/(?<nxtPtagSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/taxes", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes.json$"}, {"page": "/taxes/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/create.json$"}, {"page": "/taxes/edit/[id]", "routeKeys": {"nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/edit/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/taxes/edit/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/terms-and-conditions", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms-and-conditions.json$"}, {"page": "/terms-and-conditions/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms-and-conditions/create.json$"}, {"page": "/terms-and-conditions/[termSlug]", "routeKeys": {"nxtPtermSlug": "nxtPtermSlug"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms\\-and\\-conditions/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)\\.json$"}, {"page": "/terms-and-conditions/[termSlug]/[action]", "routeKeys": {"nxtPtermSlug": "nxtPtermSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms\\-and\\-conditions/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/users", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users.json$"}, {"page": "/users/admins", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/admins.json$"}, {"page": "/users/create", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/create.json$"}, {"page": "/users/customer", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/customer.json$"}, {"page": "/users/my-staffs", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/my-staffs.json$"}, {"page": "/users/vendor-staffs", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendor-staffs.json$"}, {"page": "/users/vendors", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendors.json$"}, {"page": "/users/vendors/pending", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/users/vendors/pending.json$"}, {"page": "/verify-email", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/verify-email.json$"}, {"page": "/verify-license", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/verify-license.json$"}, {"page": "/withdraws", "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/withdraws.json$"}, {"page": "/withdraws/[withdrawId]", "routeKeys": {"nxtPwithdrawId": "nxtPwithdrawId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/withdraws/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/withdraws/(?<nxtPwithdrawId>[^/]+?)\\.json$"}, {"page": "/[shop]", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)\\.json$"}, {"page": "/[shop]/attributes", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/attributes\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/attributes\\.json$"}, {"page": "/[shop]/attributes/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/attributes/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/attributes/create\\.json$"}, {"page": "/[shop]/attributes/[attributeId]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPattributeId": "nxtPattributeId", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/attributes/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/attributes/(?<nxtPattributeId>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/authors", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/authors\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/authors\\.json$"}, {"page": "/[shop]/authors/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/authors/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/authors/create\\.json$"}, {"page": "/[shop]/coupons", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/coupons\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/coupons\\.json$"}, {"page": "/[shop]/coupons/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/coupons/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/coupons/create\\.json$"}, {"page": "/[shop]/coupons/[couponSlug]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPcouponSlug": "nxtPcouponSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/coupons/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/coupons/(?<nxtPcouponSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/edit", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/edit\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/edit\\.json$"}, {"page": "/[shop]/faqs", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/faqs\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/faqs\\.json$"}, {"page": "/[shop]/faqs/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/faqs/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/faqs/create\\.json$"}, {"page": "/[shop]/faqs/[id]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/faqs/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/faqs/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/flash-sale", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale\\.json$"}, {"page": "/[shop]/flash-sale/my-products", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/my\\-products\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/my\\-products\\.json$"}, {"page": "/[shop]/flash-sale/vendor-request", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/vendor\\-request\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request\\.json$"}, {"page": "/[shop]/flash-sale/vendor-request/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/vendor\\-request/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/create\\.json$"}, {"page": "/[shop]/flash-sale/vendor-request/[id]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/vendor\\-request/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/[shop]/flash-sale/vendor-request/[id]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/vendor\\-request/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/vendor\\-request/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/flash-sale/[slug]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPslug": "nxtPslug"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/flash\\-sale/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/flash\\-sale/(?<nxtPslug>[^/]+?)\\.json$"}, {"page": "/[shop]/manufacturers", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/manufacturers\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/manufacturers\\.json$"}, {"page": "/[shop]/manufacturers/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/manufacturers/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/manufacturers/create\\.json$"}, {"page": "/[shop]/orders", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/orders\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/orders\\.json$"}, {"page": "/[shop]/orders/transaction", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/orders/transaction\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/orders/transaction\\.json$"}, {"page": "/[shop]/orders/[orderId]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPorderId": "nxtPorderId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/orders/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/orders/(?<nxtPorderId>[^/]+?)\\.json$"}, {"page": "/[shop]/products", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products\\.json$"}, {"page": "/[shop]/products/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products/create\\.json$"}, {"page": "/[shop]/products/draft", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products/draft\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products/draft\\.json$"}, {"page": "/[shop]/products/inventory", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products/inventory\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products/inventory\\.json$"}, {"page": "/[shop]/products/product-stock", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products/product\\-stock\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products/product\\-stock\\.json$"}, {"page": "/[shop]/products/[productSlug]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPproductSlug": "nxtPproductSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/products/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/products/(?<nxtPproductSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/questions", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/questions\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/questions\\.json$"}, {"page": "/[shop]/refunds", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/refunds\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/refunds\\.json$"}, {"page": "/[shop]/refunds/[refundId]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPrefundId": "nxtPrefundId"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/refunds/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/refunds/(?<nxtPrefundId>[^/]+?)\\.json$"}, {"page": "/[shop]/reviews", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/reviews\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/reviews\\.json$"}, {"page": "/[shop]/staffs", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/staffs\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/staffs\\.json$"}, {"page": "/[shop]/staffs/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/staffs/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/staffs/create\\.json$"}, {"page": "/[shop]/store-notices", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/store\\-notices\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/store\\-notices\\.json$"}, {"page": "/[shop]/store-notices/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/store\\-notices/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/store\\-notices/create\\.json$"}, {"page": "/[shop]/store-notices/[id]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/store\\-notices/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/store\\-notices/(?<nxtPid>[^/]+?)\\.json$"}, {"page": "/[shop]/store-notices/[id]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPid": "nxtPid", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/store\\-notices/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/store\\-notices/(?<nxtPid>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/terms-and-conditions", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/terms\\-and\\-conditions\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions\\.json$"}, {"page": "/[shop]/terms-and-conditions/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/terms\\-and\\-conditions/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/create\\.json$"}, {"page": "/[shop]/terms-and-conditions/[termSlug]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPtermSlug": "nxtPtermSlug"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/terms\\-and\\-conditions/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)\\.json$"}, {"page": "/[shop]/terms-and-conditions/[termSlug]/[action]", "routeKeys": {"nxtPshop": "nxtPshop", "nxtPtermSlug": "nxtPtermSlug", "nxtPaction": "nxtPaction"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/terms\\-and\\-conditions/([^/]+?)/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/terms\\-and\\-conditions/(?<nxtPtermSlug>[^/]+?)/(?<nxtPaction>[^/]+?)\\.json$"}, {"page": "/[shop]/transfer-ownership", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/transfer\\-ownership\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/transfer\\-ownership\\.json$"}, {"page": "/[shop]/withdraws", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/withdraws\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/withdraws\\.json$"}, {"page": "/[shop]/withdraws/create", "routeKeys": {"nxtPshop": "nxtPshop"}, "dataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/([^/]+?)/withdraws/create\\.json$", "namedDataRouteRegex": "^/_next/data/cBkf5VHBytdmLdqgWUVA9/(?<nxtPshop>[^/]+?)/withdraws/create\\.json$"}], "i18n": {"defaultLocale": "en", "locales": ["en"]}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}