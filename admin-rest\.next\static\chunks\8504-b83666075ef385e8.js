"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8504],{804:function(t,e,n){n.d(e,{Z:function(){return action_buttons}});var o=n(85893);let AdminIcon=t=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"21.308",height:"21.3",viewBox:"0 0 21.308 21.3",...t,children:(0,o.jsxs)("g",{"data-name":"Group 6",children:[(0,o.jsx)("g",{"data-name":"Group 1",children:(0,o.jsx)("path",{"data-name":"Path 1",d:"M8.684 10.65c-2.206 0-3.938-2.592-3.938-5.906C4.746.596 7.502.15 8.684.15s3.938.446 3.938 4.594c-.001 3.314-1.727 5.906-3.938 5.906Zm0-9.187c-1.739 0-2.625 1.1-2.625 3.281 0 2.487 1.2 4.594 2.625 4.594s2.625-2.107 2.625-4.594c0-2.179-.88-3.281-2.625-3.281Z",fill:"currentColor",stroke:"currentColor",strokeWidth:".3"})}),(0,o.jsx)("g",{"data-name":"Group 2",children:(0,o.jsx)("path",{"data-name":"Path 2",d:"M11.621 21.15H.809a.653.653 0 0 1-.656-.65c-.007-.643-.007-6.313 1.693-8.026a1.806 1.806 0 0 1 1.286-.564h.2c1.05.007 1.673-.033 2.152-.919a.658.658 0 0 1 1.155.63 3.214 3.214 0 0 1-3.314 1.6h-.19a.463.463 0 0 0-.348.177c-.9.906-1.267 4.187-1.313 6.438h10.147a.656.656 0 1 1 0 1.313Z",fill:"currentColor",stroke:"currentColor",strokeWidth:".3"})}),(0,o.jsx)("g",{"data-name":"Group 5",children:(0,o.jsx)("g",{"data-name":"Group 4",children:(0,o.jsx)("g",{"data-name":"Group 3",children:(0,o.jsx)("path",{"data-name":"Path 3",d:"M16.23 21.15a.858.858 0 0 1-.289-.046c-4.679-1.509-4.712-7.645-4.613-9.5a1 1 0 0 1 .368-.728.89.89 0 0 1 .7-.2 3.691 3.691 0 0 0 3.117-1.043l.105-.085a.934.934 0 0 1 1.207 0l.125.1a3.619 3.619 0 0 0 3.1 1.024.877.877 0 0 1 .7.2 1.024 1.024 0 0 1 .368.728c.1 1.844.066 7.987-4.613 9.5a.8.8 0 0 1-.275.05Zm-3.6-9.122c-.039 1.654.131 6.563 3.6 7.783 3.472-1.227 3.642-6.136 3.6-7.783a4.853 4.853 0 0 1-3.6-1.26 4.869 4.869 0 0 1-3.602 1.261Z",fill:"currentColor",stroke:"currentColor",strokeWidth:".3"})})})})]})}),BanUser=t=>{let{...e}=t;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 459.739 459.739",fill:"currentColor",...e,children:(0,o.jsx)("path",{d:"M229.869 0C102.919 0 0 102.918 0 229.87s102.919 229.869 229.869 229.869c126.952 0 229.87-102.917 229.87-229.869S356.821 0 229.869 0zM61.299 229.87c0-37.1 12.196-71.325 32.58-99.198L329.062 365.86c-27.868 20.392-62.093 32.581-99.192 32.581-92.951 0-168.571-75.621-168.571-168.571zm307.839 94.813L135.048 90.601c27.044-18.468 59.684-29.303 94.821-29.303 92.952 0 168.571 75.622 168.571 168.572 0 35.139-10.833 67.779-29.302 94.813z"})})};var a=n(94816),r=n(59272);let CloseFillIcon=t=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 511.76 511.76",...t,children:(0,o.jsx)("path",{d:"M436.896 74.869c-99.84-99.819-262.208-99.819-362.048 0-99.797 99.819-99.797 262.229 0 362.048 49.92 49.899 115.477 74.837 181.035 74.837s131.093-24.939 181.013-74.837c99.819-99.818 99.819-262.229 0-362.048zm-75.435 256.448c8.341 8.341 8.341 21.824 0 30.165a21.275 21.275 0 01-15.083 6.251 21.277 21.277 0 01-15.083-6.251l-75.413-75.435-75.392 75.413a21.348 21.348 0 01-15.083 6.251 21.277 21.277 0 01-15.083-6.251c-8.341-8.341-8.341-21.845 0-30.165l75.392-75.413-75.413-75.413c-8.341-8.341-8.341-21.845 0-30.165 8.32-8.341 21.824-8.341 30.165 0l75.413 75.413 75.413-75.413c8.341-8.341 21.824-8.341 30.165 0 8.341 8.32 8.341 21.824 0 30.165l-75.413 75.413 75.415 75.435z",fill:"currentColor"})});var l=n(71261),i=n(33367),s=n(27554);let WalletPointsIcon=t=>(0,o.jsx)("svg",{viewBox:"0 0 192 192",...t,children:(0,o.jsxs)("g",{"data-name":"06-king",children:[(0,o.jsx)("path",{d:"M96 0a96 96 0 1 0 96 96A96.108 96.108 0 0 0 96 0zm0 176a80 80 0 1 1 80-80 80.091 80.091 0 0 1-80 80z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M96 24a72 72 0 1 0 72 72 72.081 72.081 0 0 0-72-72zm0 40a8 8 0 1 1-8 8 8 8 0 0 1 8-8zM48 80a8 8 0 1 1 8 8 8 8 0 0 1-8-8zm80 48H64l-5-37 21 13 16-24 16 24 20-13zm8-40a8 8 0 1 1 8-8 8 8 0 0 1-8 8z",fill:"currentColor"})]})});var c=n(8152),d=n(75814),h=n(16203),u=n(79362),x=n(5233),m=n(11163),p=n(97514),w=n(48583),v=n(50943),f=n(71943),action_buttons=t=>{let{id:e,editModalView:n,deleteModalView:j,editUrl:g,previewUrl:C,enablePreviewMode:k=!1,detailsUrl:E,userStatus:b=!1,isUserActive:z=!1,isShopActive:I,approveButton:M=!1,termApproveButton:S=!1,showAddWalletPoints:N=!1,changeRefundStatus:_=!1,showMakeAdminButton:L=!1,showReplyQuestion:P=!1,customLocale:A,isTermsApproved:V,couponApproveButton:y,isCouponApprove:O,flashSaleVendorRequestApproveButton:R=!1,isFlashSaleVendorRequestApproved:F,transferShopOwnership:W,data:T,disabled:B}=t,{t:D}=(0,x.$G)(),{openModal:H}=(0,d.SO)(),U=(0,m.useRouter)(),{role:G}=(0,h.WA)(),[Z,Y]=(0,w.KO)(u.vz);function handleUserStatus(t){H("BAN_CUSTOMER",{id:e,type:t})}function handleShopStatus(t){!0===t?(H("SHOP_APPROVE_VIEW",{id:e,data:T}),(null==T?void 0:T.multiCommission)&&Y(!0)):H("SHOP_DISAPPROVE_VIEW",e)}function handleTermsStatus(t){!0===t?H("TERM_APPROVE_VIEW",e):H("TERM_DISAPPROVE_VIEW",e)}function handleCouponStatus(t){!0===t?H("COUPON_APPROVE_VIEW",e):H("COUPON_DISAPPROVE_VIEW",e)}function handleVendorFlashSaleStatus(t){!0!==t?H("VENDOR_FS_REQUEST_APPROVE_VIEW",e):H("VENDOR_FS_REQUEST_DISAPPROVE_VIEW",e)}return(0,o.jsxs)("div",{className:"inline-flex items-center w-auto gap-3",children:[P&&(0,o.jsx)("button",{onClick:function(){H("REPLY_QUESTION",e)},className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",children:D("form:button-text-reply")}),L&&(0,o.jsx)("button",{onClick:function(){H("MAKE_ADMIN",e)},className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-make-admin"),children:(0,o.jsx)(AdminIcon,{width:17})}),N&&(0,o.jsx)("button",{onClick:function(){H("ADD_WALLET_POINTS",e)},className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-add-wallet-points"),children:(0,o.jsx)(WalletPointsIcon,{width:18})}),_&&(0,o.jsx)("button",{onClick:function(){H("UPDATE_REFUND",e)},className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-change-refund-status"),children:(0,o.jsx)(r.c,{width:20})}),n&&(0,o.jsx)("button",{onClick:function(){H(n,e)},className:"transition duration-200 text-body hover:text-heading focus:outline-none",title:D("common:text-edit"),children:(0,o.jsx)(l.dY,{width:16})}),M&&(I?(0,o.jsx)("button",{onClick:()=>handleShopStatus(!1),className:"text-red-500 transition duration-200 hover:text-red-600 focus:outline-none",title:D("common:text-disapprove-shop"),children:(0,o.jsx)(CloseFillIcon,{width:16})}):(0,o.jsx)("button",{onClick:()=>handleShopStatus(!0),className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-approve-shop"),children:(0,o.jsx)(r.c,{width:16})})),y&&G===u.Mc&&(O?(0,o.jsx)("button",{onClick:()=>handleCouponStatus(!1),className:"ml-3 text-red-500 transition duration-200 hover:text-red-600 focus:outline-none",title:D("common:text-disapprove-coupon"),children:(0,o.jsx)(CloseFillIcon,{width:18})}):(0,o.jsx)("button",{onClick:()=>handleCouponStatus(!0),className:"ml-3 transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-approve-coupon"),children:(0,o.jsx)(r.c,{width:18})})),S&&(V?(0,o.jsx)("button",{onClick:()=>handleTermsStatus(!1),className:"text-red-500 transition duration-200 hover:text-red-600 focus:outline-none",title:D("common:text-disapprove-shop"),children:(0,o.jsx)(CloseFillIcon,{width:17})}):(0,o.jsx)("button",{onClick:()=>handleTermsStatus(!0),className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",title:D("common:text-approve-shop"),children:(0,o.jsx)(r.c,{width:16})})),b&&(0,o.jsx)(o.Fragment,{children:z?(0,o.jsx)("button",{onClick:()=>handleUserStatus("ban"),className:"text-red-500 transition duration-200 hover:text-red-600 focus:outline-none",title:D("common:text-ban-user"),children:(0,o.jsx)(BanUser,{width:16})}):(0,o.jsx)("button",{onClick:()=>handleUserStatus("active"),className:"transition duration-200 text-accent hover:text-accent focus:outline-none",title:D("common:text-activate-user"),children:(0,o.jsx)(r.c,{width:16})})}),g&&(0,o.jsx)(c.Z,{href:g,className:"text-base transition duration-200 hover:text-heading",title:D("common:text-edit"),children:(0,o.jsx)(l.dY,{width:15})}),k&&(0,o.jsx)(o.Fragment,{children:C&&(0,o.jsx)(c.Z,{href:C,className:"text-base transition duration-200 hover:text-heading",title:D("common:text-preview"),target:"_blank",children:(0,o.jsx)(a.t,{width:18})})}),E&&(0,o.jsx)(c.Z,{href:E,className:"text-base transition duration-200 hover:text-heading",title:D("common:text-view"),locale:A,children:(0,o.jsx)(i.b,{className:"w-5 h-5"})}),j&&(G!==u.G9||U.asPath!=="/".concat(U.query.shop).concat(p.Z.coupon.list))&&(0,o.jsx)("button",{onClick:function(){H(j,e)},className:"text-red-500 transition duration-200 hover:text-red-600 focus:outline-none",title:D("common:text-delete"),children:(0,o.jsx)(s.X,{width:14})}),R&&(F?(0,o.jsx)("button",{onClick:()=>handleVendorFlashSaleStatus(!0),className:"transition duration-200 text-red-500 hover:text-red-600 focus:outline-none",title:"Disapprove request ?",children:(0,o.jsx)(CloseFillIcon,{width:17})}):(0,o.jsx)("button",{onClick:()=>handleVendorFlashSaleStatus(!1),className:"text-green-500 transition duration-200 hover:text-green-600 focus:outline-none",title:"Approve request ?",children:(0,o.jsx)(r.c,{width:16})})),W&&(0,o.jsx)(f.u,{content:B?"Ownership transfer is disabled currently!":D("text-transfer-shop-ownership-status"),placement:"top-end",rounded:"none",children:(0,o.jsx)("button",{disabled:B,onClick:()=>{B||H("TRANSFER_SHOP_OWNERSHIP_VIEW",T)},className:"transition duration-200 text-accent hover:text-accent-hover focus:outline-none",children:(0,o.jsx)(v.h,{width:20})})})]})}},94816:function(t,e,n){n.d(e,{t:function(){return EyeIcon}});var o=n(85893);n(67294);let EyeIcon=t=>(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",...t,children:[(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})},59272:function(t,e,n){n.d(e,{O:function(){return CheckMarkGhost},c:function(){return CheckMarkCircle}});var o=n(85893);n(67294);let CheckMarkCircle=t=>{let{...e}=t;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 330 330",fill:"currentColor",...e,children:[(0,o.jsx)("path",{d:"M165 0C74.019 0 0 74.019 0 165s74.019 165 165 165 165-74.019 165-165S255.981 0 165 0zm0 300c-74.44 0-135-60.561-135-135S90.56 30 165 30s135 60.561 135 135-60.561 135-135 135z"}),(0,o.jsx)("path",{d:"M226.872 106.664l-84.854 84.853-38.89-38.891c-5.857-5.857-15.355-5.858-21.213-.001-5.858 5.858-5.858 15.355 0 21.213l49.496 49.498a15 15 0 0010.606 4.394h.001c3.978 0 7.793-1.581 10.606-4.393l95.461-95.459c5.858-5.858 5.858-15.355 0-21.213-5.858-5.858-15.355-5.859-21.213-.001z"})]})},CheckMarkGhost=t=>{let{...e}=t;return(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("path",{opacity:.2,d:"M17.5 10a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M13.567 7.683a.626.626 0 010 .884l-4.375 4.375a.626.626 0 01-.884 0l-1.875-1.875a.625.625 0 11.884-.884l1.433 1.433 3.933-3.933a.625.625 0 01.884 0zM18.125 10A8.125 8.125 0 1110 1.875 8.133 8.133 0 0118.125 10zm-1.25 0A6.875 6.875 0 1010 16.875 6.883 6.883 0 0016.875 10z",fill:"currentColor"})]})}},71261:function(t,e,n){n.d(e,{Iy:function(){return EditFillIcon},Iz:function(){return EditGhostIcon},dK:function(){return ComposeEditIcon},dY:function(){return EditIcon}});var o=n(85893);let EditIcon=t=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20.547 20.299",fill:"currentColor",...t,children:(0,o.jsxs)("g",{stroke:"currentColor",strokeWidth:".4",children:[(0,o.jsx)("path",{"data-name":"Path 78",d:"M18.659 12.688a.5.5 0 00-.5.5v4.423a1.5 1.5 0 01-1.494 1.494H2.691A1.5 1.5 0 011.2 17.609V4.629a1.5 1.5 0 011.494-1.494h4.419a.5.5 0 100-1H2.691A2.493 2.493 0 00.2 4.629v12.98A2.493 2.493 0 002.691 20.1h13.976a2.493 2.493 0 002.491-2.491v-4.423a.5.5 0 00-.5-.5zm0 0"}),(0,o.jsx)("path",{"data-name":"Path 79",d:"M18.96.856a2.241 2.241 0 00-3.17 0L6.899 9.739a.5.5 0 00-.128.219l-1.169 4.219a.5.5 0 00.613.613l4.219-1.169a.5.5 0 00.219-.128l8.886-8.887a2.244 2.244 0 000-3.17zm-10.971 9.21l7.273-7.273 2.346 2.346-7.273 7.273zm-.469.94l1.879 1.875-2.592.718zm11.32-7.1l-.528.528-2.346-2.345.528-.528a1.245 1.245 0 011.761 0l.585.584a1.247 1.247 0 010 1.761zm0 0"})]})}),EditFillIcon=t=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,o.jsx)("path",{d:"M4.671 7.87l4.546-4.546 1.459 1.459-4.547 4.546h0a2.563 2.563 0 01-1.08.645s0 0 0 0l-1.456.433.434-1.455c.121-.409.343-.78.644-1.081h0zm-1.189 2.57s0 0 0 0h0zm8.112-9.065a1.031 1.031 0 01.729 1.76l-.321.322-1.459-1.459.322-.32a1.03 1.03 0 01.729-.303z",fill:"currentColor",stroke:"currentColor"}),(0,o.jsx)("path",{d:"M3.063 3.063a1.75 1.75 0 00-1.75 1.75v6.125a1.75 1.75 0 001.75 1.75h6.124a1.75 1.75 0 001.75-1.75V7.874a.438.438 0 00-.874 0v3.063a.875.875 0 01-.876.874H3.064a.875.875 0 01-.876-.874V4.811a.875.875 0 01.876-.875h3.062a.437.437 0 100-.874H3.062z",fill:"currentColor"})]}),EditGhostIcon=t=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,o.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h2.793a.992.992 0 00.707-.293l5.23-5.229.217.869-2.3 2.3a.5.5 0 00.707.707l2.5-2.5a.5.5 0 00.132-.475l-.432-1.726L14.207 6a.999.999 0 000-1.414zM3 13v-1.793L4.793 13H3zm3-.207L3.207 10 8.5 4.707 11.293 7.5 6 12.793zm6-6L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]}),ComposeEditIcon=t=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,o.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h10.5a.5.5 0 000-1H7.208l7-7a.999.999 0 000-1.414zM3 10.206l5.5-5.5L11.293 7.5l-5.5 5.5H3v-2.793zm9-3.413L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]})},50943:function(t,e,n){n.d(e,{Z:function(){return ExternalLinkIconNew},h:function(){return ExternalLinkIcon}});var o=n(85893);let ExternalLinkIcon=t=>(0,o.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:(0,o.jsx)("path",{d:"M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})}),ExternalLinkIconNew=t=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,o.jsx)("path",{d:"M13.332 7.165v6a.667.667 0 01-.667.666H3.332a.667.667 0 01-.667-.666V3.83a.667.667 0 01.667-.666h6a.667.667 0 000-1.334h-6a2 2 0 00-2 2v9.334a2 2 0 002 2h9.333a2 2 0 002-2v-6a.667.667 0 00-1.333 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.001 1.831h-2a.667.667 0 100 1.334h.394l-4.867 4.86a.67.67 0 00.947.946l4.86-4.866v.393a.667.667 0 101.333 0v-2a.666.666 0 00-.667-.667z",fill:"currentColor"})]})},33367:function(t,e,n){n.d(e,{b:function(){return Eye}});var o=n(85893);let Eye=t=>(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...t,children:[(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})},27899:function(t,e,n){n.d(e,{i:function(){return o.ZP}}),n(20229);var o=n(61529)},71943:function(t,e,n){n.d(e,{u:function(){return Tooltip}});var o=n(85893),a=n(67294),r=n(93075),l=n(82364),i=n(24750),s=n(93967),c=n.n(s),d=n(67421),h=n(98388);let u={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},x={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(t){let{children:e,content:n,gap:s=8,animation:m="zoomIn",placement:p="top",size:w="md",rounded:v="DEFAULT",shadow:f="md",color:j="default",className:g,arrowClassName:C,showArrow:k=!0}=t,[E,b]=(0,a.useState)(!1),z=(0,a.useRef)(null),{t:I}=(0,d.$G)(),{x:M,y:S,refs:N,strategy:_,context:L}=(0,r.YF)({placement:p,open:E,onOpenChange:b,middleware:[(0,l.x7)({element:z}),(0,l.cv)(s),(0,l.RR)(),(0,l.uY)({padding:8})],whileElementsMounted:i.Me}),{getReferenceProps:P,getFloatingProps:A}=(0,r.NI)([(0,r.XI)(L),(0,r.KK)(L),(0,r.qs)(L,{role:"tooltip"}),(0,r.bQ)(L)]),{isMounted:V,styles:y}=(0,r.Y_)(L,{duration:{open:150,close:150},...x[m]});return(0,o.jsxs)(o.Fragment,{children:[(0,a.cloneElement)(e,P({ref:N.setReference,...e.props})),(V||E)&&(0,o.jsx)(r.ll,{children:(0,o.jsxs)("div",{role:"tooltip",ref:N.setFloating,className:(0,h.m6)(c()(u.base,u.size[w],u.rounded[v],u.variant.solid.base,u.variant.solid.color[j],u.shadow[f],g)),style:{position:_,top:null!=S?S:0,left:null!=M?M:0,...y},...A(),children:[I("".concat(n)),k&&(0,o.jsx)(r.Y$,{ref:z,context:L,className:c()(u.arrow.color[j],C),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"}}]);