(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9020,2036],{34026:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/transfer-ownership",function(){return t(1257)}])},92072:function(e,r,t){"use strict";var o=t(85893),n=t(93967),i=t.n(n),l=t(98388);r.Z=e=>{let{className:r,...t}=e;return(0,o.jsx)("div",{className:(0,l.m6)(i()("rounded bg-light p-5 shadow md:p-8",r)),...t})}},86779:function(e,r,t){"use strict";t.d(r,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var o=t(85893);let InfoIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,o.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},47133:function(e,r,t){"use strict";var o=t(85893),n=t(78985),i=t(79362),l=t(11163),s=t(16203),a=t(1631),d=t(99494),c=t(5233),u=t(74673),f=t(8144),p=t(90573),m=t(48583),b=t(93967),h=t.n(b),x=t(30824),g=t(62964);let SidebarItemMap=e=>{var r,t;let n,d,{menuItems:u}=e,{locale:f}=(0,l.useRouter)(),{t:b}=(0,c.$G)(),{settings:h}=(0,p.n)({language:f}),{childMenu:x}=u,v=null==h?void 0:null===(r=h.options)||void 0===r?void 0:r.enableTerms,w=null==h?void 0:null===(t=h.options)||void 0===t?void 0:t.enableCoupons,{permissions:y}=(0,s.WA)(),[j,N]=(0,m.KO)(i.Hf),{width:_}=(0,g.Z)(),{query:{shop:O}}=(0,l.useRouter)();return!v&&(n=null==u?void 0:u.childMenu.find(e=>"Terms And Conditions"===e.label))&&(n.permissions=s.M$),!w&&(d=null==u?void 0:u.childMenu.find(e=>"Coupons"===e.label))&&(d.permissions=s.M$),(0,o.jsx)("div",{className:"space-y-2",children:null==x?void 0:x.map(e=>{let{href:r,label:t,icon:n,permissions:l,childMenu:d}=e;return d||(0,s.Ft)(l,y)?(0,o.jsx)(a.Z,{href:r(null==O?void 0:O.toString()),label:b(t),icon:n,childMenu:d,miniSidebar:j&&_>=i.h2},t):null})})},SideBarGroup=()=>{var e,r;let[t,n]=(0,m.KO)(i.Hf),{role:l}=(0,s.WA)(),a="staff"===l?null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===d.siteSettings||void 0===d.siteSettings?void 0:null===(r=d.siteSettings.sidebarLinks)||void 0===r?void 0:r.shop,u=Object.keys(a),{width:f}=(0,g.Z)(),{t:p}=(0,c.$G)();return(0,o.jsx)(o.Fragment,{children:null==u?void 0:u.map((e,r)=>{var n;return(0,o.jsxs)("div",{className:h()("flex flex-col px-5",t&&f>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,o.jsx)("div",{className:h()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",t&&f>=i.h2?"hidden":""),children:p(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,o.jsx)(SidebarItemMap,{menuItems:a[e]})]},r)})})};r.Z=e=>{let{children:r}=e,[t,s]=(0,m.KO)(i.Hf),{locale:a}=(0,l.useRouter)(),{width:d}=(0,g.Z)(),[c]=(0,m.KO)(i.GH),[p]=(0,m.KO)(i.W4);return(0,o.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===a||"he"===a?"rtl":"ltr",children:[(0,o.jsx)(n.Z,{}),(0,o.jsx)(u.Z,{children:(0,o.jsx)(SideBarGroup,{})}),(0,o.jsxs)("div",{className:"flex flex-1",children:[(0,o.jsx)("aside",{className:h()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",d>=i.h2&&(c||p)?"pt-[8.75rem]":"pt-20",t&&d>=i.h2?"lg:w-24":"lg:w-76"),children:(0,o.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,o.jsx)(x.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,o.jsx)(SideBarGroup,{})})})}),(0,o.jsxs)("main",{className:h()("relative flex w-full flex-col justify-start transition-[padding] duration-300",d>=i.h2&&(c||p)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",t&&d>=i.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,o.jsx)("div",{className:"h-full p-5 md:p-8",children:r}),(0,o.jsx)(f.Z,{})]})]})]})}},80602:function(e,r,t){"use strict";var o=t(85893);r.Z=e=>{let{title:r,details:t,className:n,...i}=e;return(0,o.jsxs)("div",{className:n,...i,children:[r&&(0,o.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:r}),t&&(0,o.jsx)("p",{className:"text-sm text-body",children:t})]})}},33e3:function(e,r,t){"use strict";var o=t(85893),n=t(71611),i=t(93967),l=t.n(i),s=t(67294),a=t(98388);let d={small:"text-sm h-10",medium:"h-12",big:"h-14"},c=s.forwardRef((e,r)=>{let{className:t,label:i,note:s,name:c,error:u,children:f,variant:p="normal",dimension:m="medium",shadow:b=!1,type:h="text",inputClassName:x,disabled:g,showLabel:v=!0,required:w,toolTipText:y,labelClassName:j,...N}=e,_=l()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===p,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===p,"border border-border-base focus:border-accent":"outline"===p},{"focus:shadow":b},d[m],x),O="number"===h&&g?"number-disable":"";return(0,o.jsxs)("div",{className:(0,a.m6)(t),children:[v||i?(0,o.jsx)(n.Z,{htmlFor:c,toolTipText:y,label:i,required:w,className:j}):"",(0,o.jsx)("input",{id:c,name:c,type:h,ref:r,className:(0,a.m6)(l()(g?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(O," select-none"):"",_)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:g,"aria-invalid":u?"true":"false",...N}),s&&(0,o.jsx)("p",{className:"mt-2 text-xs text-body",children:s}),u&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:u})]})});c.displayName="Input",r.Z=c},23091:function(e,r,t){"use strict";var o=t(85893),n=t(93967),i=t.n(n),l=t(98388);r.Z=e=>{let{className:r,...t}=e;return(0,o.jsx)("label",{className:(0,l.m6)(i()("flex text-body-dark font-semibold text-sm leading-none mb-3",r)),...t})}},831:function(e,r,t){"use strict";var o=t(85893),n=t(93967),i=t.n(n),l=t(5233),s=t(25675),a=t.n(s),d=t(98388);r.Z=e=>{let{className:r,imageParentClassName:t,text:n,image:s="/no-result.svg"}=e,{t:c}=(0,l.$G)("common");return(0,o.jsxs)("div",{className:(0,d.m6)(i()("flex flex-col items-center",r)),children:[(0,o.jsx)("div",{className:(0,d.m6)(i()("relative flex h-full min-h-[380px] w-full items-center justify-center md:min-h-[450px]",t)),children:(0,o.jsx)(a(),{src:s,alt:c(n||"text-no-result-found"),className:"h-full w-full object-contain",fill:!0,sizes:"(max-width: 768px) 100vw"})}),n&&(0,o.jsx)("h3",{className:"my-7 w-full text-center text-base font-semibold text-heading/80 lg:text-xl",children:c(n)})]})}},28454:function(e,r,t){"use strict";var o=t(85893),n=t(79828),i=t(71611),l=t(87536);r.Z=e=>{let{control:r,options:t,name:s,rules:a,getOptionLabel:d,getOptionValue:c,disabled:u,isMulti:f,isClearable:p,isLoading:m,placeholder:b,label:h,required:x,toolTipText:g,error:v,...w}=e;return(0,o.jsxs)(o.Fragment,{children:[h?(0,o.jsx)(i.Z,{htmlFor:s,toolTipText:g,label:h,required:x}):"",(0,o.jsx)(l.Qr,{control:r,name:s,rules:a,...w,render:e=>{let{field:r}=e;return(0,o.jsx)(n.Z,{...r,getOptionLabel:d,getOptionValue:c,placeholder:b,isMulti:f,isClearable:p,isLoading:m,options:t,isDisabled:u})}}),v&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:v})]})}},87077:function(e,r,t){"use strict";t.d(r,{W:function(){return n},X:function(){return o}});let o={option:(e,r)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:r.isSelected?"#E5E7EB":r.isFocused?"#F9FAFB":"#ffffff"}),control:(e,r)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==r?void 0:r.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==r?void 0:r.isDisabled)?"#D4D8DD":r.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:r.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,r)=>({...e,color:r.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,r)=>({...e,color:r.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,r)=>({...e,paddingLeft:16}),singleValue:(e,r)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,r)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,r)=>({...e,paddingLeft:r.isRtl?0:12,paddingRight:r.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,r)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,r)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,r)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,r)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:r.isSelected?"#EEF1F4":r.isFocused?"#EEF1F4":"#ffffff"}),control:(e,r)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==r?void 0:r.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==r?void 0:r.isDisabled)?"#D4D8DD":r.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:r.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,r)=>({...e,color:r.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,r)=>({...e,color:r.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,r)=>({...e,paddingLeft:16}),singleValue:(e,r)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,r)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,r)=>({...e,paddingLeft:r.isRtl?0:12,paddingRight:r.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,r)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,r)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,r)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,r,t){"use strict";var o=t(85893),n=t(76518),i=t(67294),l=t(23157),s=t(87077);let a=i.forwardRef((e,r)=>{let{isRTL:t}=(0,n.S)();return(0,o.jsx)(l.ZP,{ref:r,styles:s.X,isRtl:t,...e})});a.displayName="Select",r.Z=a},22220:function(e,r,t){"use strict";var o=t(85893),n=t(93967),i=t.n(n),l=t(98388);r.Z=e=>{let{children:r,className:t,...n}=e;return(0,o.jsx)("div",{className:(0,l.m6)(i()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",t)),...n,children:r})}},95414:function(e,r,t){"use strict";var o=t(85893),n=t(71611),i=t(93967),l=t.n(i),s=t(67294),a=t(98388);let d=s.forwardRef((e,r)=>{let{className:t,label:i,toolTipText:s,name:d,error:c,variant:u="normal",shadow:f=!1,inputClassName:p,disabled:m,required:b,...h}=e,x=l()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===u,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===u,"border border-border-base focus:border-accent":"outline"===u},{"focus:shadow":f},p);return(0,o.jsxs)("div",{className:(0,a.m6)(l()(t)),children:[i&&(0,o.jsx)(n.Z,{htmlFor:d,toolTipText:s,label:i,required:b}),(0,o.jsx)("textarea",{id:d,name:d,className:(0,a.m6)(l()(x,m?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:r,disabled:m,...h}),c&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});d.displayName="TextArea",r.Z=d},71611:function(e,r,t){"use strict";var o=t(85893),n=t(86779),i=t(71943),l=t(23091),s=t(98388);r.Z=e=>{let{className:r,required:t,label:a,toolTipText:d,htmlFor:c}=e;return(0,o.jsxs)(l.Z,{className:(0,s.m6)(r),htmlFor:c,children:[a,t?(0,o.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",d?(0,o.jsx)(i.u,{content:d,children:(0,o.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,o.jsx)(n.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,r,t){"use strict";t.d(r,{u:function(){return Tooltip}});var o=t(85893),n=t(67294),i=t(93075),l=t(82364),s=t(24750),a=t(93967),d=t.n(a),c=t(67421),u=t(98388);let f={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:r,content:t,gap:a=8,animation:m="zoomIn",placement:b="top",size:h="md",rounded:x="DEFAULT",shadow:g="md",color:v="default",className:w,arrowClassName:y,showArrow:j=!0}=e,[N,_]=(0,n.useState)(!1),O=(0,n.useRef)(null),{t:Z}=(0,c.$G)(),{x:S,y:C,refs:F,strategy:D,context:R}=(0,i.YF)({placement:b,open:N,onOpenChange:_,middleware:[(0,l.x7)({element:O}),(0,l.cv)(a),(0,l.RR)(),(0,l.uY)({padding:8})],whileElementsMounted:s.Me}),{getReferenceProps:E,getFloatingProps:k}=(0,i.NI)([(0,i.XI)(R),(0,i.KK)(R),(0,i.qs)(R,{role:"tooltip"}),(0,i.bQ)(R)]),{isMounted:T,styles:z}=(0,i.Y_)(R,{duration:{open:150,close:150},...p[m]});return(0,o.jsxs)(o.Fragment,{children:[(0,n.cloneElement)(r,E({ref:F.setReference,...r.props})),(T||N)&&(0,o.jsx)(i.ll,{children:(0,o.jsxs)("div",{role:"tooltip",ref:F.setFloating,className:(0,u.m6)(d()(f.base,f.size[h],f.rounded[x],f.variant.solid.base,f.variant.solid.color[v],f.shadow[g],w)),style:{position:D,top:null!=C?C:0,left:null!=S?S:0,...z},...k(),children:[Z("".concat(t)),j&&(0,o.jsx)(i.Y$,{ref:O,context:R,className:d()(f.arrow.color[v],y),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},1257:function(e,r,t){"use strict";t.r(r),t.d(r,{__N_SSP:function(){return S},default:function(){return TransferShopOwnershipPage}});var o=t(85893),n=t(47133),i=t(92072),l=t(60802),s=t(80602),a=t(33e3),d=t(28454),c=t(22220),u=t(95414),f=t(30042),p=t(47533),m=t(5233),b=t(87536),h=t(16310);let x=h.Ry().shape({vendor:h.Ry().shape({id:h.Rx().required("form:error-id-required"),name:h.Z_().required("form:error-name-required")})});var g=t(67294),transfer_ownership_shop_form=e=>{let{shop:r,vendors:t}=e,{t:n}=(0,m.$G)(),{mutate:h,isLoading:v}=(0,f._3)(),{handleSubmit:w,control:y,register:j}=(0,b.cI)({shouldUnregister:!0,defaultValues:{shop_id:null==r?void 0:r.id,vendor:{id:void 0,name:""},message:""},resolver:(0,p.X)(x)}),N=(0,g.useCallback)(e=>{var t;let o={shop_id:null==r?void 0:r.id,vendor_id:null===(t=e.vendor)||void 0===t?void 0:t.id,message:null==e?void 0:e.message};h(o)},[]);return(0,o.jsxs)("form",{onSubmit:w(N),noValidate:!0,children:[(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-gray-300 border-dashed sm:my-8",children:[(0,o.jsx)(s.Z,{title:n("form:form-title-transfer-shop-ownership"),details:n("form:shop-transfer-helper-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)(a.Z,{label:n("form:input-label-shop-name"),name:"name",value:r.name,disabled:!0,variant:"outline",className:"mb-5"}),(0,o.jsx)(d.Z,{name:"vendor",control:y,getOptionLabel:e=>e.name,getOptionValue:e=>e.id,options:t,label:n("form:input-label-vendor"),required:!0}),(0,o.jsx)(u.Z,{label:"Message towards vendor.",...j("message"),placeholder:"Don't share any personal information here (Optional)",variant:"outline",className:"col-span-2 mt-5 mb-5"})]})]}),(0,o.jsx)(c.Z,{className:"z-0",children:(0,o.jsx)("div",{className:"mb-5 text-end",children:(0,o.jsx)(l.Z,{loading:v,disabled:v,children:n("form:button-label-transfer")})})})]})},v=t(45957),w=t(55846),y=t(831),j=t(97514),N=t(99930),_=t(16203),O=t(79362),Z=t(11163),S=!0;function TransferShopOwnershipPage(){var e,r,t,n;let i=(0,Z.useRouter)(),{permissions:l}=(0,_.WA)(),{data:s}=(0,N.UE)(),{query:a}=(0,Z.useRouter)(),{shop:d}=a,{t:c}=(0,m.$G)(),{data:u,isLoading:p,error:b}=(0,f.DZ)({slug:d}),{vendors:h,loading:x,error:g}=(0,N.bg)({is_active:!0,shop_id:null==u?void 0:u.id,exclude:null==u?void 0:u.owner_id});return p||x?(0,o.jsx)(w.Z,{text:c("common:text-loading")}):b?(0,o.jsx)(v.Z,{message:b.message}):g?(0,o.jsx)(v.Z,{message:g.message}):((0,_.Ft)(_.M$,l)||(null==s?void 0:null===(e=s.shops)||void 0===e?void 0:e.map(e=>e.id).includes(null==u?void 0:u.id))||(null==s?void 0:null===(r=s.managed_shop)||void 0===r?void 0:r.id)==(null==u?void 0:u.id)||i.replace(j.Z.dashboard),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"flex py-5 border-b border-dashed border-border-base sm:py-8",children:(0,o.jsx)("h1",{className:"text-lg font-semibold text-heading",children:c("form:form-title-transfer-shop-ownership")})}),(null===O.T8||void 0===O.T8?void 0:O.T8.includes(null==u?void 0:null===(t=u.ownership_history)||void 0===t?void 0:t.status))?(0,o.jsx)(y.Z,{className:"mt-10",text:"Shop transfer already in ".concat(null==u?void 0:null===(n=u.ownership_history)||void 0===n?void 0:n.status," state! ✋")}):(0,o.jsx)(transfer_ownership_shop_form,{shop:u,vendors:h})]}))}TransferShopOwnershipPage.authenticate={permissions:_.Zk},TransferShopOwnershipPage.Layout=n.Z},23157:function(e,r,t){"use strict";t.d(r,{ZP:function(){return s}});var o=t(65342),n=t(87462),i=t(67294),l=t(76416);t(48711),t(73935),t(73469);var s=(0,i.forwardRef)(function(e,r){var t=(0,o.u)(e);return i.createElement(l.S,(0,n.Z)({ref:r},t))})},97326:function(e,r,t){"use strict";function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}t.d(r,{Z:function(){return _assertThisInitialized}})},15671:function(e,r,t){"use strict";function _classCallCheck(e,r){if(!(e instanceof r))throw TypeError("Cannot call a class as a function")}t.d(r,{Z:function(){return _classCallCheck}})},43144:function(e,r,t){"use strict";t.d(r,{Z:function(){return _createClass}});var o=t(83997);function _defineProperties(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(0,o.Z)(n.key),n)}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,r,t){"use strict";function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}t.d(r,{Z:function(){return _createSuper}});var o=t(71002),n=t(97326);function _createSuper(e){var r=_isNativeReflectConstruct();return function(){var t,i=_getPrototypeOf(e);if(r){var l=_getPrototypeOf(this).constructor;t=Reflect.construct(i,arguments,l)}else t=i.apply(this,arguments);return function(e,r){if(r&&("object"==(0,o.Z)(r)||"function"==typeof r))return r;if(void 0!==r)throw TypeError("Derived constructors may only return object or undefined");return(0,n.Z)(e)}(this,t)}}},60136:function(e,r,t){"use strict";t.d(r,{Z:function(){return _inherits}});var o=t(89611);function _inherits(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&(0,o.Z)(e,r)}},1413:function(e,r,t){"use strict";t.d(r,{Z:function(){return _objectSpread2}});var o=t(4942);function ownKeys(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function _objectSpread2(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(t),!0).forEach(function(r){(0,o.Z)(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=34026)}),_N_E=e.O()}]);