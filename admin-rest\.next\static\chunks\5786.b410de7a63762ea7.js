"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5786],{75786:function(e,r,t){t.r(r),t.d(r,{ScheduleGrid:function(){return ScheduleGrid},default:function(){return m}});var n=t(85893),l=t(75131),i=t(48583),o=t(93967),a=t.n(o),schedule_card=e=>{let{checked:r,schedule:t}=e;return(0,n.jsxs)("div",{className:a()("relative p-4 h-full rounded border cursor-pointer group hover:border-accent",{"border-accent shadow-sm":r,"bg-gray-100 border-transparent":!r}),children:[(0,n.jsx)("span",{className:"text-sm text-heading font-semibold block mb-2",children:t.title}),(0,n.jsx)("span",{className:"text-sm text-heading block",children:t.description})]})},s=t(8657),d=t(67294),c=t(5233),p=t(73263);let ScheduleGrid=e=>{let{label:r,className:t,count:o}=e,{t:a}=(0,c.$G)("common"),{deliveryTime:m}=(0,p.rV)(),[f,v]=(0,i.KO)(s.Gh);return(0,d.useEffect)(()=>{v(m[0])},[]),(0,n.jsxs)("div",{className:t,children:[(0,n.jsx)("div",{className:"mb-5 flex items-center justify-between md:mb-8",children:(0,n.jsxs)("div",{className:"space-s-3 md:space-s-4 flex items-center",children:[o&&(0,n.jsx)("span",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-accent text-base text-light lg:text-xl",children:o}),(0,n.jsx)("p",{className:"text-lg capitalize text-heading lg:text-xl",children:r})]})}),m&&(null==m?void 0:m.length)?(0,n.jsxs)(l.E,{value:f,onChange:v,children:[(0,n.jsx)(l.E.Label,{className:"sr-only",children:r}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3",children:null==m?void 0:m.map((e,r)=>(0,n.jsx)(l.E.Option,{value:e,children:r=>{let{checked:t}=r;return(0,n.jsx)(schedule_card,{checked:t,schedule:e})}},r))})]}):(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3",children:(0,n.jsx)("span",{className:"relative rounded border border-border-200 bg-gray-100 px-5 py-6 text-center text-base",children:a("text-no-delivery-time-found")})})]})};var m=ScheduleGrid},95389:function(e,r,t){t.d(r,{_:function(){return c},b:function(){return H}});var n=t(67294),l=t(19946),i=t(12351),o=t(16723),a=t(23784),s=t(73781);let d=(0,n.createContext)(null);function H(){let[e,r]=(0,n.useState)([]);return[e.length>0?e.join(" "):void 0,(0,n.useMemo)(()=>function(e){let t=(0,s.z)(e=>(r(r=>[...r,e]),()=>r(r=>{let t=r.slice(),n=t.indexOf(e);return -1!==n&&t.splice(n,1),t}))),l=(0,n.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props}),[t,e.slot,e.name,e.props]);return n.createElement(d.Provider,{value:l},e.children)},[r])]}let c=Object.assign((0,i.yV)(function(e,r){let t=(0,l.M)(),{id:s=`headlessui-label-${t}`,passive:c=!1,...p}=e,m=function u(){let e=(0,n.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),f=(0,a.T)(r);(0,o.e)(()=>m.register(s),[s,m.register]);let v={ref:f,...m.props,id:s};return c&&("onClick"in v&&(delete v.htmlFor,delete v.onClick),"onClick"in p&&delete p.onClick),(0,i.sY)({ourProps:v,theirProps:p,slot:m.slot||{},defaultTag:"label",name:m.name||"Label"})}),{})},75131:function(e,r,t){t.d(r,{E:function(){return P}});var n,l,i=t(67294),o=t(12351),a=t(19946),s=t(32984),d=t(16723),c=t(61363),p=t(84575),m=t(14227),f=t(95389),v=t(39516),g=t(31591),b=t(23784),h=t(46045),x=t(18689),E=t(15466),R=t(73781),k=t(31147),y=t(64103),T=t(3855),O=t(94192),j=((n=j||{})[n.RegisterOption=0]="RegisterOption",n[n.UnregisterOption=1]="UnregisterOption",n);let C={0(e,r){let t=[...e.options,{id:r.id,element:r.element,propsRef:r.propsRef}];return{...e,options:(0,p.z2)(t,e=>e.element.current)}},1(e,r){let t=e.options.slice(),n=e.options.findIndex(e=>e.id===r.id);return -1===n?e:(t.splice(n,1),{...e,options:t})}},N=(0,i.createContext)(null);N.displayName="RadioGroupDataContext";let G=(0,i.createContext)(null);function Le(e,r){return(0,s.E)(r.type,C,e,r)}G.displayName="RadioGroupActionsContext";var A=((l=A||{})[l.Empty=1]="Empty",l[l.Active=2]="Active",l);let P=Object.assign((0,o.yV)(function(e,r){let t=(0,a.M)(),{id:n=`headlessui-radiogroup-${t}`,value:l,defaultValue:s,form:d,name:m,onChange:y,by:T=(e,r)=>e===r,disabled:j=!1,...C}=e,A=(0,R.z)("string"==typeof T?(e,r)=>(null==e?void 0:e[T])===(null==r?void 0:r[T]):T),[P,w]=(0,i.useReducer)(Le,{options:[]}),S=P.options,[L,_]=(0,f.b)(),[D,F]=(0,v.f)(),M=(0,i.useRef)(null),z=(0,b.T)(M,r),[I,V]=(0,k.q)(l,y,s),$=(0,i.useMemo)(()=>S.find(e=>!e.propsRef.current.disabled),[S]),Y=(0,i.useMemo)(()=>S.some(e=>A(e.propsRef.current.value,I)),[S,I]),K=(0,R.z)(e=>{var r;if(j||A(e,I))return!1;let t=null==(r=S.find(r=>A(r.propsRef.current.value,e)))?void 0:r.propsRef.current;return(null==t||!t.disabled)&&(null==V||V(e),!0)});(0,g.B)({container:M.current,accept:e=>"radio"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let U=(0,R.z)(e=>{let r=M.current;if(!r)return;let t=(0,E.r)(r),n=S.filter(e=>!1===e.propsRef.current.disabled).map(e=>e.element.current);switch(e.key){case c.R.Enter:(0,x.g)(e.currentTarget);break;case c.R.ArrowLeft:case c.R.ArrowUp:if(e.preventDefault(),e.stopPropagation(),(0,p.jA)(n,p.TO.Previous|p.TO.WrapAround)===p.fE.Success){let e=S.find(e=>e.element.current===(null==t?void 0:t.activeElement));e&&K(e.propsRef.current.value)}break;case c.R.ArrowRight:case c.R.ArrowDown:if(e.preventDefault(),e.stopPropagation(),(0,p.jA)(n,p.TO.Next|p.TO.WrapAround)===p.fE.Success){let e=S.find(e=>e.element.current===(null==t?void 0:t.activeElement));e&&K(e.propsRef.current.value)}break;case c.R.Space:{e.preventDefault(),e.stopPropagation();let r=S.find(e=>e.element.current===(null==t?void 0:t.activeElement));r&&K(r.propsRef.current.value)}}}),B=(0,R.z)(e=>(w({type:0,...e}),()=>w({type:1,id:e.id}))),W=(0,i.useMemo)(()=>({value:I,firstOption:$,containsCheckedOption:Y,disabled:j,compare:A,...P}),[I,$,Y,j,A,P]),q=(0,i.useMemo)(()=>({registerOption:B,change:K}),[B,K]),J=(0,i.useMemo)(()=>({value:I}),[I]),Q=(0,i.useRef)(null),X=(0,O.G)();return(0,i.useEffect)(()=>{Q.current&&void 0!==s&&X.addEventListener(Q.current,"reset",()=>{K(s)})},[Q,K]),i.createElement(F,{name:"RadioGroup.Description"},i.createElement(_,{name:"RadioGroup.Label"},i.createElement(G.Provider,{value:q},i.createElement(N.Provider,{value:W},null!=m&&null!=I&&(0,x.t)({[m]:I}).map(([e,r],t)=>i.createElement(h._,{features:h.A.Hidden,ref:0===t?e=>{var r;Q.current=null!=(r=null==e?void 0:e.closest("form"))?r:null}:void 0,...(0,o.oA)({key:e,as:"input",type:"radio",checked:null!=r,hidden:!0,readOnly:!0,form:d,name:e,value:r})})),(0,o.sY)({ourProps:{ref:z,id:n,role:"radiogroup","aria-labelledby":L,"aria-describedby":D,onKeyDown:U},theirProps:C,slot:J,defaultTag:"div",name:"RadioGroup"})))))}),{Option:(0,o.yV)(function(e,r){var t;let n=(0,a.M)(),{id:l=`headlessui-radiogroup-option-${n}`,value:s,disabled:c=!1,...p}=e,g=(0,i.useRef)(null),h=(0,b.T)(g,r),[x,E]=(0,f.b)(),[k,O]=(0,v.f)(),{addFlag:j,removeFlag:C,hasFlag:A}=(0,m.V)(1),P=(0,T.E)({value:s,disabled:c}),w=function oe(e){let r=(0,i.useContext)(N);if(null===r){let r=Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,oe),r}return r}("RadioGroup.Option"),S=function ne(e){let r=(0,i.useContext)(G);if(null===r){let r=Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ne),r}return r}("RadioGroup.Option");(0,d.e)(()=>S.registerOption({id:l,element:g,propsRef:P}),[l,S,g,e]);let L=(0,R.z)(e=>{var r;if((0,y.P)(e.currentTarget))return e.preventDefault();S.change(s)&&(j(2),null==(r=g.current)||r.focus())}),_=(0,R.z)(e=>{if((0,y.P)(e.currentTarget))return e.preventDefault();j(2)}),D=(0,R.z)(()=>C(2)),F=(null==(t=w.firstOption)?void 0:t.id)===l,M=w.disabled||c,z=w.compare(w.value,s),I={ref:h,id:l,role:"radio","aria-checked":z?"true":"false","aria-labelledby":x,"aria-describedby":k,"aria-disabled":!!M||void 0,tabIndex:M?-1:z||!w.containsCheckedOption&&F?0:-1,onClick:M?void 0:L,onFocus:M?void 0:_,onBlur:M?void 0:D},V=(0,i.useMemo)(()=>({checked:z,disabled:M,active:A(2)}),[z,M,A]);return i.createElement(O,{name:"RadioGroup.Description"},i.createElement(E,{name:"RadioGroup.Label"},(0,o.sY)({ourProps:I,theirProps:p,slot:V,defaultTag:"div",name:"RadioGroup.Option"})))}),Label:f._,Description:v.d})}}]);