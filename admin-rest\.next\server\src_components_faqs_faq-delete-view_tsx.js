"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_faqs_faq-delete-view_tsx";
exports.ids = ["src_components_faqs_faq-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/faqs/faq-delete-view.tsx":
/*!*************************************************!*\
  !*** ./src/components/faqs/faq-delete-view.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_faqs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/faqs */ \"./src/data/faqs.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_faqs__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_faqs__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst FaqsDeleteView = ()=>{\n    const { mutate: deleteFaq, isLoading: loading } = (0,_data_faqs__WEBPACK_IMPORTED_MODULE_3__.useDeleteFaqsMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        deleteFaq({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\faqs\\\\faq-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FaqsDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/faqs/faq-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/faqs.ts":
/*!*********************************!*\
  !*** ./src/data/client/faqs.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   faqsClient: () => (/* binding */ faqsClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst faqsClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS),\n    all: ({ faq_title, shop_id, ...params } = {})=>_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        }),\n    get ({ id, language }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS}/${id}`, {\n            language\n        });\n    },\n    paginated: ({ faq_title, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.FAQS, {\n            searchJoin: \"and\",\n            shop_id: shop_id,\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                faq_title,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/faqs.ts\n");

/***/ }),

/***/ "./src/data/faqs.ts":
/*!**************************!*\
  !*** ./src/data/faqs.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateFaqsMutation: () => (/* binding */ useCreateFaqsMutation),\n/* harmony export */   useDeleteFaqsMutation: () => (/* binding */ useDeleteFaqsMutation),\n/* harmony export */   useFaqQuery: () => (/* binding */ useFaqQuery),\n/* harmony export */   useFaqsLoadMoreQuery: () => (/* binding */ useFaqsLoadMoreQuery),\n/* harmony export */   useFaqsQuery: () => (/* binding */ useFaqsQuery),\n/* harmony export */   useUpdateFaqsMutation: () => (/* binding */ useUpdateFaqsMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\n/* harmony import */ var _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/client/faqs */ \"./src/data/client/faqs.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _config__WEBPACK_IMPORTED_MODULE_7__, _data_client_faqs__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Read Single FAQ\nconst useFaqQuery = ({ id, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        {\n            id,\n            language\n        }\n    ], ()=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.get({\n            id,\n            language\n        }));\n    return {\n        faqs: data,\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs\nconst useFaqsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        faqs: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n// Read All FAQs paginated\nconst useFaqsLoadMoreQuery = (options, config)=>{\n    const { data, error, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        ...config,\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        faqs: data?.pages.flatMap((page)=>page?.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? data?.pages[data.pages.length - 1] : null,\n        error,\n        hasNextPage,\n        loading: isLoading,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore\n    };\n};\n// Create FAQ\nconst useCreateFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.create, {\n        onSuccess: async ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Update FAQ\nconst useUpdateFaqsMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_5__.Routes.faqs.list;\n            await router.push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n// Delete FAQ\nconst useDeleteFaqsMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_faqs__WEBPACK_IMPORTED_MODULE_8__.faqsClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS.FAQS);\n        },\n        onError: (error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(t(`common:${error?.response?.data.message}`));\n        }\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/faqs.ts\n");

/***/ })

};
;