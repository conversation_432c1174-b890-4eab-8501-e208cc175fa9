"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4750],{24750:function(t,e,n){n.d(e,{x7:function(){return floating_ui_dom_arrow},Me:function(){return autoUpdate},oo:function(){return floating_ui_dom_computePosition},RR:function(){return floating_ui_dom_flip},cv:function(){return floating_ui_dom_offset},uY:function(){return floating_ui_dom_shift}});let i=Math.min,o=Math.max,l=Math.round,r=Math.floor,createCoords=t=>({x:t,y:t}),u={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function floating_ui_utils_evaluate(t,e){return"function"==typeof t?t(e):t}function floating_ui_utils_getSide(t){return t.split("-")[0]}function floating_ui_utils_getAlignment(t){return t.split("-")[1]}function floating_ui_utils_getOppositeAxis(t){return"x"===t?"y":"x"}function getAxisLength(t){return"y"===t?"height":"width"}function floating_ui_utils_getSideAxis(t){return["top","bottom"].includes(floating_ui_utils_getSide(t))?"y":"x"}function floating_ui_utils_getOppositeAlignmentPlacement(t){return t.replace(/start|end/g,t=>s[t])}function getOppositePlacement(t){return t.replace(/left|right|bottom|top/g,t=>u[t])}function floating_ui_utils_getPaddingObject(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function floating_ui_utils_rectToClientRect(t){let{x:e,y:n,width:i,height:o}=t;return{width:i,height:o,top:n,left:e,right:e+i,bottom:n+o,x:e,y:n}}function computeCoordsFromPlacement(t,e,n){let i,{reference:o,floating:l}=t,r=floating_ui_utils_getSideAxis(e),u=floating_ui_utils_getOppositeAxis(floating_ui_utils_getSideAxis(e)),s=getAxisLength(u),a=floating_ui_utils_getSide(e),f="y"===r,c=o.x+o.width/2-l.width/2,g=o.y+o.height/2-l.height/2,d=o[s]/2-l[s]/2;switch(a){case"top":i={x:c,y:o.y-l.height};break;case"bottom":i={x:c,y:o.y+o.height};break;case"right":i={x:o.x+o.width,y:g};break;case"left":i={x:o.x-l.width,y:g};break;default:i={x:o.x,y:o.y}}switch(floating_ui_utils_getAlignment(e)){case"start":i[u]-=d*(n&&f?-1:1);break;case"end":i[u]+=d*(n&&f?-1:1)}return i}let computePosition=async(t,e,n)=>{let{placement:i="bottom",strategy:o="absolute",middleware:l=[],platform:r}=n,u=l.filter(Boolean),s=await (null==r.isRTL?void 0:r.isRTL(e)),a=await r.getElementRects({reference:t,floating:e,strategy:o}),{x:f,y:c}=computeCoordsFromPlacement(a,i,s),g=i,d={},m=0;for(let n=0;n<u.length;n++){let{name:l,fn:p}=u[n],{x:_,y:h,data:w,reset:x}=await p({x:f,y:c,initialPlacement:i,placement:g,strategy:o,middlewareData:d,rects:a,platform:r,elements:{reference:t,floating:e}});f=null!=_?_:f,c=null!=h?h:c,d={...d,[l]:{...d[l],...w}},x&&m<=50&&(m++,"object"==typeof x&&(x.placement&&(g=x.placement),x.rects&&(a=!0===x.rects?await r.getElementRects({reference:t,floating:e,strategy:o}):x.rects),{x:f,y:c}=computeCoordsFromPlacement(a,g,s)),n=-1)}return{x:f,y:c,placement:g,strategy:o,middlewareData:d}};async function detectOverflow(t,e){var n;void 0===e&&(e={});let{x:i,y:o,platform:l,rects:r,elements:u,strategy:s}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:g=!1,padding:d=0}=floating_ui_utils_evaluate(e,t),m=floating_ui_utils_getPaddingObject(d),p=u[g?"floating"===c?"reference":"floating":c],_=floating_ui_utils_rectToClientRect(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(p)))||n?p:p.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(u.floating)),boundary:a,rootBoundary:f,strategy:s})),h="floating"===c?{x:i,y:o,width:r.floating.width,height:r.floating.height}:r.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u.floating)),x=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},v=floating_ui_utils_rectToClientRect(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:h,offsetParent:w,strategy:s}):h);return{top:(_.top-v.top+m.top)/x.y,bottom:(v.bottom-_.bottom+m.bottom)/x.y,left:(_.left-v.left+m.left)/x.x,right:(v.right-_.right+m.right)/x.x}}async function convertValueToCoords(t,e){let{placement:n,platform:i,elements:o}=t,l=await (null==i.isRTL?void 0:i.isRTL(o.floating)),r=floating_ui_utils_getSide(n),u=floating_ui_utils_getAlignment(n),s="y"===floating_ui_utils_getSideAxis(n),a=["left","top"].includes(r)?-1:1,f=l&&s?-1:1,c=floating_ui_utils_evaluate(e,t),{mainAxis:g,crossAxis:d,alignmentAxis:m}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return u&&"number"==typeof m&&(d="end"===u?-1*m:m),s?{x:d*f,y:g*a}:{x:g*a,y:d*f}}var a=n(37317);function getCssDimensions(t){let e=(0,a.Dx)(t),n=parseFloat(e.width)||0,i=parseFloat(e.height)||0,o=(0,a.Re)(t),r=o?t.offsetWidth:n,u=o?t.offsetHeight:i,s=l(n)!==r||l(i)!==u;return s&&(n=r,i=u),{width:n,height:i,$:s}}function unwrapElement(t){return(0,a.kK)(t)?t:t.contextElement}function getScale(t){let e=unwrapElement(t);if(!(0,a.Re)(e))return createCoords(1);let n=e.getBoundingClientRect(),{width:i,height:o,$:r}=getCssDimensions(e),u=(r?l(n.width):n.width)/i,s=(r?l(n.height):n.height)/o;return u&&Number.isFinite(u)||(u=1),s&&Number.isFinite(s)||(s=1),{x:u,y:s}}let f=createCoords(0);function getVisualOffsets(t){let e=(0,a.Jj)(t);return(0,a.Pf)()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:f}function getBoundingClientRect(t,e,n,i){var o;void 0===e&&(e=!1),void 0===n&&(n=!1);let l=t.getBoundingClientRect(),r=unwrapElement(t),u=createCoords(1);e&&(i?(0,a.kK)(i)&&(u=getScale(i)):u=getScale(t));let s=(void 0===(o=n)&&(o=!1),i&&(!o||i===(0,a.Jj)(r))&&o)?getVisualOffsets(r):createCoords(0),f=(l.left+s.x)/u.x,c=(l.top+s.y)/u.y,g=l.width/u.x,d=l.height/u.y;if(r){let t=(0,a.Jj)(r),e=i&&(0,a.kK)(i)?(0,a.Jj)(i):i,n=t,o=(0,a.wK)(n);for(;o&&i&&e!==n;){let t=getScale(o),e=o.getBoundingClientRect(),i=(0,a.Dx)(o),l=e.left+(o.clientLeft+parseFloat(i.paddingLeft))*t.x,r=e.top+(o.clientTop+parseFloat(i.paddingTop))*t.y;f*=t.x,c*=t.y,g*=t.x,d*=t.y,f+=l,c+=r,n=(0,a.Jj)(o),o=(0,a.wK)(n)}}return floating_ui_utils_rectToClientRect({width:g,height:d,x:f,y:c})}function getWindowScrollBarX(t,e){let n=(0,a.Lw)(t).scrollLeft;return e?e.left+n:getBoundingClientRect((0,a.tF)(t)).left+n}function getHTMLOffset(t,e,n){void 0===n&&(n=!1);let i=t.getBoundingClientRect(),o=i.left+e.scrollLeft-(n?0:getWindowScrollBarX(t,i)),l=i.top+e.scrollTop;return{x:o,y:l}}function getClientRectFromClippingAncestor(t,e,n){let i;if("viewport"===e)i=function(t,e){let n=(0,a.Jj)(t),i=(0,a.tF)(t),o=n.visualViewport,l=i.clientWidth,r=i.clientHeight,u=0,s=0;if(o){l=o.width,r=o.height;let t=(0,a.Pf)();(!t||t&&"fixed"===e)&&(u=o.offsetLeft,s=o.offsetTop)}return{width:l,height:r,x:u,y:s}}(t,n);else if("document"===e)i=function(t){let e=(0,a.tF)(t),n=(0,a.Lw)(t),i=t.ownerDocument.body,l=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),r=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight),u=-n.scrollLeft+getWindowScrollBarX(t),s=-n.scrollTop;return"rtl"===(0,a.Dx)(i).direction&&(u+=o(e.clientWidth,i.clientWidth)-l),{width:l,height:r,x:u,y:s}}((0,a.tF)(t));else if((0,a.kK)(e))i=function(t,e){let n=getBoundingClientRect(t,!0,"fixed"===e),i=n.top+t.clientTop,o=n.left+t.clientLeft,l=(0,a.Re)(t)?getScale(t):createCoords(1),r=t.clientWidth*l.x,u=t.clientHeight*l.y,s=o*l.x,f=i*l.y;return{width:r,height:u,x:s,y:f}}(e,n);else{let n=getVisualOffsets(t);i={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return floating_ui_utils_rectToClientRect(i)}function isStaticPositioned(t){return"static"===(0,a.Dx)(t).position}function getTrueOffsetParent(t,e){if(!(0,a.Re)(t)||"fixed"===(0,a.Dx)(t).position)return null;if(e)return e(t);let n=t.offsetParent;return(0,a.tF)(t)===n&&(n=n.ownerDocument.body),n}function getOffsetParent(t,e){let n=(0,a.Jj)(t);if((0,a.tR)(t))return n;if(!(0,a.Re)(t)){let e=(0,a.Ow)(t);for(;e&&!(0,a.Py)(e);){if((0,a.kK)(e)&&!isStaticPositioned(e))return e;e=(0,a.Ow)(e)}return n}let i=getTrueOffsetParent(t,e);for(;i&&(0,a.Ze)(i)&&isStaticPositioned(i);)i=getTrueOffsetParent(i,e);return i&&(0,a.Py)(i)&&isStaticPositioned(i)&&!(0,a.hT)(i)?n:i||(0,a.gQ)(t)||n}let getElementRects=async function(t){let e=this.getOffsetParent||getOffsetParent,n=this.getDimensions,i=await n(t.floating);return{reference:function(t,e,n){let i=(0,a.Re)(e),o=(0,a.tF)(e),l="fixed"===n,r=getBoundingClientRect(t,!0,l,e),u={scrollLeft:0,scrollTop:0},s=createCoords(0);if(i||!i&&!l){if(("body"!==(0,a.wk)(e)||(0,a.ao)(o))&&(u=(0,a.Lw)(e)),i){let t=getBoundingClientRect(e,!0,l,e);s.x=t.x+e.clientLeft,s.y=t.y+e.clientTop}else o&&(s.x=getWindowScrollBarX(o))}l&&!i&&o&&(s.x=getWindowScrollBarX(o));let f=!o||i||l?createCoords(0):getHTMLOffset(o,u),c=r.left+u.scrollLeft-s.x-f.x,g=r.top+u.scrollTop-s.y-f.y;return{x:c,y:g,width:r.width,height:r.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},c={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:o}=t,l="fixed"===o,r=(0,a.tF)(i),u=!!e&&(0,a.tR)(e.floating);if(i===r||u&&l)return n;let s={scrollLeft:0,scrollTop:0},f=createCoords(1),c=createCoords(0),g=(0,a.Re)(i);if((g||!g&&!l)&&(("body"!==(0,a.wk)(i)||(0,a.ao)(r))&&(s=(0,a.Lw)(i)),(0,a.Re)(i))){let t=getBoundingClientRect(i);f=getScale(i),c.x=t.x+i.clientLeft,c.y=t.y+i.clientTop}let d=!r||g||l?createCoords(0):getHTMLOffset(r,s,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-s.scrollLeft*f.x+c.x+d.x,y:n.y*f.y-s.scrollTop*f.y+c.y+d.y}},getDocumentElement:a.tF,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:l,strategy:r}=t,u="clippingAncestors"===n?(0,a.tR)(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let i=(0,a.Kx)(t,[],!1).filter(t=>(0,a.kK)(t)&&"body"!==(0,a.wk)(t)),o=null,l="fixed"===(0,a.Dx)(t).position,r=l?(0,a.Ow)(t):t;for(;(0,a.kK)(r)&&!(0,a.Py)(r);){let e=(0,a.Dx)(r),n=(0,a.hT)(r);n||"fixed"!==e.position||(o=null);let u=l?!n&&!o:!n&&"static"===e.position&&!!o&&["absolute","fixed"].includes(o.position)||(0,a.ao)(r)&&!n&&function hasFixedPositionAncestor(t,e){let n=(0,a.Ow)(t);return!(n===e||!(0,a.kK)(n)||(0,a.Py)(n))&&("fixed"===(0,a.Dx)(n).position||hasFixedPositionAncestor(n,e))}(t,r);u?i=i.filter(t=>t!==r):o=e,r=(0,a.Ow)(r)}return e.set(t,i),i}(e,this._c):[].concat(n),s=[...u,l],f=s[0],c=s.reduce((t,n)=>{let l=getClientRectFromClippingAncestor(e,n,r);return t.top=o(l.top,t.top),t.right=i(l.right,t.right),t.bottom=i(l.bottom,t.bottom),t.left=o(l.left,t.left),t},getClientRectFromClippingAncestor(e,f,r));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent,getElementRects,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=getCssDimensions(t);return{width:e,height:n}},getScale,isElement:a.kK,isRTL:function(t){return"rtl"===(0,a.Dx)(t).direction}};function rectsAreEqual(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function autoUpdate(t,e,n,l){let u;void 0===l&&(l={});let{ancestorScroll:s=!0,ancestorResize:f=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:g="function"==typeof IntersectionObserver,animationFrame:d=!1}=l,m=unwrapElement(t),p=s||f?[...m?(0,a.Kx)(m):[],...(0,a.Kx)(e)]:[];p.forEach(t=>{s&&t.addEventListener("scroll",n,{passive:!0}),f&&t.addEventListener("resize",n)});let _=m&&g?function(t,e){let n,l=null,u=(0,a.tF)(t);function cleanup(){var t;clearTimeout(n),null==(t=l)||t.disconnect(),l=null}return!function refresh(s,a){void 0===s&&(s=!1),void 0===a&&(a=1),cleanup();let f=t.getBoundingClientRect(),{left:c,top:g,width:d,height:m}=f;if(s||e(),!d||!m)return;let p=r(g),_=r(u.clientWidth-(c+d)),h=r(u.clientHeight-(g+m)),w=r(c),x={rootMargin:-p+"px "+-_+"px "+-h+"px "+-w+"px",threshold:o(0,i(1,a))||1},v=!0;function handleObserve(e){let i=e[0].intersectionRatio;if(i!==a){if(!v)return refresh();i?refresh(!1,i):n=setTimeout(()=>{refresh(!1,1e-7)},1e3)}1!==i||rectsAreEqual(f,t.getBoundingClientRect())||refresh(),v=!1}try{l=new IntersectionObserver(handleObserve,{...x,root:u.ownerDocument})}catch(t){l=new IntersectionObserver(handleObserve,x)}l.observe(t)}(!0),cleanup}(m,n):null,h=-1,w=null;c&&(w=new ResizeObserver(t=>{let[i]=t;i&&i.target===m&&w&&(w.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var t;null==(t=w)||t.observe(e)})),n()}),m&&!d&&w.observe(m),w.observe(e));let x=d?getBoundingClientRect(t):null;return d&&function frameLoop(){let e=getBoundingClientRect(t);x&&!rectsAreEqual(x,e)&&n(),x=e,u=requestAnimationFrame(frameLoop)}(),n(),()=>{var t;p.forEach(t=>{s&&t.removeEventListener("scroll",n),f&&t.removeEventListener("resize",n)}),null==_||_(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(u)}}let floating_ui_dom_offset=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;let{x:o,y:l,placement:r,middlewareData:u}=e,s=await convertValueToCoords(e,t);return r===(null==(n=u.offset)?void 0:n.placement)&&null!=(i=u.arrow)&&i.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:r}}}}},floating_ui_dom_shift=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:l,placement:r}=e,{mainAxis:u=!0,crossAxis:s=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...f}=floating_ui_utils_evaluate(t,e),c={x:n,y:l},g=await detectOverflow(e,f),d=floating_ui_utils_getSideAxis(floating_ui_utils_getSide(r)),m=floating_ui_utils_getOppositeAxis(d),p=c[m],_=c[d];if(u){let t=p+g["y"===m?"top":"left"],e=p-g["y"===m?"bottom":"right"];p=o(t,i(p,e))}if(s){let t="y"===d?"top":"left",e="y"===d?"bottom":"right",n=_+g[t],l=_-g[e];_=o(n,i(_,l))}let h=a.fn({...e,[m]:p,[d]:_});return{...h,data:{x:h.x-n,y:h.y-l,enabled:{[m]:u,[d]:s}}}}}},floating_ui_dom_flip=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i,o,l,r;let{placement:u,middlewareData:s,rects:a,initialPlacement:f,platform:c,elements:g}=e,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:_="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:w=!0,...x}=floating_ui_utils_evaluate(t,e);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let v=floating_ui_utils_getSide(u),y=floating_ui_utils_getSideAxis(f),b=floating_ui_utils_getSide(f)===f,R=await (null==c.isRTL?void 0:c.isRTL(g.floating)),O=p||(b||!w?[getOppositePlacement(f)]:function(t){let e=getOppositePlacement(t);return[floating_ui_utils_getOppositeAlignmentPlacement(t),e,floating_ui_utils_getOppositeAlignmentPlacement(e)]}(f)),C="none"!==h;!p&&C&&O.push(...function(t,e,n,i){let o=floating_ui_utils_getAlignment(t),l=function(t,e,n){let i=["left","right"],o=["right","left"];switch(t){case"top":case"bottom":if(n)return e?o:i;return e?i:o;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(floating_ui_utils_getSide(t),"start"===n,i);return o&&(l=l.map(t=>t+"-"+o),e&&(l=l.concat(l.map(floating_ui_utils_getOppositeAlignmentPlacement)))),l}(f,w,h,R));let A=[f,...O],T=await detectOverflow(e,x),S=[],E=(null==(i=s.flip)?void 0:i.overflows)||[];if(d&&S.push(T[v]),m){let t=function(t,e,n){void 0===n&&(n=!1);let i=floating_ui_utils_getAlignment(t),o=floating_ui_utils_getOppositeAxis(floating_ui_utils_getSideAxis(t)),l=getAxisLength(o),r="x"===o?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[l]>e.floating[l]&&(r=getOppositePlacement(r)),[r,getOppositePlacement(r)]}(u,a,R);S.push(T[t[0]],T[t[1]])}if(E=[...E,{placement:u,overflows:S}],!S.every(t=>t<=0)){let t=((null==(o=s.flip)?void 0:o.index)||0)+1,e=A[t];if(e){let n="alignment"===m&&y!==floating_ui_utils_getSideAxis(e);if(!n||E.every(t=>t.overflows[0]>0&&floating_ui_utils_getSideAxis(t.placement)===y))return{data:{index:t,overflows:E},reset:{placement:e}}}let n=null==(l=E.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:l.placement;if(!n)switch(_){case"bestFit":{let t=null==(r=E.filter(t=>{if(C){let e=floating_ui_utils_getSideAxis(t.placement);return e===y||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:r[0];t&&(n=t);break}case"initialPlacement":n=f}if(u!==n)return{reset:{placement:n}}}return{}}}},floating_ui_dom_arrow=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:l,placement:r,rects:u,platform:s,elements:a,middlewareData:f}=e,{element:c,padding:g=0}=floating_ui_utils_evaluate(t,e)||{};if(null==c)return{};let d=floating_ui_utils_getPaddingObject(g),m={x:n,y:l},p=floating_ui_utils_getOppositeAxis(floating_ui_utils_getSideAxis(r)),_=getAxisLength(p),h=await s.getDimensions(c),w="y"===p,x=w?"clientHeight":"clientWidth",v=u.reference[_]+u.reference[p]-m[p]-u.floating[_],y=m[p]-u.reference[p],b=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),R=b?b[x]:0;R&&await (null==s.isElement?void 0:s.isElement(b))||(R=a.floating[x]||u.floating[_]);let O=R/2-h[_]/2-1,C=i(d[w?"top":"left"],O),A=i(d[w?"bottom":"right"],O),T=R-h[_]-A,S=R/2-h[_]/2+(v/2-y/2),E=o(C,i(S,T)),P=!f.arrow&&null!=floating_ui_utils_getAlignment(r)&&S!==E&&u.reference[_]/2-(S<C?C:A)-h[_]/2<0,L=P?S<C?S-C:S-T:0;return{[p]:m[p]+L,data:{[p]:E,centerOffset:S-E-L,...P&&{alignmentOffset:L}},reset:P}}}),floating_ui_dom_computePosition=(t,e,n)=>{let i=new Map,o={platform:c,...n},l={...o.platform,_c:i};return computePosition(t,e,{...o,platform:l})}},37317:function(t,e,n){function hasWindow(){return"undefined"!=typeof window}function getNodeName(t){return isNode(t)?(t.nodeName||"").toLowerCase():"#document"}function getWindow(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function getDocumentElement(t){var e;return null==(e=(isNode(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function isNode(t){return!!hasWindow()&&(t instanceof Node||t instanceof getWindow(t).Node)}function isElement(t){return!!hasWindow()&&(t instanceof Element||t instanceof getWindow(t).Element)}function isHTMLElement(t){return!!hasWindow()&&(t instanceof HTMLElement||t instanceof getWindow(t).HTMLElement)}function isShadowRoot(t){return!!hasWindow()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof getWindow(t).ShadowRoot)}function isOverflowElement(t){let{overflow:e,overflowX:n,overflowY:i,display:o}=getComputedStyle(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(o)}function isTableElement(t){return["table","td","th"].includes(getNodeName(t))}function isTopLayer(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function isContainingBlock(t){let e=isWebKit(),n=isElement(t)?getComputedStyle(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function getContainingBlock(t){let e=getParentNode(t);for(;isHTMLElement(e)&&!isLastTraversableNode(e);){if(isContainingBlock(e))return e;if(isTopLayer(e))break;e=getParentNode(e)}return null}function isWebKit(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function isLastTraversableNode(t){return["html","body","#document"].includes(getNodeName(t))}function getComputedStyle(t){return getWindow(t).getComputedStyle(t)}function getNodeScroll(t){return isElement(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function getParentNode(t){if("html"===getNodeName(t))return t;let e=t.assignedSlot||t.parentNode||isShadowRoot(t)&&t.host||getDocumentElement(t);return isShadowRoot(e)?e.host:e}function getFrameElement(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}n.d(e,{Dx:function(){return getComputedStyle},Jj:function(){return getWindow},Kx:function(){return function getOverflowAncestors(t,e,n){var i;void 0===e&&(e=[]),void 0===n&&(n=!0);let o=function getNearestOverflowAncestor(t){let e=getParentNode(t);return isLastTraversableNode(e)?t.ownerDocument?t.ownerDocument.body:t.body:isHTMLElement(e)&&isOverflowElement(e)?e:getNearestOverflowAncestor(e)}(t),l=o===(null==(i=t.ownerDocument)?void 0:i.body),r=getWindow(o);if(l){let t=getFrameElement(r);return e.concat(r,r.visualViewport||[],isOverflowElement(o)?o:[],t&&n?getOverflowAncestors(t):[])}return e.concat(o,getOverflowAncestors(o,[],n))}},Lw:function(){return getNodeScroll},Ow:function(){return getParentNode},Pf:function(){return isWebKit},Py:function(){return isLastTraversableNode},Re:function(){return isHTMLElement},Ze:function(){return isTableElement},Zq:function(){return isShadowRoot},ao:function(){return isOverflowElement},gQ:function(){return getContainingBlock},hT:function(){return isContainingBlock},kK:function(){return isElement},tF:function(){return getDocumentElement},tR:function(){return isTopLayer},wK:function(){return getFrameElement},wk:function(){return getNodeName}})}}]);