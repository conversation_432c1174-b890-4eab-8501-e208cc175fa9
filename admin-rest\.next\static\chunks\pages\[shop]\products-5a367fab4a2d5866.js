(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3636],{43031:function(e,l,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/products",function(){return s(29273)}])},34317:function(e,l,s){"use strict";s.d(l,{n:function(){return MoreIcon}});var t=s(85893);let MoreIcon=e=>(0,t.jsxs)("svg",{viewBox:"-192 0 512 512",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",...e,children:[(0,t.jsx)("path",{d:"m128 256c0 35.347656-28.652344 64-64 64s-64-28.652344-64-64 28.652344-64 64-64 64 28.652344 64 64zm0 0"}),(0,t.jsx)("path",{d:"m128 64c0 35.347656-28.652344 64-64 64s-64-28.652344-64-64 28.652344-64 64-64 64 28.652344 64 64zm0 0"}),(0,t.jsx)("path",{d:"m128 448c0 35.347656-28.652344 64-64 64s-64-28.652344-64-64 28.652344-64 64-64 64 28.652344 64 64zm0 0"})]})},47133:function(e,l,s){"use strict";var t=s(85893),n=s(78985),i=s(79362),r=s(11163),a=s(16203),o=s(1631),d=s(99494),u=s(5233),c=s(74673),m=s(8144),x=s(90573),h=s(48583),f=s(93967),p=s.n(f),v=s(30824),g=s(62964);let SidebarItemMap=e=>{var l,s;let n,d,{menuItems:c}=e,{locale:m}=(0,r.useRouter)(),{t:f}=(0,u.$G)(),{settings:p}=(0,x.n)({language:m}),{childMenu:v}=c,b=null==p?void 0:null===(l=p.options)||void 0===l?void 0:l.enableTerms,j=null==p?void 0:null===(s=p.options)||void 0===s?void 0:s.enableCoupons,{permissions:w}=(0,a.WA)(),[N,S]=(0,h.KO)(i.Hf),{width:y}=(0,g.Z)(),{query:{shop:Z}}=(0,r.useRouter)();return!b&&(n=null==c?void 0:c.childMenu.find(e=>"Terms And Conditions"===e.label))&&(n.permissions=a.M$),!j&&(d=null==c?void 0:c.childMenu.find(e=>"Coupons"===e.label))&&(d.permissions=a.M$),(0,t.jsx)("div",{className:"space-y-2",children:null==v?void 0:v.map(e=>{let{href:l,label:s,icon:n,permissions:r,childMenu:d}=e;return d||(0,a.Ft)(r,w)?(0,t.jsx)(o.Z,{href:l(null==Z?void 0:Z.toString()),label:f(s),icon:n,childMenu:d,miniSidebar:N&&y>=i.h2},s):null})})},SideBarGroup=()=>{var e,l;let[s,n]=(0,h.KO)(i.Hf),{role:r}=(0,a.WA)(),o="staff"===r?null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===d.siteSettings||void 0===d.siteSettings?void 0:null===(l=d.siteSettings.sidebarLinks)||void 0===l?void 0:l.shop,c=Object.keys(o),{width:m}=(0,g.Z)(),{t:x}=(0,u.$G)();return(0,t.jsx)(t.Fragment,{children:null==c?void 0:c.map((e,l)=>{var n;return(0,t.jsxs)("div",{className:p()("flex flex-col px-5",s&&m>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:p()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",s&&m>=i.h2?"hidden":""),children:x(null===(n=o[e])||void 0===n?void 0:n.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:o[e]})]},l)})})};l.Z=e=>{let{children:l}=e,[s,a]=(0,h.KO)(i.Hf),{locale:o}=(0,r.useRouter)(),{width:d}=(0,g.Z)(),[u]=(0,h.KO)(i.GH),[x]=(0,h.KO)(i.W4);return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===o||"he"===o?"rtl":"ltr",children:[(0,t.jsx)(n.Z,{}),(0,t.jsx)(c.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:p()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",d>=i.h2&&(u||x)?"pt-[8.75rem]":"pt-20",s&&d>=i.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,t.jsx)(v.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:p()("relative flex w-full flex-col justify-start transition-[padding] duration-300",d>=i.h2&&(u||x)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",s&&d>=i.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,t.jsx)(m.Z,{})]})]})]})}},29273:function(e,l,s){"use strict";s.r(l),s.d(l,{__N_SSP:function(){return C},default:function(){return ProductsPage}});var t=s(85893),n=s(92072),i=s(37912),r=s(85634),a=s(46626),o=s(34317),d=s(47133),u=s(15724),c=s(145),m=s(60802),x=s(45957),h=s(61616),f=s(55846),p=s(75814),v=s(93345),g=s(97514),b=s(93242),j=s(30042),w=s(99930),N=s(10265),S=s(16203),y=s(93967),Z=s.n(y),_=s(5233),P=s(11163),k=s(67294),M=s(35484),C=!0;function ProductsPage(){var e,l;let s=(0,P.useRouter)(),{permissions:d}=(0,S.WA)(),{data:y}=(0,w.UE)(),{query:{shop:C}}=(0,P.useRouter)(),{data:O,isLoading:T}=(0,j.DZ)({slug:C}),I=null==O?void 0:O.id,{t:R}=(0,_.$G)(),[E,F]=(0,k.useState)(""),[G,K]=(0,k.useState)(""),[$,A]=(0,k.useState)(""),[H,L]=(0,k.useState)(""),[z,B]=(0,k.useState)(1),[W,D]=(0,k.useState)("created_at"),[X,q]=(0,k.useState)(N.As.Desc),[U,J]=(0,k.useState)(!0),{openModal:Q}=(0,p.SO)(),{locale:V}=(0,P.useRouter)(),{products:Y,paginatorInfo:ee,loading:el,error:es}=(0,b.kN)({language:V,name:E,limit:20,shop_id:I,type:G,categories:$,product_type:H,orderBy:W,sortedBy:X,page:z},{enabled:!!I});function handleImportModal(){Q("EXPORT_IMPORT_PRODUCT",I)}return el||T?(0,t.jsx)(f.Z,{text:R("common:text-loading")}):es?(0,t.jsx)(x.Z,{message:es.message}):((0,S.Ft)(S.M$,d)||(null==y?void 0:null===(e=y.shops)||void 0===e?void 0:e.map(e=>e.id).includes(I))||(null==y?void 0:null===(l=y.managed_shop)||void 0===l?void 0:l.id)==I||s.replace(g.Z.dashboard),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.Z,{className:"mb-8 flex flex-col",children:[(0,t.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,t.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,t.jsx)(M.Z,{title:R("form:input-label-products")})}),(0,t.jsxs)("div",{className:"flex w-full flex-col items-center md:w-3/4 md:flex-row",children:[(0,t.jsxs)("div",{className:"flex w-full items-center",children:[(0,t.jsx)(i.Z,{onSearch:function(e){let{searchText:l}=e;F(l)},placeholderText:R("form:input-placeholder-search-name")}),V===v.Config.defaultLanguage&&(0,t.jsxs)(h.Z,{href:"/".concat(C,"/products/create"),className:"h-12 ms-4 md:ms-6",children:[(0,t.jsxs)("span",{className:"hidden md:block",children:["+ ",R("form:button-label-add-product")]}),(0,t.jsxs)("span",{className:"md:hidden",children:["+ ",R("form:button-label-add")]})]})]}),(0,t.jsx)(m.Z,{onClick:handleImportModal,className:"mt-5 w-full md:hidden",children:R("common:text-export-import")}),(0,t.jsxs)("button",{className:"mt-5 flex items-center whitespace-nowrap text-base font-semibold text-accent md:mt-0 md:ms-5",onClick:()=>{J(e=>!e)},children:[R("common:text-filter")," ",U?(0,t.jsx)(a.a,{className:"ms-2"}):(0,t.jsx)(r.K,{className:"ms-2"})]}),(0,t.jsx)("button",{onClick:handleImportModal,className:"hidden h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-50 transition duration-300 ms-5 hover:bg-gray-100 md:flex",children:(0,t.jsx)(o.n,{className:"w-3.5 text-body"})})]})]}),(0,t.jsx)("div",{className:Z()("flex w-full transition",{"visible h-auto":U,"invisible h-0":!U}),children:(0,t.jsx)("div",{className:"mt-5 flex w-full flex-col border-t border-gray-200 pt-5 md:mt-8 md:flex-row md:items-center md:pt-8",children:(0,t.jsx)(u.Z,{className:"w-full",type:G,onCategoryFilter:e=>{A(null==e?void 0:e.slug),B(1)},onTypeFilter:e=>{K(null==e?void 0:e.slug),B(1)},onProductTypeFilter:e=>{L(null==e?void 0:e.slug),B(1)},enableCategory:!0,enableType:!0,enableProductType:!0})})})]}),(0,t.jsx)(c.Z,{products:Y,paginatorInfo:ee,onPagination:function(e){B(e)},onOrder:D,onSort:q})]}))}ProductsPage.authenticate={permissions:S.ce},ProductsPage.Layout=d.Z}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1077,193,9774,2888,179],function(){return e(e.s=43031)}),_N_E=e.O()}]);