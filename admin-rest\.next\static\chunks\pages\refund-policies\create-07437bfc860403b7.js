(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2606,2036],{37077:function(e,n,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/refund-policies/create",function(){return r(2345)}])},2345:function(e,n,r){"use strict";r.r(n),r.d(n,{__N_SSG:function(){return c},default:function(){return CreateRefundPolicyPage}});var t=r(85893),i=r(97670),u=r(55060),a=r(16203),o=r(5233),c=!0;function CreateRefundPolicyPage(){let{t:e}=(0,o.$G)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:form-title-create-refund-policy")})}),(0,t.jsx)(u.Z,{})]})}CreateRefundPolicyPage.authenticate={permissions:a.M$},CreateRefundPolicyPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,9494,5535,8186,1285,1631,1832,8999,9774,2888,179],function(){return e(e.s=37077)}),_N_E=e.O()}]);