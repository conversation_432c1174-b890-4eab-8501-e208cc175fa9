(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7284,9930],{83028:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return create_or_update}});var s=r(85893),o=r(75814),n=r(60802),a=r(33e3),l=r(23091),i=r(83987),u=r(95414),d=r(5233),c=r(16310),m=r(1587),f=r(10265),p=r(73263),v=r(87536),g=r(13718);let x=c.Ry().shape({type:c.Z_().oneOf([f.DL.Billing,f.DL.Shipping]).required("error-type-required"),title:c.Z_().required("error-title-required"),address:c.Ry().shape({country:c.Z_().required("error-country-required"),city:c.Z_().required("error-city-required"),state:c.Z_().required("error-state-required"),zip:c.Z_().required("error-zip-required"),street_address:c.Z_().required("error-street-required")})});var address_form=e=>{var t,r,c,h,y,S,b,E,P,w,_,M,N,A,j;let{onSubmit:C}=e,{t:I}=(0,d.$G)("common"),{useGoogleMap:R}=(0,p.rV)(),{data:{address:Q,type:L}}=(0,o.X9)();return(0,s.jsxs)("div",{className:"min-h-screen p-5 bg-light sm:p-8 md:min-h-0 md:rounded-xl",children:[(0,s.jsxs)("h1",{className:"mb-4 text-lg font-semibold text-center text-heading sm:mb-6",children:[Q?I("text-update"):I("text-add-new")," ",I("text-address")]}),(0,s.jsx)(m.l,{onSubmit:C,className:"grid h-full grid-cols-2 gap-5",validationSchema:x,options:{shouldUnregister:!0,defaultValues:{title:null!==(S=null==Q?void 0:Q.title)&&void 0!==S?S:"",type:null!==(b=null==Q?void 0:Q.type)&&void 0!==b?b:L,address:{city:null!==(E=null==Q?void 0:null===(t=Q.address)||void 0===t?void 0:t.city)&&void 0!==E?E:"",country:null!==(P=null==Q?void 0:null===(r=Q.address)||void 0===r?void 0:r.country)&&void 0!==P?P:"",state:null!==(w=null==Q?void 0:null===(c=Q.address)||void 0===c?void 0:c.state)&&void 0!==w?w:"",zip:null!==(_=null==Q?void 0:null===(h=Q.address)||void 0===h?void 0:h.zip)&&void 0!==_?_:"",street_address:null!==(M=null==Q?void 0:null===(y=Q.address)||void 0===y?void 0:y.street_address)&&void 0!==M?M:"",...null==Q?void 0:Q.address},location:null!==(N=null==Q?void 0:Q.location)&&void 0!==N?N:""}},resetValues:{title:null!==(A=null==Q?void 0:Q.title)&&void 0!==A?A:"",type:null!==(j=null==Q?void 0:Q.type)&&void 0!==j?j:L,...(null==Q?void 0:Q.address)&&Q},children:e=>{var t,r,o,d,c,m,p,x,h,y,S;let{register:b,control:E,getValues:P,setValue:w,formState:{errors:_}}=e;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.Z,{children:I("text-type")}),(0,s.jsxs)("div",{className:"flex items-center space-s-4",children:[(0,s.jsx)(i.Z,{id:"billing",...b("type"),type:"radio",value:f.DL.Billing,label:I("text-billing")}),(0,s.jsx)(i.Z,{id:"shipping",...b("type"),type:"radio",value:f.DL.Shipping,label:I("text-shipping")})]})]}),(0,s.jsx)(a.Z,{label:I("text-title"),...b("title"),error:I(null===(t=_.title)||void 0===t?void 0:t.message),variant:"outline",className:"col-span-2"}),R&&(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)(l.Z,{children:I("text-location")}),(0,s.jsx)(v.Qr,{control:E,name:"location",render:e=>{let{field:{onChange:t}}=e;return(0,s.jsx)(g.Z,{icon:!0,onChange:e=>{t(e),w("address.country",null==e?void 0:e.country),w("address.city",null==e?void 0:e.city),w("address.state",null==e?void 0:e.state),w("address.zip",null==e?void 0:e.zip),w("address.street_address",null==e?void 0:e.street_address)},data:P("location")})}})]}),(0,s.jsx)(a.Z,{label:I("text-country"),...b("address.country"),error:I(null===(o=_.address)||void 0===o?void 0:null===(r=o.country)||void 0===r?void 0:r.message),variant:"outline"}),(0,s.jsx)(a.Z,{label:I("text-city"),...b("address.city"),error:I(null===(c=_.address)||void 0===c?void 0:null===(d=c.city)||void 0===d?void 0:d.message),variant:"outline"}),(0,s.jsx)(a.Z,{label:I("text-state"),...b("address.state"),error:I(null===(p=_.address)||void 0===p?void 0:null===(m=p.state)||void 0===m?void 0:m.message),variant:"outline"}),(0,s.jsx)(a.Z,{label:I("text-zip"),...b("address.zip"),error:I(null===(h=_.address)||void 0===h?void 0:null===(x=h.zip)||void 0===x?void 0:x.message),variant:"outline"}),(0,s.jsx)(u.Z,{label:I("text-street-address"),...b("address.street_address"),error:I(null===(S=_.address)||void 0===S?void 0:null===(y=S.street_address)||void 0===y?void 0:y.message),variant:"outline",className:"col-span-2"}),(0,s.jsxs)(n.Z,{className:"w-full col-span-2",children:[Q?I("text-update"):I("text-save")," ",I("text-address")]})]})}})]})},h=r(99930),create_or_update=()=>{let{data:{customerId:e,address:t}}=(0,o.X9)(),{closeModal:r}=(0,o.SO)(),{mutate:n}=(0,h.kD)();return(0,s.jsx)(address_form,{onSubmit:function(s){let{__typename:o,...a}=s;return n({id:e,input:{address:[{...(null==t?void 0:t.id)?{id:t.id}:{},...a}]}}),r()}})}},13718:function(e,t,r){"use strict";r.d(t,{Z:function(){return GooglePlacesAutocomplete}});var s=r(85893),o=r(37054),n=r(67294),a=r(5233),l=r(55846),i=r(8366),u=r(23322);function GooglePlacesAutocomplete(e){let{onChange:t,onChangeCurrentLocation:r,data:d,disabled:c=!1,icon:m=!1}=e,{t:f}=(0,a.$G)(),[p,v]=(0,n.useState)(""),[g,x,h,y,S,b]=(0,u.ZP)({onChange:t,onChangeCurrentLocation:r,setInputValue:v});return((0,n.useEffect)(()=>{let e=null==d?void 0:d.formattedAddress;v(e)},[d]),b)?(0,s.jsx)("div",{children:f("common:text-map-cant-load")}):S?(0,s.jsxs)("div",{className:"relative",children:[m&&(0,s.jsx)("div",{className:"absolute top-0 left-0 flex h-12 w-10 items-center justify-center text-gray-400",children:(0,s.jsx)(i.$t,{className:"w-[18px]"})}),(0,s.jsx)(o.F2,{onLoad:g,onPlaceChanged:h,onUnmount:x,fields:["address_components","geometry.location","formatted_address"],types:["address"],children:(0,s.jsx)("input",{type:"text",placeholder:f("form:placeholder-search-location"),value:p,onChange:e=>v(e.target.value),className:"flex h-12 w-full appearance-none items-center rounded border border-border-base text-sm text-heading transition duration-300 ease-in-out  focus:border-accent focus:outline-none focus:ring-0 ".concat(c?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":""," ").concat(m?"pe-4 ps-9":"px-4"),disabled:c})})]}):(0,s.jsx)(l.Z,{simple:!0,className:"h-6 w-6"})}},86779:function(e,t,r){"use strict";r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var s=r(85893);let InfoIcon=e=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,s.jsx)("g",{children:(0,s.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,s.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,s.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,s.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,s.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},8366:function(e,t,r){"use strict";r.d(t,{$t:function(){return MapPin},f_:function(){return MapPinIconWithPlatform},tE:function(){return MapPinIcon}});var s=r(85893);let MapPin=e=>{let{...t}=e;return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",...t,children:(0,s.jsx)("path",{d:"M256 0C161.896 0 85.333 76.563 85.333 170.667c0 28.25 7.063 56.26 20.49 81.104L246.667 506.5c1.875 3.396 5.448 5.5 9.333 5.5s7.458-2.104 9.333-5.5l140.896-254.813c13.375-24.76 20.438-52.771 20.438-81.021C426.667 76.563 350.104 0 256 0zm0 256c-47.052 0-85.333-38.281-85.333-85.333S208.948 85.334 256 85.334s85.333 38.281 85.333 85.333S303.052 256 256 256z",fill:"currentColor"})})},MapPinIcon=e=>(0,s.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,s.jsx)("path",{opacity:.2,d:"M8 1.5a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,s.jsx)("path",{d:"M8 4a2.5 2.5 0 100 5 2.5 2.5 0 000-5zm0 4a1.5 1.5 0 110-3 1.5 1.5 0 010 3zm0-7a5.506 5.506 0 00-5.5 5.5c0 1.963.907 4.043 2.625 6.016.772.891 1.64 1.694 2.59 2.393a.5.5 0 00.574 0c.948-.7 1.816-1.502 2.586-2.393C12.591 10.543 13.5 8.463 13.5 6.5A5.506 5.506 0 008 1zm0 12.875c-1.033-.813-4.5-3.797-4.5-7.375a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375z",fill:"currentColor"})]}),MapPinIconWithPlatform=e=>(0,s.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,s.jsx)("path",{opacity:.2,d:"M8 2a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,s.jsx)("path",{d:"M12.5 14.5H9.409c.519-.464 1.009-.96 1.466-1.484C12.591 11.043 13.5 8.963 13.5 7a5.5 5.5 0 00-11 0c0 1.963.907 4.043 2.625 6.016.457.525.947 1.02 1.466 1.484H3.5a.5.5 0 000 1h9a.5.5 0 000-1zM3.5 7a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375C6.967 13.562 3.5 10.577 3.5 7zm7 0a2.5 2.5 0 10-5 0 2.5 2.5 0 005 0zm-4 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z",fill:"currentColor"})]})},1587:function(e,t,r){"use strict";r.d(t,{l:function(){return Form}});var s=r(85893),o=r(87536),n=r(47533),a=r(67294);let Form=e=>{let{onSubmit:t,children:r,options:l,validationSchema:i,serverError:u,resetValues:d,...c}=e,m=(0,o.cI)({...!!i&&{resolver:(0,n.X)(i)},...!!l&&l});return(0,a.useEffect)(()=>{u&&Object.entries(u).forEach(e=>{let[t,r]=e;m.setError(t,{type:"manual",message:r})})},[u,m]),(0,a.useEffect)(()=>{d&&m.reset(d)},[d,m]),(0,s.jsx)("form",{onSubmit:m.handleSubmit(t),noValidate:!0,...c,children:r(m)})}},33e3:function(e,t,r){"use strict";var s=r(85893),o=r(71611),n=r(93967),a=r.n(n),l=r(67294),i=r(98388);let u={small:"text-sm h-10",medium:"h-12",big:"h-14"},d=l.forwardRef((e,t)=>{let{className:r,label:n,note:l,name:d,error:c,children:m,variant:f="normal",dimension:p="medium",shadow:v=!1,type:g="text",inputClassName:x,disabled:h,showLabel:y=!0,required:S,toolTipText:b,labelClassName:E,...P}=e,w=a()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===f,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===f,"border border-border-base focus:border-accent":"outline"===f},{"focus:shadow":v},u[p],x),_="number"===g&&h?"number-disable":"";return(0,s.jsxs)("div",{className:(0,i.m6)(r),children:[y||n?(0,s.jsx)(o.Z,{htmlFor:d,toolTipText:b,label:n,required:S,className:E}):"",(0,s.jsx)("input",{id:d,name:d,type:g,ref:t,className:(0,i.m6)(a()(h?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(_," select-none"):"",w)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:h,"aria-invalid":c?"true":"false",...P}),l&&(0,s.jsx)("p",{className:"mt-2 text-xs text-body",children:l}),c&&(0,s.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:c})]})});d.displayName="Input",t.Z=d},23091:function(e,t,r){"use strict";var s=r(85893),o=r(93967),n=r.n(o),a=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,s.jsx)("label",{className:(0,a.m6)(n()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},83987:function(e,t,r){"use strict";var s=r(85893),o=r(67294),n=r(29974),a=r.n(n);let l=o.forwardRef((e,t)=>{let{className:r,label:o,name:n,id:l,error:i,...u}=e;return(0,s.jsxs)("div",{className:r,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:l,name:n,type:"radio",ref:t,className:a().radio_input,...u}),(0,s.jsx)("label",{htmlFor:l,className:"text-sm text-body",children:o})]}),i&&(0,s.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:i})]})});l.displayName="Radio",t.Z=l},95414:function(e,t,r){"use strict";var s=r(85893),o=r(71611),n=r(93967),a=r.n(n),l=r(67294),i=r(98388);let u=l.forwardRef((e,t)=>{let{className:r,label:n,toolTipText:l,name:u,error:d,variant:c="normal",shadow:m=!1,inputClassName:f,disabled:p,required:v,...g}=e,x=a()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===c,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===c,"border border-border-base focus:border-accent":"outline"===c},{"focus:shadow":m},f);return(0,s.jsxs)("div",{className:(0,i.m6)(a()(r)),children:[n&&(0,s.jsx)(o.Z,{htmlFor:u,toolTipText:l,label:n,required:v}),(0,s.jsx)("textarea",{id:u,name:u,className:(0,i.m6)(a()(x,p?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:p,...g}),d&&(0,s.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:d})]})});u.displayName="TextArea",t.Z=u},71611:function(e,t,r){"use strict";var s=r(85893),o=r(86779),n=r(71943),a=r(23091),l=r(98388);t.Z=e=>{let{className:t,required:r,label:i,toolTipText:u,htmlFor:d}=e;return(0,s.jsxs)(a.Z,{className:(0,l.m6)(t),htmlFor:d,children:[i,r?(0,s.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",u?(0,s.jsx)(n.u,{content:u,children:(0,s.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,s.jsx)(o.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){"use strict";r.d(t,{u:function(){return Tooltip}});var s=r(85893),o=r(67294),n=r(93075),a=r(82364),l=r(24750),i=r(93967),u=r.n(i),d=r(67421),c=r(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},f={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:i=8,animation:p="zoomIn",placement:v="top",size:g="md",rounded:x="DEFAULT",shadow:h="md",color:y="default",className:S,arrowClassName:b,showArrow:E=!0}=e,[P,w]=(0,o.useState)(!1),_=(0,o.useRef)(null),{t:M}=(0,d.$G)(),{x:N,y:A,refs:j,strategy:C,context:I}=(0,n.YF)({placement:v,open:P,onOpenChange:w,middleware:[(0,a.x7)({element:_}),(0,a.cv)(i),(0,a.RR)(),(0,a.uY)({padding:8})],whileElementsMounted:l.Me}),{getReferenceProps:R,getFloatingProps:Q}=(0,n.NI)([(0,n.XI)(I),(0,n.KK)(I),(0,n.qs)(I,{role:"tooltip"}),(0,n.bQ)(I)]),{isMounted:L,styles:T}=(0,n.Y_)(I,{duration:{open:150,close:150},...f[p]});return(0,s.jsxs)(s.Fragment,{children:[(0,o.cloneElement)(t,R({ref:j.setReference,...t.props})),(L||P)&&(0,s.jsx)(n.ll,{children:(0,s.jsxs)("div",{role:"tooltip",ref:j.setFloating,className:(0,c.m6)(u()(m.base,m.size[g],m.rounded[x],m.variant.solid.base,m.variant.solid.color[y],m.shadow[h],S)),style:{position:C,top:null!=A?A:0,left:null!=N?N:0,...T},...Q(),children:[M("".concat(r)),E&&(0,s.jsx)(n.Y$,{ref:_,context:I,className:u()(m.arrow.color[y],b),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},44498:function(e,t,r){"use strict";r.d(t,{B:function(){return n}});var s=r(47869),o=r(3737);let n={me:()=>o.eN.get(s.P.ME),login:e=>o.eN.post(s.P.TOKEN,e),logout:()=>o.eN.post(s.P.LOGOUT,{}),register:e=>o.eN.post(s.P.REGISTER,e),update:e=>{let{id:t,input:r}=e;return o.eN.put("".concat(s.P.USERS,"/").concat(t),r)},changePassword:e=>o.eN.post(s.P.CHANGE_PASSWORD,e),forgetPassword:e=>o.eN.post(s.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>o.eN.post(s.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>o.eN.post(s.P.RESET_PASSWORD,e),makeAdmin:e=>o.eN.post(s.P.MAKE_ADMIN,e),block:e=>o.eN.post(s.P.BLOCK_USER,e),unblock:e=>o.eN.post(s.P.UNBLOCK_USER,e),addWalletPoints:e=>o.eN.post(s.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>o.eN.post(s.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...r}=e;return o.eN.get(s.P.USERS,{searchJoin:"and",with:"wallet",...r,search:o.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return o.eN.get(s.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return o.eN.get("".concat(s.P.USERS,"/").concat(t))},resendVerificationEmail:()=>o.eN.post(s.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return o.eN.post(s.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...r}=e;return o.eN.get(s.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...r})},fetchCustomers:e=>{let{...t}=e;return o.eN.get(s.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:r,name:n,...a}=e;return o.eN.get(s.P.MY_STAFFS,{searchJoin:"and",shop_id:r,...a,search:o.eN.formatSearchParams({name:n,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:r,...n}=e;return o.eN.get(s.P.ALL_STAFFS,{searchJoin:"and",...n,search:o.eN.formatSearchParams({name:r,is_active:t})})}}},99930:function(e,t,r){"use strict";r.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var s=r(79362),o=r(97514),n=r(31955),a=r(5233),l=r(11163),i=r(88767),u=r(22920),d=r(47869),c=r(44498),m=r(28597),f=r(87066),p=r(16203);let useMeQuery=()=>{let e=(0,i.useQueryClient)(),t=(0,l.useRouter)();return(0,i.useQuery)([d.P.ME],c.B.me,{retry:!1,onSuccess:()=>{t.pathname===o.Z.verifyLicense&&t.replace(o.Z.dashboard),t.pathname===o.Z.verifyEmail&&((0,p.Fu)(!0),t.replace(o.Z.dashboard))},onError:r=>{if(f.Z.isAxiosError(r)){var s,n;if((null===(s=r.response)||void 0===s?void 0:s.status)===417){t.replace(o.Z.verifyLicense);return}if((null===(n=r.response)||void 0===n?void 0:n.status)===409){(0,p.Fu)(!1),t.replace(o.Z.verifyEmail);return}e.clear(),t.replace(o.Z.login)}}})};function useLogin(){return(0,i.useMutation)(c.B.login)}let useLogoutMutation=()=>{let e=(0,l.useRouter)(),{t}=(0,a.$G)();return(0,i.useMutation)(c.B.logout,{onSuccess:()=>{n.Z.remove(s.E$),e.replace(o.Z.login),u.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)();return(0,i.useMutation)(c.B.register,{onSuccess:()=>{u.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(d.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.update,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.ME),t.invalidateQueries(d.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.updateEmail,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};u.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(d.P.ME),t.invalidateQueries(d.P.USERS)}})},useChangePasswordMutation=()=>(0,i.useMutation)(c.B.changePassword),useForgetPasswordMutation=()=>(0,i.useMutation)(c.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,a.$G)("common");return(0,i.useMutation)(c.B.resendVerificationEmail,{onSuccess:()=>{u.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,u.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,a.$G)();(0,i.useQueryClient)();let t=(0,l.useRouter)();return(0,i.useMutation)(c.B.addLicenseKey,{onSuccess:()=>{u.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{u.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,i.useMutation)(c.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,i.useMutation)(c.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)();return(0,i.useMutation)(c.B.makeAdmin,{onSuccess:()=>{u.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(d.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)();return(0,i.useMutation)(c.B.block,{onSuccess:()=>{u.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(d.P.USERS),e.invalidateQueries(d.P.STAFFS),e.invalidateQueries(d.P.ADMIN_LIST),e.invalidateQueries(d.P.CUSTOMERS),e.invalidateQueries(d.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)();return(0,i.useMutation)(c.B.unblock,{onSuccess:()=>{u.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(d.P.USERS),e.invalidateQueries(d.P.STAFFS),e.invalidateQueries(d.P.ADMIN_LIST),e.invalidateQueries(d.P.CUSTOMERS),e.invalidateQueries(d.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,i.useQueryClient)();return(0,i.useMutation)(c.B.addWalletPoints,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,i.useQuery)([d.P.USERS,t],()=>c.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.USERS,e],()=>c.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}},useAdminsQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.ADMIN_LIST,e],()=>c.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}},useVendorsQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.VENDORS_LIST,e],()=>c.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}},useCustomersQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.CUSTOMERS,e],()=>c.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}},useMyStaffsQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.MY_STAFFS,e],()=>c.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}},useAllStaffsQuery=e=>{var t;let{data:r,isLoading:s,error:o}=(0,i.useQuery)([d.P.ALL_STAFFS,e],()=>c.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(r),loading:s,error:o}}},23322:function(e,t,r){"use strict";r.d(t,{ZP:function(){return useLocation},lH:function(){return l}});var s=r(37054),o=r(67294),n=r(67421),a=r(15103);let l=(0,a.cn)(null),i=["places"];function useLocation(e){let{onChange:t,onChangeCurrentLocation:r,setInputValue:a}=e,{t:l}=(0,n.$G)(),[u,d]=(0,o.useState)(null),{isLoaded:c,loadError:m}=(0,s.Ji)({id:"google_map_autocomplete",googleMapsApiKey:"",libraries:i}),f=(0,o.useCallback)(e=>{d(e)},[]),p=(0,o.useCallback)(()=>{d(!0)},[]);return[f,p,()=>{var e;let t=null==u?void 0:u.getPlace();null==t||null===(e=t.geometry)||void 0===e||e.location},()=>{var e,t;(null===(e=navigator)||void 0===e?void 0:e.geolocation)?null===(t=navigator)||void 0===t||t.geolocation.getCurrentPosition(async e=>{let{latitude:t,longitude:s}=e.coords,o=new google.maps.Geocoder;o.geocode({location:{lat:t,lng:s}},(e,t)=>{if("OK"===t&&(null==e?void 0:e[0])){let t=function(e){var t,r,s,o;let n={lat:null==e?void 0:null===(t=e.geometry)||void 0===t?void 0:t.location.lat(),lng:null==e?void 0:null===(r=e.geometry)||void 0===r?void 0:r.location.lng(),formattedAddress:e.formatted_address},a={postal_code:"zip",postal_code_suffix:"zip",state_name:"street_address",route:"street_address",sublocality_level_1:"street_address",locality:"city",administrative_area_level_1:"state",country:"country"};for(let t of null==e?void 0:e.address_components){let[e]=t.types,{long_name:r,short_name:l}=t;a[e]&&(null!==(o=n[s=a[e]])&&void 0!==o||(n[s]=r),"postal_code_suffix"===e&&(n.zip="".concat(null==n?void 0:n.zip,"-").concat(r)),"administrative_area_level_1"===e&&(n.state=l))}return n}(null==e?void 0:e[0]);null==r||r(t)}})},e=>{console.error("Error getting current location:",e)}):console.error("Geolocation is not supported by this browser.")},c,m&&l(m)]}(0,a.cn)(e=>{let t=e(l);return t?"".concat(t.street_address,", ").concat(t.city,", ").concat(t.state,", ").concat(t.zip,", ").concat(t.country):""})},29974:function(e){e.exports={radio_input:"radio_radio_input__EWRL5"}}}]);