(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5439,2036],{20640:function(e,a,n){"use strict";var i=n(11742),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,a){var n,l,r,s,d,m,c,t,C=!1;a||(a={}),r=a.debug||!1;try{if(d=i(),m=document.createRange(),c=document.getSelection(),(t=document.createElement("span")).textContent=e,t.ariaHidden="true",t.style.all="unset",t.style.position="fixed",t.style.top=0,t.style.clip="rect(0, 0, 0, 0)",t.style.whiteSpace="pre",t.style.webkitUserSelect="text",t.style.MozUserSelect="text",t.style.msUserSelect="text",t.style.userSelect="text",t.addEventListener("copy",function(n){if(n.stopPropagation(),a.format){if(n.preventDefault(),void 0===n.clipboardData){r&&console.warn("unable to use e.clipboardData"),r&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var i=o[a.format]||o.default;window.clipboardData.setData(i,e)}else n.clipboardData.clearData(),n.clipboardData.setData(a.format,e)}a.onCopy&&(n.preventDefault(),a.onCopy(n.clipboardData))}),document.body.appendChild(t),m.selectNodeContents(t),c.addRange(m),!document.execCommand("copy"))throw Error("copy command was unsuccessful");C=!0}catch(i){r&&console.error("unable to copy using execCommand: ",i),r&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(a.format||"text",e),a.onCopy&&a.onCopy(window.clipboardData),C=!0}catch(i){r&&console.error("unable to copy using clipboardData: ",i),r&&console.error("falling back to prompt"),n="message"in a?a.message:"Copy to clipboard: #{key}, Enter",l=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",s=n.replace(/#{\s*key\s*}/g,l),window.prompt(s,e)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(m):c.removeAllRanges()),t&&document.body.removeChild(t),d()}return C}},26508:function(e,a,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/payment",function(){return n(56221)}])},3160:function(e,a,n){"use strict";n.d(a,{r:function(){return StarIcon}});var i=n(85893);let StarIcon=e=>(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25.056 24",...e,children:(0,i.jsx)("g",{"data-name":"Group 36413",fill:"currentColor",children:(0,i.jsx)("path",{id:"Path_22667","data-name":"Path 22667",d:"M19.474,34.679l-6.946-4.346L5.583,34.679a.734.734,0,0,1-1.1-.8L6.469,25.93.263,20.668a.735.735,0,0,1,.421-1.3l8.1-.566,3.064-7.6a.765.765,0,0,1,1.362,0l3.064,7.6,8.1.566a.735.735,0,0,1,.421,1.3L18.588,25.93l1.987,7.949a.734.734,0,0,1-1.1.8Z",transform:"translate(0 -10.792)"})})})},8953:function(e,a,n){"use strict";var i=n(85893),o=n(93967),l=n.n(o),r=n(5233),s=n(98388);a.Z=e=>{let{t:a}=(0,r.$G)(),{className:n,color:o,textColor:d,text:m,textKey:c,animate:t=!1}=e,C={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("span",{className:(0,s.m6)(l()("inline-block",C.root,{[C.default]:!o,[C.text]:!d,[C.animate]:t},o,d,n)),children:c?a(c):m})})}},33e3:function(e,a,n){"use strict";var i=n(85893),o=n(71611),l=n(93967),r=n.n(l),s=n(67294),d=n(98388);let m={small:"text-sm h-10",medium:"h-12",big:"h-14"},c=s.forwardRef((e,a)=>{let{className:n,label:l,note:s,name:c,error:t,children:C,variant:g="normal",dimension:h="medium",shadow:b=!1,type:p="text",inputClassName:y,disabled:v,showLabel:f=!0,required:L,toolTipText:x,labelClassName:_,...A}=e,S=r()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===g,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===g,"border border-border-base focus:border-accent":"outline"===g},{"focus:shadow":b},m[h],y),M="number"===p&&v?"number-disable":"";return(0,i.jsxs)("div",{className:(0,d.m6)(n),children:[f||l?(0,i.jsx)(o.Z,{htmlFor:c,toolTipText:x,label:l,required:L,className:_}):"",(0,i.jsx)("input",{id:c,name:c,type:p,ref:a,className:(0,d.m6)(r()(v?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(M," select-none"):"",S)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:v,"aria-invalid":t?"true":"false",...A}),s&&(0,i.jsx)("p",{className:"mt-2 text-xs text-body",children:s}),t&&(0,i.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:t})]})});c.displayName="Input",a.Z=c},77180:function(e,a,n){"use strict";var i=n(85893),o=n(66271),l=n(71611),r=n(77768),s=n(93967),d=n.n(s),m=n(5233),c=n(87536),t=n(98388);a.Z=e=>{let{control:a,label:n,name:s,error:C,disabled:g,required:h,toolTipText:b,className:p,labelClassName:y,...v}=e,{t:f}=(0,m.$G)();return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:(0,t.m6)(d()("flex items-center gap-x-4",p)),children:[(0,i.jsx)(c.Qr,{name:s,control:a,...v,render:e=>{let{field:{onChange:a,value:o}}=e;return(0,i.jsxs)(r.r,{checked:o,onChange:a,disabled:g,className:"".concat(o?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(g?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,i.jsxs)("span",{className:"sr-only",children:["Enable ",n]}),(0,i.jsx)("span",{className:"".concat(o?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),n?(0,i.jsx)(l.Z,{htmlFor:s,className:d()("mb-0",y),toolTipText:b,label:n,required:h}):""]}),C?(0,i.jsx)(o.Z,{message:C}):""]})}},56221:function(e,a,n){"use strict";n.r(a),n.d(a,{__N_SSG:function(){return z},default:function(){return PaymentSettings}});var i=n(85893),o=n(97670),l=n(92072),r=n(12032);let s=[{name:"English (United States)",code:"en-US"},{name:"Cherokee (United States)",code:"chr-US"},{name:"Spanish (United States)",code:"es-US"},{name:"Hawaiian (United States)",code:"haw-US"},{name:"English (United Kingdom)",code:"en-GB"},{name:"Welsh (United Kingdom)",code:"cy-GB"},{name:"Scottish Gaelic (United Kingdom)",code:"gd-GB"},{name:"Cornish (United Kingdom)",code:"kw-GB"},{name:"English (Canada)",code:"en-CA"},{name:"French (Canada)",code:"fr-CA"},{name:"Arabic",code:"ar"},{name:"Arabic (United Arab Emirates)",code:"ar-AE"},{name:"Arabic (Bahrain)",code:"ar-BH"},{name:"Arabic (Djibouti)",code:"ar-DJ"},{name:"Arabic (Algeria)",code:"ar-DZ"},{name:"Arabic (Egypt)",code:"ar-EG"},{name:"Arabic (Western Sahara)",code:"ar-EH"},{name:"Arabic (Eritrea)",code:"ar-ER"},{name:"Arabic (Israel)",code:"ar-IL"},{name:"Arabic (Iraq)",code:"ar-IQ"},{name:"Arabic (Iraq)",code:"ar-IQ"},{name:"Arabic (Jordan)",code:"ar-JO"},{name:"Arabic (Jordan)",code:"ar-JO"},{name:"Arabic (Comoros)",code:"ar-KM"},{name:"Arabic (Kuwait)",code:"ar-KW"},{name:"Arabic (Lebanon)",code:"ar-LB"},{name:"Arabic (Libya)",code:"ar-LY"},{name:"Arabic (Morocco)",code:"ar-MA"},{name:"Arabic (Mauritania)",code:"ar-MR"},{name:"Arabic (Oman)",code:"ar-OM"},{name:"Arabic (Palestinian Territories)",code:"ar-PS"},{name:"Arabic (Qatar)",code:"ar-QA"},{name:"Arabic (Saudi Arabia)",code:"ar-SA"},{name:"Arabic (Sudan)",code:"ar-SD"},{name:"Arabic (Somalia)",code:"ar-SO"},{name:"Arabic (South Sudan)",code:"ar-SS"},{name:"Arabic (Syria)",code:"ar-SY"},{name:"Arabic (Chad)",code:"ar-TD"},{name:"Arabic (Tunisia)",code:"ar-TN"},{name:"Arabic (Yemen)",code:"ar-YE"},{name:"Assamese",code:"as"},{name:"Assamese (India)",code:"as-IN"},{name:"Asu",code:"asa"},{name:"Asu (Tanzania)",code:"asa-TZ"},{name:"Azerbaijani",code:"az"},{name:"Azerbaijani (Cyrillic)",code:"az-Cyrl"},{name:"Azerbaijani (Cyrillic, Azerbaijan)",code:"az-Cyrl-AZ"},{name:"Azerbaijani (Latin)",code:"az-Latn"},{name:"Azerbaijani (Latin, Azerbaijan)",code:"az-Latn-AZ"},{name:"Basaa",code:"bas"},{name:"Basaa (Cameroon)",code:"bas-CM"},{name:"Belarusian",code:"be"},{name:"Belarusian (Belarus)",code:"be-BY"},{name:"Bemba",code:"bem"},{name:"Bemba (Zambia)",code:"bem-ZM"},{name:"Bena",code:"bez"},{name:"Bena (Tanzania)",code:"bez-TZ"},{name:"Bulgarian",code:"bg"},{name:"Bulgarian (Bulgaria)",code:"bg-BG"},{name:"Bambara",code:"bm"},{name:"Bambara (Mali)",code:"bm-ML"},{name:"Bangla (bn)",code:"bn"},{name:"Bengali",code:"bn"},{name:"Bengali (Bangladesh)",code:"bn-BD"},{name:"Bangla (Bangladesh)",code:"bn-BD"},{name:"Bengali (India)",code:"bn-IN"},{name:"Bangla (India)",code:"bn-IN"},{name:"Tibetan",code:"bo"},{name:"Tibetan (China)",code:"bo-CN"},{name:"Tibetan (India)",code:"bo-IN"},{name:"Breton",code:"br"},{name:"Breton (France)",code:"br-FR"},{name:"Bodo",code:"brx"},{name:"Bodo (India)",code:"brx-IN"},{name:"Bosnian",code:"bs"},{name:"Bosnian (Cyrillic)",code:"bs-Cyrl"},{name:"Bosnian (Cyrillic, Bosnia & Herzegovina)",code:"bs-Cyrl-BA"},{name:"Bosnian (Latin)",code:"bs-Latn"},{name:"Bosnian (Latin, Bosnia & Herzegovina)",code:"bs-Latn-BA"},{name:"Catalan",code:"ca"},{name:"Catalan (Andorra)",code:"ca-AD"},{name:"Catalan (Spain)",code:"ca-ES"},{name:"Catalan (France)",code:"ca-FR"},{name:"Catalan (Italy)",code:"ca-IT"},{name:"Chechen\xa0(ce)",code:"ce"},{name:"Chechen (Russia)",code:"ce-RU"},{name:"Chiga (cgg)",code:"cgg"},{name:"Chiga (Uganda)",code:"cgg-UG"},{name:"Cherokee",code:"chr"},{name:"Czech",code:"cs"},{name:"Czech (Czech Republic)",code:"cs-CZ"},{name:"Welsh",code:"cy"},{name:"Danish",code:"da"},{name:"Danish (Denmark)",code:"da-DK"},{name:"Taita",code:"dav"},{name:"Taita (Kenya)",code:"dav-KE"},{name:"German",code:"de"},{name:"German (Austria)",code:"de-AT"},{name:"German (Belgium)",code:"de-BE"},{name:"German (Switzerland)",code:"de-CH"},{name:"German (Germany)",code:"de-DE"},{name:"German (Italy)",code:"de-IT"},{name:"German (Liechtenstein)",code:"de-LI"},{name:"German (Luxembourg)",code:"de-LU"},{name:"German (Luxembourg)",code:"de-LU"},{name:"Zarma",code:"dje"},{name:"Zarma (Niger)",code:"dje-NE"},{name:"Lower Sorbian",code:"dsb"},{name:"Lower Sorbian (Germany)",code:"dsb-DE"},{name:"Duala",code:"dua"},{name:"Duala (Cameroon)",code:"dua-CM"},{name:"Jola-Fonyi",code:"dyo"},{name:"Jola-Fonyi (Senegal)",code:"dyo-SN"},{name:"Dzongkha",code:"dz"},{name:"Dzongkha (Bhutan)",code:"dz-BT"},{name:"Embu",code:"ebu"},{name:"Embu (Kenya)",code:"ebu-KE"},{name:"Ewe (ee)",code:"ee"},{name:"Ewe (Ghana)",code:"ee-GH"},{name:"Ewe (Togo)",code:"ee-TG"},{name:"Greek",code:"el"},{name:"Greek (Cyprus)",code:"el-CY"},{name:"Greek (Greece)",code:"el-GR"},{name:"English",code:"en"},{name:"English (Antigua & Barbuda)",code:"en-AG"},{name:"English (Anguilla)",code:"en-AI"},{name:"English (Anguilla)",code:"en-AI"},{name:"English (American Samoa)",code:"en-AS"},{name:"English (Austria)",code:"en-AT"},{name:"English (Australia)",code:"en-AU"},{name:"English (Australia)",code:"en-AU"},{name:"English (Barbados)",code:"en-BB"},{name:"English (Bangladesh)",code:"en-BD"},{name:"English (Belgium)",code:"en-BE"},{name:"English (Burundi)",code:"en-BI"},{name:"English (Bermuda)",code:"en-BM"},{name:"English (Bahamas)",code:"en-BS"},{name:"English (Botswana)",code:"en-BW"},{name:"English (Belize)",code:"en-BZ"},{name:"English (Cocos [Keeling] Islands)",code:"en-CC"},{name:"English (Switzerland)",code:"en-CH"},{name:"English (Cook Islands)",code:"en-CK"},{name:"English (Cameroon)",code:"en-CM"},{name:"English (Christmas Island)",code:"en-CX"},{name:"English (Cyprus)",code:"en-CY"},{name:"English (Germany)",code:"en-DE"},{name:"English (Diego Garcia)",code:"en-DG"},{name:"English (Denmark)",code:"en-DK"},{name:"English (Dominica)",code:"en-DM"},{name:"English (Eritrea)",code:"en-ER"},{name:"English (Finland)",code:"en-FI"},{name:"English (Fiji)",code:"en-FJ"},{name:"English (Falkland Islands)",code:"en-FK"},{name:"English (Micronesia)",code:"en-FM"},{name:"English (Grenada)",code:"en-GD"},{name:"English (Guernsey)",code:"en-GG"},{name:"English (Ghana)",code:"en-GH"},{name:"English (Gibraltar)",code:"en-GI"},{name:"English (Gambia)",code:"en-GM"},{name:"English (Guam)",code:"en-GU"},{name:"English (Guyana)",code:"en-GY"},{name:"English (Hong Kong SAR China)",code:"en-HK"},{name:"English (Ireland)",code:"en-IE"},{name:"English (Ireland)",code:"en-IE"},{name:"English (Israel)",code:"en-IL"},{name:"English (Isle of Man)",code:"en-IM"},{name:"English (India)",code:"en-IN"},{name:"English (India)",code:"en-IN"},{name:"English (British Indian Ocean Territory)",code:"en-IO"},{name:"English (Jersey)",code:"en-JE"},{name:"English (Jamaica)",code:"en-JM"},{name:"English (Kenya)",code:"en-KE"},{name:"English (Kiribati)",code:"en-KI"},{name:"English (St. Kitts & Nevis)",code:"en-KN"},{name:"English (Cayman Islands)",code:"en-KY"},{name:"English (St. Lucia)",code:"en-LC"},{name:"English (Liberia)",code:"en-LR"},{name:"English (Lesotho)",code:"en-LS"},{name:"English (Madagascar)",code:"en-MG"},{name:"English (Marshall Islands)",code:"en-MH"},{name:"English (Macau SAR China)",code:"en-MO"},{name:"English (Northern Mariana Islands)",code:"en-MP"},{name:"English (Montserrat)",code:"en-MS"},{name:"English (Malta)",code:"en-MT"},{name:"English (Malta)",code:"en-MT"},{name:"English (Mauritius)",code:"en-MU"},{name:"English (Malawi)",code:"en-MW"},{name:"English (Malaysia)",code:"en-MY"},{name:"English (Namibia)",code:"en-NA"},{name:"English (Norfolk Island)",code:"en-NF"},{name:"English (Nigeria)",code:"en-NG"},{name:"English (Netherlands)",code:"en-NL"},{name:"English (Nauru)",code:"en-NR"},{name:"English (Niue)",code:"en-NU"},{name:"English (New Zealand)",code:"en-NZ"},{name:"English (New Zealand)",code:"en-NZ"},{name:"English (Papua New Guinea)",code:"en-PG"},{name:"English (Philippines)",code:"en-PH"},{name:"English (Philippines)",code:"en-PH"},{name:"English (Pakistan)",code:"en-PK"},{name:"English (Pitcairn Islands)",code:"en-PN"},{name:"English (Puerto Rico)",code:"en-PR"},{name:"English (Palau)",code:"en-PW"},{name:"English (Rwanda)",code:"en-RW"},{name:"English (Solomon Islands)",code:"en-SB"},{name:"English (Seychelles)",code:"en-SC"},{name:"English (Sudan)",code:"en-SD"},{name:"English (Sweden)",code:"en-SE"},{name:"English (Singapore)",code:"en-SG"},{name:"English (Singapore)",code:"en-SG"},{name:"English (St. Helena)",code:"en-SH"},{name:"English (Slovenia)",code:"en-SI"},{name:"English (Sierra Leone)",code:"en-SL"},{name:"English (South Sudan)",code:"en-SS"},{name:"English (Sint Maarten)",code:"en-SX"},{name:"English (Swaziland)",code:"en-SZ"},{name:"English (Turks & Caicos Islands)",code:"en-TC"},{name:"English (Tokelau)",code:"en-TK"},{name:"English (Tonga)",code:"en-TO"},{name:"English (Trinidad & Tobago)",code:"en-TT"},{name:"English (Tuvalu)",code:"en-TV"},{name:"English (Tanzania)",code:"en-TZ"},{name:"English (Uganda)",code:"en-UG"},{name:"English (U.S. Outlying Islands)",code:"en-UM"},{name:"English (St. Vincent & Grenadines)",code:"en-VC"},{name:"English (British Virgin Islands)",code:"en-VG"},{name:"English (U.S. Virgin Islands)",code:"en-VI"},{name:"English (Vanuatu)",code:"en-VU"},{name:"English (Samoa)",code:"en-WS"},{name:"English (South Africa)",code:"en-ZA"},{name:"English (South Africa)",code:"en-ZA"},{name:"English (Zambia)",code:"en-ZM"},{name:"English (Zimbabwe)",code:"en-ZW"},{name:"Esperanto",code:"eo"},{name:"Spanish",code:"es"},{name:"Spanish (Argentina)",code:"es-AR"},{name:"Spanish (Bolivia)",code:"es-BO"},{name:"Spanish (Brazil)",code:"es-BR"},{name:"Spanish (Belize)",code:"es-BZ"},{name:"Spanish (Chile)",code:"es-CL"},{name:"Spanish (Colombia)",code:"es-CO"},{name:"Spanish (Costa Rica)",code:"es-CR"},{name:"Spanish (Cuba)",code:"es-CU"},{name:"Spanish (Dominican Republic)",code:"es-DO"},{name:"Spanish (Ceuta & Melilla",code:"es-EA"},{name:"Spanish (Ecuador)",code:"es-EC"},{name:"Spanish (Spain)",code:"es-ES"},{name:"Spanish (Equatorial Guinea)",code:"es-GQ"},{name:"Spanish (Guatemala)",code:"es-GT"},{name:"Spanish (Honduras)",code:"es-HN"},{name:"Spanish (Canary Islands)",code:"es-IC"},{name:"Spanish (Mexico)",code:"es-MX"},{name:"Spanish (Nicaragua)",code:"es-NI"},{name:"Spanish (Panama)",code:"es-PA"},{name:"Spanish (Peru)",code:"es-PE"},{name:"Spanish (Puerto Rico)",code:"es-PR"},{name:"Spanish (Paraguay)",code:"es-PY"},{name:"Spanish (El Salvador)",code:"es-SV"},{name:"Spanish (Uruguay)",code:"es-UY"},{name:"Spanish (Venezuela)",code:"es-VE"},{name:"Estonian",code:"et"},{name:"Estonian (Estonia)",code:"et-EE"},{name:"Basque\xa0(eu)",code:"eu"},{name:"Basque (Spain)",code:"eu-ES"},{name:"Ewondo",code:"ewo"},{name:"Ewondo (Cameroon)",code:"ewo-CM"},{name:"Persian",code:"fa"},{name:"Persian (Afghanistan)",code:"fa-AF"},{name:"Persian (Iran)",code:"fa-IR"},{name:"Fulah",code:"ff"},{name:"Fulah (Cameroon",code:"ff-CM"},{name:"Fulah (Guinea)",code:"ff-GN"},{name:"Fulah (Mauritania)",code:"ff-MR"},{name:"Fulah (Senegal)",code:"ff-SN"},{name:"Finnish",code:"fi"},{name:"Finnish (Finland)",code:"fi-FI"},{name:"Filipino",code:"fil"},{name:"Filipino (Philippines)",code:"fil-PH"},{name:"Faroese",code:"fo"},{name:"Faroese (Denmark)",code:"fo-DK"},{name:"Faroese (Faroe Islands)",code:"fo-FO"},{name:"French",code:"fr"},{name:"French (Belgium)",code:"fr-BE"},{name:"French (Burkina Faso)",code:"fr-BF"},{name:"French (Burundi)",code:"fr-BI"},{name:"French (Benin)",code:"fr-BJ"},{name:"French (St. Barth\xe9lemy)",code:"fr-BL"},{name:"French (Congo - Kinshasa)",code:"fr-CD"},{name:"French (Central African Republic)",code:"fr-CF"},{name:"French (Congo - Brazzaville)",code:"fr-CG"},{name:"French (Switzerland)",code:"fr-CH"},{name:"French (C\xf4te d’Ivoire)",code:"fr-CI"},{name:"French (Cameroon)",code:"fr-CM"},{name:"French (Djibouti)",code:"fr-DJ"},{name:"French (Algeria)",code:"fr-DZ"},{name:"French (France)",code:"fr-FR"},{name:"French (Gabon)",code:"fr-GA"},{name:"French (French Guiana)",code:"fr-GF"},{name:"French (Guinea)",code:"fr-GN"},{name:"French (Guadeloupe)",code:"fr-GP"},{name:"French (Equatorial Guinea)",code:"fr-GQ"},{name:"French (Haiti)",code:"fr-HT"},{name:"French (Comoros)",code:"fr-KM"},{name:"French (Luxembourg)",code:"fr-LU"},{name:"French (Morocco)",code:"fr-MA"},{name:"French (Monaco)",code:"fr-MC"},{name:"French (St. Martin)",code:"fr-MF"},{name:"French (Madagascar)",code:"fr-MG"},{name:"French (Mali)",code:"fr-ML"},{name:"French (Martinique)",code:"fr-MQ"},{name:"French (Mauritania)",code:"fr-MR"},{name:"French (Mauritius)",code:"fr-MU"},{name:"French (New Caledonia)",code:"fr-NC"},{name:"French (Niger)",code:"fr-NE"},{name:"French (French Polynesia)",code:"fr-PF"},{name:"French (St. Pierre & Miquelon)",code:"fr-PM"},{name:"French (R\xe9union)",code:"fr-RE"},{name:"French (Rwanda)",code:"fr-RW"},{name:"French (Seychelles)",code:"fr-SC"},{name:"French (Senegal)",code:"fr-SN"},{name:"French (Syria)",code:"fr-SY"},{name:"French (Chad)",code:"fr-TD"},{name:"French (Togo)",code:"fr-TG"},{name:"French (Tunisia)",code:"fr-TN"},{name:"French (Vanuatu)",code:"fr-VU"},{name:"French (Wallis & Futuna)",code:"fr-WF"},{name:"French (Mayotte)",code:"fr-YT"},{name:"Friulian",code:"fur"},{name:"Friulian (Italy)",code:"fur-IT"},{name:"Western Frisian",code:"fy"},{name:"Western Frisian (Netherlands)",code:"fy-NL"},{name:"Irish",code:"ga"},{name:"Irish",code:"ga"},{name:"Irish (Ireland)",code:"ga-IE"},{name:"Irish (Ireland)",code:"ga-IE"},{name:"Scottish Gaelic",code:"gd"},{name:"Galician",code:"gl"},{name:"Galician (Spain)",code:"gl-ES"},{name:"Swiss German",code:"gsw"},{name:"Swiss German (Switzerland)",code:"gsw-CH"},{name:"Swiss German (France)",code:"gsw-FR"},{name:"Swiss German (Liechtenstein)",code:"gsw-LI"},{name:"Gujarati",code:"gu"},{name:"Gujarati (India)",code:"gu-IN"},{name:"Gusii",code:"guz"},{name:"Gusii (Kenya)",code:"guz-KE"},{name:"Manx",code:"gv"},{name:"Manx (Isle of Man)",code:"gv-IM"},{name:"Hausa",code:"ha"},{name:"Hausa (Ghana)",code:"ha-GH"},{name:"Hausa (Niger)",code:"ha-NE"},{name:"Hausa (Nigeria)",code:"ha-NG"},{name:"Hawaiian",code:"haw"},{name:"Hebrew",code:"he"},{name:"Hebrew (Israel)",code:"he-IL"},{name:"Hindi",code:"hi"},{name:"Hindi (India)",code:"hi-IN"},{name:"Croatian",code:"hr"},{name:"Croatian (Bosnia & Herzegovina)",code:"hr-BA"},{name:"Croatian (Croatia)",code:"hr-HR"},{name:"Upper Sorbian",code:"hsb"},{name:"Upper Sorbian (Germany)",code:"hsb-DE"},{name:"Hungarian",code:"hu"},{name:"Hungarian (Hungary)",code:"hu-HU"},{name:"Armenian",code:"hy"},{name:"Armenian (Armenia)",code:"hy-AM"},{name:"Igbo",code:"ig"},{name:"Igbo (Nigeria)",code:"ig-NG"},{name:"Sichuan Yi",code:"ii"},{name:"Sichuan Yi (China)",code:"ii-CN"},{name:"Indonesian",code:"in"},{name:"Indonesian (Indonesia)",code:"in-ID"},{name:"Icelandic",code:"is"},{name:"Icelandic (Iceland)",code:"is-IS"},{name:"Italian",code:"it"},{name:"Italian (Switzerland)",code:"it-CH"},{name:"Italian (Italy)",code:"it-IT"},{name:"Italian (San Marino)",code:"it-SM"},{name:"Italian (Vatican City)",code:"it-VA"},{name:"Hebrew",code:"iw"},{name:"Hebrew (Israel)",code:"iw-IL"},{name:"Japanese",code:"ja"},{name:"Japanese (Japan)",code:"ja-JP"},{name:"Ngomba",code:"jgo"},{name:"Ngomba (Cameroon)",code:"jgo-CM"},{name:"Machame",code:"jmc"},{name:"Machame (Tanzania)",code:"jmc-TZ"},{name:"Georgian",code:"ka"},{name:"Georgian (Georgia)",code:"ka-GE"},{name:"Kabyle",code:"kab"},{name:"Kabyle (Algeria)",code:"kab-DZ"},{name:"Kamba",code:"kam"},{name:"Kamba (Kenya)",code:"kam-KE"},{name:"Makonde",code:"kde"},{name:"Makonde (Tanzania)",code:"kde-TZ"},{name:"Kabuverdianu",code:"kea"},{name:"Kabuverdianu (Cape Verde)",code:"kea-CV"},{name:"Koyra Chiini",code:"khq"},{name:"Koyra Chiini (Mali)",code:"khq-ML"},{name:"Kikuyu",code:"ki"},{name:"Kikuyu (Kenya)",code:"ki-KE"},{name:"Kazakh",code:"kk"},{name:"Kazakh (Kazakhstan)",code:"kk-KZ"},{name:"Kako",code:"kkj"},{name:"Kako (Cameroon)",code:"kkj-CM"},{name:"Kalaallisut",code:"kl"},{name:"Kalaallisut (Greenland)",code:"kl-GL"},{name:"Kalenjin",code:"kln"},{name:"Kalenjin (Kenya)",code:"kln-KE"},{name:"Khmer",code:"km"},{name:"Khmer (Cambodia)",code:"km-KH"},{name:"Kannada",code:"kn"},{name:"Kannada (India)",code:"kn-IN"},{name:"Korean",code:"ko"},{name:"Korean",code:"ko"},{name:"Korean (North Korea)",code:"ko-KP"},{name:"Korean (South Korea)",code:"ko-KR"},{name:"Korean (South Korea)",code:"ko-KR"},{name:"Konkani",code:"kok"},{name:"Konkani (India)",code:"kok-IN"},{name:"Kashmiri",code:"ks"},{name:"Kashmiri (India)",code:"ks-IN"},{name:"Shambala",code:"ksb"},{name:"Shambala (Tanzania)",code:"ksb-TZ"},{name:"Bafia",code:"ksf"},{name:"Bafia (Cameroon)",code:"ksf-CM"},{name:"Colognian",code:"ksh"},{name:"Colognian (Germany)",code:"ksh-DE"},{name:"Cornish",code:"kw"},{name:"Kyrgyz",code:"ky"},{name:"Kyrgyz (Kyrgyzstan)",code:"ky-KG"},{name:"Langi",code:"lag"},{name:"Langi (Tanzania)",code:"lag-TZ"},{name:"Lithuanian",code:"lt"},{name:"Lithuanian (Lithuania)",code:"lt-LT"},{name:"Luxembourgish",code:"lb"},{name:"Luxembourgish (Luxembourg)",code:"lb-LU"},{name:"Latvian",code:"lv"},{name:"Latvian (Latvia)",code:"lv-LV"},{name:"Ganda",code:"lg"},{name:"Ganda (Uganda)",code:"lg-UG"},{name:"Macedonian",code:"mk"},{name:"Macedonian (Macedonia)",code:"mk-MK"},{name:"Malay",code:"ms"},{name:"Malay (Malaysia)",code:"ms-MY"},{name:"Maltese",code:"mt"},{name:"Maltese (Malta)",code:"mt-MT"},{name:"Dutch",code:"nl"},{name:"Dutch (Belgium)",code:"nl-BE"},{name:"Dutch (Netherlands)",code:"nl-NL"},{name:"Norwegian",code:"no"},{name:"Norwegian (Norway)",code:"no-NO"},{name:"Norwegian (Norway Nynorsk)",code:"no-NO-NY"},{name:"Polish",code:"pl"},{name:"Polish (Poland)",code:"pl-PL"},{name:"Portuguese",code:"pt"},{name:"Portuguese (Angola)",code:"pt-AO"},{name:"Portuguese (Brazil)",code:"pt-BR"},{name:"Portuguese (Switzerland)",code:"pt-CH"},{name:"Portuguese (Cape Verde)",code:"pt-CV"},{name:"Portuguese (Equatorial Guinea)",code:"pt-GQ"},{name:"Portuguese (Guinea-Bissau)",code:"pt-GW"},{name:"Portuguese (Luxembourg)",code:"pt-LU"},{name:"Portuguese (Macau SAR China)",code:"pt-MO"},{name:"Portuguese (Mozambique)",code:"pt-MZ"},{name:"Portuguese (Portugal)",code:"pt-PT"},{name:"Portuguese (S\xe3o Tom\xe9 & Pr\xedncipe)",code:"pt-ST"},{name:"Portuguese (Timor-Leste)",code:"pt-TL"},{name:"Romanian",code:"ro"},{name:"Romanian (Romania)",code:"ro-RO"},{name:"Russian",code:"ru"},{name:"Russian (Belarus)",code:"ru-BY"},{name:"Russian (Kyrgyzstan)",code:"ru-KG"},{name:"Russian (Kazakhstan)",code:"ru-KZ"},{name:"Russian (Moldova)",code:"ru-MD"},{name:"Russian (Russia)",code:"ru-RU"},{name:"Russian (Ukraine)",code:"ru-UA"},{name:"Slovak",code:"sk"},{name:"Slovak (Slovakia)",code:"sk-SK"},{name:"Slovenian",code:"sl"},{name:"Slovenian (Slovenia)",code:"sl-SI"},{name:"Albanian",code:"sq"},{name:"Albanian (Albania)",code:"sq-AL"},{name:"Serbian",code:"sr"},{name:"Serbian (Bosnia and Herzegovina)",code:"sr-BA"},{name:"Serbian (Serbia and Montenegro)",code:"sr-CS"},{name:"Serbian (Montenegro)",code:"sr-ME"},{name:"Serbian (Serbia)",code:"sr-RS"},{name:"Swedish",code:"sv"},{name:"Swedish (Sweden)",code:"sv-SE"},{name:"Thai",code:"th"},{name:"Thai (Thailand)",code:"th-TH"},{name:"Thai (Thailand)",code:"th-TH-TH"},{name:"Turkish",code:"tr"},{name:"Turkish (Turkey)",code:"tr-TR"},{name:"Ukrainian",code:"uk"},{name:"Ukrainian (Ukraine)",code:"uk-UA"},{name:"Vietnamese",code:"vi"},{name:"Vietnamese (Vietnam)",code:"vi-VN"},{name:"Chinese",code:"zh"},{name:"Chinese (Simplified)\xa0(zh-Hans)",code:"zh-Hans"},{name:"Chinese (Simplified, Hong Kong SAR China)",code:"zh-Hans-HK"},{name:"Chinese (Simplified, Macau SAR China)",code:"zh-Hans-MO"},{name:"Chinese (Simplified, Singapore)",code:"zh-Hans-SG"},{name:"Chinese (Traditional)",code:"zh-Hant"},{name:"Chinese (Traditional, Hong Kong SAR China)",code:"zh-Hant-HK"},{name:"Chinese (Traditional, Macau SAR China)",code:"zh-Hant-MO"},{name:"Chinese (Traditional, Taiwan)",code:"zh-Hant-TW"},{name:"Chinese (Simplified, China)\xa0(zh-Hans-CN)",code:"zh-HK"},{name:"Zulu",code:"zu"},{name:"Zulu (South Africa)",code:"zu-ZA"}],d=[{symbol:"$",name:"US Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"USD",name_plural:"US dollars"},{symbol:"CA$",name:"Canadian Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"CAD",name_plural:"Canadian dollars"},{symbol:"€",name:"Euro",symbol_native:"€",decimal_digits:2,rounding:0,code:"EUR",name_plural:"euros"},{symbol:"AED",name:"United Arab Emirates Dirham",symbol_native:"د.إ.‏",decimal_digits:2,rounding:0,code:"AED",name_plural:"UAE dirhams"},{symbol:"Af",name:"Afghan Afghani",symbol_native:"؋",decimal_digits:0,rounding:0,code:"AFN",name_plural:"Afghan Afghanis"},{symbol:"ALL",name:"Albanian Lek",symbol_native:"Lek",decimal_digits:0,rounding:0,code:"ALL",name_plural:"Albanian lek\xeb"},{symbol:"AMD",name:"Armenian Dram",symbol_native:"դր.",decimal_digits:0,rounding:0,code:"AMD",name_plural:"Armenian drams"},{symbol:"AR$",name:"Argentine Peso",symbol_native:"$",decimal_digits:2,rounding:0,code:"ARS",name_plural:"Argentine pesos"},{symbol:"AU$",name:"Australian Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"AUD",name_plural:"Australian dollars"},{symbol:"man.",name:"Azerbaijani Manat",symbol_native:"ман.",decimal_digits:2,rounding:0,code:"AZN",name_plural:"Azerbaijani manats"},{symbol:"KM",name:"Bosnia-Herzegovina Convertible Mark",symbol_native:"KM",decimal_digits:2,rounding:0,code:"BAM",name_plural:"Bosnia-Herzegovina convertible marks"},{symbol:"Tk",name:"Bangladeshi Taka",symbol_native:"৳",decimal_digits:2,rounding:0,code:"BDT",name_plural:"Bangladeshi takas"},{symbol:"BGN",name:"Bulgarian Lev",symbol_native:"лв.",decimal_digits:2,rounding:0,code:"BGN",name_plural:"Bulgarian leva"},{symbol:"BD",name:"Bahraini Dinar",symbol_native:"د.ب.‏",decimal_digits:3,rounding:0,code:"BHD",name_plural:"Bahraini dinars"},{symbol:"FBu",name:"Burundian Franc",symbol_native:"FBu",decimal_digits:0,rounding:0,code:"BIF",name_plural:"Burundian francs"},{symbol:"BN$",name:"Brunei Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"BND",name_plural:"Brunei dollars"},{symbol:"Bs",name:"Bolivian Boliviano",symbol_native:"Bs",decimal_digits:2,rounding:0,code:"BOB",name_plural:"Bolivian bolivianos"},{symbol:"R$",name:"Brazilian Real",symbol_native:"R$",decimal_digits:2,rounding:0,code:"BRL",name_plural:"Brazilian reals"},{symbol:"BWP",name:"Botswanan Pula",symbol_native:"P",decimal_digits:2,rounding:0,code:"BWP",name_plural:"Botswanan pulas"},{symbol:"Br",name:"Belarusian Ruble",symbol_native:"руб.",decimal_digits:2,rounding:0,code:"BYN",name_plural:"Belarusian rubles"},{symbol:"BZ$",name:"Belize Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"BZD",name_plural:"Belize dollars"},{symbol:"CDF",name:"Congolese Franc",symbol_native:"FrCD",decimal_digits:2,rounding:0,code:"CDF",name_plural:"Congolese francs"},{symbol:"CHF",name:"Swiss Franc",symbol_native:"CHF",decimal_digits:2,rounding:.05,code:"CHF",name_plural:"Swiss francs"},{symbol:"CL$",name:"Chilean Peso",symbol_native:"$",decimal_digits:0,rounding:0,code:"CLP",name_plural:"Chilean pesos"},{symbol:"CN\xa5",name:"Chinese Yuan",symbol_native:"CN\xa5",decimal_digits:2,rounding:0,code:"CNY",name_plural:"Chinese yuan"},{symbol:"CO$",name:"Colombian Peso",symbol_native:"$",decimal_digits:0,rounding:0,code:"COP",name_plural:"Colombian pesos"},{symbol:"₡",name:"Costa Rican Col\xf3n",symbol_native:"₡",decimal_digits:0,rounding:0,code:"CRC",name_plural:"Costa Rican col\xf3ns"},{symbol:"CV$",name:"Cape Verdean Escudo",symbol_native:"CV$",decimal_digits:2,rounding:0,code:"CVE",name_plural:"Cape Verdean escudos"},{symbol:"Kč",name:"Czech Republic Koruna",symbol_native:"Kč",decimal_digits:2,rounding:0,code:"CZK",name_plural:"Czech Republic korunas"},{symbol:"Fdj",name:"Djiboutian Franc",symbol_native:"Fdj",decimal_digits:0,rounding:0,code:"DJF",name_plural:"Djiboutian francs"},{symbol:"Dkr",name:"Danish Krone",symbol_native:"kr",decimal_digits:2,rounding:0,code:"DKK",name_plural:"Danish kroner"},{symbol:"RD$",name:"Dominican Peso",symbol_native:"RD$",decimal_digits:2,rounding:0,code:"DOP",name_plural:"Dominican pesos"},{symbol:"DA",name:"Algerian Dinar",symbol_native:"د.ج.‏",decimal_digits:2,rounding:0,code:"DZD",name_plural:"Algerian dinars"},{symbol:"Ekr",name:"Estonian Kroon",symbol_native:"kr",decimal_digits:2,rounding:0,code:"EEK",name_plural:"Estonian kroons"},{symbol:"EGP",name:"Egyptian Pound",symbol_native:"ج.م.‏",decimal_digits:2,rounding:0,code:"EGP",name_plural:"Egyptian pounds"},{symbol:"Nfk",name:"Eritrean Nakfa",symbol_native:"Nfk",decimal_digits:2,rounding:0,code:"ERN",name_plural:"Eritrean nakfas"},{symbol:"Br",name:"Ethiopian Birr",symbol_native:"Br",decimal_digits:2,rounding:0,code:"ETB",name_plural:"Ethiopian birrs"},{symbol:"\xa3",name:"British Pound Sterling",symbol_native:"\xa3",decimal_digits:2,rounding:0,code:"GBP",name_plural:"British pounds sterling"},{symbol:"GEL",name:"Georgian Lari",symbol_native:"GEL",decimal_digits:2,rounding:0,code:"GEL",name_plural:"Georgian laris"},{symbol:"GH₵",name:"Ghanaian Cedi",symbol_native:"GH₵",decimal_digits:2,rounding:0,code:"GHS",name_plural:"Ghanaian cedis"},{symbol:"FG",name:"Guinean Franc",symbol_native:"FG",decimal_digits:0,rounding:0,code:"GNF",name_plural:"Guinean francs"},{symbol:"GTQ",name:"Guatemalan Quetzal",symbol_native:"Q",decimal_digits:2,rounding:0,code:"GTQ",name_plural:"Guatemalan quetzals"},{symbol:"HK$",name:"Hong Kong Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"HKD",name_plural:"Hong Kong dollars"},{symbol:"HNL",name:"Honduran Lempira",symbol_native:"L",decimal_digits:2,rounding:0,code:"HNL",name_plural:"Honduran lempiras"},{symbol:"kn",name:"Croatian Kuna",symbol_native:"kn",decimal_digits:2,rounding:0,code:"HRK",name_plural:"Croatian kunas"},{symbol:"Ft",name:"Hungarian Forint",symbol_native:"Ft",decimal_digits:0,rounding:0,code:"HUF",name_plural:"Hungarian forints"},{symbol:"Rp",name:"Indonesian Rupiah",symbol_native:"Rp",decimal_digits:0,rounding:0,code:"IDR",name_plural:"Indonesian rupiahs"},{symbol:"₪",name:"Israeli New Sheqel",symbol_native:"₪",decimal_digits:2,rounding:0,code:"ILS",name_plural:"Israeli new sheqels"},{symbol:"Rs",name:"Indian Rupee",symbol_native:"টকা",decimal_digits:2,rounding:0,code:"INR",name_plural:"Indian rupees"},{symbol:"IQD",name:"Iraqi Dinar",symbol_native:"د.ع.‏",decimal_digits:0,rounding:0,code:"IQD",name_plural:"Iraqi dinars"},{symbol:"IRR",name:"Iranian Rial",symbol_native:"﷼",decimal_digits:0,rounding:0,code:"IRR",name_plural:"Iranian rials"},{symbol:"Ikr",name:"Icelandic Kr\xf3na",symbol_native:"kr",decimal_digits:0,rounding:0,code:"ISK",name_plural:"Icelandic kr\xf3nur"},{symbol:"J$",name:"Jamaican Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"JMD",name_plural:"Jamaican dollars"},{symbol:"JD",name:"Jordanian Dinar",symbol_native:"د.أ.‏",decimal_digits:3,rounding:0,code:"JOD",name_plural:"Jordanian dinars"},{symbol:"\xa5",name:"Japanese Yen",symbol_native:"￥",decimal_digits:0,rounding:0,code:"JPY",name_plural:"Japanese yen"},{symbol:"Ksh",name:"Kenyan Shilling",symbol_native:"Ksh",decimal_digits:2,rounding:0,code:"KES",name_plural:"Kenyan shillings"},{symbol:"KHR",name:"Cambodian Riel",symbol_native:"៛",decimal_digits:2,rounding:0,code:"KHR",name_plural:"Cambodian riels"},{symbol:"CF",name:"Comorian Franc",symbol_native:"FC",decimal_digits:0,rounding:0,code:"KMF",name_plural:"Comorian francs"},{symbol:"₩",name:"South Korean Won",symbol_native:"₩",decimal_digits:0,rounding:0,code:"KRW",name_plural:"South Korean won"},{symbol:"KD",name:"Kuwaiti Dinar",symbol_native:"د.ك.‏",decimal_digits:3,rounding:0,code:"KWD",name_plural:"Kuwaiti dinars"},{symbol:"KZT",name:"Kazakhstani Tenge",symbol_native:"тңг.",decimal_digits:2,rounding:0,code:"KZT",name_plural:"Kazakhstani tenges"},{symbol:"LB\xa3",name:"Lebanese Pound",symbol_native:"ل.ل.‏",decimal_digits:0,rounding:0,code:"LBP",name_plural:"Lebanese pounds"},{symbol:"SLRs",name:"Sri Lankan Rupee",symbol_native:"SL Re",decimal_digits:2,rounding:0,code:"LKR",name_plural:"Sri Lankan rupees"},{symbol:"Lt",name:"Lithuanian Litas",symbol_native:"Lt",decimal_digits:2,rounding:0,code:"LTL",name_plural:"Lithuanian litai"},{symbol:"Ls",name:"Latvian Lats",symbol_native:"Ls",decimal_digits:2,rounding:0,code:"LVL",name_plural:"Latvian lati"},{symbol:"LD",name:"Libyan Dinar",symbol_native:"د.ل.‏",decimal_digits:3,rounding:0,code:"LYD",name_plural:"Libyan dinars"},{symbol:"MAD",name:"Moroccan Dirham",symbol_native:"د.م.‏",decimal_digits:2,rounding:0,code:"MAD",name_plural:"Moroccan dirhams"},{symbol:"MDL",name:"Moldovan Leu",symbol_native:"MDL",decimal_digits:2,rounding:0,code:"MDL",name_plural:"Moldovan lei"},{symbol:"MGA",name:"Malagasy Ariary",symbol_native:"MGA",decimal_digits:0,rounding:0,code:"MGA",name_plural:"Malagasy Ariaries"},{symbol:"MKD",name:"Macedonian Denar",symbol_native:"MKD",decimal_digits:2,rounding:0,code:"MKD",name_plural:"Macedonian denari"},{symbol:"MMK",name:"Myanma Kyat",symbol_native:"K",decimal_digits:0,rounding:0,code:"MMK",name_plural:"Myanma kyats"},{symbol:"MOP$",name:"Macanese Pataca",symbol_native:"MOP$",decimal_digits:2,rounding:0,code:"MOP",name_plural:"Macanese patacas"},{symbol:"MURs",name:"Mauritian Rupee",symbol_native:"MURs",decimal_digits:0,rounding:0,code:"MUR",name_plural:"Mauritian rupees"},{symbol:"MX$",name:"Mexican Peso",symbol_native:"$",decimal_digits:2,rounding:0,code:"MXN",name_plural:"Mexican pesos"},{symbol:"RM",name:"Malaysian Ringgit",symbol_native:"RM",decimal_digits:2,rounding:0,code:"MYR",name_plural:"Malaysian ringgits"},{symbol:"MTn",name:"Mozambican Metical",symbol_native:"MTn",decimal_digits:2,rounding:0,code:"MZN",name_plural:"Mozambican meticals"},{symbol:"N$",name:"Namibian Dollar",symbol_native:"N$",decimal_digits:2,rounding:0,code:"NAD",name_plural:"Namibian dollars"},{symbol:"₦",name:"Nigerian Naira",symbol_native:"₦",decimal_digits:2,rounding:0,code:"NGN",name_plural:"Nigerian nairas"},{symbol:"C$",name:"Nicaraguan C\xf3rdoba",symbol_native:"C$",decimal_digits:2,rounding:0,code:"NIO",name_plural:"Nicaraguan c\xf3rdobas"},{symbol:"Nkr",name:"Norwegian Krone",symbol_native:"kr",decimal_digits:2,rounding:0,code:"NOK",name_plural:"Norwegian kroner"},{symbol:"NPRs",name:"Nepalese Rupee",symbol_native:"नेरू",decimal_digits:2,rounding:0,code:"NPR",name_plural:"Nepalese rupees"},{symbol:"NZ$",name:"New Zealand Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"NZD",name_plural:"New Zealand dollars"},{symbol:"OMR",name:"Omani Rial",symbol_native:"ر.ع.‏",decimal_digits:3,rounding:0,code:"OMR",name_plural:"Omani rials"},{symbol:"B/.",name:"Panamanian Balboa",symbol_native:"B/.",decimal_digits:2,rounding:0,code:"PAB",name_plural:"Panamanian balboas"},{symbol:"S/.",name:"Peruvian Nuevo Sol",symbol_native:"S/.",decimal_digits:2,rounding:0,code:"PEN",name_plural:"Peruvian nuevos soles"},{symbol:"₱",name:"Philippine Peso",symbol_native:"₱",decimal_digits:2,rounding:0,code:"PHP",name_plural:"Philippine pesos"},{symbol:"PKRs",name:"Pakistani Rupee",symbol_native:"₨",decimal_digits:0,rounding:0,code:"PKR",name_plural:"Pakistani rupees"},{symbol:"zł",name:"Polish Zloty",symbol_native:"zł",decimal_digits:2,rounding:0,code:"PLN",name_plural:"Polish zlotys"},{symbol:"₲",name:"Paraguayan Guarani",symbol_native:"₲",decimal_digits:0,rounding:0,code:"PYG",name_plural:"Paraguayan guaranis"},{symbol:"QR",name:"Qatari Rial",symbol_native:"ر.ق.‏",decimal_digits:2,rounding:0,code:"QAR",name_plural:"Qatari rials"},{symbol:"RON",name:"Romanian Leu",symbol_native:"RON",decimal_digits:2,rounding:0,code:"RON",name_plural:"Romanian lei"},{symbol:"din.",name:"Serbian Dinar",symbol_native:"дин.",decimal_digits:0,rounding:0,code:"RSD",name_plural:"Serbian dinars"},{symbol:"RUB",name:"Russian Ruble",symbol_native:"₽.",decimal_digits:2,rounding:0,code:"RUB",name_plural:"Russian rubles"},{symbol:"RWF",name:"Rwandan Franc",symbol_native:"FR",decimal_digits:0,rounding:0,code:"RWF",name_plural:"Rwandan francs"},{symbol:"SR",name:"Saudi Riyal",symbol_native:"ر.س.‏",decimal_digits:2,rounding:0,code:"SAR",name_plural:"Saudi riyals"},{symbol:"SDG",name:"Sudanese Pound",symbol_native:"SDG",decimal_digits:2,rounding:0,code:"SDG",name_plural:"Sudanese pounds"},{symbol:"Skr",name:"Swedish Krona",symbol_native:"kr",decimal_digits:2,rounding:0,code:"SEK",name_plural:"Swedish kronor"},{symbol:"S$",name:"Singapore Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"SGD",name_plural:"Singapore dollars"},{symbol:"Ssh",name:"Somali Shilling",symbol_native:"Ssh",decimal_digits:0,rounding:0,code:"SOS",name_plural:"Somali shillings"},{symbol:"SY\xa3",name:"Syrian Pound",symbol_native:"ل.س.‏",decimal_digits:0,rounding:0,code:"SYP",name_plural:"Syrian pounds"},{symbol:"฿",name:"Thai Baht",symbol_native:"฿",decimal_digits:2,rounding:0,code:"THB",name_plural:"Thai baht"},{symbol:"DT",name:"Tunisian Dinar",symbol_native:"د.ت.‏",decimal_digits:3,rounding:0,code:"TND",name_plural:"Tunisian dinars"},{symbol:"T$",name:"Tongan Paʻanga",symbol_native:"T$",decimal_digits:2,rounding:0,code:"TOP",name_plural:"Tongan paʻanga"},{symbol:"TL",name:"Turkish Lira",symbol_native:"TL",decimal_digits:2,rounding:0,code:"TRY",name_plural:"Turkish Lira"},{symbol:"TT$",name:"Trinidad and Tobago Dollar",symbol_native:"$",decimal_digits:2,rounding:0,code:"TTD",name_plural:"Trinidad and Tobago dollars"},{symbol:"NT$",name:"New Taiwan Dollar",symbol_native:"NT$",decimal_digits:2,rounding:0,code:"TWD",name_plural:"New Taiwan dollars"},{symbol:"TSh",name:"Tanzanian Shilling",symbol_native:"TSh",decimal_digits:0,rounding:0,code:"TZS",name_plural:"Tanzanian shillings"},{symbol:"₴",name:"Ukrainian Hryvnia",symbol_native:"₴",decimal_digits:2,rounding:0,code:"UAH",name_plural:"Ukrainian hryvnias"},{symbol:"USh",name:"Ugandan Shilling",symbol_native:"USh",decimal_digits:0,rounding:0,code:"UGX",name_plural:"Ugandan shillings"},{symbol:"$U",name:"Uruguayan Peso",symbol_native:"$",decimal_digits:2,rounding:0,code:"UYU",name_plural:"Uruguayan pesos"},{symbol:"UZS",name:"Uzbekistan Som",symbol_native:"UZS",decimal_digits:0,rounding:0,code:"UZS",name_plural:"Uzbekistan som"},{symbol:"Bs.F.",name:"Venezuelan Bol\xedvar",symbol_native:"Bs.F.",decimal_digits:2,rounding:0,code:"VEF",name_plural:"Venezuelan bol\xedvars"},{symbol:"₫",name:"Vietnamese Dong",symbol_native:"₫",decimal_digits:0,rounding:0,code:"VND",name_plural:"Vietnamese dong"},{symbol:"FCFA",name:"CFA Franc BEAC",symbol_native:"FCFA",decimal_digits:0,rounding:0,code:"XAF",name_plural:"CFA francs BEAC"},{symbol:"CFA",name:"CFA Franc BCEAO",symbol_native:"CFA",decimal_digits:0,rounding:0,code:"XOF",name_plural:"CFA francs BCEAO"},{symbol:"YR",name:"Yemeni Rial",symbol_native:"ر.ي.‏",decimal_digits:0,rounding:0,code:"YER",name_plural:"Yemeni rials"},{symbol:"R",name:"South African Rand",symbol_native:"R",decimal_digits:2,rounding:0,code:"ZAR",name_plural:"South African rand"},{symbol:"ZK",name:"Zambian Kwacha",symbol_native:"ZK",decimal_digits:0,rounding:0,code:"ZMK",name_plural:"Zambian kwachas"},{symbol:"ZWL$",name:"Zimbabwean Dollar",symbol_native:"ZWL$",decimal_digits:0,rounding:0,code:"ZWL",name_plural:"Zimbabwean Dollar"}],m=[{name:"stripe",title:"Stripe"},{name:"paypal",title:"Paypal"},{name:"razorpay",title:"RazorPay"},{name:"mollie",title:"Mollie"},{name:"paymongo",title:"Paymongo"},{name:"paystack",title:"Paystack"},{name:"xendit",title:"Xendit"},{name:"sslcommerz",title:"SslCommerz"},{name:"iyzico",title:"Iyzico"},{name:"bkash",title:"bKash"},{name:"flutterwave",title:"Flutterwave"}];var c=n(16310);let t=c.Ry().shape({});var C=n(67294),g=n(86366);let ClipboardIcon=e=>{let{...a}=e;return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"18",viewBox:"0 0 14 18",fill:"none",...a,children:(0,i.jsx)("path",{d:"M9.8125 12.9375V15.4688C9.8125 15.9345 9.4345 16.3125 8.96875 16.3125H1.65625C1.43247 16.3125 1.21786 16.2236 1.05963 16.0654C0.901395 15.9071 0.8125 15.6925 0.8125 15.4688V5.90625C0.8125 5.4405 1.1905 5.0625 1.65625 5.0625H3.0625C3.43943 5.06224 3.81573 5.09335 4.1875 5.1555M9.8125 12.9375H12.3438C12.8095 12.9375 13.1875 12.5595 13.1875 12.0938V8.4375C13.1875 5.0925 10.7552 2.31675 7.5625 1.7805C7.19073 1.71835 6.81443 1.68725 6.4375 1.6875H5.03125C4.5655 1.6875 4.1875 2.0655 4.1875 2.53125V5.1555M9.8125 12.9375H5.03125C4.80747 12.9375 4.59286 12.8486 4.43463 12.6904C4.27639 12.5321 4.1875 12.3175 4.1875 12.0938V5.1555M13.1875 10.125V8.71875C13.1875 8.04742 12.9208 7.40359 12.4461 6.92889C11.9714 6.45419 11.3276 6.1875 10.6562 6.1875H9.53125C9.30747 6.1875 9.09286 6.09861 8.93463 5.94037C8.77639 5.78214 8.6875 5.56753 8.6875 5.34375V4.21875C8.6875 3.88634 8.62203 3.55719 8.49482 3.25008C8.36761 2.94298 8.18116 2.66394 7.94611 2.42889C7.71107 2.19384 7.43202 2.00739 7.12492 1.88018C6.81781 1.75297 6.48866 1.6875 6.15625 1.6875H5.3125",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})},MollieIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"82",height:"24",viewBox:"0 0 82 24",fill:"none",...a,children:[(0,i.jsx)("path",{d:"M34.3586 7.83789C29.8972 7.83789 26.2773 11.4675 26.2773 15.9191C26.2773 20.3708 29.9069 24.0004 34.3586 24.0004C38.8102 24.0004 42.4398 20.3708 42.4398 15.9191C42.4398 11.4675 38.82 7.83789 34.3586 7.83789ZM34.3586 20.1758C32.0158 20.1758 30.1084 18.2684 30.1084 15.9256C30.1084 13.5828 32.0158 11.6754 34.3586 11.6754C36.7014 11.6754 38.6088 13.5828 38.6088 15.9256C38.6088 18.2684 36.7014 20.1758 34.3586 20.1758Z",fill:"black"}),(0,i.jsx)("path",{d:"M61.2413 5.03981C62.632 5.03981 63.7628 3.91227 63.7628 2.51828C63.7628 1.12429 62.632 0 61.2413 0C59.8505 0 58.7197 1.12754 58.7197 2.52153C58.7197 3.91552 59.8505 5.03981 61.2413 5.03981Z",fill:"black"}),(0,i.jsx)("path",{d:"M17.8587 7.84435C17.6474 7.82811 17.446 7.81836 17.238 7.81836C15.2884 7.81836 13.4395 8.61771 12.1137 10.0279C10.788 8.62421 8.94557 7.81836 7.01543 7.81836C3.14866 7.82161 0 10.9605 0 14.8273V23.6721H3.77904V14.9345C3.77904 13.3293 5.09829 11.8509 6.6515 11.6916C6.76198 11.6819 6.86921 11.6754 6.96994 11.6754C8.71812 11.6754 10.1446 13.1019 10.1543 14.85V23.6689H14.0179V14.9183C14.0179 13.3228 15.3274 11.8444 16.8903 11.6851C17.0008 11.6754 17.108 11.6689 17.2088 11.6689C18.9569 11.6689 20.3932 13.0889 20.3997 14.8273V23.6721H24.2632V14.9345C24.2632 13.1636 23.6068 11.4544 22.424 10.1384C21.2445 8.81267 19.6231 7.99708 17.8587 7.84435Z",fill:"black"}),(0,i.jsx)("path",{d:"M48.387 0.376953H44.5234V23.6881H48.387V0.376953ZM55.7826 0.376953H51.9191V23.6881H55.7826V0.376953ZM63.1749 8.22423H59.3114V23.6816H63.1749V8.22423Z",fill:"black"}),(0,i.jsx)("path",{d:"M81.2351 15.5581C81.2351 13.5077 80.4358 11.5776 78.9931 10.1056C77.5406 8.63689 75.6234 7.82129 73.5828 7.82129H73.4821C71.3667 7.84728 69.3651 8.68563 67.8704 10.1901C66.3757 11.6946 65.5341 13.6832 65.5113 15.8083C65.4853 17.9756 66.3172 20.026 67.8541 21.5792C69.3911 23.1324 71.4252 23.9903 73.5926 23.9903H73.6023C76.4423 23.9903 79.1035 22.4695 80.5593 20.026L80.7445 19.7141L77.5536 18.1446L77.3944 18.4046C76.5885 19.7238 75.2043 20.5037 73.6673 20.5037C71.7014 20.5037 70.0052 19.1942 69.4821 17.329H81.2351V15.5581ZM73.4301 11.3306C75.1945 11.3306 76.7737 12.4907 77.3294 14.1349H69.5406C70.0865 12.4907 71.6657 11.3306 73.4301 11.3306Z",fill:"black"})]})},PayPalIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"91",height:"24",viewBox:"0 0 91 24",fill:"none",...a,children:[(0,i.jsx)("path",{d:"M33.7439 5.91309H28.7528C28.5876 5.91298 28.4277 5.97189 28.3021 6.0792C28.1765 6.18651 28.0933 6.33517 28.0675 6.49838L26.0489 19.2967C26.0397 19.3561 26.0434 19.4168 26.0598 19.4747C26.0762 19.5325 26.105 19.586 26.1441 19.6317C26.1832 19.6773 26.2318 19.7139 26.2864 19.739C26.341 19.7641 26.4004 19.777 26.4605 19.7769H28.8433C29.0086 19.777 29.1686 19.718 29.2942 19.6106C29.4199 19.5031 29.503 19.3543 29.5286 19.1909L30.073 15.739C30.0986 15.5758 30.1816 15.4271 30.3071 15.3196C30.4326 15.2122 30.5923 15.1531 30.7576 15.153H32.3376C35.6253 15.153 37.5227 13.562 38.0183 10.4093C38.2416 9.03003 38.0277 7.94629 37.3819 7.1873C36.6725 6.35388 35.4144 5.91309 33.7439 5.91309ZM34.3197 10.5874C34.0467 12.3783 32.6784 12.3783 31.3553 12.3783H30.6021L31.1305 9.03368C31.1459 8.93583 31.1958 8.84671 31.2711 8.78236C31.3465 8.71801 31.4423 8.68265 31.5413 8.68265H31.8865C32.7878 8.68265 33.638 8.68265 34.0774 9.19642C34.3394 9.50293 34.4197 9.95832 34.3197 10.5874ZM48.663 10.5298H46.2729C46.1739 10.5298 46.0781 10.5651 46.0027 10.6295C45.9274 10.6938 45.8775 10.7829 45.8621 10.8808L45.7562 11.5493L45.5891 11.307C45.0717 10.556 43.9179 10.305 42.7663 10.305C40.1252 10.305 37.8694 12.3053 37.43 15.1114C37.2016 16.5111 37.5264 17.8496 38.3204 18.783C39.0487 19.6412 40.0909 19.9988 41.3308 19.9988C43.4589 19.9988 44.6389 18.6304 44.6389 18.6304L44.5324 19.2946C44.5229 19.3539 44.5264 19.4147 44.5426 19.4726C44.5589 19.5305 44.5874 19.5841 44.6264 19.6299C44.6654 19.6757 44.7139 19.7125 44.7684 19.7378C44.823 19.763 44.8824 19.7761 44.9425 19.7762H47.0954C47.2607 19.7763 47.4207 19.7173 47.5463 19.6098C47.672 19.5024 47.7551 19.3535 47.7807 19.1902L49.0724 11.01C49.0819 10.9507 49.0785 10.8901 49.0623 10.8323C49.0461 10.7746 49.0175 10.721 48.9786 10.6753C48.9396 10.6297 48.8913 10.593 48.8368 10.5679C48.7823 10.5427 48.723 10.5297 48.663 10.5298ZM45.3315 15.1814C45.1009 16.5469 44.0171 17.4635 42.6349 17.4635C41.9409 17.4635 41.3862 17.2409 41.0301 16.8191C40.6769 16.4002 40.5426 15.804 40.655 15.1399C40.8703 13.7861 41.9723 12.8395 43.3333 12.8395C44.012 12.8395 44.5638 13.0651 44.9272 13.4905C45.2914 13.9204 45.4359 14.5203 45.3315 15.1814ZM61.392 10.5298H58.9903C58.877 10.5299 58.7655 10.5577 58.6654 10.6108C58.5653 10.6638 58.4797 10.7405 58.4159 10.8341L55.1034 15.7135L53.6993 11.0246C53.6562 10.8815 53.5682 10.7562 53.4483 10.6671C53.3285 10.578 53.1831 10.5298 53.0337 10.5298H50.6736C50.6073 10.5296 50.542 10.5452 50.483 10.5754C50.424 10.6056 50.3731 10.6494 50.3345 10.7032C50.2959 10.7571 50.2707 10.8193 50.261 10.8849C50.2513 10.9504 50.2574 11.0173 50.2788 11.08L52.9243 18.8435L50.4371 22.3546C50.3929 22.4168 50.3667 22.49 50.3614 22.5662C50.356 22.6424 50.3717 22.7185 50.4067 22.7864C50.4418 22.8542 50.4948 22.9111 50.56 22.9507C50.6253 22.9904 50.7001 23.0114 50.7765 23.0114H53.1753C53.2873 23.0115 53.3977 22.9845 53.4969 22.9327C53.5962 22.8808 53.6814 22.8057 53.7453 22.7136L61.7336 11.1829C61.7769 11.1205 61.8023 11.0475 61.807 10.9717C61.8118 10.8959 61.7957 10.8203 61.7605 10.753C61.7253 10.6857 61.6723 10.6293 61.6074 10.59C61.5425 10.5506 61.468 10.5298 61.392 10.5298Z",fill:"#253B80"}),(0,i.jsx)("path",{d:"M69.3436 5.91382H64.3518C64.1867 5.91389 64.027 5.97287 63.9016 6.08017C63.7761 6.18746 63.693 6.33602 63.6673 6.49911L61.6487 19.2975C61.6393 19.3568 61.6429 19.4174 61.6591 19.4752C61.6754 19.5329 61.7041 19.5865 61.7431 19.6321C61.782 19.6778 61.8304 19.7144 61.885 19.7395C61.9395 19.7647 61.9988 19.7777 62.0588 19.7777H64.6204C64.7359 19.7775 64.8477 19.7361 64.9354 19.6609C65.0232 19.5857 65.0812 19.4817 65.0991 19.3675L65.672 15.7397C65.6976 15.5765 65.7806 15.4278 65.9061 15.3203C66.0316 15.2129 66.1913 15.1538 66.3566 15.1537H67.9358C71.2243 15.1537 73.121 13.5628 73.6173 10.4101C73.8413 9.03076 73.626 7.94702 72.9802 7.18803C72.2715 6.35461 71.0141 5.91382 69.3436 5.91382ZM69.9194 10.5881C69.6472 12.379 68.2788 12.379 66.955 12.379H66.2026L66.7317 9.03441C66.7468 8.93655 66.7965 8.84736 66.8718 8.78297C66.947 8.71858 67.0428 8.68325 67.1418 8.68338H67.487C68.3876 8.68338 69.2385 8.68338 69.6778 9.19715C69.9398 9.50366 70.0194 9.95905 69.9194 10.5881ZM84.262 10.5305H81.8734C81.7743 10.5302 81.6785 10.5655 81.6032 10.6299C81.5279 10.6943 81.4783 10.7836 81.4633 10.8815L81.3574 11.55L81.1896 11.3077C80.6722 10.5568 79.5191 10.3057 78.3675 10.3057C75.7264 10.3057 73.4713 12.3061 73.032 15.1121C72.8043 16.5119 73.1276 17.8503 73.9216 18.7837C74.6514 19.6419 75.6921 19.9995 76.932 19.9995C79.0601 19.9995 80.2401 18.6312 80.2401 18.6312L80.1336 19.2953C80.1241 19.3548 80.1276 19.4156 80.1439 19.4736C80.1602 19.5316 80.189 19.5854 80.2281 19.6312C80.2672 19.677 80.3159 19.7138 80.3706 19.7389C80.4254 19.7641 80.4849 19.7771 80.5452 19.777H82.6973C82.8625 19.7769 83.0223 19.7178 83.1478 19.6103C83.2733 19.5029 83.3563 19.3541 83.3819 19.1909L84.6743 11.0107C84.6835 10.9513 84.6797 10.8905 84.6632 10.8327C84.6467 10.7749 84.6178 10.7213 84.5786 10.6757C84.5395 10.6301 84.4909 10.5935 84.4362 10.5684C84.3816 10.5434 84.3221 10.5304 84.262 10.5305ZM80.9305 15.1822C80.7014 16.5476 79.6162 17.4642 78.2339 17.4642C77.5414 17.4642 76.9853 17.2417 76.6291 16.8198C76.2759 16.4009 76.1431 15.8047 76.254 15.1406C76.4707 13.7868 77.5713 12.8403 78.9323 12.8403C79.611 12.8403 80.1628 13.0658 80.5262 13.4912C80.8918 13.9211 81.0363 14.521 80.9305 15.1822ZM87.0797 6.26485L85.0312 19.2975C85.0218 19.3568 85.0254 19.4174 85.0417 19.4752C85.058 19.5329 85.0866 19.5865 85.1256 19.6321C85.1646 19.6778 85.213 19.7144 85.2675 19.7395C85.322 19.7647 85.3813 19.7777 85.4414 19.7777H87.5008C87.8431 19.7777 88.1336 19.5296 88.1861 19.1917L90.2062 6.39402C90.2155 6.3347 90.212 6.27405 90.1957 6.21624C90.1794 6.15843 90.1508 6.10484 90.1118 6.05914C90.0728 6.01344 90.0244 5.97673 89.9699 5.95151C89.9154 5.9263 89.8561 5.91319 89.796 5.91309H87.4899C87.3909 5.91344 87.2952 5.94903 87.2201 6.01348C87.1449 6.07794 87.0952 6.16705 87.0797 6.26485Z",fill:"#179BD7"}),(0,i.jsx)("path",{d:"M5.32231 22.2644L5.70399 19.84L4.85378 19.8203H0.793945L3.61532 1.93094C3.62372 1.87628 3.65148 1.82646 3.69355 1.79056C3.73561 1.75465 3.78918 1.73506 3.84448 1.73535H10.6899C12.9625 1.73535 14.5308 2.20826 15.3497 3.14166C15.7335 3.57954 15.978 4.03712 16.0962 4.54068C16.2203 5.06905 16.2225 5.70032 16.1013 6.47025L16.0926 6.52644V7.01978L16.4765 7.23726C16.7695 7.38573 17.0329 7.58657 17.2537 7.82985C17.5821 8.20423 17.7945 8.68006 17.8842 9.24419C17.9769 9.82437 17.9463 10.5148 17.7945 11.2964C17.6193 12.1955 17.3361 12.9785 16.9537 13.6193C16.6163 14.1933 16.1627 14.6905 15.6219 15.0789C15.1139 15.4394 14.5104 15.7131 13.828 15.8882C13.1668 16.0604 12.413 16.1473 11.5861 16.1473H11.0534C10.6724 16.1473 10.3024 16.2845 10.012 16.5304C9.72191 16.779 9.5295 17.1224 9.46899 17.4996L9.42885 17.7178L8.75452 21.9907L8.72387 22.1476C8.71584 22.1973 8.70197 22.2221 8.68154 22.2389C8.66175 22.2551 8.63705 22.2641 8.61148 22.2644H5.32231Z",fill:"#253B80"}),(0,i.jsx)("path",{d:"M16.8402 6.58301C16.8197 6.71364 16.7964 6.84719 16.7701 6.98439C15.8674 11.6193 12.7789 13.2205 8.83435 13.2205H6.82596C6.34357 13.2205 5.93708 13.5708 5.86191 14.0466L4.83363 20.568L4.54244 22.4166C4.53084 22.4899 4.53527 22.5649 4.55542 22.6363C4.57558 22.7078 4.61097 22.774 4.65918 22.8305C4.70738 22.8869 4.76725 22.9323 4.83466 22.9634C4.90206 22.9945 4.97541 23.0106 5.04965 23.0106H8.61176C9.03359 23.0106 9.39191 22.7041 9.45832 22.2881L9.49335 22.1072L10.164 17.851L10.2071 17.6175C10.2728 17.2 10.6318 16.8935 11.0536 16.8935H11.5864C15.0376 16.8935 17.7393 15.4923 18.5289 11.4376C18.8588 9.74374 18.688 8.3294 17.8152 7.33469C17.5385 7.02699 17.2081 6.77226 16.8402 6.58301Z",fill:"#179BD7"}),(0,i.jsx)("path",{d:"M15.8952 6.2071C15.607 6.12372 15.3138 6.05863 15.0173 6.01225C14.4315 5.92222 13.8395 5.87903 13.2468 5.88307H7.88139C7.6772 5.88291 7.47969 5.95581 7.32456 6.08859C7.16943 6.22136 7.06692 6.40526 7.03556 6.60703L5.89417 13.8363L5.86133 14.0473C5.89689 13.8171 6.01364 13.6073 6.19047 13.4558C6.36729 13.3043 6.59251 13.221 6.82538 13.2211H8.83377C12.7783 13.2211 15.8668 11.6192 16.7695 6.98506C16.7965 6.84786 16.8192 6.71431 16.8396 6.58367C16.6014 6.45875 16.3531 6.35403 16.0974 6.27059C16.0304 6.24835 15.963 6.22718 15.8952 6.2071Z",fill:"#222D65"}),(0,i.jsx)("path",{d:"M7.03569 6.60695C7.06678 6.40513 7.16924 6.22115 7.32444 6.08844C7.47965 5.95573 7.67731 5.88309 7.88152 5.88373H13.247C13.8826 5.88373 14.4759 5.92533 15.0174 6.0129C15.3839 6.07049 15.7452 6.15663 16.0982 6.27052C16.3646 6.35882 16.612 6.46318 16.8404 6.5836C17.109 4.87078 16.8383 3.70457 15.9122 2.64856C14.8912 1.486 13.0484 0.988281 10.6905 0.988281H3.84504C3.36337 0.988281 2.9525 1.33858 2.87806 1.81514L0.0267633 19.8884C0.0134824 19.9724 0.0185353 20.0581 0.0415745 20.1399C0.0646136 20.2217 0.105092 20.2975 0.160226 20.3621C0.215359 20.4268 0.283839 20.4787 0.360955 20.5143C0.438071 20.55 0.521993 20.5685 0.606948 20.5686H4.83318L5.89429 13.8363L7.03569 6.60695Z",fill:"#253B80"})]})},RazorPayIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"114",height:"24",viewBox:"0 0 114 24",fill:"none",...a,children:[(0,i.jsx)("path",{d:"M7.27893 6.32871L6.33789 9.79232L11.7242 6.30899L8.20144 19.4524L11.7792 19.4554L16.9832 0.0419922",fill:"#3395FF"}),(0,i.jsx)("path",{d:"M1.48176 13.9301L0 19.4557H7.33291L10.3335 8.21521L1.48176 13.9301ZM26.9537 9.06542C26.7744 9.73162 26.4291 10.221 25.914 10.5334C25.4002 10.8453 24.679 11.0019 23.7482 11.0019H20.7906L21.8291 7.13018H24.7866C25.7163 7.13018 26.355 7.28552 26.7015 7.60219C27.0481 7.91885 27.1317 8.40282 26.9537 9.072M30.0158 8.98835C30.3922 7.59024 30.2368 6.51477 29.5485 5.76194C28.8614 5.01509 27.6557 4.63867 25.935 4.63867H19.3346L15.3613 19.4622H18.568L20.1692 13.4874H22.2724C22.7444 13.4874 23.116 13.5651 23.3873 13.7144C23.6591 13.8698 23.8187 14.1387 23.8677 14.527L24.4401 19.4622H27.8756L27.3187 14.8616C27.2052 13.8339 26.735 13.2305 25.9087 13.0512C26.962 12.7465 27.8445 12.2387 28.5555 11.5336C29.2616 10.8338 29.766 9.95658 30.0158 8.99432M37.8105 14.1566C37.5417 15.1604 37.1294 15.9192 36.572 16.4509C36.0139 16.9827 35.3471 17.2456 34.5692 17.2456C33.7769 17.2456 33.2398 16.9887 32.956 16.4688C32.6716 15.949 32.662 15.1962 32.9261 14.2104C33.1902 13.2245 33.6114 12.4538 34.191 11.8981C34.7705 11.3424 35.4481 11.0646 36.226 11.0646C37.0027 11.0646 37.5345 11.3335 37.8034 11.867C38.0782 12.403 38.0842 13.1695 37.8153 14.1673L37.8105 14.1566ZM39.2158 8.91068L38.8143 10.4104C38.641 9.87262 38.3047 9.44244 37.8075 9.1198C37.3092 8.80313 36.6926 8.64181 35.9571 8.64181C35.0549 8.64181 34.1886 8.87483 33.3581 9.34087C32.5276 9.8069 31.7987 10.4641 31.1773 11.3126C30.5559 12.161 30.1018 13.1229 29.809 14.2044C29.5222 15.2918 29.4625 16.2418 29.6358 17.0663C29.815 17.8968 30.1914 18.5302 30.771 18.9723C31.3565 19.4204 32.1034 19.6415 33.0175 19.6415C33.7439 19.6452 34.4621 19.488 35.1207 19.1814C35.7717 18.8877 36.3508 18.4555 36.8175 17.9147L36.3993 19.4778H39.5002L42.3317 8.91605H39.2248L39.2158 8.91068ZM53.4748 8.91068H44.4569L43.8266 11.2648H49.0737L42.1369 17.2575L41.5442 19.4682H50.853L51.4833 17.1141H45.861L52.9042 11.0317M61.4123 14.1387C61.1333 15.1783 60.7192 15.9598 60.1725 16.4688C59.6258 16.9827 58.9638 17.2396 58.1865 17.2396C56.5614 17.2396 56.0272 16.2059 56.5817 14.1387C56.8565 13.111 57.2724 12.3384 57.828 11.818C58.3837 11.2958 59.057 11.0353 59.8487 11.0353C60.6254 11.0353 61.15 11.294 61.4201 11.815C61.6901 12.3349 61.6878 13.1098 61.4123 14.1375M63.2275 9.308C62.5135 8.86348 61.6023 8.64121 60.491 8.64121C59.3659 8.64121 58.3245 8.86228 57.3662 9.30442C56.4118 9.74379 55.573 10.3997 54.9165 11.2199C54.2413 12.0505 53.7556 13.0243 53.4574 14.1357C53.1647 15.2428 53.1288 16.2149 53.3559 17.0472C53.5829 17.8777 54.0609 18.517 54.7779 18.9592C55.5008 19.4049 56.4209 19.6265 57.5502 19.6265C58.6615 19.6265 59.6952 19.4031 60.6452 18.9586C61.5951 18.5116 62.4077 17.8771 63.0829 17.0406C63.758 16.2077 64.242 15.2362 64.5407 14.1249C64.8395 13.0136 64.8753 12.0433 64.6483 11.2092C64.4212 10.3787 63.9492 9.73939 63.2382 9.29426M74.2976 11.732L75.0923 8.8581C74.8234 8.72068 74.4709 8.64898 74.0288 8.64898C73.3178 8.64898 72.6366 8.82464 71.9794 9.18074C71.4142 9.48307 70.9338 9.90967 70.5275 10.4438L70.9398 8.89634L70.0394 8.89992H67.8287L64.9787 19.4575H68.1232L69.602 13.9385C69.8171 13.1361 70.2043 12.5045 70.7629 12.0564C71.3186 11.6065 72.0117 11.3813 72.8482 11.3813C73.362 11.3813 73.84 11.499 74.2941 11.7338M83.0472 14.1894C82.7783 15.1753 82.372 15.9281 81.8164 16.4479C81.2607 16.9701 80.5915 17.2306 79.8148 17.2306C79.0381 17.2306 78.5063 16.9677 78.2255 16.442C77.9387 15.9132 77.9327 15.1514 78.2016 14.15C78.4705 13.1492 78.8827 12.3815 79.4503 11.8497C80.018 11.3138 80.6871 11.0461 81.4639 11.0461C82.2286 11.0461 82.7425 11.3209 83.0173 11.8766C83.2922 12.4322 83.2981 13.203 83.034 14.1888M85.2328 9.32533C84.6502 8.85929 83.9064 8.62627 83.0042 8.62627C82.2137 8.62627 81.4603 8.80552 80.7457 9.16759C80.0317 9.52907 79.4521 10.022 79.007 10.6458L79.0178 10.5741L79.5453 8.89514H76.4743L75.6916 11.8168L75.6677 11.9184L72.4413 23.9541H75.59L77.2152 17.8956C77.3765 18.4346 77.7051 18.8576 78.207 19.1635C78.7089 19.4682 79.3284 19.6194 80.0651 19.6194C80.9793 19.6194 81.8516 19.3983 82.6791 18.9562C83.5096 18.5128 84.2266 17.8747 84.8361 17.0502C85.4455 16.2257 85.8978 15.2697 86.1864 14.1882C86.4791 13.105 86.5389 12.1389 86.3716 11.2934C86.2013 10.4468 85.8243 9.79137 85.2423 9.32772M95.6774 14.1464C95.4085 15.1442 94.9963 15.909 94.4406 16.4348C93.8849 16.9642 93.2158 17.2276 92.439 17.2276C91.6444 17.2276 91.1066 16.9707 90.8258 16.4509C90.539 15.9311 90.5331 15.1783 90.7959 14.1924C91.0588 13.2066 91.4783 12.4358 92.0578 11.8802C92.6374 11.3245 93.3155 11.0473 94.0935 11.0473C94.8702 11.0473 95.396 11.3161 95.6708 11.8479C95.9457 12.3815 95.9474 13.148 95.6798 14.1482L95.6774 14.1464ZM97.0815 8.89753L96.6794 10.3972C96.5061 9.85649 96.1715 9.42631 95.6756 9.10665C95.1737 8.7876 94.5583 8.62866 93.8234 8.62866C92.9212 8.62866 92.0501 8.86168 91.2184 9.32772C90.3879 9.79376 89.6589 10.4474 89.0376 11.2934C88.4162 12.1395 87.9621 13.1038 87.6693 14.1853C87.3795 15.2709 87.3228 16.2227 87.496 17.0508C87.6711 17.8753 88.0481 18.5122 88.6313 18.9568C89.2132 19.3989 89.9637 19.6224 90.8778 19.6224C91.6127 19.6224 92.3148 19.4694 92.9809 19.1623C93.6305 18.8673 94.2081 18.4344 94.6736 17.8938L94.2554 19.458H97.3563L100.187 8.90052H97.0863L97.0815 8.89753ZM113.206 8.90112L113.208 8.89813H111.302C111.241 8.89813 111.187 8.90112 111.131 8.90231H110.142L109.635 9.60734L109.509 9.77464L109.455 9.85829L105.437 15.4555L104.607 8.90112H101.316L102.983 18.8612L99.3023 23.9577H102.582L103.473 22.6952C103.498 22.6582 103.521 22.6271 103.55 22.5877L104.59 21.1119L104.62 21.0701L109.276 14.4679L113.202 8.91127L113.208 8.90769H113.206V8.90112Z",fill:"#072654"})]})},StripeIcon=e=>{let{...a}=e;return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"58",height:"24",viewBox:"0 0 58 24",fill:"none",...a,children:(0,i.jsx)("path",{d:"M4.03506 9.36297C4.03506 8.74103 4.54542 8.50172 5.39085 8.50172C6.60287 8.50172 8.13393 8.86865 9.34607 9.52254V5.77445C8.02235 5.24817 6.71456 5.04082 5.39085 5.04082C2.15311 5.04082 0 6.73146 0 9.55451C0 13.9563 6.06056 13.2546 6.06056 15.1526C6.06056 15.8861 5.4227 16.1254 4.52949 16.1254C3.20578 16.1254 1.51514 15.5831 0.175389 14.8495V18.6453C1.65868 19.2833 3.15789 19.5544 4.52949 19.5544C7.84685 19.5544 10.1276 17.9117 10.1276 15.0568C10.1117 10.3041 4.03506 11.1493 4.03506 9.36297ZM14.8165 1.85108L10.925 2.68036L10.9091 15.4555C10.9091 17.8161 12.6795 19.5544 15.0399 19.5544C16.3477 19.5544 17.3046 19.3152 17.8309 19.0281V15.7905C17.3206 15.9978 14.8006 16.7314 14.8006 14.371V8.70907H17.8309V5.31198H14.8006L14.8165 1.85108ZM22.7911 6.49215L22.5359 5.31198H19.0909V19.2673H23.0782V9.80963C24.0191 8.58157 25.614 8.80484 26.1085 8.98023V5.31198C25.5981 5.12056 23.7321 4.76967 22.7911 6.49215ZM27.0813 5.31198H31.0845V19.2673H27.0813V5.31198ZM27.0813 4.09985L31.0845 3.2386V0.000976562L27.0813 0.846297V4.09985ZM39.4099 5.04082C37.8469 5.04082 36.8421 5.77445 36.2839 6.28492L36.0765 5.29606H32.5678V23.8924L36.555 23.0472L36.571 18.5336C37.1452 18.9483 37.9904 19.5384 39.394 19.5384C42.2488 19.5384 44.8485 17.2418 44.8485 12.186C44.8326 7.56075 42.201 5.04082 39.4099 5.04082ZM38.453 16.0296C37.512 16.0296 36.9536 15.6948 36.571 15.2801L36.555 9.36297C36.9697 8.9005 37.5438 8.58157 38.453 8.58157C39.9043 8.58157 40.9091 10.2083 40.9091 12.2976C40.9091 14.4348 39.9202 16.0296 38.453 16.0296ZM57.4163 12.3455C57.4163 8.26253 55.4385 5.04082 51.6587 5.04082C47.8627 5.04082 45.5661 8.26264 45.5661 12.3136C45.5661 17.1142 48.2775 19.5384 52.169 19.5384C54.0669 19.5384 55.5024 19.1078 56.5869 18.5018V15.3119C55.5025 15.8543 54.2584 16.1892 52.6794 16.1892C51.1323 16.1892 49.7607 15.6469 49.5853 13.765H57.3844C57.3844 13.5575 57.4163 12.7282 57.4163 12.3455ZM49.5375 10.8303C49.5375 9.02811 50.638 8.27845 51.6428 8.27845C52.6156 8.27845 53.6524 9.02811 53.6524 10.8303H49.5375Z",fill:"#6772E5"})})},SSLComerz=e=>{let{...a}=e;return(0,i.jsxs)("svg",{width:148,height:32,viewBox:"0 0 148 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",...a,children:[(0,i.jsx)("path",{fill:"url(#pattern0)",d:"M0 0H147.84V32H0z"}),(0,i.jsxs)("defs",{children:[(0,i.jsx)("pattern",{id:"pattern0",patternContentUnits:"objectBoundingBox",width:1,height:1,children:(0,i.jsx)("use",{xlinkHref:"#image0_920_646",transform:"scale(.00216 .01)"})}),(0,i.jsx)("image",{id:"image0_920_646",width:462,height:100,xlinkHref:"data:image/png;base64,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"})]})]})},PayStack=e=>{let{...a}=e;return(0,i.jsxs)("svg",{width:90,height:24,viewBox:"0 0 153 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,i.jsxs)("g",{clipPath:"url(#clip0_907_69)",children:[(0,i.jsx)("path",{d:"M28.462 2.29h-18.05c-.607 0-1.123.516-1.123 1.145v2.043c0 .629.516 1.145 1.123 1.145h18.05c.629 0 1.123-.516 1.145-1.145v-2.02c0-.652-.516-1.168-1.145-1.168zm0 11.338h-18.05c-.292 0-.584.112-.786.336a1.102 1.102 0 00-.337.809v2.043c0 .628.516 1.145 1.123 1.145h18.05c.629 0 1.123-.494 1.145-1.145v-2.043c-.022-.651-.516-1.145-1.145-1.145zm-7.88 5.657h-10.17c-.292 0-.584.113-.786.337-.202.225-.337.494-.337.808v2.043c0 .629.516 1.145 1.123 1.145h10.147c.629 0 1.123-.516 1.123-1.122v-2.043c.022-.674-.471-1.19-1.1-1.168zm9.025-11.337H10.412c-.292 0-.584.112-.786.336-.202.225-.337.494-.337.809v2.043c0 .628.516 1.145 1.123 1.145h19.173a1.13 1.13 0 001.122-1.145V9.093c.023-.629-.494-1.123-1.1-1.145z",fill:"#00C3F7"}),(0,i.jsx)("path",{d:"M50.599 6.87a5.857 5.857 0 00-1.954-1.347 5.944 5.944 0 00-2.335-.472 4.983 4.983 0 00-2.267.494 4.228 4.228 0 00-1.257.92v-.358c0-.18-.068-.36-.18-.494a.643.643 0 00-.494-.225H39.62c-.18 0-.359.068-.471.225a.595.595 0 00-.18.494V22.9c0 .18.067.36.18.494a.646.646 0 00.471.202h2.56c.18 0 .336-.067.471-.202a.562.562 0 00.202-.494v-5.747c.36.404.83.696 1.347.875a6.03 6.03 0 004.445-.09 5.95 5.95 0 001.976-1.347 6.517 6.517 0 001.347-2.11c.36-.876.516-1.819.494-2.762a7.44 7.44 0 00-.494-2.784c-.337-.74-.786-1.459-1.37-2.065zm-2.29 6.084c-.135.36-.337.674-.606.966a2.751 2.751 0 01-2.021.875c-.382 0-.763-.067-1.123-.247a3.194 3.194 0 01-.92-.628 2.708 2.708 0 01-.606-.966 3.283 3.283 0 010-2.357c.134-.36.359-.674.606-.943.27-.27.584-.494.92-.651a2.81 2.81 0 011.123-.247c.404 0 .763.067 1.145.247.337.157.651.359.898.628.27.27.449.584.606.943.27.786.247 1.617-.022 2.38zm17.87-7.543h-2.536c-.18 0-.36.067-.472.202a.71.71 0 00-.202.516v.314a3.323 3.323 0 00-1.145-.875 5.097 5.097 0 00-2.222-.494 6.207 6.207 0 00-4.356 1.796 6.283 6.283 0 00-1.392 2.11 6.896 6.896 0 00-.516 2.784 7.04 7.04 0 00.516 2.784c.337.786.786 1.504 1.392 2.11a6.083 6.083 0 004.333 1.82 4.8 4.8 0 002.223-.495c.426-.224.853-.516 1.167-.875v.336c0 .18.067.36.202.494.135.113.292.202.472.202h2.537c.18 0 .359-.067.471-.202a.697.697 0 00.202-.494V6.152c0-.18-.067-.36-.18-.494a.662.662 0 00-.493-.247zm-3.434 7.543c-.135.36-.337.674-.607.966-.269.269-.56.494-.898.65a2.67 2.67 0 01-2.267 0 3.27 3.27 0 01-.92-.65 2.706 2.706 0 01-.607-.966 3.54 3.54 0 010-2.357c.135-.36.337-.651.606-.943.27-.27.562-.494.92-.651a2.62 2.62 0 012.246 0c.337.157.651.359.898.628.247.27.449.584.606.943a3.23 3.23 0 01.023 2.38zm28.714-1.527a4.372 4.372 0 00-1.234-.763c-.472-.202-.988-.337-1.482-.449l-1.931-.382c-.494-.09-.853-.224-1.033-.381a.617.617 0 01-.27-.494c0-.202.113-.382.36-.539.337-.18.696-.27 1.078-.247.494 0 .987.112 1.436.292.45.202.876.404 1.28.674.562.359 1.055.291 1.392-.113l.92-1.055c.18-.18.27-.404.293-.651a1.005 1.005 0 00-.36-.674c-.381-.336-1.01-.696-1.84-1.055-.831-.359-1.887-.539-3.121-.539a6.743 6.743 0 00-2.223.315 6.144 6.144 0 00-1.706.875 3.785 3.785 0 00-1.482 2.986c0 1.056.314 1.909.943 2.537.629.629 1.46 1.056 2.492 1.258l2.02.448c.427.068.876.203 1.28.405.225.09.36.314.36.56 0 .225-.113.428-.36.607-.247.18-.65.292-1.19.292-.538 0-1.1-.112-1.594-.36a6.2 6.2 0 01-1.302-.852 2.479 2.479 0 00-.584-.337c-.224-.068-.516 0-.808.247l-1.1.83c-.314.225-.471.607-.382.966.068.381.36.74.921 1.167a8.081 8.081 0 004.737 1.392c.786 0 1.572-.09 2.313-.314a5.865 5.865 0 001.796-.898c.493-.36.898-.83 1.167-1.392.27-.539.404-1.123.404-1.729a3.46 3.46 0 00-.314-1.571 4.76 4.76 0 00-.876-1.056zm11.091 3.076a.783.783 0 00-.561-.381c-.225 0-.472.067-.651.202a2.168 2.168 0 01-1.033.336c-.112 0-.247-.022-.36-.044a.618.618 0 01-.336-.18 1.476 1.476 0 01-.27-.382 1.424 1.424 0 01-.112-.673V8.778h3.278c.202 0 .382-.09.516-.224a.68.68 0 00.225-.494V6.107c0-.202-.067-.382-.225-.494a.694.694 0 00-.493-.202h-3.3V2.268c0-.18-.068-.382-.203-.494a.893.893 0 00-.471-.202h-2.56c-.18 0-.359.067-.494.202a.721.721 0 00-.224.494V5.41h-1.46c-.179 0-.359.067-.493.224a.784.784 0 00-.18.494v1.953c0 .18.067.36.18.494.112.157.292.225.494.225h1.459v5.478a4.178 4.178 0 00.382 1.886c.246.494.56.92.987 1.28.404.336.876.583 1.392.718a5.495 5.495 0 001.594.247 6.66 6.66 0 002.088-.337 3.923 3.923 0 001.639-1.033c.292-.292.315-.763.09-1.1l-.898-1.437zm13.875-9.092h-2.537a.647.647 0 00-.472.202.714.714 0 00-.202.516v.314a3.125 3.125 0 00-1.145-.875 5.096 5.096 0 00-2.222-.494 6.21 6.21 0 00-4.356 1.796 6.282 6.282 0 00-1.392 2.11 6.855 6.855 0 00-.516 2.762 7.034 7.034 0 00.516 2.784c.314.786.808 1.504 1.392 2.11a6.044 6.044 0 004.333 1.819c.763.022 1.527-.157 2.223-.472a3.793 3.793 0 001.167-.875v.336c0 .18.068.36.202.472a.647.647 0 00.472.202h2.537a.662.662 0 00.673-.674V6.152c0-.18-.067-.36-.179-.494a.597.597 0 00-.494-.247zm-3.413 7.543c-.135.36-.337.674-.606.966-.269.269-.561.494-.898.65a2.834 2.834 0 01-1.145.248c-.404 0-.763-.09-1.123-.247a3.272 3.272 0 01-.92-.651 2.37 2.37 0 01-.584-.966 3.544 3.544 0 010-2.357c.135-.36.337-.674.584-.943.269-.27.584-.494.92-.651.36-.157.741-.247 1.123-.247s.763.067 1.145.247c.337.157.629.359.898.628.269.27.471.584.606.943a3.342 3.342 0 010 2.38zm17.332 1.37l-1.459-1.123c-.27-.224-.539-.292-.763-.202-.202.09-.382.225-.539.382a5.906 5.906 0 01-1.1 1.01c-.449.247-.921.382-1.415.337a2.685 2.685 0 01-1.594-.494c-.471-.337-.83-.786-1.01-1.347a3.414 3.414 0 01-.202-1.145c0-.404.067-.786.202-1.19a2.56 2.56 0 01.584-.943c.269-.27.561-.494.898-.629a2.834 2.834 0 011.145-.247c.494-.022.988.113 1.414.36.427.269.786.606 1.1 1.01.135.157.315.292.517.382.224.09.493.022.763-.202l1.459-1.1a.928.928 0 00.382-.495.72.72 0 00-.067-.673 6.332 6.332 0 00-2.246-2.11c-.965-.54-2.11-.831-3.39-.831-.898 0-1.796.18-2.649.516a6.558 6.558 0 00-2.133 1.415 6.624 6.624 0 00-1.437 2.133 6.87 6.87 0 000 5.253c.337.786.809 1.527 1.437 2.11 1.28 1.258 2.986 1.931 4.782 1.931 1.28 0 2.425-.292 3.39-.83a6.257 6.257 0 002.268-2.133.83.83 0 00.067-.651 1.39 1.39 0 00-.404-.494zm13.516 2.626l-4.019-5.882 3.435-4.535a.803.803 0 00.135-.74c-.068-.18-.225-.36-.651-.36h-2.717c-.157 0-.314.045-.449.112a.95.95 0 00-.404.382l-2.739 3.84h-.651V.695a.7.7 0 00-.202-.494.647.647 0 00-.472-.202h-2.537a.698.698 0 00-.494.202.663.663 0 00-.202.494v16.726c0 .202.068.36.202.494a.698.698 0 00.494.202h2.537c.18 0 .359-.067.472-.202a.7.7 0 00.202-.494v-4.423h.718l2.986 4.58c.18.337.517.539.876.539h2.851c.427 0 .606-.202.696-.382a.813.813 0 00-.067-.786zM80.593 5.41h-2.851a.795.795 0 00-.584.225 1 1 0 00-.27.472l-2.11 7.813h-.516l-2.245-7.813a1.505 1.505 0 00-.225-.472.671.671 0 00-.516-.247H68.38c-.382 0-.606.112-.719.382a1.225 1.225 0 000 .696l3.593 11c.067.158.134.338.269.45a.765.765 0 00.539.202h1.526l-.134.36-.337 1.01a1.646 1.646 0 01-.561.785c-.247.18-.54.292-.853.27-.27 0-.517-.068-.764-.158a3.354 3.354 0 01-.673-.404 1.07 1.07 0 00-.651-.202h-.023a.797.797 0 00-.651.404l-.898 1.325c-.36.584-.157.943.067 1.145a4.57 4.57 0 001.684.988c.696.247 1.415.359 2.133.359 1.302 0 2.38-.36 3.21-1.055.854-.764 1.505-1.751 1.819-2.874l4.176-13.605c.09-.247.112-.494.022-.719-.022-.157-.18-.336-.561-.336z",fill:"#011B33"})]}),(0,i.jsx)("defs",{children:(0,i.jsx)("clipPath",{id:"clip0_907_69",children:(0,i.jsx)("path",{fill:"#fff",transform:"rotate(180 76.5 0)",d:"M0 0H152.672V-24H0z"})})})]})},IyzicoIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{className:"w-14 h-6",viewBox:"0 0 868 410",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M26.2094 123.389C14.2609 123.389 4.625 133.025 4.625 144.819V270.471C4.625 282.342 14.338 291.978 26.2094 291.978C38.1579 291.978 47.7937 282.419 47.7937 270.471V144.819C47.7937 132.947 38.0808 123.389 26.2094 123.389Z",fill:"#1E64FF"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M26.2095 54.916C13.0276 54.916 2.3125 65.554 2.3125 78.6588C2.3125 91.7636 13.0276 102.402 26.2095 102.402C39.3914 102.402 50.1065 91.7636 50.1065 78.6588C50.0294 65.554 39.3914 54.916 26.2095 54.916Z",fill:"#1E64FF"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M413.032 270.47C413.032 258.599 403.319 248.963 391.371 248.963H331.706L408.022 158.617C415.653 149.521 414.497 135.953 405.324 128.399C401.084 124.853 395.842 123.157 390.677 123.388C390.446 123.388 295.243 123.388 295.243 123.388C283.295 123.388 273.659 133.024 273.659 144.818C273.659 156.69 283.295 166.326 295.243 166.326H345.119L268.803 256.595C261.094 265.691 262.327 279.258 271.424 286.813C275.509 290.205 280.443 291.823 285.299 291.823H391.294C403.319 291.901 413.032 282.342 413.032 270.47ZM603.514 163.474C615.386 163.474 626.409 168.022 634.812 176.347C643.214 184.75 656.936 184.75 665.415 176.347C673.818 167.945 673.818 154.377 665.415 145.975C648.842 129.555 626.872 120.459 603.514 120.459C580.157 120.459 558.187 129.478 541.691 145.975C525.194 162.394 516.021 184.21 516.021 207.49C516.021 230.693 525.117 252.586 541.691 268.929C558.187 285.348 580.157 294.444 603.514 294.444C626.872 294.444 648.842 285.348 665.415 268.929C673.818 260.603 673.818 246.959 665.415 238.556C656.936 230.154 643.291 230.154 634.812 238.556C626.409 246.805 615.309 251.43 603.514 251.43C591.72 251.43 580.62 246.805 572.217 238.556C563.815 230.231 559.267 219.207 559.267 207.413C559.267 195.696 563.892 184.595 572.217 176.347C580.543 168.022 591.643 163.474 603.514 163.474ZM778.194 251.43C753.834 251.43 733.946 231.696 733.946 207.413C733.946 183.131 753.757 163.396 778.194 163.396C802.63 163.396 822.442 183.131 822.442 207.413C822.442 231.696 802.63 251.43 778.194 251.43ZM778.194 120.459C730.014 120.459 690.777 159.465 690.777 207.413C690.777 255.361 730.014 294.367 778.194 294.367C826.45 294.367 865.687 255.361 865.687 207.413C865.687 159.465 826.45 120.459 778.194 120.459ZM466.068 123.388C454.12 123.388 444.484 133.024 444.484 144.818V270.47C444.484 282.342 454.12 291.978 466.068 291.978C478.017 291.978 487.653 282.419 487.653 270.47V144.818C487.73 132.947 478.017 123.388 466.068 123.388Z",fill:"#1E64FF"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M466.094 54.916C452.912 54.916 442.197 65.554 442.197 78.6588C442.197 91.7636 452.912 102.402 466.094 102.402C479.276 102.402 489.991 91.7636 489.991 78.6588C489.991 65.554 479.276 54.916 466.094 54.916Z",fill:"#1E64FF"}),(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M236.041 125.778C225.403 120.305 212.375 124.39 206.902 134.951L160.573 223.755L114.243 134.951C108.77 124.467 95.6653 120.305 85.1044 125.778C74.5435 131.251 70.3808 144.202 75.931 154.763L136.29 270.393L108.462 323.892C102.989 334.452 107.074 347.403 117.635 352.876C121.104 354.726 124.881 355.42 128.504 355.266C135.982 354.958 143.074 350.795 146.774 343.703L245.291 154.763C250.764 144.202 246.602 131.251 236.041 125.778Z",fill:"#1E64FF"})]})},XenditIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{className:"w-15 h-6",viewBox:"0 0 868 410",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M162.089 259.424L185.501 299.813L196.728 292.831L200.699 279.413L175.643 236.011L162.089 259.424ZM86.5119 99.5075L75.4219 109.091L104.995 160.161L118.55 136.885L91.5778 90.1973L86.5119 99.5075ZM111.841 224.51L78.9816 281.33L83.0891 296.39L93.2208 303.51L140.593 221.498L154.148 198.086L206.586 107.175L197.823 95.1262L194.811 90.8818L182.078 102.656L111.841 224.51Z",fill:"#D24D57"}),(0,i.jsx)("path",{d:"M85.4145 79.6523L71.8599 103.065L30.3748 174.945L16.8203 198.357L85.2776 317.062L98.8321 293.65L43.7925 198.357L98.8321 103.065H138.263L151.818 79.6523H85.4145ZM182.076 103.065L237.116 198.357L182.076 293.65H142.782L129.227 317.062H195.631L209.185 293.65L250.533 221.77L264.088 198.357L250.533 174.808L209.048 103.065L195.631 79.6523L182.076 103.065Z",fill:"#446CB3"}),(0,i.jsx)("path",{d:"M380.193 155.779L355.137 192.336L329.945 155.779H309.956L344.869 204.795L308.176 256.138H328.165L355.137 217.528L381.973 256.138H401.962L365.269 204.795L400.182 155.779H380.193ZM456.043 168.923C463.3 168.923 469.05 171.251 472.884 175.906C476.581 180.424 478.634 186.859 478.771 195.348H430.303C431.262 187.27 433.863 180.835 438.108 176.18C442.626 171.388 448.65 168.923 456.043 168.923ZM456.317 154C442.9 154 432.083 158.792 424.005 168.376C416.064 177.823 412.094 190.83 412.094 206.712C412.094 222.594 416.475 235.327 424.964 244.226C433.589 253.263 445.364 257.781 460.151 257.781C466.86 257.781 472.61 257.233 477.402 256.275C482.194 255.316 487.26 253.673 492.463 251.346L493.284 251.072V235.053L491.504 235.874C481.373 240.256 471.104 242.309 460.699 242.309C451.115 242.309 443.584 239.434 438.381 233.821C433.316 228.344 430.577 220.266 430.03 209.861H497.392V199.181C497.392 185.764 493.695 174.674 486.302 166.459C479.045 158.244 468.913 154 456.317 154ZM567.629 154C560.783 154 554.348 155.369 548.735 158.107C543.806 160.571 539.698 163.857 536.686 167.965L534.495 155.779H520.119V256.138H537.508V203.563C537.508 191.24 539.835 182.204 544.353 177.001C548.871 171.798 556.128 169.197 565.849 169.197C573.242 169.197 578.582 170.977 582.005 174.674C585.428 178.37 587.071 184.121 587.071 191.651V256.138H604.459V191.103C604.459 178.37 601.31 168.786 595.149 162.899C588.987 157.012 579.814 154 567.629 154ZM670.725 242.994C662.237 242.994 655.939 239.982 651.557 233.958C647.176 227.796 644.985 218.623 644.985 206.438C644.985 194.389 647.176 185.079 651.694 178.507C656.075 172.072 662.237 168.923 670.588 168.923C680.309 168.923 687.292 171.798 691.673 177.275C696.055 182.889 698.382 192.609 698.382 206.164V209.313C698.382 221.225 696.191 229.85 691.81 235.19C687.429 240.393 680.446 242.994 670.725 242.994ZM698.245 155.916L698.656 162.899L699.067 167.28C691.81 158.518 681.679 154 668.809 154C655.665 154 645.396 158.655 638.003 167.828C630.746 177.001 627.05 189.871 627.05 206.164C627.05 222.457 630.746 235.327 638.003 244.226C645.396 253.263 655.665 257.781 668.809 257.781C682.089 257.781 692.358 253.126 699.477 243.952L701.257 256.001H715.633V114.705H698.245V155.916ZM753.285 119.497C750.41 119.497 748.082 120.455 746.165 122.235C744.248 124.015 743.29 126.89 743.29 130.587C743.29 134.284 744.248 137.022 746.165 138.939C748.082 140.856 750.41 141.814 753.285 141.814C756.023 141.814 758.351 140.856 760.267 139.076C762.184 137.159 763.28 134.421 763.28 130.724C763.28 127.027 762.321 124.289 760.267 122.372C758.351 120.319 756.023 119.497 753.285 119.497ZM744.522 256.138H761.91V155.779H744.522V256.138ZM836.803 241.488C835.433 241.899 833.517 242.309 831.189 242.72C828.862 243.131 826.397 243.268 823.796 243.268C819.278 243.268 815.855 241.899 813.253 239.023C810.652 236.148 809.42 231.904 809.42 226.29V170.019H837.898V155.916H809.42V133.189H798.603L792.305 154.41L778.066 160.708V169.882H792.032V226.838C792.032 247.512 802.026 258.055 821.742 258.055C824.343 258.055 827.219 257.781 830.368 257.233C833.654 256.685 836.118 256.001 837.761 255.179L838.446 254.769V240.94L836.803 241.488Z",fill:"#446CB3"})]})};var h=n(8953);let BkashIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{className:"w-18 h-8",viewBox:"0 0 868 410",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,i.jsx)("path",{d:"M774.679 223.842L594.225 195.564L618.147 303.095L774.679 223.842Z",fill:"#D12053"}),(0,i.jsx)("path",{d:"M774.678 223.844L638.699 34.916L594.258 195.6L774.678 223.844Z",fill:"#E2136E"}),(0,i.jsx)("path",{d:"M590.039 193.422L447.561 11.334L634.14 33.6228L590.039 193.422Z",fill:"#D12053"}),(0,i.jsx)("path",{d:"M525.146 117.332L446.029 42.7754H466.855L525.146 117.332Z",fill:"#9E1638"}),(0,i.jsx)("path",{d:"M813.167 132L779.682 222.823L725.406 147.755L813.167 132Z",fill:"#D12053"}),(0,i.jsx)("path",{d:"M639.211 297.653L770.596 244.874L776.109 228.098L639.211 297.653Z",fill:"#E2136E"}),(0,i.jsx)("path",{d:"M534.129 397.253L590.413 199.818L618.963 328.277L534.129 397.253Z",fill:"#9E1638"}),(0,i.jsx)("path",{d:"M818.406 132.715L804.59 170.215L854.408 169.398L818.406 132.715Z",fill:"#E2136E"}),(0,i.jsx)("path",{d:"M13.625 147.754C16.041 147.958 18.4911 148.4 21.0773 148.4C23.6635 148.4 25.7733 147.958 28.5296 147.754V227.619C36.3903 214.246 46.2926 205.433 60.857 205.433C87.1953 205.433 98.4928 231.499 98.4928 255.455C98.4928 284.141 83.1799 311.603 56.3311 311.603C50.9556 311.721 45.6503 310.366 40.9897 307.684C36.3292 305.003 32.4909 301.098 29.8908 296.392C25.399 300.339 21.4176 305.069 17.164 309.357H13.7611L13.625 147.754ZM28.1893 265.221C28.1893 288.497 38.0577 304.729 54.2894 304.729C75.3532 304.729 82.125 276.417 82.125 256.34C82.125 233.03 74.4345 214.689 56.195 214.484C34.893 214.314 28.1893 239.53 28.1893 265.255",fill:"#E2136E"}),(0,i.jsx)("path",{d:"M167.198 199.411L151.34 219.829C166.245 241.607 181.694 262.943 196.666 284.994L211.775 308.814V310.005C208.066 309.767 204.697 309.29 201.567 309.29C198.436 309.29 194.489 309.767 191.154 310.005C187.036 302.382 182.953 295.338 178.291 288.431L137.456 228.029C136.538 227.077 134.326 226.328 134.326 227.315V310.005C131.331 309.767 128.711 309.29 126.125 309.29C123.539 309.29 120.544 309.767 117.924 310.005V147.756C120.544 147.96 123.368 148.47 126.125 148.47C128.881 148.47 131.331 147.96 134.326 147.756V220.986C134.326 222.415 137.116 221.462 138.954 219.556C142.498 215.882 145.816 211.995 148.89 207.918L194.795 147.688C197.211 147.892 199.627 148.402 202.247 148.402C204.867 148.402 206.875 147.892 209.529 147.688L167.198 199.411Z",fill:"#231F20"}),(0,i.jsx)("path",{d:"M290.689 291.731C290.689 300.17 290.144 304.457 301.135 301.667V306.397C299.34 307.297 297.459 308.015 295.521 308.541C285.822 310.48 277.792 308.745 276.294 296.052L274.627 297.924C271.385 302.172 267.203 305.612 262.409 307.973C257.615 310.335 252.34 311.554 246.996 311.535C233.792 311.535 222.223 301.123 222.223 285.163C222.223 260.56 239.237 257.327 256.694 254.095C271.462 251.304 276.499 250.011 276.499 239.633C276.499 223.639 268.672 214.383 254.686 214.383C249.91 214.29 245.22 215.666 241.252 218.326C237.283 220.985 234.228 224.8 232.499 229.254H230.458V217.276C238.855 209.97 249.516 205.787 260.641 205.434C280.208 205.434 290.859 217.276 290.859 241.674L290.689 291.731ZM275.784 256.102L269.216 257.6C256.524 260.39 237.502 262.534 237.502 282.237C237.502 295.848 244.307 302.654 255.741 302.654C261.655 301.925 267.064 298.953 270.85 294.351C272.211 292.786 275.954 289.11 275.954 287.545L275.784 256.102Z",fill:"#231F20"}),(0,i.jsx)("path",{d:"M323.934 288.871C328.358 297.345 336.593 304.933 345.372 304.933C350.382 304.677 355.095 302.481 358.513 298.809C361.93 295.138 363.783 290.279 363.68 285.264C363.68 256.136 319.612 275.056 319.612 237.352C319.612 216.662 333.224 205.467 350 205.467C357.418 205.302 364.732 207.236 371.098 211.047C369.26 216.04 367.782 221.157 366.674 226.36H364.973C362.523 219.248 356.023 212.102 349.115 212.102C339.791 212.102 332.101 218.398 332.101 230.07C332.101 257.667 376.168 242.967 376.168 277.506C376.168 300.611 358.269 311.535 341.799 311.535C333.551 311.528 325.476 309.167 318.523 304.729C320.194 299.552 321.501 294.264 322.437 288.905L323.934 288.871Z",fill:"#231F20"}),(0,i.jsx)("path",{d:"M401.281 147.754C403.765 147.958 406.181 148.4 408.768 148.4C411.354 148.4 413.464 147.958 416.254 147.754V226.326C422.787 213.157 433.03 205.433 446.301 205.433C467.944 205.433 476.349 220.269 476.349 247.492V310.003C473.524 309.765 471.347 309.357 468.862 309.357C466.378 309.357 463.826 309.799 461.376 310.003V252.426C461.376 228.606 456.578 216.594 440.687 216.594C423.876 216.594 416.254 228.708 416.254 251.133V310.003C413.464 309.765 411.252 309.357 408.768 309.357C406.283 309.357 403.799 309.799 401.281 310.003V147.754Z",fill:"#231F20"})]})},PaymongoIcon=e=>{let{...a}=e;return(0,i.jsx)("svg",{className:"w-15 h-6",viewBox:"0 0 1205 208",xmlns:"http://www.w3.org/2000/svg",...a,children:(0,i.jsx)("g",{stroke:"none",strokeWidth:"1",fill:"none","fill-rule":"evenodd",children:(0,i.jsx)("g",{transform:"translate(-256.000000, -359.000000)",fill:"#24B47E",children:(0,i.jsxs)("g",{transform:"translate(256.000000, 359.000000)",children:[(0,i.jsx)("path",{d:"M208,104.160188 C208,158.944641 165.733828,203.853946 112.082512,208 C106.850991,191.679623 104.028228,174.285041 104.028228,156.212014 C104.028228,142.085277 103.990591,144.422144 103.990591,130.299176 C104.028228,151.53828 114.227811,170.402827 129.997648,182.237927 C142.925902,173.286219 153.708857,161.48881 161.480865,147.731449 C186.67873,103.085984 142.925902,41.0647821 103.990591,13.0223793 C65.1305528,40.9705536 21.3024518,103.048292 46.5003167,147.712603 C57.4902741,167.199058 74.5773998,182.765607 95.1459332,191.849234 L92.360807,207.641932 C40.4031485,201.875147 0,157.738516 0,104.160188 C0,46.6242638 46.5567719,0 103.990591,0 C161.42441,0 208,46.6242638 208,104.160188",id:"Fill-17"}),(0,i.jsx)("path",{d:"M285.94,105.5 C285.94,77.186 308.71,54.416 336.826,54.416 C364.942,54.416 387.91,77.186 387.91,105.5 C387.91,133.814 364.942,156.584 336.826,156.584 C323.56,156.584 312.868,151.04 305.542,142.328 L305.542,194.6 L285.94,194.6 L285.94,105.5 Z M336.826,137.774 C354.844,137.774 368.506,123.518 368.506,105.5 C368.506,87.482 355.042,73.226 336.826,73.226 C318.808,73.226 305.146,87.482 305.146,105.5 C305.146,123.518 319.006,137.774 336.826,137.774 Z M448.894,156.584 C420.778,156.584 397.81,133.814 397.81,105.5 C397.81,77.186 420.778,54.416 448.894,54.416 C477.01,54.416 499.78,77.186 499.78,105.5 L499.78,155 L480.376,155 L480.376,139.358 C475.624,149.06 462.952,156.584 448.894,156.584 Z M449.092,137.774 C467.11,137.774 480.574,123.518 480.574,105.5 C480.574,87.482 467.308,73.226 449.092,73.226 C430.876,73.226 417.214,87.482 417.214,105.5 C417.214,123.518 431.074,137.774 449.092,137.774 Z M557.2,194.6 C536.806,194.6 521.758,182.126 515.026,163.91 L536.806,163.91 C540.964,170.84 547.3,175.79 557.2,175.79 C572.446,175.79 581.158,165.692 581.158,152.426 L581.158,147.08 C575.614,153.02 566.902,156.584 557.2,156.584 C531.856,156.584 513.64,141.338 513.64,108.074 L513.64,56 L533.044,56 L533.044,108.074 C533.044,128.27 541.756,137.774 557.2,137.774 C572.446,137.774 581.356,128.468 581.356,109.262 L581.356,56 L600.76,56 L600.76,150.05 C600.76,175.394 582.544,194.6 557.2,194.6 Z M724.114,54.416 C748.864,54.416 767.08,69.662 767.08,102.926 L767.08,155 L747.676,155 L747.676,102.926 C747.676,82.73 739.162,73.226 724.114,73.226 C709.264,73.226 700.552,82.73 700.552,102.926 L700.552,155 L681.148,155 L681.148,102.926 C681.148,82.73 672.634,73.226 657.586,73.226 C642.736,73.226 634.024,82.73 634.024,102.926 L634.024,155 L614.62,155 L614.62,102.926 C614.62,69.662 632.836,54.416 657.586,54.416 C671.842,54.416 684.316,60.356 690.85,72.83 C697.384,60.356 709.858,54.416 724.114,54.416 Z M829.054,54.416 C857.368,54.416 880.138,77.186 880.138,105.5 C880.138,133.814 857.368,156.584 829.054,156.584 C800.74,156.584 777.97,133.814 777.97,105.5 C777.97,77.186 800.74,54.416 829.054,54.416 Z M829.252,73.226 C811.036,73.226 797.374,87.482 797.374,105.5 C797.374,123.518 811.234,137.774 829.252,137.774 C847.27,137.774 860.734,123.518 860.734,105.5 C860.734,87.482 847.468,73.226 829.252,73.226 Z M978.148,155 L958.744,155 L958.744,102.926 C958.744,82.73 950.032,73.226 934.588,73.226 C919.144,73.226 910.432,82.73 910.432,102.926 L910.432,155 L891.028,155 L891.028,102.926 C891.028,69.662 909.244,54.416 934.588,54.416 C959.932,54.416 978.148,69.662 978.148,102.926 L978.148,155 Z M1040.122,194.6 C1019.332,194.6 1001.314,182.126 993.394,163.91 L1016.758,163.91 C1022.698,171.434 1030.618,175.79 1040.122,175.79 C1057.15,175.79 1071.406,164.702 1071.406,148.07 L1071.406,143.516 C1063.684,151.436 1052.596,156.584 1040.122,156.584 C1011.808,156.584 989.038,133.814 989.038,105.5 C989.038,77.186 1011.808,54.416 1040.122,54.416 C1068.436,54.416 1091.206,77.186 1091.206,105.5 L1091.206,143.516 C1091.206,171.83 1068.436,194.6 1040.122,194.6 Z M1040.32,137.774 C1058.338,137.774 1071.802,123.518 1071.802,105.5 C1071.802,87.482 1058.536,73.226 1040.32,73.226 C1022.104,73.226 1008.442,87.482 1008.442,105.5 C1008.442,123.518 1022.302,137.774 1040.32,137.774 Z M1153.18,54.416 C1181.494,54.416 1204.264,77.186 1204.264,105.5 C1204.264,133.814 1181.494,156.584 1153.18,156.584 C1124.866,156.584 1102.096,133.814 1102.096,105.5 C1102.096,77.186 1124.866,54.416 1153.18,54.416 Z M1153.378,73.226 C1135.162,73.226 1121.5,87.482 1121.5,105.5 C1121.5,123.518 1135.36,137.774 1153.378,137.774 C1171.396,137.774 1184.86,123.518 1184.86,105.5 C1184.86,87.482 1171.594,73.226 1153.378,73.226 Z",id:"paymongo","fill-rule":"nonzero"})]})})})})},FlutterwaveIcon=e=>{let{...a}=e;return(0,i.jsxs)("svg",{height:"-285",viewBox:"-135.4 209.8 604.3 125.4",width:"2500",xmlns:"http://www.w3.org/2000/svg",...a,children:[(0,i.jsx)("path",{d:"m67.2 244.4h.2c1.2 0 1.9.9 1.9 2.1v53.9c0 .9-.7 1.9-1.9 1.9-.9 0-1.9-.7-1.9-1.9v-54.1c0-1 .7-1.9 1.7-1.9zm-51.3 2.8h35.3c1.2 0 1.9 1.2 1.9 2.1s-.7 1.9-1.9 1.9h-33.4v22.5h30.2c.9 0 1.9.7 1.9 1.9 0 .9-.7 1.9-1.9 1.9h-29.9v23.2c-.2 1.2-1.2 2.1-2.3 2.1-1.2 0-2.1-.9-2.1-2.1v-51.3h.2a2 2 0 0 1 2-2.2zm102.5 16.7c0-1.2-.9-1.9-1.9-1.9-1.2 0-1.9.9-1.9 1.9v22.5c0 7.7-6.3 13.7-14 13.5-8.2 0-12.9-5.3-12.9-13.7v-22.3c0-1.2-.9-1.9-1.9-1.9-.9 0-1.9.9-1.9 1.9v23c0 9.5 6.1 16.5 16.1 16.5 6.1.2 11.9-3 14.7-8.4v5.8c0 1.2.9 1.9 1.9 1.9 1.2 0 1.9-.9 1.9-1.9h-.2v-36.9zm35.5.3c0 .9-.9 1.6-1.9 1.6h-12.9v25.8c0 5.8 3.3 7.9 8.2 7.9 1.6 0 3.3-.2 4.7-.7.9 0 1.6.7 1.6 1.6 0 .7-.5 1.4-1.2 1.6-1.9.7-4 .9-5.8.9-6.1 0-11.2-3.5-11.2-10.9v-26.2h-4.4c-.9 0-1.9-.9-1.9-1.9 0-.9.9-1.6 1.9-1.6h4.4v-11.1c0-.9.7-1.9 1.6-1.9h.2c.9 0 1.9.9 1.9 1.9v11.1h12.9c1 0 1.9.9 1.9 1.9zm30 1.6c.9 0 1.9-.7 1.9-1.6s-.9-1.9-1.9-1.9h-12.9v-11.1c0-.9-.9-1.9-1.9-1.9h-.2c-.9 0-1.6.9-1.6 1.9v11.1h-4.4c-.9 0-1.9.7-1.9 1.6s.9 1.9 1.9 1.9h4.4v26.2c0 7.4 5.1 10.9 11.2 10.9 1.9 0 4-.2 5.8-.9.7-.2 1.2-.9 1.2-1.6 0-.9-.7-1.6-1.6-1.6-1.4.5-3 .7-4.7.7-4.9 0-8.2-2.1-8.2-7.9v-25.8zm8.9 16.5c0-11.8 8.2-21.1 19.2-21.1 11.5 0 18.7 9.3 18.7 21.1 0 .9-.9 1.9-1.9 1.9h-31.8c.7 10 8 15.8 15.9 15.8 4.9 0 9.8-2.1 13.1-5.6.2-.2.7-.5 1.2-.5.9 0 1.9.9 1.9 1.9 0 .5-.2.9-.7 1.4-4 4.4-9.8 6.7-15.7 6.5-10.8 0-19.9-8.4-19.9-21.1zm53.1-8.4c2.6-6.7 8.9-11.6 16.1-12.1 1.2 0 2.1.9 2.1 2.3 0 .9-.7 2.1-1.9 2.1h-.2c-8.7.9-16.1 7.2-16.1 20.2v14.9c-.2 1.2-.9 1.9-2.1 1.9-.9 0-1.9-.9-1.9-1.9v-37.2c.2-1.2.9-1.9 2.1-1.9.9 0 1.9.9 1.9 1.9zm79.9-14.2c-2.8 0-5.1 1.9-5.8 4.6l-6.8 21.6-6.8-21.6c-.7-2.8-3.3-4.9-6.3-4.9h-.7c-3 0-5.6 1.9-6.3 4.9l-6.8 21.4-6.5-21.6c-.7-2.6-3-4.6-5.8-4.6h-.2c-3 0-5.4 2.6-5.4 5.6 0 .9.2 1.9.5 2.8l10.5 30c.7 3 3.3 5.1 6.5 5.3h.5c3 0 5.6-2.1 6.5-5.1l6.8-21.4 6.8 21.4c.7 3 3.5 5.1 6.5 5.1h.5c3.3 0 6.1-2.1 6.8-5.3l10.5-30.2c.2-.7.5-1.6.5-2.3v-.2c-.1-3.1-2.4-5.5-5.5-5.5zm16.4 2.1c4.4-1.4 8.9-2.3 13.6-2.1 6.5 0 11.2 1.9 14.7 4.9 3.3 3.7 4.9 8.6 4.7 13.5v19c0 3.3-2.6 5.8-5.8 5.8-3 0-5.6-2.1-5.8-5.1-3.3 3.5-8 5.6-12.9 5.3-7.7 0-14.5-4.6-14.5-13 0-9.3 7-13.7 17.1-13.7 3.5 0 7 .5 10.3 1.6v-.7c0-5.1-3-7.7-9.1-7.7-2.8 0-5.6.2-8.4 1.2-.5.2-1.2.2-1.6.2-2.8.2-5.1-1.9-5.1-4.6-.2-2 .9-3.9 2.8-4.6zm69 1.9c.7-2.6 3-4.2 5.6-4.2 3.3 0 5.8 2.6 5.8 5.6v.2c0 .9-.2 1.9-.7 2.8l-12.6 30c-1.2 3-4 4.9-7 5.1h-.7c-3.3-.2-5.8-2.3-6.8-5.3l-13.1-30c-.5-.9-.7-1.9-.7-2.8.2-3.3 2.8-5.6 5.8-5.6 2.8 0 5.1 1.9 5.8 4.4l9.1 24.4zm16.2 19.5c.5 11.6 10.5 20.7 22.2 20 5.4 0 10.5-1.6 14.7-5.1 1.2-.9 1.6-2.1 1.6-3.5v-.2c0-2.6-2.1-4.6-4.7-4.6-.9 0-2.1.2-2.8.9-2.6 1.9-5.6 3-8.7 2.8-5.1.2-9.6-3.3-10.3-8.4h24.1c3-.2 5.4-2.8 5.1-5.8v-.9c0-10.5-9.1-19.3-20.3-19-12.4 0-21.1 10-21.1 22.1v1.7z",fill:"#10112b"}),(0,i.jsx)("path",{d:"m196.7 280.4c.7-8.8 7-15.6 15-15.6 9.1 0 14 7.4 14.5 15.6zm167 7c0 4.6-4 7.4-9.6 7.2-3.7 0-6.5-1.9-6.5-5.1v-.2c0-3.5 3.3-5.8 8.4-5.8 2.6 0 5.4.7 7.7 1.6zm84.7-18.4c-4.9 0-8.2 3.5-9.1 9.1h18c-.7-5.4-4-9.1-8.9-9.1z",fill:"#fff"}),(0,i.jsx)("path",{d:"m-46.7 217.7c52.6-7.9 18.9 36.9 2.6 49.5 11.2 8.6 22.7 20.7 27.6 34.4 9.1 25.1-13.3 28.8-30.2 22.5-18.5-6.5-34.8-20.4-45.6-36.7-3 0-6.3-.5-9.4-1.4 6.1 17.2 8.7 34.8 7 49.2 0-29-13.8-57.8-33.7-81.8-7-8.4.2-14.6 6.5-6.5 4.3 5.9 8.2 12 11.7 18.3 6.9-23.6 40.5-44.2 63.5-47.5zm-7.5 42.7c10.3-6.3 41.6-39.9 12.4-36.9-16.8 1.9-37.2 17.4-45.6 27.4 11.7-1.4 23.6 3.7 33.2 9.5zm-29 26.1c9.4 10.5 22.2 20.7 36 24.4 8 2.1 16.8 1.2 13.6-10.2-3.3-10.5-11.7-19.7-19.9-26.7-2.3 1.6-4.9 3.3-7.5 4.4-7 3.9-14.5 6.7-22.2 8.1z",fill:"#eba12a"}),(0,i.jsx)("path",{d:"m-87.7 258.3c8-.7 16.6 3.5 23.2 7.7-6.3 3-13.3 4.9-20.6 5.3-10.7.1-12.9-12-2.6-13z",fill:"#fff"})]})};var webhook_url=e=>{var a;let{gateway:n}=e,[o,l]=(0,g.Z)(),[r,s]=(0,C.useState)(!1),d={stripe:(0,i.jsx)(StripeIcon,{className:"h-4 w-auto"}),paypal:(0,i.jsx)(PayPalIcon,{className:"h-4 w-auto"}),razorpay:(0,i.jsx)(RazorPayIcon,{className:"h-4 w-auto"}),mollie:(0,i.jsx)(MollieIcon,{className:"h-4 w-auto"}),sslcommerz:(0,i.jsx)(SSLComerz,{className:"h-4 w-auto"}),paystack:(0,i.jsx)(PayStack,{className:"h-4 w-auto"}),iyzico:(0,i.jsx)(IyzicoIcon,{className:"h-4 w-auto"}),xendit:(0,i.jsx)(XenditIcon,{className:"h-4 w-auto"}),bkash:(0,i.jsx)(BkashIcon,{className:"h-4 w-auto"}),paymongo:(0,i.jsx)(PaymongoIcon,{className:"h-4 w-auto"}),flutterwave:(0,i.jsx)(FlutterwaveIcon,{className:"h-4 w-auto"})},m="".concat("http://localhost:9000/api","/webhooks/").concat(null==n?void 0:null===(a=n.name)||void 0===a?void 0:a.toLowerCase());return setTimeout(()=>{s(!1)},5e3),(0,i.jsxs)("div",{className:"flex items-center border-t border-t-[#D1D5DB] px-5 py-4 transition-all first:border-t-0 hover:bg-gray-100",children:[(0,i.jsx)("span",{className:"relative h-5 min-w-[80px] sm:min-w-[100px] lg:min-w-[120px]",children:d[null==n?void 0:n.name]?d[null==n?void 0:n.name]:""}),(0,i.jsx)("span",{className:"ml-5 flex-grow truncate pr-2 text-xs text-gray-500",children:m}),(0,i.jsxs)("div",{className:"relative flex items-center",children:[r&&(0,i.jsx)("span",{className:"absolute right-full top-1/2 z-10 -translate-y-1/2 px-2",children:(0,i.jsx)(h.Z,{text:"Copied!",className:"inline-flex"})}),(0,i.jsx)("button",{type:"button",onClick:()=>{l(m),s(!0)},className:"text-accent-500 transition hover:text-accent-400",children:(0,i.jsx)(ClipboardIcon,{})})]})]})},b=n(60802),p=n(80602),y=n(66271),v=n(33e3),f=n(23091),L=n(55846),x=n(48403),_=n.n(x),A=n(87536);function CheckboxGroup(e){let{children:a,values:n,onChange:o}=e,l=(0,C.useCallback)(e=>{let{name:a}=e.target,i=n.includes(a)?n.filter(e=>e!==a):[...n,a];o(i)},[n,o]);return(0,i.jsx)(i.Fragment,{children:C.Children.map(a,e=>(0,C.isValidElement)(e)?(0,C.cloneElement)(e,{onChange:l,checked:null==n?void 0:n.includes(e.props.value)}):e)})}var S=n(3160),M=n(93967),w=n.n(M);let PaymentMethodCard=e=>{let{name:a,value:n,isDefault:o,disable:l,...r}=e,s={stripe:(0,i.jsx)(StripeIcon,{}),paypal:(0,i.jsx)(PayPalIcon,{}),razorpay:(0,i.jsx)(RazorPayIcon,{}),mollie:(0,i.jsx)(MollieIcon,{}),sslcommerz:(0,i.jsx)(SSLComerz,{}),paystack:(0,i.jsx)(PayStack,{}),iyzico:(0,i.jsx)(IyzicoIcon,{}),xendit:(0,i.jsx)(XenditIcon,{}),bkash:(0,i.jsx)(BkashIcon,{}),paymongo:(0,i.jsx)(PaymongoIcon,{}),flutterwave:(0,i.jsx)(FlutterwaveIcon,{})};return(0,i.jsxs)("label",{"aria-label":a,className:w()(o||l?"pointer-events-none cursor-not-allowed opacity-60":"cursor-pointer"),children:[(0,i.jsx)("input",{type:"checkbox",className:"peer invisible absolute -z-[1] opacity-0",value:n,name:a,...r}),(0,i.jsxs)("span",{className:"relative block w-full overflow-hidden rounded-md bg-gray-100 pb-[52%] peer-checked:border-2 peer-checked:border-accent peer-checked:shadow-md",children:[(0,i.jsx)("span",{className:"absolute flex h-full w-full items-center justify-center p-6 md:p-9",children:s[a]?s[a]:""}),o&&(0,i.jsx)("span",{className:"absolute -top-7 -right-7 flex h-14 w-14 rotate-45 items-end justify-center bg-accent p-2 text-white",children:(0,i.jsx)(S.r,{className:"h-auto w-2.5"})})]})]},a)};var payment_select=e=>{let{options:a,control:n,rules:o,name:l,defaultItem:r,disable:s,...d}=e;return(0,i.jsx)(A.Qr,{control:n,name:l,rules:o,...d,render:e=>{let{field:{onChange:n,value:o}}=e;return(0,i.jsx)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-5",children:(0,i.jsx)(CheckboxGroup,{values:o.map(e=>null==e?void 0:e.name),onChange:e=>{let a=e.map(e=>({name:e,title:_()(e)}));n(a)},children:null==a?void 0:a.map(e=>(0,i.jsx)(PaymentMethodCard,{value:e.name,name:e.name,isDefault:(null==e?void 0:e.name)===r,disable:s},null==e?void 0:e.name))})})}})},E=n(28454),N=n(22220),B=n(77180),k=n(90573),j=n(3986),D=n(60942),F=n(47533),R=n(41609),Z=n.n(R),G=n(5233),I=n(11163);function PaymentSettingsForm(e){var a,n,o,c,g,x,_,S;let{settings:M}=e,{t:w}=(0,G.$G)(),{locale:R}=(0,I.useRouter)(),[T,P]=(0,C.useState)(!1),{mutate:K,isLoading:z}=(0,k.B)(),{language:V,options:U}=null!=M?M:{},{register:O,handleSubmit:Q,control:X,watch:Y,reset:W,formState:{errors:J,isDirty:q}}=(0,A.cI)({shouldUnregister:!0,resolver:(0,F.X)(t),defaultValues:{...U,useEnableGateway:null===(_=null==U?void 0:U.useEnableGateway)||void 0===_||_,currency:(null==U?void 0:U.currency)?d.find(e=>e.code==(null==U?void 0:U.currency)):"",defaultPaymentGateway:(null==U?void 0:U.defaultPaymentGateway)?m.find(e=>e.name==(null==U?void 0:U.defaultPaymentGateway)):m[0],currencyOptions:{...null==U?void 0:U.currencyOptions,formation:(null==U?void 0:null===(a=U.currencyOptions)||void 0===a?void 0:a.formation)?s.find(e=>{var a;return e.code==(null==U?void 0:null===(a=U.currencyOptions)||void 0===a?void 0:a.formation)}):s[0]},paymentGateway:(null==U?void 0:U.paymentGateway)?null==U?void 0:null===(n=U.paymentGateway)||void 0===n?void 0:n.map(e=>({name:null==e?void 0:e.name,title:null==e?void 0:e.title})):[]}}),$=Y("currency"),ee=Y("currencyOptions.formation"),ea=Y("currencyOptions.fractions");async function onSubmit(e){var a,n,i,o,l;K({language:R,options:{...U,...e,currency:null===(a=e.currency)||void 0===a?void 0:a.code,defaultPaymentGateway:null===(n=e.defaultPaymentGateway)||void 0===n?void 0:n.name,paymentGateway:(null==e?void 0:e.paymentGateway)&&(null==e?void 0:e.paymentGateway.length)?null==e?void 0:null===(i=e.paymentGateway)||void 0===i?void 0:i.map(e=>({name:e.name,title:e.title})):m.filter((e,a)=>a<2),useEnableGateway:null==e?void 0:e.useEnableGateway,currencyOptions:{...e.currencyOptions,formation:null==e?void 0:null===(l=e.currencyOptions)||void 0===l?void 0:null===(o=l.formation)||void 0===o?void 0:o.code}}}),W(e,{keepValues:!0})}(0,j.H)({isDirty:q});let en=Y("paymentGateway"),ei=Y("defaultPaymentGateway"),eo=Y("useEnableGateway"),el=null==en?void 0:en.some(e=>(null==e?void 0:e.name)===(null==ei?void 0:ei.name)),er=null==en?void 0:en.some(e=>(null==e?void 0:e.name)==="stripe");return(0,i.jsxs)("form",{onSubmit:Q(onSubmit),children:[(0,i.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,i.jsx)(p.Z,{title:w("form:form-title-payment"),details:w("form:payment-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,i.jsxs)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,i.jsx)("div",{className:"mb-5",children:(0,i.jsx)(B.Z,{name:"useCashOnDelivery",control:X,label:w("form:input-label-cash-on-delivery"),toolTipText:w("form:input-tooltip-enable-cash-on-delivery")})}),(0,i.jsxs)("div",{className:"mb-5",children:[(0,i.jsx)(E.Z,{name:"currency",control:X,getOptionLabel:e=>e.name,getOptionValue:e=>e.code,options:d,label:w("form:input-label-currency"),toolTipText:w("form:input-tooltip-currency")}),(0,i.jsx)(y.Z,{message:w(null===(o=J.currency)||void 0===o?void 0:o.message)})]}),(0,i.jsx)(B.Z,{control:X,...O("useEnableGateway"),label:w("text-enable-gateway"),toolTipText:w("form:input-tooltip-enable-gateway")}),eo?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mt-5 mb-5",children:[(0,i.jsx)(f.Z,{children:w("text-select-payment-gateway")}),(0,i.jsx)(payment_select,{options:m,control:X,name:"paymentGateway",defaultItem:el?null==ei?void 0:ei.name:"",disable:Z()(en)})]}),Z()(en)?(0,i.jsx)("div",{className:"flex px-5 py-4",children:(0,i.jsx)(L.Z,{simple:!1,showText:!0,text:"form:text-payment-method-preparing",className:"mx-auto !h-20"})}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"mb-5",children:(0,i.jsx)(E.Z,{name:"defaultPaymentGateway",control:X,getOptionLabel:e=>e.title,getOptionValue:e=>e.name,options:null!=en?en:[],label:w("text-select-default-payment-gateway")})}),er&&(0,i.jsx)("div",{className:"mb-5",children:(0,i.jsx)(B.Z,{name:"StripeCardOnly",control:X,label:w("Enable Stripe Element")})}),(0,i.jsx)(f.Z,{children:w("text-webhook-url")}),(0,i.jsx)("div",{className:"relative flex flex-col overflow-hidden rounded-md border border-solid border-[#D1D5DB]",children:null==en?void 0:en.map((e,a)=>(0,i.jsx)(webhook_url,{gateway:e},a))})]})]}):""]})]}),(0,i.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:mt-8 sm:mb-3",children:[(0,i.jsx)(p.Z,{title:w("text-currency-options"),details:w("form:currency-options-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pr-4 md:w-1/3 md:pr-5"}),(0,i.jsxs)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,i.jsx)("div",{className:"mb-5",children:(0,i.jsx)(E.Z,{...O("currencyOptions.formation"),control:X,getOptionLabel:e=>e.name,getOptionValue:e=>e.code,options:s,required:!0,label:w("form:input-label-currency-formations"),toolTipText:w("form:input-tooltip-currency-formation")})}),(0,i.jsx)(v.Z,{label:w("form:input-label-currency-number-of-decimal"),required:!0,toolTipText:w("form:input-tooltip-fractional-digits"),...O("currencyOptions.fractions"),type:"number",variant:"outline",placeholder:w("form:input-placeholder-currency-number-of-decimal"),error:w(null===(g=J.currencyOptions)||void 0===g?void 0:null===(c=g.fractions)||void 0===c?void 0:c.message),className:"mb-5"}),ee&&(0,i.jsx)("div",{className:"mb-5",children:(0,i.jsxs)(f.Z,{className:"flex items-center gap-2.5",children:["Sample Output: ",(0,i.jsx)(h.Z,{text:(0,D.T4)({amount:987456321.1234568,currencyCode:null!==(S=null==$?void 0:$.code)&&void 0!==S?S:null==M?void 0:null===(x=M.options)||void 0===x?void 0:x.currency,locale:null==ee?void 0:ee.code,fractions:ea}),color:"bg-accent"})]})})]})]}),(0,i.jsx)(N.Z,{className:"z-0",children:(0,i.jsxs)(b.Z,{loading:z,disabled:z||!q,className:"text-sm md:text-base",children:[(0,i.jsx)(r.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),w("form:button-label-save-settings")]})})]})}var T=n(59122),P=n(45957),K=n(16203),z=!0;function PaymentSettings(){let{t:e}=(0,G.$G)(),{locale:a}=(0,I.useRouter)(),{settings:n,loading:o,error:l}=(0,k.n)({language:a});return o?(0,i.jsx)(L.Z,{text:e("common:text-loading")}):l?(0,i.jsx)(P.Z,{message:l.message}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T.Z,{pageTitle:"form:form-title-payment-settings"}),(0,i.jsx)(PaymentSettingsForm,{settings:n})]})}PaymentSettings.authenticate={permissions:K.M$},PaymentSettings.Layout=o.default},60942:function(e,a,n){"use strict";n.d(a,{T4:function(){return formatPrice},ZP:function(){return usePrice}});var i=n(67294),o=n(99494),l=n(73263);function formatPrice(e){let{amount:a,currencyCode:n,locale:i,fractions:o=2}=e,l=new Intl.NumberFormat(i,{style:"currency",currency:n,maximumFractionDigits:o>20||o<0||!o?2:o});return l.format(a)}function usePrice(e){let{currency:a,currencyOptions:n}=(0,l.rV)(),{formation:r,fractions:s}=n,{amount:d,baseAmount:m,currencyCode:c=a}=null!=e?e:{},t=null!=r?r:o.siteSettings.defaultLanguage,C=(0,i.useMemo)(()=>"number"==typeof d&&c?m?function(e){let{amount:a,baseAmount:n,currencyCode:i,locale:o,fractions:l=2}=e,r=n<a,s=new Intl.NumberFormat(o,{style:"percent"}),d=r?s.format((a-n)/a):null,m=formatPrice({amount:a,currencyCode:i,locale:o,fractions:l}),c=r?formatPrice({amount:n,currencyCode:i,locale:o,fractions:l}):null;return{price:m,basePrice:c,discount:d}}({amount:d,baseAmount:m,currencyCode:c,locale:t,fractions:s}):formatPrice({amount:d,currencyCode:c,locale:t,fractions:s}):"",[d,m,c]);return"string"==typeof C?{price:C,basePrice:null,discount:null}:C}},86366:function(e,a,n){"use strict";n.d(a,{Z:function(){return esm_useCopyToClipboard}});var i=n(20640),o=n.n(i),l=n(67294),esm_useSetState=function(e){void 0===e&&(e={});var a=(0,l.useState)(e),n=a[0],i=a[1];return[n,(0,l.useCallback)(function(e){i(function(a){return Object.assign({},a,e instanceof Function?e(a):e)})},[])]},esm_useCopyToClipboard=function(){var e,a,n=(e=(0,l.useRef)(!1),a=(0,l.useCallback)(function(){return e.current},[]),(0,l.useEffect)(function(){return e.current=!0,function(){e.current=!1}},[]),a),i=esm_useSetState({value:void 0,error:void 0,noUserInteraction:!0}),r=i[0],s=i[1];return[r,(0,l.useCallback)(function(e){if(n())try{if("string"!=typeof e&&"number"!=typeof e){var a,i,l=Error("Cannot copy typeof "+typeof e+" to clipboard, must be a string");s({value:e,error:l,noUserInteraction:!0});return}if(""===e){var l=Error("Cannot copy empty string to clipboard.");s({value:e,error:l,noUserInteraction:!0});return}i=e.toString(),a=o()(i),s({value:i,error:void 0,noUserInteraction:a})}catch(e){s({value:i,error:e,noUserInteraction:a})}},[])]}},11742:function(e){e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var a=document.activeElement,n=[],i=0;i<e.rangeCount;i++)n.push(e.getRangeAt(i));switch(a.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":a.blur();break;default:a=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(a){e.addRange(a)}),a&&a.focus()}}},95389:function(e,a,n){"use strict";n.d(a,{_:function(){return c},b:function(){return H}});var i=n(67294),o=n(19946),l=n(12351),r=n(16723),s=n(23784),d=n(73781);let m=(0,i.createContext)(null);function H(){let[e,a]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)(()=>function(e){let n=(0,d.z)(e=>(a(a=>[...a,e]),()=>a(a=>{let n=a.slice(),i=n.indexOf(e);return -1!==i&&n.splice(i,1),n}))),o=(0,i.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return i.createElement(m.Provider,{value:o},e.children)},[a])]}let c=Object.assign((0,l.yV)(function(e,a){let n=(0,o.M)(),{id:d=`headlessui-label-${n}`,passive:c=!1,...t}=e,C=function u(){let e=(0,i.useContext)(m);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),g=(0,s.T)(a);(0,r.e)(()=>C.register(d),[d,C.register]);let h={ref:g,...C.props,id:d};return c&&("onClick"in h&&(delete h.htmlFor,delete h.onClick),"onClick"in t&&delete t.onClick),(0,l.sY)({ourProps:h,theirProps:t,slot:C.slot||{},defaultTag:"label",name:C.name||"Label"})}),{})},77768:function(e,a,n){"use strict";n.d(a,{r:function(){return f}});var i=n(67294),o=n(12351),l=n(19946),r=n(61363),s=n(64103),d=n(95389),m=n(39516),c=n(14157),t=n(23784),C=n(46045),g=n(18689),h=n(73781),b=n(31147),p=n(94192);let y=(0,i.createContext)(null);y.displayName="GroupContext";let v=i.Fragment,f=Object.assign((0,o.yV)(function(e,a){let n=(0,l.M)(),{id:d=`headlessui-switch-${n}`,checked:m,defaultChecked:v=!1,onChange:f,name:L,value:x,form:_,...A}=e,S=(0,i.useContext)(y),M=(0,i.useRef)(null),w=(0,t.T)(M,a,null===S?null:S.setSwitch),[E,N]=(0,b.q)(m,f,v),B=(0,h.z)(()=>null==N?void 0:N(!E)),k=(0,h.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),B()}),j=(0,h.z)(e=>{e.key===r.R.Space?(e.preventDefault(),B()):e.key===r.R.Enter&&(0,g.g)(e.currentTarget)}),D=(0,h.z)(e=>e.preventDefault()),F=(0,i.useMemo)(()=>({checked:E}),[E]),R={id:d,ref:w,role:"switch",type:(0,c.f)(e,M),tabIndex:0,"aria-checked":E,"aria-labelledby":null==S?void 0:S.labelledby,"aria-describedby":null==S?void 0:S.describedby,onClick:k,onKeyUp:j,onKeyPress:D},Z=(0,p.G)();return(0,i.useEffect)(()=>{var e;let a=null==(e=M.current)?void 0:e.closest("form");a&&void 0!==v&&Z.addEventListener(a,"reset",()=>{N(v)})},[M,N]),i.createElement(i.Fragment,null,null!=L&&E&&i.createElement(C._,{features:C.A.Hidden,...(0,o.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:_,checked:E,name:L,value:x})}),(0,o.sY)({ourProps:R,theirProps:A,slot:F,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var a;let[n,l]=(0,i.useState)(null),[r,s]=(0,d.b)(),[c,t]=(0,m.f)(),C=(0,i.useMemo)(()=>({switch:n,setSwitch:l,labelledby:r,describedby:c}),[n,l,r,c]);return i.createElement(t,{name:"Switch.Description"},i.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(a=C.switch)?void 0:a.id,onClick(e){n&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},i.createElement(y.Provider,{value:C},(0,o.sY)({ourProps:{},theirProps:e,defaultTag:v,name:"Switch.Group"}))))},Label:d._,Description:m.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,9494,5535,8186,1285,1631,963,9774,2888,179],function(){return e(e.s=26508)}),_N_E=e.O()}]);