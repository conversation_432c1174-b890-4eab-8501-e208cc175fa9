"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_details_favorite-button_tsx";
exports.ids = ["src_components_products_details_favorite-button_tsx"];
exports.modules = {

/***/ "./src/components/icons/heart-fill.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/heart-fill.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartFillIcon: () => (/* binding */ HeartFillIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartFillIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 -28 512 512\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1em\",\n        height: \"1em\",\n        fill: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M471.383 44.578C444.879 15.832 408.512 0 368.973 0c-29.555 0-56.621 9.344-80.45 27.77C276.5 37.07 265.605 48.45 256 61.73c-9.602-13.277-20.5-24.66-32.527-33.96C199.648 9.344 172.582 0 143.027 0c-39.539 0-75.91 15.832-102.414 44.578C14.426 72.988 0 111.801 0 153.871c0 43.3 16.137 82.938 50.781 124.742 30.992 37.395 75.535 75.356 127.117 119.313 17.614 15.012 37.579 32.027 58.309 50.152A30.023 30.023 0 0 0 256 455.516c7.285 0 14.316-2.641 19.785-7.43 20.73-18.129 40.707-35.152 58.328-50.172 51.575-43.95 96.117-81.906 127.11-119.305C495.867 236.81 512 197.172 512 153.867c0-42.066-14.426-80.879-40.617-109.289zm0 0\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-fill.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-fill.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9oZWFydC1maWxsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sTUFBTUEsZ0JBQW1ELENBQUNDLHNCQUMvRCw4REFBQ0M7UUFDQ0MsU0FBUTtRQUNSQyxPQUFNO1FBQ05DLE9BQU07UUFDTkMsUUFBTztRQUNQQyxNQUFLO1FBQ0osR0FBR04sS0FBSztrQkFFVCw0RUFBQ087WUFBS0MsR0FBRTs7Ozs7Ozs7OztrQkFFViIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9pY29ucy9oZWFydC1maWxsLnRzeD8xOGIwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBIZWFydEZpbGxJY29uOiBSZWFjdC5GQzxSZWFjdC5TVkdBdHRyaWJ1dGVzPHt9Pj4gPSAocHJvcHMpID0+IChcclxuICA8c3ZnXHJcbiAgICB2aWV3Qm94PVwiMCAtMjggNTEyIDUxMlwiXHJcbiAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgIHdpZHRoPVwiMWVtXCJcclxuICAgIGhlaWdodD1cIjFlbVwiXHJcbiAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcclxuICAgIHsuLi5wcm9wc31cclxuICA+XHJcbiAgICA8cGF0aCBkPVwiTTQ3MS4zODMgNDQuNTc4QzQ0NC44NzkgMTUuODMyIDQwOC41MTIgMCAzNjguOTczIDBjLTI5LjU1NSAwLTU2LjYyMSA5LjM0NC04MC40NSAyNy43N0MyNzYuNSAzNy4wNyAyNjUuNjA1IDQ4LjQ1IDI1NiA2MS43M2MtOS42MDItMTMuMjc3LTIwLjUtMjQuNjYtMzIuNTI3LTMzLjk2QzE5OS42NDggOS4zNDQgMTcyLjU4MiAwIDE0My4wMjcgMGMtMzkuNTM5IDAtNzUuOTEgMTUuODMyLTEwMi40MTQgNDQuNTc4QzE0LjQyNiA3Mi45ODggMCAxMTEuODAxIDAgMTUzLjg3MWMwIDQzLjMgMTYuMTM3IDgyLjkzOCA1MC43ODEgMTI0Ljc0MiAzMC45OTIgMzcuMzk1IDc1LjUzNSA3NS4zNTYgMTI3LjExNyAxMTkuMzEzIDE3LjYxNCAxNS4wMTIgMzcuNTc5IDMyLjAyNyA1OC4zMDkgNTAuMTUyQTMwLjAyMyAzMC4wMjMgMCAwIDAgMjU2IDQ1NS41MTZjNy4yODUgMCAxNC4zMTYtMi42NDEgMTkuNzg1LTcuNDMgMjAuNzMtMTguMTI5IDQwLjcwNy0zNS4xNTIgNTguMzI4LTUwLjE3MiA1MS41NzUtNDMuOTUgOTYuMTE3LTgxLjkwNiAxMjcuMTEtMTE5LjMwNUM0OTUuODY3IDIzNi44MSA1MTIgMTk3LjE3MiA1MTIgMTUzLjg2N2MwLTQyLjA2Ni0xNC40MjYtODAuODc5LTQwLjYxNy0xMDkuMjg5em0wIDBcIiAvPlxyXG4gIDwvc3ZnPlxyXG4pO1xyXG4iXSwibmFtZXMiOlsiSGVhcnRGaWxsSWNvbiIsInByb3BzIiwic3ZnIiwidmlld0JveCIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJmaWxsIiwicGF0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/heart-fill.tsx\n");

/***/ }),

/***/ "./src/components/icons/heart-ghost.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/heart-ghost.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartGhostIcon: () => (/* binding */ HeartGhostIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartGhostIcon = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                opacity: 0.2,\n                d: \"M14.5 5.875C14.5 10 8 13.5 8 13.5S1.5 10 1.5 5.875A3.375 3.375 0 014.875 2.5c1.412 0 2.621.77 3.125 2 .504-1.23 1.713-2 3.125-2A3.375 3.375 0 0114.5 5.875z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11.125 2C9.835 2 8.705 2.555 8 3.493 7.296 2.555 6.166 2 4.875 2A3.88 3.88 0 001 5.875c0 4.375 6.487 7.916 6.763 8.063a.5.5 0 00.474 0C8.513 13.79 15 10.25 15 5.874A3.88 3.88 0 0011.125 2zM8 12.925c-1.141-.665-6-3.694-6-7.05A2.879 2.879 0 014.875 3c1.216 0 2.236.647 2.662 1.688a.5.5 0 00.926 0C8.889 3.646 9.909 3 11.125 3A2.879 2.879 0 0114 5.875c0 3.35-4.86 6.384-6 7.05z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-ghost.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/heart-ghost.tsx\n");

/***/ }),

/***/ "./src/components/icons/heart-outline.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/heart-outline.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeartOutlineIcon: () => (/* binding */ HeartOutlineIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst HeartOutlineIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 -28 512.001 512\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"1em\",\n        height: \"1em\",\n        fill: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M256 455.516c-7.29 0-14.316-2.641-19.793-7.438-20.684-18.086-40.625-35.082-58.219-50.074l-.09-.078c-51.582-43.957-96.125-81.918-127.117-119.313C16.137 236.81 0 197.172 0 153.871c0-42.07 14.426-80.883 40.617-109.293C67.121 15.832 103.488 0 143.031 0c29.555 0 56.621 9.344 80.446 27.77C235.5 37.07 246.398 48.453 256 61.73c9.605-13.277 20.5-24.66 32.527-33.96C312.352 9.344 339.418 0 368.973 0c39.539 0 75.91 15.832 102.414 44.578C497.578 72.988 512 111.801 512 153.871c0 43.3-16.133 82.938-50.777 124.738-30.993 37.399-75.532 75.356-127.106 119.309-17.625 15.016-37.597 32.039-58.328 50.168a30.046 30.046 0 0 1-19.789 7.43zM143.031 29.992c-31.066 0-59.605 12.399-80.367 34.914-21.07 22.856-32.676 54.45-32.676 88.965 0 36.418 13.535 68.988 43.883 105.606 29.332 35.394 72.961 72.574 123.477 115.625l.093.078c17.66 15.05 37.68 32.113 58.516 50.332 20.961-18.254 41.012-35.344 58.707-50.418 50.512-43.051 94.137-80.223 123.469-115.617 30.344-36.618 43.879-69.188 43.879-105.606 0-34.516-11.606-66.11-32.676-88.965-20.758-22.515-49.3-34.914-80.363-34.914-22.758 0-43.653 7.235-62.102 21.5-16.441 12.719-27.894 28.797-34.61 40.047-3.452 5.785-9.53 9.238-16.261 9.238s-12.809-3.453-16.262-9.238c-6.71-11.25-18.164-27.328-34.61-40.047-18.448-14.265-39.343-21.5-62.097-21.5zm0 0\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-outline.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\heart-outline.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/icons/heart-outline.tsx\n");

/***/ }),

/***/ "./src/components/products/details/favorite-button.tsx":
/*!*************************************************************!*\
  !*** ./src/components/products/details/favorite-button.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_heart_fill__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/heart-fill */ \"./src/components/icons/heart-fill.tsx\");\n/* harmony import */ var _components_icons_heart_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons/heart-outline */ \"./src/components/icons/heart-outline.tsx\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n/* harmony import */ var _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/wishlist */ \"./src/framework/rest/wishlist.ts\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\n/* harmony import */ var _components_icons_heart_ghost__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/icons/heart-ghost */ \"./src/components/icons/heart-ghost.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_user__WEBPACK_IMPORTED_MODULE_5__, _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__, tailwind_merge__WEBPACK_IMPORTED_MODULE_8__]);\n([_framework_user__WEBPACK_IMPORTED_MODULE_5__, _framework_wishlist__WEBPACK_IMPORTED_MODULE_6__, tailwind_merge__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction FavoriteButton({ productId, className, variant = \"default\" }) {\n    const { isAuthorized } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_5__.useUser)();\n    const { toggleWishlist, isLoading: adding } = (0,_framework_wishlist__WEBPACK_IMPORTED_MODULE_6__.useToggleWishlist)(productId);\n    const { inWishlist, isLoading: checking } = (0,_framework_wishlist__WEBPACK_IMPORTED_MODULE_6__.useInWishlist)({\n        enabled: isAuthorized,\n        product_id: productId\n    });\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_4__.useModalAction)();\n    function toggle() {\n        if (!isAuthorized) {\n            openModal(\"LOGIN_VIEW\");\n            return;\n        }\n        toggleWishlist({\n            product_id: productId\n        });\n    }\n    const isLoading = adding || checking;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_8__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"mt-0.5 flex h-10 w-10 shrink-0 items-center justify-center rounded-full border border-gray-300\", variant === \"minimal\" ? \"bg-black bg-opacity-20\" : \"\", className)),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                simple: true,\n                className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(variant === \"default\" ? \"h-5 w-5\" : \"h-4 w-4\")\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"button\",\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_8__.twMerge)(classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"mt-0.5 flex h-10 w-10 shrink-0 items-center justify-center rounded-full border border-gray-300 text-xl text-accent transition-colors\", inWishlist ? \"border-accent\" : \"border-gray-300\", variant === \"minimal\" && (inWishlist ? \"bg-accent text-white\" : \"bg-black bg-opacity-20 text-white\"), className)),\n        onClick: toggle,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: variant === \"default\" ? inWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_fill__WEBPACK_IMPORTED_MODULE_1__.HeartFillIcon, {}, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_outline__WEBPACK_IMPORTED_MODULE_2__.HeartOutlineIcon, {}, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_heart_ghost__WEBPACK_IMPORTED_MODULE_9__.HeartGhostIcon, {}, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n                lineNumber: 78,\n                columnNumber: 11\n            }, this)\n        }, void 0, false)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\details\\\\favorite-button.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FavoriteButton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/details/favorite-button.tsx\n");

/***/ }),

/***/ "./src/framework/rest/wishlist.ts":
/*!****************************************!*\
  !*** ./src/framework/rest/wishlist.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInWishlist: () => (/* binding */ useInWishlist),\n/* harmony export */   useRemoveFromWishlist: () => (/* binding */ useRemoveFromWishlist),\n/* harmony export */   useToggleWishlist: () => (/* binding */ useToggleWishlist),\n/* harmony export */   useWishlist: () => (/* binding */ useWishlist)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client */ \"./src/framework/rest/client/index.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/framework/rest/client/api-endpoints.ts\");\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/data-mappers */ \"./src/framework/rest/utils/data-mappers.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _client__WEBPACK_IMPORTED_MODULE_4__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, react_toastify__WEBPACK_IMPORTED_MODULE_3__, _client__WEBPACK_IMPORTED_MODULE_4__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction useToggleWishlist(product_id) {\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const { mutate: toggleWishlist, isLoading, isSuccess } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.toggle, {\n        onSuccess: (data)=>{\n            queryClient.setQueryData([\n                `${_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.WISHLIST}/in_wishlist`,\n                product_id\n            ], (old)=>!old);\n        },\n        onError: (error)=>{\n            if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(`${t(error.response?.data.message)}`);\n            }\n        }\n    });\n    return {\n        toggleWishlist,\n        isLoading,\n        isSuccess\n    };\n}\nfunction useRemoveFromWishlist() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const { mutate: removeFromWishlist, isLoading, isSuccess } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.remove, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(`${t(\"text-removed-from-wishlist\")}`);\n            queryClient.refetchQueries([\n                _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.USERS_WISHLIST\n            ]);\n        },\n        onError: (error)=>{\n            if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(`${t(error.response?.data.message)}`);\n            }\n        }\n    });\n    return {\n        removeFromWishlist,\n        isLoading,\n        isSuccess\n    };\n}\nfunction useWishlist(options) {\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const formattedOptions = {\n        ...options\n    };\n    const { data, isLoading, error, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useInfiniteQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.USERS_WISHLIST,\n        formattedOptions\n    ], ({ queryKey, pageParam })=>_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.all(Object.assign({}, queryKey[1], pageParam)), {\n        getNextPageParam: ({ current_page, last_page })=>last_page > current_page && {\n                page: current_page + 1\n            }\n    });\n    function handleLoadMore() {\n        fetchNextPage();\n    }\n    return {\n        wishlists: data?.pages?.flatMap((page)=>page.data) ?? [],\n        paginatorInfo: Array.isArray(data?.pages) ? (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_6__.mapPaginatorData)(data?.pages[data.pages.length - 1]) : null,\n        isLoading,\n        error,\n        isFetching,\n        isLoadingMore: isFetchingNextPage,\n        loadMore: handleLoadMore,\n        hasMore: Boolean(hasNextPage)\n    };\n}\nfunction useInWishlist({ enabled, product_id }) {\n    const { data, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)([\n        `${_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.WISHLIST}/in_wishlist`,\n        product_id\n    ], ()=>_client__WEBPACK_IMPORTED_MODULE_4__[\"default\"].wishlist.checkIsInWishlist({\n            product_id\n        }), {\n        enabled\n    });\n    return {\n        inWishlist: Boolean(data) ?? false,\n        isLoading,\n        error,\n        refetch\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/framework/rest/wishlist.ts\n");

/***/ })

};
;