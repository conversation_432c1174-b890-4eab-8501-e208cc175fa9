"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9824],{17099:function(e,t,u){u.r(t);var n=u(85893),s=u(71421),i=u(75814),a=u(52541);t.default=()=>{let{mutate:e,isLoading:t}=(0,a.VR)(),{data:u}=(0,i.X9)(),{closeModal:o}=(0,i.SO)();return(0,n.jsx)(s.Z,{onCancel:o,onDelete:function(){e({id:null==u?void 0:u.id}),o()},deleteBtnLoading:t})}},52541:function(e,t,u){u.d(t,{VR:function(){return useDeleteQuestionMutation},Pb:function(){return useQuestionsQuery},bD:function(){return useReplyQuestionMutation}});var n=u(88767),s=u(28597),i=u(47869),a=u(22920),o=u(5233),r=u(55191),l=u(3737);let c={...(0,r.h)(i.P.QUESTIONS),get(e){let{id:t}=e;return l.eN.get("".concat(i.P.QUESTIONS,"/").concat(t))},paginated:e=>{let{type:t,shop_id:u,...n}=e;return l.eN.get(i.P.QUESTIONS,{searchJoin:"and",with:"product;user",...n,search:l.eN.formatSearchParams({type:t,shop_id:u})})}},useQuestionsQuery=e=>{var t;let{data:u,error:a,isLoading:o}=(0,n.useQuery)([i.P.QUESTIONS,e],e=>{let{queryKey:t,pageParam:u}=e;return c.paginated(Object.assign({},t[1],u))},{keepPreviousData:!0});return{questions:null!==(t=null==u?void 0:u.data)&&void 0!==t?t:[],paginatorInfo:(0,s.Q)(u),error:a,loading:o}},useReplyQuestionMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(c.update,{onSuccess:()=>{a.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.QUESTIONS)}})},useDeleteQuestionMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,o.$G)();return(0,n.useMutation)(c.delete,{onSuccess:()=>{a.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.QUESTIONS)}})}}}]);