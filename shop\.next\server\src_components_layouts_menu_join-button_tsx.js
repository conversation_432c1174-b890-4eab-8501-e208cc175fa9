"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_layouts_menu_join-button_tsx";
exports.ids = ["src_components_layouts_menu_join-button_tsx"];
exports.modules = {

/***/ "./src/components/layouts/menu/join-button.tsx":
/*!*****************************************************!*\
  !*** ./src/components/layouts/menu/join-button.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JoinButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction JoinButton({ title = \"join-button\", size = \"small\", className = \"font-semibold\" }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleJoin() {\n        return openModal(\"LOGIN_VIEW\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        className: className,\n        size: size,\n        onClick: handleJoin,\n        children: t(title)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\layouts\\\\menu\\\\join-button.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21lbnUvam9pbi1idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ3lCO0FBRXZCO0FBRS9CLFNBQVNHLFdBQVcsRUFDakNDLFFBQVEsYUFBYSxFQUNyQkMsT0FBTyxPQUFPLEVBQ2RDLFlBQVksZUFBZSxFQUN1QjtJQUNsRCxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHTCw0REFBY0EsQ0FBQztJQUM3QixNQUFNLEVBQUVNLFNBQVMsRUFBRSxHQUFHUCxrRkFBY0E7SUFDcEMsU0FBU1E7UUFDUCxPQUFPRCxVQUFVO0lBQ25CO0lBQ0EscUJBQ0UsOERBQUNSLDZEQUFNQTtRQUFDTSxXQUFXQTtRQUFXRCxNQUFNQTtRQUFNSyxTQUFTRDtrQkFDaERGLEVBQUVIOzs7Ozs7QUFHVCIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9sYXlvdXRzL21lbnUvam9pbi1idXR0b24udHN4PzEwNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJ1dHRvbiBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgdXNlTW9kYWxBY3Rpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbW9kYWwvbW9kYWwuY29udGV4dCc7XHJcbmltcG9ydCB7IEJ1dHRvblByb3BzIH0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEpvaW5CdXR0b24oe1xyXG4gIHRpdGxlID0gJ2pvaW4tYnV0dG9uJyxcclxuICBzaXplID0gJ3NtYWxsJyxcclxuICBjbGFzc05hbWUgPSAnZm9udC1zZW1pYm9sZCcsXHJcbn06IFBpY2s8QnV0dG9uUHJvcHMsICd0aXRsZScgfCAnc2l6ZScgfCAnY2xhc3NOYW1lJz4pIHtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcclxuICBjb25zdCB7IG9wZW5Nb2RhbCB9ID0gdXNlTW9kYWxBY3Rpb24oKTtcclxuICBmdW5jdGlvbiBoYW5kbGVKb2luKCkge1xyXG4gICAgcmV0dXJuIG9wZW5Nb2RhbCgnTE9HSU5fVklFVycpO1xyXG4gIH1cclxuICByZXR1cm4gKFxyXG4gICAgPEJ1dHRvbiBjbGFzc05hbWU9e2NsYXNzTmFtZX0gc2l6ZT17c2l6ZX0gb25DbGljaz17aGFuZGxlSm9pbn0+XHJcbiAgICAgIHt0KHRpdGxlKX1cclxuICAgIDwvQnV0dG9uPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsInVzZU1vZGFsQWN0aW9uIiwidXNlVHJhbnNsYXRpb24iLCJKb2luQnV0dG9uIiwidGl0bGUiLCJzaXplIiwiY2xhc3NOYW1lIiwidCIsIm9wZW5Nb2RhbCIsImhhbmRsZUpvaW4iLCJvbkNsaWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/layouts/menu/join-button.tsx\n");

/***/ })

};
;