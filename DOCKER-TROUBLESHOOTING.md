# 🐳 Docker Setup Troubleshooting Guide

## ✅ **Current Working Setup**

The backend services are successfully running in Docker containers:

### **✅ Working Services**
- **PostgreSQL**: Running on port 5432
- **MinIO**: Running on ports 9000 (API) and 9001 (Console)
- **API REST**: Running on port 5000 with database seeded

### **🔧 Frontend Services**
- **Admin Panel**: Running locally on port 3002
- **Shop**: Running locally on port 3003

## 🚀 **Quick Start Commands**

### **1. Start Backend Services (Docker)**
```powershell
# Start backend services
docker-compose -f docker-compose.simple.yml up -d

# Check services status
docker-compose -f docker-compose.simple.yml ps

# Seed database (if needed)
docker-compose -f docker-compose.simple.yml exec api-rest node simple-seed.js
```

### **2. Start Frontend Services (Local)**
```powershell
# Terminal 1: Start Admin Panel
cd admin-rest
yarn dev

# Terminal 2: Start Shop
cd shop
yarn dev:rest
```

## 🌐 **Access URLs**

| Service | URL | Status |
|---------|-----|--------|
| API REST | http://localhost:5000/api | ✅ Working |
| API Docs | http://localhost:5000/docs | ✅ Working |
| MinIO Console | http://localhost:9001 | ✅ Working |
| MinIO API | http://localhost:9000 | ✅ Working |
| Admin Panel | http://localhost:3002 | ✅ Working (Local) |
| Shop Frontend | http://localhost:3003 | ✅ Working (Local) |

## 🔐 **Default Credentials**

- **MinIO**: minioadmin / minioadmin123
- **Admin Panel**: <EMAIL> / password
- **PostgreSQL**: ecommerce_owner / npg_aI0Dn8AMfbWj

## ❌ **Known Issues & Solutions**

### **Issue 1: Docker Frontend Build Failures**
**Problem**: Next.js builds failing in Docker due to environment variable issues

**Solution**: Use local development for frontend services
```powershell
# Instead of Docker, run locally:
cd admin-rest && yarn dev
cd shop && yarn dev:rest
```

### **Issue 2: MinIO TypeScript Errors**
**Problem**: MinIO SDK causing TypeScript compilation errors

**Current Solution**: Temporarily removed MinIO integration from API
**Future Fix**: Implement proper MinIO types or use require() instead of import

### **Issue 3: Traefik Windows Compatibility**
**Problem**: Traefik Docker socket mounting issues on Windows

**Solution**: Use simplified docker-compose without Traefik
```yaml
# Use docker-compose.simple.yml instead of docker-compose.yml
```

## 🔧 **File Upload Status**

### **Current Implementation**
- ✅ Upload endpoint: `/api/attachments`
- ✅ File validation (type, size)
- ✅ Mock file storage (returns placeholder URLs)
- ❌ MinIO integration (temporarily disabled due to build issues)

### **Upload Testing**
```bash
# Test file upload
curl -X POST http://localhost:5000/api/attachments \
  -F "attachment[]=@test-file.jpg"
```

## 🛠️ **Development Workflow**

### **Daily Development**
1. **Start Backend**: `docker-compose -f docker-compose.simple.yml up -d`
2. **Start Admin**: `cd admin-rest && yarn dev`
3. **Start Shop**: `cd shop && yarn dev:rest`
4. **Access Services**: Use URLs from table above

### **Reset Everything**
```powershell
# Stop all services
docker-compose -f docker-compose.simple.yml down

# Remove volumes (optional - will lose data)
docker-compose -f docker-compose.simple.yml down -v

# Restart fresh
docker-compose -f docker-compose.simple.yml up -d
docker-compose -f docker-compose.simple.yml exec api-rest node simple-seed.js
```

## 📊 **Service Health Checks**

### **Check Backend Services**
```powershell
# Check API health
curl http://localhost:5000/api

# Check database connection
docker-compose -f docker-compose.simple.yml exec postgres psql -U ecommerce_owner -d ecommerce -c "\dt"

# Check MinIO
curl http://localhost:9000/minio/health/live
```

### **Check Frontend Services**
- Admin Panel: http://localhost:3002 (should show login page)
- Shop: http://localhost:3003 (should show product catalog)

## 🎯 **Next Steps for Full Docker Setup**

1. **Fix MinIO Integration**
   - Resolve TypeScript compilation issues
   - Implement proper MinIO service integration

2. **Fix Frontend Docker Builds**
   - Resolve environment variable passing during build
   - Fix Next.js configuration issues

3. **Implement Traefik (Optional)**
   - Fix Windows Docker socket mounting
   - Configure proper routing

4. **Production Optimization**
   - Multi-stage builds optimization
   - Security hardening
   - Performance tuning

## 🆘 **Common Commands**

```powershell
# View logs
docker-compose -f docker-compose.simple.yml logs api-rest
docker-compose -f docker-compose.simple.yml logs postgres
docker-compose -f docker-compose.simple.yml logs minio

# Restart specific service
docker-compose -f docker-compose.simple.yml restart api-rest

# Execute commands in containers
docker-compose -f docker-compose.simple.yml exec api-rest bash
docker-compose -f docker-compose.simple.yml exec postgres psql -U ecommerce_owner -d ecommerce

# Check container status
docker ps
docker stats
```

## ✅ **Current Status Summary**

**✅ Working:**
- Backend API with database
- File upload endpoints (mock implementation)
- MinIO object storage
- Frontend services (local development)
- Database seeding
- API documentation

**🔧 In Progress:**
- Full Docker containerization
- MinIO file storage integration
- Traefik reverse proxy

**🎉 The system is functional for development and testing!**
