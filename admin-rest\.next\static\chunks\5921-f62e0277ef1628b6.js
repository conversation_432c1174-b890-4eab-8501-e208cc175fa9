"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5921],{88388:function(e,t,n){n.d(t,{ht:function(){return tabbable}});var r='input:not([inert]),select:not([inert]),textarea:not([inert]),a[href]:not([inert]),button:not([inert]),[tabindex]:not(slot):not([inert]),audio[controls]:not([inert]),video[controls]:not([inert]),[contenteditable]:not([contenteditable="false"]):not([inert]),details>summary:first-of-type:not([inert]),details:not([inert])',o="undefined"==typeof Element,i=o?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,a=!o&&Element.prototype.getRootNode?function(e){var t;return null==e?void 0:null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},isInert=function isInert(e,t){void 0===t&&(t=!0);var n,r=null==e?void 0:null===(n=e.getAttribute)||void 0===n?void 0:n.call(e,"inert");return""===r||"true"===r||t&&e&&isInert(e.parentNode)},isContentEditable=function(e){var t,n=null==e?void 0:null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n},getCandidates=function(e,t,n){if(isInert(e))return[];var o=Array.prototype.slice.apply(e.querySelectorAll(r));return t&&i.call(e,r)&&o.unshift(e),o=o.filter(n)},getCandidatesIteratively=function getCandidatesIteratively(e,t,n){for(var o=[],a=Array.from(e);a.length;){var u=a.shift();if(!isInert(u,!1)){if("SLOT"===u.tagName){var l=u.assignedElements(),s=getCandidatesIteratively(l.length?l:u.children,!0,n);n.flatten?o.push.apply(o,s):o.push({scopeParent:u,candidates:s})}else{i.call(u,r)&&n.filter(u)&&(t||!e.includes(u))&&o.push(u);var d=u.shadowRoot||"function"==typeof n.getShadowRoot&&n.getShadowRoot(u),c=!isInert(d,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(u));if(d&&c){var f=getCandidatesIteratively(!0===d?u.children:d.children,!0,n);n.flatten?o.push.apply(o,f):o.push({scopeParent:u,candidates:f})}else a.unshift.apply(a,u.children)}}}return o},hasTabIndex=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},getTabIndex=function(e){if(!e)throw Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||isContentEditable(e))&&!hasTabIndex(e)?0:e.tabIndex},getSortOrderTabIndex=function(e,t){var n=getTabIndex(e);return n<0&&t&&!hasTabIndex(e)?0:n},sortOrderedTabbables=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},isInput=function(e){return"INPUT"===e.tagName},getCheckedRadio=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},isTabbableRadio=function(e){if(!e.name)return!0;var t,n=e.form||a(e),queryRadios=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=queryRadios(window.CSS.escape(e.name));else try{t=queryRadios(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var r=getCheckedRadio(t,e.form);return!r||r===e},isNodeAttached=function(e){var t,n,r,o,i,u,l,s=e&&a(e),d=null===(t=s)||void 0===t?void 0:t.host,c=!1;if(s&&s!==e)for(c=!!(null!==(n=d)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(d)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!c&&d;)c=!!(null!==(u=d=null===(i=s=a(d))||void 0===i?void 0:i.host)&&void 0!==u&&null!==(l=u.ownerDocument)&&void 0!==l&&l.contains(d));return c},isZeroArea=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},isHidden=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=i.call(e,"details>summary:first-of-type")?e.parentElement:e;if(i.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return isZeroArea(e)}else{if("function"==typeof r){for(var u=e;e;){var l=e.parentElement,s=a(e);if(l&&!l.shadowRoot&&!0===r(l))return isZeroArea(e);e=e.assignedSlot?e.assignedSlot:l||s===e.ownerDocument?l:s.host}e=u}if(isNodeAttached(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},isDisabledFromFieldset=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!i.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1},isNodeMatchingSelectorTabbable=function(e,t){var n,r,o;return!(isInput(n=t)&&"radio"===n.type&&!isTabbableRadio(n)||0>getTabIndex(t))&&(r=e,!((o=t).disabled||isInert(o)||isInput(o)&&"hidden"===o.type||isHidden(o,r)||"DETAILS"===o.tagName&&Array.prototype.slice.apply(o.children).some(function(e){return"SUMMARY"===e.tagName})||isDisabledFromFieldset(o)))},isValidShadowRootTabbable=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!isNaN(t)||t>=0},sortByOrder=function sortByOrder(e){var t=[],n=[];return e.forEach(function(e,r){var o=!!e.scopeParent,i=o?e.scopeParent:e,a=getSortOrderTabIndex(i,o),u=o?sortByOrder(e.candidates):i;0===a?o?t.push.apply(t,u):t.push(i):n.push({documentOrder:r,tabIndex:a,item:e,isScope:o,content:u})}),n.sort(sortOrderedTabbables).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(t)},tabbable=function(e,t){return sortByOrder((t=t||{}).getShadowRoot?getCandidatesIteratively([e],t.includeContainer,{filter:isNodeMatchingSelectorTabbable.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:isValidShadowRootTabbable}):getCandidates(e,t.includeContainer,isNodeMatchingSelectorTabbable.bind(null,t)))}},97145:function(e,t,n){n.d(t,{AW:function(){return activeElement},G6:function(){return isSafari},MM:function(){return isReactEvent},Me:function(){return getDocument},Pe:function(){return isEventTargetWithin},U9:function(){return getTarget},V5:function(){return isMac},cr:function(){return isVirtualPointerEvent},ex:function(){return isRootElement},j7:function(){return isTypeableElement},r:function(){return isMouseLikePointerType},r3:function(){return contains}});var r=n(37317);function activeElement(e){let t=e.activeElement;for(;(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement)!=null;){var n;t=t.shadowRoot.activeElement}return t}function contains(e,t){if(!e||!t)return!1;let n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&(0,r.Zq)(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function getPlatform(){let e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function getUserAgent(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function isVirtualPointerEvent(e){return!getUserAgent().includes("jsdom/")&&(!isAndroid()&&0===e.width&&0===e.height||isAndroid()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function isSafari(){return/apple/i.test(navigator.vendor)}function isAndroid(){let e=/android/i;return e.test(getPlatform())||e.test(getUserAgent())}function isMac(){return getPlatform().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function isMouseLikePointerType(e,t){let n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function isReactEvent(e){return"nativeEvent"in e}function isRootElement(e){return e.matches("html,body")}function getDocument(e){return(null==e?void 0:e.ownerDocument)||document}function isEventTargetWithin(e,t){return null!=t&&("composedPath"in e?e.composedPath().includes(t):null!=e.target&&t.contains(e.target))}function getTarget(e){return"composedPath"in e?e.composedPath()[0]:e.target}function isTypeableElement(e){return(0,r.Re)(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}},82364:function(e,t,n){n.d(t,{RR:function(){return flip},YF:function(){return useFloating},cv:function(){return offset},uY:function(){return shift},x7:function(){return arrow}});var r=n(24750),o=n(67294),i=n(73935),a="undefined"!=typeof document?o.useLayoutEffect:function(){};function deepEqual(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!deepEqual(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!deepEqual(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function getDPR(e){if("undefined"==typeof window)return 1;let t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function roundByDPR(e,t){let n=getDPR(e);return Math.round(t*n)/n}function useLatestRef(e){let t=o.useRef(e);return a(()=>{t.current=e}),t}function useFloating(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:u=[],platform:l,elements:{reference:s,floating:d}={},transform:c=!0,whileElementsMounted:f,open:p}=e,[h,g]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,b]=o.useState(u);deepEqual(m,u)||b(u);let[v,y]=o.useState(null),[w,R]=o.useState(null),E=o.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),S=o.useCallback(e=>{e!==P.current&&(P.current=e,R(e))},[]),I=s||v,T=d||w,A=o.useRef(null),P=o.useRef(null),x=o.useRef(h),C=null!=f,N=useLatestRef(f),D=useLatestRef(l),M=useLatestRef(p),O=o.useCallback(()=>{if(!A.current||!P.current)return;let e={placement:t,strategy:n,middleware:m};D.current&&(e.platform=D.current),(0,r.oo)(A.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};k.current&&!deepEqual(x.current,t)&&(x.current=t,i.flushSync(()=>{g(t)}))})},[m,t,n,D,M]);a(()=>{!1===p&&x.current.isPositioned&&(x.current.isPositioned=!1,g(e=>({...e,isPositioned:!1})))},[p]);let k=o.useRef(!1);a(()=>(k.current=!0,()=>{k.current=!1}),[]),a(()=>{if(I&&(A.current=I),T&&(P.current=T),I&&T){if(N.current)return N.current(I,T,O);O()}},[I,T,O,N,C]);let L=o.useMemo(()=>({reference:A,floating:P,setReference:E,setFloating:S}),[E,S]),q=o.useMemo(()=>({reference:I,floating:T}),[I,T]),F=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!q.floating)return e;let t=roundByDPR(q.floating,h.x),r=roundByDPR(q.floating,h.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...getDPR(q.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,q.floating,h.x,h.y]);return o.useMemo(()=>({...h,update:O,refs:L,elements:q,floatingStyles:F}),[h,O,L,q,F])}let arrow$1=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.x7)({element:n.current,padding:o}).fn(t):{}:n?(0,r.x7)({element:n,padding:o}).fn(t):{}}}),offset=(e,t)=>({...(0,r.cv)(e),options:[e,t]}),shift=(e,t)=>({...(0,r.uY)(e),options:[e,t]}),flip=(e,t)=>({...(0,r.RR)(e),options:[e,t]}),arrow=(e,t)=>({...arrow$1(e),options:[e,t]})}}]);