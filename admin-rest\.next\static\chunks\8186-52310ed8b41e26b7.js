"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8186],{76785:function(e,t,s){s.d(t,{E:function(){return MessageAvatarPlaceholderIcon}});var l=s(85893);let MessageAvatarPlaceholderIcon=e=>{let{color:t="currentColor",...s}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 40 40",width:"1em",height:"1em",fill:"none",...s,children:(0,l.jsx)("path",{fill:t,fillRule:"evenodd",d:"M20 40c11.046 0 20-8.954 20-20S31.046 0 20 0 0 8.954 0 20s8.954 20 20 20Zm1.116-28.32 7.204 7.204c.89.89.909 2.317.04 3.186l-6.29 6.29c-.869.869-2.295.85-3.186-.04l-7.204-7.204a2.298 2.298 0 0 1-.642-2.042l.994-5.297a2.177 2.177 0 0 1 1.745-1.744l5.297-.995a2.298 2.298 0 0 1 2.042.642Zm-6.123 5.672a1.668 1.668 0 1 0 2.359-2.36 1.668 1.668 0 0 0-2.36 2.36Z",clipRule:"evenodd"})})}},86599:function(e,t,s){s.d(t,{Cq:function(){return useConversationQuery},sw:function(){return useConversationsQuery},As:function(){return useCreateConversations},uX:function(){return useMessageSeen},kY:function(){return useMessagesQuery},$3:function(){return useSendMessage}});var l=s(11163),n=s(88767),i=s(22920),r=s(5233),a=s(97514),u=s(47869),h=s(28597),o=s(55191),c=s(3737);let d={...(0,o.h)(u.P.CONVERSIONS),create(e){let{shop_id:t,via:s}=e;return c.eN.post(u.P.CONVERSIONS,{shop_id:t,via:s})},getMessage(e){let{slug:t,...s}=e;return c.eN.get("".concat(u.P.MESSAGE,"/").concat(t),{searchJoin:"and",...s})},getConversion(e){let{id:t}=e;return c.eN.get("".concat(u.P.CONVERSIONS,"/").concat(t))},messageCreate(e){let{id:t,...s}=e;return c.eN.post("".concat(u.P.MESSAGE,"/").concat(t),s)},messageSeen(e){let{id:t}=e;return c.eN.post("".concat(u.P.MESSAGE_SEEN,"/").concat(t),t)},allConversation:e=>c.eN.get(u.P.CONVERSIONS,e)};var v=s(75814),p=s(16203);let useConversationsQuery=e=>{var t,s;let{data:l,isLoading:i,error:r,refetch:a,fetchNextPage:o,hasNextPage:c,isFetching:v,isSuccess:p,isFetchingNextPage:x}=(0,n.useInfiniteQuery)([u.P.CONVERSIONS,e],e=>{let{queryKey:t,pageParam:s}=e;return d.allConversation(Object.assign({},t[1],s))},{getNextPageParam:e=>{let{current_page:t,last_page:s}=e;return s>t&&{page:t+1}}});return{conversations:null!==(s=null==l?void 0:null===(t=l.pages)||void 0===t?void 0:t.flatMap(e=>e.data))&&void 0!==s?s:[],paginatorInfo:Array.isArray(null==l?void 0:l.pages)?(0,h.Q)(null==l?void 0:l.pages[l.pages.length-1]):null,loading:i,error:r,isFetching:v,refetch:a,isSuccess:p,isLoadingMore:x,loadMore:function(){c&&o()},hasMore:!!c}},useCreateConversations=()=>{let{t:e}=(0,r.$G)(),t=(0,l.useRouter)(),{closeModal:s}=(0,v.SO)(),h=(0,n.useQueryClient)(),{permissions:o}=(0,p.WA)(),c=(0,p.Ft)(p.M$,o);return(0,n.useMutation)(d.create,{onSuccess:l=>{if(null==l?void 0:l.id){var n,r;let u=c?null===a.Z||void 0===a.Z?void 0:null===(n=a.Z.message)||void 0===n?void 0:n.details(null==l?void 0:l.id):null===a.Z||void 0===a.Z?void 0:null===(r=a.Z.shopMessage)||void 0===r?void 0:r.details(null==l?void 0:l.id);i.Am.success(e("common:successfully-created")),t.push("".concat(u)),s()}else i.Am.error("Something went wrong!")},onSettled:()=>{h.invalidateQueries(u.P.MESSAGE),h.invalidateQueries(u.P.CONVERSIONS)}})},useMessagesQuery=e=>{var t,s;let{data:l,isLoading:i,error:r,refetch:a,fetchNextPage:o,hasNextPage:c,isFetching:v,isSuccess:p,isFetchingNextPage:x}=(0,n.useInfiniteQuery)([u.P.MESSAGE,e],e=>{let{queryKey:t,pageParam:s}=e;return d.getMessage(Object.assign({},t[1],s))},{getNextPageParam:e=>{let{current_page:t,last_page:s}=e;return s>t&&{page:t+1}}});return{messages:null!==(s=null==l?void 0:null===(t=l.pages)||void 0===t?void 0:t.flatMap(e=>e.data))&&void 0!==s?s:[],paginatorInfo:Array.isArray(null==l?void 0:l.pages)?(0,h.Q)(null==l?void 0:l.pages[l.pages.length-1]):null,loading:i,error:r,isFetching:v,refetch:a,isSuccess:p,isLoadingMore:x,loadMore:function(){c&&o()},hasMore:!!c}},useConversationQuery=e=>{let{id:t}=e,{data:s,error:l,isLoading:i,isFetching:r}=(0,n.useQuery)([u.P.CONVERSIONS,t],()=>d.getConversion({id:t}),{keepPreviousData:!0});return{data:null!=s?s:[],error:l,loading:i,isFetching:r}},useSendMessage=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(d.messageCreate,{onSuccess:()=>{i.Am.success(e("common:text-message-sent"))},onSettled:()=>{t.invalidateQueries(u.P.MESSAGE),t.invalidateQueries(u.P.CONVERSIONS)}})},useMessageSeen=()=>{let{t:e}=(0,r.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(d.messageSeen,{onSettled:()=>{t.invalidateQueries(u.P.MESSAGE),t.invalidateQueries(u.P.CONVERSIONS)}})}},30042:function(e,t,s){s.d(t,{bg:function(){return useApproveShopMutation},TC:function(){return useCreateShopMutation},mj:function(){return useDisApproveShopMutation},T3:function(){return useInActiveShopsQuery},DZ:function(){return useShopQuery},uL:function(){return useShopsQuery},_3:function(){return useTransferShopOwnershipMutation},D9:function(){return useUpdateShopMutation}});var l=s(93345),n=s(97514),i=s(47869),r=s(16203),a=s(28597),u=s(5233),h=s(11163),o=s(88767),c=s(22920),d=s(3737),v=s(55191);let p={...(0,v.h)(i.P.SHOPS),get(e){let{slug:t}=e;return d.eN.get("".concat(i.P.SHOPS,"/").concat(t))},paginated:e=>{let{name:t,...s}=e;return d.eN.get(i.P.SHOPS,{searchJoin:"and",...s,search:d.eN.formatSearchParams({name:t})})},newOrInActiveShops:e=>{let{is_active:t,name:s,...l}=e;return d.eN.get(i.P.NEW_OR_INACTIVE_SHOPS,{searchJoin:"and",is_active:t,name:s,...l,search:d.eN.formatSearchParams({is_active:t,name:s})})},approve:e=>d.eN.post(i.P.APPROVE_SHOP,e),disapprove:e=>d.eN.post(i.P.DISAPPROVE_SHOP,e),transferShopOwnership:e=>d.eN.post(i.P.TRANSFER_SHOP_OWNERSHIP,e)},useApproveShopMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.approve,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.SHOPS)}})},useDisApproveShopMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.disapprove,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.SHOPS)}})},useCreateShopMutation=()=>{let e=(0,o.useQueryClient)(),t=(0,h.useRouter)();return(0,o.useMutation)(p.create,{onSuccess:()=>{let{permissions:e}=(0,r.WA)();if((0,r.Ft)(r.M$,e))return t.push(n.Z.adminMyShops);t.push(n.Z.dashboard)},onSettled:()=>{e.invalidateQueries(i.P.SHOPS)}})},useUpdateShopMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,h.useRouter)(),s=(0,o.useQueryClient)();return(0,o.useMutation)(p.update,{onSuccess:async s=>{await t.push("/".concat(null==s?void 0:s.slug,"/edit"),void 0,{locale:l.Config.defaultLanguage}),c.Am.success(e("common:successfully-updated"))},onSettled:()=>{s.invalidateQueries(i.P.SHOPS)}})},useTransferShopOwnershipMutation=()=>{let{t:e}=(0,u.$G)(),t=(0,o.useQueryClient)();return(0,o.useMutation)(p.transferShopOwnership,{onSuccess:t=>{var s;c.Am.success("".concat(e("common:successfully-transferred")).concat(null===(s=t.owner)||void 0===s?void 0:s.name))},onSettled:()=>{t.invalidateQueries(i.P.SHOPS)}})},useShopQuery=(e,t)=>{let{slug:s}=e;return(0,o.useQuery)([i.P.SHOPS,{slug:s}],()=>p.get({slug:s}),t)},useShopsQuery=e=>{var t;let{data:s,error:l,isLoading:n}=(0,o.useQuery)([i.P.SHOPS,e],e=>{let{queryKey:t,pageParam:s}=e;return p.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{shops:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(s),error:l,loading:n}},useInActiveShopsQuery=e=>{var t;let{data:s,error:l,isLoading:n}=(0,o.useQuery)([i.P.NEW_OR_INACTIVE_SHOPS,e],e=>{let{queryKey:t,pageParam:s}=e;return p.newOrInActiveShops(Object.assign({},t[1],s))},{keepPreviousData:!0});return{shops:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(s),error:l,loading:n}}},76518:function(e,t,s){s.d(t,{X:function(){return r},S:function(){return useIsRTL}});var l=s(85893),n=s(11163);let i=["ar","he"];function useIsRTL(){let{locale:e}=(0,n.useRouter)();return e&&i.includes(e)?{isRTL:!0,alignLeft:"right",alignRight:"left"}:{isRTL:!1,alignLeft:"left",alignRight:"right"}}let r=[{id:"ar",name:"عربى",value:"ar",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:s,viewBox:"0 0 512 512",children:[(0,l.jsx)("mask",{id:"a",children:(0,l.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,l.jsxs)("g",{mask:"url(#a)",children:[(0,l.jsx)("path",{fill:"#496e2d",d:"M0 0h512v512H0z"}),(0,l.jsxs)("g",{fill:"#eee",children:[(0,l.jsx)("path",{d:"M144.7 306c0 18.5 15 33.5 33.4 33.5h100.2a27.8 27.8 0 0 0 27.8 27.8h33.4a27.8 27.8 0 0 0 27.8-27.8V306zm225.4-161.3v78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9H370zm-239.3 78c0 12.2-10 22.2-22.3 22.2v33.4c30.7 0 55.7-25 55.7-55.7v-77.9h-33.4z"}),(0,l.jsx)("path",{d:"M320 144.7h33.4v78H320zm-50 44.5a5.6 5.6 0 0 1-11.2 0v-44.5h-33.4v44.5a5.6 5.6 0 0 1-11.1 0v-44.5h-33.4v44.5a39 39 0 0 0 39 39 38.7 38.7 0 0 0 22.2-7 38.7 38.7 0 0 0 22.2 7c1.7 0 3.4-.1 5-.3a22.3 22.3 0 0 1-21.6 17v33.4c30.6 0 55.6-25 55.6-55.7v-77.9H270z"}),(0,l.jsx)("path",{d:"M180.9 244.9h50v33.4h-50z"})]})]})]})},{width:"28px",height:"28px"})},{id:"zh",name:"中国人",value:"zh",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:s,viewBox:"0 0 512 512",children:[(0,l.jsx)("mask",{id:"a",children:(0,l.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,l.jsxs)("g",{mask:"url(#a)",children:[(0,l.jsx)("path",{fill:"#d80027",d:"M0 0h512v512H0z"}),(0,l.jsx)("path",{fill:"#ffda44",d:"m140.1 155.8 22.1 68h71.5l-57.8 42.1 22.1 68-57.9-42-57.9 42 22.2-68-57.9-42.1H118zm163.4 240.7-16.9-20.8-25 9.7 14.5-22.5-16.9-20.9 25.9 6.9 14.6-22.5 1.4 26.8 26 6.9-25.1 9.6zm33.6-61 8-25.6-21.9-15.5 26.8-.4 7.9-25.6 8.7 25.4 26.8-.3-21.5 16 8.6 25.4-21.9-15.5zm45.3-147.6L370.6 212l19.2 18.7-26.5-3.8-11.8 24-4.6-26.4-26.6-3.8 23.8-12.5-4.6-26.5 19.2 18.7zm-78.2-73-2 26.7 24.9 10.1-26.1 6.4-1.9 26.8-14.1-22.8-26.1 6.4 17.3-20.5-14.2-22.7 24.9 10.1z"})]})]})},{width:"28px",height:"28px"})},{id:"en",name:"English",value:"en",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:s,viewBox:"0 0 512 512",children:[(0,l.jsx)("mask",{id:"a",children:(0,l.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,l.jsxs)("g",{mask:"url(#a)",children:[(0,l.jsx)("path",{fill:"#eee",d:"M256 0h256v64l-32 32 32 32v64l-32 32 32 32v64l-32 32 32 32v64l-256 32L0 448v-64l32-32-32-32v-64z"}),(0,l.jsx)("path",{fill:"#d80027",d:"M224 64h288v64H224Zm0 128h288v64H256ZM0 320h512v64H0Zm0 128h512v64H0Z"}),(0,l.jsx)("path",{fill:"#0052b4",d:"M0 0h256v256H0Z"}),(0,l.jsx)("path",{fill:"#eee",d:"m187 243 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67zm162-81 57-41h-70l57 41-22-67zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Zm162-82 57-41h-70l57 41-22-67Zm-81 0 57-41H93l57 41-22-67zm-81 0 57-41H12l57 41-22-67Z"})]})]})},{width:"28px",height:"28px"})},{id:"de",name:"Deutsch",value:"de",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:s,viewBox:"0 0 512 512",children:[(0,l.jsx)("mask",{id:"a",children:(0,l.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,l.jsxs)("g",{mask:"url(#a)",children:[(0,l.jsx)("path",{fill:"#ffda44",d:"m0 345 256.7-25.5L512 345v167H0z"}),(0,l.jsx)("path",{fill:"#d80027",d:"m0 167 255-23 257 23v178H0z"}),(0,l.jsx)("path",{fill:"#333",d:"M0 0h512v167H0z"})]})]})},{width:"28px",height:"28px"})},{id:"he",name:"rעברית",value:"he",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 480",width:t,height:s,children:[(0,l.jsx)("defs",{children:(0,l.jsx)("clipPath",{id:"il-a",children:(0,l.jsx)("path",{fillOpacity:".7",d:"M-87.6 0H595v512H-87.6z"})})}),(0,l.jsxs)("g",{fillRule:"evenodd",clipPath:"url(#il-a)",transform:"translate(82.1) scale(.94)",children:[(0,l.jsx)("path",{fill:"#fff",d:"M619.4 512H-112V0h731.4z"}),(0,l.jsx)("path",{fill:"#00c",d:"M619.4 115.2H-112V48h731.4zm0 350.5H-112v-67.2h731.4zm-483-275l110.1 191.6L359 191.6l-222.6-.8z"}),(0,l.jsx)("path",{fill:"#fff",d:"M225.8 317.8l20.9 35.5 21.4-35.3-42.4-.2z"}),(0,l.jsx)("path",{fill:"#00c",d:"M136 320.6L246.2 129l112.4 190.8-222.6.8z"}),(0,l.jsx)("path",{fill:"#fff",d:"M225.8 191.6l20.9-35.5 21.4 35.4-42.4.1zM182 271.1l-21.7 36 41-.1-19.3-36zm-21.3-66.5l41.2.3-19.8 36.3-21.4-36.6zm151.2 67l20.9 35.5-41.7-.5 20.8-35zm20.5-67l-41.2.3 19.8 36.3 21.4-36.6zm-114.3 0L189.7 256l28.8 50.3 52.8 1.2 32-51.5-29.6-52-55.6.5z"})]})]})},{width:"28px",height:"28px"})},{id:"es",name:"Espa\xf1ol",value:"es",icon:(0,l.jsx)(e=>{let{width:t="640px",height:s="480px"}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:s,viewBox:"0 0 512 512",children:[(0,l.jsx)("mask",{id:"a",children:(0,l.jsx)("circle",{cx:"256",cy:"256",r:"256",fill:"#fff"})}),(0,l.jsxs)("g",{mask:"url(#a)",children:[(0,l.jsx)("path",{fill:"#ffda44",d:"m0 128 256-32 256 32v256l-256 32L0 384Z"}),(0,l.jsx)("path",{fill:"#d80027",d:"M0 0h512v128H0zm0 384h512v128H0z"}),(0,l.jsxs)("g",{fill:"#eee",children:[(0,l.jsx)("path",{d:"M144 304h-16v-80h16zm128 0h16v-80h-16z"}),(0,l.jsx)("ellipse",{cx:"208",cy:"296",rx:"48",ry:"32"})]}),(0,l.jsxs)("g",{fill:"#d80027",children:[(0,l.jsx)("rect",{width:"16",height:"24",x:"128",y:"192",rx:"8"}),(0,l.jsx)("rect",{width:"16",height:"24",x:"272",y:"192",rx:"8"}),(0,l.jsx)("path",{d:"M208 272v24a24 24 0 0 0 24 24 24 24 0 0 0 24-24v-24h-24z"})]}),(0,l.jsx)("rect",{width:"32",height:"16",x:"120",y:"208",fill:"#ff9811",ry:"8"}),(0,l.jsx)("rect",{width:"32",height:"16",x:"264",y:"208",fill:"#ff9811",ry:"8"}),(0,l.jsx)("rect",{width:"32",height:"16",x:"120",y:"304",fill:"#ff9811",rx:"8"}),(0,l.jsx)("rect",{width:"32",height:"16",x:"264",y:"304",fill:"#ff9811",rx:"8"}),(0,l.jsx)("path",{fill:"#ff9811",d:"M160 272v24c0 8 4 14 9 19l5-6 5 10a21 21 0 0 0 10 0l5-10 5 6c6-5 9-11 9-19v-24h-9l-5 8-5-8h-10l-5 8-5-8z"}),(0,l.jsx)("path",{d:"M122 252h172m-172 24h28m116 0h28"}),(0,l.jsx)("path",{fill:"#d80027",d:"M122 248a4 4 0 0 0-4 4 4 4 0 0 0 4 4h172a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm0 24a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4zm144 0a4 4 0 0 0-4 4 4 4 0 0 0 4 4h28a4 4 0 0 0 4-4 4 4 0 0 0-4-4z"}),(0,l.jsx)("path",{fill:"#eee",d:"M196 168c-7 0-13 5-15 11l-5-1c-9 0-16 7-16 16s7 16 16 16c7 0 13-4 15-11a16 16 0 0 0 17-4 16 16 0 0 0 17 4 16 16 0 1 0 10-20 16 16 0 0 0-27-5c-3-4-7-6-12-6zm0 8c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm24 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-8 0-4 4-8 8-8zm-44 10 4 1 4 8c0 4-4 7-8 7s-8-3-8-8c0-4 4-8 8-8zm64 0c5 0 8 4 8 8 0 5-3 8-8 8-4 0-8-3-8-7l4-8z"}),(0,l.jsx)("path",{fill:"none",d:"M220 284v12c0 7 5 12 12 12s12-5 12-12v-12z"}),(0,l.jsx)("path",{fill:"#ff9811",d:"M200 160h16v32h-16z"}),(0,l.jsx)("path",{fill:"#eee",d:"M208 224h48v48h-48z"}),(0,l.jsx)("path",{fill:"#d80027",d:"m248 208-8 8h-64l-8-8c0-13 18-24 40-24s40 11 40 24zm-88 16h48v48h-48z"}),(0,l.jsx)("rect",{width:"20",height:"32",x:"222",y:"232",fill:"#d80027",rx:"10",ry:"10"}),(0,l.jsx)("path",{fill:"#ff9811",d:"M168 232v8h8v16h-8v8h32v-8h-8v-16h8v-8zm8-16h64v8h-64z"}),(0,l.jsxs)("g",{fill:"#ffda44",children:[(0,l.jsx)("circle",{cx:"186",cy:"202",r:"6"}),(0,l.jsx)("circle",{cx:"208",cy:"202",r:"6"}),(0,l.jsx)("circle",{cx:"230",cy:"202",r:"6"})]}),(0,l.jsx)("path",{fill:"#d80027",d:"M169 272v43a24 24 0 0 0 10 4v-47h-10zm20 0v47a24 24 0 0 0 10-4v-43h-10z"}),(0,l.jsxs)("g",{fill:"#338af3",children:[(0,l.jsx)("circle",{cx:"208",cy:"272",r:"16"}),(0,l.jsx)("rect",{width:"32",height:"16",x:"264",y:"320",ry:"8"}),(0,l.jsx)("rect",{width:"32",height:"16",x:"120",y:"320",ry:"8"})]})]})]})},{width:"28px",height:"28px"})}]}}]);