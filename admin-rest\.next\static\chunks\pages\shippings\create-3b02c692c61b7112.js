(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6415,2036],{34398:function(e,n,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shippings/create",function(){return r(31952)}])},31952:function(e,n,r){"use strict";r.r(n),r.d(n,{__N_SSG:function(){return u},default:function(){return CreateShippingPage}});var t=r(85893),i=r(97670),a=r(92941),s=r(5233),u=!0;function CreateShippingPage(){let{t:e}=(0,s.$G)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:form-title-create-shipping")})}),(0,t.jsx)(a.Z,{})]})}CreateShippingPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,9494,5535,8186,1285,1631,8118,9774,2888,179],function(){return e(e.s=34398)}),_N_E=e.O()}]);