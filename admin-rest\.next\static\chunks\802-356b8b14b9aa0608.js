"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[802],{802:function(e,t,r){r.d(t,{Z:function(){return CreateOrUpdateAttributeForm}});var a=r(85893),l=r(33e3),s=r(87536),o=r(60802),n=r(80602),i=r(92072),u=r(11163),d=r(5233),c=r(30042),m=r(67294),p=r(21587),b=r(66261),f=r(89664),x=r(47533),h=r(16310);let v=h.Ry().shape({name:h.Z_().required("form:error-attribute-name-required"),values:h.IX().of(h.Ry().shape({value:h.Z_().required("form:error-value-required"),meta:h.Z_().required("form:error-meta-required")}))});var g=r(22220);function CreateOrUpdateAttributeForm(e){var t;let{initialValues:r}=e,h=(0,u.useRouter)(),[w,y]=(0,m.useState)(null),{query:{shop:N}}=h,{t:j}=(0,d.$G)(),{data:T}=(0,c.DZ)({slug:N},{enabled:!!N}),A=null==T?void 0:T.id,{register:Z,handleSubmit:I,control:C,formState:{errors:S}}=(0,s.cI)({defaultValues:r||{name:"",values:[]},resolver:(0,x.X)(v)}),{fields:k,append:E,remove:M}=(0,s.Dq)({control:C,name:"values"}),{mutate:R,isLoading:z}=(0,f.gL)(),{mutate:q,isLoading:U}=(0,f.uT)();return(0,a.jsxs)(a.Fragment,{children:[w?(0,a.jsx)(p.Z,{message:j("common:".concat(w)),variant:"error",closeable:!0,className:"mt-5",onClose:()=>y(null)}):null,(0,a.jsxs)("form",{onSubmit:I(e=>{r&&r.translated_languages.includes(h.locale)?q({id:r.id,name:e.name,shop_id:Number(null==r?void 0:r.shop_id),values:e.values.map(e=>{let{id:t,value:r,meta:a}=e;return{language:h.locale,id:Number(t),value:r,meta:a}})}):R({language:h.locale,name:e.name,shop_id:A?Number(A):Number(null==r?void 0:r.shop_id),values:null==e?void 0:e.values.map(e=>{let{id:t,value:r,meta:a}=e;return{language:h.locale,value:r,meta:a}}),...(null==r?void 0:r.slug)&&{slug:r.slug}},{onError:e=>{var t,r;y(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message),b.NY.scrollToTop()}})}),children:[(0,a.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,a.jsx)(n.Z,{title:j("common:attribute"),details:"".concat(r?j("form:item-description-update"):j("form:item-description-add")," ").concat(j("form:form-description-attribute-name")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,a.jsx)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,a.jsx)(l.Z,{label:j("form:input-label-name"),...Z("name",{required:"form:error-name-required"}),error:j(null===(t=S.name)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5"})})]}),(0,a.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,a.jsx)(n.Z,{title:j("common:attribute-values"),details:"".concat(r?j("form:item-description-update"):j("form:item-description-add")," ").concat(j("form:form-description-attribute-value")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,a.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,a.jsx)("div",{children:k.map((e,t)=>{var r,s,o,n,i,u;return(0,a.jsx)("div",{className:"py-5 border-b border-dashed border-border-200 last:border-0 md:py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-5",children:[(0,a.jsx)(l.Z,{className:"sm:col-span-2",label:j("form:input-label-value"),variant:"outline",...Z("values.".concat(t,".value")),defaultValue:e.value,error:j(null==S?void 0:null===(o=S.values)||void 0===o?void 0:null===(s=o[t])||void 0===s?void 0:null===(r=s.value)||void 0===r?void 0:r.message)}),(0,a.jsx)(l.Z,{className:"sm:col-span-2",label:j("form:input-label-meta"),variant:"outline",...Z("values.".concat(t,".meta")),defaultValue:e.meta,error:j(null==S?void 0:null===(u=S.values)||void 0===u?void 0:null===(i=u[t])||void 0===i?void 0:null===(n=i.meta)||void 0===n?void 0:n.message)}),(0,a.jsx)("button",{onClick:()=>M(t),type:"button",className:"text-sm text-red-500 transition-colors duration-200 hover:text-red-700 focus:outline-none sm:col-span-1 sm:mt-4",children:j("form:button-label-remove")})]})},e.id)})}),(0,a.jsx)(o.Z,{type:"button",onClick:()=>E({value:"",meta:""}),className:"w-full sm:w-auto",children:j("form:button-label-add-value")})]})]}),(0,a.jsx)(g.Z,{className:"z-0",children:(0,a.jsxs)("div",{className:"text-end",children:[r&&(0,a.jsx)(o.Z,{variant:"outline",onClick:h.back,className:"text-sm me-4 md:text-base",type:"button",children:j("form:button-label-back")}),(0,a.jsxs)(o.Z,{loading:z||U,disabled:z||U,className:"text-sm md:text-base",children:[r?j("form:item-description-update"):j("form:item-description-add")," ",j("common:attribute")]})]})})]})]})}},92072:function(e,t,r){var a=r(85893),l=r(93967),s=r.n(l),o=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,o.m6)(s()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},86779:function(e,t,r){r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var a=r(85893);let InfoIcon=e=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,a.jsx)("g",{children:(0,a.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,a.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,a.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,a.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,a.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},80602:function(e,t,r){var a=r(85893);t.Z=e=>{let{title:t,details:r,className:l,...s}=e;return(0,a.jsxs)("div",{className:l,...s,children:[t&&(0,a.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),r&&(0,a.jsx)("p",{className:"text-sm text-body",children:r})]})}},33e3:function(e,t,r){var a=r(85893),l=r(71611),s=r(93967),o=r.n(s),n=r(67294),i=r(98388);let u={small:"text-sm h-10",medium:"h-12",big:"h-14"},d=n.forwardRef((e,t)=>{let{className:r,label:s,note:n,name:d,error:c,children:m,variant:p="normal",dimension:b="medium",shadow:f=!1,type:x="text",inputClassName:h,disabled:v,showLabel:g=!0,required:w,toolTipText:y,labelClassName:N,...j}=e,T=o()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===p,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===p,"border border-border-base focus:border-accent":"outline"===p},{"focus:shadow":f},u[b],h),A="number"===x&&v?"number-disable":"";return(0,a.jsxs)("div",{className:(0,i.m6)(r),children:[g||s?(0,a.jsx)(l.Z,{htmlFor:d,toolTipText:y,label:s,required:w,className:N}):"",(0,a.jsx)("input",{id:d,name:d,type:x,ref:t,className:(0,i.m6)(o()(v?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(A," select-none"):"",T)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:v,"aria-invalid":c?"true":"false",...j}),n&&(0,a.jsx)("p",{className:"mt-2 text-xs text-body",children:n}),c&&(0,a.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:c})]})});d.displayName="Input",t.Z=d},23091:function(e,t,r){var a=r(85893),l=r(93967),s=r.n(l),o=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,a.jsx)("label",{className:(0,o.m6)(s()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},22220:function(e,t,r){var a=r(85893),l=r(93967),s=r.n(l),o=r(98388);t.Z=e=>{let{children:t,className:r,...l}=e;return(0,a.jsx)("div",{className:(0,o.m6)(s()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...l,children:t})}},71611:function(e,t,r){var a=r(85893),l=r(86779),s=r(71943),o=r(23091),n=r(98388);t.Z=e=>{let{className:t,required:r,label:i,toolTipText:u,htmlFor:d}=e;return(0,a.jsxs)(o.Z,{className:(0,n.m6)(t),htmlFor:d,children:[i,r?(0,a.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",u?(0,a.jsx)(s.u,{content:u,children:(0,a.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,a.jsx)(l.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){r.d(t,{u:function(){return Tooltip}});var a=r(85893),l=r(67294),s=r(93075),o=r(82364),n=r(24750),i=r(93967),u=r.n(i),d=r(67421),c=r(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:i=8,animation:b="zoomIn",placement:f="top",size:x="md",rounded:h="DEFAULT",shadow:v="md",color:g="default",className:w,arrowClassName:y,showArrow:N=!0}=e,[j,T]=(0,l.useState)(!1),A=(0,l.useRef)(null),{t:Z}=(0,d.$G)(),{x:I,y:C,refs:S,strategy:k,context:E}=(0,s.YF)({placement:f,open:j,onOpenChange:T,middleware:[(0,o.x7)({element:A}),(0,o.cv)(i),(0,o.RR)(),(0,o.uY)({padding:8})],whileElementsMounted:n.Me}),{getReferenceProps:M,getFloatingProps:R}=(0,s.NI)([(0,s.XI)(E),(0,s.KK)(E),(0,s.qs)(E,{role:"tooltip"}),(0,s.bQ)(E)]),{isMounted:z,styles:q}=(0,s.Y_)(E,{duration:{open:150,close:150},...p[b]});return(0,a.jsxs)(a.Fragment,{children:[(0,l.cloneElement)(t,M({ref:S.setReference,...t.props})),(z||j)&&(0,a.jsx)(s.ll,{children:(0,a.jsxs)("div",{role:"tooltip",ref:S.setFloating,className:(0,c.m6)(u()(m.base,m.size[x],m.rounded[h],m.variant.solid.base,m.variant.solid.color[g],m.shadow[v],w)),style:{position:k,top:null!=C?C:0,left:null!=I?I:0,...q},...R(),children:[Z("".concat(r)),N&&(0,a.jsx)(s.Y$,{ref:A,context:E,className:u()(m.arrow.color[g],y),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},89664:function(e,t,r){r.d(t,{I1:function(){return useAttributeQuery},OO:function(){return useAttributesQuery},gL:function(){return useCreateAttributeMutation},Oi:function(){return useDeleteAttributeMutation},uT:function(){return useUpdateAttributeMutation}});var a=r(11163),l=r.n(a),s=r(88767),o=r(22920),n=r(5233),i=r(97514),u=r(47869),d=r(55191),c=r(3737);let m={...(0,d.h)(u.P.ATTRIBUTES),paginated:e=>{let{type:t,name:r,shop_id:a,...l}=e;return c.eN.get(u.P.ATTRIBUTES,{searchJoin:"and",...l,search:c.eN.formatSearchParams({type:t,name:r,shop_id:a})})},all:e=>{let{type:t,name:r,shop_id:a,...l}=e;return c.eN.get(u.P.ATTRIBUTES,{searchJoin:"and",...l,search:c.eN.formatSearchParams({type:t,name:r,shop_id:a})})}};var p=r(93345);let useCreateAttributeMutation=()=>{let e=(0,a.useRouter)(),t=(0,s.useQueryClient)(),{t:r}=(0,n.$G)();return(0,s.useMutation)(m.create,{onSuccess:()=>{let t=e.query.shop?"/".concat(e.query.shop).concat(i.Z.attribute.list):i.Z.attribute.list;l().push(t,void 0,{locale:p.Config.defaultLanguage}),o.Am.success(r("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(u.P.ATTRIBUTES)}})},useUpdateAttributeMutation=()=>{let{t:e}=(0,n.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(m.update,{onSuccess:()=>{o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(u.P.ATTRIBUTES)}})},useDeleteAttributeMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,n.$G)();return(0,s.useMutation)(m.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(u.P.ATTRIBUTES)}})},useAttributeQuery=e=>{let{slug:t,language:r}=e;return(0,s.useQuery)([u.P.ATTRIBUTES,{slug:t,language:r}],()=>m.get({slug:t,language:r}))},useAttributesQuery=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:r,error:a,isLoading:l}=(0,s.useQuery)([u.P.ATTRIBUTES,e],e=>{let{queryKey:t,pageParam:r}=e;return m.all(Object.assign({},t[1],r))},{keepPreviousData:!0,...t});return{attributes:null!=r?r:[],error:a,loading:l}}}}]);