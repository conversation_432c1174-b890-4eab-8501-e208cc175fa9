"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_reviews_review-image-modal_tsx";
exports.ids = ["src_components_reviews_review-image-modal_tsx"];
exports.modules = {

/***/ "./src/components/icons/chevron-left.tsx":
/*!***********************************************!*\
  !*** ./src/components/icons/chevron-left.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeft: () => (/* binding */ ChevronLeft),\n/* harmony export */   IosGhostArrowLeft: () => (/* binding */ IosGhostArrowLeft)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ChevronLeft = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 19l-7-7 7-7\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\nconst IosGhostArrowLeft = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 14 10\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M7 3H4.5V.5a.5.5 0 00-.853-.354l-3 3a.5.5 0 000 .708l3 3A.5.5 0 004.5 6.5V4H7a5.506 5.506 0 015.5 ******* 0 001 0A6.507 6.507 0 007 3zM3.5 5.293L1.707 3.5 3.5 1.707v3.586z\",\n            fill: \"currentColor\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-left.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/chevron-left.tsx\n");

/***/ }),

/***/ "./src/components/icons/chevron-right.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/chevron-right.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronRight: () => (/* binding */ ChevronRight),\n/* harmony export */   ChevronRightNew: () => (/* binding */ ChevronRightNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ChevronRight = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 5l7 7-7 7\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\nconst ChevronRightNew = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 6 10\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M5.849 5c0 .18-.069.358-.205.495l-4.3 4.3a.7.7 0 11-.99-.99L4.157 5 .354 1.196a.7.7 0 01.99-.99l4.3 4.299A.698.698 0 015.849 5z\",\n            fill: \"currentColor\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\chevron-right.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/chevron-right.tsx\n");

/***/ }),

/***/ "./src/components/reviews/review-image-modal.tsx":
/*!*******************************************************!*\
  !*** ./src/components/reviews/review-image-modal.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _lib_locals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/locals */ \"./src/lib/locals.tsx\");\n/* harmony import */ var _components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/chevron-left */ \"./src/components/icons/chevron-left.tsx\");\n/* harmony import */ var _components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/chevron-right */ \"./src/components/icons/chevron-right.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/slider */ \"./src/components/ui/slider.tsx\");\n/* harmony import */ var _lib_hooks_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/use-swiper-ref */ \"./src/lib/hooks/use-swiper-ref.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__]);\n_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nconst swiperParams = {\n    slidesPerView: 1,\n    spaceBetween: 0\n};\nconst ReviewImageModal = ()=>{\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { isRTL } = (0,_lib_locals__WEBPACK_IMPORTED_MODULE_3__.useIsRTL)();\n    const [nextEl, nextRef] = (0,_lib_hooks_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const [prevEl, prevRef] = (0,_lib_hooks_use_swiper_ref__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"m-auto block w-full max-w-[680px] rounded bg-light p-3\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Swiper, {\n                    id: \"review-gallery\",\n                    modules: [\n                        _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Navigation\n                    ],\n                    initialSlide: data?.initSlide ?? 0,\n                    onSwiper: (swiper)=>{\n                        setTimeout(()=>{\n                            swiper.navigation.init();\n                        }, 100);\n                    },\n                    loop: data?.images?.length > 1,\n                    navigation: {\n                        prevEl,\n                        nextEl\n                    },\n                    ...swiperParams,\n                    children: data?.images?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.SwiperSlide, {\n                            className: \"flex items-center justify-center selection:bg-transparent\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                src: item?.original ?? \"/product-placeholder-borderless.svg\",\n                                alt: `Review gallery ${item.id}`,\n                                width: 600,\n                                height: 600,\n                                objectFit: \"contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, undefined)\n                        }, `review-gallery-${item.id}`, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                data?.images?.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: prevRef,\n                            className: \"absolute z-10 flex items-center justify-center w-8 h-8 -mt-4 transition-all duration-200 border rounded-full shadow-xl cursor-pointer review-gallery-prev top-2/4 border-border-200 border-opacity-70 bg-light text-heading hover:bg-gray-100 ltr:left-2 rtl:right-2 md:-mt-5 md:h-9 md:w-9 ltr:md:left-3 rtl:md:right-3\",\n                            children: isRTL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: nextRef,\n                            className: \"absolute z-10 flex items-center justify-center w-8 h-8 -mt-4 transition-all duration-200 border rounded-full shadow-xl cursor-pointer review-gallery-next top-2/4 border-border-200 border-opacity-70 bg-light text-heading hover:bg-gray-100 ltr:right-2 rtl:left-2 md:-mt-5 md:h-9 md:w-9 ltr:md:right-3 rtl:md:left-3\",\n                            children: isRTL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_left__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_chevron_right__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\reviews\\\\review-image-modal.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewImageModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/reviews/review-image-modal.tsx\n");

/***/ }),

/***/ "./src/components/ui/slider.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/slider.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeMode: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_5__.FreeMode),\n/* harmony export */   Navigation: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Navigation),\n/* harmony export */   Pagination: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Pagination),\n/* harmony export */   Swiper: () => (/* reexport safe */ swiper_react__WEBPACK_IMPORTED_MODULE_6__.Swiper),\n/* harmony export */   SwiperSlide: () => (/* reexport safe */ swiper_react__WEBPACK_IMPORTED_MODULE_6__.SwiperSlide),\n/* harmony export */   Thumbs: () => (/* reexport safe */ swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Thumbs)\n/* harmony export */ });\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swiper/css */ \"./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(swiper_css__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var swiper_css_free_mode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper/css/free-mode */ \"./node_modules/swiper/modules/free-mode.css\");\n/* harmony import */ var swiper_css_free_mode__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(swiper_css_free_mode__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/css/navigation */ \"./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/css/pagination */ \"./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(swiper_css_pagination__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/css/thumbs */ \"./node_modules/swiper/modules/thumbs.css\");\n/* harmony import */ var swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(swiper_css_thumbs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_modules__WEBPACK_IMPORTED_MODULE_5__, swiper_react__WEBPACK_IMPORTED_MODULE_6__]);\n([swiper_modules__WEBPACK_IMPORTED_MODULE_5__, swiper_react__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9zbGlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvQjtBQUNVO0FBQ0M7QUFDQTtBQUNKO0FBQytDO0FBQ3ZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL3VpL3NsaWRlci50c3g/MTdjOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ3N3aXBlci9jc3MnO1xyXG5pbXBvcnQgJ3N3aXBlci9jc3MvZnJlZS1tb2RlJztcclxuaW1wb3J0ICdzd2lwZXIvY3NzL25hdmlnYXRpb24nO1xyXG5pbXBvcnQgJ3N3aXBlci9jc3MvcGFnaW5hdGlvbic7XHJcbmltcG9ydCAnc3dpcGVyL2Nzcy90aHVtYnMnO1xyXG5leHBvcnQgeyBOYXZpZ2F0aW9uLCBUaHVtYnMsIFBhZ2luYXRpb24sIEZyZWVNb2RlIH0gZnJvbSAnc3dpcGVyL21vZHVsZXMnO1xyXG5leHBvcnQgeyBTd2lwZXIsIFN3aXBlclNsaWRlIH0gZnJvbSAnc3dpcGVyL3JlYWN0JztcclxuZXhwb3J0IHR5cGUgeyBTd2lwZXJPcHRpb25zIH0gZnJvbSAnc3dpcGVyL3R5cGVzJztcclxuIl0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJUaHVtYnMiLCJQYWdpbmF0aW9uIiwiRnJlZU1vZGUiLCJTd2lwZXIiLCJTd2lwZXJTbGlkZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/ui/slider.tsx\n");

/***/ }),

/***/ "./src/lib/hooks/use-swiper-ref.ts":
/*!*****************************************!*\
  !*** ./src/lib/hooks/use-swiper-ref.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useSwiperRef = ()=>{\n    const [wrapper, setWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (ref.current) {\n            setWrapper(ref.current);\n        }\n    }, []);\n    return [\n        wrapper,\n        ref\n    ];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSwiperRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2hvb2tzL3VzZS1zd2lwZXItcmVmLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUVwRCxNQUFNRyxlQUFlO0lBSW5CLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHTCwrQ0FBUUE7SUFDdEMsTUFBTU0sTUFBTUwsNkNBQU1BLENBQUk7SUFFdEJDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSUksSUFBSUMsT0FBTyxFQUFFO1lBQ2ZGLFdBQVdDLElBQUlDLE9BQU87UUFDeEI7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO1FBQUNIO1FBQVNFO0tBQUk7QUFDdkI7QUFFQSxpRUFBZUgsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvbGliL2hvb2tzL3VzZS1zd2lwZXItcmVmLnRzP2EyNGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCB1c2VTd2lwZXJSZWYgPSA8VCBleHRlbmRzIEhUTUxFbGVtZW50PigpOiBbXHJcbiAgVCB8IHVuZGVmaW5lZCxcclxuICBSZWFjdC5SZWY8VD5cclxuXSA9PiB7XHJcbiAgY29uc3QgW3dyYXBwZXIsIHNldFdyYXBwZXJdID0gdXNlU3RhdGU8VD4oKTtcclxuICBjb25zdCByZWYgPSB1c2VSZWY8VD4obnVsbCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAocmVmLmN1cnJlbnQpIHtcclxuICAgICAgc2V0V3JhcHBlcihyZWYuY3VycmVudCk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gW3dyYXBwZXIsIHJlZl07XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VTd2lwZXJSZWY7XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZVN3aXBlclJlZiIsIndyYXBwZXIiLCJzZXRXcmFwcGVyIiwicmVmIiwiY3VycmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/lib/hooks/use-swiper-ref.ts\n");

/***/ })

};
;