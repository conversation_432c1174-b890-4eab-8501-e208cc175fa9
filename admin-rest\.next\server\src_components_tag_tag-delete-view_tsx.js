"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_tag_tag-delete-view_tsx";
exports.ids = ["src_components_tag_tag-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/tag/tag-delete-view.tsx":
/*!************************************************!*\
  !*** ./src/components/tag/tag-delete-view.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_tag__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/tag */ \"./src/data/tag.ts\");\n/* harmony import */ var _utils_form_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/form-error */ \"./src/utils/form-error.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tag__WEBPACK_IMPORTED_MODULE_3__, _utils_form_error__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_tag__WEBPACK_IMPORTED_MODULE_3__, _utils_form_error__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst TagDeleteView = ()=>{\n    const { mutate: deleteTagById, isLoading: loading } = (0,_data_tag__WEBPACK_IMPORTED_MODULE_3__.useDeleteTagMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        try {\n            deleteTagById({\n                id: data\n            });\n            closeModal();\n        } catch (error) {\n            closeModal();\n            (0,_utils_form_error__WEBPACK_IMPORTED_MODULE_4__.getErrorMessage)(error);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\tag\\\\tag-delete-view.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TagDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/tag/tag-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/client/tag.ts":
/*!********************************!*\
  !*** ./src/data/client/tag.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tagClient: () => (/* binding */ tagClient)\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst tagClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.TAGS),\n    paginated: ({ type, name, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.TAGS, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9jbGllbnQvdGFnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBeUQ7QUFRRztBQUNMO0FBRWhELE1BQU1HLFlBQVk7SUFDdkIsR0FBR0gsc0VBQVdBLENBQW9DQyxxRUFBYUEsQ0FBQ0csSUFBSSxDQUFDO0lBQ3JFQyxXQUFXLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsUUFBa0M7UUFDN0QsT0FBT04sZ0VBQVVBLENBQUNPLEdBQUcsQ0FBZVIscUVBQWFBLENBQUNHLElBQUksRUFBRTtZQUN0RE0sWUFBWTtZQUNaLEdBQUdGLE1BQU07WUFDVEcsUUFBUVQsZ0VBQVVBLENBQUNVLGtCQUFrQixDQUFDO2dCQUFFTjtnQkFBTUM7WUFBSztRQUNyRDtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL0BtYXJ2ZWwvYWRtaW4tcmVzdC8uL3NyYy9kYXRhL2NsaWVudC90YWcudHM/MTU5YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcnVkRmFjdG9yeSB9IGZyb20gJ0AvZGF0YS9jbGllbnQvY3VyZC1mYWN0b3J5JztcclxuaW1wb3J0IHtcclxuICBDcmVhdGVUYWdJbnB1dCxcclxuICBRdWVyeU9wdGlvbnMsXHJcbiAgVGFnLFxyXG4gIFRhZ1BhZ2luYXRvcixcclxuICBUYWdRdWVyeU9wdGlvbnMsXHJcbn0gZnJvbSAnQC90eXBlcyc7XHJcbmltcG9ydCB7IEFQSV9FTkRQT0lOVFMgfSBmcm9tICdAL2RhdGEvY2xpZW50L2FwaS1lbmRwb2ludHMnO1xyXG5pbXBvcnQgeyBIdHRwQ2xpZW50IH0gZnJvbSAnQC9kYXRhL2NsaWVudC9odHRwLWNsaWVudCc7XHJcblxyXG5leHBvcnQgY29uc3QgdGFnQ2xpZW50ID0ge1xyXG4gIC4uLmNydWRGYWN0b3J5PFRhZywgUXVlcnlPcHRpb25zLCBDcmVhdGVUYWdJbnB1dD4oQVBJX0VORFBPSU5UUy5UQUdTKSxcclxuICBwYWdpbmF0ZWQ6ICh7IHR5cGUsIG5hbWUsIC4uLnBhcmFtcyB9OiBQYXJ0aWFsPFRhZ1F1ZXJ5T3B0aW9ucz4pID0+IHtcclxuICAgIHJldHVybiBIdHRwQ2xpZW50LmdldDxUYWdQYWdpbmF0b3I+KEFQSV9FTkRQT0lOVFMuVEFHUywge1xyXG4gICAgICBzZWFyY2hKb2luOiAnYW5kJyxcclxuICAgICAgLi4ucGFyYW1zLFxyXG4gICAgICBzZWFyY2g6IEh0dHBDbGllbnQuZm9ybWF0U2VhcmNoUGFyYW1zKHsgdHlwZSwgbmFtZSB9KSxcclxuICAgIH0pO1xyXG4gIH0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJjcnVkRmFjdG9yeSIsIkFQSV9FTkRQT0lOVFMiLCJIdHRwQ2xpZW50IiwidGFnQ2xpZW50IiwiVEFHUyIsInBhZ2luYXRlZCIsInR5cGUiLCJuYW1lIiwicGFyYW1zIiwiZ2V0Iiwic2VhcmNoSm9pbiIsInNlYXJjaCIsImZvcm1hdFNlYXJjaFBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/client/tag.ts\n");

/***/ }),

/***/ "./src/data/tag.ts":
/*!*************************!*\
  !*** ./src/data/tag.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateTagMutation: () => (/* binding */ useCreateTagMutation),\n/* harmony export */   useDeleteTagMutation: () => (/* binding */ useDeleteTagMutation),\n/* harmony export */   useTagQuery: () => (/* binding */ useTagQuery),\n/* harmony export */   useTagsQuery: () => (/* binding */ useTagsQuery),\n/* harmony export */   useUpdateTagMutation: () => (/* binding */ useUpdateTagMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _data_client_tag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/client/tag */ \"./src/data/client/tag.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _data_client_tag__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__, _data_client_tag__WEBPACK_IMPORTED_MODULE_7__, _config__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst useCreateTagMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_tag__WEBPACK_IMPORTED_MODULE_7__.tagClient.create, {\n        onSuccess: ()=>{\n            next_router__WEBPACK_IMPORTED_MODULE_0___default().push(_config_routes__WEBPACK_IMPORTED_MODULE_6__.Routes.tag.list, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TAGS);\n        }\n    });\n};\nconst useDeleteTagMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_tag__WEBPACK_IMPORTED_MODULE_7__.tagClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TAGS);\n        }\n    });\n};\nconst useUpdateTagMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_tag__WEBPACK_IMPORTED_MODULE_7__.tagClient.update, {\n        onSuccess: async (data)=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_6__.Routes.tag.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_6__.Routes.tag.list;\n            await router.push(`${generateRedirectUrl}/${data?.slug}/edit`, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_8__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // onSuccess: () => {\n        //   toast.success(t('common:successfully-updated'));\n        // },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TAGS);\n        }\n    });\n};\nconst useTagQuery = ({ slug, language })=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TYPES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_tag__WEBPACK_IMPORTED_MODULE_7__.tagClient.get({\n            slug,\n            language\n        }));\n    return {\n        tag: data,\n        error,\n        loading: isLoading\n    };\n};\nconst useTagsQuery = (options)=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.TAGS,\n        options\n    ], ({ queryKey, pageParam })=>_data_client_tag__WEBPACK_IMPORTED_MODULE_7__.tagClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true\n    });\n    return {\n        tags: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_4__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/tag.ts\n");

/***/ }),

/***/ "./src/utils/form-error.tsx":
/*!**********************************!*\
  !*** ./src/utils/form-error.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_1__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction getErrorMessage(error) {\n    let processedError = {\n        message: \"\",\n        validation: []\n    };\n    if (error.graphQLErrors) {\n        for (const graphQLError of error.graphQLErrors){\n            if (graphQLError.extensions && graphQLError.extensions.category === \"validation\") {\n                processedError[\"message\"] = graphQLError.message;\n                processedError[\"validation\"] = graphQLError.extensions.validation;\n                return processedError;\n            } else if (graphQLError.extensions && graphQLError.extensions.category === \"authorization\") {\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"auth_permissions\");\n                next_router__WEBPACK_IMPORTED_MODULE_0___default().push(\"/\");\n            }\n        }\n    }\n    processedError[\"message\"] = error.message;\n    return processedError;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/form-error.tsx\n");

/***/ })

};
;