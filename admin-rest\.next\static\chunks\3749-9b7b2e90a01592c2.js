"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3749],{92072:function(e,t,r){var l=r(85893),a=r(93967),s=r.n(a),n=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,l.jsx)("div",{className:(0,n.m6)(s()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},86779:function(e,t,r){r.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var l=r(85893);let InfoIcon=e=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,l.jsx)("g",{children:(0,l.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,l.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,l.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,l.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},97670:function(e,t,r){r.r(t);var l=r(85893),a=r(78985),s=r(79362),n=r(8144),o=r(74673),i=r(99494),d=r(5233),c=r(1631),u=r(11163),m=r(48583),x=r(93967),f=r.n(x),p=r(30824),b=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,d.$G)(),[a,n]=(0,m.KO)(s.Hf),{childMenu:o}=t,{width:i}=(0,b.Z)();return(0,l.jsx)("div",{className:"space-y-2",children:null==o?void 0:o.map(e=>{let{href:t,label:n,icon:o,childMenu:d}=e;return(0,l.jsx)(c.Z,{href:t,label:r(n),icon:o,childMenu:d,miniSidebar:a&&i>=s.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[r,a]=(0,m.KO)(s.Hf),n=null===i.siteSettings||void 0===i.siteSettings?void 0:null===(e=i.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,o=Object.keys(n),{width:c}=(0,b.Z)();return(0,l.jsx)(l.Fragment,{children:null==o?void 0:o.map((e,a)=>{var o;return(0,l.jsxs)("div",{className:f()("flex flex-col px-5",r&&c>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,l.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&c>=s.h2?"hidden":""),children:t(null===(o=n[e])||void 0===o?void 0:o.label)}),(0,l.jsx)(SidebarItemMap,{menuItems:n[e]})]},a)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,u.useRouter)(),[i,d]=(0,m.KO)(s.Hf),[c]=(0,m.KO)(s.GH),[x]=(0,m.KO)(s.W4),{width:h}=(0,b.Z)();return(0,l.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,l.jsx)(a.Z,{}),(0,l.jsx)(o.Z,{children:(0,l.jsx)(SideBarGroup,{})}),(0,l.jsxs)("div",{className:"flex flex-1",children:[(0,l.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=s.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-20",i&&h>=s.h2?"lg:w-24":"lg:w-76"),children:(0,l.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,l.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,l.jsx)(SideBarGroup,{})})})}),(0,l.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=s.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",i&&h>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,l.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,l.jsx)(n.Z,{})]})]})]})}},79063:function(e,t,r){r.d(t,{Z:function(){return CreateOrUpdateTaxForm}});var l=r(85893),a=r(33e3),s=r(87536),n=r(60802),o=r(80602),i=r(92072),d=r(11163),c=r(32232),u=r(5233),m=r(47533),x=r(16310);let f=x.Ry().shape({name:x.Z_().required("form:error-name-required"),rate:x.Rx().typeError("form:error-rate-must-number").positive("form:error-rate-must-positive").integer("form:error-rate-must-integer").required("form:error-rate-required")});var p=r(22220);let b={name:"",rate:0,country:"",state:"",zip:"",city:""};function CreateOrUpdateTaxForm(e){var t,r,x,h,g,v;let{initialValues:y}=e,j=(0,d.useRouter)(),{t:w}=(0,u.$G)(),{register:N,handleSubmit:S,formState:{errors:Z}}=(0,s.cI)({shouldUnregister:!0,resolver:(0,m.X)(f),defaultValues:null!=y?y:b}),{mutate:T,isLoading:C}=(0,c.wo)(),{mutate:k,isLoading:M}=(0,c.y2)(),onSubmit=async e=>{y?k({id:y.id,...e}):T({...e})};return(0,l.jsxs)("form",{onSubmit:S(onSubmit),children:[(0,l.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,l.jsx)(o.Z,{title:w("form:form-title-information"),details:"".concat(w(y?"form:item-description-update":"form:item-description-add")," ").concat(w("form:tax-form-info-help-text")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5 "}),(0,l.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,l.jsx)(a.Z,{label:w("form:input-label-name"),...N("name",{required:"Name is required"}),error:w(null===(t=Z.name)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5",required:!0}),(0,l.jsx)(a.Z,{label:w("form:input-label-rate"),...N("rate"),type:"number",error:w(null===(r=Z.rate)||void 0===r?void 0:r.message),variant:"outline",className:"mb-5",required:!0}),(0,l.jsx)(a.Z,{label:w("form:input-label-country"),...N("country"),error:w(null===(x=Z.country)||void 0===x?void 0:x.message),variant:"outline",className:"mb-5"}),(0,l.jsx)(a.Z,{label:w("form:input-label-city"),...N("city"),error:w(null===(h=Z.city)||void 0===h?void 0:h.message),variant:"outline",className:"mb-5"}),(0,l.jsx)(a.Z,{label:w("form:input-label-state"),...N("state"),error:w(null===(g=Z.state)||void 0===g?void 0:g.message),variant:"outline",className:"mb-5"}),(0,l.jsx)(a.Z,{label:w("form:input-label-zip"),...N("zip"),error:w(null===(v=Z.zip)||void 0===v?void 0:v.message),variant:"outline",className:"mb-5"})]})]}),(0,l.jsx)(p.Z,{className:"z-0",children:(0,l.jsxs)("div",{className:"text-end",children:[y&&(0,l.jsx)(n.Z,{variant:"outline",onClick:j.back,className:"text-sm me-4 md:text-base",type:"button",children:w("form:button-label-back")}),(0,l.jsxs)(n.Z,{loading:C||M,disabled:C||M,className:"text-sm md:text-base",children:[w(y?"form:button-label-update":"form:button-label-add")," ",w("form:button-label-tax")]})]})})]})}},80602:function(e,t,r){var l=r(85893);t.Z=e=>{let{title:t,details:r,className:a,...s}=e;return(0,l.jsxs)("div",{className:a,...s,children:[t&&(0,l.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),r&&(0,l.jsx)("p",{className:"text-sm text-body",children:r})]})}},33e3:function(e,t,r){var l=r(85893),a=r(71611),s=r(93967),n=r.n(s),o=r(67294),i=r(98388);let d={small:"text-sm h-10",medium:"h-12",big:"h-14"},c=o.forwardRef((e,t)=>{let{className:r,label:s,note:o,name:c,error:u,children:m,variant:x="normal",dimension:f="medium",shadow:p=!1,type:b="text",inputClassName:h,disabled:g,showLabel:v=!0,required:y,toolTipText:j,labelClassName:w,...N}=e,S=n()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===x,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===x,"border border-border-base focus:border-accent":"outline"===x},{"focus:shadow":p},d[f],h),Z="number"===b&&g?"number-disable":"";return(0,l.jsxs)("div",{className:(0,i.m6)(r),children:[v||s?(0,l.jsx)(a.Z,{htmlFor:c,toolTipText:j,label:s,required:y,className:w}):"",(0,l.jsx)("input",{id:c,name:c,type:b,ref:t,className:(0,i.m6)(n()(g?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(Z," select-none"):"",S)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:g,"aria-invalid":u?"true":"false",...N}),o&&(0,l.jsx)("p",{className:"mt-2 text-xs text-body",children:o}),u&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:u})]})});c.displayName="Input",t.Z=c},23091:function(e,t,r){var l=r(85893),a=r(93967),s=r.n(a),n=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,l.jsx)("label",{className:(0,n.m6)(s()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...r})}},22220:function(e,t,r){var l=r(85893),a=r(93967),s=r.n(a),n=r(98388);t.Z=e=>{let{children:t,className:r,...a}=e;return(0,l.jsx)("div",{className:(0,n.m6)(s()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...a,children:t})}},71611:function(e,t,r){var l=r(85893),a=r(86779),s=r(71943),n=r(23091),o=r(98388);t.Z=e=>{let{className:t,required:r,label:i,toolTipText:d,htmlFor:c}=e;return(0,l.jsxs)(n.Z,{className:(0,o.m6)(t),htmlFor:c,children:[i,r?(0,l.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",d?(0,l.jsx)(s.u,{content:d,children:(0,l.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,l.jsx)(a.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,r){r.d(t,{u:function(){return Tooltip}});var l=r(85893),a=r(67294),s=r(93075),n=r(82364),o=r(24750),i=r(93967),d=r.n(i),c=r(67421),u=r(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},x={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:r,gap:i=8,animation:f="zoomIn",placement:p="top",size:b="md",rounded:h="DEFAULT",shadow:g="md",color:v="default",className:y,arrowClassName:j,showArrow:w=!0}=e,[N,S]=(0,a.useState)(!1),Z=(0,a.useRef)(null),{t:T}=(0,c.$G)(),{x:C,y:k,refs:M,strategy:z,context:I}=(0,s.YF)({placement:p,open:N,onOpenChange:S,middleware:[(0,n.x7)({element:Z}),(0,n.cv)(i),(0,n.RR)(),(0,n.uY)({padding:8})],whileElementsMounted:o.Me}),{getReferenceProps:A,getFloatingProps:E}=(0,s.NI)([(0,s.XI)(I),(0,s.KK)(I),(0,s.qs)(I,{role:"tooltip"}),(0,s.bQ)(I)]),{isMounted:Q,styles:F}=(0,s.Y_)(I,{duration:{open:150,close:150},...x[f]});return(0,l.jsxs)(l.Fragment,{children:[(0,a.cloneElement)(t,A({ref:M.setReference,...t.props})),(Q||N)&&(0,l.jsx)(s.ll,{children:(0,l.jsxs)("div",{role:"tooltip",ref:M.setFloating,className:(0,u.m6)(d()(m.base,m.size[b],m.rounded[h],m.variant.solid.base,m.variant.solid.color[v],m.shadow[g],y)),style:{position:z,top:null!=k?k:0,left:null!=C?C:0,...F},...E(),children:[T("".concat(r)),w&&(0,l.jsx)(s.Y$,{ref:Z,context:I,className:d()(m.arrow.color[v],j),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},32232:function(e,t,r){r.d(t,{wo:function(){return useCreateTaxClassMutation},MA:function(){return useDeleteTaxMutation},io:function(){return useTaxQuery},sQ:function(){return useTaxesQuery},y2:function(){return useUpdateTaxClassMutation}});var l=r(97514),a=r(11163),s=r(88767),n=r(47869),o=r(22920),i=r(5233),d=r(55191),c=r(3737);let u={...(0,d.h)(n.P.TAXES),get(e){let{id:t}=e;return c.eN.get("".concat(n.P.TAXES,"/").concat(t))},paginated:e=>{let{name:t,...r}=e;return c.eN.get(n.P.TAXES,{searchJoin:"and",...r,search:c.eN.formatSearchParams({name:t})})},all:e=>{let{name:t,...r}=e;return c.eN.get(n.P.TAXES,{searchJoin:"and",...r,search:c.eN.formatSearchParams({name:t})})}},useCreateTaxClassMutation=()=>{let e=(0,s.useQueryClient)(),t=(0,a.useRouter)(),{t:r}=(0,i.$G)();return(0,s.useMutation)(u.create,{onSuccess:()=>{t.push(l.Z.tax.list),o.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(n.P.TAXES)}})},useDeleteTaxMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,i.$G)();return(0,s.useMutation)(u.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(n.P.TAXES)}})},useUpdateTaxClassMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(u.update,{onSuccess:()=>{o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(n.P.TAXES)}})},useTaxQuery=e=>(0,s.useQuery)([n.P.TAXES,e],()=>u.get({id:e})),useTaxesQuery=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{data:t,error:r,isLoading:l}=(0,s.useQuery)([n.P.TAXES,e],e=>{let{queryKey:t,pageParam:r}=e;return u.all(Object.assign({},t[1],r))},{keepPreviousData:!0});return{taxes:null!=t?t:[],error:r,loading:l}}}}]);