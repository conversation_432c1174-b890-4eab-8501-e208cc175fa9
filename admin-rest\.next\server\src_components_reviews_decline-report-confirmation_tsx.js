"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_reviews_decline-report-confirmation_tsx";
exports.ids = ["src_components_reviews_decline-report-confirmation_tsx"];
exports.modules = {

/***/ "./src/components/reviews/decline-report-confirmation.tsx":
/*!****************************************************************!*\
  !*** ./src/components/reviews/decline-report-confirmation.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_review__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/review */ \"./src/data/review.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_review__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_review__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst DeclineAbuseReportView = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { mutate: declineReports, isLoading: loading } = (0,_data_review__WEBPACK_IMPORTED_MODULE_3__.useDeclineReviewMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    function handleDelete() {\n        declineReports(data);\n        closeModal();\n        router.push(`/reviews`);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"text-decline\",\n        description: \"text-decline-report-modal-description\",\n        onCancel: closeModal,\n        deleteBtnText: \"text-decline\",\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\reviews\\\\decline-report-confirmation.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DeclineAbuseReportView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/reviews/decline-report-confirmation.tsx\n");

/***/ }),

/***/ "./src/data/client/review.ts":
/*!***********************************!*\
  !*** ./src/data/client/review.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reviewClient: () => (/* binding */ reviewClient)\n/* harmony export */ });\n/* harmony import */ var _api_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _curd_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_curd_factory__WEBPACK_IMPORTED_MODULE_1__, _http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst reviewClient = {\n    ...(0,_curd_factory__WEBPACK_IMPORTED_MODULE_1__.crudFactory)(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS),\n    reportAbuse: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS, data);\n    },\n    decline: (data)=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.post(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.ABUSIVE_REPORTS_DECLINE, data);\n    },\n    get ({ id }) {\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(`${_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS}/${id}`, {\n            with: \"abusive_reports.user;product;user\"\n        });\n    },\n    paginated: ({ type, shop_id, ...params })=>{\n        return _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_api_endpoints__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.REVIEWS, {\n            searchJoin: \"and\",\n            with: \"product;user\",\n            ...params,\n            search: _http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/review.ts\n");

/***/ }),

/***/ "./src/data/review.ts":
/*!****************************!*\
  !*** ./src/data/review.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAbuseReportMutation: () => (/* binding */ useAbuseReportMutation),\n/* harmony export */   useDeclineReviewMutation: () => (/* binding */ useDeclineReviewMutation),\n/* harmony export */   useDeleteReviewMutation: () => (/* binding */ useDeleteReviewMutation),\n/* harmony export */   useReviewQuery: () => (/* binding */ useReviewQuery),\n/* harmony export */   useReviewsQuery: () => (/* binding */ useReviewsQuery)\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/data-mappers */ \"./src/utils/data-mappers.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_client_review__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/review */ \"./src/data/client/review.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__, _data_client_review__WEBPACK_IMPORTED_MODULE_6__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_1__, _utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__, _data_client_review__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst useAbuseReportMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.reportAbuse, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"text-abuse-report-submitted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n            closeModal();\n        }\n    });\n};\nconst useDeclineReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.decline, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"successfully-decline\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.refetchQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useDeleteReviewMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS);\n        }\n    });\n};\nconst useReviewQuery = (id)=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        id\n    ], ()=>_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.get({\n            id\n        }));\n};\nconst useReviewsQuery = (params, options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_4__.API_ENDPOINTS.REVIEWS,\n        params\n    ], ({ queryKey, pageParam })=>_data_client_review__WEBPACK_IMPORTED_MODULE_6__.reviewClient.paginated(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true,\n        ...options\n    });\n    return {\n        reviews: data?.data ?? [],\n        paginatorInfo: (0,_utils_data_mappers__WEBPACK_IMPORTED_MODULE_3__.mapPaginatorData)(data),\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/review.ts\n");

/***/ })

};
;