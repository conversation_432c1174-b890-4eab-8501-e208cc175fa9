(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7396,2036,9930],{27484:function(e){var n,r,s,o,c,f,m,S,v,g,p,E,P,A,O,N,C,Q,w,T,I,U;e.exports=(n="millisecond",r="second",s="minute",o="hour",c="week",f="month",m="quarter",S="year",v="date",g="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,E=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,P=function(e,n,r){var s=String(e);return!s||s.length>=n?e:""+Array(n+1-s.length).join(r)+e},(O={})[A="en"]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var n=["th","st","nd","rd"],r=e%100;return"["+e+(n[(r-20)%10]||n[r]||"th")+"]"}},N="$isDayjsObject",C=function(e){return e instanceof I||!(!e||!e[N])},Q=function t(e,n,r){var s;if(!e)return A;if("string"==typeof e){var o=e.toLowerCase();O[o]&&(s=o),n&&(O[o]=n,s=o);var c=e.split("-");if(!s&&c.length>1)return t(c[0])}else{var f=e.name;O[f]=e,s=f}return!r&&s&&(A=s),s||!r&&A},w=function(e,n){if(C(e))return e.clone();var r="object"==typeof n?n:{};return r.date=e,r.args=arguments,new I(r)},(T={s:P,z:function(e){var n=-e.utcOffset(),r=Math.abs(n);return(n<=0?"+":"-")+P(Math.floor(r/60),2,"0")+":"+P(r%60,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),s=e.clone().add(r,f),o=n-s<0,c=e.clone().add(r+(o?-1:1),f);return+(-(r+(n-s)/(o?s-c:c-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return({M:f,y:S,w:c,d:"day",D:v,h:o,m:s,s:r,ms:n,Q:m})[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}}).l=Q,T.i=C,T.w=function(e,n){return w(e,{locale:n.$L,utc:n.$u,x:n.$x,$offset:n.$offset})},U=(I=function(){function M(e){this.$L=Q(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[N]=!0}var e=M.prototype;return e.parse=function(e){this.$d=function(e){var n=e.date,r=e.utc;if(null===n)return new Date(NaN);if(T.u(n))return new Date;if(n instanceof Date)return new Date(n);if("string"==typeof n&&!/Z$/i.test(n)){var s=n.match(p);if(s){var o=s[2]-1||0,c=(s[7]||"0").substring(0,3);return r?new Date(Date.UTC(s[1],o,s[3]||1,s[4]||0,s[5]||0,s[6]||0,c)):new Date(s[1],o,s[3]||1,s[4]||0,s[5]||0,s[6]||0,c)}}return new Date(n)}(e),this.init()},e.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},e.$utils=function(){return T},e.isValid=function(){return this.$d.toString()!==g},e.isSame=function(e,n){var r=w(e);return this.startOf(n)<=r&&r<=this.endOf(n)},e.isAfter=function(e,n){return w(e)<this.startOf(n)},e.isBefore=function(e,n){return this.endOf(n)<w(e)},e.$g=function(e,n,r){return T.u(e)?this[n]:this.set(r,e)},e.unix=function(){return Math.floor(this.valueOf()/1e3)},e.valueOf=function(){return this.$d.getTime()},e.startOf=function(e,n){var m=this,g=!!T.u(n)||n,p=T.p(e),l=function(e,n){var r=T.w(m.$u?Date.UTC(m.$y,n,e):new Date(m.$y,n,e),m);return g?r:r.endOf("day")},$=function(e,n){return T.w(m.toDate()[e].apply(m.toDate("s"),(g?[0,0,0,0]:[23,59,59,999]).slice(n)),m)},E=this.$W,P=this.$M,A=this.$D,O="set"+(this.$u?"UTC":"");switch(p){case S:return g?l(1,0):l(31,11);case f:return g?l(1,P):l(0,P+1);case c:var N=this.$locale().weekStart||0,C=(E<N?E+7:E)-N;return l(g?A-C:A+(6-C),P);case"day":case v:return $(O+"Hours",0);case o:return $(O+"Minutes",1);case s:return $(O+"Seconds",2);case r:return $(O+"Milliseconds",3);default:return this.clone()}},e.endOf=function(e){return this.startOf(e,!1)},e.$set=function(e,c){var m,g=T.p(e),p="set"+(this.$u?"UTC":""),E=((m={}).day=p+"Date",m[v]=p+"Date",m[f]=p+"Month",m[S]=p+"FullYear",m[o]=p+"Hours",m[s]=p+"Minutes",m[r]=p+"Seconds",m[n]=p+"Milliseconds",m)[g],P="day"===g?this.$D+(c-this.$W):c;if(g===f||g===S){var A=this.clone().set(v,1);A.$d[E](P),A.init(),this.$d=A.set(v,Math.min(this.$D,A.daysInMonth())).$d}else E&&this.$d[E](P);return this.init(),this},e.set=function(e,n){return this.clone().$set(e,n)},e.get=function(e){return this[T.p(e)]()},e.add=function(e,n){var m,v=this;e=Number(e);var g=T.p(n),y=function(n){var r=w(v);return T.w(r.date(r.date()+Math.round(n*e)),v)};if(g===f)return this.set(f,this.$M+e);if(g===S)return this.set(S,this.$y+e);if("day"===g)return y(1);if(g===c)return y(7);var p=((m={})[s]=6e4,m[o]=36e5,m[r]=1e3,m)[g]||1,E=this.$d.getTime()+e*p;return T.w(E,this)},e.subtract=function(e,n){return this.add(-1*e,n)},e.format=function(e){var n=this,r=this.$locale();if(!this.isValid())return r.invalidDate||g;var s=e||"YYYY-MM-DDTHH:mm:ssZ",o=T.z(this),c=this.$H,f=this.$m,m=this.$M,S=r.weekdays,v=r.months,p=r.meridiem,h=function(e,r,o,c){return e&&(e[r]||e(n,s))||o[r].slice(0,c)},d=function(e){return T.s(c%12||12,e,"0")},P=p||function(e,n,r){var s=e<12?"AM":"PM";return r?s.toLowerCase():s};return s.replace(E,function(e,s){return s||function(e){switch(e){case"YY":return String(n.$y).slice(-2);case"YYYY":return T.s(n.$y,4,"0");case"M":return m+1;case"MM":return T.s(m+1,2,"0");case"MMM":return h(r.monthsShort,m,v,3);case"MMMM":return h(v,m);case"D":return n.$D;case"DD":return T.s(n.$D,2,"0");case"d":return String(n.$W);case"dd":return h(r.weekdaysMin,n.$W,S,2);case"ddd":return h(r.weekdaysShort,n.$W,S,3);case"dddd":return S[n.$W];case"H":return String(c);case"HH":return T.s(c,2,"0");case"h":return d(1);case"hh":return d(2);case"a":return P(c,f,!0);case"A":return P(c,f,!1);case"m":return String(f);case"mm":return T.s(f,2,"0");case"s":return String(n.$s);case"ss":return T.s(n.$s,2,"0");case"SSS":return T.s(n.$ms,3,"0");case"Z":return o}return null}(e)||o.replace(":","")})},e.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},e.diff=function(e,n,v){var g,p=this,E=T.p(n),P=w(e),A=(P.utcOffset()-this.utcOffset())*6e4,O=this-P,D=function(){return T.m(p,P)};switch(E){case S:g=D()/12;break;case f:g=D();break;case m:g=D()/3;break;case c:g=(O-A)/6048e5;break;case"day":g=(O-A)/864e5;break;case o:g=O/36e5;break;case s:g=O/6e4;break;case r:g=O/1e3;break;default:g=O}return v?g:T.a(g)},e.daysInMonth=function(){return this.endOf(f).$D},e.$locale=function(){return O[this.$L]},e.locale=function(e,n){if(!e)return this.$L;var r=this.clone(),s=Q(e,n,!0);return s&&(r.$L=s),r},e.clone=function(){return T.w(this.$d,this)},e.toDate=function(){return new Date(this.valueOf())},e.toJSON=function(){return this.isValid()?this.toISOString():null},e.toISOString=function(){return this.$d.toISOString()},e.toString=function(){return this.$d.toUTCString()},M}()).prototype,w.prototype=U,[["$ms",n],["$s",r],["$m",s],["$H",o],["$W","day"],["$M",f],["$y",S],["$D",v]].forEach(function(e){U[e[1]]=function(n){return this.$g(n,e[0],e[1])}}),w.extend=function(e,n){return e.$i||(e(n,I,w),e.$i=!0),w},w.locale=Q,w.isDayjs=C,w.unix=function(e){return w(1e3*e)},w.en=O[A],w.Ls=O,w.p={},w)},84110:function(e){e.exports=function(e,n,r){e=e||{};var s=n.prototype,o={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function i(e,n,r,o){return s.fromToBase(e,n,r,o)}r.en.relativeTime=o,s.fromToBase=function(n,s,c,f,m){for(var S,v,g,p=c.$locale().relativeTime||o,E=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],P=E.length,A=0;A<P;A+=1){var O=E[A];O.d&&(S=f?r(n).diff(c,O.d,!0):c.diff(n,O.d,!0));var N=(e.rounding||Math.round)(Math.abs(S));if(g=S>0,N<=O.r||!O.r){N<=1&&A>0&&(O=E[A-1]);var C=p[O.l];m&&(N=m(""+N)),v="string"==typeof C?C.replace("%d",N):C(N,s,O.l,g);break}}if(s)return v;var Q=g?p.future:p.past;return"function"==typeof Q?Q(v):Q.replace("%s",v)},s.to=function(e,n){return i(e,n,this,!0)},s.from=function(e,n){return i(e,n,this)};var d=function(e){return e.$u?r.utc():r()};s.toNow=function(e){return this.to(d(this),e)},s.fromNow=function(e){return this.from(d(this),e)}}},29387:function(e){var n,r;e.exports=(n={year:0,month:1,day:2,hour:3,minute:4,second:5},r={},function(e,s,o){var c,a=function(e,n,s){void 0===s&&(s={});var o,c,f,m,S=new Date(e);return(void 0===(o=s)&&(o={}),(m=r[f=n+"|"+(c=o.timeZoneName||"short")])||(m=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:c}),r[f]=m),m).formatToParts(S)},u=function(e,r){for(var s=a(e,r),c=[],f=0;f<s.length;f+=1){var m=s[f],S=m.type,v=m.value,g=n[S];g>=0&&(c[g]=parseInt(v,10))}var p=c[3],E=c[0]+"-"+c[1]+"-"+c[2]+" "+(24===p?0:p)+":"+c[4]+":"+c[5]+":000",P=+e;return(o.utc(E).valueOf()-(P-=P%1e3))/6e4},f=s.prototype;f.tz=function(e,n){void 0===e&&(e=c);var r,s=this.utcOffset(),f=this.toDate(),m=f.toLocaleString("en-US",{timeZone:e}),S=Math.round((f-new Date(m))/1e3/60),v=-(15*Math.round(f.getTimezoneOffset()/15))-S;if(Number(v)){if(r=o(m,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(v,!0),n){var g=r.utcOffset();r=r.add(s-g,"minute")}}else r=this.utcOffset(0,n);return r.$x.$timezone=e,r},f.offsetName=function(e){var n=this.$x.$timezone||o.tz.guess(),r=a(this.valueOf(),n,{timeZoneName:e}).find(function(e){return"timezonename"===e.type.toLowerCase()});return r&&r.value};var m=f.startOf;f.startOf=function(e,n){if(!this.$x||!this.$x.$timezone)return m.call(this,e,n);var r=o(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return m.call(r,e,n).tz(this.$x.$timezone,!0)},o.tz=function(e,n,r){var s=r&&n,f=r||n||c,m=u(+o(),f);if("string"!=typeof e)return o(e).tz(f);var S=function(e,n,r){var s=e-60*n*1e3,o=u(s,r);if(n===o)return[s,n];var c=u(s-=60*(o-n)*1e3,r);return o===c?[s,o]:[e-60*Math.min(o,c)*1e3,Math.max(o,c)]}(o.utc(e,s).valueOf(),m,f),v=S[0],g=S[1],p=o(v).utcOffset(g);return p.$x.$timezone=f,p},o.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},o.tz.setDefault=function(e){c=e}})},70178:function(e){var n,r,s;e.exports=(n="minute",r=/[+-]\d\d(?::?\d\d)?/g,s=/([+-]|\d\d)/g,function(e,o,c){var f=o.prototype;c.utc=function(e){var n={date:e,utc:!0,args:arguments};return new o(n)},f.utc=function(e){var r=c(this.toDate(),{locale:this.$L,utc:!0});return e?r.add(this.utcOffset(),n):r},f.local=function(){return c(this.toDate(),{locale:this.$L,utc:!1})};var m=f.parse;f.parse=function(e){e.utc&&(this.$u=!0),this.$utils().u(e.$offset)||(this.$offset=e.$offset),m.call(this,e)};var S=f.init;f.init=function(){if(this.$u){var e=this.$d;this.$y=e.getUTCFullYear(),this.$M=e.getUTCMonth(),this.$D=e.getUTCDate(),this.$W=e.getUTCDay(),this.$H=e.getUTCHours(),this.$m=e.getUTCMinutes(),this.$s=e.getUTCSeconds(),this.$ms=e.getUTCMilliseconds()}else S.call(this)};var v=f.utcOffset;f.utcOffset=function(e,o){var c=this.$utils().u;if(c(e))return this.$u?0:c(this.$offset)?v.call(this):this.$offset;if("string"==typeof e&&null===(e=function(e){void 0===e&&(e="");var n=e.match(r);if(!n)return null;var o=(""+n[0]).match(s)||["-",0,0],c=o[0],f=60*+o[1]+ +o[2];return 0===f?0:"+"===c?f:-f}(e)))return this;var f=16>=Math.abs(e)?60*e:e,m=this;if(o)return m.$offset=f,m.$u=0===e,m;if(0!==e){var S=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(m=this.local().add(f+S,n)).$offset=f,m.$x.$localOffset=S}else m=this.utc();return m};var g=f.format;f.format=function(e){var n=e||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return g.call(this,n)},f.valueOf=function(){var e=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*e},f.isUTC=function(){return!!this.$u},f.toISOString=function(){return this.toDate().toISOString()},f.toString=function(){return this.toDate().toUTCString()};var p=f.toDate;f.toDate=function(e){return"s"===e&&this.$offset?c(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():p.call(this)};var E=f.diff;f.diff=function(e,n,r){if(e&&this.$u===e.$u)return E.call(this,e,n,r);var s=this.local(),o=c(e).local();return E.call(s,o,n,r)}})},71816:function(e,n,r){"use strict";var s=r(85893),o=r(67294),c=r(93967),f=r.n(c),m=r(25675),S=r.n(m),v=r(98388);let g={base:"inline-flex items-center justify-center flex-shrink-0 border text-accent border-border-100 bg-accent/10 overflow-hidden relative",size:{sm:"32px",DEFAULT:"40px",lg:"48px",xl:"56px"},fontSize:{sm:"text-xs",DEFAULT:"text-sm",lg:"text-base",xl:"text-lg"},rounded:{none:"rounded-none",sm:"rounded",md:"rounded-xl",lg:"rounded-2xl",full:"rounded-full"}},p=/(\d*px)?/g;n.Z=e=>{let{src:n,name:r,size:c="DEFAULT",initials:m,customSize:E,rounded:P="full",onClick:A,className:O,...N}=e,[C,Q]=o.useState(!1);if(null==E?void 0:E.match(p)){var w;let e=null!==(w=null==E?void 0:E.match(p))&&void 0!==w?w:[];""===e[0]&&console.warn('customSize prop value is not valid. Please set customSize prop like -> customSize="50px"')}return n&&!C?(0,s.jsx)("div",{className:(0,v.m6)(f()(g.base,g.rounded[P],A&&"cursor-pointer",O)),style:{width:null!=E?E:g.size[c],height:null!=E?E:g.size[c]},onClick:A,...N,children:(0,s.jsx)(S(),{alt:r,src:n,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw",onError:()=>Q(()=>!0)})}):(0,s.jsx)("span",{title:r,className:(0,v.m6)(f()(g.base,g.fontSize[c],g.rounded[P],"font-semibold",A&&"cursor-pointer",O)),style:{width:null!=E?E:g.size[c],height:null!=E?E:g.size[c]},onClick:A,children:m||function(e){if(!e)return"GU";let n=e.split(" "),r=n.map(e=>e[0]);return r.slice(0,2).join("").toUpperCase()}(r)})}},26833:function(e,n,r){"use strict";r.r(n);var s=r(85893),o=r(87006),c=r(16203);let Message=()=>(0,s.jsx)(o.Z,{});Message.authenticate={permissions:c.aF},n.default=Message},80287:function(e,n,r){"use strict";r.d(n,{W:function(){return SearchIcon}});var s=r(85893);let SearchIcon=e=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})},30824:function(e,n,r){"use strict";var s=r(85893),o=r(93967),c=r.n(o),f=r(42189);r(85251),n.Z=e=>{let{options:n,children:r,style:o,className:m,...S}=e;return(0,s.jsx)(f.E,{options:{scrollbars:{autoHide:"scroll"},...n},className:c()("os-theme-thin-dark",m),style:o,...S,children:r})}},44498:function(e,n,r){"use strict";r.d(n,{B:function(){return c}});var s=r(47869),o=r(3737);let c={me:()=>o.eN.get(s.P.ME),login:e=>o.eN.post(s.P.TOKEN,e),logout:()=>o.eN.post(s.P.LOGOUT,{}),register:e=>o.eN.post(s.P.REGISTER,e),update:e=>{let{id:n,input:r}=e;return o.eN.put("".concat(s.P.USERS,"/").concat(n),r)},changePassword:e=>o.eN.post(s.P.CHANGE_PASSWORD,e),forgetPassword:e=>o.eN.post(s.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>o.eN.post(s.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>o.eN.post(s.P.RESET_PASSWORD,e),makeAdmin:e=>o.eN.post(s.P.MAKE_ADMIN,e),block:e=>o.eN.post(s.P.BLOCK_USER,e),unblock:e=>o.eN.post(s.P.UNBLOCK_USER,e),addWalletPoints:e=>o.eN.post(s.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>o.eN.post(s.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:n,...r}=e;return o.eN.get(s.P.USERS,{searchJoin:"and",with:"wallet",...r,search:o.eN.formatSearchParams({name:n})})},fetchAdmins:e=>{let{...n}=e;return o.eN.get(s.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...n})},fetchUser:e=>{let{id:n}=e;return o.eN.get("".concat(s.P.USERS,"/").concat(n))},resendVerificationEmail:()=>o.eN.post(s.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:n}=e;return o.eN.post(s.P.UPDATE_EMAIL,{email:n})},fetchVendors:e=>{let{is_active:n,...r}=e;return o.eN.get(s.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:n,...r})},fetchCustomers:e=>{let{...n}=e;return o.eN.get(s.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...n})},getMyStaffs:e=>{let{is_active:n,shop_id:r,name:c,...f}=e;return o.eN.get(s.P.MY_STAFFS,{searchJoin:"and",shop_id:r,...f,search:o.eN.formatSearchParams({name:c,is_active:n})})},getAllStaffs:e=>{let{is_active:n,name:r,...c}=e;return o.eN.get(s.P.ALL_STAFFS,{searchJoin:"and",...c,search:o.eN.formatSearchParams({name:r,is_active:n})})}}},86599:function(e,n,r){"use strict";r.d(n,{Cq:function(){return useConversationQuery},sw:function(){return useConversationsQuery},As:function(){return useCreateConversations},uX:function(){return useMessageSeen},kY:function(){return useMessagesQuery},$3:function(){return useSendMessage}});var s=r(11163),o=r(88767),c=r(22920),f=r(5233),m=r(97514),S=r(47869),v=r(28597),g=r(55191),p=r(3737);let E={...(0,g.h)(S.P.CONVERSIONS),create(e){let{shop_id:n,via:r}=e;return p.eN.post(S.P.CONVERSIONS,{shop_id:n,via:r})},getMessage(e){let{slug:n,...r}=e;return p.eN.get("".concat(S.P.MESSAGE,"/").concat(n),{searchJoin:"and",...r})},getConversion(e){let{id:n}=e;return p.eN.get("".concat(S.P.CONVERSIONS,"/").concat(n))},messageCreate(e){let{id:n,...r}=e;return p.eN.post("".concat(S.P.MESSAGE,"/").concat(n),r)},messageSeen(e){let{id:n}=e;return p.eN.post("".concat(S.P.MESSAGE_SEEN,"/").concat(n),n)},allConversation:e=>p.eN.get(S.P.CONVERSIONS,e)};var P=r(75814),A=r(16203);let useConversationsQuery=e=>{var n,r;let{data:s,isLoading:c,error:f,refetch:m,fetchNextPage:g,hasNextPage:p,isFetching:P,isSuccess:A,isFetchingNextPage:O}=(0,o.useInfiniteQuery)([S.P.CONVERSIONS,e],e=>{let{queryKey:n,pageParam:r}=e;return E.allConversation(Object.assign({},n[1],r))},{getNextPageParam:e=>{let{current_page:n,last_page:r}=e;return r>n&&{page:n+1}}});return{conversations:null!==(r=null==s?void 0:null===(n=s.pages)||void 0===n?void 0:n.flatMap(e=>e.data))&&void 0!==r?r:[],paginatorInfo:Array.isArray(null==s?void 0:s.pages)?(0,v.Q)(null==s?void 0:s.pages[s.pages.length-1]):null,loading:c,error:f,isFetching:P,refetch:m,isSuccess:A,isLoadingMore:O,loadMore:function(){p&&g()},hasMore:!!p}},useCreateConversations=()=>{let{t:e}=(0,f.$G)(),n=(0,s.useRouter)(),{closeModal:r}=(0,P.SO)(),v=(0,o.useQueryClient)(),{permissions:g}=(0,A.WA)(),p=(0,A.Ft)(A.M$,g);return(0,o.useMutation)(E.create,{onSuccess:s=>{if(null==s?void 0:s.id){var o,f;let S=p?null===m.Z||void 0===m.Z?void 0:null===(o=m.Z.message)||void 0===o?void 0:o.details(null==s?void 0:s.id):null===m.Z||void 0===m.Z?void 0:null===(f=m.Z.shopMessage)||void 0===f?void 0:f.details(null==s?void 0:s.id);c.Am.success(e("common:successfully-created")),n.push("".concat(S)),r()}else c.Am.error("Something went wrong!")},onSettled:()=>{v.invalidateQueries(S.P.MESSAGE),v.invalidateQueries(S.P.CONVERSIONS)}})},useMessagesQuery=e=>{var n,r;let{data:s,isLoading:c,error:f,refetch:m,fetchNextPage:g,hasNextPage:p,isFetching:P,isSuccess:A,isFetchingNextPage:O}=(0,o.useInfiniteQuery)([S.P.MESSAGE,e],e=>{let{queryKey:n,pageParam:r}=e;return E.getMessage(Object.assign({},n[1],r))},{getNextPageParam:e=>{let{current_page:n,last_page:r}=e;return r>n&&{page:n+1}}});return{messages:null!==(r=null==s?void 0:null===(n=s.pages)||void 0===n?void 0:n.flatMap(e=>e.data))&&void 0!==r?r:[],paginatorInfo:Array.isArray(null==s?void 0:s.pages)?(0,v.Q)(null==s?void 0:s.pages[s.pages.length-1]):null,loading:c,error:f,isFetching:P,refetch:m,isSuccess:A,isLoadingMore:O,loadMore:function(){p&&g()},hasMore:!!p}},useConversationQuery=e=>{let{id:n}=e,{data:r,error:s,isLoading:c,isFetching:f}=(0,o.useQuery)([S.P.CONVERSIONS,n],()=>E.getConversion({id:n}),{keepPreviousData:!0});return{data:null!=r?r:[],error:s,loading:c,isFetching:f}},useSendMessage=()=>{let{t:e}=(0,f.$G)(),n=(0,o.useQueryClient)();return(0,o.useMutation)(E.messageCreate,{onSuccess:()=>{c.Am.success(e("common:text-message-sent"))},onSettled:()=>{n.invalidateQueries(S.P.MESSAGE),n.invalidateQueries(S.P.CONVERSIONS)}})},useMessageSeen=()=>{let{t:e}=(0,f.$G)(),n=(0,o.useQueryClient)();return(0,o.useMutation)(E.messageSeen,{onSettled:()=>{n.invalidateQueries(S.P.MESSAGE),n.invalidateQueries(S.P.CONVERSIONS)}})}},99930:function(e,n,r){"use strict";r.d(n,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var s=r(79362),o=r(97514),c=r(31955),f=r(5233),m=r(11163),S=r(88767),v=r(22920),g=r(47869),p=r(44498),E=r(28597),P=r(87066),A=r(16203);let useMeQuery=()=>{let e=(0,S.useQueryClient)(),n=(0,m.useRouter)();return(0,S.useQuery)([g.P.ME],p.B.me,{retry:!1,onSuccess:()=>{n.pathname===o.Z.verifyLicense&&n.replace(o.Z.dashboard),n.pathname===o.Z.verifyEmail&&((0,A.Fu)(!0),n.replace(o.Z.dashboard))},onError:r=>{if(P.Z.isAxiosError(r)){var s,c;if((null===(s=r.response)||void 0===s?void 0:s.status)===417){n.replace(o.Z.verifyLicense);return}if((null===(c=r.response)||void 0===c?void 0:c.status)===409){(0,A.Fu)(!1),n.replace(o.Z.verifyEmail);return}e.clear(),n.replace(o.Z.login)}}})};function useLogin(){return(0,S.useMutation)(p.B.login)}let useLogoutMutation=()=>{let e=(0,m.useRouter)(),{t:n}=(0,f.$G)();return(0,S.useMutation)(p.B.logout,{onSuccess:()=>{c.Z.remove(s.E$),e.replace(o.Z.login),v.Am.success(n("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,S.useQueryClient)(),{t:n}=(0,f.$G)();return(0,S.useMutation)(p.B.register,{onSuccess:()=>{v.Am.success(n("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(g.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,f.$G)(),n=(0,S.useQueryClient)();return(0,S.useMutation)(p.B.update,{onSuccess:()=>{v.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(g.P.ME),n.invalidateQueries(g.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,f.$G)(),n=(0,S.useQueryClient)();return(0,S.useMutation)(p.B.updateEmail,{onSuccess:()=>{v.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:n}}=null!=e?e:{};v.Am.error(null==n?void 0:n.message)},onSettled:()=>{n.invalidateQueries(g.P.ME),n.invalidateQueries(g.P.USERS)}})},useChangePasswordMutation=()=>(0,S.useMutation)(p.B.changePassword),useForgetPasswordMutation=()=>(0,S.useMutation)(p.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,f.$G)("common");return(0,S.useMutation)(p.B.resendVerificationEmail,{onSuccess:()=>{v.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,v.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,f.$G)();(0,S.useQueryClient)();let n=(0,m.useRouter)();return(0,S.useMutation)(p.B.addLicenseKey,{onSuccess:()=>{v.Am.success(e("common:successfully-updated")),setTimeout(()=>{n.reload()},1e3)},onError:()=>{v.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,S.useMutation)(p.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,S.useMutation)(p.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,S.useQueryClient)(),{t:n}=(0,f.$G)();return(0,S.useMutation)(p.B.makeAdmin,{onSuccess:()=>{v.Am.success(n("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(g.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,S.useQueryClient)(),{t:n}=(0,f.$G)();return(0,S.useMutation)(p.B.block,{onSuccess:()=>{v.Am.success(n("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(g.P.USERS),e.invalidateQueries(g.P.STAFFS),e.invalidateQueries(g.P.ADMIN_LIST),e.invalidateQueries(g.P.CUSTOMERS),e.invalidateQueries(g.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,S.useQueryClient)(),{t:n}=(0,f.$G)();return(0,S.useMutation)(p.B.unblock,{onSuccess:()=>{v.Am.success(n("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(g.P.USERS),e.invalidateQueries(g.P.STAFFS),e.invalidateQueries(g.P.ADMIN_LIST),e.invalidateQueries(g.P.CUSTOMERS),e.invalidateQueries(g.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,f.$G)(),n=(0,S.useQueryClient)();return(0,S.useMutation)(p.B.addWalletPoints,{onSuccess:()=>{v.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(g.P.USERS)}})},useUserQuery=e=>{let{id:n}=e;return(0,S.useQuery)([g.P.USERS,n],()=>p.B.fetchUser({id:n}),{enabled:!!n})},useUsersQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.USERS,e],()=>p.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}},useAdminsQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.ADMIN_LIST,e],()=>p.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}},useVendorsQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.VENDORS_LIST,e],()=>p.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}},useCustomersQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.CUSTOMERS,e],()=>p.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}},useMyStaffsQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.MY_STAFFS,e],()=>p.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}},useAllStaffsQuery=e=>{var n;let{data:r,isLoading:s,error:o}=(0,S.useQuery)([g.P.ALL_STAFFS,e],()=>p.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,E.Q)(r),loading:s,error:o}}}}]);