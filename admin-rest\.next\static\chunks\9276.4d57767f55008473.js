"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9276],{89276:function(e,t,u){u.r(t);var n=u(85893),s=u(71421),r=u(75814),i=u(70402),o=u(11163);t.default=()=>{let e=(0,o.useRouter)(),{mutate:t,isLoading:u}=(0,i.Bs)(),{data:c}=(0,r.X9)(),{closeModal:l}=(0,r.SO)();return(0,n.jsx)(s.Z,{title:"text-decline",description:"text-decline-report-modal-description",onCancel:l,deleteBtnText:"text-decline",onDelete:function(){t(c),l(),e.push("/reviews")},deleteBtnLoading:u})}},70402:function(e,t,u){u.d(t,{Cw:function(){return useAbuseReportMutation},Bs:function(){return useDeclineReviewMutation},hI:function(){return useDeleteReviewMutation},$B:function(){return useReviewQuery},_s:function(){return useReviewsQuery}});var n=u(88767),s=u(22920),r=u(5233),i=u(28597),o=u(47869),c=u(75814),l=u(55191),a=u(3737);let d={...(0,l.h)(o.P.REVIEWS),reportAbuse:e=>a.eN.post(o.P.ABUSIVE_REPORTS,e),decline:e=>a.eN.post(o.P.ABUSIVE_REPORTS_DECLINE,e),get(e){let{id:t}=e;return a.eN.get("".concat(o.P.REVIEWS,"/").concat(t),{with:"abusive_reports.user;product;user"})},paginated:e=>{let{type:t,shop_id:u,...n}=e;return a.eN.get(o.P.REVIEWS,{searchJoin:"and",with:"product;user",...n,search:a.eN.formatSearchParams({type:t,shop_id:u})})}},useAbuseReportMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)("common"),{closeModal:u}=(0,c.SO)();return(0,n.useMutation)(d.reportAbuse,{onSuccess:()=>{s.Am.success(t("text-abuse-report-submitted"))},onSettled:()=>{e.refetchQueries(o.P.REVIEWS),u()}})},useDeclineReviewMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)("common");return(0,n.useMutation)(d.decline,{onSuccess:()=>{s.Am.success(t("successfully-decline"))},onSettled:()=>{e.refetchQueries(o.P.REVIEWS)}})},useDeleteReviewMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,r.$G)();return(0,n.useMutation)(d.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(o.P.REVIEWS)}})},useReviewQuery=e=>(0,n.useQuery)([o.P.REVIEWS,e],()=>d.get({id:e})),useReviewsQuery=function(e){var t;let u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:s,error:r,isLoading:c}=(0,n.useQuery)([o.P.REVIEWS,e],e=>{let{queryKey:t,pageParam:u}=e;return d.paginated(Object.assign({},t[1],u))},{keepPreviousData:!0,...u});return{reviews:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(s),error:r,loading:c}}}}]);