(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4646],{16027:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shops",function(){return t(53002)}])},53002:function(e,n,t){"use strict";t.r(n),t.d(n,{__N_SSG:function(){return g},default:function(){return AllShopPage}});var s=t(85893),o=t(92072),i=t(97670),a=t(45957),l=t(55846),u=t(5233),r=t(4852),c=t(67294),m=t(37912),d=t(16203),f=t(30042),h=t(10265),x=t(35484),p=t(11163),_=t(90573),g=!0;function AllShopPage(){var e;let{t:n}=(0,u.$G)(),{locale:t}=(0,p.useRouter)(),[i,d]=(0,c.useState)(""),[g,w]=(0,c.useState)(1),[S,j]=(0,c.useState)("created_at"),[N,v]=(0,c.useState)(h.As.Desc),{shops:b,paginatorInfo:P,loading:Z,error:A}=(0,f.uL)({name:i,limit:10,page:g,orderBy:S,sortedBy:N}),{settings:E,loading:k}=(0,_.n)({language:t});return Z||k?(0,s.jsx)(l.Z,{text:n("common:text-loading")}):A?(0,s.jsx)(a.Z,{message:A.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o.Z,{className:"mb-8 flex flex-col items-center justify-between md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(x.Z,{title:n("common:sidebar-nav-item-shops")})}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center ms-auto md:w-1/2 md:flex-row",children:(0,s.jsx)(m.Z,{onSearch:function(e){let{searchText:n}=e;d(n)},placeholderText:n("form:input-placeholder-search-name")})})]}),(0,s.jsx)(r.Z,{shops:b,paginatorInfo:P,onPagination:function(e){w(e)},onOrder:j,onSort:v,isMultiCommissionRate:!!(null==E?void 0:null===(e=E.options)||void 0===e?void 0:e.isMultiCommissionRate)})]})}AllShopPage.authenticate={permissions:d.M$},AllShopPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,8504,131,9774,2888,179],function(){return e(e.s=16027)}),_N_E=e.O()}]);