(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5443],{81418:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/refund-policies",function(){return n(35305)}])},37912:function(e,t,n){"use strict";var l=n(85893),r=n(5114),o=n(80287),a=n(93967),s=n.n(a),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:m,...v}=e,{register:b,handleSubmit:x,watch:h,reset:g,formState:{errors:P}}=(0,i.cI)({defaultValues:{searchText:""}}),y=h("searchText"),{t:S}=(0,c.$G)();(0,u.useEffect)(()=>{y||n({searchText:""})},[y]);let E=s()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,l.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(s()("relative flex w-full items-center",t)),onSubmit:x(n),children:[(0,l.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,l.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,l.jsx)(o.W,{className:"h-5 w-5"})}),(0,l.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(E),placeholder:null!=m?m:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),P.searchText&&(0,l.jsx)("p",{children:P.searchText.message}),!!y&&(0,l.jsx)("button",{type:"button",onClick:function(){g(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,l.jsx)(r.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){"use strict";n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var l=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,l.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,l.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},8953:function(e,t,n){"use strict";var l=n(85893),r=n(93967),o=n.n(r),a=n(5233),s=n(98388);t.Z=e=>{let{t}=(0,a.$G)(),{className:n,color:r,textColor:u,text:i,textKey:c,animate:d=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("span",{className:(0,s.m6)(o()("inline-block",p.root,{[p.default]:!r,[p.text]:!u,[p.animate]:d},r,u,n)),children:c?t(c):i})})}},18230:function(e,t,n){"use strict";n.d(t,{Z:function(){return pagination}});var l=n(85893),r=n(55891),o=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,l.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,l.jsx)(r.Z,{nextIcon:(0,l.jsx)(o.T,{}),prevIcon:(0,l.jsx)(ArrowPrev,{}),...e})},35305:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return O},default:function(){return RefundPolicies}});var l=n(85893),r=n(92072),o=n(37912),a=n(97670),s=n(804),u=n(8953),i=n(34927),c=n(18230),d=n(27899),p=n(78998),f=n(97514),m=n(10265),v=n(5233),b=n(67294),x=n(77556),h=n(76518),refund_policy_list=e=>{let{refundPolicies:t,paginatorInfo:n,onPagination:r,onSort:o,onOrder:a}=e,{t:g}=(0,v.$G)(),{alignLeft:P}=(0,h.S)(),[y,S]=(0,b.useState)({sort:m.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{o(e=>e===m.As.Desc?m.As.Asc:m.As.Desc),a(e),S({sort:y.sort===m.As.Desc?m.As.Asc:m.As.Desc,column:e})}}),E=[{title:(0,l.jsx)(p.Z,{title:g("table:table-item-id"),ascending:y.sort===m.As.Asc&&"id"===y.column,isActive:"id"===y.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:P,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(g("table:table-item-id"),": ").concat(e)},{title:(0,l.jsx)(p.Z,{title:g("table:table-item-heading"),ascending:y.sort===m.As.Asc&&"title"===y.column,isActive:"title"===y.column}),className:"cursor-pointer",dataIndex:"title",key:"title",align:"left",overflow:"hidden",textOverflow:"ellipsis",width:200,render:e=>(0,l.jsx)("span",{className:"font-medium",children:e}),onHeaderCell:()=>onHeaderClick("title")},{title:g("table:table-item-description"),className:"cursor-pointer",dataIndex:"description",key:"description",width:350,ellipsis:!0,align:P,render:e=>(0,l.jsx)("span",{dangerouslySetInnerHTML:{__html:(null==e?void 0:e.length)<130?e:(null==e?void 0:e.substring(0,130))+"..."}})},{title:(0,l.jsx)(p.Z,{title:g("table:table-item-target"),ascending:y.sort===m.As.Asc&&"target"===y.column,isActive:"target"===y.column}),className:"cursor-pointer",dataIndex:"target",key:"target",width:200,align:"center",onHeaderCell:()=>onHeaderClick("target"),render:e=>e.toUpperCase()},{title:g("table:table-item-status"),dataIndex:"status",key:"status",align:"center",width:180,render:(e,t)=>(0,l.jsx)("div",{className:"flex items-center justify-center space-x-3 rtl:space-x-reverse",children:(0,l.jsx)(u.Z,{text:e,color:"pending"===e.toLocaleLowerCase()?"bg-yellow-400/10 text-yellow-400":"bg-accent/10 text-accent"})})},{title:g("table:table-item-actions"),dataIndex:"slug",key:"actions",align:"right",width:180,render:(e,t)=>(0,l.jsxs)("div",{className:"inline-flex w-auto items-center gap-3",children:[(0,l.jsx)(s.Z,{id:e,detailsUrl:"".concat(f.Z.refundPolicies.details(e)),previewUrl:"".concat(f.Z.refundPolicies.details(e))}),(0,l.jsx)(i.Z,{slug:e,record:t,deleteModalView:"DELETE_REFUND_POLICY",routes:null===f.Z||void 0===f.Z?void 0:f.Z.refundPolicies})]})}];return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,l.jsx)(d.i,{columns:E,emptyText:()=>(0,l.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,l.jsx)(x.m,{className:"w-52"}),(0,l.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:g("table:empty-table-data")}),(0,l.jsx)("p",{className:"text-[13px]",children:g("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:900}})}),!!(null==n?void 0:n.total)&&(0,l.jsx)("div",{className:"flex items-center justify-end",children:(0,l.jsx)(c.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:r})})]})},g=n(49097),P=n(79828),y=n(93967),S=n.n(y);function RefundPolicyStatusFilter(e){let{onStatusFilter:t,className:n}=e,{t:r}=(0,v.$G)();return(0,l.jsx)("div",{className:S()("flex w-full",n),children:(0,l.jsx)("div",{className:"w-full",children:(0,l.jsx)(P.Z,{options:g.I,getOptionLabel:e=>r("form:".concat(e.name)),getOptionValue:e=>e.value,placeholder:r("common:filter-by-status"),onChange:t,isClearable:!0})})})}function RefundPolicyTypeFilter(e){let{onTargetFilter:t,className:n}=e,{t:r}=(0,v.$G)();return(0,l.jsx)("div",{className:S()("flex w-full",n),children:(0,l.jsx)("div",{className:"w-full",children:(0,l.jsx)(P.Z,{options:g.p,getOptionLabel:e=>r("form:".concat(e.name)),getOptionValue:e=>e.value,placeholder:r("common:filter-by-refund-policy"),onChange:t,isClearable:!0})})})}var E=n(45957),N=n(61616),T=n(55846),I=n(93345),j=n(41874),C=n(16203),k=n(79362),R=n(11163),O=!0;function RefundPolicies(){let{t:e}=(0,v.$G)(),{locale:t}=(0,R.useRouter)(),[n,a]=(0,b.useState)(""),[s,u]=(0,b.useState)(1),[i,c]=(0,b.useState)(),[d,p]=(0,b.useState)(),[x,h]=(0,b.useState)("created_at"),[g,P]=(0,b.useState)(m.As.Desc),{refundPolicies:y,loading:S,paginatorInfo:C,error:O}=(0,j.V4)({limit:k.VZ,title:n.replace(/-/g," "),target:i,status:d,page:s,orderBy:x,sortedBy:g,language:t});return S?(0,l.jsx)(T.Z,{text:e("common:text-loading")}):O?(0,l.jsx)(E.Z,{message:O.message}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(r.Z,{className:"mb-8 flex flex-col items-center xl:flex-row",children:[(0,l.jsx)("div",{className:"mb-4 md:w-1/4 xl:mb-0",children:(0,l.jsx)("h1",{className:"before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 xl:before:w-1",children:e("common:text-refund-policies")})}),(0,l.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-3/4",children:[(0,l.jsx)(o.Z,{onSearch:function(e){let{searchText:t}=e;a(t)},placeholderText:e("form:input-placeholder-search-heading")}),(0,l.jsx)(RefundPolicyTypeFilter,{className:"md:ms-6",onTargetFilter:e=>{c(null==e?void 0:e.value),u(1)}}),(0,l.jsx)(RefundPolicyStatusFilter,{className:"md:ms-6",onStatusFilter:e=>{p(null==e?void 0:e.value),u(1)}}),t===I.Config.defaultLanguage&&(0,l.jsx)(N.Z,{href:"".concat(f.Z.refundPolicies.create),className:"h-12 w-full md:w-auto md:ms-6",children:(0,l.jsxs)("span",{children:["+ ",e("form:button-label-add-refund-policy")]})})]})]}),(0,l.jsx)(refund_policy_list,{refundPolicies:y,paginatorInfo:C,onPagination:function(e){u(e)},onOrder:h,onSort:P})]})}RefundPolicies.authenticate={permissions:C.M$},RefundPolicies.Layout=a.default},28368:function(e,t,n){"use strict";n.d(t,{p:function(){return I}});var l,r,o,a=n(67294),s=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),m=n(14157),v=n(15466),b=n(73781);let x=null!=(o=a.startTransition)?o:function(e){e()};var h=((l=h||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l),g=((r=g||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let P={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},y=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(y);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}y.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let E=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,P,e,t)}E.displayName="DisclosurePanelContext";let N=a.Fragment,T=u.AN.RenderStrategy|u.AN.Static,I=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...l}=e,r=(0,a.useRef)(null),o=(0,i.T)(t,(0,i.h)(e=>{r.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:m,buttonId:x},h]=p,g=(0,b.z)(e=>{h({type:1});let t=(0,v.r)(r);if(!t||!x)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(x):t.getElementById(x);null==n||n.focus()}),P=(0,a.useMemo)(()=>({close:g}),[g]),E=(0,a.useMemo)(()=>({open:0===m,close:g}),[m,g]);return a.createElement(y.Provider,{value:p},a.createElement(S.Provider,{value:P},a.createElement(f.up,{value:(0,s.E)(m,{0:f.ZM.Open,1:f.ZM.Closed})},(0,u.sY)({ourProps:{ref:o},theirProps:l,slot:E,defaultTag:N,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:l=`headlessui-disclosure-button-${n}`,...r}=e,[o,s]=M("Disclosure.Button"),f=(0,a.useContext)(E),v=null!==f&&f===o.panelId,x=(0,a.useRef)(null),h=(0,i.T)(x,t,v?null:o.buttonRef);(0,a.useEffect)(()=>{if(!v)return s({type:2,buttonId:l}),()=>{s({type:2,buttonId:null})}},[l,s,v]);let g=(0,b.z)(e=>{var t;if(v){if(1===o.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),P=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),y=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(v?(s({type:0}),null==(n=o.buttonRef.current)||n.focus()):s({type:0}))}),S=(0,a.useMemo)(()=>({open:0===o.disclosureState}),[o]),N=(0,m.f)(e,x),T=v?{ref:h,type:N,onKeyDown:g,onClick:y}:{ref:h,id:l,type:N,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:g,onKeyUp:P,onClick:y};return(0,u.sY)({ourProps:T,theirProps:r,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:l=`headlessui-disclosure-panel-${n}`,...r}=e,[o,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,o.panelRef,e=>{x(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:l}),()=>{s({type:3,panelId:null})}),[l,s]);let m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===o.disclosureState,b=(0,a.useMemo)(()=>({open:0===o.disclosureState,close:d}),[o,d]);return a.createElement(E.Provider,{value:o.panelId},(0,u.sY)({ourProps:{ref:p,id:l},theirProps:r,slot:b,defaultTag:"div",features:T,visible:v,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){"use strict";n.d(t,{J:function(){return Z}});var l,r,o=n(67294),a=n(32984),s=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),m=n(14157),v=n(39650),b=n(15466),x=n(51074),h=n(14007),g=n(46045),P=n(73781),y=n(45662),S=n(3855),E=n(16723),N=n(65958),T=n(2740),I=((l=I||{})[l.Open=0]="Open",l[l.Closed=1]="Closed",l),j=((r=j||{})[r.TogglePopover=0]="TogglePopover",r[r.ClosePopover=1]="ClosePopover",r[r.SetButton=2]="SetButton",r[r.SetButtonId=3]="SetButtonId",r[r.SetPanel=4]="SetPanel",r[r.SetPanelId=5]="SetPanelId",r);let C={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},k=(0,o.createContext)(null);function oe(e){let t=(0,o.useContext)(k);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}k.displayName="PopoverContext";let R=(0,o.createContext)(null);function fe(e){let t=(0,o.useContext)(R);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}R.displayName="PopoverAPIContext";let O=(0,o.createContext)(null);function Ee(){return(0,o.useContext)(O)}O.displayName="PopoverGroupContext";let A=(0,o.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,C,e,t)}A.displayName="PopoverPanelContext";let D=s.AN.RenderStrategy|s.AN.Static,_=s.AN.RenderStrategy|s.AN.Static,Z=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:l=!1,...r}=e,i=(0,o.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,o.useRef)([]),m=(0,o.useReducer)(Ne,{__demoMode:l,popoverState:l?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,o.createRef)(),afterPanelSentinel:(0,o.createRef)()}),[{popoverState:b,button:g,buttonId:y,panel:E,panelId:I,beforePanelSentinel:j,afterPanelSentinel:C},O]=m,D=(0,x.i)(null!=(n=i.current)?n:g),_=(0,o.useMemo)(()=>{if(!g||!E)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(g))^Number(null==e?void 0:e.contains(E)))return!0;let e=(0,p.GO)(),t=e.indexOf(g),n=(t+e.length-1)%e.length,l=(t+1)%e.length,r=e[n],o=e[l];return!E.contains(r)&&!E.contains(o)},[g,E]),Z=(0,S.E)(y),F=(0,S.E)(I),B=(0,o.useMemo)(()=>({buttonId:Z,panelId:F,close:()=>O({type:1})}),[Z,F,O]),L=Ee(),z=null==L?void 0:L.registerPopover,$=(0,P.z)(()=>{var e;return null!=(e=null==L?void 0:L.isFocusWithinPopoverGroup())?e:(null==D?void 0:D.activeElement)&&((null==g?void 0:g.contains(D.activeElement))||(null==E?void 0:E.contains(D.activeElement)))});(0,o.useEffect)(()=>null==z?void 0:z(B),[z,B]);let[G,H]=(0,T.k)(),V=(0,N.v)({mainTreeNodeRef:null==L?void 0:L.mainTreeNodeRef,portals:G,defaultContainers:[g,E]});(0,h.O)(null==D?void 0:D.defaultView,"focus",e=>{var t,n,l,r;e.target!==window&&e.target instanceof HTMLElement&&0===b&&($()||g&&E&&(V.contains(e.target)||null!=(n=null==(t=j.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(r=null==(l=C.current)?void 0:l.contains)&&r.call(l,e.target)||O({type:1})))},!0),(0,v.O)(V.resolveContainers,(e,t)=>{O({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==g||g.focus())},0===b);let K=(0,P.z)(e=>{O({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:g:g;null==t||t.focus()}),U=(0,o.useMemo)(()=>({close:K,isPortalled:_}),[K,_]),J=(0,o.useMemo)(()=>({open:0===b,close:K}),[b,K]);return o.createElement(A.Provider,{value:null},o.createElement(k.Provider,{value:m},o.createElement(R.Provider,{value:U},o.createElement(f.up,{value:(0,a.E)(b,{0:f.ZM.Open,1:f.ZM.Closed})},o.createElement(H,null,(0,s.sY)({ourProps:{ref:c},theirProps:r,slot:J,defaultTag:"div",name:"Popover"}),o.createElement(V.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-button-${n}`,...r}=e,[f,v]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),h=(0,o.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,E=Ee(),N=null==E?void 0:E.closeOthers,T=null!==(0,o.useContext)(A);(0,o.useEffect)(()=>{if(!T)return v({type:3,buttonId:l}),()=>{v({type:3,buttonId:null})}},[T,l,v]);let[I]=(0,o.useState)(()=>Symbol()),j=(0,u.T)(h,t,T?null:e=>{if(e)f.buttons.current.push(I);else{let e=f.buttons.current.indexOf(I);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&v({type:2,button:e})}),C=(0,u.T)(h,t),k=(0,x.i)(h),R=(0,P.z)(e=>{var t,n,l;if(T){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),v({type:1}),null==(l=f.button)||l.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==N||N(f.buttonId)),v({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==N?void 0:N(f.buttonId);if(!h.current||null!=k&&k.activeElement&&!h.current.contains(k.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1})}}),O=(0,P.z)(e=>{T||e.key===c.R.Space&&e.preventDefault()}),D=(0,P.z)(t=>{var n,l;(0,d.P)(t.currentTarget)||e.disabled||(T?(v({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==N||N(f.buttonId)),v({type:0}),null==(l=f.button)||l.focus()))}),_=(0,P.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===f.popoverState,F=(0,o.useMemo)(()=>({open:Z}),[Z]),B=(0,m.f)(e,h),L=T?{ref:C,type:B,onKeyDown:R,onClick:D}:{ref:j,id:f.buttonId,type:B,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:R,onKeyUp:O,onClick:D,onMouseDown:_},z=(0,y.l)(),$=(0,P.z)(()=>{let e=f.panel;e&&(0,a.E)(z.current,{[y.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[y.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(z.current,{[y.N.Forwards]:p.TO.Next,[y.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return o.createElement(o.Fragment,null,(0,s.sY)({ourProps:L,theirProps:r,slot:F,defaultTag:"button",name:"Popover.Button"}),Z&&!T&&b&&o.createElement(g._,{id:S,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:$}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-overlay-${n}`,...r}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,u.T)(t),m=(0,f.oJ)(),v=null!==m?(m&f.ZM.Open)===f.ZM.Open:0===a,b=(0,P.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),x=(0,o.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:p,id:l,"aria-hidden":!0,onClick:b},theirProps:r,slot:x,defaultTag:"div",features:D,visible:v,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:l=`headlessui-popover-panel-${n}`,focus:r=!1,...d}=e,[m,v]=oe("Popover.Panel"),{close:b,isPortalled:h}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,N=`headlessui-focus-sentinel-after-${(0,i.M)()}`,T=(0,o.useRef)(null),I=(0,u.T)(T,t,e=>{v({type:4,panel:e})}),j=(0,x.i)(T);(0,E.e)(()=>(v({type:5,panelId:l}),()=>{v({type:5,panelId:null})}),[l,v]);let C=(0,f.oJ)(),k=null!==C?(C&f.ZM.Open)===f.ZM.Open:0===m.popoverState,R=(0,P.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==m.popoverState||!T.current||null!=j&&j.activeElement&&!T.current.contains(j.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1}),null==(t=m.button)||t.focus()}});(0,o.useEffect)(()=>{var t;e.static||1===m.popoverState&&(null==(t=e.unmount)||t)&&v({type:4,panel:null})},[m.popoverState,e.unmount,e.static,v]),(0,o.useEffect)(()=>{if(m.__demoMode||!r||0!==m.popoverState||!T.current)return;let e=null==j?void 0:j.activeElement;T.current.contains(e)||(0,p.jA)(T.current,p.TO.First)},[m.__demoMode,r,T,m.popoverState]);let O=(0,o.useMemo)(()=>({open:0===m.popoverState,close:b}),[m,b]),D={ref:I,id:l,onKeyDown:R,onBlur:r&&0===m.popoverState?e=>{var t,n,l,r,o;let a=e.relatedTarget;a&&T.current&&(null!=(t=T.current)&&t.contains(a)||(v({type:1}),(null!=(l=null==(n=m.beforePanelSentinel.current)?void 0:n.contains)&&l.call(n,a)||null!=(o=null==(r=m.afterPanelSentinel.current)?void 0:r.contains)&&o.call(r,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,y.l)(),F=(0,P.z)(()=>{let e=T.current;e&&(0,a.E)(Z.current,{[y.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=m.afterPanelSentinel.current)||t.focus())},[y.N.Backwards]:()=>{var e;null==(e=m.button)||e.focus({preventScroll:!0})}})}),B=(0,P.z)(()=>{let e=T.current;e&&(0,a.E)(Z.current,{[y.N.Forwards]:()=>{var e;if(!m.button)return;let t=(0,p.GO)(),n=t.indexOf(m.button),l=t.slice(0,n+1),r=[...t.slice(n+1),...l];for(let t of r.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=m.panel)&&e.contains(t)){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}(0,p.jA)(r,p.TO.First,{sorted:!1})},[y.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=m.button)||t.focus())}})});return o.createElement(A.Provider,{value:l},k&&h&&o.createElement(g._,{id:S,ref:m.beforePanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}),(0,s.sY)({ourProps:D,theirProps:d,slot:O,defaultTag:"div",features:_,visible:k,name:"Popover.Panel"}),k&&h&&o.createElement(g._,{id:N,ref:m.afterPanelSentinel,features:g.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:B}))}),Group:(0,s.yV)(function(e,t){let n=(0,o.useRef)(null),l=(0,u.T)(n,t),[r,a]=(0,o.useState)([]),i=(0,N.H)(),c=(0,P.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,P.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,P.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let l=t.activeElement;return!!(null!=(e=n.current)&&e.contains(l))||r.some(e=>{var n,r;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(l))||(null==(r=t.getElementById(e.panelId.current))?void 0:r.contains(l))})}),f=(0,P.z)(e=>{for(let t of r)t.buttonId.current!==e&&t.close()}),m=(0,o.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),v=(0,o.useMemo)(()=>({}),[]);return o.createElement(O.Provider,{value:m},(0,s.sY)({ourProps:{ref:l},theirProps:e,slot:v,defaultTag:"div",name:"Popover.Group"}),o.createElement(i.MainTreeNode,null))})})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1832,9774,2888,179],function(){return e(e.s=81418)}),_N_E=e.O()}]);