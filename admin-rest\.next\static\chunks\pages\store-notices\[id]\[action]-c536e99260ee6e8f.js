(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3520],{52938:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/store-notices/[id]/[action]",function(){return l(57268)}])},97670:function(e,t,l){"use strict";l.r(t);var r=l(85893),i=l(78985),s=l(79362),n=l(8144),a=l(74673),d=l(99494),o=l(5233),c=l(1631),u=l(11163),x=l(48583),h=l(93967),f=l.n(h),p=l(30824),m=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,o.$G)(),[i,n]=(0,x.KO)(s.Hf),{childMenu:a}=t,{width:d}=(0,m.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:t,label:n,icon:a,childMenu:o}=e;return(0,r.jsx)(c.Z,{href:t,label:l(n),icon:a,childMenu:o,miniSidebar:i&&d>=s.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,o.$G)(),[l,i]=(0,x.KO)(s.Hf),n=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(n),{width:c}=(0,m.Z)();return(0,r.jsx)(r.Fragment,{children:null==a?void 0:a.map((e,i)=>{var a;return(0,r.jsxs)("div",{className:f()("flex flex-col px-5",l&&c>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&c>=s.h2?"hidden":""),children:t(null===(a=n[e])||void 0===a?void 0:a.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:n[e]})]},i)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,u.useRouter)(),[d,o]=(0,x.KO)(s.Hf),[c]=(0,x.KO)(s.GH),[h]=(0,x.KO)(s.W4),{width:g}=(0,m.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(a.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=s.h2&&(c||h)?"lg:pt-[8.75rem]":"pt-20",d&&g>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=s.h2&&(c||h)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&g>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(n.Z,{})]})]})]})}},57268:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSP:function(){return x},default:function(){return UpdateStoreNoticePage}});var r=l(85893),i=l(97670),s=l(25238),n=l(45957),a=l(55846),d=l(5233),o=l(93345),c=l(24616),u=l(11163),x=!0;function UpdateStoreNoticePage(){let{query:e,locale:t}=(0,u.useRouter)(),{t:l}=(0,d.$G)(),{storeNotice:i,loading:x,error:h}=(0,c.Vm)({id:e.id,language:"edit"===e.action.toString()?t:o.Config.defaultLanguage});return x?(0,r.jsx)(a.Z,{text:l("common:text-loading")}):h?(0,r.jsx)(n.Z,{message:h.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:l("form:form-title-edit-store-notice")})}),(0,r.jsx)(s.Z,{initialValues:i})]})}UpdateStoreNoticePage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,3261,7755,939,1273,9494,5535,8186,1285,1631,5238,9774,2888,179],function(){return e(e.s=52938)}),_N_E=e.O()}]);