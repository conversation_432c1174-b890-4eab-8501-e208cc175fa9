"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_attribute_attribute-delete-view_tsx";
exports.ids = ["src_components_attribute_attribute-delete-view_tsx"];
exports.modules = {

/***/ "./src/components/attribute/attribute-delete-view.tsx":
/*!************************************************************!*\
  !*** ./src/components/attribute/attribute-delete-view.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/confirmation-card */ \"./src/components/common/confirmation-card.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _data_attributes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/attributes */ \"./src/data/attributes.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_attributes__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__, _data_attributes__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst AttributeDeleteView = ()=>{\n    const { mutate: deleteAttributeByID, isLoading: loading } = (0,_data_attributes__WEBPACK_IMPORTED_MODULE_3__.useDeleteAttributeMutation)();\n    const { data } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalAction)();\n    async function handleDelete() {\n        deleteAttributeByID({\n            id: data\n        });\n        closeModal();\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_confirmation_card__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        onCancel: closeModal,\n        onDelete: handleDelete,\n        deleteBtnLoading: loading\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\attribute\\\\attribute-delete-view.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AttributeDeleteView);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/attribute/attribute-delete-view.tsx\n");

/***/ }),

/***/ "./src/data/attributes.ts":
/*!********************************!*\
  !*** ./src/data/attributes.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAttributeQuery: () => (/* binding */ useAttributeQuery),\n/* harmony export */   useAttributesQuery: () => (/* binding */ useAttributesQuery),\n/* harmony export */   useCreateAttributeMutation: () => (/* binding */ useCreateAttributeMutation),\n/* harmony export */   useDeleteAttributeMutation: () => (/* binding */ useDeleteAttributeMutation),\n/* harmony export */   useUpdateAttributeMutation: () => (/* binding */ useUpdateAttributeMutation)\n/* harmony export */ });\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/client/attribute */ \"./src/data/client/attribute.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config */ \"./src/config/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_7__]);\n([react_toastify__WEBPACK_IMPORTED_MODULE_2__, _data_client_attribute__WEBPACK_IMPORTED_MODULE_6__, _config__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst useCreateAttributeMutation = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.create, {\n        onSuccess: ()=>{\n            const generateRedirectUrl = router.query.shop ? `/${router.query.shop}${_config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.attribute.list}` : _config_routes__WEBPACK_IMPORTED_MODULE_4__.Routes.attribute.list;\n            next_router__WEBPACK_IMPORTED_MODULE_0___default().push(generateRedirectUrl, undefined, {\n                locale: _config__WEBPACK_IMPORTED_MODULE_7__.Config.defaultLanguage\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-created\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useUpdateAttributeMutation = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.update, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-updated\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useDeleteAttributeMutation = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.delete, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(t(\"common:successfully-deleted\"));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES);\n        }\n    });\n};\nconst useAttributeQuery = ({ slug, language })=>{\n    return (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES,\n        {\n            slug,\n            language\n        }\n    ], ()=>_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.get({\n            slug,\n            language\n        }));\n};\nconst useAttributesQuery = (params, options = {})=>{\n    const { data, error, isLoading } = (0,react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)([\n        _client_api_endpoints__WEBPACK_IMPORTED_MODULE_5__.API_ENDPOINTS.ATTRIBUTES,\n        params\n    ], ({ queryKey, pageParam })=>_data_client_attribute__WEBPACK_IMPORTED_MODULE_6__.attributeClient.all(Object.assign({}, queryKey[1], pageParam)), {\n        keepPreviousData: true,\n        ...options\n    });\n    return {\n        attributes: data ?? [],\n        error,\n        loading: isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZGF0YS9hdHRyaWJ1dGVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0Q7QUFDb0I7QUFDN0I7QUFDTztBQUNMO0FBQ2M7QUFFRztBQUN4QjtBQUkzQixNQUFNVyw2QkFBNkI7SUFDeEMsTUFBTUMsU0FBU1gsc0RBQVNBO0lBQ3hCLE1BQU1ZLGNBQWNULDJEQUFjQTtJQUNsQyxNQUFNLEVBQUVVLENBQUMsRUFBRSxHQUFHUiw0REFBY0E7SUFFNUIsT0FBT0osd0RBQVdBLENBQUNPLG1FQUFlQSxDQUFDTSxNQUFNLEVBQUU7UUFDekNDLFdBQVc7WUFDVCxNQUFNQyxzQkFBc0JMLE9BQU9NLEtBQUssQ0FBQ0MsSUFBSSxHQUN6QyxDQUFDLENBQUMsRUFBRVAsT0FBT00sS0FBSyxDQUFDQyxJQUFJLENBQUMsRUFBRVosa0RBQU1BLENBQUNhLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDLENBQUMsR0FDL0NkLGtEQUFNQSxDQUFDYSxTQUFTLENBQUNDLElBQUk7WUFDekJyQix1REFBVyxDQUFDaUIscUJBQXFCTSxXQUFXO2dCQUMxQ0MsUUFBUWQsMkNBQU1BLENBQUNlLGVBQWU7WUFDaEM7WUFDQXBCLGlEQUFLQSxDQUFDcUIsT0FBTyxDQUFDWixFQUFFO1FBQ2xCO1FBQ0EseUNBQXlDO1FBQ3pDYSxXQUFXO1lBQ1RkLFlBQVllLGlCQUFpQixDQUFDcEIsZ0VBQWFBLENBQUNxQixVQUFVO1FBQ3hEO0lBQ0Y7QUFDRixFQUFFO0FBRUssTUFBTUMsNkJBQTZCO0lBQ3hDLE1BQU0sRUFBRWhCLENBQUMsRUFBRSxHQUFHUiw0REFBY0E7SUFDNUIsTUFBTU8sY0FBY1QsMkRBQWNBO0lBQ2xDLE9BQU9GLHdEQUFXQSxDQUFDTyxtRUFBZUEsQ0FBQ3NCLE1BQU0sRUFBRTtRQUN6Q2YsV0FBVztZQUNUWCxpREFBS0EsQ0FBQ3FCLE9BQU8sQ0FBQ1osRUFBRTtRQUNsQjtRQUNBLHlDQUF5QztRQUN6Q2EsV0FBVztZQUNUZCxZQUFZZSxpQkFBaUIsQ0FBQ3BCLGdFQUFhQSxDQUFDcUIsVUFBVTtRQUN4RDtJQUNGO0FBQ0YsRUFBRTtBQUVLLE1BQU1HLDZCQUE2QjtJQUN4QyxNQUFNbkIsY0FBY1QsMkRBQWNBO0lBQ2xDLE1BQU0sRUFBRVUsQ0FBQyxFQUFFLEdBQUdSLDREQUFjQTtJQUU1QixPQUFPSix3REFBV0EsQ0FBQ08sbUVBQWVBLENBQUN3QixNQUFNLEVBQUU7UUFDekNqQixXQUFXO1lBQ1RYLGlEQUFLQSxDQUFDcUIsT0FBTyxDQUFDWixFQUFFO1FBQ2xCO1FBQ0EseUNBQXlDO1FBQ3pDYSxXQUFXO1lBQ1RkLFlBQVllLGlCQUFpQixDQUFDcEIsZ0VBQWFBLENBQUNxQixVQUFVO1FBQ3hEO0lBQ0Y7QUFDRixFQUFFO0FBRUssTUFBTUssb0JBQW9CLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQWE7SUFDN0QsT0FBT2pDLHFEQUFRQSxDQUNiO1FBQUNLLGdFQUFhQSxDQUFDcUIsVUFBVTtRQUFFO1lBQUVNO1lBQU1DO1FBQVM7S0FBRSxFQUM5QyxJQUFNM0IsbUVBQWVBLENBQUM0QixHQUFHLENBQUM7WUFBRUY7WUFBTUM7UUFBUztBQUUvQyxFQUFFO0FBRUssTUFBTUUscUJBQXFCLENBQ2hDQyxRQUNBQyxVQUFlLENBQUMsQ0FBQztJQUVqQixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxTQUFTLEVBQUUsR0FBR3hDLHFEQUFRQSxDQUN6QztRQUFDSyxnRUFBYUEsQ0FBQ3FCLFVBQVU7UUFBRVU7S0FBTyxFQUNsQyxDQUFDLEVBQUVLLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQ3RCcEMsbUVBQWVBLENBQUNxQyxHQUFHLENBQUNDLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdKLFFBQVEsQ0FBQyxFQUFFLEVBQUVDLGFBQ3JEO1FBQ0VJLGtCQUFrQjtRQUNsQixHQUFHVCxPQUFPO0lBQ1o7SUFHRixPQUFPO1FBQ0xVLFlBQVlULFFBQVEsRUFBRTtRQUN0QkM7UUFDQVMsU0FBU1I7SUFDWDtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbWFydmVsL2FkbWluLXJlc3QvLi9zcmMvZGF0YS9hdHRyaWJ1dGVzLnRzP2RlOWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJvdXRlciwgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCB7IHVzZU11dGF0aW9uLCB1c2VRdWVyeSwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICdyZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtdG9hc3RpZnknO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XHJcbmltcG9ydCB7IFJvdXRlcyB9IGZyb20gJ0AvY29uZmlnL3JvdXRlcyc7XHJcbmltcG9ydCB7IEFQSV9FTkRQT0lOVFMgfSBmcm9tICcuL2NsaWVudC9hcGktZW5kcG9pbnRzJztcclxuaW1wb3J0IHsgQXR0cmlidXRlLCBBdHRyaWJ1dGVRdWVyeU9wdGlvbnMsIEdldFBhcmFtcyB9IGZyb20gJ0AvdHlwZXMnO1xyXG5pbXBvcnQgeyBhdHRyaWJ1dGVDbGllbnQgfSBmcm9tICdAL2RhdGEvY2xpZW50L2F0dHJpYnV0ZSc7XHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gJ0AvY29uZmlnJztcclxuaW1wb3J0IHsgU1RPUkVfT1dORVIsIFNVUEVSX0FETUlOIH0gZnJvbSAnQC91dGlscy9jb25zdGFudHMnO1xyXG5pbXBvcnQgeyBnZXRBdXRoQ3JlZGVudGlhbHMgfSBmcm9tICdAL3V0aWxzL2F1dGgtdXRpbHMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUNyZWF0ZUF0dHJpYnV0ZU11dGF0aW9uID0gKCkgPT4ge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XHJcblxyXG4gIHJldHVybiB1c2VNdXRhdGlvbihhdHRyaWJ1dGVDbGllbnQuY3JlYXRlLCB7XHJcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgY29uc3QgZ2VuZXJhdGVSZWRpcmVjdFVybCA9IHJvdXRlci5xdWVyeS5zaG9wXHJcbiAgICAgICAgPyBgLyR7cm91dGVyLnF1ZXJ5LnNob3B9JHtSb3V0ZXMuYXR0cmlidXRlLmxpc3R9YFxyXG4gICAgICAgIDogUm91dGVzLmF0dHJpYnV0ZS5saXN0O1xyXG4gICAgICBSb3V0ZXIucHVzaChnZW5lcmF0ZVJlZGlyZWN0VXJsLCB1bmRlZmluZWQsIHtcclxuICAgICAgICBsb2NhbGU6IENvbmZpZy5kZWZhdWx0TGFuZ3VhZ2UsXHJcbiAgICAgIH0pO1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKHQoJ2NvbW1vbjpzdWNjZXNzZnVsbHktY3JlYXRlZCcpKTtcclxuICAgIH0sXHJcbiAgICAvLyBBbHdheXMgcmVmZXRjaCBhZnRlciBlcnJvciBvciBzdWNjZXNzOlxyXG4gICAgb25TZXR0bGVkOiAoKSA9PiB7XHJcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKEFQSV9FTkRQT0lOVFMuQVRUUklCVVRFUyk7XHJcbiAgICB9LFxyXG4gIH0pO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZVVwZGF0ZUF0dHJpYnV0ZU11dGF0aW9uID0gKCkgPT4ge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KCk7XHJcbiAgcmV0dXJuIHVzZU11dGF0aW9uKGF0dHJpYnV0ZUNsaWVudC51cGRhdGUsIHtcclxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICB0b2FzdC5zdWNjZXNzKHQoJ2NvbW1vbjpzdWNjZXNzZnVsbHktdXBkYXRlZCcpKTtcclxuICAgIH0sXHJcbiAgICAvLyBBbHdheXMgcmVmZXRjaCBhZnRlciBlcnJvciBvciBzdWNjZXNzOlxyXG4gICAgb25TZXR0bGVkOiAoKSA9PiB7XHJcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKEFQSV9FTkRQT0lOVFMuQVRUUklCVVRFUyk7XHJcbiAgICB9LFxyXG4gIH0pO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZURlbGV0ZUF0dHJpYnV0ZU11dGF0aW9uID0gKCkgPT4ge1xyXG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKTtcclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XHJcblxyXG4gIHJldHVybiB1c2VNdXRhdGlvbihhdHRyaWJ1dGVDbGllbnQuZGVsZXRlLCB7XHJcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgdG9hc3Quc3VjY2Vzcyh0KCdjb21tb246c3VjY2Vzc2Z1bGx5LWRlbGV0ZWQnKSk7XHJcbiAgICB9LFxyXG4gICAgLy8gQWx3YXlzIHJlZmV0Y2ggYWZ0ZXIgZXJyb3Igb3Igc3VjY2VzczpcclxuICAgIG9uU2V0dGxlZDogKCkgPT4ge1xyXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyhBUElfRU5EUE9JTlRTLkFUVFJJQlVURVMpO1xyXG4gICAgfSxcclxuICB9KTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VBdHRyaWJ1dGVRdWVyeSA9ICh7IHNsdWcsIGxhbmd1YWdlIH06IEdldFBhcmFtcykgPT4ge1xyXG4gIHJldHVybiB1c2VRdWVyeTxBdHRyaWJ1dGUsIEVycm9yPihcclxuICAgIFtBUElfRU5EUE9JTlRTLkFUVFJJQlVURVMsIHsgc2x1ZywgbGFuZ3VhZ2UgfV0sXHJcbiAgICAoKSA9PiBhdHRyaWJ1dGVDbGllbnQuZ2V0KHsgc2x1ZywgbGFuZ3VhZ2UgfSlcclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IHVzZUF0dHJpYnV0ZXNRdWVyeSA9IChcclxuICBwYXJhbXM6IFBhcnRpYWw8QXR0cmlidXRlUXVlcnlPcHRpb25zPixcclxuICBvcHRpb25zOiBhbnkgPSB7fVxyXG4pID0+IHtcclxuICBjb25zdCB7IGRhdGEsIGVycm9yLCBpc0xvYWRpbmcgfSA9IHVzZVF1ZXJ5PEF0dHJpYnV0ZVtdLCBFcnJvcj4oXHJcbiAgICBbQVBJX0VORFBPSU5UUy5BVFRSSUJVVEVTLCBwYXJhbXNdLFxyXG4gICAgKHsgcXVlcnlLZXksIHBhZ2VQYXJhbSB9KSA9PlxyXG4gICAgICBhdHRyaWJ1dGVDbGllbnQuYWxsKE9iamVjdC5hc3NpZ24oe30sIHF1ZXJ5S2V5WzFdLCBwYWdlUGFyYW0pKSxcclxuICAgIHtcclxuICAgICAga2VlcFByZXZpb3VzRGF0YTogdHJ1ZSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH1cclxuICApO1xyXG5cclxuICByZXR1cm4ge1xyXG4gICAgYXR0cmlidXRlczogZGF0YSA/PyBbXSxcclxuICAgIGVycm9yLFxyXG4gICAgbG9hZGluZzogaXNMb2FkaW5nLFxyXG4gIH07XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSb3V0ZXIiLCJ1c2VSb3V0ZXIiLCJ1c2VNdXRhdGlvbiIsInVzZVF1ZXJ5IiwidXNlUXVlcnlDbGllbnQiLCJ0b2FzdCIsInVzZVRyYW5zbGF0aW9uIiwiUm91dGVzIiwiQVBJX0VORFBPSU5UUyIsImF0dHJpYnV0ZUNsaWVudCIsIkNvbmZpZyIsInVzZUNyZWF0ZUF0dHJpYnV0ZU11dGF0aW9uIiwicm91dGVyIiwicXVlcnlDbGllbnQiLCJ0IiwiY3JlYXRlIiwib25TdWNjZXNzIiwiZ2VuZXJhdGVSZWRpcmVjdFVybCIsInF1ZXJ5Iiwic2hvcCIsImF0dHJpYnV0ZSIsImxpc3QiLCJwdXNoIiwidW5kZWZpbmVkIiwibG9jYWxlIiwiZGVmYXVsdExhbmd1YWdlIiwic3VjY2VzcyIsIm9uU2V0dGxlZCIsImludmFsaWRhdGVRdWVyaWVzIiwiQVRUUklCVVRFUyIsInVzZVVwZGF0ZUF0dHJpYnV0ZU11dGF0aW9uIiwidXBkYXRlIiwidXNlRGVsZXRlQXR0cmlidXRlTXV0YXRpb24iLCJkZWxldGUiLCJ1c2VBdHRyaWJ1dGVRdWVyeSIsInNsdWciLCJsYW5ndWFnZSIsImdldCIsInVzZUF0dHJpYnV0ZXNRdWVyeSIsInBhcmFtcyIsIm9wdGlvbnMiLCJkYXRhIiwiZXJyb3IiLCJpc0xvYWRpbmciLCJxdWVyeUtleSIsInBhZ2VQYXJhbSIsImFsbCIsIk9iamVjdCIsImFzc2lnbiIsImtlZXBQcmV2aW91c0RhdGEiLCJhdHRyaWJ1dGVzIiwibG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/data/attributes.ts\n");

/***/ }),

/***/ "./src/data/client/attribute.ts":
/*!**************************************!*\
  !*** ./src/data/client/attribute.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attributeClient: () => (/* binding */ attributeClient)\n/* harmony export */ });\n/* harmony import */ var _data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/client/curd-factory */ \"./src/data/client/curd-factory.ts\");\n/* harmony import */ var _data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/client/api-endpoints */ \"./src/data/client/api-endpoints.ts\");\n/* harmony import */ var _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/client/http-client */ \"./src/data/client/http-client.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__]);\n([_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__, _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst attributeClient = {\n    ...(0,_data_client_curd_factory__WEBPACK_IMPORTED_MODULE_0__.crudFactory)(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES),\n    paginated: ({ type, name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                shop_id\n            })\n        });\n    },\n    all: ({ type, name, shop_id, ...params })=>{\n        return _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.get(_data_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.ATTRIBUTES, {\n            searchJoin: \"and\",\n            ...params,\n            search: _data_client_http_client__WEBPACK_IMPORTED_MODULE_2__.HttpClient.formatSearchParams({\n                type,\n                name,\n                shop_id\n            })\n        });\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/data/client/attribute.ts\n");

/***/ })

};
;