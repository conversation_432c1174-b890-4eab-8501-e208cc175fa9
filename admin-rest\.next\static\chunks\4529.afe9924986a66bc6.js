"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4529],{74529:function(e,n,t){t.r(n);var o=t(85893),s=t(71421),u=t(75814),i=t(41107);n.default=()=>{let{mutate:e,isLoading:n}=(0,i.Du)(),{data:t}=(0,u.X9)(),{closeModal:r}=(0,u.SO)();return(0,o.jsx)(s.Z,{onCancel:r,onDelete:function(){e({id:t}),r()},deleteBtnLoading:n})}},41107:function(e,n,t){t.d(n,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var o=t(11163),s=t.n(o),u=t(88767),i=t(22920),r=t(5233),a=t(28597),d=t(97514),c=t(47869),l=t(93345),m=t(55191),A=t(3737);let C={...(0,m.h)(c.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:n,shop_id:t,...o}=e;return A.eN.get(c.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:t,...o,search:A.eN.formatSearchParams({title:n,shop_id:t})})},approve:e=>A.eN.post(c.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>A.eN.post(c.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,r.$G)(),n=(0,u.useQueryClient)();return(0,u.useMutation)(C.approve,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(c.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,r.$G)(),n=(0,u.useQueryClient)();return(0,u.useMutation)(C.disapprove,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(c.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:n,language:t}=e,{data:o,error:s,isLoading:i}=(0,u.useQuery)([c.P.TERMS_AND_CONDITIONS,{slug:n,language:t}],()=>C.get({slug:n,language:t}));return{termsAndConditions:o,error:s,loading:i}},useTermsAndConditionsQuery=e=>{var n;let{data:t,error:o,isLoading:s}=(0,u.useQuery)([c.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:n,pageParam:t}=e;return C.paginated(Object.assign({},n[1],t))},{keepPreviousData:!0});return{termsAndConditions:null!==(n=null==t?void 0:t.data)&&void 0!==n?n:[],paginatorInfo:(0,a.Q)(t),error:o,loading:s}},useCreateTermsAndConditionsMutation=()=>{let e=(0,u.useQueryClient)(),n=(0,o.useRouter)(),{t}=(0,r.$G)();return(0,u.useMutation)(C.create,{onSuccess:async()=>{let e=n.query.shop?"/".concat(n.query.shop).concat(d.Z.termsAndCondition.list):d.Z.termsAndCondition.list;await s().push(e,void 0,{locale:l.Config.defaultLanguage}),i.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;i.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,r.$G)(),n=(0,u.useQueryClient)(),t=(0,o.useRouter)();return(0,u.useMutation)(C.update,{onSuccess:async n=>{let o=t.query.shop?"/".concat(t.query.shop).concat(d.Z.termsAndCondition.list):d.Z.termsAndCondition.list;await t.push(o,void 0,{locale:l.Config.defaultLanguage}),i.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:n=>{var t;i.Am.error(e("common:".concat(null==n?void 0:null===(t=n.response)||void 0===t?void 0:t.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,u.useQueryClient)(),{t:n}=(0,r.$G)();return(0,u.useMutation)(C.delete,{onSuccess:()=>{i.Am.success(n("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;i.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})}}}]);