"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2547],{2547:function(e,r,t){t.r(r),t.d(r,{AddressGrid:function(){return AddressGrid},default:function(){return m}});var n=t(85893),l=t(75814),o=t(75131),i=t(48583),a=t(67294),s=t(35841),d=t(93967),c=t.n(d),p=t(5233),address_card=e=>{let{checked:r,address:t,userId:o}=e,{t:i}=(0,p.$G)(),{openModal:a}=(0,l.SO)();function onEdit(){a("ADD_OR_UPDATE_ADDRESS",{customerId:o,address:t})}return(0,n.jsxs)("div",{className:c()("group relative h-full cursor-pointer overflow-hidden rounded border p-4 hover:border-accent",{"border-accent shadow-sm":r,"border-transparent bg-gray-100":!r}),children:[(0,n.jsx)("p",{className:"mb-3 text-sm font-semibold capitalize truncate text-heading",children:t.title}),(0,n.jsx)("p",{className:"flex flex-col text-sm group text-sub-heading",children:(0,s.T)(t.address)}),(0,n.jsx)("div",{className:"absolute flex opacity-0 top-4 end-4 space-s-2 group-hover:opacity-100",children:onEdit&&(0,n.jsx)("button",{className:"flex items-center justify-center w-5 h-5 rounded-full bg-accent text-light",onClick:onEdit,children:(0,n.jsx)("span",{className:"sr-only",children:i("text-edit")})})})]})},f=t(85031);let AddressHeader=e=>{let{onAdd:r,count:t,label:l}=e,{t:o}=(0,p.$G)("common");return(0,n.jsxs)("div",{className:"mb-5 flex items-center justify-between md:mb-8",children:[(0,n.jsxs)("div",{className:"space-s-3 md:space-s-4 flex items-center",children:[t&&(0,n.jsx)("span",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-accent text-base text-light lg:text-xl",children:t}),(0,n.jsx)("p",{className:"text-lg capitalize text-heading lg:text-xl",children:l})]}),r&&(0,n.jsxs)("button",{className:"flex items-center text-sm font-semibold text-accent transition-colors duration-200 hover:text-accent-hover focus:text-accent-hover focus:outline-none",onClick:r,children:[(0,n.jsx)(f.p,{className:"me-0.5 h-4 w-4 stroke-2"}),o("text-add")]})]})},AddressGrid=e=>{let{addresses:r,label:t,atom:s,className:d,userId:c,count:f,type:m}=e,{t:v}=(0,p.$G)("common"),[g,b]=(0,i.KO)(s),{openModal:h}=(0,l.SO)();return(0,a.useEffect)(()=>{if(null==r?void 0:r.length){if(null==g?void 0:g.id){let e=r.findIndex(e=>e.id===g.id);b(r[e])}else b(null==r?void 0:r[0])}},[r,null==r?void 0:r.length,null==g?void 0:g.id,b]),(0,n.jsxs)("div",{className:d,children:[(0,n.jsx)(AddressHeader,{onAdd:function(){h("ADD_OR_UPDATE_ADDRESS",{customerId:c,type:m})},count:f,label:t}),r&&(null==r?void 0:r.length)?(0,n.jsxs)(o.E,{value:g,onChange:b,children:[(0,n.jsx)(o.E.Label,{className:"sr-only",children:t}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3",children:null==r?void 0:r.map(e=>(0,n.jsx)(o.E.Option,{value:e,children:r=>{let{checked:t}=r;return(0,n.jsx)(address_card,{checked:t,address:e,userId:c})}},e.id))})]}):(0,n.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3",children:(0,n.jsx)("span",{className:"relative rounded border border-border-200 bg-gray-100 px-5 py-6 text-center text-base",children:v("text-no-address")})})]})};var m=AddressGrid},35841:function(e,r,t){t.d(r,{T:function(){return formatAddress}});function formatAddress(e){if(!e)return;let r=["street_address","city","state","zip","country"].reduce((r,t)=>({...r,[t]:e[t]}),{}),t=Object.fromEntries(Object.entries(r).filter(e=>{let[r,t]=e;return!!t}));return Object.values(t).join(", ")}},95389:function(e,r,t){t.d(r,{_:function(){return c},b:function(){return H}});var n=t(67294),l=t(19946),o=t(12351),i=t(16723),a=t(23784),s=t(73781);let d=(0,n.createContext)(null);function H(){let[e,r]=(0,n.useState)([]);return[e.length>0?e.join(" "):void 0,(0,n.useMemo)(()=>function(e){let t=(0,s.z)(e=>(r(r=>[...r,e]),()=>r(r=>{let t=r.slice(),n=t.indexOf(e);return -1!==n&&t.splice(n,1),t}))),l=(0,n.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props}),[t,e.slot,e.name,e.props]);return n.createElement(d.Provider,{value:l},e.children)},[r])]}let c=Object.assign((0,o.yV)(function(e,r){let t=(0,l.M)(),{id:s=`headlessui-label-${t}`,passive:c=!1,...p}=e,f=function u(){let e=(0,n.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),m=(0,a.T)(r);(0,i.e)(()=>f.register(s),[s,f.register]);let v={ref:m,...f.props,id:s};return c&&("onClick"in v&&(delete v.htmlFor,delete v.onClick),"onClick"in p&&delete p.onClick),(0,o.sY)({ourProps:v,theirProps:p,slot:f.slot||{},defaultTag:"label",name:f.name||"Label"})}),{})},75131:function(e,r,t){t.d(r,{E:function(){return G}});var n,l,o=t(67294),i=t(12351),a=t(19946),s=t(32984),d=t(16723),c=t(61363),p=t(84575),f=t(14227),m=t(95389),v=t(39516),g=t(31591),b=t(23784),h=t(46045),x=t(18689),E=t(15466),R=t(73781),j=t(31147),y=t(64103),A=t(3855),O=t(94192),k=((n=k||{})[n.RegisterOption=0]="RegisterOption",n[n.UnregisterOption=1]="UnregisterOption",n);let T={0(e,r){let t=[...e.options,{id:r.id,element:r.element,propsRef:r.propsRef}];return{...e,options:(0,p.z2)(t,e=>e.element.current)}},1(e,r){let t=e.options.slice(),n=e.options.findIndex(e=>e.id===r.id);return -1===n?e:(t.splice(n,1),{...e,options:t})}},N=(0,o.createContext)(null);N.displayName="RadioGroupDataContext";let C=(0,o.createContext)(null);function Le(e,r){return(0,s.E)(r.type,T,e,r)}C.displayName="RadioGroupActionsContext";var D=((l=D||{})[l.Empty=1]="Empty",l[l.Active=2]="Active",l);let G=Object.assign((0,i.yV)(function(e,r){let t=(0,a.M)(),{id:n=`headlessui-radiogroup-${t}`,value:l,defaultValue:s,form:d,name:f,onChange:y,by:A=(e,r)=>e===r,disabled:k=!1,...T}=e,D=(0,R.z)("string"==typeof A?(e,r)=>(null==e?void 0:e[A])===(null==r?void 0:r[A]):A),[G,w]=(0,o.useReducer)(Le,{options:[]}),P=G.options,[_,S]=(0,m.b)(),[L,z]=(0,v.f)(),F=(0,o.useRef)(null),M=(0,b.T)(F,r),[I,$]=(0,j.q)(l,y,s),U=(0,o.useMemo)(()=>P.find(e=>!e.propsRef.current.disabled),[P]),V=(0,o.useMemo)(()=>P.some(e=>D(e.propsRef.current.value,I)),[P,I]),Y=(0,R.z)(e=>{var r;if(k||D(e,I))return!1;let t=null==(r=P.find(r=>D(r.propsRef.current.value,e)))?void 0:r.propsRef.current;return(null==t||!t.disabled)&&(null==$||$(e),!0)});(0,g.B)({container:F.current,accept:e=>"radio"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let K=(0,R.z)(e=>{let r=F.current;if(!r)return;let t=(0,E.r)(r),n=P.filter(e=>!1===e.propsRef.current.disabled).map(e=>e.element.current);switch(e.key){case c.R.Enter:(0,x.g)(e.currentTarget);break;case c.R.ArrowLeft:case c.R.ArrowUp:if(e.preventDefault(),e.stopPropagation(),(0,p.jA)(n,p.TO.Previous|p.TO.WrapAround)===p.fE.Success){let e=P.find(e=>e.element.current===(null==t?void 0:t.activeElement));e&&Y(e.propsRef.current.value)}break;case c.R.ArrowRight:case c.R.ArrowDown:if(e.preventDefault(),e.stopPropagation(),(0,p.jA)(n,p.TO.Next|p.TO.WrapAround)===p.fE.Success){let e=P.find(e=>e.element.current===(null==t?void 0:t.activeElement));e&&Y(e.propsRef.current.value)}break;case c.R.Space:{e.preventDefault(),e.stopPropagation();let r=P.find(e=>e.element.current===(null==t?void 0:t.activeElement));r&&Y(r.propsRef.current.value)}}}),B=(0,R.z)(e=>(w({type:0,...e}),()=>w({type:1,id:e.id}))),W=(0,o.useMemo)(()=>({value:I,firstOption:U,containsCheckedOption:V,disabled:k,compare:D,...G}),[I,U,V,k,D,G]),q=(0,o.useMemo)(()=>({registerOption:B,change:Y}),[B,Y]),J=(0,o.useMemo)(()=>({value:I}),[I]),Q=(0,o.useRef)(null),X=(0,O.G)();return(0,o.useEffect)(()=>{Q.current&&void 0!==s&&X.addEventListener(Q.current,"reset",()=>{Y(s)})},[Q,Y]),o.createElement(z,{name:"RadioGroup.Description"},o.createElement(S,{name:"RadioGroup.Label"},o.createElement(C.Provider,{value:q},o.createElement(N.Provider,{value:W},null!=f&&null!=I&&(0,x.t)({[f]:I}).map(([e,r],t)=>o.createElement(h._,{features:h.A.Hidden,ref:0===t?e=>{var r;Q.current=null!=(r=null==e?void 0:e.closest("form"))?r:null}:void 0,...(0,i.oA)({key:e,as:"input",type:"radio",checked:null!=r,hidden:!0,readOnly:!0,form:d,name:e,value:r})})),(0,i.sY)({ourProps:{ref:M,id:n,role:"radiogroup","aria-labelledby":_,"aria-describedby":L,onKeyDown:K},theirProps:T,slot:J,defaultTag:"div",name:"RadioGroup"})))))}),{Option:(0,i.yV)(function(e,r){var t;let n=(0,a.M)(),{id:l=`headlessui-radiogroup-option-${n}`,value:s,disabled:c=!1,...p}=e,g=(0,o.useRef)(null),h=(0,b.T)(g,r),[x,E]=(0,m.b)(),[j,O]=(0,v.f)(),{addFlag:k,removeFlag:T,hasFlag:D}=(0,f.V)(1),G=(0,A.E)({value:s,disabled:c}),w=function oe(e){let r=(0,o.useContext)(N);if(null===r){let r=Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,oe),r}return r}("RadioGroup.Option"),P=function ne(e){let r=(0,o.useContext)(C);if(null===r){let r=Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ne),r}return r}("RadioGroup.Option");(0,d.e)(()=>P.registerOption({id:l,element:g,propsRef:G}),[l,P,g,e]);let _=(0,R.z)(e=>{var r;if((0,y.P)(e.currentTarget))return e.preventDefault();P.change(s)&&(k(2),null==(r=g.current)||r.focus())}),S=(0,R.z)(e=>{if((0,y.P)(e.currentTarget))return e.preventDefault();k(2)}),L=(0,R.z)(()=>T(2)),z=(null==(t=w.firstOption)?void 0:t.id)===l,F=w.disabled||c,M=w.compare(w.value,s),I={ref:h,id:l,role:"radio","aria-checked":M?"true":"false","aria-labelledby":x,"aria-describedby":j,"aria-disabled":!!F||void 0,tabIndex:F?-1:M||!w.containsCheckedOption&&z?0:-1,onClick:F?void 0:_,onFocus:F?void 0:S,onBlur:F?void 0:L},$=(0,o.useMemo)(()=>({checked:M,disabled:F,active:D(2)}),[M,F,D]);return o.createElement(O,{name:"RadioGroup.Description"},o.createElement(E,{name:"RadioGroup.Label"},(0,i.sY)({ourProps:I,theirProps:p,slot:$,defaultTag:"div",name:"RadioGroup.Option"})))}),Label:m._,Description:v.d})}}]);