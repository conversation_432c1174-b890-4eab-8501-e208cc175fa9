(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3837],{84034:function(e,s,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/orders/[orderId]",function(){return l(48113)}])},47133:function(e,s,l){"use strict";var t=l(85893),i=l(78985),n=l(79362),d=l(11163),r=l(16203),a=l(1631),o=l(99494),c=l(5233),m=l(74673),u=l(8144),x=l(90573),h=l(48583),p=l(93967),v=l.n(p),b=l(30824),f=l(62964);let SidebarItemMap=e=>{var s,l;let i,o,{menuItems:m}=e,{locale:u}=(0,d.useRouter)(),{t:p}=(0,c.$G)(),{settings:v}=(0,x.n)({language:u}),{childMenu:b}=m,j=null==v?void 0:null===(s=v.options)||void 0===s?void 0:s.enableTerms,g=null==v?void 0:null===(l=v.options)||void 0===l?void 0:l.enableCoupons,{permissions:N}=(0,r.WA)(),[w,y]=(0,h.KO)(n.Hf),{width:_}=(0,f.Z)(),{query:{shop:Z}}=(0,d.useRouter)();return!j&&(i=null==m?void 0:m.childMenu.find(e=>"Terms And Conditions"===e.label))&&(i.permissions=r.M$),!g&&(o=null==m?void 0:m.childMenu.find(e=>"Coupons"===e.label))&&(o.permissions=r.M$),(0,t.jsx)("div",{className:"space-y-2",children:null==b?void 0:b.map(e=>{let{href:s,label:l,icon:i,permissions:d,childMenu:o}=e;return o||(0,r.Ft)(d,N)?(0,t.jsx)(a.Z,{href:s(null==Z?void 0:Z.toString()),label:p(l),icon:i,childMenu:o,miniSidebar:w&&_>=n.h2},l):null})})},SideBarGroup=()=>{var e,s;let[l,i]=(0,h.KO)(n.Hf),{role:d}=(0,r.WA)(),a="staff"===d?null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===o.siteSettings||void 0===o.siteSettings?void 0:null===(s=o.siteSettings.sidebarLinks)||void 0===s?void 0:s.shop,m=Object.keys(a),{width:u}=(0,f.Z)(),{t:x}=(0,c.$G)();return(0,t.jsx)(t.Fragment,{children:null==m?void 0:m.map((e,s)=>{var i;return(0,t.jsxs)("div",{className:v()("flex flex-col px-5",l&&u>=n.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:v()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&u>=n.h2?"hidden":""),children:x(null===(i=a[e])||void 0===i?void 0:i.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:a[e]})]},s)})})};s.Z=e=>{let{children:s}=e,[l,r]=(0,h.KO)(n.Hf),{locale:a}=(0,d.useRouter)(),{width:o}=(0,f.Z)(),[c]=(0,h.KO)(n.GH),[x]=(0,h.KO)(n.W4);return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===a||"he"===a?"rtl":"ltr",children:[(0,t.jsx)(i.Z,{}),(0,t.jsx)(m.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:v()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",o>=n.h2&&(c||x)?"pt-[8.75rem]":"pt-20",l&&o>=n.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,t.jsx)(b.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:v()("relative flex w-full flex-col justify-start transition-[padding] duration-300",o>=n.h2&&(c||x)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",l&&o>=n.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:s}),(0,t.jsx)(u.Z,{})]})]})]})}},48113:function(e,s,l){"use strict";l.r(s),l.d(s,{__N_SSP:function(){return C},default:function(){return OrderDetailsPage}});var t=l(85893),i=l(92072),n=l(25675),d=l.n(n),r=l(27899),a=l(11163),o=l(87536),c=l(60802),m=l(45957),u=l(99494),x=l(60942),h=l(35841),p=l(55846),v=l(66271),b=l(5233),f=l(28454),j=l(47133),g=l(77556),N=l(76518),w=l(16203),y=l(73626),_=l(10265),Z=l(41312),S=l(36184),k=l(51640),O=l(85705),D=l(97514),I=l(30042),P=l(99930),E=l(68040),C=!0;function OrderDetailsPage(){var e,s,l,n,j,C;let{t:L}=(0,b.$G)(),{locale:F,query:M}=(0,a.useRouter)(),T=(0,a.useRouter)(),{permissions:A}=(0,w.WA)(),{data:G}=(0,P.UE)(),{data:K}=(0,I.DZ)({slug:null==M?void 0:M.shop}),R=null==K?void 0:K.id,{alignLeft:$,alignRight:H,isRTL:W}=(0,N.S)(),{mutate:q,isLoading:B}=(0,y.Oc)(),{order:z,isLoading:U,error:V}=(0,y.OT)({id:M.orderId,language:F}),{refetch:X}=(0,y.qD)({order_id:M.orderId,language:F,isRTL:W},{enabled:!1}),{handleSubmit:J,control:Q,formState:{errors:Y}}=(0,o.cI)({defaultValues:{order_status:null!==(C=null==z?void 0:z.order_status)&&void 0!==C?C:""}});async function handleDownloadInvoice(){let{data:e}=await X();if(e){let s=document.createElement("a");s.href=e,s.setAttribute("download","order-invoice"),s.click()}}let{price:ee}=(0,x.ZP)(z&&{amount:null==z?void 0:z.amount}),{price:es}=(0,x.ZP)(z&&{amount:null==z?void 0:z.paid_total}),{price:el}=(0,x.ZP)(z&&{amount:null==z?void 0:z.discount}),{price:et}=(0,x.ZP)(z&&{amount:null==z?void 0:z.delivery_fee}),{price:ei}=(0,x.ZP)(z&&{amount:null==z?void 0:z.sales_tax}),en=(0,E.b)({customer_contact:null==z?void 0:z.customer_contact});if(U)return(0,t.jsx)(p.Z,{text:L("common:text-loading")});if(V)return(0,t.jsx)(m.Z,{message:V.message});let ed=[{dataIndex:"image",key:"image",width:70,render:e=>{var s;return(0,t.jsx)(d(),{src:null!==(s=null==e?void 0:e.thumbnail)&&void 0!==s?s:u.siteSettings.product.placeholder,alt:"alt text",width:50,height:50})}},{title:L("table:table-item-products"),dataIndex:"name",key:"name",align:$,render:(e,s)=>(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{children:e}),(0,t.jsx)("span",{className:"mx-2",children:"x"}),(0,t.jsx)("span",{className:"font-semibold text-heading",children:s.pivot.order_quantity})]})},{title:L("table:table-item-total"),dataIndex:"pivot",key:"pivot",align:H,render:function(e){let{price:s}=(0,x.ZP)({amount:Number(null==e?void 0:e.subtotal)});return(0,t.jsx)("span",{children:s})}}];return(0,w.Ft)(w.M$,A)||(null==G?void 0:null===(e=G.shops)||void 0===e?void 0:e.map(e=>e.id).includes(R))||(null==G?void 0:null===(s=G.managed_shop)||void 0===s?void 0:s.id)==R||T.replace(D.Z.dashboard),(0,t.jsx)("div",{children:(0,t.jsxs)(i.Z,{children:[(0,t.jsx)("div",{className:"mb-6 -mt-5 -ml-5 -mr-5 md:-mr-8 md:-ml-8 md:-mt-8",children:(0,t.jsx)(S.Z,{order:z,wrapperClassName:"px-8 py-4"})}),(0,t.jsx)("div",{className:"flex w-full",children:(0,t.jsxs)(c.Z,{onClick:handleDownloadInvoice,className:"mb-5 bg-blue-500 ltr:ml-auto rtl:mr-auto",children:[(0,t.jsx)(Z._,{className:"h-4 w-4 me-3"}),L("common:text-download")," ",L("common:text-invoice")]})}),(0,t.jsxs)("div",{className:"flex flex-col items-center lg:flex-row",children:[(0,t.jsxs)("h3",{className:"mb-8 w-full whitespace-nowrap text-center text-2xl font-semibold text-heading lg:mb-0 lg:w-1/3 lg:text-start",children:[L("form:input-label-order-id")," - ",null==z?void 0:z.tracking_number]}),![_.iF.FAILED,_.iF.CANCELLED,_.iF.REFUNDED].includes(null==z?void 0:z.order_status)&&(0,t.jsxs)("form",{onSubmit:J(e=>{let{order_status:s}=e;q({id:null==z?void 0:z.id,order_status:null==s?void 0:s.status})}),className:"flex w-full items-start ms-auto lg:w-2/4",children:[(0,t.jsxs)("div",{className:"z-20 w-full me-5",children:[(0,t.jsx)(f.Z,{name:"order_status",control:Q,getOptionLabel:e=>L(e.name),getOptionValue:e=>e.status,options:k.D.slice(0,6),placeholder:L("form:input-placeholder-order-status")}),(0,t.jsx)(v.Z,{message:L(null==Y?void 0:null===(l=Y.order_status)||void 0===l?void 0:l.message)})]}),(0,t.jsxs)(c.Z,{loading:B,children:[(0,t.jsx)("span",{className:"hidden sm:block",children:L("form:button-label-change-status")}),(0,t.jsx)("span",{className:"block sm:hidden",children:L("form:form:button-label-change")})]})]})]}),(0,t.jsx)("div",{className:"my-5 flex items-center justify-center lg:my-10",children:(0,t.jsx)(O.Z,{orderStatus:null==z?void 0:z.order_status,paymentStatus:null==z?void 0:z.payment_status})}),(0,t.jsxs)("div",{className:"mb-10",children:[z?(0,t.jsx)(r.i,{columns:ed,emptyText:()=>(0,t.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,t.jsx)(g.m,{className:"w-52"}),(0,t.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:L("table:empty-table-data")}),(0,t.jsx)("p",{className:"text-[13px]",children:L("table:empty-table-sorry-text")})]}),data:null==z?void 0:z.products,rowKey:"id",scroll:{x:300}}):(0,t.jsx)("span",{children:L("common:no-order-found")}),(0,t.jsxs)("div",{className:"flex w-full flex-col space-y-2 border-t-4 border-double border-border-200 px-4 py-4 ms-auto sm:w-1/2 md:w-1/3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,t.jsx)("span",{children:L("common:order-sub-total")}),(0,t.jsx)("span",{children:ee})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,t.jsx)("span",{children:L("common:order-tax")}),(0,t.jsx)("span",{children:ei})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,t.jsx)("span",{children:L("common:order-delivery-fee")}),(0,t.jsx)("span",{children:et})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,t.jsx)("span",{children:L("common:order-discount")}),(0,t.jsx)("span",{children:el})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between font-semibold text-body",children:[(0,t.jsx)("span",{children:L("common:order-total")}),(0,t.jsx)("span",{children:es})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between",children:[(0,t.jsxs)("div",{className:"mb-10 w-full sm:mb-0 sm:w-1/2 sm:pe-8",children:[(0,t.jsx)("h3",{className:"mb-3 border-b border-border-200 pb-2 font-semibold text-heading",children:L("common:billing-address")}),(0,t.jsxs)("div",{className:"flex flex-col items-start space-y-1 text-sm text-body",children:[(0,t.jsx)("span",{children:null==z?void 0:null===(n=z.customer)||void 0===n?void 0:n.name}),(null==z?void 0:z.billing_address)&&(0,t.jsx)("span",{children:(0,h.T)(z.billing_address)}),(null==z?void 0:z.customer_contact)&&(0,t.jsx)("span",{children:en})]})]}),(0,t.jsxs)("div",{className:"w-full sm:w-1/2 sm:ps-8",children:[(0,t.jsx)("h3",{className:"mb-3 border-b border-border-200 pb-2 font-semibold text-heading text-start sm:text-end",children:L("common:shipping-address")}),(0,t.jsxs)("div",{className:"flex flex-col items-start space-y-1 text-sm text-body text-start sm:items-end sm:text-end",children:[(0,t.jsx)("span",{children:null==z?void 0:null===(j=z.customer)||void 0===j?void 0:j.name}),(null==z?void 0:z.shipping_address)&&(0,t.jsx)("span",{children:(0,h.T)(z.shipping_address)}),(null==z?void 0:z.customer_contact)&&(0,t.jsx)("span",{children:en})]})]})]})]})})}OrderDetailsPage.authenticate={permissions:w.ce},OrderDetailsPage.Layout=j.Z}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9709,1247,9494,5535,8186,1285,1631,7556,6779,9774,2888,179],function(){return e(e.s=84034)}),_N_E=e.O()}]);