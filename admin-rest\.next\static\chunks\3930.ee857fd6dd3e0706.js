"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3930,2920],{33419:function(e,t,s){s.r(t);var l=s(85893),a=s(25675),n=s.n(a),r=s(99494),i=s(60942),c=s(11163),u=s(93967),o=s.n(u),d=s(36805),m=s(10265),x=s(3160),f=s(16720),p=s(77556),h=s(67421),b=s(41609),v=s.n(b);function SoldProductCard(e){var t,s;let{product:a}=e,{name:u,image:d,product_type:p,price:h,max_price:b,min_price:v,sale_price:g,actual_rating:y,description:j,type_slug:w}=null!=a?a:{},N=(0,c.useRouter)(),{locale:P}=N,{data:S}=(0,f.F2)({slug:w,language:P}),{price:T,basePrice:C}=(0,i.ZP)({amount:g||h,baseAmount:null!=h?h:0}),{price:Q}=(0,i.ZP)({amount:null!=v?v:0}),{price:M}=(0,i.ZP)({amount:null!=b?b:0});return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"flex aspect-square w-full items-center justify-center overflow-hidden rounded-xl border border-border-200/60 2xl:aspect-[1/0.88]",children:(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:o()("relative w-52 sm:w-80 md:w-96 lg:w-48 xl:w-72 2xl:w-80",(null==S?void 0:null===(t=S.settings)||void 0===t?void 0:t.productCard)==="radon"?"aspect-[2.5/3.6]":"aspect-square"),children:(0,l.jsx)(n(),{alt:u,src:null!==(s=null==d?void 0:d.original)&&void 0!==s?s:r.siteSettings.product.placeholder,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw"})})})}),(0,l.jsxs)("div",{className:"flex items-start justify-between pt-4",children:[(0,l.jsxs)("div",{className:"w-full max-w-[calc(100%-110px)]",children:[(0,l.jsx)("h4",{className:"mb-1.5 truncate text-base font-semibold text-heading",children:u}),(0,l.jsx)("p",{className:"mb-3 text-sm font-normal text-gray-500 truncate",children:j}),p===m.kv.Variable?(0,l.jsxs)("div",{className:"block",children:[(0,l.jsx)("span",{className:"text-base font-semibold text-heading/80",children:Q}),(0,l.jsx)("span",{children:" - "}),(0,l.jsx)("span",{className:"text-base font-semibold text-heading/80",children:M})]}):(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-base font-semibold text-heading/80",children:T}),C&&(0,l.jsx)("del",{className:"text-xs text-muted ms-2 md:text-base",children:C})]})]}),(0,l.jsx)("div",{className:"pt-1.5",children:(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[[void 0,void 0,void 0,void 0,void 0].map((e,t)=>t<Math.round(y)?(0,l.jsx)(x.r,{className:"w-3.5 text-yellow-500"},t):(0,l.jsx)(x.r,{className:"w-3.5 text-gray-300"},t))," "]})})]})]})}t.default=e=>{let{products:t,title:s,className:a}=e,{t:n}=(0,h.$G)();return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:o()("overflow-hidden rounded-lg bg-white p-6 md:p-7",a),children:[(0,l.jsx)("div",{className:"mb-5 mt-1.5 flex items-center justify-between md:mb-7",children:(0,l.jsx)("h3",{className:"before:content-'' relative mt-1 bg-light text-lg font-semibold text-heading before:absolute before:-top-px before:h-7 before:w-1 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-6 rtl:before:-right-6 md:before:-top-0.5 md:ltr:before:-left-7 md:rtl:before:-right-7 lg:before:h-8",children:n(s)})}),v()(t)?(0,l.jsx)("div",{className:"flex h-[calc(100%-60px)] items-center justify-center",children:(0,l.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,l.jsx)(p.m,{className:"w-52"}),(0,l.jsx)("div",{className:"pt-6 mb-1 text-base font-semibold text-heading",children:n("table:empty-table-data")}),(0,l.jsx)("p",{className:"text-[13px]",children:n("table:empty-table-sorry-text")})]})}):(0,l.jsx)(d.tq,{id:"sold-products-gallery",modules:[d.tl],pagination:{clickable:!0},spaceBetween:0,slidesPerView:1,children:null==t?void 0:t.map(e=>(0,l.jsx)(d.o5,{children:(0,l.jsx)(SoldProductCard,{product:e})},"sold-gallery-".concat(e.id)))})]})})}},3160:function(e,t,s){s.d(t,{r:function(){return StarIcon}});var l=s(85893);let StarIcon=e=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25.056 24",...e,children:(0,l.jsx)("g",{"data-name":"Group 36413",fill:"currentColor",children:(0,l.jsx)("path",{id:"Path_22667","data-name":"Path 22667",d:"M19.474,34.679l-6.946-4.346L5.583,34.679a.734.734,0,0,1-1.1-.8L6.469,25.93.263,20.668a.735.735,0,0,1,.421-1.3l8.1-.566,3.064-7.6a.765.765,0,0,1,1.362,0l3.064,7.6,8.1.566a.735.735,0,0,1,.421,1.3L18.588,25.93l1.987,7.949a.734.734,0,0,1-1.1.8Z",transform:"translate(0 -10.792)"})})})},36805:function(e,t,s){s.d(t,{W_:function(){return l.W_},o5:function(){return a.o5},tl:function(){return l.tl},tq:function(){return a.tq}}),s(60439),s(7025),s(19360),s(56161);var l=s(99304),a=s(2261)},16720:function(e,t,s){s.d(t,{jT:function(){return useCreateTypeMutation},e7:function(){return useDeleteTypeMutation},F2:function(){return useTypeQuery},qs:function(){return useTypesQuery},oy:function(){return useUpdateTypeMutation}});var l=s(11163),a=s.n(l),n=s(88767),r=s(22920),i=s(5233),c=s(97514),u=s(47869),o=s(55191),d=s(3737);let m={...(0,o.h)(u.P.TYPES),all:e=>{let{name:t,...s}=e;return d.eN.get(u.P.TYPES,{searchJoin:"and",...s,search:d.eN.formatSearchParams({name:t})})}};var x=s(93345);let useCreateTypeMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,n.useQueryClient)();return(0,n.useMutation)(m.create,{onSuccess:()=>{a().push(c.Z.type.list,void 0,{locale:x.Config.defaultLanguage}),r.Am.success(e("common:successfully-created"))},onSettled:()=>{t.invalidateQueries(u.P.TYPES)}})},useDeleteTypeMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,i.$G)();return(0,n.useMutation)(m.delete,{onSuccess:()=>{r.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(u.P.TYPES)}})},useUpdateTypeMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useRouter)(),s=(0,n.useQueryClient)();return(0,n.useMutation)(m.update,{onSuccess:async s=>{let l=t.query.shop?"/".concat(t.query.shop).concat(c.Z.type.list):c.Z.type.list;await t.push("".concat(l,"/").concat(null==s?void 0:s.slug,"/edit"),void 0,{locale:x.Config.defaultLanguage}),r.Am.success(e("common:successfully-updated"))},onSettled:()=>{s.invalidateQueries(u.P.TYPES)}})},useTypeQuery=e=>{let{slug:t,language:s}=e;return(0,n.useQuery)([u.P.TYPES,{slug:t,language:s}],()=>m.get({slug:t,language:s}))},useTypesQuery=e=>{let{data:t,isLoading:s,error:l}=(0,n.useQuery)([u.P.TYPES,e],e=>{let{queryKey:t,pageParam:s}=e;return m.all(Object.assign({},t[1],s))},{keepPreviousData:!0});return{types:null!=t?t:[],loading:s,error:l}}}}]);