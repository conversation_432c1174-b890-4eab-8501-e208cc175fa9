(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2423],{78354:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/taxes",function(){return s(22782)}])},92072:function(e,t,s){"use strict";var a=s(85893),l=s(93967),r=s.n(l),n=s(98388);t.Z=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,n.m6)(r()("rounded bg-light p-5 shadow md:p-8",t)),...s})}},35484:function(e,t,s){"use strict";var a=s(85893),l=s(93967),r=s.n(l),n=s(98388);t.Z=e=>{let{title:t,className:s,...l}=e;return(0,a.jsx)("h2",{className:(0,n.m6)(r()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",s)),...l,children:t})}},37912:function(e,t,s){"use strict";var a=s(85893),l=s(5114),r=s(80287),n=s(93967),i=s.n(n),o=s(67294),c=s(87536),d=s(5233),u=s(98388);t.Z=e=>{let{className:t,onSearch:s,variant:n="outline",shadow:x=!1,inputClassName:m,placeholderText:h,...f}=e,{register:b,handleSubmit:p,watch:g,reset:j,formState:{errors:w}}=(0,c.cI)({defaultValues:{searchText:""}}),y=g("searchText"),{t:v}=(0,d.$G)();(0,o.useEffect)(()=>{y||s({searchText:""})},[y]);let N=i()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===n,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===n,"border border-border-base focus:border-accent":"outline"===n},{"focus:shadow":x},m);return(0,a.jsxs)("form",{noValidate:!0,role:"search",className:(0,u.m6)(i()("relative flex w-full items-center",t)),onSubmit:p(s),children:[(0,a.jsx)("label",{htmlFor:"search",className:"sr-only",children:v("form:input-label-search")}),(0,a.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,a.jsx)(r.W,{className:"h-5 w-5"})}),(0,a.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,u.m6)(N),placeholder:null!=h?h:v("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...f}),w.searchText&&(0,a.jsx)("p",{children:w.searchText.message}),!!y&&(0,a.jsx)("button",{type:"button",onClick:function(){j(),s({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,a.jsx)(l.T,{className:"h-5 w-5"})})]})}},97670:function(e,t,s){"use strict";s.r(t);var a=s(85893),l=s(78985),r=s(79362),n=s(8144),i=s(74673),o=s(99494),c=s(5233),d=s(1631),u=s(11163),x=s(48583),m=s(93967),h=s.n(m),f=s(30824),b=s(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:s}=(0,c.$G)(),[l,n]=(0,x.KO)(r.Hf),{childMenu:i}=t,{width:o}=(0,b.Z)();return(0,a.jsx)("div",{className:"space-y-2",children:null==i?void 0:i.map(e=>{let{href:t,label:n,icon:i,childMenu:c}=e;return(0,a.jsx)(d.Z,{href:t,label:s(n),icon:i,childMenu:c,miniSidebar:l&&o>=r.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,c.$G)(),[s,l]=(0,x.KO)(r.Hf),n=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,i=Object.keys(n),{width:d}=(0,b.Z)();return(0,a.jsx)(a.Fragment,{children:null==i?void 0:i.map((e,l)=>{var i;return(0,a.jsxs)("div",{className:h()("flex flex-col px-5",s&&d>=r.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,a.jsx)("div",{className:h()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",s&&d>=r.h2?"hidden":""),children:t(null===(i=n[e])||void 0===i?void 0:i.label)}),(0,a.jsx)(SidebarItemMap,{menuItems:n[e]})]},l)})})};t.default=e=>{let{children:t}=e,{locale:s}=(0,u.useRouter)(),[o,c]=(0,x.KO)(r.Hf),[d]=(0,x.KO)(r.GH),[m]=(0,x.KO)(r.W4),{width:p}=(0,b.Z)();return(0,a.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===s||"he"===s?"rtl":"ltr",children:[(0,a.jsx)(l.Z,{}),(0,a.jsx)(i.Z,{children:(0,a.jsx)(SideBarGroup,{})}),(0,a.jsxs)("div",{className:"flex flex-1",children:[(0,a.jsx)("aside",{className:h()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",p>=r.h2&&(d||m)?"lg:pt-[8.75rem]":"pt-20",o&&p>=r.h2?"lg:w-24":"lg:w-76"),children:(0,a.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,a.jsx)(f.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,a.jsx)(SideBarGroup,{})})})}),(0,a.jsxs)("main",{className:h()("relative flex w-full flex-col justify-start transition-[padding] duration-300",p>=r.h2&&(d||m)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",o&&p>=r.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,a.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,a.jsx)(n.Z,{})]})]})]})}},78998:function(e,t,s){"use strict";s.d(t,{Z:function(){return title_with_sort}});var a=s(85893),l=s(93967),r=s.n(l);s(67294);let TriangleArrowDown=e=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,a.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,a.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:t,ascending:s,isActive:l=!0,className:n}=e;return(0,a.jsxs)("span",{className:r()("inline-flex items-center",n),children:[(0,a.jsx)("span",{title:"Sort by ".concat(t),children:t}),s?(0,a.jsx)(TriangleArrowUp,{width:"9",className:r()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":l})}):(0,a.jsx)(TriangleArrowDown,{width:"9",className:r()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":l})})]})}},32232:function(e,t,s){"use strict";s.d(t,{wo:function(){return useCreateTaxClassMutation},MA:function(){return useDeleteTaxMutation},io:function(){return useTaxQuery},sQ:function(){return useTaxesQuery},y2:function(){return useUpdateTaxClassMutation}});var a=s(97514),l=s(11163),r=s(88767),n=s(47869),i=s(22920),o=s(5233),c=s(55191),d=s(3737);let u={...(0,c.h)(n.P.TAXES),get(e){let{id:t}=e;return d.eN.get("".concat(n.P.TAXES,"/").concat(t))},paginated:e=>{let{name:t,...s}=e;return d.eN.get(n.P.TAXES,{searchJoin:"and",...s,search:d.eN.formatSearchParams({name:t})})},all:e=>{let{name:t,...s}=e;return d.eN.get(n.P.TAXES,{searchJoin:"and",...s,search:d.eN.formatSearchParams({name:t})})}},useCreateTaxClassMutation=()=>{let e=(0,r.useQueryClient)(),t=(0,l.useRouter)(),{t:s}=(0,o.$G)();return(0,r.useMutation)(u.create,{onSuccess:()=>{t.push(a.Z.tax.list),i.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(n.P.TAXES)}})},useDeleteTaxMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,o.$G)();return(0,r.useMutation)(u.delete,{onSuccess:()=>{i.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(n.P.TAXES)}})},useUpdateTaxClassMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,r.useQueryClient)();return(0,r.useMutation)(u.update,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(n.P.TAXES)}})},useTaxQuery=e=>(0,r.useQuery)([n.P.TAXES,e],()=>u.get({id:e})),useTaxesQuery=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{data:t,error:s,isLoading:a}=(0,r.useQuery)([n.P.TAXES,e],e=>{let{queryKey:t,pageParam:s}=e;return u.all(Object.assign({},t[1],s))},{keepPreviousData:!0});return{taxes:null!=t?t:[],error:s,loading:a}}},22782:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSG:function(){return v},default:function(){return TaxesPage}});var a=s(85893),l=s(92072),r=s(97670),n=s(37912),i=s(27899),o=s(804),c=s(10265),d=s(97514),u=s(5233),x=s(76518),m=s(67294),h=s(78998),f=s(77556),tax_list=e=>{let{taxes:t,onSort:s,onOrder:l}=e,{t:r}=(0,u.$G)(),{alignLeft:n}=(0,x.S)(),[b,p]=(0,m.useState)({sort:c.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{s(e=>e===c.As.Desc?c.As.Asc:c.As.Desc),l(e),p({sort:b.sort===c.As.Desc?c.As.Asc:c.As.Desc,column:e})}}),g=[{title:(0,a.jsx)(h.Z,{title:r("table:table-item-id"),ascending:b.sort===c.As.Asc&&"id"===b.column,isActive:"id"===b.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:n,width:130,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(r("table:table-item-id"),": ").concat(e)},{title:(0,a.jsx)(h.Z,{title:r("table:table-item-title"),ascending:b.sort===c.As.Asc&&"name"===b.column,isActive:"name"===b.column}),className:"cursor-pointer",dataIndex:"name",key:"name",align:n,width:150,onHeaderCell:()=>onHeaderClick("name")},{title:(0,a.jsx)(h.Z,{title:"".concat(r("table:table-item-rate")," (%)"),ascending:b.sort===c.As.Asc&&"rate"===b.column,isActive:"rate"===b.column}),className:"cursor-pointer",dataIndex:"rate",key:"rate",align:"center",onHeaderCell:()=>onHeaderClick("rate")},{title:(0,a.jsx)(h.Z,{title:r("table:table-item-country"),ascending:b.sort===c.As.Asc&&"country"===b.column,isActive:"country"===b.column}),className:"cursor-pointer",dataIndex:"country",key:"country",align:"center",onHeaderCell:()=>onHeaderClick("country")},{title:r("table:table-item-city"),dataIndex:"city",key:"city",align:"center"},{title:r("table:table-item-state"),dataIndex:"state",key:"state",align:"center"},{title:r("table:table-item-zip"),dataIndex:"zip",key:"zip",align:"center"},{title:r("table:table-item-actions"),dataIndex:"id",key:"actions",align:"right",render:e=>(0,a.jsx)(o.Z,{id:e,editUrl:"".concat(d.Z.tax.list,"/edit/").concat(e),deleteModalView:"DELETE_TAX"}),width:200}];return(0,a.jsx)("div",{className:"mb-8 overflow-hidden rounded shadow",children:(0,a.jsx)(i.i,{columns:g,emptyText:()=>(0,a.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,a.jsx)(f.m,{className:"w-52"}),(0,a.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:r("table:empty-table-data")}),(0,a.jsx)("p",{className:"text-[13px]",children:r("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:900}})})},b=s(45957),p=s(61616),g=s(55846),j=s(32232),w=s(16203),y=s(35484),v=!0;function TaxesPage(){let{t:e}=(0,u.$G)(),[t,s]=(0,m.useState)(""),[r,i]=(0,m.useState)("created_at"),[o,x]=(0,m.useState)(c.As.Desc),{taxes:h,loading:f,error:w}=(0,j.sQ)({name:t,orderBy:r,sortedBy:o});return f?(0,a.jsx)(g.Z,{text:e("common:text-loading")}):w?(0,a.jsx)(b.Z,{message:w.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.Z,{className:"mb-8 flex flex-col items-center md:flex-row",children:[(0,a.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,a.jsx)(y.Z,{title:e("form:input-label-taxes")})}),(0,a.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-1/2",children:[(0,a.jsx)(n.Z,{onSearch:function(e){let{searchText:t}=e;s(t)},placeholderText:e("form:input-placeholder-search-name")}),(0,a.jsx)(p.Z,{href:"".concat(d.Z.tax.create),className:"h-12 w-full md:w-auto md:ms-6",children:(0,a.jsxs)("span",{children:["+ ",e("form:button-label-add-tax")]})})]})]}),f?null:(0,a.jsx)(tax_list,{taxes:h,onOrder:i,onSort:x})]})}TaxesPage.authenticate={permissions:w.M$},TaxesPage.Layout=r.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,3186,9494,5535,8186,1285,1631,7556,8504,9774,2888,179],function(){return e(e.s=78354)}),_N_E=e.O()}]);