declare const awsS3Endpoint: {
  'af-south-1': string;
  'ap-east-1': string;
  'ap-south-1': string;
  'ap-south-2': string;
  'ap-southeast-1': string;
  'ap-southeast-2': string;
  'ap-southeast-3': string;
  'ap-southeast-4': string;
  'ap-southeast-5': string;
  'ap-northeast-1': string;
  'ap-northeast-2': string;
  'ap-northeast-3': string;
  'ca-central-1': string;
  'ca-west-1': string;
  'cn-north-1': string;
  'eu-central-1': string;
  'eu-central-2': string;
  'eu-north-1': string;
  'eu-south-1': string;
  'eu-south-2': string;
  'eu-west-1': string;
  'eu-west-2': string;
  'eu-west-3': string;
  'il-central-1': string;
  'me-central-1': string;
  'me-south-1': string;
  'sa-east-1': string;
  'us-east-1': string;
  'us-east-2': string;
  'us-west-1': string;
  'us-west-2': string;
  'us-gov-east-1': string;
  'us-gov-west-1': string;
};
export type Region = keyof typeof awsS3Endpoint | string;
export declare function getS3Endpoint(region: Region): string;
export {};