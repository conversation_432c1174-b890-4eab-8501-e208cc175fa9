"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TotalYearSaleByMonth = void 0;
const openapi = require("@nestjs/swagger");
const sequelize_typescript_1 = require("sequelize-typescript");
const analytics_entity_1 = require("./analytics.entity");
let TotalYearSaleByMonth = class TotalYearSaleByMonth extends sequelize_typescript_1.Model {
    static _OPENAPI_METADATA_FACTORY() {
        return { total: { required: false, type: () => Number }, month: { required: false, type: () => String }, analyticsId: { required: true, type: () => Number } };
    }
};
__decorate([
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.FLOAT),
    __metadata("design:type", Number)
], TotalYearSaleByMonth.prototype, "total", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)(sequelize_typescript_1.DataType.STRING),
    __metadata("design:type", String)
], TotalYearSaleByMonth.prototype, "month", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => analytics_entity_1.Analytics),
    sequelize_typescript_1.Column,
    __metadata("design:type", Number)
], TotalYearSaleByMonth.prototype, "analyticsId", void 0);
TotalYearSaleByMonth = __decorate([
    sequelize_typescript_1.Table
], TotalYearSaleByMonth);
exports.TotalYearSaleByMonth = TotalYearSaleByMonth;
//# sourceMappingURL=total-year-sale-by-month.entity.js.map