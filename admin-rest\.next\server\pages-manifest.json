{"/_app": "pages/_app.js", "/_document": "pages/_document.js", "/_error": "pages/_error.js", "/[shop]/attributes/create": "pages/[shop]/attributes/create.js", "/[shop]/authors/create": "pages/[shop]/authors/create.js", "/[shop]/attributes": "pages/[shop]/attributes.js", "/[shop]/attributes/[attributeId]/[action]": "pages/[shop]/attributes/[attributeId]/[action].js", "/[shop]/coupons/create": "pages/[shop]/coupons/create.js", "/[shop]/coupons/[couponSlug]/[action]": "pages/[shop]/coupons/[couponSlug]/[action].js", "/[shop]/faqs/[id]/[action]": "pages/[shop]/faqs/[id]/[action].js", "/[shop]/edit": "pages/[shop]/edit.js", "/[shop]/faqs/create": "pages/[shop]/faqs/create.js", "/[shop]/flash-sale/[slug]": "pages/[shop]/flash-sale/[slug].js", "/[shop]/flash-sale": "pages/[shop]/flash-sale.js", "/[shop]/authors": "pages/[shop]/authors.js", "/[shop]/faqs": "pages/[shop]/faqs.js", "/[shop]/coupons": "pages/[shop]/coupons.js", "/[shop]/flash-sale/vendor-request/[id]/[action]": "pages/[shop]/flash-sale/vendor-request/[id]/[action].js", "/[shop]/flash-sale/vendor-request/create": "pages/[shop]/flash-sale/vendor-request/create.js", "/[shop]/flash-sale/my-products": "pages/[shop]/flash-sale/my-products.js", "/[shop]/flash-sale/vendor-request/[id]": "pages/[shop]/flash-sale/vendor-request/[id].js", "/[shop]/manufacturers/create": "pages/[shop]/manufacturers/create.js", "/[shop]/flash-sale/vendor-request": "pages/[shop]/flash-sale/vendor-request.js", "/[shop]/manufacturers": "pages/[shop]/manufacturers.js", "/[shop]/orders/transaction": "pages/[shop]/orders/transaction.js", "/[shop]": "pages/[shop].js", "/[shop]/orders": "pages/[shop]/orders.js", "/[shop]/products/[productSlug]/[action]": "pages/[shop]/products/[productSlug]/[action].js", "/[shop]/products/create": "pages/[shop]/products/create.js", "/[shop]/products/draft": "pages/[shop]/products/draft.js", "/[shop]/products/inventory": "pages/[shop]/products/inventory.js", "/[shop]/products": "pages/[shop]/products.js", "/[shop]/orders/[orderId]": "pages/[shop]/orders/[orderId].js", "/[shop]/products/product-stock": "pages/[shop]/products/product-stock.js", "/[shop]/questions": "pages/[shop]/questions.js", "/[shop]/refunds": "pages/[shop]/refunds.js", "/[shop]/refunds/[refundId]": "pages/[shop]/refunds/[refundId].js", "/[shop]/staffs/create": "pages/[shop]/staffs/create.js", "/[shop]/reviews": "pages/[shop]/reviews.js", "/[shop]/store-notices/[id]/[action]": "pages/[shop]/store-notices/[id]/[action].js", "/[shop]/staffs": "pages/[shop]/staffs.js", "/[shop]/store-notices/create": "pages/[shop]/store-notices/create.js", "/[shop]/terms-and-conditions/[termSlug]": "pages/[shop]/terms-and-conditions/[termSlug].js", "/[shop]/terms-and-conditions/[termSlug]/[action]": "pages/[shop]/terms-and-conditions/[termSlug]/[action].js", "/[shop]/store-notices": "pages/[shop]/store-notices.js", "/[shop]/store-notices/[id]": "pages/[shop]/store-notices/[id].js", "/[shop]/terms-and-conditions/create": "pages/[shop]/terms-and-conditions/create.js", "/[shop]/withdraws/create": "pages/[shop]/withdraws/create.js", "/attributes/[attributeId]/[action]": "pages/attributes/[attributeId]/[action].js", "/[shop]/terms-and-conditions": "pages/[shop]/terms-and-conditions.js", "/[shop]/transfer-ownership": "pages/[shop]/transfer-ownership.js", "/[shop]/withdraws": "pages/[shop]/withdraws.js", "/authors/create": "pages/authors/create.js", "/authors/[authorSlug]/[action]": "pages/authors/[authorSlug]/[action].js", "/attributes": "pages/attributes.js", "/become-seller": "pages/become-seller.js", "/authors": "pages/authors.js", "/categories/[categorySlug]/[action]": "pages/categories/[categorySlug]/[action].js", "/categories/create": "pages/categories/create.js", "/coupons/create": "pages/coupons/create.js", "/coupons/[couponSlug]/[action]": "pages/coupons/[couponSlug]/[action].js", "/faqs/create": "pages/faqs/create.js", "/faqs/[id]/[action]": "pages/faqs/[id]/[action].js", "/coupons": "pages/coupons.js", "/categories": "pages/categories.js", "/flash-sale/[slug]/[action]": "pages/flash-sale/[slug]/[action].js", "/flash-sale/create": "pages/flash-sale/create.js", "/flash-sale/[slug]": "pages/flash-sale/[slug].js", "/faqs": "pages/faqs.js", "/flash-sale/vendor-request/create": "pages/flash-sale/vendor-request/create.js", "/flash-sale/vendor-request/[id]": "pages/flash-sale/vendor-request/[id].js", "/flash-sale/vendor-request/[id]/[action]": "pages/flash-sale/vendor-request/[id]/[action].js", "/forgot-password": "pages/forgot-password.js", "/flash-sale": "pages/flash-sale.js", "/groups/[groupSlug]/[action]": "pages/groups/[groupSlug]/[action].js", "/groups/create": "pages/groups/create.js", "/flash-sale/vendor-request": "pages/flash-sale/vendor-request.js", "/login": "pages/login.js", "/logout": "pages/logout.js", "/": "pages/index.js", "/manufacturers/[manufacturerSlug]/[action]": "pages/manufacturers/[manufacturerSlug]/[action].js", "/groups": "pages/groups.js", "/manufacturers/create": "pages/manufacturers/create.js", "/message/[id]": "pages/message/[id].js", "/message": "pages/message.js", "/my-shop": "pages/my-shop.js", "/my-shops": "pages/my-shops.js", "/notice": "pages/notice.js", "/manufacturers": "pages/manufacturers.js", "/new-shops": "pages/new-shops.js", "/orders/checkout": "pages/orders/checkout.js", "/notify-logs/user/[id]": "pages/notify-logs/user/[id].js", "/notify-logs": "pages/notify-logs.js", "/orders/create": "pages/orders/create.js", "/orders/[orderId]": "pages/orders/[orderId].js", "/owner-message": "pages/owner-message.js", "/orders/transaction": "pages/orders/transaction.js", "/orders": "pages/orders.js", "/products/[productSlug]/[action]": "pages/products/[productSlug]/[action].js", "/products/draft": "pages/products/draft.js", "/products/inventory": "pages/products/inventory.js", "/products": "pages/products.js", "/profile-update": "pages/profile-update.js", "/products/product-stock": "pages/products/product-stock.js", "/questions": "pages/questions.js", "/refund-policies/[refundPolicySlug]/[action]": "pages/refund-policies/[refundPolicySlug]/[action].js", "/refund-policies/create": "pages/refund-policies/create.js", "/refund-policies/[refundPolicySlug]": "pages/refund-policies/[refundPolicySlug].js", "/refund-reasons/[refundReasonSlug]/[action]": "pages/refund-reasons/[refundReasonSlug]/[action].js", "/refund-reasons/create": "pages/refund-reasons/create.js", "/refund-policies": "pages/refund-policies.js", "/refund-reasons": "pages/refund-reasons.js", "/refunds/[refundId]": "pages/refunds/[refundId].js", "/register": "pages/register.js", "/reviews/[reviewId]": "pages/reviews/[reviewId].js", "/refunds": "pages/refunds.js", "/reviews": "pages/reviews.js", "/settings/company-information": "pages/settings/company-information.js", "/settings/events": "pages/settings/events.js", "/settings": "pages/settings.js", "/settings/maintenance": "pages/settings/maintenance.js", "/settings/payment": "pages/settings/payment.js", "/settings/promotion-popup": "pages/settings/promotion-popup.js", "/settings/seo": "pages/settings/seo.js", "/settings/shop": "pages/settings/shop.js", "/shippings/create": "pages/shippings/create.js", "/shop-message/[id]": "pages/shop-message/[id].js", "/shippings/edit/[id]": "pages/shippings/edit/[id].js", "/shops/create": "pages/shops/create.js", "/shippings": "pages/shippings.js", "/shop-transfer/[transaction_identifier]": "pages/shop-transfer/[transaction_identifier].js", "/shop-transfer": "pages/shop-transfer.js", "/store-notices/create": "pages/store-notices/create.js", "/store-notices/[id]/[action]": "pages/store-notices/[id]/[action].js", "/shops": "pages/shops.js", "/store-notices/[id]": "pages/store-notices/[id].js", "/tags/[tagSlug]/[action]": "pages/tags/[tagSlug]/[action].js", "/tags/create": "pages/tags/create.js", "/store-notices": "pages/store-notices.js", "/taxes/create": "pages/taxes/create.js", "/shop-transfer/vendor": "pages/shop-transfer/vendor.js", "/shop-transfer/vendor/[transaction_identifier]": "pages/shop-transfer/vendor/[transaction_identifier].js", "/taxes/edit/[id]": "pages/taxes/edit/[id].js", "/tags": "pages/tags.js", "/terms-and-conditions/[termSlug]/[action]": "pages/terms-and-conditions/[termSlug]/[action].js", "/taxes": "pages/taxes.js", "/terms-and-conditions/[termSlug]": "pages/terms-and-conditions/[termSlug].js", "/terms-and-conditions/create": "pages/terms-and-conditions/create.js", "/users/create": "pages/users/create.js", "/terms-and-conditions": "pages/terms-and-conditions.js", "/users/customer": "pages/users/customer.js", "/users/admins": "pages/users/admins.js", "/users": "pages/users.js", "/users/my-staffs": "pages/users/my-staffs.js", "/users/vendor-staffs": "pages/users/vendor-staffs.js", "/users/vendors": "pages/users/vendors.js", "/users/vendors/pending": "pages/users/vendors/pending.js", "/verify-email": "pages/verify-email.js", "/verify-license": "pages/verify-license.js", "/withdraws": "pages/withdraws.js", "/withdraws/[withdrawId]": "pages/withdraws/[withdrawId].js", "/en/404": "pages/en/404.html", "/en/500": "pages/en/500.html"}