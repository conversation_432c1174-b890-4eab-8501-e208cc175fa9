(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4781],{48005:function(e,l,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/store-notices/[id]/[action]",function(){return i(9241)}])},47133:function(e,l,i){"use strict";var t=i(85893),s=i(78985),r=i(79362),n=i(11163),o=i(16203),d=i(1631),a=i(99494),u=i(5233),c=i(74673),p=i(8144),h=i(90573),f=i(48583),m=i(93967),v=i.n(m),x=i(30824),g=i(62964);let SidebarItemMap=e=>{var l,i;let s,a,{menuItems:c}=e,{locale:p}=(0,n.useRouter)(),{t:m}=(0,u.$G)(),{settings:v}=(0,h.n)({language:p}),{childMenu:x}=c,b=null==v?void 0:null===(l=v.options)||void 0===l?void 0:l.enableTerms,j=null==v?void 0:null===(i=v.options)||void 0===i?void 0:i.enableCoupons,{permissions:N}=(0,o.WA)(),[S,w]=(0,f.KO)(r.Hf),{width:_}=(0,g.Z)(),{query:{shop:Z}}=(0,n.useRouter)();return!b&&(s=null==c?void 0:c.childMenu.find(e=>"Terms And Conditions"===e.label))&&(s.permissions=o.M$),!j&&(a=null==c?void 0:c.childMenu.find(e=>"Coupons"===e.label))&&(a.permissions=o.M$),(0,t.jsx)("div",{className:"space-y-2",children:null==x?void 0:x.map(e=>{let{href:l,label:i,icon:s,permissions:n,childMenu:a}=e;return a||(0,o.Ft)(n,N)?(0,t.jsx)(d.Z,{href:l(null==Z?void 0:Z.toString()),label:m(i),icon:s,childMenu:a,miniSidebar:S&&_>=r.h2},i):null})})},SideBarGroup=()=>{var e,l;let[i,s]=(0,f.KO)(r.Hf),{role:n}=(0,o.WA)(),d="staff"===n?null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.staff:null===a.siteSettings||void 0===a.siteSettings?void 0:null===(l=a.siteSettings.sidebarLinks)||void 0===l?void 0:l.shop,c=Object.keys(d),{width:p}=(0,g.Z)(),{t:h}=(0,u.$G)();return(0,t.jsx)(t.Fragment,{children:null==c?void 0:c.map((e,l)=>{var s;return(0,t.jsxs)("div",{className:v()("flex flex-col px-5",i&&p>=r.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:v()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",i&&p>=r.h2?"hidden":""),children:h(null===(s=d[e])||void 0===s?void 0:s.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:d[e]})]},l)})})};l.Z=e=>{let{children:l}=e,[i,o]=(0,f.KO)(r.Hf),{locale:d}=(0,n.useRouter)(),{width:a}=(0,g.Z)(),[u]=(0,f.KO)(r.GH),[h]=(0,f.KO)(r.W4);return(0,t.jsxs)("div",{className:"flex flex-col min-h-screen transition-colors duration-150 bg-gray-100",dir:"ar"===d||"he"===d?"rtl":"ltr",children:[(0,t.jsx)(s.Z,{}),(0,t.jsx)(c.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:v()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",a>=r.h2&&(u||h)?"pt-[8.75rem]":"pt-20",i&&a>=r.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"w-full h-full overflow-x-hidden sidebar-scrollbar",children:(0,t.jsx)(x.Z,{className:"w-full h-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:v()("relative flex w-full flex-col justify-start transition-[padding] duration-300",a>=r.h2&&(u||h)?"lg:pt-[8.0625rem]":"pt-[3.9375rem] lg:pt-[4.75rem]",i&&a>=r.h2?"ltr:pl-24 rtl:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,t.jsx)(p.Z,{})]})]})]})}},9241:function(e,l,i){"use strict";i.r(l),i.d(l,{__N_SSP:function(){return v},default:function(){return UpdateStoreNoticePage}});var t=i(85893),s=i(47133),r=i(25238),n=i(45957),o=i(55846),d=i(5233),a=i(93345),u=i(24616),c=i(16203),p=i(11163),h=i(97514),f=i(30042),m=i(99930),v=!0;function UpdateStoreNoticePage(){var e,l;let{query:i,locale:s}=(0,p.useRouter)(),{t:v}=(0,d.$G)(),x=(0,p.useRouter)(),{permissions:g}=(0,c.WA)(),{data:b}=(0,m.UE)(),{data:j}=(0,f.DZ)({slug:null==i?void 0:i.shop}),N=null==j?void 0:j.id,{storeNotice:S,loading:w,error:_}=(0,u.Vm)({id:i.id,language:"edit"===i.action.toString()?s:a.Config.defaultLanguage});return w?(0,t.jsx)(o.Z,{text:v("common:text-loading")}):_?(0,t.jsx)(n.Z,{message:_.message}):((0,c.Ft)(c.M$,g)||(null==b?void 0:null===(e=b.shops)||void 0===e?void 0:e.map(e=>e.id).includes(N))||(null==b?void 0:null===(l=b.managed_shop)||void 0===l?void 0:l.id)==N||x.replace(h.Z.dashboard),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-heading",children:v("form:form-title-edit-store-notice")})}),(0,t.jsx)(r.Z,{initialValues:S})]}))}UpdateStoreNoticePage.authenticate={permissions:c.ce},UpdateStoreNoticePage.Layout=s.Z}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,3261,7755,939,1273,9494,5535,8186,1285,1631,5238,9774,2888,179],function(){return e(e.s=48005)}),_N_E=e.O()}]);