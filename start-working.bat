@echo off
echo.
echo ========================================
echo   E-commerce Platform - Working Setup
echo ========================================
echo.

echo [1/4] Stopping existing containers...
docker-compose -f docker-compose.simple.yml down

echo.
echo [2/4] Starting backend services (Docker)...
docker-compose -f docker-compose.simple.yml up -d

echo.
echo [3/4] Waiting for services to be ready...
timeout /t 15 /nobreak > nul

echo.
echo [4/4] Seeding database...
docker-compose -f docker-compose.simple.yml exec api-rest node simple-seed.js

echo.
echo ========================================
echo   Backend Services Ready!
echo ========================================
echo.
echo Backend URLs:
echo   API REST: http://localhost:5000/api
echo   API Docs: http://localhost:5000/docs  
echo   MinIO Console: http://localhost:9001
echo.
echo Credentials:
echo   MinIO: minioadmin / minioadmin123
echo   Admin: <EMAIL> / password
echo.
echo ========================================
echo   Next Steps - Start Frontend Services
echo ========================================
echo.
echo Open 2 new command prompts and run:
echo.
echo   Terminal 1:
echo   cd admin-rest
echo   yarn dev
echo.
echo   Terminal 2:  
echo   cd shop
echo   yarn dev:rest
echo.
echo Frontend URLs (after starting):
echo   Admin Panel: http://localhost:3002
echo   Shop: http://localhost:3003
echo.
echo ========================================
echo   System Status
echo ========================================

docker-compose -f docker-compose.simple.yml ps

echo.
pause
