(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2035],{14306:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/products/draft",function(){return l(45224)}])},97670:function(e,t,l){"use strict";l.r(t);var s=l(85893),r=l(78985),a=l(79362),i=l(8144),n=l(74673),d=l(99494),o=l(5233),c=l(1631),u=l(11163),m=l(48583),x=l(93967),f=l.n(x),h=l(30824),p=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,o.$G)(),[r,i]=(0,m.KO)(a.Hf),{childMenu:n}=t,{width:d}=(0,p.Z)();return(0,s.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:t,label:i,icon:n,childMenu:o}=e;return(0,s.jsx)(c.Z,{href:t,label:l(i),icon:n,childMenu:o,miniSidebar:r&&d>=a.h2},i)})})},SideBarGroup=()=>{var e;let{t}=(0,o.$G)(),[l,r]=(0,m.KO)(a.Hf),i=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(i),{width:c}=(0,p.Z)();return(0,s.jsx)(s.Fragment,{children:null==n?void 0:n.map((e,r)=>{var n;return(0,s.jsxs)("div",{className:f()("flex flex-col px-5",l&&c>=a.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&c>=a.h2?"hidden":""),children:t(null===(n=i[e])||void 0===n?void 0:n.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:i[e]})]},r)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,u.useRouter)(),[d,o]=(0,m.KO)(a.Hf),[c]=(0,m.KO)(a.GH),[x]=(0,m.KO)(a.W4),{width:g}=(0,p.Z)();return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(n.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",g>=a.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-20",d&&g>=a.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,s.jsx)(h.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",g>=a.h2&&(c||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&g>=a.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,s.jsx)(i.Z,{})]})]})]})}},45224:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSG:function(){return S},default:function(){return DraftProductPage}});var s=l(85893),r=l(92072),a=l(35484),i=l(37912),n=l(85634),d=l(46626),o=l(97670),c=l(15724),u=l(145),m=l(45957),x=l(55846),f=l(93242),h=l(99930),p=l(10265),g=l(16203),b=l(93967),j=l.n(b),v=l(5233),w=l(11163),N=l(67294),S=!0;function DraftProductPage(){let{t:e}=(0,v.$G)(),{locale:t}=(0,w.useRouter)(),{data:l}=(0,h.UE)(),[o,g]=(0,N.useState)(""),[b,S]=(0,N.useState)(1),[_,Z]=(0,N.useState)("created_at"),[y,P]=(0,N.useState)(p.As.Desc),[k,O]=(0,N.useState)(""),[G,C]=(0,N.useState)(""),[E,K]=(0,N.useState)(!1),{products:D,paginatorInfo:H,loading:F,error:I}=(0,f.YC)({name:o,limit:10,page:b,orderBy:_,sortedBy:y,status:"draft",user_id:null==l?void 0:l.id,language:t});return F?(0,s.jsx)(x.Z,{text:e("common:text-loading")}):I?(0,s.jsx)(m.Z,{message:I.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(r.Z,{className:"mb-8 flex flex-col",children:[(0,s.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(a.Z,{title:e("form:input-label-products")})}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center ms-auto md:w-2/4",children:(0,s.jsx)(i.Z,{onSearch:function(e){let{searchText:t}=e;g(t),S(1)}})}),(0,s.jsxs)("button",{className:"mt-5 flex items-center whitespace-nowrap text-base font-semibold text-accent md:mt-0 md:ms-5",onClick:()=>{K(e=>!e)},children:[e("common:text-filter")," ",E?(0,s.jsx)(d.a,{className:"ms-2"}):(0,s.jsx)(n.K,{className:"ms-2"})]})]}),(0,s.jsx)("div",{className:j()("flex w-full transition",{"visible h-auto":E,"invisible h-0":!E}),children:(0,s.jsx)("div",{className:"mt-5 flex w-full flex-col border-t border-gray-200 pt-5 md:mt-8 md:flex-row md:items-center md:pt-8",children:(0,s.jsx)(c.Z,{className:"w-full",onCategoryFilter:e=>{let{slug:t}=e;S(1),C(t)},onTypeFilter:e=>{let{slug:t}=e;O(t),S(1)},enableCategory:!0,enableType:!0})})})]}),(0,s.jsx)(u.Z,{products:D,paginatorInfo:H,onPagination:function(e){S(e)},onOrder:Z,onSort:P})]})}DraftProductPage.authenticate={permissions:g.M$},DraftProductPage.Layout=o.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9494,5535,8186,1285,1631,7556,8504,2713,1077,193,9774,2888,179],function(){return e(e.s=14306)}),_N_E=e.O()}]);