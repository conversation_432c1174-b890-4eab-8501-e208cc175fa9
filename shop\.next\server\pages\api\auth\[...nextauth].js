"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/facebook":
/*!***********************************************!*\
  !*** external "next-auth/providers/facebook" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/facebook");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/config/get-env.ts":
/*!*******************************!*\
  !*** ./src/config/get-env.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnv: () => (/* binding */ getEnv),\n/* harmony export */   getEnvOptional: () => (/* binding */ getEnvOptional)\n/* harmony export */ });\nfunction getEnv(name) {\n    const val = process.env[name];\n    if (!val) {\n        throw new Error(`Cannot find environmental variable: ${name}`);\n    }\n    return val;\n}\nfunction getEnvOptional(name, defaultValue = \"\") {\n    const val = process.env[name];\n    return val || defaultValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/config/get-env.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _config_get_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/get-env */ \"(api)/./src/config/get-env.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/facebook */ \"next-auth/providers/facebook\");\n/* harmony import */ var next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// For more information on each option (and a full list of options) go to\n// https://next-auth.js.org/configuration/options\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_1___default()({\n    // https://next-auth.js.org/configuration/providers\n    providers: [\n        // Only enable OAuth providers if credentials are properly configured\n        ...(0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_ID\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_SECRET\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"GOOGLE_CLIENT_ID\") !== \"google_client_id_placeholder\" ? [\n            next_auth_providers_google__WEBPACK_IMPORTED_MODULE_3___default()({\n                clientId: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_ID\"),\n                clientSecret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"GOOGLE_CLIENT_SECRET\")\n            })\n        ] : [],\n        ...(0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_ID\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_SECRET\") && (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnvOptional)(\"FACEBOOK_CLIENT_ID\") !== \"facebook_client_id_placeholder\" ? [\n            next_auth_providers_facebook__WEBPACK_IMPORTED_MODULE_2___default()({\n                clientId: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"FACEBOOK_CLIENT_ID\"),\n                clientSecret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"FACEBOOK_CLIENT_SECRET\")\n            })\n        ] : []\n    ],\n    // The secret should be set to a reasonably long random string.\n    // It is used to sign cookies and to sign and encrypt JSON Web Tokens, unless\n    // a separate secret is defined explicitly for encrypting the JWT.\n    secret: (0,_config_get_env__WEBPACK_IMPORTED_MODULE_0__.getEnv)(\"SECRET\"),\n    session: {\n        // Use JSON Web Tokens for session instead of database sessions.\n        // This option can be used with or without a database for users/accounts.\n        // Note: `jwt` is automatically set to `true` if no database is specified.\n        strategy: \"jwt\"\n    },\n    // JSON Web tokens are only used for sessions if the `jwt: true` session\n    // option is set - or by default if no database is specified.\n    // https://next-auth.js.org/configuration/options#jwt\n    jwt: {\n    },\n    // You can define custom pages to override the built-in ones. These will be regular Next.js pages\n    // so ensure that they are placed outside of the '/api' folder, e.g. signIn: '/auth/mycustom-signin'\n    // The Routes shown here are the default URLs that will be used when a custom\n    // pages is not specified for that route.\n    // https://next-auth.js.org/configuration/pages\n    pages: {\n    },\n    // Callbacks are asynchronous functions you can use to control what happens\n    // when an action is performed.\n    // https://next-auth.js.org/configuration/callbacks\n    callbacks: {\n        // async signIn({ account, profile, user}) {\n        // \tif (account.provider === \"google\") {\n        //     return profile.email_verified && profile?.email?.endsWith(\"@gmail.com\")\n        //   }\n        //   return true // Return true to allow sign in\n        // },\n        async jwt ({ token, account }) {\n            if (account) {\n                const { access_token, provider } = account;\n                token.provider = provider;\n                // reform the `token` object from the access token we appended to the `user` object\n                token.access_token = access_token;\n            }\n            return token;\n        },\n        async session ({ session, token, user }) {\n            const { access_token, provider } = token;\n            //@ts-ignore\n            session.provider = provider;\n            //@ts-ignore\n            session.access_token = access_token;\n            return session;\n        }\n    },\n    // Events are useful for logging\n    // https://next-auth.js.org/configuration/events\n    events: {}\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();