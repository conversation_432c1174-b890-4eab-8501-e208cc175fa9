{"version": 3, "file": "TextDecoder.js", "sourceRoot": "", "sources": ["../../../src/common/TextDecoder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,8BAA8B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAEnF,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAWlC;;;;;GAKG;AACH,MAAM,OAAO,WAAW;IAYtB,YAAY,KAAc,EAAE,OAA4B;QAEtD,KAAK,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QAC/D,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,4DAA4D;QAC5D,2DAA2D;QAC3D,gEAAgE;QAChE,oCAAoC;QAEpC,eAAe;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,gCAAgC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,+BAA+B;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,8BAA8B;QAC9B,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC;QACjC,+BAA+B;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAG3B,4DAA4D;QAC5D,SAAS;QACT,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAEpC,gEAAgE;QAChE,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa;YACtD,MAAM,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,KAAK,CAAC,sBAAsB;gBAChC,uDAAuD,CAAC,CAAC;SAC5D;QAED,0CAA0C;QAC1C,oBAAoB;QACpB,gDAAgD;QAChD,2DAA2D;QAE3D,qCAAqC;QACrC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,gEAAgE;QAChE,SAAS;QACT,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAE7B,iEAAiE;QACjE,QAAQ;QACR,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEzB,wBAAwB;QACxB,gCAAgC;QAChC,sDAAsD;QACtD,8CAA8C;QAC9C,qCAAqC;QACrC,IAAI;QAEJ,iBAAiB;QACjB,cAAc;IAChB,CAAC;IAED,+BAA+B;IAC/B,+DAA+D;IAC/D,+DAA+D;IAC/D,iCAAiC;IACjC,qEAAqE;IACrE,QAAQ;IACR,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3C,CAAC;IAED,8DAA8D;IAC9D,iCAAiC;IACjC,4DAA4D;IAC5D,iCAAiC;IACjC,gEAAgE;IAChE,QAAQ;IACR,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;IACtC,CAAC;IAED,8DAA8D;IAC9D,wCAAwC;IACxC,gEAAgE;IAChE,iCAAiC;IACjC,mDAAmD;IACnD,QAAQ;IACR,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IACD,IAAI;IAEJ;;;;OAIG;IACH,MAAM,CAAC,KAAoD,EAAE,OAAuB;QAElF,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEvC,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAEzC,6DAA6D;QAC7D,gEAAgE;QAChE,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5C,KAAK,EAAE,IAAI,CAAC,WAAW,KAAK,OAAO;aACpC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;QAED,iEAAiE;QACjE,yCAAyC;QACzC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEnD,wDAAwD;QACxD,iEAAiE;QACjE,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;QAEvC,iCAAiC;QACjC,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,wCAAwC;QACxC,IAAI,MAAyB,CAAC;QAE9B,iBAAiB;QACjB,OAAO,IAAI,EAAE;YACX,qDAAqD;YACrD,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;YAElC,4DAA4D;YAC5D,kCAAkC;YAClC,mCAAmC;YACnC,IAAI,KAAK,KAAK,aAAa;gBACzB,MAAM;YAER,uCAAuC;YAEvC,+DAA+D;YAC/D,kCAAkC;YAClC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAEpD,uDAAuD;YACvD,IAAI,MAAM,KAAK,QAAQ;gBACrB,MAAM;YAER,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,4BAA4B,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC;;oBAEhE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACvB;YAED,uDAAuD;YACvD,sBAAsB;YAEtB,4BAA4B;SAC7B;QACD,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,GAAG;gBACD,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClE,IAAI,MAAM,KAAK,QAAQ;oBACrB,MAAM;gBACR,IAAI,CAAC,MAAM;oBACT,SAAS;gBACX,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,4BAA4B,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC;;oBAEhE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACvB,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE;YACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED,+DAA+D;IAC/D,eAAe;IACf;;;;OAIG;IACK,eAAe,CAAC,MAAqB;QAC3C,qDAAqD;QACrD,oDAAoD;QAEpD,6DAA6D;QAC7D,+DAA+D;QAC/D,IAAI,QAAQ,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClE,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACpC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;gBAC7C,4CAA4C;gBAC5C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,MAAM,CAAC,KAAK,EAAE,CAAC;aAChB;iBAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC5B,4DAA4D;gBAC5D,mCAAmC;gBACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;aACtB;iBAAM;gBACL,4DAA4D;gBAC5D,aAAa;gBACb,UAAU;aACX;SACF;QACD,+BAA+B;QAC/B,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;CACF;AAED,SAAS,gBAAgB,CAAC,KAAU;IAClC,IAAI;QACF,OAAO,KAAK,YAAY,WAAW,CAAA;KACpC;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAmD;IAE5E,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAExD,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;QAC3B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;KAC9B;IAED,IAAI,QAAQ,IAAI,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QACvD,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;KACzE;IAED,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC"}