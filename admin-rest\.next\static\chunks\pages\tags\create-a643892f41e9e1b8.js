(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7097,2036],{32096:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/tags/create",function(){return r(76534)}])},76534:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return u},default:function(){return CreateCategoriesPage}});var n=r(85893),a=r(5233),s=r(97670),i=r(40796),o=r(16203),u=!0;function CreateCategoriesPage(){let{t:e}=(0,a.$G)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex border-b border-dashed border-gray-300 pb-5 md:pb-7",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:e("form:button-label-add-tag")})}),(0,n.jsx)(i.Z,{})]})}CreateCategoriesPage.authenticate={permissions:o.M$},CreateCategoriesPage.Layout=s.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,9494,5535,8186,1285,1631,5468,1737,9434,9774,2888,179],function(){return e(e.s=32096)}),_N_E=e.O()}]);