"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5051],{75051:function(e,u,t){t.r(u);var a=t(85893),n=t(71421),r=t(75814),s=t(61571);u.default=()=>{let{mutate:e,isLoading:u}=(0,s.ty)(),{data:t}=(0,r.X9)(),{closeModal:c}=(0,r.SO)();return(0,a.jsx)(n.Z,{onCancel:c,onDelete:function(){e({id:t}),c()},deleteBtnLoading:u})}},61571:function(e,u,t){t.d(u,{M5:function(){return useCreateManufacturerMutation},ty:function(){return useDeleteManufacturerMutation},a7:function(){return useManufacturerQuery},ML:function(){return useManufacturersQuery},pN:function(){return useUpdateManufacturerMutation},TN:function(){return useUpdateManufacturerMutationInList}});var a=t(11163),n=t.n(a),r=t(88767),s=t(22920),c=t(5233),o=t(97514),i=t(47869),l=t(28597),d=t(55191),f=t(3737);let M={...(0,d.h)(i.P.MANUFACTURERS),paginated:e=>{let{name:u,shop_id:t,...a}=e;return f.eN.get(i.P.MANUFACTURERS,{searchJoin:"and",...a,search:f.eN.formatSearchParams({name:u,shop_id:t})})}};var y=t(93345);let useCreateManufacturerMutation=()=>{let e=(0,r.useQueryClient)(),{t:u}=(0,c.$G)(),t=(0,a.useRouter)();return(0,r.useMutation)(M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(o.Z.manufacturer.list):o.Z.manufacturer.list;await n().push(e,void 0,{locale:y.Config.defaultLanguage}),s.Am.success(u("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useDeleteManufacturerMutation=()=>{let e=(0,r.useQueryClient)(),{t:u}=(0,c.$G)();return(0,r.useMutation)(M.delete,{onSuccess:()=>{s.Am.success(u("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutation=()=>{let{t:e}=(0,c.$G)(),u=(0,a.useRouter)(),t=(0,r.useQueryClient)();return(0,r.useMutation)(M.update,{onSuccess:async t=>{let a=u.query.shop?"/".concat(u.query.shop).concat(o.Z.manufacturer.list):o.Z.manufacturer.list;await u.push("".concat(a,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:y.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(i.P.MANUFACTURERS)}})},useUpdateManufacturerMutationInList=()=>{let{t:e}=(0,c.$G)(),u=(0,r.useQueryClient)();return(0,r.useMutation)(M.update,{onSuccess:async()=>{s.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(i.P.MANUFACTURERS)}})},useManufacturerQuery=e=>{let{slug:u,language:t}=e,{data:a,error:n,isLoading:s}=(0,r.useQuery)([i.P.MANUFACTURERS,{slug:u,language:t}],()=>M.get({slug:u,language:t}));return{manufacturer:a,error:n,loading:s}},useManufacturersQuery=e=>{var u;let{data:t,error:a,isLoading:n}=(0,r.useQuery)([i.P.MANUFACTURERS,e],e=>{let{queryKey:u,pageParam:t}=e;return M.paginated(Object.assign({},u[1],t))},{keepPreviousData:!0});return{manufacturers:null!==(u=null==t?void 0:t.data)&&void 0!==u?u:[],paginatorInfo:(0,l.Q)(t),error:a,loading:n}}}}]);