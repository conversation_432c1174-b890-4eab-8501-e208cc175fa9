(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5203],{95876:function(e,o,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shop-transfer/vendor",function(){return r(4435)}])},87077:function(e,o,r){"use strict";r.d(o,{W:function(){return n},X:function(){return a}});let a={option:(e,o)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,cursor:"pointer",borderBottom:"1px solid #E5E7EB",backgroundColor:o.isSelected?"#E5E7EB":o.isFocused?"#F9FAFB":"#ffffff"}),control:(e,o)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==o?void 0:o.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==o?void 0:o.isDisabled)?"#D4D8DD":o.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:o.menuIsOpen&&"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #E5E7EB",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}),valueContainer:(e,o)=>({...e,paddingLeft:16}),singleValue:(e,o)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,o)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,o)=>({...e,paddingLeft:o.isRtl?0:12,paddingRight:o.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,o)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})},n={option:(e,o)=>({...e,fontSize:"0.875rem",color:"#6B7280",paddingLeft:16,paddingRight:16,paddingTop:12,paddingBottom:12,marginTop:12,marginBottom:12,cursor:"pointer",border:"1px solid #E5E5E5",borderRadius:6,position:"relative",backgroundColor:o.isSelected?"#EEF1F4":o.isFocused?"#EEF1F4":"#ffffff"}),control:(e,o)=>({display:"flex",alignItems:"center",minHeight:50,backgroundColor:(null==o?void 0:o.isDisabled)?"#EEF1F4":"#ffffff",borderRadius:5,border:"1px solid #D1D5DB",borderColor:(null==o?void 0:o.isDisabled)?"#D4D8DD":o.isFocused?"rgb(var(--color-accent-500))":"#D1D5DB",boxShadow:o.menuIsOpen&&"0px 2px 6px rgba(59, 74, 92, 0.1)"}),indicatorSeparator:()=>({display:"none"}),dropdownIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc","&:hover":{color:"#9CA3AF"}}),clearIndicator:(e,o)=>({...e,color:o.isFocused?"#9CA3AF":"#cccccc",padding:0,cursor:"pointer","&:hover":{color:"#9CA3AF"}}),menu:e=>({...e,borderRadius:5,border:"1px solid #D1D5DB",paddingLeft:16,paddingRight:16,boxShadow:"0px 2px 6px rgba(59, 74, 92, 0.1)"}),valueContainer:(e,o)=>({...e,paddingLeft:16}),singleValue:(e,o)=>({...e,fontSize:"0.875rem",color:"#4B5563"}),multiValue:(e,o)=>({...e,backgroundColor:"rgb(var(--color-accent-400))",borderRadius:9999,overflow:"hidden",boxShadow:"0 0px 3px 0 rgba(0, 0, 0, 0.1), 0 0px 2px 0 rgba(0, 0, 0, 0.06)"}),multiValueLabel:(e,o)=>({...e,paddingLeft:o.isRtl?0:12,paddingRight:o.isRtl?12:0,fontSize:"0.875rem",color:"#ffffff"}),multiValueRemove:(e,o)=>({...e,paddingLeft:6,paddingRight:6,color:"#ffffff",cursor:"pointer","&:hover":{backgroundColor:"rgb(var(--color-accent-300))",color:"#F3F4F6"}}),placeholder:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"}),noOptionsMessage:(e,o)=>({...e,fontSize:"0.875rem",color:"rgba(107, 114, 128, 0.7)"})}},79828:function(e,o,r){"use strict";var a=r(85893),n=r(76518),t=r(67294),i=r(23157),l=r(87077);let d=t.forwardRef((e,o)=>{let{isRTL:r}=(0,n.S)();return(0,a.jsx)(i.ZP,{ref:o,styles:l.X,isRtl:r,...e})});d.displayName="Select",o.Z=d},4435:function(e,o,r){"use strict";r.r(o),r.d(o,{__N_SSG:function(){return S},default:function(){return ShopTransferRequestVendorPage}});var a=r(85893),n=r(92072),t=r(35484),i=r(37912),l=r(82801),d=r(7058),s=r(45957),c=r(55846),u=r(76240),f=r(10265),p=r(16203),g=r(5233),m=r(11163),x=r(67294),b=r(99930),h=r(79828),v=r(93967),F=r.n(v);function BasicFilter(e){let{onFilterFunction:o,className:r,filterOptions:n,placeholder:t,defaultValue:i}=e,{locale:l}=(0,m.useRouter)(),{t:d}=(0,g.$G)();return(0,a.jsx)("div",{className:F()("flex w-full flex-col space-y-5 rtl:space-x-reverse md:flex-row md:items-end md:space-x-5 md:space-y-0",r),children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(h.Z,{options:n,getOptionLabel:e=>e.name,getOptionValue:e=>e.value,placeholder:t,onChange:o,isClearable:!0,defaultValue:i})})})}var S=!0;function ShopTransferRequestVendorPage(){let{t:e}=(0,g.$G)(),{locale:o}=(0,m.useRouter)(),[r,l]=(0,x.useState)("created_at"),[h,v]=(0,x.useState)(f.As.Desc),[F,S]=(0,x.useState)(""),[R,w]=(0,x.useState)("from"),[D,E]=(0,x.useState)(1),{data:C,isLoading:A,error:B}=(0,b.UE)(),{role:_}=(0,p.WA)(),{ownershipTransfer:j,loading:y,paginatorInfo:L,error:V}=(0,u.il)({language:o,limit:10,page:D,orderBy:r,sortedBy:h,type:R,transaction_identifier:F});return A||y?(0,a.jsx)(c.Z,{text:e("common:text-loading")}):B||V?(0,a.jsx)(s.Z,{message:null==B?void 0:B.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.Z,{className:"mb-8 flex flex-col",children:(0,a.jsxs)("div",{className:"flex w-full flex-col items-center md:flex-row",children:[(0,a.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,a.jsx)(t.Z,{title:"Shop Ownership Transfer Request List"})}),(0,a.jsxs)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-3/4",children:[(0,a.jsx)(i.Z,{onSearch:function(e){let{searchText:o}=e;S(o),E(1)},placeholderText:"Search by Request Tracker"}),(0,a.jsx)(BasicFilter,{className:"md:ms-6",filterOptions:[{name:"Request from",value:"from"},{name:"Request to",value:"to"}],onFilterFunction:e=>{w(null==e?void 0:e.value),E(1)},placeholder:"filter by request type",defaultValue:[{name:"Request from",value:"from"}]})]})]})}),(0,a.jsx)(d.Z,{userRole:_,user:C,ownershipTransfer:j,paginatorInfo:L,onPagination:function(e){E(e)},onOrder:l,onSort:v})]})}ShopTransferRequestVendorPage.authenticate={permissions:p.Zk},ShopTransferRequestVendorPage.Layout=l.default},23157:function(e,o,r){"use strict";r.d(o,{ZP:function(){return l}});var a=r(65342),n=r(87462),t=r(67294),i=r(76416);r(48711),r(73935),r(73469);var l=(0,t.forwardRef)(function(e,o){var r=(0,a.u)(e);return t.createElement(i.S,(0,n.Z)({ref:o},r))})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9693,9709,9494,5535,8186,1285,7556,8504,2801,3589,9774,2888,179],function(){return e(e.s=95876)}),_N_E=e.O()}]);