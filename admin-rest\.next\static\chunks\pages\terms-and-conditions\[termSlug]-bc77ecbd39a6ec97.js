(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6294,2007,2036],{50162:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/terms-and-conditions/[termSlug]",function(){return n(16099)}])},97670:function(e,t,n){"use strict";n.r(t);var s=n(85893),r=n(78985),i=n(79362),o=n(8144),l=n(74673),a=n(99494),u=n(5233),d=n(1631),c=n(11163),m=n(48583),p=n(93967),f=n.n(p),g=n(30824),v=n(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:n}=(0,u.$G)(),[r,o]=(0,m.KO)(i.Hf),{childMenu:l}=t,{width:a}=(0,v.Z)();return(0,s.jsx)("div",{className:"space-y-2",children:null==l?void 0:l.map(e=>{let{href:t,label:o,icon:l,childMenu:u}=e;return(0,s.jsx)(d.Z,{href:t,label:n(o),icon:l,childMenu:u,miniSidebar:r&&a>=i.h2},o)})})},SideBarGroup=()=>{var e;let{t}=(0,u.$G)(),[n,r]=(0,m.KO)(i.Hf),o=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,l=Object.keys(o),{width:d}=(0,v.Z)();return(0,s.jsx)(s.Fragment,{children:null==l?void 0:l.map((e,r)=>{var l;return(0,s.jsxs)("div",{className:f()("flex flex-col px-5",n&&d>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",n&&d>=i.h2?"hidden":""),children:t(null===(l=o[e])||void 0===l?void 0:l.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:o[e]})]},r)})})};t.default=e=>{let{children:t}=e,{locale:n}=(0,c.useRouter)(),[a,u]=(0,m.KO)(i.Hf),[d]=(0,m.KO)(i.GH),[p]=(0,m.KO)(i.W4),{width:h}=(0,v.Z)();return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===n||"he"===n?"rtl":"ltr",children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(l.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-20",a&&h>=i.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,s.jsx)(g.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=i.h2&&(d||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&h>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,s.jsx)(o.Z,{})]})]})]})}},63364:function(e,t,n){"use strict";var s=n(85893),r=n(3673);t.Z=e=>{let{termsAndConditions:t}=e,n=(0,r.t)({description:null==t?void 0:t.description});return(0,s.jsxs)("div",{className:"rounded bg-white px-8 py-10 shadow",children:[(null==t?void 0:t.title)?(0,s.jsx)("h3",{className:"mb-4 text-[22px] font-bold",children:null==t?void 0:t.title}):"",n?(0,s.jsx)("p",{className:"text-[15px] leading-[1.75em] text-[#5A5A5A] react-editor-description",dangerouslySetInnerHTML:{__html:n}}):""]})}},41107:function(e,t,n){"use strict";n.d(t,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var s=n(11163),r=n.n(s),i=n(88767),o=n(22920),l=n(5233),a=n(28597),u=n(97514),d=n(47869),c=n(93345),m=n(55191),p=n(3737);let f={...(0,m.h)(d.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:t,shop_id:n,...s}=e;return p.eN.get(d.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:n,...s,search:p.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>p.eN.post(d.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>p.eN.post(d.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,i.useQueryClient)();return(0,i.useMutation)(f.approve,{onSuccess:()=>{o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,i.useQueryClient)();return(0,i.useMutation)(f.disapprove,{onSuccess:()=>{o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:t,language:n}=e,{data:s,error:r,isLoading:o}=(0,i.useQuery)([d.P.TERMS_AND_CONDITIONS,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{termsAndConditions:s,error:r,loading:o}},useTermsAndConditionsQuery=e=>{var t;let{data:n,error:s,isLoading:r}=(0,i.useQuery)([d.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{termsAndConditions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(n),error:s,loading:r}},useCreateTermsAndConditionsMutation=()=>{let e=(0,i.useQueryClient)(),t=(0,s.useRouter)(),{t:n}=(0,l.$G)();return(0,i.useMutation)(f.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(u.Z.termsAndCondition.list):u.Z.termsAndCondition.list;await r().push(e,void 0,{locale:c.Config.defaultLanguage}),o.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;o.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,i.useQueryClient)(),n=(0,s.useRouter)();return(0,i.useMutation)(f.update,{onSuccess:async t=>{let s=n.query.shop?"/".concat(n.query.shop).concat(u.Z.termsAndCondition.list):u.Z.termsAndCondition.list;await n.push(s,void 0,{locale:c.Config.defaultLanguage}),o.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:t=>{var n;o.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,l.$G)();return(0,i.useMutation)(f.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;o.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},16099:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSP:function(){return m}});var s=n(85893),r=n(97670),i=n(45957),o=n(55846),l=n(5233),a=n(11163),u=n(16203),d=n(41107),c=n(63364);let TermsAndConditionsPage=()=>{let{query:e,locale:t}=(0,a.useRouter)(),{t:n}=(0,l.$G)(),{termsAndConditions:r,loading:u,error:m}=(0,d.nF)({slug:e.termSlug,language:t});return u?(0,s.jsx)(o.Z,{text:n("common:text-loading")}):m?(0,s.jsx)(i.Z,{message:m.message}):(0,s.jsx)(c.Z,{termsAndConditions:r})};TermsAndConditionsPage.authenticate={permissions:u.M$},TermsAndConditionsPage.Layout=r.default;var m=!0;t.default=TermsAndConditionsPage},3673:function(e,t,n){"use strict";n.d(t,{t:function(){return useSanitizeContent}});var s=n(67294),r=n(91036),i=n.n(r);function useSanitizeContent(e){var t,n;let{description:r}=e,[o,l]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{l(!0)},[]),!r)return;let a=i()(r,{allowedTags:null===i()||void 0===i()?void 0:null===(n=i().defaults)||void 0===n?void 0:null===(t=n.allowedTags)||void 0===t?void 0:t.concat(["img","iframe"]),allowedAttributes:{p:["style","class"],strong:["style","class"],em:["style","class"],u:["style","class"],pre:["style","class"],sub:["style"],sup:["style"],span:["style","class"],a:["style","href","data-*","name","target","class"],img:["src","srcset","alt","title","width","height","loading","class"],li:["style","class"],iframe:["src","frameborder","allowfullscreen","class"]},allowedIframeHostnames:["www.youtube.com","player.vimeo.com"],allowedStyles:{"*":{color:[/^#(0x)?[0-9a-f]+$/i,/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/],"background-color":[/^#(0x)?[0-9a-f]+$/i,/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/],"text-align":[/^left$/,/^right$/,/^center$/],"font-size":[/^\d+(?:px|em|%)$/]}},allowedSchemesByTag:{img:["data"]}});return o?a:""}},75347:function(){},31777:function(){},34017:function(){},59905:function(){}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,1036,9494,5535,8186,1285,1631,9774,2888,179],function(){return e(e.s=50162)}),_N_E=e.O()}]);