(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[823,2036],{47134:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/taxes/edit/[id]",function(){return n(87270)}])},87270:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return x},default:function(){return UpdateTaxPage}});var a=n(85893),s=n(97670),d=n(11163),i=n(79063),r=n(45957),u=n(55846),o=n(32232),l=n(5233),x=!0;function UpdateTaxPage(){let{t:e}=(0,l.$G)(),{query:t}=(0,d.useRouter)(),{data:n,isLoading:s,error:x}=(0,o.io)(t.id);return s?(0,a.jsx)(u.Z,{text:e("common:text-loading")}):x?(0,a.jsx)(r.Z,{message:x.message}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,a.jsxs)("h1",{className:"text-lg font-semibold text-heading",children:[e("text-update")," ",e("text-tax")," #",null==n?void 0:n.id]})}),(0,a.jsx)(i.Z,{initialValues:n})]})}UpdateTaxPage.Layout=s.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,9494,5535,8186,1285,1631,3749,9774,2888,179],function(){return e(e.s=47134)}),_N_E=e.O()}]);