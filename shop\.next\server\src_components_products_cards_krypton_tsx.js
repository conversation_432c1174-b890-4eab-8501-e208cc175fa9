"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_products_cards_krypton_tsx";
exports.ids = ["src_components_products_cards_krypton_tsx"];
exports.modules = {

/***/ "./src/components/icons/external-icon.tsx":
/*!************************************************!*\
  !*** ./src/components/icons/external-icon.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalIcon: () => (/* binding */ ExternalIcon),\n/* harmony export */   ExternalIconNew: () => (/* binding */ ExternalIconNew)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ExternalIcon = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\nconst ExternalIconNew = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"1em\",\n        height: \"1em\",\n        viewBox: \"0 0 20 20\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                opacity: 0.4,\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M1.668 6.667a2.5 2.5 0 012.5-2.5h5a.833.833 0 110 1.666h-5a.833.833 0 00-.833.834v9.166c0 .46.373.834.833.834h9.167c.46 0 .833-.373.833-.834v-5a.833.833 0 111.667 0v5a2.5 2.5 0 01-2.5 2.5H4.168a2.5 2.5 0 01-2.5-2.5V6.667z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fillRule: \"evenodd\",\n                clipRule: \"evenodd\",\n                d: \"M18.34 7.5a.833.833 0 01-1.667 0V4.512L9.5 11.684a.833.833 0 11-1.179-1.178l7.172-7.173h-2.99a.833.833 0 110-1.666h5.002c.46 0 .833.373.833.833v5z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\external-icon.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9leHRlcm5hbC1pY29uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFPLE1BQU1BLGVBQWUsQ0FBQyxFQUFFLEdBQUdDLE9BQU87SUFDdkMscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU07UUFBNkJDLE1BQUs7UUFBT0MsU0FBUTtRQUFZQyxhQUFhO1FBQUtDLFFBQU87UUFBZ0IsR0FBR04sS0FBSztrQkFDdkgsNEVBQUNPO1lBQUtDLGVBQWM7WUFBUUMsZ0JBQWU7WUFBUUMsR0FBRTs7Ozs7Ozs7Ozs7QUFHM0QsRUFBRTtBQUVLLE1BQU1DLGtCQUFxRCxDQUFDWDtJQUNqRSxxQkFDRSw4REFBQ0M7UUFDQ1csT0FBTTtRQUNOQyxRQUFPO1FBQ1BULFNBQVE7UUFDUkQsTUFBSztRQUNMRCxPQUFNO1FBQ0wsR0FBR0YsS0FBSzs7MEJBRVQsOERBQUNPO2dCQUNDTyxTQUFTO2dCQUNUQyxVQUFTO2dCQUNUQyxVQUFTO2dCQUNUTixHQUFFO2dCQUNGUCxNQUFLOzs7Ozs7MEJBRVAsOERBQUNJO2dCQUNDUSxVQUFTO2dCQUNUQyxVQUFTO2dCQUNUTixHQUFFO2dCQUNGUCxNQUFLOzs7Ozs7Ozs7Ozs7QUFJYixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHBpY2stYmF6YXIvc2hvcC8uL3NyYy9jb21wb25lbnRzL2ljb25zL2V4dGVybmFsLWljb24udHN4PzM4MmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEV4dGVybmFsSWNvbiA9ICh7IC4uLnByb3BzIH0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlV2lkdGg9ezEuNX0gc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgey4uLnByb3BzfT5cclxuICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIGQ9XCJNMTMuNSA2SDUuMjVBMi4yNSAyLjI1IDAgMDAzIDguMjV2MTAuNUEyLjI1IDIuMjUgMCAwMDUuMjUgMjFoMTAuNUEyLjI1IDIuMjUgMCAwMDE4IDE4Ljc1VjEwLjVtLTEwLjUgNkwyMSAzbTAgMGgtNS4yNU0yMSAzdjUuMjVcIiAvPlxyXG4gICAgPC9zdmc+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBFeHRlcm5hbEljb25OZXc6IFJlYWN0LkZDPFJlYWN0LlNWR0F0dHJpYnV0ZXM8e30+PiA9IChwcm9wcykgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8c3ZnXHJcbiAgICAgIHdpZHRoPVwiMWVtXCJcclxuICAgICAgaGVpZ2h0PVwiMWVtXCJcclxuICAgICAgdmlld0JveD1cIjAgMCAyMCAyMFwiXHJcbiAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgID5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBvcGFjaXR5PXswLjR9XHJcbiAgICAgICAgZmlsbFJ1bGU9XCJldmVub2RkXCJcclxuICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxyXG4gICAgICAgIGQ9XCJNMS42NjggNi42NjdhMi41IDIuNSAwIDAxMi41LTIuNWg1YS44MzMuODMzIDAgMTEwIDEuNjY2aC01YS44MzMuODMzIDAgMDAtLjgzMy44MzR2OS4xNjZjMCAuNDYuMzczLjgzNC44MzMuODM0aDkuMTY3Yy40NiAwIC44MzMtLjM3My44MzMtLjgzNHYtNWEuODMzLjgzMyAwIDExMS42NjcgMHY1YTIuNSAyLjUgMCAwMS0yLjUgMi41SDQuMTY4YTIuNSAyLjUgMCAwMS0yLjUtMi41VjYuNjY3elwiXHJcbiAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgIC8+XHJcbiAgICAgIDxwYXRoXHJcbiAgICAgICAgZmlsbFJ1bGU9XCJldmVub2RkXCJcclxuICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxyXG4gICAgICAgIGQ9XCJNMTguMzQgNy41YS44MzMuODMzIDAgMDEtMS42NjcgMFY0LjUxMkw5LjUgMTEuNjg0YS44MzMuODMzIDAgMTEtMS4xNzktMS4xNzhsNy4xNzItNy4xNzNoLTIuOTlhLjgzMy44MzMgMCAxMTAtMS42NjZoNS4wMDJjLjQ2IDAgLjgzMy4zNzMuODMzLjgzM3Y1elwiXHJcbiAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgIC8+XHJcbiAgICA8L3N2Zz5cclxuICApO1xyXG59OyJdLCJuYW1lcyI6WyJFeHRlcm5hbEljb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJkIiwiRXh0ZXJuYWxJY29uTmV3Iiwid2lkdGgiLCJoZWlnaHQiLCJvcGFjaXR5IiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/external-icon.tsx\n");

/***/ }),

/***/ "./src/components/products/cards/krypton.tsx":
/*!***************************************************!*\
  !*** ./src/components/products/cards/krypton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/link */ \"./src/components/ui/link.tsx\");\n/* harmony import */ var _components_ui_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/image */ \"./src/components/ui/image.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_use_price__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/use-price */ \"./src/lib/use-price.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/placeholders */ \"./src/lib/placeholders.tsx\");\n/* harmony import */ var _components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/external-icon */ \"./src/components/icons/external-icon.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_use_price__WEBPACK_IMPORTED_MODULE_4__]);\n_lib_use_price__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\nconst Krypton = ({ product, className })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"common\");\n    const { query } = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { name, image, slug, min_price, max_price, product_type, is_external } = product ?? {};\n    const { price, basePrice, discount } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: product.sale_price ? product.sale_price : product.price,\n        baseAmount: product.price\n    });\n    const { price: minPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: min_price\n    });\n    const { price: maxPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        amount: max_price\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: _config_routes__WEBPACK_IMPORTED_MODULE_6__.Routes.product(slug),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"product-card cart-type-krypton h-full cursor-pointer overflow-hidden rounded border border-border-200 bg-light transition-shadow duration-200 hover:shadow-sm\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"relative flex h-48 w-auto items-center justify-center sm:h-64\", query?.pages ? query?.pages?.includes(\"medicine\") ? \"m-4 mb-0\" : \"\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: t(\"text-product-image\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                            src: image?.original ?? _lib_placeholders__WEBPACK_IMPORTED_MODULE_7__.productPlaceholder,\n                            alt: name,\n                            fill: true,\n                            sizes: \"(max-width: 768px) 100vw\",\n                            className: \"product-image object-contain\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-3 rounded-full bg-yellow-500 px-2 text-xs font-semibold leading-6 text-light ltr:right-3 rtl:left-3 md:top-4 md:px-2.5 ltr:md:right-4 rtl:md:left-4\",\n                            children: discount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"p-3 text-center md:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mb-2 flex items-center justify-center gap-1 truncate text-sm font-semibold text-heading\",\n                            children: [\n                                name,\n                                is_external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_external_icon__WEBPACK_IMPORTED_MODULE_8__.ExternalIcon, {\n                                    className: \"h-3.5 w-3.5 stroke-2 text-muted\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, undefined) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        product_type.toLowerCase() === \"variable\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: minPrice\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \" - \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: maxPrice\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-sub-heading\",\n                                    children: price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined),\n                                basePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"del\", {\n                                    className: \"text-sm text-muted ltr:ml-2 rtl:mr-2\",\n                                    children: basePrice\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\cards\\\\krypton.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Krypton);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/cards/krypton.tsx\n");

/***/ }),

/***/ "./src/lib/use-price.tsx":
/*!*******************************!*\
  !*** ./src/lib/use-price.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePrice),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatVariantPrice: () => (/* binding */ formatVariantPrice)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_settings__WEBPACK_IMPORTED_MODULE_2__]);\n_framework_settings__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction formatPrice({ amount, currencyCode, locale, fractions }) {\n    const formatCurrency = new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        maximumFractionDigits: fractions\n    });\n    return formatCurrency.format(amount);\n}\nfunction formatVariantPrice({ amount, baseAmount, currencyCode, locale, fractions = 2 }) {\n    const hasDiscount = baseAmount > amount;\n    const formatDiscount = new Intl.NumberFormat(locale, {\n        style: \"percent\"\n    });\n    const discount = hasDiscount ? formatDiscount.format((baseAmount - amount) / baseAmount) : null;\n    const price = formatPrice({\n        amount,\n        currencyCode,\n        locale,\n        fractions\n    });\n    const basePrice = hasDiscount ? formatPrice({\n        amount: baseAmount,\n        currencyCode,\n        locale,\n        fractions\n    }) : null;\n    return {\n        price,\n        basePrice,\n        discount\n    };\n}\nfunction usePrice(data) {\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    const currency = settings?.currency;\n    const currencyOptions = settings?.currencyOptions;\n    const { amount, baseAmount, currencyCode, currencyOptionsFormat } = {\n        ...data,\n        currencyCode: currency ?? \"USD\",\n        currencyOptionsFormat: currencyOptions ?? {\n            formation: \"en-US\",\n            fractions: 2\n        }\n    };\n    const { formation = \"en-US\", fractions = 2 } = currencyOptionsFormat;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (typeof amount !== \"number\" || !currencyCode) return \"\";\n        const fractionalDigit = fractions ? fractions : 2;\n        let currentLocale = formation ? formation : \"en\";\n        // if (process.env.NEXT_PUBLIC_ENABLE_MULTI_LANG) {\n        //   currentLocale = locale ? locale : 'en';\n        // }\n        return baseAmount ? formatVariantPrice({\n            amount,\n            baseAmount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        }) : formatPrice({\n            amount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        });\n    }, [\n        amount,\n        baseAmount,\n        currencyCode,\n        locale\n    ]);\n    return typeof value === \"string\" ? {\n        price: value,\n        basePrice: null,\n        discount: null\n    } : value;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-price.tsx\n");

/***/ })

};
;