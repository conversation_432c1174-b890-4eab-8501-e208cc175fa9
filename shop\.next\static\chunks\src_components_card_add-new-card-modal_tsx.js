/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_card_add-new-card-modal_tsx"],{

/***/ "./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval(__webpack_require__.ts("(function (global, factory) {\n   true ? factory(exports, __webpack_require__(/*! react */ \"./node_modules/react/index.js\")) :\n  0;\n})(this, (function (exports, React) { 'use strict';\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n\n    return target;\n  }\n\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n\n    var key, i;\n\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n\n    return target;\n  }\n\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n\n  function _iterableToArrayLimit(arr, i) {\n    var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n\n    var _s, _e;\n\n    try {\n      for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  function getDefaultExportFromCjs (x) {\n  \treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n  }\n\n  var propTypes = {exports: {}};\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var ReactPropTypesSecret_1;\n  var hasRequiredReactPropTypesSecret;\n\n  function requireReactPropTypesSecret() {\n    if (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n    hasRequiredReactPropTypesSecret = 1;\n\n    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n    ReactPropTypesSecret_1 = ReactPropTypesSecret;\n    return ReactPropTypesSecret_1;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  var factoryWithThrowingShims;\n  var hasRequiredFactoryWithThrowingShims;\n\n  function requireFactoryWithThrowingShims() {\n    if (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n    hasRequiredFactoryWithThrowingShims = 1;\n\n    var ReactPropTypesSecret = requireReactPropTypesSecret();\n\n    function emptyFunction() {}\n\n    function emptyFunctionWithReset() {}\n\n    emptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n    factoryWithThrowingShims = function () {\n      function shim(props, propName, componentName, location, propFullName, secret) {\n        if (secret === ReactPropTypesSecret) {\n          // It is still safe when called from React.\n          return;\n        }\n\n        var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n        err.name = 'Invariant Violation';\n        throw err;\n      }\n      shim.isRequired = shim;\n\n      function getShim() {\n        return shim;\n      }\n      // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\n      var ReactPropTypes = {\n        array: shim,\n        bool: shim,\n        func: shim,\n        number: shim,\n        object: shim,\n        string: shim,\n        symbol: shim,\n        any: shim,\n        arrayOf: getShim,\n        element: shim,\n        elementType: shim,\n        instanceOf: getShim,\n        node: shim,\n        objectOf: getShim,\n        oneOf: getShim,\n        oneOfType: getShim,\n        shape: getShim,\n        exact: getShim,\n        checkPropTypes: emptyFunctionWithReset,\n        resetWarningCache: emptyFunction\n      };\n      ReactPropTypes.PropTypes = ReactPropTypes;\n      return ReactPropTypes;\n    };\n\n    return factoryWithThrowingShims;\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    propTypes.exports = requireFactoryWithThrowingShims()();\n  }\n\n  var propTypesExports = propTypes.exports;\n  var PropTypes = /*@__PURE__*/getDefaultExportFromCjs(propTypesExports);\n\n  var useAttachEvent = function useAttachEvent(element, event, cb) {\n    var cbDefined = !!cb;\n    var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n    // Using a ref saves us from calling element.on/.off every render.\n\n    React.useEffect(function () {\n      cbRef.current = cb;\n    }, [cb]);\n    React.useEffect(function () {\n      if (!cbDefined || !element) {\n        return function () {};\n      }\n\n      var decoratedCb = function decoratedCb() {\n        if (cbRef.current) {\n          cbRef.current.apply(cbRef, arguments);\n        }\n      };\n\n      element.on(event, decoratedCb);\n      return function () {\n        element.off(event, decoratedCb);\n      };\n    }, [cbDefined, event, element, cbRef]);\n  };\n\n  var usePrevious = function usePrevious(value) {\n    var ref = React.useRef(value);\n    React.useEffect(function () {\n      ref.current = value;\n    }, [value]);\n    return ref.current;\n  };\n\n  var isUnknownObject = function isUnknownObject(raw) {\n    return raw !== null && _typeof(raw) === 'object';\n  };\n  var isPromise = function isPromise(raw) {\n    return isUnknownObject(raw) && typeof raw.then === 'function';\n  }; // We are using types to enforce the `stripe` prop in this lib,\n  // but in an untyped integration `stripe` could be anything, so we need\n  // to do some sanity validation to prevent type errors.\n\n  var isStripe = function isStripe(raw) {\n    return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n  };\n\n  var PLAIN_OBJECT_STR = '[object Object]';\n  var isEqual = function isEqual(left, right) {\n    if (!isUnknownObject(left) || !isUnknownObject(right)) {\n      return left === right;\n    }\n\n    var leftArray = Array.isArray(left);\n    var rightArray = Array.isArray(right);\n    if (leftArray !== rightArray) return false;\n    var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n    var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n    if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n    // fallback to reference check.\n\n    if (!leftPlainObject && !leftArray) return left === right;\n    var leftKeys = Object.keys(left);\n    var rightKeys = Object.keys(right);\n    if (leftKeys.length !== rightKeys.length) return false;\n    var keySet = {};\n\n    for (var i = 0; i < leftKeys.length; i += 1) {\n      keySet[leftKeys[i]] = true;\n    }\n\n    for (var _i = 0; _i < rightKeys.length; _i += 1) {\n      keySet[rightKeys[_i]] = true;\n    }\n\n    var allKeys = Object.keys(keySet);\n\n    if (allKeys.length !== leftKeys.length) {\n      return false;\n    }\n\n    var l = left;\n    var r = right;\n\n    var pred = function pred(key) {\n      return isEqual(l[key], r[key]);\n    };\n\n    return allKeys.every(pred);\n  };\n\n  var extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n    if (!isUnknownObject(options)) {\n      return null;\n    }\n\n    return Object.keys(options).reduce(function (newOptions, key) {\n      var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n      if (immutableKeys.includes(key)) {\n        if (isUpdated) {\n          console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n        }\n\n        return newOptions;\n      }\n\n      if (!isUpdated) {\n        return newOptions;\n      }\n\n      return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n    }, null);\n  };\n\n  var INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n  // integration `stripe` could be anything, so we need to do some sanity\n  // validation to prevent type errors.\n\n  var validateStripe = function validateStripe(maybeStripe) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (maybeStripe === null || isStripe(maybeStripe)) {\n      return maybeStripe;\n    }\n\n    throw new Error(errorMsg);\n  };\n\n  var parseStripeProp = function parseStripeProp(raw) {\n    var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n    if (isPromise(raw)) {\n      return {\n        tag: 'async',\n        stripePromise: Promise.resolve(raw).then(function (result) {\n          return validateStripe(result, errorMsg);\n        })\n      };\n    }\n\n    var stripe = validateStripe(raw, errorMsg);\n\n    if (stripe === null) {\n      return {\n        tag: 'empty'\n      };\n    }\n\n    return {\n      tag: 'sync',\n      stripe: stripe\n    };\n  };\n\n  var registerWithStripeJs = function registerWithStripeJs(stripe) {\n    if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n      return;\n    }\n\n    stripe._registerWrapper({\n      name: 'react-stripe-js',\n      version: \"2.8.0\"\n    });\n\n    stripe.registerAppInfo({\n      name: 'react-stripe-js',\n      version: \"2.8.0\",\n      url: 'https://stripe.com/docs/stripe-js/react'\n    });\n  };\n\n  var ElementsContext = /*#__PURE__*/React.createContext(null);\n  ElementsContext.displayName = 'ElementsContext';\n  var parseElementsContext = function parseElementsContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n    }\n\n    return ctx;\n  };\n  /**\n   * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n   * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n   *\n   * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n   * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n   * Pass the returned `Promise` to `Elements`.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n   */\n\n  var Elements = function Elements(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp);\n    }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n    var _React$useState = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n      };\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      var isMounted = true;\n\n      var safeSetContext = function safeSetContext(stripe) {\n        setContext(function (ctx) {\n          // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n          if (ctx.stripe) return ctx;\n          return {\n            stripe: stripe,\n            elements: stripe.elements(options)\n          };\n        });\n      }; // For an async stripePromise, store it in context once resolved\n\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted) {\n            // Only update Elements context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            safeSetContext(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !ctx.stripe) {\n        // Or, handle a sync stripe instance going from null -> populated\n        safeSetContext(parsed.stripe);\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!ctx.elements) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n      if (updates) {\n        ctx.elements.update(updates);\n      }\n    }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n      value: ctx\n    }, children);\n  };\n  Elements.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.object\n  };\n  var useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n    var ctx = React.useContext(ElementsContext);\n    return parseElementsContext(ctx, useCaseMessage);\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n   */\n\n  var useElements = function useElements() {\n    var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n        elements = _useElementsContextWi.elements;\n\n    return elements;\n  };\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n   */\n\n  var ElementsConsumer = function ElementsConsumer(_ref2) {\n    var children = _ref2.children;\n    var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n    return children(ctx);\n  };\n  ElementsConsumer.propTypes = {\n    children: PropTypes.func.isRequired\n  };\n\n  var _excluded = [\"on\", \"session\"];\n  var CustomCheckoutSdkContext = /*#__PURE__*/React.createContext(null);\n  CustomCheckoutSdkContext.displayName = 'CustomCheckoutSdkContext';\n  var parseCustomCheckoutSdkContext = function parseCustomCheckoutSdkContext(ctx, useCase) {\n    if (!ctx) {\n      throw new Error(\"Could not find CustomCheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CustomCheckoutProvider> provider.\"));\n    }\n\n    return ctx;\n  };\n  var CustomCheckoutContext = /*#__PURE__*/React.createContext(null);\n  CustomCheckoutContext.displayName = 'CustomCheckoutContext';\n  var extractCustomCheckoutContextValue = function extractCustomCheckoutContextValue(customCheckoutSdk, sessionState) {\n    if (!customCheckoutSdk) {\n      return null;\n    }\n\n    customCheckoutSdk.on;\n        customCheckoutSdk.session;\n        var actions = _objectWithoutProperties(customCheckoutSdk, _excluded);\n\n    if (!sessionState) {\n      return _objectSpread2(_objectSpread2({}, actions), customCheckoutSdk.session());\n    }\n\n    return _objectSpread2(_objectSpread2({}, actions), sessionState);\n  };\n  var INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CustomCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var CustomCheckoutProvider = function CustomCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n    }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        session = _React$useState2[0],\n        setSession = _React$useState2[1];\n\n    var _React$useState3 = React.useState(function () {\n      return {\n        stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n        customCheckoutSdk: null\n      };\n    }),\n        _React$useState4 = _slicedToArray(_React$useState3, 2),\n        ctx = _React$useState4[0],\n        setContext = _React$useState4[1];\n\n    var safeSetContext = function safeSetContext(stripe, customCheckoutSdk) {\n      setContext(function (ctx) {\n        if (ctx.stripe && ctx.customCheckoutSdk) {\n          return ctx;\n        }\n\n        return {\n          stripe: stripe,\n          customCheckoutSdk: customCheckoutSdk\n        };\n      });\n    }; // Ref used to avoid calling initCustomCheckout multiple times when options changes\n\n\n    var initCustomCheckoutCalledRef = React.useRef(false);\n    React.useEffect(function () {\n      var isMounted = true;\n\n      if (parsed.tag === 'async' && !ctx.stripe) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe && isMounted && !initCustomCheckoutCalledRef.current) {\n            // Only update context if the component is still mounted\n            // and stripe is not null. We allow stripe to be null to make\n            // handling SSR easier.\n            initCustomCheckoutCalledRef.current = true;\n            stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n              if (customCheckoutSdk) {\n                safeSetContext(stripe, customCheckoutSdk);\n                customCheckoutSdk.on('change', setSession);\n              }\n            });\n          }\n        });\n      } else if (parsed.tag === 'sync' && parsed.stripe && !initCustomCheckoutCalledRef.current) {\n        initCustomCheckoutCalledRef.current = true;\n        parsed.stripe.initCustomCheckout(options).then(function (customCheckoutSdk) {\n          if (customCheckoutSdk) {\n            safeSetContext(parsed.stripe, customCheckoutSdk);\n            customCheckoutSdk.on('change', setSession);\n          }\n        });\n      }\n\n      return function () {\n        isMounted = false;\n      };\n    }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on CustomCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      var _prevOptions$elements, _options$elementsOpti;\n\n      if (!ctx.customCheckoutSdk) {\n        return;\n      }\n\n      if (options.clientSecret && !isUnknownObject(prevOptions) && !isEqual(options.clientSecret, prevOptions.clientSecret)) {\n        console.warn('Unsupported prop change: options.client_secret is not a mutable property.');\n      }\n\n      var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n      var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n\n      if (currentAppearance && !isEqual(currentAppearance, previousAppearance)) {\n        ctx.customCheckoutSdk.changeAppearance(currentAppearance);\n      }\n    }, [options, prevOptions, ctx.customCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(ctx.stripe);\n    }, [ctx.stripe]);\n    var customCheckoutContextValue = React.useMemo(function () {\n      return extractCustomCheckoutContextValue(ctx.customCheckoutSdk, session);\n    }, [ctx.customCheckoutSdk, session]);\n\n    if (!ctx.customCheckoutSdk) {\n      return null;\n    }\n\n    return /*#__PURE__*/React.createElement(CustomCheckoutSdkContext.Provider, {\n      value: ctx\n    }, /*#__PURE__*/React.createElement(CustomCheckoutContext.Provider, {\n      value: customCheckoutContextValue\n    }, children));\n  };\n  CustomCheckoutProvider.propTypes = {\n    stripe: PropTypes.any,\n    options: PropTypes.shape({\n      clientSecret: PropTypes.string.isRequired,\n      elementsOptions: PropTypes.object\n    }).isRequired\n  };\n  var useCustomCheckoutSdkContextWithUseCase = function useCustomCheckoutSdkContextWithUseCase(useCaseString) {\n    var ctx = React.useContext(CustomCheckoutSdkContext);\n    return parseCustomCheckoutSdkContext(ctx, useCaseString);\n  };\n  var useElementsOrCustomCheckoutSdkContextWithUseCase = function useElementsOrCustomCheckoutSdkContextWithUseCase(useCaseString) {\n    var customCheckoutSdkContext = React.useContext(CustomCheckoutSdkContext);\n    var elementsContext = React.useContext(ElementsContext);\n\n    if (customCheckoutSdkContext && elementsContext) {\n      throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CustomCheckoutProvider> and <Elements> providers.\"));\n    }\n\n    if (customCheckoutSdkContext) {\n      return parseCustomCheckoutSdkContext(customCheckoutSdkContext, useCaseString);\n    }\n\n    return parseElementsContext(elementsContext, useCaseString);\n  };\n  var useCustomCheckout = function useCustomCheckout() {\n    // ensure it's in CustomCheckoutProvider\n    useCustomCheckoutSdkContextWithUseCase('calls useCustomCheckout()');\n    var ctx = React.useContext(CustomCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('Could not find CustomCheckout Context; You need to wrap the part of your app that calls useCustomCheckout() in an <CustomCheckoutProvider> provider.');\n    }\n\n    return ctx;\n  };\n\n  var capitalized = function capitalized(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n\n  var createElementComponent = function createElementComponent(type, isServer) {\n    var displayName = \"\".concat(capitalized(type), \"Element\");\n\n    var ClientElement = function ClientElement(_ref) {\n      var id = _ref.id,\n          className = _ref.className,\n          _ref$options = _ref.options,\n          options = _ref$options === void 0 ? {} : _ref$options,\n          onBlur = _ref.onBlur,\n          onFocus = _ref.onFocus,\n          onReady = _ref.onReady,\n          onChange = _ref.onChange,\n          onEscape = _ref.onEscape,\n          onClick = _ref.onClick,\n          onLoadError = _ref.onLoadError,\n          onLoaderStart = _ref.onLoaderStart,\n          onNetworksChange = _ref.onNetworksChange,\n          onConfirm = _ref.onConfirm,\n          onCancel = _ref.onCancel,\n          onShippingAddressChange = _ref.onShippingAddressChange,\n          onShippingRateChange = _ref.onShippingRateChange;\n      var ctx = useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var elements = 'elements' in ctx ? ctx.elements : null;\n      var customCheckoutSdk = 'customCheckoutSdk' in ctx ? ctx.customCheckoutSdk : null;\n\n      var _React$useState = React.useState(null),\n          _React$useState2 = _slicedToArray(_React$useState, 2),\n          element = _React$useState2[0],\n          setElement = _React$useState2[1];\n\n      var elementRef = React.useRef(null);\n      var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n      // with that callback. If the merchant ever changes the callback, removes\n      // the old callback with element.off and then call element.on with the new one.\n\n      useAttachEvent(element, 'blur', onBlur);\n      useAttachEvent(element, 'focus', onFocus);\n      useAttachEvent(element, 'escape', onEscape);\n      useAttachEvent(element, 'click', onClick);\n      useAttachEvent(element, 'loaderror', onLoadError);\n      useAttachEvent(element, 'loaderstart', onLoaderStart);\n      useAttachEvent(element, 'networkschange', onNetworksChange);\n      useAttachEvent(element, 'confirm', onConfirm);\n      useAttachEvent(element, 'cancel', onCancel);\n      useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n      useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n      useAttachEvent(element, 'change', onChange);\n      var readyCallback;\n\n      if (onReady) {\n        if (type === 'expressCheckout') {\n          // Passes through the event, which includes visible PM types\n          readyCallback = onReady;\n        } else {\n          // For other Elements, pass through the Element itself.\n          readyCallback = function readyCallback() {\n            onReady(element);\n          };\n        }\n      }\n\n      useAttachEvent(element, 'ready', readyCallback);\n      React.useLayoutEffect(function () {\n        if (elementRef.current === null && domNode.current !== null && (elements || customCheckoutSdk)) {\n          var newElement = null;\n\n          if (customCheckoutSdk) {\n            newElement = customCheckoutSdk.createElement(type, options);\n          } else if (elements) {\n            newElement = elements.create(type, options);\n          } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n          elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n          setElement(newElement);\n\n          if (newElement) {\n            newElement.mount(domNode.current);\n          }\n        }\n      }, [elements, customCheckoutSdk, options]);\n      var prevOptions = usePrevious(options);\n      React.useEffect(function () {\n        if (!elementRef.current) {\n          return;\n        }\n\n        var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n        if (updates) {\n          elementRef.current.update(updates);\n        }\n      }, [options, prevOptions]);\n      React.useLayoutEffect(function () {\n        return function () {\n          if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n            try {\n              elementRef.current.destroy();\n              elementRef.current = null;\n            } catch (error) {// Do nothing\n            }\n          }\n        };\n      }, []);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className,\n        ref: domNode\n      });\n    }; // Only render the Element wrapper in a server environment.\n\n\n    var ServerElement = function ServerElement(props) {\n      useElementsOrCustomCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n      var id = props.id,\n          className = props.className;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        id: id,\n        className: className\n      });\n    };\n\n    var Element = isServer ? ServerElement : ClientElement;\n    Element.propTypes = {\n      id: PropTypes.string,\n      className: PropTypes.string,\n      onChange: PropTypes.func,\n      onBlur: PropTypes.func,\n      onFocus: PropTypes.func,\n      onReady: PropTypes.func,\n      onEscape: PropTypes.func,\n      onClick: PropTypes.func,\n      onLoadError: PropTypes.func,\n      onLoaderStart: PropTypes.func,\n      onNetworksChange: PropTypes.func,\n      onConfirm: PropTypes.func,\n      onCancel: PropTypes.func,\n      onShippingAddressChange: PropTypes.func,\n      onShippingRateChange: PropTypes.func,\n      options: PropTypes.object\n    };\n    Element.displayName = displayName;\n    Element.__elementType = type;\n    return Element;\n  };\n\n  var isServer = typeof window === 'undefined';\n\n  var EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\n  EmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\n  var useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n    var ctx = React.useContext(EmbeddedCheckoutContext);\n\n    if (!ctx) {\n      throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n    }\n\n    return ctx;\n  };\n  var INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\n  var EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n    var rawStripeProp = _ref.stripe,\n        options = _ref.options,\n        children = _ref.children;\n    var parsed = React.useMemo(function () {\n      return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n    }, [rawStripeProp]);\n    var embeddedCheckoutPromise = React.useRef(null);\n    var loadedStripe = React.useRef(null);\n\n    var _React$useState = React.useState({\n      embeddedCheckout: null\n    }),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        ctx = _React$useState2[0],\n        setContext = _React$useState2[1];\n\n    React.useEffect(function () {\n      // Don't support any ctx updates once embeddedCheckout or stripe is set.\n      if (loadedStripe.current || embeddedCheckoutPromise.current) {\n        return;\n      }\n\n      var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n        if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n        loadedStripe.current = stripe;\n        embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n          setContext({\n            embeddedCheckout: embeddedCheckout\n          });\n        });\n      }; // For an async stripePromise, store it once resolved\n\n\n      if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        parsed.stripePromise.then(function (stripe) {\n          if (stripe) {\n            setStripeAndInitEmbeddedCheckout(stripe);\n          }\n        });\n      } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n        // Or, handle a sync stripe instance going from null -> populated\n        setStripeAndInitEmbeddedCheckout(parsed.stripe);\n      }\n    }, [parsed, options, ctx, loadedStripe]);\n    React.useEffect(function () {\n      // cleanup on unmount\n      return function () {\n        // If embedded checkout is fully initialized, destroy it.\n        if (ctx.embeddedCheckout) {\n          embeddedCheckoutPromise.current = null;\n          ctx.embeddedCheckout.destroy();\n        } else if (embeddedCheckoutPromise.current) {\n          // If embedded checkout is still initializing, destroy it once\n          // it's done. This could be caused by unmounting very quickly\n          // after mounting.\n          embeddedCheckoutPromise.current.then(function () {\n            embeddedCheckoutPromise.current = null;\n\n            if (ctx.embeddedCheckout) {\n              ctx.embeddedCheckout.destroy();\n            }\n          });\n        }\n      };\n    }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n    React.useEffect(function () {\n      registerWithStripeJs(loadedStripe);\n    }, [loadedStripe]); // Warn on changes to stripe prop.\n    // The stripe prop value can only go from null to non-null once and\n    // can't be changed after that.\n\n    var prevStripe = usePrevious(rawStripeProp);\n    React.useEffect(function () {\n      if (prevStripe !== null && prevStripe !== rawStripeProp) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n      }\n    }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (prevOptions == null) {\n        return;\n      }\n\n      if (options == null) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n        return;\n      }\n\n      if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n        console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n      }\n\n      if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n      }\n\n      if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n      }\n\n      if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n        console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n      }\n    }, [prevOptions, options]);\n    return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n      value: ctx\n    }, children);\n  };\n\n  var EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className;\n\n    var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n        embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n    var isMounted = React.useRef(false);\n    var domNode = React.useRef(null);\n    React.useLayoutEffect(function () {\n      if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n        embeddedCheckout.mount(domNode.current);\n        isMounted.current = true;\n      } // Clean up on unmount\n\n\n      return function () {\n        if (isMounted.current && embeddedCheckout) {\n          try {\n            embeddedCheckout.unmount();\n            isMounted.current = false;\n          } catch (e) {// Do nothing.\n            // Parent effects are destroyed before child effects, so\n            // in cases where both the EmbeddedCheckoutProvider and\n            // the EmbeddedCheckout component are removed at the same\n            // time, the embeddedCheckout instance will be destroyed,\n            // which causes an error when calling unmount.\n          }\n        }\n      };\n    }, [embeddedCheckout]);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: domNode,\n      id: id,\n      className: className\n    });\n  }; // Only render the wrapper in a server environment.\n\n\n  var EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n    var id = _ref2.id,\n        className = _ref2.className;\n    // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n    useEmbeddedCheckoutContext();\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n   */\n\n  var useStripe = function useStripe() {\n    var _useElementsOrCustomC = useElementsOrCustomCheckoutSdkContextWithUseCase('calls useStripe()'),\n        stripe = _useElementsOrCustomC.stripe;\n\n    return stripe;\n  };\n\n  /**\n   * Requires beta access:\n   * Contact [Stripe support](https://support.stripe.com/) for more information.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardElement = createElementComponent('card', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardNumberElement = createElementComponent('cardNumber', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardExpiryElement = createElementComponent('cardExpiry', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var CardCvcElement = createElementComponent('cardCvc', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var FpxBankElement = createElementComponent('fpxBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IbanElement = createElementComponent('iban', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var IdealBankElement = createElementComponent('idealBank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var P24BankElement = createElementComponent('p24Bank', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var EpsBankElement = createElementComponent('epsBank', isServer);\n  var PaymentElement = createElementComponent('payment', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AddressElement = createElementComponent('address', isServer);\n  /**\n   * @deprecated\n   * Use `AddressElement` instead.\n   *\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n  /**\n   * @docs https://stripe.com/docs/stripe-js/react#element-components\n   */\n\n  var AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\n  exports.AddressElement = AddressElement;\n  exports.AffirmMessageElement = AffirmMessageElement;\n  exports.AfterpayClearpayMessageElement = AfterpayClearpayMessageElement;\n  exports.AuBankAccountElement = AuBankAccountElement;\n  exports.CardCvcElement = CardCvcElement;\n  exports.CardElement = CardElement;\n  exports.CardExpiryElement = CardExpiryElement;\n  exports.CardNumberElement = CardNumberElement;\n  exports.CustomCheckoutProvider = CustomCheckoutProvider;\n  exports.Elements = Elements;\n  exports.ElementsConsumer = ElementsConsumer;\n  exports.EmbeddedCheckout = EmbeddedCheckout;\n  exports.EmbeddedCheckoutProvider = EmbeddedCheckoutProvider;\n  exports.EpsBankElement = EpsBankElement;\n  exports.ExpressCheckoutElement = ExpressCheckoutElement;\n  exports.FpxBankElement = FpxBankElement;\n  exports.IbanElement = IbanElement;\n  exports.IdealBankElement = IdealBankElement;\n  exports.LinkAuthenticationElement = LinkAuthenticationElement;\n  exports.P24BankElement = P24BankElement;\n  exports.PaymentElement = PaymentElement;\n  exports.PaymentMethodMessagingElement = PaymentMethodMessagingElement;\n  exports.PaymentRequestButtonElement = PaymentRequestButtonElement;\n  exports.ShippingAddressElement = ShippingAddressElement;\n  exports.useCustomCheckout = useCustomCheckout;\n  exports.useElements = useElements;\n  exports.useStripe = useStripe;\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\n"));

/***/ }),

/***/ "./node_modules/@stripe/stripe-js/dist/stripe.esm.js":
/*!***********************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/stripe.esm.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: function() { return /* binding */ loadStripe; }\n/* harmony export */ });\nvar V3_URL = 'https://js.stripe.com/v3';\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(V3_URL, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!V3_URL_REGEX.test(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(V3_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"2.1.11\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise = null;\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise !== null) {\n    return stripePromise;\n  }\n\n  stripePromise = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      }\n\n      script.addEventListener('load', function () {\n        if (window.Stripe) {\n          resolve(window.Stripe);\n        } else {\n          reject(new Error('Stripe.js not available'));\n        }\n      });\n      script.addEventListener('error', function () {\n        reject(new Error('Failed to load Stripe.js'));\n      });\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  });\n  return stripePromise;\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\n// own script injection.\n\nvar stripePromise$1 = Promise.resolve().then(function () {\n  return loadScript(null);\n});\nvar loadCalled = false;\nstripePromise$1[\"catch\"](function (err) {\n  if (!loadCalled) {\n    console.warn(err);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now();\n  return stripePromise$1.then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@stripe/stripe-js/dist/stripe.esm.js\n"));

/***/ }),

/***/ "./src/components/card/add-new-card-modal.tsx":
/*!****************************************************!*\
  !*** ./src/components/card/add-new-card-modal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_card_stripe_stripe_card_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/card/stripe/stripe-card-form */ \"./src/components/card/stripe/stripe-card-form.tsx\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _lib_is_stripe_available__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/is-stripe-available */ \"./src/lib/is-stripe-available.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst StripeNotAvailable = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"payment-modal relative h-full w-screen max-w-md overflow-hidden rounded-[10px] bg-light md:h-auto md:min-h-0 lg:max-w-[46rem]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 lg:p-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mb-2 block text-sm font-semibold text-black\",\n                children: \"Sorry this feature is not available!\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\add-new-card-modal.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\add-new-card-modal.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\add-new-card-modal.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StripeNotAvailable;\nconst CARDS_FORM_COMPONENTS = {\n    STRIPE: {\n        component: _components_card_stripe_stripe_card_form__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    STRIPE_NA: {\n        component: StripeNotAvailable\n    }\n};\nconst AddNewCardModal = ()=>{\n    _s();\n    const { data: { paymentGateway } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState)();\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_4__.useSettings)();\n    // At first it will check if default payment gateway is stripe or not? if yes then it will directly work on if condition. No need to run else condition.\n    const isStripeGatewayAvailable = (0,_lib_is_stripe_available__WEBPACK_IMPORTED_MODULE_5__.isStripeAvailable)(settings);\n    let gatewayName = \"non-stripe\";\n    if (isStripeGatewayAvailable) {\n        gatewayName = _types__WEBPACK_IMPORTED_MODULE_3__.PaymentGateway.STRIPE;\n    }\n    const PaymentMethod = isStripeGatewayAvailable ? CARDS_FORM_COMPONENTS[gatewayName] : CARDS_FORM_COMPONENTS[\"STRIPE_NA\"];\n    const CardFormComponent = PaymentMethod === null || PaymentMethod === void 0 ? void 0 : PaymentMethod.component;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardFormComponent, {}, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\add-new-card-modal.tsx\",\n        lineNumber: 49,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AddNewCardModal, \"PQM6SJS2XhZaePJD9f+bxkUMusg=\", false, function() {\n    return [\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_2__.useModalState,\n        _framework_settings__WEBPACK_IMPORTED_MODULE_4__.useSettings\n    ];\n});\n_c1 = AddNewCardModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddNewCardModal);\nvar _c, _c1;\n$RefreshReg$(_c, \"StripeNotAvailable\");\n$RefreshReg$(_c1, \"AddNewCardModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/card/add-new-card-modal.tsx\n"));

/***/ }),

/***/ "./src/components/card/stripe/stripe-card-form.tsx":
/*!*********************************************************!*\
  !*** ./src/components/card/stripe/stripe-card-form.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _framework_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/framework/card */ \"./src/framework/rest/card.ts\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"./node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_payment_stripe_stripe_base_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/payment/stripe/stripe-base-form */ \"./src/components/payment/stripe/stripe-base-form.tsx\");\n/* harmony import */ var _lib_get_stripejs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/get-stripejs */ \"./src/lib/get-stripejs.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CardForm = ()=>{\n    _s();\n    const stripe = (0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.useStripe)();\n    const elements = (0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.useElements)();\n    const { addNewCard, isLoading } = (0,_framework_card__WEBPACK_IMPORTED_MODULE_1__.useAddCards)();\n    const [defaultCard, setDefaultCard] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [cardError, setCardError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const handleSubmit = async (event)=>{\n        var _event_target_owner_name, _event_target;\n        event.preventDefault();\n        if (!stripe || !elements) {\n            return;\n        }\n        setLoading(true);\n        const cardElement = elements.getElement(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.CardNumberElement);\n        const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({\n            type: \"card\",\n            card: cardElement,\n            billing_details: {\n                name: event === null || event === void 0 ? void 0 : (_event_target = event.target) === null || _event_target === void 0 ? void 0 : (_event_target_owner_name = _event_target.owner_name) === null || _event_target_owner_name === void 0 ? void 0 : _event_target_owner_name.value\n            }\n        });\n        if (paymentMethodError) {\n            setCardError(paymentMethodError === null || paymentMethodError === void 0 ? void 0 : paymentMethodError.message);\n            setLoading(false);\n        } else {\n            setLoading(false);\n            await addNewCard({\n                method_key: paymentMethod === null || paymentMethod === void 0 ? void 0 : paymentMethod.id,\n                default_card: defaultCard,\n                //@ts-ignore\n                payment_gateway: \"stripe\"\n            });\n        }\n    };\n    const changeDefaultCard = ()=>{\n        setDefaultCard(!defaultCard);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(cardError)) {\n            setTimeout(()=>{\n                setCardError(\"\");\n            }, 5000);\n        }\n    }, [\n        cardError\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_payment_stripe_stripe_base_form__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        handleSubmit: handleSubmit,\n        type: \"save_card\",\n        loading: loading || isLoading,\n        cardError: cardError,\n        defaultCard: defaultCard,\n        changeDefaultCard: changeDefaultCard\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\stripe\\\\stripe-card-form.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CardForm, \"CgC0f2r5Xe5F3MTkPteq2Bur97k=\", false, function() {\n    return [\n        _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.useStripe,\n        _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.useElements,\n        _framework_card__WEBPACK_IMPORTED_MODULE_1__.useAddCards\n    ];\n});\n_c = CardForm;\nconst StripeCardForm = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_2__.Elements, {\n        stripe: (0,_lib_get_stripejs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardForm, {}, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\stripe\\\\stripe-card-form.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\card\\\\stripe\\\\stripe-card-form.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = StripeCardForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StripeCardForm);\nvar _c, _c1;\n$RefreshReg$(_c, \"CardForm\");\n$RefreshReg$(_c1, \"StripeCardForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/card/stripe/stripe-card-form.tsx\n"));

/***/ }),

/***/ "./src/components/payment/stripe-element-view-header.tsx":
/*!***************************************************************!*\
  !*** ./src/components/payment/stripe-element-view-header.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst StipeElementViewHeader = (param)=>{\n    let { paymentIntentInfo, trackingNumber, paymentGateway } = param;\n    _s();\n    const { openModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalAction)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_3__.useSettings)();\n    const handleAddNewCard = ()=>{\n        openModal(\"STRIPE_ELEMENT_MODAL\", {\n            paymentIntentInfo,\n            trackingNumber,\n            paymentGateway\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-8 flex items-center justify-between sm:mb-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-center text-lg font-semibold text-heading sm:text-xl\",\n                    children: t(\"profile-new-cards\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe-element-view-header.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                Boolean(settings === null || settings === void 0 ? void 0 : settings.StripeCardOnly) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center text-sm font-semibold text-accent capitalize\",\n                    onClick: handleAddNewCard,\n                    children: t(\"Try another method\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe-element-view-header.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe-element-view-header.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(StipeElementViewHeader, \"ghUK9gljqoKHe+uSqMazdusRMKI=\", false, function() {\n    return [\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalAction,\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _framework_settings__WEBPACK_IMPORTED_MODULE_3__.useSettings\n    ];\n});\n_c = StipeElementViewHeader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StipeElementViewHeader);\nvar _c;\n$RefreshReg$(_c, \"StipeElementViewHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/stripe-element-view-header.tsx\n"));

/***/ }),

/***/ "./src/components/payment/stripe/stripe-base-form.tsx":
/*!************************************************************!*\
  !*** ./src/components/payment/stripe/stripe-base-form.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/forms/label */ \"./src/components/ui/forms/label.tsx\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_forms_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/forms/input */ \"./src/components/ui/forms/input.tsx\");\n/* harmony import */ var _components_ui_forms_checkbox_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/forms/checkbox/checkbox */ \"./src/components/ui/forms/checkbox/checkbox.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/isEmpty */ \"./node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/framework/card */ \"./src/framework/rest/card.ts\");\n/* harmony import */ var _stripe_element_view_header__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../stripe-element-view-header */ \"./src/components/payment/stripe-element-view-header.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst StripeBaseForm = (param)=>{\n    let { handleSubmit, type = \"save_card\", loading = false, changeSaveCard, saveCard, changeDefaultCard, defaultCard, cardError } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const { isAuthorized } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const { openModal, closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_10__.useModalAction)();\n    const { cards, isLoading, error } = (0,_framework_card__WEBPACK_IMPORTED_MODULE_11__.useCards)();\n    const { data: { paymentGateway, paymentIntentInfo, trackingNumber } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_10__.useModalState)();\n    const cardInputStyle = {\n        base: {\n            \"::placeholder\": {\n                color: \"#000000\"\n            }\n        }\n    };\n    const backModal = ()=>{\n        openModal(\"PAYMENT_MODAL\", {\n            paymentGateway,\n            paymentIntentInfo,\n            trackingNumber\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"payment-modal relative h-full w-screen max-w-md overflow-hidden rounded-[10px] bg-light md:h-auto md:min-h-0 lg:max-w-[46rem]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 lg:p-12\",\n            children: [\n                !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default()(cardError) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"mb-4\",\n                    message: cardError,\n                    variant: \"error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined) : \"\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_element_view_header__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    paymentIntentInfo: paymentIntentInfo,\n                    trackingNumber: trackingNumber,\n                    paymentGateway: paymentGateway\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"flex flex-col gap-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mb-2 block text-sm font-semibold text-black\",\n                                        children: t(\"text-name\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        name: \"owner_name\",\n                                        placeholder: t(\"text-name\"),\n                                        required: true,\n                                        inputClassName: \"h-auto rounded border border-solid border-[#D4D8DD] bg-white py-[14px] px-4 text-black transition-all duration-300 focus:shadow-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"mb-0 block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mb-2 block text-sm font-semibold text-black\",\n                                        children: t(\"text-card-number\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4__.CardNumberElement, {\n                                        options: {\n                                            showIcon: true,\n                                            style: cardInputStyle,\n                                            placeholder: t(\"text-card-number\")\n                                        },\n                                        className: \"h-auto rounded border border-solid border-[#D4D8DD] bg-white py-[14px] px-4 text-black transition-all duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-5 lg:flex-nowrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"mb-0 max-w-full basis-full lg:max-w-[50%] lg:basis-1/2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mb-2 block text-sm font-semibold text-black\",\n                                            children: t(\"text-card-expiry\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4__.CardExpiryElement, {\n                                            options: {\n                                                style: cardInputStyle,\n                                                placeholder: t(\"text-expire-date-placeholder\")\n                                            },\n                                            className: \"h-auto rounded border border-solid border-[#D4D8DD] bg-white py-[14px] px-4 text-black transition-all duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_label__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"mb-0 max-w-full basis-full lg:max-w-[50%] lg:basis-1/2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mb-2 block text-sm font-semibold text-black\",\n                                            children: t(\"text-card-cvc\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_4__.CardCvcElement, {\n                                            options: {\n                                                style: cardInputStyle,\n                                                placeholder: t(\"text-card-cvc\")\n                                            },\n                                            className: \"h-auto rounded border border-solid border-[#D4D8DD] bg-white py-[14px] px-4 text-black transition-all duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        isAuthorized && type === \"checkout\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_checkbox_checkbox__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            name: \"save_card\",\n                            label: t(\"text-save-card\"),\n                            className: \"mt-3\",\n                            onChange: changeSaveCard,\n                            checked: saveCard\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined),\n                        isAuthorized && type === \"save_card\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_checkbox_checkbox__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            name: \"make_default_card\",\n                            label: t(\"text-add-default-card\"),\n                            className: \"mt-3\",\n                            onChange: changeDefaultCard,\n                            checked: defaultCard\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-x-4 lg:mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    type: \"submit\",\n                                    loading: loading,\n                                    disabled: loading,\n                                    className: \"StripePay px-11 text-sm shadow-none\",\n                                    children: type === \"checkout\" ? t(\"text-pay\") : t(\"text-save\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                isAuthorized && type === \"checkout\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    type: \"submit\",\n                                    variant: \"outline\",\n                                    disabled: !!loading,\n                                    className: \"px-11 text-sm shadow-none\",\n                                    onClick: closeModal,\n                                    children: t(\"pay-latter\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined),\n                                isAuthorized && (cards === null || cards === void 0 ? void 0 : cards.length) > 0 && type === \"checkout\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    disabled: !!loading,\n                                    variant: \"outline\",\n                                    className: \"cursor-pointer\",\n                                    onClick: backModal,\n                                    children: t(\"text-back\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\stripe\\\\stripe-base-form.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StripeBaseForm, \"8LHaoJE6Z37ohZi1zwKRZDqQo1M=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _framework_user__WEBPACK_IMPORTED_MODULE_9__.useUser,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_10__.useModalAction,\n        _framework_card__WEBPACK_IMPORTED_MODULE_11__.useCards,\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_10__.useModalState\n    ];\n});\n_c = StripeBaseForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StripeBaseForm);\nvar _c;\n$RefreshReg$(_c, \"StripeBaseForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/stripe/stripe-base-form.tsx\n"));

/***/ }),

/***/ "./src/components/ui/forms/checkbox/checkbox.tsx":
/*!*******************************************************!*\
  !*** ./src/components/ui/forms/checkbox/checkbox.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef(_c = (param, ref)=>{\n    let { className, label, name, error, theme = \"primary\", ...rest } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: name,\n                        name: name,\n                        type: \"checkbox\",\n                        ref: ref,\n                        className: \"checkbox\",\n                        ...rest\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\checkbox\\\\checkbox.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: name,\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(className, \"text-body text-sm\", {\n                            primary: theme === \"primary\",\n                            secondary: theme === \"secondary\"\n                        }),\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\checkbox\\\\checkbox.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\checkbox\\\\checkbox.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"my-2 text-xs ltr:text-right rtl:text-left text-red-500\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\checkbox\\\\checkbox.tsx\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\checkbox\\\\checkbox.tsx\",\n        lineNumber: 15,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Checkbox;\nCheckbox.displayName = \"Checkbox\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Checkbox);\nvar _c, _c1;\n$RefreshReg$(_c, \"Checkbox$React.forwardRef\");\n$RefreshReg$(_c1, \"Checkbox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/forms/checkbox/checkbox.tsx\n"));

/***/ }),

/***/ "./src/components/ui/forms/label.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/forms/label.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Label = (param)=>{\n    let { className, ...rest } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"block text-body-dark font-semibold text-sm leading-none mb-3\", className),\n        ...rest\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\ui\\\\forms\\\\label.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Label;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Label);\nvar _c;\n$RefreshReg$(_c, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9mb3Jtcy9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRCO0FBRTVCLE1BQU1DLFFBQStEO1FBQUMsRUFDcEVDLFNBQVMsRUFDVCxHQUFHQyxNQUNKO0lBQ0MscUJBQ0UsOERBQUNDO1FBQ0NGLFdBQVdGLGlEQUFFQSxDQUNYLGdFQUNBRTtRQUVELEdBQUdDLElBQUk7Ozs7OztBQUdkO0tBYk1GO0FBZU4sK0RBQWVBLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvZm9ybXMvbGFiZWwudHN4PzU0OTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnO1xyXG5cclxuY29uc3QgTGFiZWw6IFJlYWN0LkZDPFJlYWN0LkxhYmVsSFRNTEF0dHJpYnV0ZXM8SFRNTExhYmVsRWxlbWVudD4+ID0gKHtcclxuICBjbGFzc05hbWUsXHJcbiAgLi4ucmVzdFxyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxsYWJlbFxyXG4gICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICdibG9jayB0ZXh0LWJvZHktZGFyayBmb250LXNlbWlib2xkIHRleHQtc20gbGVhZGluZy1ub25lIG1iLTMnLFxyXG4gICAgICAgIGNsYXNzTmFtZVxyXG4gICAgICApfVxyXG4gICAgICB7Li4ucmVzdH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IExhYmVsO1xyXG4iXSwibmFtZXMiOlsiY24iLCJMYWJlbCIsImNsYXNzTmFtZSIsInJlc3QiLCJsYWJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/ui/forms/label.tsx\n"));

/***/ }),

/***/ "./src/framework/rest/card.ts":
/*!************************************!*\
  !*** ./src/framework/rest/card.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAddCards: function() { return /* binding */ useAddCards; },\n/* harmony export */   useCards: function() { return /* binding */ useCards; },\n/* harmony export */   useDefaultPaymentMethod: function() { return /* binding */ useDefaultPaymentMethod; },\n/* harmony export */   useDeleteCard: function() { return /* binding */ useDeleteCard; }\n/* harmony export */ });\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var _framework_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/framework/client/api-endpoints */ \"./src/framework/rest/client/api-endpoints.ts\");\n/* harmony import */ var _framework_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/client */ \"./src/framework/rest/client/index.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _framework_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/user */ \"./src/framework/rest/user.ts\");\n\n\n\n\n\n\n\nfunction useCards(params, options) {\n    const { isAuthorized } = (0,_framework_user__WEBPACK_IMPORTED_MODULE_6__.useUser)();\n    const { data, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)([\n        _framework_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CARDS,\n        params\n    ], ()=>_framework_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].cards.all(params), {\n        enabled: isAuthorized,\n        ...options\n    });\n    return {\n        cards: data !== null && data !== void 0 ? data : [],\n        isLoading,\n        error\n    };\n}\nconst useDeleteCard = ()=>{\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__.useModalAction)();\n    const { mutate, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_framework_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].cards.remove, {\n        onSuccess: ()=>{\n            closeModal();\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(t(\"common:card-successfully-deleted\")));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_framework_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CARDS);\n        }\n    });\n    return {\n        deleteCard: mutate,\n        isLoading,\n        error\n    };\n};\nfunction useAddCards(method_key) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_3__.useModalAction)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { mutate, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_framework_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].cards.addPaymentMethod, {\n        onSuccess: ()=>{\n            closeModal();\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(t(\"common:card-successfully-add\")), {\n                toastId: \"success\"\n            });\n        },\n        onError: (error)=>{\n            const { response: { data } } = error !== null && error !== void 0 ? error : {};\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"\".concat(t(data === null || data === void 0 ? void 0 : data.message)), {\n                toastId: \"error\"\n            });\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_framework_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CARDS);\n        }\n    });\n    return {\n        addNewCard: mutate,\n        isLoading,\n        error\n    };\n}\nfunction useDefaultPaymentMethod() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useQueryClient)();\n    const { mutate, isLoading, error } = (0,react_query__WEBPACK_IMPORTED_MODULE_0__.useMutation)(_framework_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].cards.makeDefaultPaymentMethod, {\n        onSuccess: ()=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(t(\"common:set-default-card-message\")));\n        },\n        // Always refetch after error or success:\n        onSettled: ()=>{\n            queryClient.invalidateQueries(_framework_client_api_endpoints__WEBPACK_IMPORTED_MODULE_1__.API_ENDPOINTS.CARDS);\n        }\n    });\n    return {\n        createDefaultPaymentMethod: mutate,\n        isLoading,\n        error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/framework/rest/card.ts\n"));

/***/ }),

/***/ "./src/lib/get-stripejs.ts":
/*!*********************************!*\
  !*** ./src/lib/get-stripejs.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"./node_modules/@stripe/stripe-js/dist/stripe.esm.js\");\n/**\r\n * This is a singleton to ensure we only instantiate Stripe once.\r\n */ \nlet stripePromise;\nconst getStripe = ()=>{\n    if (!stripePromise) {\n        stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(\"\");\n    }\n    return stripePromise;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (getStripe);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2dldC1zdHJpcGVqcy50cyIsIm1hcHBpbmdzIjoiOztBQUFBOztDQUVDLEdBQ3NEO0FBRXZELElBQUlDO0FBQ0osTUFBTUMsWUFBWTtJQUNoQixJQUFJLENBQUNELGVBQWU7UUFDbEJBLGdCQUFnQkQsNkRBQVVBLENBQUNHLEVBQThDO0lBQzNFO0lBQ0EsT0FBT0Y7QUFDVDtBQUVBLCtEQUFlQyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvZ2V0LXN0cmlwZWpzLnRzP2ZjZGYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIFRoaXMgaXMgYSBzaW5nbGV0b24gdG8gZW5zdXJlIHdlIG9ubHkgaW5zdGFudGlhdGUgU3RyaXBlIG9uY2UuXHJcbiAqL1xyXG5pbXBvcnQgeyBTdHJpcGUsIGxvYWRTdHJpcGUgfSBmcm9tICdAc3RyaXBlL3N0cmlwZS1qcyc7XHJcblxyXG5sZXQgc3RyaXBlUHJvbWlzZTogUHJvbWlzZTxTdHJpcGUgfCBudWxsPjtcclxuY29uc3QgZ2V0U3RyaXBlID0gKCkgPT4ge1xyXG4gIGlmICghc3RyaXBlUHJvbWlzZSkge1xyXG4gICAgc3RyaXBlUHJvbWlzZSA9IGxvYWRTdHJpcGUocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1RSSVBFX1BVQkxJU0hBQkxFX0tFWSEpO1xyXG4gIH1cclxuICByZXR1cm4gc3RyaXBlUHJvbWlzZTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGdldFN0cmlwZTtcclxuIl0sIm5hbWVzIjpbImxvYWRTdHJpcGUiLCJzdHJpcGVQcm9taXNlIiwiZ2V0U3RyaXBlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NUUklQRV9QVUJMSVNIQUJMRV9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/get-stripejs.ts\n"));

/***/ }),

/***/ "./src/lib/is-stripe-available.ts":
/*!****************************************!*\
  !*** ./src/lib/is-stripe-available.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isStripeAvailable: function() { return /* binding */ isStripeAvailable; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"./src/types/index.ts\");\n\n/**\r\n *\r\n *  Utility method to find out is stripe is available as a active payment gateway\r\n *\r\n */ function isStripeAvailable(props) {\n    const { defaultPaymentGateway, paymentGateway } = props;\n    let processPaymentGatewayName = [];\n    if (Boolean(paymentGateway) && Array.isArray(paymentGateway)) {\n        const PaymentGatewaysName = [\n            ...paymentGateway\n        ].map((p)=>p.name.toUpperCase());\n        processPaymentGatewayName = [\n            ...PaymentGatewaysName\n        ];\n    }\n    // relation would be\n    //\n    // false false = false\n    // true false = true\n    // false true = true\n    // true true = true\n    // check if stripe exists in default payment gateway\n    let isStripeDefault = false;\n    if ((defaultPaymentGateway === null || defaultPaymentGateway === void 0 ? void 0 : defaultPaymentGateway.toUpperCase()) === _types__WEBPACK_IMPORTED_MODULE_0__.PaymentGateway.STRIPE) {\n        isStripeDefault = true;\n    }\n    // check if stripe exists in selected payment gateways\n    let isStripeAsChosen = false;\n    if (processPaymentGatewayName.includes(_types__WEBPACK_IMPORTED_MODULE_0__.PaymentGateway.STRIPE)) {\n        isStripeAsChosen = true;\n    }\n    let isStripeAvailable = false;\n    if (isStripeAsChosen || isStripeDefault) {\n        isStripeAvailable = true;\n    }\n    return isStripeAvailable;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/is-stripe-available.ts\n"));

/***/ })

}]);