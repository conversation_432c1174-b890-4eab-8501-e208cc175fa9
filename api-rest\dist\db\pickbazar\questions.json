[{"id": 68, "user_id": 2, "shop_id": 5, "product_id": 470, "question": "Is it sweet or sour?", "answer": "its sweet as it contains suger.", "deleted_at": null, "created_at": "2022-03-18T10:19:46.000000Z", "updated_at": "2022-03-18T10:20:42.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 470, "name": "Sun Tropics Organic Mango Nectar,250ml", "slug": "sun-tropics-organic-mango-nectar-250ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice", "type_id": 2, "price": 2.25, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 2.25, "max_price": 2.25, "sku": "3003", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "493", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/492/Juice-1_lx8xnf.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/492/conversions/Juice-1_lx8xnf-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-14T09:16:18.000000Z", "updated_at": "2023-10-12T04:44:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 0, "total_reviews": 0, "rating_count": [], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 67, "user_id": 2, "shop_id": 5, "product_id": 468, "question": "Does it have preservatives?", "answer": "No, this one does not have any preservatives", "deleted_at": null, "created_at": "2022-03-18T10:19:24.000000Z", "updated_at": "2022-03-18T10:21:00.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 468, "name": "Wonderful Pomegranate Juice, 300 ml", "slug": "wonderful-pomegranate-juice-300-ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice", "type_id": 2, "price": 3, "shop_id": 5, "sale_price": 2.4, "language": "en", "min_price": 3, "max_price": 3, "sku": "3001", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "491", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/490/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/490/conversions/Juice5_bz8od4-thumbnail.jpg"}, "video": null, "gallery": [{"id": "807", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/805/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/805/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "808", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/806/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/806/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "809", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/807/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/807/conversions/Juice5_bz8od4-thumbnail.jpg"}, {"id": "810", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/808/Juice5_bz8od4.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/808/conversions/Juice5_bz8od4-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:14:15.000000Z", "updated_at": "2023-10-12T04:44:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 66, "user_id": 2, "shop_id": 5, "product_id": 467, "question": "How many calories does it have?", "answer": "its low on calories 30cal", "deleted_at": null, "created_at": "2022-03-18T10:18:47.000000Z", "updated_at": "2022-03-18T10:21:15.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 467, "name": "Ritai Organic Orange Juice 500 ml", "slug": "ritai-organic-orange-juice-500-ml", "description": "Juice is a drink made from the extraction or pressing of the natural liquid contained in fruit and vegetables. It can also refer to liquids that are flavored with concentrate or other biological food sources, such as meat or seafood, such as clam juice", "type_id": 2, "price": 1.8, "shop_id": 5, "sale_price": null, "language": "en", "min_price": 1.8, "max_price": 1.8, "sku": "3000", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "490", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/489/Juice-5_eqrtuu.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/489/conversions/Juice-5_eqrtuu-thumbnail.jpg"}, "video": null, "gallery": [{"id": "605", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/604/Organic-orange-juice-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/604/conversions/Organic-orange-juice-1-thumbnail.jpg"}, {"id": "606", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/605/Organic-orange-juice.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/605/conversions/Organic-orange-juice-thumbnail.jpg"}, {"id": "795", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/793/Juice-5_eqrtuu.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/793/conversions/Juice-5_eqrtuu-thumbnail.jpg"}, {"id": "796", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/794/Juice-1_lx8xnf.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/794/conversions/Juice-1_lx8xnf-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T09:13:11.000000Z", "updated_at": "2023-10-12T04:44:06.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4, "total_reviews": 1, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 65, "user_id": 2, "shop_id": 4, "product_id": 25, "question": "Is it original?", "answer": "yes its original", "deleted_at": null, "created_at": "2022-03-18T10:18:21.000000Z", "updated_at": "2022-03-18T10:23:36.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 25, "name": "<PERSON><PERSON>ins Everlasting Compact Foundation", "slug": "clarins-everlasting-compact-foundation", "description": "A 15-hour matte finish your skin will feel good about! <PERSON><PERSON><PERSON>' long-wearing compact foundation evens out skin tone and minimizes the look of imperfections in seconds, delivering a shine-free, matte finish and hours of comfortable wear. Ultra-fine texture resists heat, humidity and perspiration for flawless coverage that lasts throughout the day.", "type_id": 3, "price": 54, "shop_id": 4, "sale_price": null, "language": "en", "min_price": 54, "max_price": 54, "sku": "1005", "quantity": 60, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "26", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/26/Clarins.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/26/conversions/Clar<PERSON>-thumbnail.jpg"}, "video": null, "gallery": [{"id": "656", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/655/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/655/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "657", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/656/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/656/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "660", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/659/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/659/conversions/Blusher-2-thumbnail.jpg"}, {"id": "764", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/763/Clarins.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/763/conversions/Clar<PERSON>-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:31:30.000000Z", "updated_at": "2023-11-03T17:37:17.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 64, "user_id": 2, "shop_id": 4, "product_id": 23, "question": "Do you have the big ones as well?", "answer": "Unfortunately, this is the only ones we have.", "deleted_at": null, "created_at": "2022-03-18T10:17:59.000000Z", "updated_at": "2022-03-18T10:23:54.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 23, "name": "Smashbox The Cali Contour Palette", "slug": "smashbox-the-cali-contour-palette", "description": "An easy-to-use, six-well contour kit with pigment-packed, blendable highlighter, bronzer, and blush powders. Use these versatile shades to create an effortlessly lifted neutral look or a warm, just-cruised-down-the-coast glow. It features three matte and two pearlescent powders to shape, bronze, and highlight. It also includes one matte blush to add a youthful flush of subtle color to any look. It is formulated Without: - Parabens- Phthalates This laid-back makeup palette makes it easy to add warmth and dimension to your look. Customize your signature Cali glow using six neutrals, including two of Smashbox's bestselling contour shades. This product is cruelty-free and formulated without parabens, phthalates, fragrance.", "type_id": 3, "price": 42, "shop_id": 4, "sale_price": 38.59, "language": "en", "min_price": 42, "max_price": 42, "sku": "1003", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "24", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/24/Smashbox.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/24/conversions/Smashbox-thumbnail.jpg"}, "video": null, "gallery": [{"id": "644", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/643/Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/643/conversions/Blusher-1-thumbnail.jpg"}, {"id": "645", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/644/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/644/conversions/Blusher-2-thumbnail.jpg"}, {"id": "646", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/645/Blusher.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/645/conversions/Blusher-thumbnail.jpg"}, {"id": "759", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/758/Smashbox.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/758/conversions/Smashbox-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:26:00.000000Z", "updated_at": "2023-10-12T04:43:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 63, "user_id": 2, "shop_id": 4, "product_id": 22, "question": "Is it possible to get some more discount?", "answer": "Unfortunaltey we do not have any further discounts.", "deleted_at": null, "created_at": "2022-03-18T10:16:48.000000Z", "updated_at": "2022-03-18T10:24:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 22, "name": "Cyo Crush On Blush Powder Blush", "slug": "cyo-crush-on-blush-powder-blush", "description": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher has been keeping women beautiful for generations. Made in an exclusive baked technology process, its incredibly transparent & light texture formula is easy to apply and blends impeccably.", "type_id": 3, "price": 11, "shop_id": 4, "sale_price": 8, "language": "en", "min_price": 11, "max_price": 11, "sku": "1002", "quantity": 60, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "23", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/23/CYO.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/23/conversions/CYO-thumbnail.jpg"}, "video": null, "gallery": [{"id": "640", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/639/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/639/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "641", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/640/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/640/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "643", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/642/Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/642/conversions/Blusher-2-thumbnail.jpg"}, {"id": "758", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/757/CYO.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/757/conversions/CYO-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:23:13.000000Z", "updated_at": "2023-10-12T04:43:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4, "total_reviews": 1, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 62, "user_id": 2, "shop_id": 4, "product_id": 21, "question": "What other shades you have?", "answer": "We have 26 other shades please check our profile for full shades.", "deleted_at": null, "created_at": "2022-03-18T10:16:25.000000Z", "updated_at": "2022-03-18T10:28:53.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 21, "name": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher", "slug": "bourjois-little-round-pot-blusher", "description": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher has been keeping women beautiful for generations. Made in an exclusive baked technology process, its incredibly transparent & light texture formula is easy to apply and blends impeccably.", "type_id": 3, "price": 9, "shop_id": 4, "sale_price": 8, "language": "en", "min_price": 9, "max_price": 9, "sku": "1001", "quantity": 49, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "22", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/Bourjois.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/conversions/Bo<PERSON><PERSON><PERSON>-thumbnail.jpg"}, "video": null, "gallery": [{"id": "637", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "638", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "639", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/Bour<PERSON>is-Little-Round-Pot-Blusher.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/conversions/<PERSON><PERSON><PERSON><PERSON>-<PERSON>-Round-Pot-<PERSON>sher-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:00:30.000000Z", "updated_at": "2023-10-12T04:43:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 61, "user_id": 2, "shop_id": 4, "product_id": 21, "question": "Does it come with brush?", "answer": "Yes", "deleted_at": null, "created_at": "2022-03-18T10:16:11.000000Z", "updated_at": "2022-03-18T10:31:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 21, "name": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher", "slug": "bourjois-little-round-pot-blusher", "description": "<PERSON><PERSON><PERSON><PERSON> Little Round Pot Blusher has been keeping women beautiful for generations. Made in an exclusive baked technology process, its incredibly transparent & light texture formula is easy to apply and blends impeccably.", "type_id": 3, "price": 9, "shop_id": 4, "sale_price": 8, "language": "en", "min_price": 9, "max_price": 9, "sku": "1001", "quantity": 49, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1pc(s)", "height": null, "width": null, "length": null, "image": {"id": "22", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/Bourjois.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/22/conversions/Bo<PERSON><PERSON><PERSON>-thumbnail.jpg"}, "video": null, "gallery": [{"id": "637", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/Bour<PERSON>is-Little-Round-Pot-Blusher-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/636/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-1-thumbnail.jpg"}, {"id": "638", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/Bour<PERSON>is-Little-Round-Pot-Blusher-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/637/conversions/<PERSON><PERSON><PERSON><PERSON>-Little-Round-Pot-Blusher-2-thumbnail.jpg"}, {"id": "639", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/Bour<PERSON>is-Little-Round-Pot-Blusher.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/638/conversions/<PERSON><PERSON><PERSON><PERSON>-<PERSON>-Round-Pot-<PERSON>sher-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T06:00:30.000000Z", "updated_at": "2023-10-12T04:43:35.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 60, "user_id": 2, "shop_id": 3, "product_id": 105, "question": "Is there any variation available?", "answer": "Unfortunately no", "deleted_at": null, "created_at": "2022-03-18T10:14:40.000000Z", "updated_at": "2022-03-18T10:32:07.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 105, "name": "Givenchy Mini Purse", "slug": "givenchy-mini-purse", "description": "Luxury Italian fashion house Gucci is renowned for its instantly recognisable bags and accessories, infusing its unique sense of quality and exquisite design into each piece. This pink logo print leather backpack from Gucci features top handles, a drawstring fastening, a pebbled leather texture, a removable zipped pouch and a vintage Gucci logo.", "type_id": 4, "price": 80, "shop_id": 3, "sale_price": 70, "language": "en", "min_price": 80, "max_price": 80, "sku": "2003", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "106", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/106/gucci_mini_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/106/conversions/gucci_mini_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:19:22.000000Z", "updated_at": "2023-10-12T04:43:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 59, "user_id": 2, "shop_id": 3, "product_id": 107, "question": "Does it have any color variation?", "answer": "Yes, its also available in black and brown", "deleted_at": null, "created_at": "2022-03-18T10:14:11.000000Z", "updated_at": "2022-03-18T10:39:25.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 107, "name": "Armani Silver Purse", "slug": "armani-silver-purse", "description": "The name <PERSON> has become synonymous with clean lines and Italian style. One of the most recognisable fashion houses in the world, the label has dressed some of the world’s most beautiful women.", "type_id": 4, "price": 120, "shop_id": 3, "sale_price": null, "language": "en", "min_price": 120, "max_price": 120, "sku": "2006", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "108", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/108/91PirQjxGjL._UL1500_.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/108/conversions/91PirQjxGjL._UL1500_-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:40:49.000000Z", "updated_at": "2023-10-12T04:43:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 58, "user_id": 2, "shop_id": 3, "product_id": 104, "question": "Does it have any color variation?", "answer": "Unfortunatley no", "deleted_at": null, "created_at": "2022-03-18T10:13:52.000000Z", "updated_at": "2022-03-18T10:39:33.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 104, "name": "Givenchy Purse", "slug": "givenchy-purse", "description": "Established in 1952, Givenchy's stance on contemporary elegance is perfectly captured through the brand’s premium accessory collections. Crafted from calf leather, this grey GV3 croc-effect shoulder bag from Givenchy features a chain top handle with logo charm, a detachable shoulder strap, a front flap closure, a metal logo plaque to the front, gold-tone hardware and suede panels.", "type_id": 4, "price": 75, "shop_id": 3, "sale_price": 60, "language": "en", "min_price": 75, "max_price": 75, "sku": "2002", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "105", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/105/givency_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/105/conversions/givency_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:18:11.000000Z", "updated_at": "2023-10-12T04:43:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 57, "user_id": 2, "shop_id": 3, "product_id": 103, "question": "How many pockets does it have?", "answer": "3 pckets", "deleted_at": null, "created_at": "2022-03-18T10:13:27.000000Z", "updated_at": "2022-03-18T10:39:40.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 103, "name": "<PERSON><PERSON>", "slug": "armani-purse", "description": "Black logo embossed messenger bag from <PERSON> featuring an adjustable shoulder strap, a top zip fastening and a front zip pocket.", "type_id": 4, "price": 80, "shop_id": 3, "sale_price": 72, "language": "en", "min_price": 80, "max_price": 80, "sku": "2002", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "104", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/104/Armani_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/104/conversions/Armani_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:16:29.000000Z", "updated_at": "2023-10-12T04:43:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 56, "user_id": 2, "shop_id": 3, "product_id": 102, "question": "Hi\nWhat is the maximum capacity.", "answer": "3-4 kgs", "deleted_at": null, "created_at": "2022-03-18T10:12:16.000000Z", "updated_at": "2022-03-18T10:39:51.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 102, "name": "<PERSON><PERSON>", "slug": "armani-leather-purse", "description": "The name <PERSON> has become synonymous with clean lines and Italian style. One of the most recognisable fashion houses in the world, the label has dressed some of the world’s most beautiful women.", "type_id": 4, "price": 50, "shop_id": 3, "sale_price": 40, "language": "en", "min_price": 50, "max_price": 50, "sku": "2001", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "103", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/103/Armani_leather_purse.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/103/conversions/Armani_leather_purse-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:12:48.000000Z", "updated_at": "2023-10-12T04:43:24.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 55, "user_id": 2, "shop_id": 2, "product_id": 113, "question": "Can you make it yellow?", "answer": "yes please provide us with the size details", "deleted_at": null, "created_at": "2022-03-18T09:22:43.000000Z", "updated_at": "2022-03-18T10:40:07.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 113, "name": "Forever 21 Solid Bodycon Midi Dress", "slug": "forever-21-solid-bodycon-midi-dress", "description": "Grey solid woven bodycon dress, has a round neck, sleeveless, straight hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 100, "max_price": 120, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "115", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/115/FOREVER_21.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/115/conversions/FOREVER_21-thumbnail.jpg"}, "video": null, "gallery": [{"id": "672", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/671/Striped-Dress.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/671/conversions/Striped-Dress-thumbnail.jpg"}, {"id": "768", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/767/FOREVER_21.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/767/conversions/FOREVER_21-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T18:01:23.000000Z", "updated_at": "2023-10-12T04:42:59.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 54, "user_id": 2, "shop_id": 2, "product_id": 111, "question": "Is it silk or linen?", "answer": "Its acutally cotton", "deleted_at": null, "created_at": "2022-03-18T09:22:17.000000Z", "updated_at": "2022-03-18T10:40:15.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 111, "name": "Mango Self Striped A Line Dress", "slug": "mango-self-striped-a-line-dress", "description": "Off-White self-striped knitted midi A-line dress, has a scoop neck, sleeveless, straight hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 70, "max_price": 81, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "112", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/112/mango.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/112/conversions/mango-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-09T17:56:41.000000Z", "updated_at": "2023-10-12T04:42:59.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 53, "user_id": 2, "shop_id": 2, "product_id": 116, "question": "Can you make XXL size?", "answer": "yes, it is possible.", "deleted_at": null, "created_at": "2022-03-18T09:21:59.000000Z", "updated_at": "2022-03-18T10:43:49.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 116, "name": "Fold Over Collar Plain Blazers", "slug": "fold-over-collar-plain-blazers", "description": "Black bandhgala, has a mandarin collar, a full button placket, long sleeves, three pockets, double vented back hem, and has an attached lining", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 199, "max_price": 200, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "117", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/117/Fold_over.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/117/conversions/Fold_over-thumbnail.jpg"}, "video": null, "gallery": [{"id": "692", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/691/Plain-Blazers-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/691/conversions/Plain-Blazers-4-thumbnail.jpg"}, {"id": "693", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/692/Plain-Blazers-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/692/conversions/Plain-Blazers-5-thumbnail.jpg"}, {"id": "774", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/773/Fold_over.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/773/conversions/Fold_over-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T18:04:53.000000Z", "updated_at": "2023-10-12T04:42:59.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 52, "user_id": 2, "shop_id": 2, "product_id": 110, "question": "Do you have red color for this dress?", "answer": "yes it is available.", "deleted_at": null, "created_at": "2022-03-18T09:21:14.000000Z", "updated_at": "2022-03-18T10:43:39.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 110, "name": "Magnetic Designs Women Printed Fit And Flare Dress", "slug": "magnetic-designs-women-printed-fit-and-flare-dress", "description": "Mauve printed knitted fit and flare dress, has a round neck, three-quarter sleeves, concealed zip closure,, flared hem", "type_id": 5, "price": null, "shop_id": 2, "sale_price": null, "language": "en", "min_price": 35, "max_price": 35, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "111", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/111/magnetic.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/111/conversions/magnetic-thumbnail.jpg"}, "video": null, "gallery": [{"id": "668", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/667/Printed-Dress-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/667/conversions/Printed-Dress-2-thumbnail.jpg"}, {"id": "669", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/668/Printed-Dress.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/668/conversions/Printed-Dress-thumbnail.jpg"}, {"id": "767", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/766/magnetic.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/766/conversions/magnetic-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-09T17:55:02.000000Z", "updated_at": "2023-10-12T04:42:59.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 51, "user_id": 2, "shop_id": 1, "product_id": 416, "question": "What foam has been used?", "answer": "4 inch poly foam", "deleted_at": null, "created_at": "2022-03-18T09:20:02.000000Z", "updated_at": "2022-03-18T10:43:28.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 416, "name": "Partex Coushoned Double Bed", "slug": "partex-coushoned-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 270, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 270, "max_price": 270, "sku": "2205", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "439", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/438/Partex.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/438/conversions/Partex-thumbnail.jpg"}, "video": null, "gallery": [{"id": "715", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/714/Bed-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/714/conversions/Bed-4-thumbnail.jpg"}, {"id": "716", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/715/Bed-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/715/conversions/Bed-5-thumbnail.jpg"}, {"id": "717", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/716/Bed-6.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/716/conversions/Bed-6-thumbnail.jpg"}, {"id": "784", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/783/Partex.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/783/conversions/Partex-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:53:46.000000Z", "updated_at": "2023-10-12T04:40:13.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 50, "user_id": 2, "shop_id": 1, "product_id": 414, "question": "Can you make it silver color?", "answer": "yes it is possible", "deleted_at": null, "created_at": "2022-03-18T09:19:43.000000Z", "updated_at": "2022-03-18T10:43:18.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 414, "name": "Deluxe Mahagony Double Bed", "slug": "deluxe-mahagony-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 300, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 300, "max_price": 300, "sku": "2202", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "437", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/436/Mahogany.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/436/conversions/Mahogany-thumbnail.jpg"}, "video": null, "gallery": [{"id": "712", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/711/Bed-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/711/conversions/Bed-1-thumbnail.jpg"}, {"id": "713", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/712/Bed-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/712/conversions/Bed-2-thumbnail.jpg"}, {"id": "714", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/713/Bed-3.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/713/conversions/Bed-3-thumbnail.jpg"}, {"id": "781", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/780/Mahogany.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/780/conversions/Mahogany-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:51:38.000000Z", "updated_at": "2023-10-12T04:40:13.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 49, "user_id": 2, "shop_id": 1, "product_id": 413, "question": "Can you make some customization?", "answer": "yes, please let us know in detail about your requirement.", "deleted_at": null, "created_at": "2022-03-18T09:19:26.000000Z", "updated_at": "2022-03-18T10:42:40.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 413, "name": "Brown Hardwood Double Bed", "slug": "brown-hardwood-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 250, "shop_id": 1, "sale_price": 220, "language": "en", "min_price": 250, "max_price": 250, "sku": "2201", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "436", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/435/Hardwoods.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/435/conversions/Hardwoods-thumbnail.jpg"}, "video": null, "gallery": [{"id": "709", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/708/Bed-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/708/conversions/Bed-1-thumbnail.jpg"}, {"id": "710", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/709/Bed-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/709/conversions/Bed-2-thumbnail.jpg"}, {"id": "711", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/710/Bed-3.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/710/conversions/Bed-3-thumbnail.jpg"}, {"id": "780", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/779/Hardwoods.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/779/conversions/Hardwoods-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:43:43.000000Z", "updated_at": "2023-10-12T04:40:13.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 48, "user_id": 2, "shop_id": 1, "product_id": 412, "question": "What wood you have used?", "answer": "It is processed Barmatik wood", "deleted_at": null, "created_at": "2022-03-18T09:18:54.000000Z", "updated_at": "2022-03-18T10:42:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 412, "name": "Ash Double Bed", "slug": "ash-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 250, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 250, "max_price": 250, "sku": "2200", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "435", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/conversions/Ash-thumbnail.jpg"}, "video": null, "gallery": [{"id": "706", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/Bed-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/conversions/Bed-4-thumbnail.jpg"}, {"id": "707", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/Bed-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/conversions/Bed-5-thumbnail.jpg"}, {"id": "708", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/Bed-6.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/conversions/Bed-6-thumbnail.jpg"}, {"id": "779", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/conversions/Ash-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:42:44.000000Z", "updated_at": "2023-10-12T04:40:13.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 47, "user_id": 2, "shop_id": 1, "product_id": 412, "question": "What other color you can offer?", "answer": "Any color of your choice.", "deleted_at": null, "created_at": "2022-03-18T09:18:46.000000Z", "updated_at": "2022-03-18T10:41:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 412, "name": "Ash Double Bed", "slug": "ash-double-bed", "description": "A bed is a piece of furniture which is used as a place to sleep or relax.", "type_id": 6, "price": 250, "shop_id": 1, "sale_price": null, "language": "en", "min_price": 250, "max_price": 250, "sku": "2200", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc(s)", "height": null, "width": null, "length": null, "image": {"id": "435", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/434/conversions/Ash-thumbnail.jpg"}, "video": null, "gallery": [{"id": "706", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/Bed-4.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/705/conversions/Bed-4-thumbnail.jpg"}, {"id": "707", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/Bed-5.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/706/conversions/Bed-5-thumbnail.jpg"}, {"id": "708", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/Bed-6.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/707/conversions/Bed-6-thumbnail.jpg"}, {"id": "779", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/Ash.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/778/conversions/Ash-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-14T07:42:44.000000Z", "updated_at": "2023-10-12T04:40:13.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 46, "user_id": 2, "shop_id": 7, "product_id": 930, "question": "Do you have Hebrew version?", "answer": "Yes we do have Hebrew version.", "deleted_at": null, "created_at": "2022-03-18T09:18:12.000000Z", "updated_at": "2022-03-18T10:41:47.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 930, "name": "Milan The Story of Love", "slug": "milan-the-story-of-love", "description": "The runes of Lyrical Ditties designedlyre-imagined the way poetry should sound\"By fitting to rhythmic arrangement a selection of the real language of men,\"<PERSON><PERSON> and his English coevals, similar as <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, wrote poetry that was meant to boil up from serious, reflective reflection over the commerce of humans with their terrain. Although numerous stress the notion of naturalness in Romantic poetry, the movement was still greatly concerned with the difficulty of composition and of rephrasing these feelings into lyrical form. Indeed, <PERSON><PERSON>, in his essay On Poesy or Art, sees art as “ the mediatress between, and jurist of nature and man”. Such an station reflects what might be called the dominant theme of English Romantic poetry the filtering of natural emotion through the mortal mind in order to produce meaning.", "type_id": 8, "price": 160, "shop_id": 7, "sale_price": 150, "language": "en", "min_price": 160, "max_price": 160, "sku": "+g+df5gda56f14dsa5f456sdf", "quantity": 500, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1630, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1630/Romantic-Books.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1630/conversions/Romantic-Books-thumbnail.jpg"}, "video": [], "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:17:46.000000Z", "updated_at": "2023-10-25T07:58:46.000000Z", "author_id": 11, "manufacturer_id": 4, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3, "total_reviews": 1, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 45, "user_id": 2, "shop_id": 7, "product_id": 931, "question": "How long will the delivery take?", "answer": "1-2 days depending on your location.", "deleted_at": null, "created_at": "2022-03-18T09:17:51.000000Z", "updated_at": "2022-03-18T10:41:33.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 931, "name": "Space Force First Chapter", "slug": "space-force-first-chapter", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": 200, "shop_id": 7, "sale_price": 180, "language": "en", "min_price": 200, "max_price": 200, "sku": "df5as+f5sda+f5s9+f5sda+5f9+sa5fsf+", "quantity": 500, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1628, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/science-fiction-6.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/conversions/science-fiction-6-thumbnail.jpg"}, "video": [], "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2023-10-25T07:56:00.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 44, "user_id": 2, "shop_id": 7, "product_id": 931, "question": "Do you have the soft version?", "answer": "Unfortunately no", "deleted_at": null, "created_at": "2022-03-18T09:17:38.000000Z", "updated_at": "2022-03-18T10:41:20.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 931, "name": "Space Force First Chapter", "slug": "space-force-first-chapter", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": 200, "shop_id": 7, "sale_price": 180, "language": "en", "min_price": 200, "max_price": 200, "sku": "df5as+f5sda+f5s9+f5sda+5f9+sa5fsf+", "quantity": 500, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1628, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/science-fiction-6.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1628/conversions/science-fiction-6-thumbnail.jpg"}, "video": [], "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2023-10-25T07:56:00.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 43, "user_id": 2, "shop_id": 7, "product_id": 932, "question": "Do you have German version?", "answer": "Unfortunately no", "deleted_at": null, "created_at": "2022-03-18T09:16:59.000000Z", "updated_at": "2022-03-18T10:41:16.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 932, "name": "Space Force Second Chapter", "slug": "space-force-first-chapter-2", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": null, "shop_id": 7, "sale_price": null, "language": "en", "min_price": 150, "max_price": 152, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1629, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/science-fiction-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/conversions/science-fiction-7-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2023-10-25T03:30:21.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 42, "user_id": 2, "shop_id": 7, "product_id": 932, "question": "Do you have Hindi Version as well?", "answer": "Unfortunately no", "deleted_at": null, "created_at": "2022-03-18T09:16:24.000000Z", "updated_at": "2022-03-18T10:41:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 932, "name": "Space Force Second Chapter", "slug": "space-force-first-chapter-2", "description": "Science fabrication is a kidney of academic fabrication that generally deals with imaginative and futuristic generalities similar as advanced wisdom and technology, space disquisition, time trip, resemblant worlds, and extraterrestrial life.", "type_id": 8, "price": null, "shop_id": 7, "sale_price": null, "language": "en", "min_price": 150, "max_price": 152, "sku": null, "quantity": 1000, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "variable", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1629, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/science-fiction-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1629/conversions/science-fiction-7-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:35:14.000000Z", "updated_at": "2023-10-25T03:30:21.000000Z", "author_id": 10, "manufacturer_id": 2, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 1, "rating_count": [{"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 41, "user_id": 2, "shop_id": 7, "product_id": 924, "question": "What is the version ?", "answer": "Its the 3rd version", "deleted_at": null, "created_at": "2022-03-18T09:15:47.000000Z", "updated_at": "2022-03-18T10:41:02.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 924, "name": "<PERSON><PERSON><PERSON>", "slug": "greddy-love", "description": "The runes of Lyrical Ditties designedlyre-imagined the way poetry should sound\"By fitting to rhythmic arrangement a selection of the real language of men,\"<PERSON><PERSON> and his English coevals, similar as <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, wrote poetry that was meant to boil up from serious, reflective reflection over the commerce of humans with their terrain. Although numerous stress the notion of naturalness in Romantic poetry, the movement was still greatly concerned with the difficulty of composition and of rephrasing these feelings into lyrical form. Indeed, <PERSON><PERSON>, in his essay On Poesy or Art, sees art as “ the mediatress between, and jurist of nature and man”. Such an station reflects what might be called the dominant theme of English Romantic poetry the filtering of natural emotion through the mortal mind in order to produce meaning.", "type_id": 8, "price": 180, "shop_id": 7, "sale_price": 150, "language": "en", "min_price": 180, "max_price": 180, "sku": "4g6g4d54fd6g54gd+++", "quantity": 500, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1636, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/Romantic-Books-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/conversions/Romantic-Books-7-thumbnail.jpg"}, "video": [], "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:10:16.000000Z", "updated_at": "2023-10-25T08:10:03.000000Z", "author_id": 11, "manufacturer_id": 4, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 40, "user_id": 2, "shop_id": 7, "product_id": 924, "question": "Can I get a soft copy?", "answer": "Yes, it is possible. After purchase please send us your email address and we will mail you the soft copy.", "deleted_at": null, "created_at": "2022-03-18T09:15:26.000000Z", "updated_at": "2022-03-18T10:40:53.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 924, "name": "<PERSON><PERSON><PERSON>", "slug": "greddy-love", "description": "The runes of Lyrical Ditties designedlyre-imagined the way poetry should sound\"By fitting to rhythmic arrangement a selection of the real language of men,\"<PERSON><PERSON> and his English coevals, similar as <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, wrote poetry that was meant to boil up from serious, reflective reflection over the commerce of humans with their terrain. Although numerous stress the notion of naturalness in Romantic poetry, the movement was still greatly concerned with the difficulty of composition and of rephrasing these feelings into lyrical form. Indeed, <PERSON><PERSON>, in his essay On Poesy or Art, sees art as “ the mediatress between, and jurist of nature and man”. Such an station reflects what might be called the dominant theme of English Romantic poetry the filtering of natural emotion through the mortal mind in order to produce meaning.", "type_id": 8, "price": 180, "shop_id": 7, "sale_price": 150, "language": "en", "min_price": 180, "max_price": 180, "sku": "4g6g4d54fd6g54gd+++", "quantity": 500, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1 pc", "height": null, "width": null, "length": null, "image": {"id": 1636, "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/Romantic-Books-7.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1636/conversions/Romantic-Books-7-thumbnail.jpg"}, "video": [], "gallery": [], "deleted_at": null, "created_at": "2021-12-12T17:10:16.000000Z", "updated_at": "2023-10-25T08:10:03.000000Z", "author_id": 11, "manufacturer_id": 4, "is_digital": 1, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 39, "user_id": 4, "shop_id": 6, "product_id": 3, "question": "how many will be there in 1lbs?", "answer": "About 80-90 pcs", "deleted_at": null, "created_at": "2022-03-17T16:21:32.000000Z", "updated_at": "2022-03-17T16:22:18.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 38, "user_id": 4, "shop_id": 6, "product_id": 3, "question": "Do you also have local product?", "answer": "Blueberries are imported and not locally produced.", "deleted_at": null, "created_at": "2022-03-17T16:21:18.000000Z", "updated_at": "2022-03-17T16:22:09.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 37, "user_id": 4, "shop_id": 6, "product_id": 3, "question": "How long can I store them in the freezer?", "answer": "at least 2-3 weeks.", "deleted_at": null, "created_at": "2022-03-17T16:20:24.000000Z", "updated_at": "2022-03-17T16:21:52.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 36, "user_id": 4, "shop_id": 6, "product_id": 3, "question": "Hi\nIs this fresh?", "answer": "yes", "deleted_at": null, "created_at": "2022-03-17T16:20:02.000000Z", "updated_at": "2022-03-17T16:21:40.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 35, "user_id": 4, "shop_id": 6, "product_id": 2, "question": "Can you arrange 1-hour delivery?", "answer": "it will take more than one hour for delivery", "deleted_at": null, "created_at": "2022-03-17T16:14:27.000000Z", "updated_at": "2022-03-17T16:19:06.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 34, "user_id": 4, "shop_id": 6, "product_id": 1, "question": "Will it be good for apple puree?", "answer": "Yes", "deleted_at": null, "created_at": "2022-03-17T16:12:05.000000Z", "updated_at": "2022-03-17T16:12:12.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 33, "user_id": 4, "shop_id": 6, "product_id": 1, "question": "Can you give me discount if I purchase 5lbs?", "answer": "Unfortunately, we can not offer any further discount.", "deleted_at": null, "created_at": "2022-03-17T16:06:23.000000Z", "updated_at": "2022-03-17T16:07:14.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 32, "user_id": 4, "shop_id": 6, "product_id": 1, "question": "Is this local produce?", "answer": "yes these are locally produced", "deleted_at": null, "created_at": "2022-03-17T16:05:48.000000Z", "updated_at": "2022-03-17T16:06:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 31, "user_id": 4, "shop_id": 6, "product_id": 1, "question": "do you have green apples as well?", "answer": "Unfortunately no.", "deleted_at": null, "created_at": "2022-03-17T16:05:24.000000Z", "updated_at": "2022-03-17T16:06:39.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 30, "user_id": 4, "shop_id": 6, "product_id": 4, "question": "How many sprouts will be in 1lbs?", "answer": "about 20-25 pcs", "deleted_at": null, "created_at": "2022-03-17T16:02:58.000000Z", "updated_at": "2022-03-17T16:04:37.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 29, "user_id": 4, "shop_id": 6, "product_id": 4, "question": "Is there an option to buy 0.5 lbs?", "answer": "unfortunately no.", "deleted_at": null, "created_at": "2022-03-17T16:02:44.000000Z", "updated_at": "2022-03-17T16:04:29.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 28, "user_id": 4, "shop_id": 6, "product_id": 4, "question": "Is it fresh?", "answer": "yes these are fresh products.", "deleted_at": null, "created_at": "2022-03-17T16:02:24.000000Z", "updated_at": "2022-03-17T16:04:17.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 27, "user_id": 4, "shop_id": 6, "product_id": 2, "question": "Hi\nis it frozen?", "answer": "No, its fresh", "deleted_at": null, "created_at": "2022-03-17T15:19:21.000000Z", "updated_at": "2022-03-17T15:19:30.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 26, "user_id": 4, "shop_id": 6, "product_id": 1, "question": "How many apples will be there on 1 lbs", "answer": "3-4 pcs approximately", "deleted_at": null, "created_at": "2022-03-17T14:32:00.000000Z", "updated_at": "2022-03-17T15:18:44.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 4, "name": "customer2", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2022-03-17T14:15:08.000000Z", "updated_at": "2022-03-17T14:15:08.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 25, "user_id": 2, "shop_id": 6, "product_id": 20, "question": "Hi\nWhat will be the approximate weight of 4pcs?", "answer": "approximately 1 lbs", "deleted_at": null, "created_at": "2022-03-17T13:39:11.000000Z", "updated_at": "2022-03-17T13:40:33.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 20, "name": "Lemon", "slug": "lemon", "description": "The lemon/lime, Citrus limon <PERSON>beck, is a species of small evergreen tree in the flowering plant family Rutaceae, native to South Asia, primarily North eastern India.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "20", "quantity": 60, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "4pc(s)", "height": null, "width": null, "length": null, "image": {"id": "21", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/21/Yellow-Limes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/21/conversions/Yellow-Limes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T11:01:10.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 1, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 24, "user_id": 2, "shop_id": 6, "product_id": 19, "question": "Hi\nWhat vegetables are in here?", "answer": "green cabbage, lettuce, spinach and others.", "deleted_at": null, "created_at": "2022-03-17T13:38:42.000000Z", "updated_at": "2022-03-17T13:40:56.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 19, "name": "Mix Vegetable Platter", "slug": "mix-vegetable-platter", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.2, "language": "en", "min_price": 4, "max_price": 4, "sku": "19", "quantity": 100, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "20", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/VeggiePlatter.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/20/conversions/VeggiePlatter-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T11:00:12.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 23, "user_id": 2, "shop_id": 6, "product_id": 18, "question": "Hi\nFrom where it is imported?", "answer": "These are locally grown. We imported the seeds and grown them locally.", "deleted_at": null, "created_at": "2022-03-17T13:38:17.000000Z", "updated_at": "2022-03-17T13:41:17.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 18, "name": "<PERSON><PERSON>berry", "slug": "strawberry", "description": "The garden strawberry is a widely grown hybrid species of the genus Fragaria, collectively known as the strawberries, which are cultivated worldwide for their fruit. The fruit is widely appreciated for its characteristic aroma, bright red color, juicy texture, and sweetness.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "17", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "19", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/strawberry.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/19/conversions/strawberry-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:54.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1, "total_reviews": 2, "rating_count": [{"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 22, "user_id": 2, "shop_id": 6, "product_id": 17, "question": "Hi\nAre these freshly packed?", "answer": "yes these are freshly packed.", "deleted_at": null, "created_at": "2022-03-17T13:37:53.000000Z", "updated_at": "2022-03-17T13:41:28.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 17, "name": "Cherry", "slug": "cherry", "description": "A cherry is the fruit of many plants of the genus Prunus, and is a fleshy drupe. Commercial cherries are obtained from cultivars of several species, such as the sweet Prunus avium and the sour Prunus cerasus", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.8, "language": "en", "min_price": 2, "max_price": 2, "sku": "16", "quantity": 15, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "18", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/RedCherries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/18/conversions/RedCherries-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:58:02.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 21, "user_id": 2, "shop_id": 6, "product_id": 16, "question": "Hi\nIs this Frozen or Freshly chopped?", "answer": "Hi\nit is freshly chopped.", "deleted_at": null, "created_at": "2022-03-17T13:34:04.000000Z", "updated_at": "2022-03-17T13:34:18.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 16, "name": "Peeled <PERSON>", "slug": "peeled-baby-carrot", "description": "The carrot is a root vegetable, usually orange in colour, though purple, black, red, white, and yellow cultivars exist. They are a domesticated form of the wild carrot, Daucus carota, native to Europe and Southwestern Asia. The plant probably originated in Persia and was originally cultivated for its leaves and seeds.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": 2.2, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "16", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "17", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/Peeled-Carrots.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/conversions/Peeled-Carrots-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:56:44.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 20, "user_id": 2, "shop_id": 6, "product_id": 16, "question": "Hi\nis this chopped as like the picture?", "answer": "Hi\nYes. it is chopped and cleaned", "deleted_at": null, "created_at": "2022-03-17T13:33:07.000000Z", "updated_at": "2022-03-17T13:33:38.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 16, "name": "Peeled <PERSON>", "slug": "peeled-baby-carrot", "description": "The carrot is a root vegetable, usually orange in colour, though purple, black, red, white, and yellow cultivars exist. They are a domesticated form of the wild carrot, Daucus carota, native to Europe and Southwestern Asia. The plant probably originated in Persia and was originally cultivated for its leaves and seeds.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": 2.2, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "16", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "17", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/Peeled-Carrots.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/17/conversions/Peeled-Carrots-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:56:44.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 19, "user_id": 2, "shop_id": 6, "product_id": 15, "question": "Hi\nIs this local or imported?", "answer": "These are imported", "deleted_at": null, "created_at": "2022-03-17T13:32:44.000000Z", "updated_at": "2022-03-17T13:34:42.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 15, "name": "<PERSON><PERSON>s", "slug": "pears", "description": "The pear tree and shrub are a species of genus Pyrus, in the family Rosaceae, bearing the pomaceous fruit of the same name. Several species of pear are valued for their edible fruit and juices while others are cultivated as trees.", "type_id": 1, "price": 4, "shop_id": 6, "sale_price": 3.5, "language": "en", "min_price": 4, "max_price": 4, "sku": "15", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "16", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/pears.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/16/conversions/pears-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:58.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 2.5, "total_reviews": 2, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 18, "user_id": 2, "shop_id": 6, "product_id": 14, "question": "Hi\nWill I get mixed color pepers or only one singe color", "answer": "You will get mixed color peppers", "deleted_at": null, "created_at": "2022-03-17T13:27:58.000000Z", "updated_at": "2022-03-17T13:34:56.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 14, "name": "Pepper", "slug": "pepper", "description": "Black pepper is a flowering vine in the family Piperaceae, cultivated for its fruit, known as a peppercorn, which is usually dried and used as a spice and seasoning. When fresh and fully mature, it is about 5 mm in diameter and dark red, and contains a single seed, like all drupes", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "14", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "15", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/MiniPeppers.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/conversions/MiniPeppers-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:14.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 17, "user_id": 2, "shop_id": 6, "product_id": 14, "question": "Hi\nFrom where it is imported?", "answer": "Hi\nthere are quite a few countries from where we import these high-quality mixed peppers. Brasil, Mexico and Chile are some of them", "deleted_at": null, "created_at": "2022-03-17T13:27:38.000000Z", "updated_at": "2022-03-17T13:36:00.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 14, "name": "Pepper", "slug": "pepper", "description": "Black pepper is a flowering vine in the family Piperaceae, cultivated for its fruit, known as a peppercorn, which is usually dried and used as a spice and seasoning. When fresh and fully mature, it is about 5 mm in diameter and dark red, and contains a single seed, like all drupes", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "14", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "15", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/MiniPeppers.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/15/conversions/MiniPeppers-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:55:14.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 16, "user_id": 2, "shop_id": 6, "product_id": 13, "question": "Hi\nIs it the sweet lot or sour lot?", "answer": "Sweet lot.", "deleted_at": null, "created_at": "2022-03-17T13:27:05.000000Z", "updated_at": "2022-03-17T13:36:13.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 13, "name": "Mango", "slug": "mango", "description": "A mango is a juicy stone fruit produced from numerous species of tropical trees belonging to the flowering plant genus Mangifera, cultivated mostly for their edible fruit. Most of these species are found in nature as wild mangoes. The genus belongs to the cashew family Anacardiaceae.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "13", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "14", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/Mangoes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/conversions/Mangoes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:54:24.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 15, "user_id": 2, "shop_id": 6, "product_id": 13, "question": "Hi\nWhat is the breed of these mangoes?", "answer": "These are Harivanga Mango<PERSON>", "deleted_at": null, "created_at": "2022-03-17T13:25:35.000000Z", "updated_at": "2022-03-17T13:36:41.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 13, "name": "Mango", "slug": "mango", "description": "A mango is a juicy stone fruit produced from numerous species of tropical trees belonging to the flowering plant genus Mangifera, cultivated mostly for their edible fruit. Most of these species are found in nature as wild mangoes. The genus belongs to the cashew family Anacardiaceae.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "13", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "14", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/Mangoes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/14/conversions/Mangoes-thumbnail.jpg"}, "video": null, "gallery": [], "deleted_at": null, "created_at": "2021-03-08T10:54:24.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 14, "user_id": 2, "shop_id": 6, "product_id": 12, "question": "Hi\nWhat type of limes are these?", "answer": "These are Fat limes.", "deleted_at": null, "created_at": "2022-03-17T13:25:03.000000Z", "updated_at": "2022-03-17T13:36:52.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 12, "name": "Lime", "slug": "lime", "description": "The lemon/lime, Citrus limon <PERSON>beck, is a species of small evergreen tree in the flowering plant family Rutaceae, native to South Asia, primarily North eastern India.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.5, "language": "en", "min_price": 2, "max_price": 2, "sku": "12", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "4pc(s)", "height": null, "width": null, "length": null, "image": {"id": "13", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/GreenLimes.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/13/conversions/GreenLimes-thumbnail.jpg"}, "video": null, "gallery": [{"id": "570", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/lime-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/569/conversions/lime-1-thumbnail.jpg"}, {"id": "572", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/lime.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/571/conversions/lime-thumbnail.jpg"}, {"id": "754", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/lime-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/753/conversions/lime-2-thumbnail.jpg"}, {"id": "755", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/GreenLimes_jrodle.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/754/conversions/GreenLimes_jrodle-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:53:23.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 13, "user_id": 2, "shop_id": 6, "product_id": 11, "question": "Hi\nAre these cleaned and processed?", "answer": "Hi\nyes, these are cleaned and processed.", "deleted_at": null, "created_at": "2022-03-17T13:24:18.000000Z", "updated_at": "2022-03-17T13:39:50.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 11, "name": "Green Beans", "slug": "green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "11", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "12", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/GreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/12/conversions/GreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "748", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/747/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}, {"id": "749", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/748/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "750", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/749/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "751", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/750/conversions/French-Green-Beans-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:52:16.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.5, "total_reviews": 2, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 12, "user_id": 2, "shop_id": 6, "product_id": 10, "question": "Hi\nAre this processed?", "answer": "Hi\nyes these are processed?", "deleted_at": null, "created_at": "2022-03-17T13:23:50.000000Z", "updated_at": "2022-03-17T13:40:05.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 10, "name": "French Green Beans", "slug": "french-green-beans", "description": "Green beans are the unripe, young fruit and protective pods of various cultivars of the common bean. Immature or young pods of the runner bean, yardlong bean, and hyacinth bean are used in a similar way.", "type_id": 1, "price": 1.5, "shop_id": 6, "sale_price": 1.2, "language": "en", "min_price": 1.5, "max_price": 1.5, "sku": "10", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "0.5lb", "height": null, "width": null, "length": null, "image": {"id": "11", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/FrenchGreenBeans.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/11/conversions/FrenchGreenBeans-thumbnail.jpg"}, "video": null, "gallery": [{"id": "602", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/French-Green-Beans-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/601/conversions/French-Green-Beans-1-thumbnail.jpg"}, {"id": "603", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/French-Green-Beans-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/602/conversions/French-Green-Beans-2-thumbnail.jpg"}, {"id": "604", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/French-Green-Beans.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/603/conversions/French-Green-Beans-thumbnail.jpg"}, {"id": "747", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/FrenchGreenBeans_azivow.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/746/conversions/FrenchGreenBeans_azivow-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:51:28.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 11, "user_id": 2, "shop_id": 6, "product_id": 9, "question": "Hi\nHow long is the expiry of these dates?", "answer": "approximately one year", "deleted_at": null, "created_at": "2022-03-17T13:23:24.000000Z", "updated_at": "2022-03-17T13:40:19.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 9, "name": "Dates", "slug": "dates", "description": "Phoenix dactylifera, commonly known as date or date palm, is a flowering plant species in the palm family, Arecaceae, cultivated for its edible sweet fruit.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "9", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1.5lb", "height": null, "width": null, "length": null, "image": {"id": "10", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/Dates.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/conversions/Dates-thumbnail.jpg"}, "video": null, "gallery": [{"id": "599", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/Dates-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/conversions/Dates-1-thumbnail.jpg"}, {"id": "600", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/Dates-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/conversions/Dates-2-thumbnail.jpg"}, {"id": "601", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/Dates.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/conversions/Dates-thumbnail.jpg"}, {"id": "746", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/Dates_pq4oad.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/conversions/Dates_pq4oad-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:50:41.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 10, "user_id": 2, "shop_id": 6, "product_id": 9, "question": "Hi\nwhich brand of dates are these?", "answer": "These are Ajwah dates", "deleted_at": null, "created_at": "2022-03-17T13:23:03.000000Z", "updated_at": "2022-03-17T13:41:49.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 9, "name": "Dates", "slug": "dates", "description": "Phoenix dactylifera, commonly known as date or date palm, is a flowering plant species in the palm family, Arecaceae, cultivated for its edible sweet fruit.", "type_id": 1, "price": 10, "shop_id": 6, "sale_price": 8, "language": "en", "min_price": 10, "max_price": 10, "sku": "9", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1.5lb", "height": null, "width": null, "length": null, "image": {"id": "10", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/Dates.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/10/conversions/Dates-thumbnail.jpg"}, "video": null, "gallery": [{"id": "599", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/Dates-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/598/conversions/Dates-1-thumbnail.jpg"}, {"id": "600", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/Dates-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/599/conversions/Dates-2-thumbnail.jpg"}, {"id": "601", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/Dates.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/600/conversions/Dates-thumbnail.jpg"}, {"id": "746", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/Dates_pq4oad.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/745/conversions/Dates_pq4oad-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:50:41.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 9, "user_id": 2, "shop_id": 6, "product_id": 8, "question": "Hi\nIs this local or imported ?", "answer": "These are local.", "deleted_at": null, "created_at": "2022-03-17T13:21:18.000000Z", "updated_at": "2022-03-17T13:41:59.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 8, "name": "<PERSON><PERSON><PERSON>ber", "slug": "cucumber", "description": "Cucumber is a widely cultivated plant in the gourd family, Cucurbitaceae. It is a creeping vine that bears cucumiform fruits that are used as vegetables. There are three main varieties of cucumber: slicing, pickling, and seedless. Within these varieties, several cultivars have been created.", "type_id": 1, "price": 2.5, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 2.5, "max_price": 2.5, "sku": "8", "quantity": 25, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2.5lb", "height": null, "width": null, "length": null, "image": {"id": "8", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/Cucumber.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/8/conversions/Cucumber-thumbnail.jpg"}, "video": null, "gallery": [{"id": "596", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/Cucumber-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/595/conversions/Cucumber-1-thumbnail.jpg"}, {"id": "597", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/Cucumber-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/596/conversions/Cucumber-2-thumbnail.jpg"}, {"id": "598", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/Cucumber.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/597/conversions/Cucumber-thumbnail.jpg"}, {"id": "745", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/Cucumber_w6hlxr.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/744/conversions/Cucumber_w6hlxr-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:49:18.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 8, "user_id": 2, "shop_id": 6, "product_id": 7, "question": "Hi\nCan this be stored in normal temperature?", "answer": "yes you can store them in normal temperature for about a week.", "deleted_at": null, "created_at": "2022-03-17T13:20:52.000000Z", "updated_at": "2022-03-17T13:42:20.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 7, "name": "Sweet Corn", "slug": "sweet-corn", "description": "Maize, also known as corn, is a cereal grain first domesticated by indigenous peoples in southern Mexico about 10,000 years ago. The leafy stalk of the plant produces pollen inflorescences and separate ovuliferous inflorescences called ears that yield kernels or seeds, which are fruits.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 4, "language": "en", "min_price": 5, "max_price": 5, "sku": "7", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "7", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/Corn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/7/conversions/Corn-thumbnail.jpg"}, "video": null, "gallery": [{"id": "593", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/Sweet-Corn-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/592/conversions/Sweet-Corn-1-thumbnail.jpg"}, {"id": "594", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/Sweet-Corn-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/593/conversions/Sweet-Corn-2-thumbnail.jpg"}, {"id": "595", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/Sweet-Corn.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/594/conversions/Sweet-Corn-thumbnail.jpg"}, {"id": "744", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/Corn_dlrtbv.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/743/conversions/Corn_dlrtbv-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:48:20.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 7, "user_id": 2, "shop_id": 6, "product_id": 6, "question": "Hi\nIs this the sweet lot or sour lot?", "answer": "sweet lot", "deleted_at": null, "created_at": "2022-03-17T13:16:58.000000Z", "updated_at": "2022-03-17T13:42:29.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 6, "name": "Clementines", "slug": "clementines", "description": "clementine is a tangor, a citrus fruit hybrid between a willowleaf mandarin orange and a sweet orange, named for its late 19th-century discoverer. The exterior is a deep orange colour with a smooth, glossy appearance.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": 2.5, "language": "en", "min_price": 3, "max_price": 3, "sku": "6", "quantity": 50, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "6", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/clementines.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/6/conversions/clementines-thumbnail.jpg"}, "video": null, "gallery": [{"id": "590", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/Clementines-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/589/conversions/Clementines-1-thumbnail.jpg"}, {"id": "591", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/Clementines.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/590/conversions/Clementines-thumbnail.jpg"}, {"id": "592", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/Clementines-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/591/conversions/Clementines-2-thumbnail.jpg"}, {"id": "743", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/clementines_h74qrp.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/742/conversions/clementines_h74qrp-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:45:18.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 2, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 2, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 6, "user_id": 2, "shop_id": 6, "product_id": 5, "question": "Hi\nIs this fresh or frozen?", "answer": "these are frozen ones.", "deleted_at": null, "created_at": "2022-03-17T13:16:21.000000Z", "updated_at": "2022-03-17T13:42:38.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 5, "name": "Celery Stick", "slug": "celery-stick", "description": "celery stick - celery stalks cut into small sticks. crudites - raw vegetables cut into bite-sized strips and served with a dip. celery - stalks eaten raw or cooked or used as seasoning.", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "5", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "5", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/CelerySticks.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/conversions/CelerySticks-thumbnail.jpg"}, "video": null, "gallery": [{"id": "585", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/Celery-Stick-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/conversions/Celery-Stick-1-thumbnail.jpg"}, {"id": "586", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/Celery-Stick-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/conversions/Celery-Stick-2-thumbnail.jpg"}, {"id": "587", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/Celery-Stick.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/conversions/Celery-Stick-thumbnail.jpg"}, {"id": "742", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/CelerySticks_ulljfz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/conversions/CelerySticks_ulljfz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:44:09.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1.67, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 5, "user_id": 2, "shop_id": 6, "product_id": 5, "question": "Hi\nis this sliced?", "answer": "yes these are sliced.", "deleted_at": null, "created_at": "2022-03-17T13:16:09.000000Z", "updated_at": "2022-03-17T13:42:46.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 5, "name": "Celery Stick", "slug": "celery-stick", "description": "celery stick - celery stalks cut into small sticks. crudites - raw vegetables cut into bite-sized strips and served with a dip. celery - stalks eaten raw or cooked or used as seasoning.", "type_id": 1, "price": 6, "shop_id": 6, "sale_price": 5, "language": "en", "min_price": 6, "max_price": 6, "sku": "5", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "5", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/CelerySticks.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/5/conversions/CelerySticks-thumbnail.jpg"}, "video": null, "gallery": [{"id": "585", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/Celery-Stick-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/584/conversions/Celery-Stick-1-thumbnail.jpg"}, {"id": "586", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/Celery-Stick-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/585/conversions/Celery-Stick-2-thumbnail.jpg"}, {"id": "587", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/Celery-Stick.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/586/conversions/Celery-Stick-thumbnail.jpg"}, {"id": "742", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/CelerySticks_ulljfz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/741/conversions/CelerySticks_ulljfz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:44:09.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 1.67, "total_reviews": 3, "rating_count": [{"rating": 3, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 1, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 4, "user_id": 2, "shop_id": 6, "product_id": 4, "question": "Hi\nFrom where this is imported?", "answer": "these are imported from Latin America", "deleted_at": null, "created_at": "2022-03-17T13:15:50.000000Z", "updated_at": "2022-03-17T13:51:35.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 4, "name": "Brussels Sprout", "slug": "brussels-sprout", "description": "The Brussels sprout is a member of the Gemmifera Group of cabbages, grown for its edible buds. The leaf vegetables are typically 1.5–4.0 cm in diameter and look like miniature cabbages. The Brussels sprout has long been popular in Brussels, Belgium, and may have gained its name there.", "type_id": 1, "price": 5, "shop_id": 6, "sale_price": 3, "language": "en", "min_price": 5, "max_price": 5, "sku": "4", "quantity": 17, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "4", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/BrusselsSprouts.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/4/conversions/BrusselsSprouts-thumbnail.jpg"}, "video": null, "gallery": [{"id": "582", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/Brussels-Sprout-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/581/conversions/Brussels-Sprout-1-thumbnail.jpg"}, {"id": "583", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/Brussels-Sprout-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/582/conversions/Brussels-Sprout-2-thumbnail.jpg"}, {"id": "584", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/Brussels-Sprout.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/583/conversions/Brussels-Sprout-thumbnail.jpg"}, {"id": "741", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/BrusselsSprouts_adwhet.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/740/conversions/BrusselsSprouts_adwhet-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:42:32.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 5, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 3, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 3, "user_id": 2, "shop_id": 6, "product_id": 3, "question": "Hi\nFrom which country it is imported?", "answer": "its imported from Europe.", "deleted_at": null, "created_at": "2022-03-17T13:15:11.000000Z", "updated_at": "2022-03-17T13:42:58.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 3, "name": "Blueberries", "slug": "blueberries", "description": "Blueberries are perennial flowering plants with blue or purple berries. They are classified in the section Cyanococcus within the genus Vaccinium. Vaccinium also includes cranberries, bilberries, huckleberries and Madeira blueberries. Commercial blueberries—both wild and cultivated —are all native to North America.", "type_id": 1, "price": 3, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 3, "max_price": 3, "sku": "3", "quantity": 30, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "3", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/blueberries.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/3/conversions/blueberries-thumbnail.jpg"}, "video": null, "gallery": [{"id": "580", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/Bluberries-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/579/conversions/Bluberries-2-thumbnail.jpg"}, {"id": "581", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/Bluberries.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/580/conversions/Bluberries-thumbnail.jpg"}, {"id": "740", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/blueberries_relyfn.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/739/conversions/blueberries_relyfn-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:40:00.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 2, "user_id": 2, "shop_id": 6, "product_id": 1, "question": "Hi\nHow long I can store this product?", "answer": "in freezer you can store theme for about 2 weeks in minimum", "deleted_at": null, "created_at": "2022-03-17T13:14:10.000000Z", "updated_at": "2022-03-17T13:43:17.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 1, "name": "Apples", "slug": "apples", "description": "An apple is a sweet, edible fruit produced by an apple tree (Malus domestica). Apple trees are ... The skin of ripe apples is generally red, yellow, green, pink, or russetted, though many bi- or tri-colored cultivars may be found.", "type_id": 1, "price": 2, "shop_id": 6, "sale_price": 1.6, "language": "en", "min_price": 2, "max_price": 2, "sku": "1", "quantity": 18, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 0, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "1lb", "height": null, "width": null, "length": null, "image": {"id": "1", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/Apples.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/1/conversions/Apples-thumbnail.jpg"}, "video": null, "gallery": [{"id": "573", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/apple-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/572/conversions/apple-1-thumbnail.jpg"}, {"id": "574", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/573/conversions/apple-2-thumbnail.jpg"}, {"id": "575", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/apple.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/574/conversions/apple-thumbnail.jpg"}, {"id": "737", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/apple-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/736/conversions/apple-2-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:24:53.000000Z", "updated_at": "2023-10-06T06:19:56.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 4.67, "total_reviews": 3, "rating_count": [{"rating": 5, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}, {"id": 1, "user_id": 2, "shop_id": 6, "product_id": 2, "question": "Hi \nIs this a Fresh product?", "answer": "yes these are fresh products.", "deleted_at": null, "created_at": "2022-03-17T13:13:14.000000Z", "updated_at": "2022-03-17T13:43:33.000000Z", "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0, "product": {"id": 2, "name": "<PERSON>", "slug": "baby-spinach", "description": "Spinach (Spinacia oleracea) is a leafy green flowering plant native to central and western Asia. It is of the order Caryophyllales, family Amaranthaceae, subfamily Chenopodioideae. Its leaves are a common edible vegetable consumed either fresh.", "type_id": 1, "price": 0.6, "shop_id": 6, "sale_price": null, "language": "en", "min_price": 0.6, "max_price": 0.6, "sku": "2", "quantity": 10, "sold_quantity": 0, "in_stock": 1, "is_taxable": 0, "in_flash_sale": 1, "shipping_class_id": null, "status": "publish", "product_type": "simple", "unit": "2lb", "height": null, "width": null, "length": null, "image": {"id": "2", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/BabySpinach.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/2/conversions/BabySpinach-thumbnail.jpg"}, "video": null, "gallery": [{"id": "576", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/baby-spinach-1.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/575/conversions/baby-spinach-1-thumbnail.jpg"}, {"id": "577", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/baby-spinach-2.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/576/conversions/baby-spinach-2-thumbnail.jpg"}, {"id": "578", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/baby-spinach.png", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/577/conversions/baby-spinach-thumbnail.jpg"}, {"id": "738", "original": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/BabySpinach_xronqz.jpg", "thumbnail": "https://pickbazarlaravel.s3.ap-southeast-1.amazonaws.com/737/conversions/BabySpinach_xronqz-thumbnail.jpg"}], "deleted_at": null, "created_at": "2021-03-08T10:26:13.000000Z", "updated_at": "2023-10-16T06:54:29.000000Z", "author_id": null, "manufacturer_id": null, "is_digital": 0, "is_external": 0, "external_product_url": null, "external_product_button_text": null, "blocked_dates": [], "ratings": 3.33, "total_reviews": 3, "rating_count": [{"rating": 4, "total": 1, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}, {"rating": 3, "total": 2, "positive_feedbacks_count": 0, "negative_feedbacks_count": 0, "my_feedback": null, "abusive_reports_count": 0}], "my_review": null, "in_wishlist": false, "translated_languages": ["en"]}, "user": {"id": 2, "name": "Customer", "email": "<EMAIL>", "email_verified_at": null, "created_at": "2021-08-18T10:30:29.000000Z", "updated_at": "2021-08-18T13:17:53.000000Z", "is_active": 1, "shop_id": null, "email_verified": false}}]