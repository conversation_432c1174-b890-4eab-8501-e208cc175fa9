"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[426],{40426:function(e,t,o){o.r(t);var n=o(85893),u=o(71421),s=o(59272),r=o(75814),a=o(92717),c=o(5233);t.default=()=>{let{t:e}=(0,c.$G)(),{mutate:t,isLoading:o}=(0,a.l9)(),{data:l}=(0,r.X9)(),{closeModal:i}=(0,r.SO)();async function handleDelete(){t({id:l},{onSettled:()=>{i()}})}return(0,n.jsx)(u.Z,{onCancel:i,onDelete:handleDelete,deleteBtnLoading:o,deleteBtnText:"text-disapprove",icon:(0,n.jsx)(s.c,{className:"w-10 h-10 m-auto mt-4 text-accent"}),deleteBtnClassName:"!bg-accent focus:outline-none hover:!bg-accent-hover focus:!bg-accent-hover",cancelBtnClassName:"!bg-red-600 focus:outline-none hover:!bg-red-700 focus:!bg-red-700",title:"text-disapprove-coupon",description:"text-want-disapprove-coupon"})}},59272:function(e,t,o){o.d(t,{O:function(){return CheckMarkGhost},c:function(){return CheckMarkCircle}});var n=o(85893);o(67294);let CheckMarkCircle=e=>{let{...t}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 330 330",fill:"currentColor",...t,children:[(0,n.jsx)("path",{d:"M165 0C74.019 0 0 74.019 0 165s74.019 165 165 165 165-74.019 165-165S255.981 0 165 0zm0 300c-74.44 0-135-60.561-135-135S90.56 30 165 30s135 60.561 135 135-60.561 135-135 135z"}),(0,n.jsx)("path",{d:"M226.872 106.664l-84.854 84.853-38.89-38.891c-5.857-5.857-15.355-5.858-21.213-.001-5.858 5.858-5.858 15.355 0 21.213l49.496 49.498a15 15 0 0010.606 4.394h.001c3.978 0 7.793-1.581 10.606-4.393l95.461-95.459c5.858-5.858 5.858-15.355 0-21.213-5.858-5.858-15.355-5.859-21.213-.001z"})]})},CheckMarkGhost=e=>{let{...t}=e;return(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,n.jsx)("path",{opacity:.2,d:"M17.5 10a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M13.567 7.683a.626.626 0 010 .884l-4.375 4.375a.626.626 0 01-.884 0l-1.875-1.875a.625.625 0 11.884-.884l1.433 1.433 3.933-3.933a.625.625 0 01.884 0zM18.125 10A8.125 8.125 0 1110 1.875 8.133 8.133 0 0118.125 10zm-1.25 0A6.875 6.875 0 1010 16.875 6.883 6.883 0 0016.875 10z",fill:"currentColor"})]})}},92717:function(e,t,o){o.d(t,{OR:function(){return useApproveCouponMutation},Bo:function(){return useCouponQuery},ID:function(){return useCouponsQuery},wr:function(){return useCreateCouponMutation},kN:function(){return useDeleteCouponMutation},l9:function(){return useDisApproveCouponMutation},w3:function(){return useUpdateCouponMutation},Mu:function(){return useVerifyCouponMutation}});var n=o(11163),u=o.n(n),s=o(88767),r=o(22920),a=o(5233),c=o(28597),l=o(47869),i=o(55191),p=o(3737);let d={...(0,i.h)(l.P.COUPONS),get(e){let{code:t,language:o}=e;return p.eN.get("".concat(l.P.COUPONS,"/").concat(t),{language:o})},paginated:e=>{let{code:t,...o}=e;return p.eN.get(l.P.COUPONS,{searchJoin:"and",...o,search:p.eN.formatSearchParams({code:t})})},verify:e=>p.eN.post(l.P.VERIFY_COUPONS,e),approve:e=>p.eN.post(l.P.APPROVE_COUPON,e),disapprove:e=>p.eN.post(l.P.DISAPPROVE_COUPON,e)};var C=o(97514),v=o(93345);let useCreateCouponMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,a.$G)(),o=(0,n.useRouter)();return(0,s.useMutation)(d.create,{onSuccess:async()=>{let e=o.query.shop?"/".concat(o.query.shop).concat(C.Z.coupon.list):C.Z.coupon.list;await u().push(e,void 0,{locale:v.Config.defaultLanguage}),r.Am.success(t("common:successfully-created"))},onError:e=>{var o;r.Am.error(t("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))},onSettled:()=>{e.invalidateQueries(l.P.COUPONS)}})},useDeleteCouponMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,a.$G)();return(0,s.useMutation)(d.delete,{onSuccess:()=>{r.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.COUPONS)}})},useUpdateCouponMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,s.useQueryClient)(),o=(0,n.useRouter)();return(0,s.useMutation)(d.update,{onSuccess:async t=>{let n=o.query.shop?"/".concat(o.query.shop).concat(C.Z.coupon.list):C.Z.coupon.list;await o.push(n,void 0,{locale:v.Config.defaultLanguage}),r.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.COUPONS)},onError:t=>{var o;r.Am.error(e("common:".concat(null==t?void 0:null===(o=t.response)||void 0===o?void 0:o.data.message)))}})},useVerifyCouponMutation=()=>(0,s.useMutation)(d.verify),useCouponQuery=e=>{let{code:t,language:o}=e,{data:n,error:u,isLoading:r}=(0,s.useQuery)([l.P.COUPONS,{code:t,language:o}],()=>d.get({code:t,language:o}));return{coupon:n,error:u,loading:r}},useCouponsQuery=e=>{var t;let{data:o,error:n,isLoading:u}=(0,s.useQuery)([l.P.COUPONS,e],e=>{let{queryKey:t,pageParam:o}=e;return d.paginated(Object.assign({},t[1],o))},{keepPreviousData:!0});return{coupons:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,c.Q)(o),error:n,loading:u}},useApproveCouponMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(d.approve,{onSuccess:()=>{r.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.COUPONS)}})},useDisApproveCouponMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(d.disapprove,{onSuccess:()=>{r.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.COUPONS)}})}}}]);