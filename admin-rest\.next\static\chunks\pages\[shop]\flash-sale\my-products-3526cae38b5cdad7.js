(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4485],{59700:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/[shop]/flash-sale/my-products",function(){return r(91062)}])},92072:function(e,t,r){"use strict";var o=r(85893),a=r(93967),n=r.n(a),s=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,o.jsx)("div",{className:(0,s.m6)(n()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},35484:function(e,t,r){"use strict";var o=r(85893),a=r(93967),n=r.n(a),s=r(98388);t.Z=e=>{let{title:t,className:r,...a}=e;return(0,o.jsx)("h2",{className:(0,s.m6)(n()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",r)),...a,children:t})}},37912:function(e,t,r){"use strict";var o=r(85893),a=r(5114),n=r(80287),s=r(93967),u=r.n(s),c=r(67294),l=r(87536),i=r(5233),d=r(98388);t.Z=e=>{let{className:t,onSearch:r,variant:s="outline",shadow:f=!1,inputClassName:h,placeholderText:m,...p}=e,{register:P,handleSubmit:g,watch:S,reset:v,formState:{errors:b}}=(0,l.cI)({defaultValues:{searchText:""}}),_=S("searchText"),{t:y}=(0,i.$G)();(0,c.useEffect)(()=>{_||r({searchText:""})},[_]);let O=u()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===s,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===s,"border border-border-base focus:border-accent":"outline"===s},{"focus:shadow":f},h);return(0,o.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(u()("relative flex w-full items-center",t)),onSubmit:g(r),children:[(0,o.jsx)("label",{htmlFor:"search",className:"sr-only",children:y("form:input-label-search")}),(0,o.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,o.jsx)(n.W,{className:"h-5 w-5"})}),(0,o.jsx)("input",{type:"text",id:"search",...P("searchText"),className:(0,d.m6)(O),placeholder:null!=m?m:y("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...p}),b.searchText&&(0,o.jsx)("p",{children:b.searchText.message}),!!_&&(0,o.jsx)("button",{type:"button",onClick:function(){v(),r({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,o.jsx)(a.T,{className:"h-5 w-5"})})]})}},13202:function(e,t,r){"use strict";r.d(t,{M:function(){return s}});var o=r(47869),a=r(55191),n=r(3737);let s={...(0,a.h)(o.P.PRODUCTS),get(e){let{slug:t,language:r}=e;return n.eN.get("".concat(o.P.PRODUCTS,"/").concat(t),{language:r,with:"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file"})},paginated:e=>{let{type:t,name:r,categories:a,shop_id:s,product_type:u,status:c,...l}=e;return n.eN.get(o.P.PRODUCTS,{searchJoin:"and",with:"shop;type;categories",shop_id:s,...l,search:n.eN.formatSearchParams({type:t,name:r,categories:a,shop_id:s,product_type:u,status:c})})},popular(e){let{shop_id:t,...r}=e;return n.eN.get(o.P.POPULAR_PRODUCTS,{searchJoin:"and",with:"type;shop",...r,search:n.eN.formatSearchParams({shop_id:t})})},lowStock(e){let{shop_id:t,...r}=e;return n.eN.get(o.P.LOW_STOCK_PRODUCTS_ANALYTICS,{searchJoin:"and",with:"type;shop",...r,search:n.eN.formatSearchParams({shop_id:t})})},generateDescription:e=>n.eN.post(o.P.GENERATE_DESCRIPTION,e),newOrInActiveProducts:e=>{let{user_id:t,shop_id:r,status:a,name:s,...u}=e;return n.eN.get(o.P.NEW_OR_INACTIVE_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:a,name:s,...u,search:n.eN.formatSearchParams({status:a,name:s})})},lowOrOutOfStockProducts:e=>{let{user_id:t,shop_id:r,status:a,categories:s,name:u,type:c,...l}=e;return n.eN.get(o.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:a,name:u,...l,search:n.eN.formatSearchParams({status:a,name:u,categories:s,type:c})})},productByCategory(e){let{limit:t,language:r}=e;return n.eN.get(o.P.CATEGORY_WISE_PRODUCTS,{limit:t,language:r})},mostSoldProductByCategory(e){let{shop_id:t,...r}=e;return n.eN.get(o.P.CATEGORY_WISE_PRODUCTS_SALE,{searchJoin:"and",...r,search:n.eN.formatSearchParams({shop_id:t})})},getProductsByFlashSale:e=>{let{user_id:t,shop_id:r,slug:a,name:s,...u}=e;return n.eN.get(o.P.PRODUCTS_BY_FLASH_SALE,{searchJoin:"and",user_id:t,shop_id:r,slug:a,name:s,...u,search:n.eN.formatSearchParams({name:s})})},topRated(e){let{shop_id:t,...r}=e;return n.eN.get(o.P.TOP_RATED_PRODUCTS,{searchJoin:"and",...r,search:n.eN.formatSearchParams({shop_id:t})})}}},93242:function(e,t,r){"use strict";r.d(t,{FA:function(){return useProductQuery},Uc:function(){return useProductsByFlashSaleQuery},YC:function(){return useInActiveProductsQuery},bJ:function(){return useGenerateDescriptionMutation},eH:function(){return useProductStockQuery},kN:function(){return useProductsQuery},qX:function(){return useCreateProductMutation},wE:function(){return useUpdateProductMutation},xq:function(){return useDeleteProductMutation}});var o=r(11163),a=r.n(o),n=r(22920),s=r(5233),u=r(88767),c=r(47869),l=r(13202),i=r(28597),d=r(97514),f=r(93345);let useCreateProductMutation=()=>{let e=(0,u.useQueryClient)(),t=(0,o.useRouter)(),{t:r}=(0,s.$G)();return(0,u.useMutation)(l.M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.product.list):d.Z.product.list;await a().push(e,void 0,{locale:f.Config.defaultLanguage}),n.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{let{data:t,status:o}=null==e?void 0:e.response;if(422===o){let e=Object.values(t).flat();n.Am.error(e[0])}else{var a;n.Am.error(r("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}}})},useUpdateProductMutation=()=>{let{t:e}=(0,s.$G)(),t=(0,u.useQueryClient)(),r=(0,o.useRouter)();return(0,u.useMutation)(l.M.update,{onSuccess:async t=>{let o=r.query.shop?"/".concat(r.query.shop).concat(d.Z.product.list):d.Z.product.list;await r.push("".concat(o,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:f.Config.defaultLanguage}),n.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.PRODUCTS)},onError:t=>{var r;n.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteProductMutation=()=>{let e=(0,u.useQueryClient)(),{t}=(0,s.$G)();return(0,u.useMutation)(l.M.delete,{onSuccess:()=>{n.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{var r;n.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})},useProductQuery=e=>{let{slug:t,language:r}=e,{data:o,error:a,isLoading:n}=(0,u.useQuery)([c.P.PRODUCTS,{slug:t,language:r}],()=>l.M.get({slug:t,language:r}));return{product:o,error:a,isLoading:n}},useProductsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:o,error:a,isLoading:n}=(0,u.useQuery)([c.P.PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{products:null!==(t=null==o?void 0:o.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(o),error:a,loading:n}},useGenerateDescriptionMutation=()=>{let e=(0,u.useQueryClient)(),{t}=(0,s.$G)("common");return(0,u.useMutation)(l.M.generateDescription,{onSuccess:()=>{n.Am.success(t("Generated..."))},onSettled:t=>{e.refetchQueries(c.P.GENERATE_DESCRIPTION)}})},useInActiveProductsQuery=e=>{var t;let{data:r,error:o,isLoading:a}=(0,u.useQuery)([c.P.NEW_OR_INACTIVE_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.newOrInActiveProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:o,loading:a}},useProductStockQuery=e=>{var t;let{data:r,error:o,isLoading:a}=(0,u.useQuery)([c.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.lowOrOutOfStockProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:o,loading:a}},useProductsByFlashSaleQuery=e=>{var t;let{data:r,error:o,isLoading:a}=(0,u.useQuery)([c.P.PRODUCTS_BY_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:r}=e;return l.M.getProductsByFlashSale(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(r),error:o,loading:a}}},91062:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSP:function(){return b},default:function(){return VendorFlashSalePage}});var o=r(85893),a=r(92072),n=r(35484),s=r(37912),u=r(45517),c=r(47133),l=r(45957),i=r(55846),d=r(97514),f=r(93242),h=r(30042),m=r(99930),p=r(10265),P=r(16203),g=r(5233),S=r(11163),v=r(67294),b=!0;function VendorFlashSalePage(){var e,t;let r=(0,S.useRouter)(),{permissions:c}=(0,P.WA)(),{data:b}=(0,m.UE)(),{t:_}=(0,g.$G)(),{query:{shop:y},locale:O}=(0,S.useRouter)(),[N,x]=(0,v.useState)(""),[T,C]=(0,v.useState)(1),[R,D]=(0,v.useState)("created_at"),[w,E]=(0,v.useState)(p.As.Desc),{data:A}=(0,h.DZ)({slug:y}),{id:Q}=null!=A?A:{},{products:U,paginatorInfo:j,loading:M,error:I}=(0,f.kN)({language:O,name:N,limit:20,shop_id:Q,orderBy:R,sortedBy:w,page:T,flash_sale_builder:!0},{enabled:!!Q});return M?(0,o.jsx)(i.Z,{text:_("common:text-loading")}):I?(0,o.jsx)(l.Z,{message:I.message}):((0,P.Ft)(P.M$,c)||(null==b?void 0:null===(e=b.shops)||void 0===e?void 0:e.map(e=>e.id).includes(Q))||(null==b?void 0:null===(t=b.managed_shop)||void 0===t?void 0:t.id)==Q||r.replace(d.Z.dashboard),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(a.Z,{className:"mb-8 flex flex-col items-center xl:flex-row",children:[(0,o.jsx)("div",{className:"mb-4 md:w-1/3 xl:mb-0",children:(0,o.jsx)(n.Z,{title:_("form:form-title-my-products-flash-sales")})}),(0,o.jsx)("div",{className:"flex w-full flex-col items-center space-y-4 ms-auto md:flex-row md:space-y-0 xl:w-2/4",children:(0,o.jsx)(s.Z,{onSearch:function(e){let{searchText:t}=e;x(t)}})})]}),(0,o.jsx)(u.Z,{products:U,paginatorInfo:j,onPagination:function(e){C(e)}})]}))}VendorFlashSalePage.authenticate={permissions:P.ce},VendorFlashSalePage.Layout=c.Z}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,9461,9774,2888,179],function(){return e(e.s=59700)}),_N_E=e.O()}]);