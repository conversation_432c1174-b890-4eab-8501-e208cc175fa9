"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6942],{26942:function(e,u,t){t.r(u);var o=t(85893),n=t(71421),s=t(75814),r=t(92717);u.default=()=>{let{mutate:e,isLoading:u}=(0,r.kN)(),{data:t}=(0,s.X9)(),{closeModal:a}=(0,s.SO)();return(0,o.jsx)(n.Z,{onCancel:a,onDelete:function(){e({id:t}),a()},deleteBtnLoading:u})}},92717:function(e,u,t){t.d(u,{OR:function(){return useApproveCouponMutation},Bo:function(){return useCouponQuery},ID:function(){return useCouponsQuery},wr:function(){return useCreateCouponMutation},kN:function(){return useDeleteCouponMutation},l9:function(){return useDisApproveCouponMutation},w3:function(){return useUpdateCouponMutation},Mu:function(){return useVerifyCouponMutation}});var o=t(11163),n=t.n(o),s=t(88767),r=t(22920),a=t(5233),i=t(28597),c=t(47869),l=t(55191),p=t(3737);let d={...(0,l.h)(c.P.COUPONS),get(e){let{code:u,language:t}=e;return p.eN.get("".concat(c.P.COUPONS,"/").concat(u),{language:t})},paginated:e=>{let{code:u,...t}=e;return p.eN.get(c.P.COUPONS,{searchJoin:"and",...t,search:p.eN.formatSearchParams({code:u})})},verify:e=>p.eN.post(c.P.VERIFY_COUPONS,e),approve:e=>p.eN.post(c.P.APPROVE_COUPON,e),disapprove:e=>p.eN.post(c.P.DISAPPROVE_COUPON,e)};var C=t(97514),v=t(93345);let useCreateCouponMutation=()=>{let e=(0,s.useQueryClient)(),{t:u}=(0,a.$G)(),t=(0,o.useRouter)();return(0,s.useMutation)(d.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(C.Z.coupon.list):C.Z.coupon.list;await n().push(e,void 0,{locale:v.Config.defaultLanguage}),r.Am.success(u("common:successfully-created"))},onError:e=>{var t;r.Am.error(u("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))},onSettled:()=>{e.invalidateQueries(c.P.COUPONS)}})},useDeleteCouponMutation=()=>{let e=(0,s.useQueryClient)(),{t:u}=(0,a.$G)();return(0,s.useMutation)(d.delete,{onSuccess:()=>{r.Am.success(u("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.COUPONS)}})},useUpdateCouponMutation=()=>{let{t:e}=(0,a.$G)(),u=(0,s.useQueryClient)(),t=(0,o.useRouter)();return(0,s.useMutation)(d.update,{onSuccess:async u=>{let o=t.query.shop?"/".concat(t.query.shop).concat(C.Z.coupon.list):C.Z.coupon.list;await t.push(o,void 0,{locale:v.Config.defaultLanguage}),r.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(c.P.COUPONS)},onError:u=>{var t;r.Am.error(e("common:".concat(null==u?void 0:null===(t=u.response)||void 0===t?void 0:t.data.message)))}})},useVerifyCouponMutation=()=>(0,s.useMutation)(d.verify),useCouponQuery=e=>{let{code:u,language:t}=e,{data:o,error:n,isLoading:r}=(0,s.useQuery)([c.P.COUPONS,{code:u,language:t}],()=>d.get({code:u,language:t}));return{coupon:o,error:n,loading:r}},useCouponsQuery=e=>{var u;let{data:t,error:o,isLoading:n}=(0,s.useQuery)([c.P.COUPONS,e],e=>{let{queryKey:u,pageParam:t}=e;return d.paginated(Object.assign({},u[1],t))},{keepPreviousData:!0});return{coupons:null!==(u=null==t?void 0:t.data)&&void 0!==u?u:[],paginatorInfo:(0,i.Q)(t),error:o,loading:n}},useApproveCouponMutation=()=>{let{t:e}=(0,a.$G)(),u=(0,s.useQueryClient)();return(0,s.useMutation)(d.approve,{onSuccess:()=>{r.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(c.P.COUPONS)}})},useDisApproveCouponMutation=()=>{let{t:e}=(0,a.$G)(),u=(0,s.useQueryClient)();return(0,s.useMutation)(d.disapprove,{onSuccess:()=>{r.Am.success(e("common:successfully-updated"))},onSettled:()=>{u.invalidateQueries(c.P.COUPONS)}})}}}]);