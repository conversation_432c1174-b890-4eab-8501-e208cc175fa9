"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5518,2036],{80818:function(t,r,n){n.d(r,{F:function(){return ChatIconNew},k:function(){return ChatIcon}});var a=n(85893);let ChatIcon=t=>(0,a.jsxs)("svg",{...t,viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M3.27533 2.272C4.65867 2.092 6.06867 2 7.5 2C8.93133 2 10.3413 2.09267 11.7247 2.272C13.006 2.43867 13.9187 3.51267 13.9947 4.75667C13.7718 4.68242 13.5408 4.63519 13.3067 4.616C11.4388 4.46094 9.56123 4.46094 7.69333 4.616C6.12133 4.74667 5 6.076 5 7.572V10.4293C4.99937 10.9785 5.15052 11.5172 5.43674 11.9859C5.72297 12.4546 6.13315 12.8351 6.622 13.0853L4.85333 14.8533C4.78341 14.9232 4.69436 14.9707 4.59742 14.99C4.50049 15.0092 4.40003 14.9993 4.30872 14.9615C4.21741 14.9237 4.13935 14.8597 4.08441 14.7776C4.02946 14.6954 4.00009 14.5988 4 14.5V11.8133C3.75813 11.7876 3.51656 11.7592 3.27533 11.728C1.93667 11.5533 1 10.3887 1 9.07467V4.92533C1 3.612 1.93667 2.44667 3.27533 2.27267V2.272Z",fill:"currentColor"}),(0,a.jsx)("path",{d:"M10.5 5.5C9.58267 5.5 8.674 5.538 7.776 5.61267C6.74933 5.698 6 6.56867 6 7.57267V10.4293C6 11.434 6.752 12.3053 7.78 12.3893C8.60867 12.4573 9.44667 12.494 10.292 12.4993L12.1467 14.3533C12.2166 14.4232 12.3056 14.4707 12.4026 14.49C12.4995 14.5092 12.6 14.4993 12.6913 14.4615C12.7826 14.4237 12.8606 14.3597 12.9156 14.2776C12.9705 14.1954 12.9999 14.0988 13 14V12.4067L13.22 12.3893C14.248 12.306 15 11.434 15 10.4293V7.572C15 6.56867 14.25 5.698 13.224 5.612C12.3179 5.53708 11.4092 5.49971 10.5 5.5Z",fill:"currentColor"})]}),ChatIconNew=t=>(0,a.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:(0,a.jsx)("path",{d:"M17 0H5a5.006 5.006 0 00-5 5v8a5.01 5.01 0 004 4.9V21a1 1 0 001.555.832L11.3 18H17a5.006 5.006 0 005-5V5a5.006 5.006 0 00-5-5zm-2 12H7a1 1 0 010-2h8a1 1 0 010 2zm2-4H5a1 1 0 010-2h12a1 1 0 110 2z",fill:"currentColor"})})},86779:function(t,r,n){n.d(r,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var a=n(85893);let InfoIcon=t=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...t,width:"1em",height:"1em",children:(0,a.jsx)("g",{children:(0,a.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=t=>(0,a.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t,children:[(0,a.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,a.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,a.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},18243:function(t,r,n){n.r(r),n.d(r,{default:function(){return approve_shop_view}});var a=n(85893),l=n(13125),c=n(60802),d=n(1587),m=n(33e3),h=n(5233);let DefaultCommission=t=>{let{onSubmit:r,loading:n}=t,{t:g}=(0,h.$G)();return(0,a.jsx)(d.l,{onSubmit:r,validationSchema:l.O,children:t=>{var r;let{register:l,formState:{errors:d}}=t;return(0,a.jsxs)("div",{className:"rounded bg-light p-5 sm:w-[24rem] m-auto w-full max-w-sm",children:[(0,a.jsx)(m.Z,{label:g("form:input-label-admin-commission-rate"),...l("admin_commission_rate"),defaultValue:"10",variant:"outline",className:"mb-4",inputClassName:"border-[#E5E7EB]",labelClassName:"font-medium text-[#111827]",required:!0,error:g(null===(r=d.admin_commission_rate)||void 0===r?void 0:r.message)}),(0,a.jsx)(c.Z,{type:"submit",loading:n,disabled:n,className:"ms-auto",children:g("form:button-label-submit")})]})}})};var g=n(80818),v=n(5114),b=n(75814),x=n(30824),S=n(77180);let MultiCommission=t=>{let{data:r,onSubmit:n,loading:C,creating:y,createAConversations:N}=t,{closeModal:w}=(0,b.SO)(),{t:j}=(0,h.$G)();return(0,a.jsxs)("div",{className:"rounded-[0.625rem] bg-light lg:w-[50rem] m-auto w-full max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-5 md:px-8 px-4 border-b border-b-black border-opacity-10 text-black",children:[(0,a.jsx)("h2",{className:"font-semibold text-lg leading-none",children:j("form:text-shop-approve-modal-title")}),(0,a.jsx)("div",{className:"cursor-pointer p-2 -mr-2 leading-none text-base transition-colors hover:text-black/70",children:(0,a.jsx)(v.Q,{onClick:w})})]}),(0,a.jsxs)("div",{className:"md:p-8 p-4 space-y-5 border-b border-b-black border-opacity-10",children:[r&&(null==r?void 0:r.content)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h3",{className:"text-black font-medium text-sm leading-none mb-3",children:j("form:text-shop-approve-modal-message-title")}),(0,a.jsx)("div",{className:"bg-[#F9FAFB] text-[#374151] text-sm border border-[#E5E7EB] rounded p-4 h-40 leading-[150%]",children:(0,a.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"scroll"}},children:(0,a.jsx)("p",{className:"pr-2",children:null==r?void 0:r.content})})})]}):"",(0,a.jsxs)(c.Z,{onClick:N,disabled:y,loading:y,className:"cursor-pointer gap-2 rounded-md md:px-5 h-auto md:py-4 p-3 p text-sm font-semibold",children:[(0,a.jsx)(g.F,{className:"md:text-xl"}),j("form:text-shop-approve-modal-message-button")]})]}),(0,a.jsx)(d.l,{validationSchema:l.approveShopWithCommissionSchema,onSubmit:n,options:{shouldUnregister:!0,defaultValues:{isCustomCommission:!!r&&null!=r&&!!r.enable&&!!(null==r?void 0:r.enable),admin_commission_rate:r&&(null==r?void 0:r.quote)?Number(null==r?void 0:r.quote):0}},className:"md:p-8 p-4",children:t=>{var r;let{register:n,control:l,watch:d,formState:{errors:h}}=t,g=d("isCustomCommission");return(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(S.Z,{name:"isCustomCommission",control:l,label:j("form:text-shop-approve-switch"),className:"flex flex-row-reverse justify-between",labelClassName:"text-base font-medium text-[#111827]"}),g?(0,a.jsx)(m.Z,{label:j("form:input-label-admin-commission-rate"),...n("admin_commission_rate"),variant:"outline",className:"mt-8",inputClassName:"border-[#E5E7EB]",labelClassName:"font-medium text-[#111827]",required:!0,error:j(null===(r=h.admin_commission_rate)||void 0===r?void 0:r.message)}):"",(0,a.jsx)("div",{className:"mt-5",children:(0,a.jsx)(c.Z,{type:"submit",className:"rounded-md",loading:C,disabled:C,children:g?j("form:button-label-submit"):j("form:text-shop-approve-button-title")})})]})}})]})};var C=n(86599),y=n(30042),N=n(67294),approve_shop_view=()=>{let{mutate:t,isLoading:r}=(0,y.bg)(),{mutate:n,isLoading:l}=(0,C.As)(),{data:{id:c,data:d}}=(0,b.X9)(),{closeModal:m}=(0,b.SO)(),h=(0,N.useCallback)(r=>{let{admin_commission_rate:n,isCustomCommission:a}=r;t({id:c,admin_commission_rate:Number(n),isCustomCommission:!!a}),m()},[]),g=(0,N.useCallback)(()=>{n({shop_id:c,via:"admin"})},[]);return console.log("data",d),(null==d?void 0:d.multiCommission)?(0,a.jsx)(MultiCommission,{data:d,createAConversations:g,creating:l,loading:r,onSubmit:h}):(0,a.jsx)(DefaultCommission,{loading:r,onSubmit:h})}},13125:function(t,r,n){n.d(r,{I:function(){return d},O:function(){return m}});var a=n(16310),l=n(79362);let c=new Date,d=a.Ry().shape({name:a.Z_().required("form:error-name-required"),balance:a.Ry().shape({payment_info:a.Ry().shape({email:a.Z_().required("form:error-account-holder-email-required").typeError("form:error-email-string").email("form:error-email-format"),name:a.Z_().required("form:error-account-holder-name-required"),bank:a.Z_().required("form:error-bank-name-required"),account:a.Rx().positive("form:error-account-number-positive-required").integer("form:error-account-number-integer-required").required("form:error-account-number-required").transform(t=>isNaN(t)?void 0:t)})}),settings:a.Ry().shape({contact:a.Z_().required("form:error-contact-number-required").matches(l.Oj,"form:error-contact-number-valid-required"),website:a.Z_().required("form:error-website-required").matches(l.F7,"form:error-url-valid-required"),socials:a.IX().of(a.Ry().shape({url:a.Z_().when("icon",t=>t?a.Z_().required("form:error-url-required"):a.Z_().nullable())})),shopMaintenance:a.Ry().when("isShopUnderMaintenance",{is:t=>t,then:()=>a.Ry().shape({title:a.Z_().required("Title is required"),description:a.Z_().required("Description is required"),start:a.hT().min(c.toDateString(),"Maintenance start date  field must be later than ".concat(c.toDateString())).required("Start date is required"),until:a.hT().required("Until date is required").min(a.iH("start"),"Until date must be greater than or equal to start date")})}).notRequired()})}),m=a.Ry().shape({admin_commission_rate:a.Rx().typeError("Commission rate must be a number").required("You must need to set your commission rate")})},66271:function(t,r,n){var a=n(85893),l=n(93967),c=n.n(l),d=n(98388);r.Z=t=>{let{message:r,className:n}=t;return(0,a.jsx)("p",{className:(0,d.m6)(c()("my-2 text-xs text-start text-red-500",n)),children:r})}},1587:function(t,r,n){n.d(r,{l:function(){return Form}});var a=n(85893),l=n(87536),c=n(47533),d=n(67294);let Form=t=>{let{onSubmit:r,children:n,options:m,validationSchema:h,serverError:g,resetValues:v,...b}=t,x=(0,l.cI)({...!!h&&{resolver:(0,c.X)(h)},...!!m&&m});return(0,d.useEffect)(()=>{g&&Object.entries(g).forEach(t=>{let[r,n]=t;x.setError(r,{type:"manual",message:n})})},[g,x]),(0,d.useEffect)(()=>{v&&x.reset(v)},[v,x]),(0,a.jsx)("form",{onSubmit:x.handleSubmit(r),noValidate:!0,...b,children:n(x)})}},33e3:function(t,r,n){var a=n(85893),l=n(71611),c=n(93967),d=n.n(c),m=n(67294),h=n(98388);let g={small:"text-sm h-10",medium:"h-12",big:"h-14"},v=m.forwardRef((t,r)=>{let{className:n,label:c,note:m,name:v,error:b,children:x,variant:S="normal",dimension:C="medium",shadow:y=!1,type:N="text",inputClassName:w,disabled:j,showLabel:E=!0,required:P,toolTipText:M,labelClassName:O,...I}=t,_=d()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===S,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===S,"border border-border-base focus:border-accent":"outline"===S},{"focus:shadow":y},g[C],w),A="number"===N&&j?"number-disable":"";return(0,a.jsxs)("div",{className:(0,h.m6)(n),children:[E||c?(0,a.jsx)(l.Z,{htmlFor:v,toolTipText:M,label:c,required:P,className:O}):"",(0,a.jsx)("input",{id:v,name:v,type:N,ref:r,className:(0,h.m6)(d()(j?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(A," select-none"):"",_)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:j,"aria-invalid":b?"true":"false",...I}),m&&(0,a.jsx)("p",{className:"mt-2 text-xs text-body",children:m}),b&&(0,a.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:b})]})});v.displayName="Input",r.Z=v},23091:function(t,r,n){var a=n(85893),l=n(93967),c=n.n(l),d=n(98388);r.Z=t=>{let{className:r,...n}=t;return(0,a.jsx)("label",{className:(0,d.m6)(c()("flex text-body-dark font-semibold text-sm leading-none mb-3",r)),...n})}},30824:function(t,r,n){var a=n(85893),l=n(93967),c=n.n(l),d=n(42189);n(85251),r.Z=t=>{let{options:r,children:n,style:l,className:m,...h}=t;return(0,a.jsx)(d.E,{options:{scrollbars:{autoHide:"scroll"},...r},className:c()("os-theme-thin-dark",m),style:l,...h,children:n})}},77180:function(t,r,n){var a=n(85893),l=n(66271),c=n(71611),d=n(77768),m=n(93967),h=n.n(m),g=n(5233),v=n(87536),b=n(98388);r.Z=t=>{let{control:r,label:n,name:m,error:x,disabled:S,required:C,toolTipText:y,className:N,labelClassName:w,...j}=t,{t:E}=(0,g.$G)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:(0,b.m6)(h()("flex items-center gap-x-4",N)),children:[(0,a.jsx)(v.Qr,{name:m,control:r,...j,render:t=>{let{field:{onChange:r,value:l}}=t;return(0,a.jsxs)(d.r,{checked:l,onChange:r,disabled:S,className:"".concat(l?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(S?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:m,children:[(0,a.jsxs)("span",{className:"sr-only",children:["Enable ",n]}),(0,a.jsx)("span",{className:"".concat(l?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),n?(0,a.jsx)(c.Z,{htmlFor:m,className:h()("mb-0",w),toolTipText:y,label:n,required:C}):""]}),x?(0,a.jsx)(l.Z,{message:x}):""]})}},71611:function(t,r,n){var a=n(85893),l=n(86779),c=n(71943),d=n(23091),m=n(98388);r.Z=t=>{let{className:r,required:n,label:h,toolTipText:g,htmlFor:v}=t;return(0,a.jsxs)(d.Z,{className:(0,m.m6)(r),htmlFor:v,children:[h,n?(0,a.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",g?(0,a.jsx)(c.u,{content:g,children:(0,a.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,a.jsx)(l.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(t,r,n){n.d(r,{u:function(){return Tooltip}});var a=n(85893),l=n(67294),c=n(93075),d=n(82364),m=n(24750),h=n(93967),g=n.n(h),v=n(67421),b=n(98388);let x={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},S={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(t){let{children:r,content:n,gap:h=8,animation:C="zoomIn",placement:y="top",size:N="md",rounded:w="DEFAULT",shadow:j="md",color:E="default",className:P,arrowClassName:M,showArrow:O=!0}=t,[I,_]=(0,l.useState)(!1),A=(0,l.useRef)(null),{t:k}=(0,v.$G)(),{x:Q,y:q,refs:R,strategy:Z,context:D}=(0,c.YF)({placement:y,open:I,onOpenChange:_,middleware:[(0,d.x7)({element:A}),(0,d.cv)(h),(0,d.RR)(),(0,d.uY)({padding:8})],whileElementsMounted:m.Me}),{getReferenceProps:F,getFloatingProps:V}=(0,c.NI)([(0,c.XI)(D),(0,c.KK)(D),(0,c.qs)(D,{role:"tooltip"}),(0,c.bQ)(D)]),{isMounted:z,styles:G}=(0,c.Y_)(D,{duration:{open:150,close:150},...S[C]});return(0,a.jsxs)(a.Fragment,{children:[(0,l.cloneElement)(r,F({ref:R.setReference,...r.props})),(z||I)&&(0,a.jsx)(c.ll,{children:(0,a.jsxs)("div",{role:"tooltip",ref:R.setFloating,className:(0,b.m6)(g()(x.base,x.size[N],x.rounded[w],x.variant.solid.base,x.variant.solid.color[E],x.shadow[j],P)),style:{position:Z,top:null!=q?q:0,left:null!=Q?Q:0,...G},...V(),children:[k("".concat(n)),O&&(0,a.jsx)(c.Y$,{ref:A,context:D,className:g()(x.arrow.color[E],M),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},86599:function(t,r,n){n.d(r,{Cq:function(){return useConversationQuery},sw:function(){return useConversationsQuery},As:function(){return useCreateConversations},uX:function(){return useMessageSeen},kY:function(){return useMessagesQuery},$3:function(){return useSendMessage}});var a=n(11163),l=n(88767),c=n(22920),d=n(5233),m=n(97514),h=n(47869),g=n(28597),v=n(55191),b=n(3737);let x={...(0,v.h)(h.P.CONVERSIONS),create(t){let{shop_id:r,via:n}=t;return b.eN.post(h.P.CONVERSIONS,{shop_id:r,via:n})},getMessage(t){let{slug:r,...n}=t;return b.eN.get("".concat(h.P.MESSAGE,"/").concat(r),{searchJoin:"and",...n})},getConversion(t){let{id:r}=t;return b.eN.get("".concat(h.P.CONVERSIONS,"/").concat(r))},messageCreate(t){let{id:r,...n}=t;return b.eN.post("".concat(h.P.MESSAGE,"/").concat(r),n)},messageSeen(t){let{id:r}=t;return b.eN.post("".concat(h.P.MESSAGE_SEEN,"/").concat(r),r)},allConversation:t=>b.eN.get(h.P.CONVERSIONS,t)};var S=n(75814),C=n(16203);let useConversationsQuery=t=>{var r,n;let{data:a,isLoading:c,error:d,refetch:m,fetchNextPage:v,hasNextPage:b,isFetching:S,isSuccess:C,isFetchingNextPage:y}=(0,l.useInfiniteQuery)([h.P.CONVERSIONS,t],t=>{let{queryKey:r,pageParam:n}=t;return x.allConversation(Object.assign({},r[1],n))},{getNextPageParam:t=>{let{current_page:r,last_page:n}=t;return n>r&&{page:r+1}}});return{conversations:null!==(n=null==a?void 0:null===(r=a.pages)||void 0===r?void 0:r.flatMap(t=>t.data))&&void 0!==n?n:[],paginatorInfo:Array.isArray(null==a?void 0:a.pages)?(0,g.Q)(null==a?void 0:a.pages[a.pages.length-1]):null,loading:c,error:d,isFetching:S,refetch:m,isSuccess:C,isLoadingMore:y,loadMore:function(){b&&v()},hasMore:!!b}},useCreateConversations=()=>{let{t}=(0,d.$G)(),r=(0,a.useRouter)(),{closeModal:n}=(0,S.SO)(),g=(0,l.useQueryClient)(),{permissions:v}=(0,C.WA)(),b=(0,C.Ft)(C.M$,v);return(0,l.useMutation)(x.create,{onSuccess:a=>{if(null==a?void 0:a.id){var l,d;let h=b?null===m.Z||void 0===m.Z?void 0:null===(l=m.Z.message)||void 0===l?void 0:l.details(null==a?void 0:a.id):null===m.Z||void 0===m.Z?void 0:null===(d=m.Z.shopMessage)||void 0===d?void 0:d.details(null==a?void 0:a.id);c.Am.success(t("common:successfully-created")),r.push("".concat(h)),n()}else c.Am.error("Something went wrong!")},onSettled:()=>{g.invalidateQueries(h.P.MESSAGE),g.invalidateQueries(h.P.CONVERSIONS)}})},useMessagesQuery=t=>{var r,n;let{data:a,isLoading:c,error:d,refetch:m,fetchNextPage:v,hasNextPage:b,isFetching:S,isSuccess:C,isFetchingNextPage:y}=(0,l.useInfiniteQuery)([h.P.MESSAGE,t],t=>{let{queryKey:r,pageParam:n}=t;return x.getMessage(Object.assign({},r[1],n))},{getNextPageParam:t=>{let{current_page:r,last_page:n}=t;return n>r&&{page:r+1}}});return{messages:null!==(n=null==a?void 0:null===(r=a.pages)||void 0===r?void 0:r.flatMap(t=>t.data))&&void 0!==n?n:[],paginatorInfo:Array.isArray(null==a?void 0:a.pages)?(0,g.Q)(null==a?void 0:a.pages[a.pages.length-1]):null,loading:c,error:d,isFetching:S,refetch:m,isSuccess:C,isLoadingMore:y,loadMore:function(){b&&v()},hasMore:!!b}},useConversationQuery=t=>{let{id:r}=t,{data:n,error:a,isLoading:c,isFetching:d}=(0,l.useQuery)([h.P.CONVERSIONS,r],()=>x.getConversion({id:r}),{keepPreviousData:!0});return{data:null!=n?n:[],error:a,loading:c,isFetching:d}},useSendMessage=()=>{let{t}=(0,d.$G)(),r=(0,l.useQueryClient)();return(0,l.useMutation)(x.messageCreate,{onSuccess:()=>{c.Am.success(t("common:text-message-sent"))},onSettled:()=>{r.invalidateQueries(h.P.MESSAGE),r.invalidateQueries(h.P.CONVERSIONS)}})},useMessageSeen=()=>{let{t}=(0,d.$G)(),r=(0,l.useQueryClient)();return(0,l.useMutation)(x.messageSeen,{onSettled:()=>{r.invalidateQueries(h.P.MESSAGE),r.invalidateQueries(h.P.CONVERSIONS)}})}},30042:function(t,r,n){n.d(r,{bg:function(){return useApproveShopMutation},TC:function(){return useCreateShopMutation},mj:function(){return useDisApproveShopMutation},T3:function(){return useInActiveShopsQuery},DZ:function(){return useShopQuery},uL:function(){return useShopsQuery},_3:function(){return useTransferShopOwnershipMutation},D9:function(){return useUpdateShopMutation}});var a=n(93345),l=n(97514),c=n(47869),d=n(16203),m=n(28597),h=n(5233),g=n(11163),v=n(88767),b=n(22920),x=n(3737),S=n(55191);let C={...(0,S.h)(c.P.SHOPS),get(t){let{slug:r}=t;return x.eN.get("".concat(c.P.SHOPS,"/").concat(r))},paginated:t=>{let{name:r,...n}=t;return x.eN.get(c.P.SHOPS,{searchJoin:"and",...n,search:x.eN.formatSearchParams({name:r})})},newOrInActiveShops:t=>{let{is_active:r,name:n,...a}=t;return x.eN.get(c.P.NEW_OR_INACTIVE_SHOPS,{searchJoin:"and",is_active:r,name:n,...a,search:x.eN.formatSearchParams({is_active:r,name:n})})},approve:t=>x.eN.post(c.P.APPROVE_SHOP,t),disapprove:t=>x.eN.post(c.P.DISAPPROVE_SHOP,t),transferShopOwnership:t=>x.eN.post(c.P.TRANSFER_SHOP_OWNERSHIP,t)},useApproveShopMutation=()=>{let{t}=(0,h.$G)(),r=(0,v.useQueryClient)();return(0,v.useMutation)(C.approve,{onSuccess:()=>{b.Am.success(t("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(c.P.SHOPS)}})},useDisApproveShopMutation=()=>{let{t}=(0,h.$G)(),r=(0,v.useQueryClient)();return(0,v.useMutation)(C.disapprove,{onSuccess:()=>{b.Am.success(t("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(c.P.SHOPS)}})},useCreateShopMutation=()=>{let t=(0,v.useQueryClient)(),r=(0,g.useRouter)();return(0,v.useMutation)(C.create,{onSuccess:()=>{let{permissions:t}=(0,d.WA)();if((0,d.Ft)(d.M$,t))return r.push(l.Z.adminMyShops);r.push(l.Z.dashboard)},onSettled:()=>{t.invalidateQueries(c.P.SHOPS)}})},useUpdateShopMutation=()=>{let{t}=(0,h.$G)(),r=(0,g.useRouter)(),n=(0,v.useQueryClient)();return(0,v.useMutation)(C.update,{onSuccess:async n=>{await r.push("/".concat(null==n?void 0:n.slug,"/edit"),void 0,{locale:a.Config.defaultLanguage}),b.Am.success(t("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(c.P.SHOPS)}})},useTransferShopOwnershipMutation=()=>{let{t}=(0,h.$G)(),r=(0,v.useQueryClient)();return(0,v.useMutation)(C.transferShopOwnership,{onSuccess:r=>{var n;b.Am.success("".concat(t("common:successfully-transferred")).concat(null===(n=r.owner)||void 0===n?void 0:n.name))},onSettled:()=>{r.invalidateQueries(c.P.SHOPS)}})},useShopQuery=(t,r)=>{let{slug:n}=t;return(0,v.useQuery)([c.P.SHOPS,{slug:n}],()=>C.get({slug:n}),r)},useShopsQuery=t=>{var r;let{data:n,error:a,isLoading:l}=(0,v.useQuery)([c.P.SHOPS,t],t=>{let{queryKey:r,pageParam:n}=t;return C.paginated(Object.assign({},r[1],n))},{keepPreviousData:!0});return{shops:null!==(r=null==n?void 0:n.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(n),error:a,loading:l}},useInActiveShopsQuery=t=>{var r;let{data:n,error:a,isLoading:l}=(0,v.useQuery)([c.P.NEW_OR_INACTIVE_SHOPS,t],t=>{let{queryKey:r,pageParam:n}=t;return C.newOrInActiveShops(Object.assign({},r[1],n))},{keepPreviousData:!0});return{shops:null!==(r=null==n?void 0:n.data)&&void 0!==r?r:[],paginatorInfo:(0,m.Q)(n),error:a,loading:l}}},95389:function(t,r,n){n.d(r,{_:function(){return v},b:function(){return H}});var a=n(67294),l=n(19946),c=n(12351),d=n(16723),m=n(23784),h=n(73781);let g=(0,a.createContext)(null);function H(){let[t,r]=(0,a.useState)([]);return[t.length>0?t.join(" "):void 0,(0,a.useMemo)(()=>function(t){let n=(0,h.z)(t=>(r(r=>[...r,t]),()=>r(r=>{let n=r.slice(),a=n.indexOf(t);return -1!==a&&n.splice(a,1),n}))),l=(0,a.useMemo)(()=>({register:n,slot:t.slot,name:t.name,props:t.props}),[n,t.slot,t.name,t.props]);return a.createElement(g.Provider,{value:l},t.children)},[r])]}let v=Object.assign((0,c.yV)(function(t,r){let n=(0,l.M)(),{id:h=`headlessui-label-${n}`,passive:v=!1,...b}=t,x=function u(){let t=(0,a.useContext)(g);if(null===t){let t=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return t}(),S=(0,m.T)(r);(0,d.e)(()=>x.register(h),[h,x.register]);let C={ref:S,...x.props,id:h};return v&&("onClick"in C&&(delete C.htmlFor,delete C.onClick),"onClick"in b&&delete b.onClick),(0,c.sY)({ourProps:C,theirProps:b,slot:x.slot||{},defaultTag:"label",name:x.name||"Label"})}),{})},77768:function(t,r,n){n.d(r,{r:function(){return E}});var a=n(67294),l=n(12351),c=n(19946),d=n(61363),m=n(64103),h=n(95389),g=n(39516),v=n(14157),b=n(23784),x=n(46045),S=n(18689),C=n(73781),y=n(31147),N=n(94192);let w=(0,a.createContext)(null);w.displayName="GroupContext";let j=a.Fragment,E=Object.assign((0,l.yV)(function(t,r){let n=(0,c.M)(),{id:h=`headlessui-switch-${n}`,checked:g,defaultChecked:j=!1,onChange:E,name:P,value:M,form:O,...I}=t,_=(0,a.useContext)(w),A=(0,a.useRef)(null),k=(0,b.T)(A,r,null===_?null:_.setSwitch),[Q,q]=(0,y.q)(g,E,j),R=(0,C.z)(()=>null==q?void 0:q(!Q)),Z=(0,C.z)(t=>{if((0,m.P)(t.currentTarget))return t.preventDefault();t.preventDefault(),R()}),D=(0,C.z)(t=>{t.key===d.R.Space?(t.preventDefault(),R()):t.key===d.R.Enter&&(0,S.g)(t.currentTarget)}),F=(0,C.z)(t=>t.preventDefault()),V=(0,a.useMemo)(()=>({checked:Q}),[Q]),z={id:h,ref:k,role:"switch",type:(0,v.f)(t,A),tabIndex:0,"aria-checked":Q,"aria-labelledby":null==_?void 0:_.labelledby,"aria-describedby":null==_?void 0:_.describedby,onClick:Z,onKeyUp:D,onKeyPress:F},G=(0,N.G)();return(0,a.useEffect)(()=>{var t;let r=null==(t=A.current)?void 0:t.closest("form");r&&void 0!==j&&G.addEventListener(r,"reset",()=>{q(j)})},[A,q]),a.createElement(a.Fragment,null,null!=P&&Q&&a.createElement(x._,{features:x.A.Hidden,...(0,l.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:O,checked:Q,name:P,value:M})}),(0,l.sY)({ourProps:z,theirProps:I,slot:V,defaultTag:"button",name:"Switch"}))}),{Group:function(t){var r;let[n,c]=(0,a.useState)(null),[d,m]=(0,h.b)(),[v,b]=(0,g.f)(),x=(0,a.useMemo)(()=>({switch:n,setSwitch:c,labelledby:d,describedby:v}),[n,c,d,v]);return a.createElement(b,{name:"Switch.Description"},a.createElement(m,{name:"Switch.Label",props:{htmlFor:null==(r=x.switch)?void 0:r.id,onClick(t){n&&("LABEL"===t.currentTarget.tagName&&t.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},a.createElement(w.Provider,{value:x},(0,l.sY)({ourProps:{},theirProps:t,defaultTag:j,name:"Switch.Group"}))))},Label:h._,Description:g.d})},31147:function(t,r,n){n.d(r,{q:function(){return T}});var a=n(67294),l=n(73781);function T(t,r,n){let[c,d]=(0,a.useState)(n),m=void 0!==t,h=(0,a.useRef)(m),g=(0,a.useRef)(!1),v=(0,a.useRef)(!1);return!m||h.current||g.current?m||!h.current||v.current||(v.current=!0,h.current=m,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(g.current=!0,h.current=m,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[m?t:c,(0,l.z)(t=>(m||d(t),null==r?void 0:r(t)))]}},14157:function(t,r,n){n.d(r,{f:function(){return s}});var a=n(67294),l=n(16723);function i(t){var r;if(t.type)return t.type;let n=null!=(r=t.as)?r:"button";if("string"==typeof n&&"button"===n.toLowerCase())return"button"}function s(t,r){let[n,c]=(0,a.useState)(()=>i(t));return(0,l.e)(()=>{c(i(t))},[t.type,t.as]),(0,l.e)(()=>{n||r.current&&r.current instanceof HTMLButtonElement&&!r.current.hasAttribute("type")&&c("button")},[n,r]),n}},18689:function(t,r,n){function f(t,r){return t?t+"["+r+"]":r}function p(t){var r,n;let a=null!=(r=null==t?void 0:t.form)?r:t.closest("form");if(a){for(let r of a.elements)if(r!==t&&("INPUT"===r.tagName&&"submit"===r.type||"BUTTON"===r.tagName&&"submit"===r.type||"INPUT"===r.nodeName&&"image"===r.type)){r.click();return}null==(n=a.requestSubmit)||n.call(a)}}n.d(r,{g:function(){return p},t:function(){return function e(t={},r=null,n=[]){for(let[a,l]of Object.entries(t))!function o(t,r,n){if(Array.isArray(n))for(let[a,l]of n.entries())o(t,f(r,a.toString()),l);else n instanceof Date?t.push([r,n.toISOString()]):"boolean"==typeof n?t.push([r,n?"1":"0"]):"string"==typeof n?t.push([r,n]):"number"==typeof n?t.push([r,`${n}`]):null==n?t.push([r,""]):e(n,r,t)}(n,f(r,a),l);return n}}})}}]);