"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_dashboard_widgets_table_widget-product-count-by-category_tsx-_32bc1"],{

/***/ "./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx":
/*!*************************************************************************************!*\
  !*** ./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/table */ \"./src/components/ui/table.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_locals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/locals */ \"./src/utils/locals.tsx\");\n/* harmony import */ var _components_icons_no_data_found__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/no-data-found */ \"./src/components/icons/no-data-found.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ProductCountByCategory = (param)=>{\n    let { products, title, className } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { alignLeft } = (0,_utils_locals__WEBPACK_IMPORTED_MODULE_4__.useIsRTL)();\n    let columns = [\n        {\n            title: t(\"table:table-item-category-id\"),\n            dataIndex: \"category_id\",\n            key: \"category_id\",\n            align: alignLeft,\n            width: 120,\n            render: (id)=>\"#\".concat(t(\"table:table-item-id\"), \": \").concat(id)\n        },\n        {\n            title: t(\"table:table-item-category-name\"),\n            className: \"cursor-pointer\",\n            dataIndex: \"category_name\",\n            key: \"category_name\",\n            align: alignLeft,\n            width: 220,\n            ellipsis: true\n        },\n        {\n            title: t(\"table:table-item-shop\"),\n            dataIndex: \"shop_name\",\n            key: \"shop\",\n            align: alignLeft,\n            ellipsis: true,\n            width: 200,\n            render: (shop_name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate whitespace-nowrap\",\n                    children: shop_name\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: t(\"table:table-item-Product-count\"),\n            className: \"cursor-pointer\",\n            dataIndex: \"product_count\",\n            key: \"product_count\",\n            width: 180,\n            align: \"center\",\n            render: (product_count)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: product_count\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"overflow-hidden rounded-lg bg-white p-7\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between pb-7\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"before:content-'' relative mt-1.5 bg-light text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:w-1 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-7 rtl:before:-right-7\",\n                        children: t(title)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_1__.Table, {\n                    /* @ts-ignore */ columns: columns,\n                    emptyText: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center py-7\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_no_data_found__WEBPACK_IMPORTED_MODULE_5__.NoDataFound, {\n                                    className: \"w-52\"\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-1 pt-6 text-base font-semibold text-heading\",\n                                    children: t(\"table:empty-table-data\")\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[13px]\",\n                                    children: t(\"table:empty-table-sorry-text\")\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                    data: products,\n                    rowKey: \"category_id\",\n                    scroll: {\n                        x: 200\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\dashboard\\\\widgets\\\\table\\\\widget-product-count-by-category.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(ProductCountByCategory, \"uofAaYMwV3u6bdyc87uzx77AbwA=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _utils_locals__WEBPACK_IMPORTED_MODULE_4__.useIsRTL\n    ];\n});\n_c = ProductCountByCategory;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductCountByCategory);\nvar _c;\n$RefreshReg$(_c, \"ProductCountByCategory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/dashboard/widgets/table/widget-product-count-by-category.tsx\n"));

/***/ })

}]);