(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[750,2007,2036],{19543:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/promotion-popup",function(){return r(54224)}])},62003:function(e,t,r){"use strict";r.d(t,{s:function(){return ChevronLeft}});var o=r(85893);let ChevronLeft=e=>(0,o.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},12032:function(e,t,r){"use strict";r.d(t,{N:function(){return SaveIcon}});var o=r(85893);let SaveIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,o.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},97670:function(e,t,r){"use strict";r.r(t);var o=r(85893),l=r(78985),i=r(79362),n=r(8144),s=r(74673),a=r(99494),d=r(5233),p=r(1631),c=r(11163),m=r(48583),f=r(93967),x=r.n(f),v=r(30824),b=r(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:r}=(0,d.$G)(),[l,n]=(0,m.KO)(i.Hf),{childMenu:s}=t,{width:a}=(0,b.Z)();return(0,o.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:n,icon:s,childMenu:d}=e;return(0,o.jsx)(p.Z,{href:t,label:r(n),icon:s,childMenu:d,miniSidebar:l&&a>=i.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[r,l]=(0,m.KO)(i.Hf),n=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(n),{width:p}=(0,b.Z)();return(0,o.jsx)(o.Fragment,{children:null==s?void 0:s.map((e,l)=>{var s;return(0,o.jsxs)("div",{className:x()("flex flex-col px-5",r&&p>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,o.jsx)("div",{className:x()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&p>=i.h2?"hidden":""),children:t(null===(s=n[e])||void 0===s?void 0:s.label)}),(0,o.jsx)(SidebarItemMap,{menuItems:n[e]})]},l)})})};t.default=e=>{let{children:t}=e,{locale:r}=(0,c.useRouter)(),[a,d]=(0,m.KO)(i.Hf),[p]=(0,m.KO)(i.GH),[f]=(0,m.KO)(i.W4),{width:h}=(0,b.Z)();return(0,o.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,o.jsx)(l.Z,{}),(0,o.jsx)(s.Z,{children:(0,o.jsx)(SideBarGroup,{})}),(0,o.jsxs)("div",{className:"flex flex-1",children:[(0,o.jsx)("aside",{className:x()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=i.h2&&(p||f)?"lg:pt-[8.75rem]":"pt-20",a&&h>=i.h2?"lg:w-24":"lg:w-76"),children:(0,o.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,o.jsx)(v.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,o.jsx)(SideBarGroup,{})})})}),(0,o.jsxs)("main",{className:x()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=i.h2&&(p||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&h>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,o.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,o.jsx)(n.Z,{})]})]})]})}},59122:function(e,t,r){"use strict";r.d(t,{Z:function(){return SettingsPageHeader}});var o=r(85893),l=r(11163),i=r(99494),n=r(8152),s=r(93967),a=r.n(s),d=r(5233),p=r(67294),c=r(86998),m=r(62003);function SettingsPageHeader(e){var t,r,s,f;let{pageTitle:x}=e,{t:v}=(0,d.$G)(),b=(0,l.useRouter)(),{sliderEl:h,sliderPrevBtn:g,sliderNextBtn:w,scrollToTheRight:y,scrollToTheLeft:j}=function(){let e=(0,p.useRef)(null),t=(0,p.useRef)(null),r=(0,p.useRef)(null);return(0,p.useEffect)(()=>{let o=e.current,l=t.current,i=r.current,n=o.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=o.offsetWidth;o.scrollWidth>e?(null==i||i.classList.remove("opacity-0","invisible"),n&&(null==o||o.classList.add("!-mb-[43px]"))):(null==i||i.classList.add("opacity-0","invisible"),n&&(null==o||o.classList.remove("!-mb-[43px]"))),null==l||l.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==o?void 0:o.scrollLeft,t=null==o?void 0:o.offsetWidth;(null==o?void 0:o.scrollWidth)-e==t?(null==i||i.classList.add("opacity-0","invisible"),null==l||l.classList.remove("opacity-0","invisible")):null==i||i.classList.remove("opacity-0","invisible"),0===e?(null==l||l.classList.add("opacity-0","invisible"),null==i||i.classList.remove("opacity-0","invisible")):null==l||l.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),o.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),o.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:r,scrollToTheRight:function(){let r=e.current.offsetWidth;e.current.scrollLeft+=r/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,r.current.classList.remove("opacity-0","invisible")}}}(),N=null===i.siteSettings||void 0===i.siteSettings?void 0:null===(f=i.siteSettings.sidebarLinks)||void 0===f?void 0:null===(s=f.admin)||void 0===s?void 0:null===(r=s.settings)||void 0===r?void 0:null===(t=r.childMenu[0])||void 0===t?void 0:t.childMenu,P=b.asPath.split("#")[0].split("?")[0];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,o.jsx)("h1",{className:"text-lg font-semibold text-heading",children:v(x)})}),(0,o.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,o.jsx)("button",{title:"Prev",ref:g,onClick:()=>j(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,o.jsx)(m.s,{className:"h-[18px] w-[18px]"})}),(0,o.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,o.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:h,children:null==N?void 0:N.map((e,t)=>(0,o.jsx)(n.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:a()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",P===e.href?"text-heading before:w-full":null),children:v(e.label)},t))})}),(0,o.jsx)("button",{title:"Next",ref:w,onClick:()=>y(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,o.jsx)(c._,{className:"h-[18px] w-[18px]"})})]})]})}},22220:function(e,t,r){"use strict";var o=r(85893),l=r(93967),i=r.n(l),n=r(98388);t.Z=e=>{let{children:t,className:r,...l}=e;return(0,o.jsx)("div",{className:(0,n.m6)(i()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",r)),...l,children:t})}},77180:function(e,t,r){"use strict";var o=r(85893),l=r(66271),i=r(71611),n=r(77768),s=r(93967),a=r.n(s),d=r(5233),p=r(87536),c=r(98388);t.Z=e=>{let{control:t,label:r,name:s,error:m,disabled:f,required:x,toolTipText:v,className:b,labelClassName:h,...g}=e,{t:w}=(0,d.$G)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:(0,c.m6)(a()("flex items-center gap-x-4",b)),children:[(0,o.jsx)(p.Qr,{name:s,control:t,...g,render:e=>{let{field:{onChange:t,value:l}}=e;return(0,o.jsxs)(n.r,{checked:l,onChange:t,disabled:f,className:"".concat(l?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(f?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,o.jsxs)("span",{className:"sr-only",children:["Enable ",r]}),(0,o.jsx)("span",{className:"".concat(l?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),r?(0,o.jsx)(i.Z,{htmlFor:s,className:a()("mb-0",h),toolTipText:v,label:r,required:x}):""]}),m?(0,o.jsx)(l.Z,{message:m}):""]})}},95414:function(e,t,r){"use strict";var o=r(85893),l=r(71611),i=r(93967),n=r.n(i),s=r(67294),a=r(98388);let d=s.forwardRef((e,t)=>{let{className:r,label:i,toolTipText:s,name:d,error:p,variant:c="normal",shadow:m=!1,inputClassName:f,disabled:x,required:v,...b}=e,h=n()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===c,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===c,"border border-border-base focus:border-accent":"outline"===c},{"focus:shadow":m},f);return(0,o.jsxs)("div",{className:(0,a.m6)(n()(r)),children:[i&&(0,o.jsx)(l.Z,{htmlFor:d,toolTipText:s,label:i,required:v}),(0,o.jsx)("textarea",{id:d,name:d,className:(0,a.m6)(n()(h,x?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:x,...b}),p&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:p})]})});d.displayName="TextArea",t.Z=d},54224:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return L},default:function(){return PromotionPopup}});var o=r(85893),l=r(97670),i=r(92072),n=r(12032),s=r(16310);let a=s.Ry().shape({promoPopup:s.Ry().when("isPromoPopUp",{is:e=>e,then:()=>s.Ry().shape({title:s.Z_().required("form:error-title-required"),description:s.Z_().required("form:error-description-required"),popUpDelay:s.Rx().transform(e=>isNaN(e)?void 0:e).min(1e3,"form:error-popup-delay-min").required("form:error-popup-delay"),popUpExpiredIn:s.Rx().transform(e=>isNaN(e)?void 0:e).min(1,"form:error-popup-expired-min").required("form:error-popup-expired"),image:s.nK().test("file","form:error-image",e=>!!e&&Object.keys(e).length>0),popUpNotShow:s.Ry().when("isPopUpNotShow",{is:e=>e,then:()=>s.Ry().shape({title:s.Z_().required("form:error-title-required"),popUpExpiredIn:s.Rx().transform(e=>isNaN(e)?void 0:e).min(1,"form:error-popup-expired-min").required("form:error-popup-expired")})})})})});var d=r(60802),p=r(80602),c=r(66272),m=r(33e3),f=r(22220),x=r(77180),v=r(95414),b=r(90573),h=r(3986),g=r(47533),w=r(5233),y=r(11163),j=r(87536);function PromoPopUpSettingsForm(e){var t,r,l,s,N,P,S,k,L,E,Z,T,C,R,U,_,q,D;let{settings:F}=e,{t:O}=(0,w.$G)(),{locale:G}=(0,y.useRouter)(),{mutate:I,isLoading:M}=(0,b.B)(),{options:B}=null!=F?F:{},{register:z,handleSubmit:V,control:A,watch:W,reset:$,formState:{errors:K,isDirty:Y}}=(0,j.cI)({shouldUnregister:!0,resolver:(0,g.X)(a),defaultValues:{...B}});async function onSubmit(e){I({language:G,options:{...B,...e}}),$(e,{keepValues:!0})}(0,h.H)({isDirty:Y});let X=(0,o.jsxs)("span",{children:[O("form:popup-cover-image-help-text")," ",(0,o.jsx)("br",{}),O("form:cover-image-dimension-help-text")," \xa0",(0,o.jsxs)("span",{className:"font-bold",children:["450 x 450",O("common:text-px")]})]}),J=W("isPromoPopUp"),Q=W("promoPopup.isPopUpNotShow");return(0,o.jsxs)("form",{onSubmit:V(onSubmit),children:[(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,o.jsx)(p.Z,{title:O("form:form-title-information"),details:O("form:site-popup-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsx)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,o.jsx)("div",{className:"my-5",children:(0,o.jsx)(x.Z,{name:"isPromoPopUp",control:A,label:O("form:text-popup-switch"),toolTipText:O("form:input-tooltip-promo-enable")})})})]}),(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,o.jsx)(p.Z,{title:O("form:input-label-popup-cover-image"),details:X,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsx)(i.Z,{className:"w-full logo-field-area sm:w-8/12 md:w-2/3",children:(0,o.jsx)(c.Z,{name:"promoPopup.image",control:A,multiple:!1,disabled:!J,label:O("text-upload-highlight"),...J&&{required:!0},error:O(null==K?void 0:null===(r=K.promoPopup)||void 0===r?void 0:null===(t=r.image)||void 0===t?void 0:t.message)})})]}),(0,o.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,o.jsx)(p.Z,{title:O("form:form-title-popup-information"),details:O("form:site-popup-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)(m.Z,{label:O("form:input-label-title"),toolTipText:O("form:input-tooltip-promo-title"),...z("promoPopup.title"),error:O(null==K?void 0:null===(s=K.promoPopup)||void 0===s?void 0:null===(l=s.title)||void 0===l?void 0:l.message),variant:"outline",className:"mb-5",...J&&{required:!0},disabled:!J}),(0,o.jsx)(v.Z,{label:O("form:input-label-description"),toolTipText:O("form:input-tooltip-promo-description"),...z("promoPopup.description"),error:O(null==K?void 0:null===(P=K.promoPopup)||void 0===P?void 0:null===(N=P.description)||void 0===N?void 0:N.message),variant:"outline",className:"mb-5",...J&&{required:!0},disabled:!J})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,o.jsx)(p.Z,{title:O("form:form-title-popup-control"),details:O("form:site-popup-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,o.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)(m.Z,{label:O("form:title-popup-delay"),toolTipText:O("form:input-tooltip-promo-delay"),...z("promoPopup.popUpDelay"),error:O(null==K?void 0:null===(k=K.promoPopup)||void 0===k?void 0:null===(S=k.popUpDelay)||void 0===S?void 0:S.message),variant:"outline",className:"mb-5",type:"number",...J&&{required:!0},disabled:!J,note:O("form:title-popup-delay-info")}),(0,o.jsx)(m.Z,{label:O("form:title-popup-expired-in"),toolTipText:O("form:input-tooltip-promo-expired"),...z("promoPopup.popUpExpiredIn"),error:O(null==K?void 0:null===(E=K.promoPopup)||void 0===E?void 0:null===(L=E.popUpExpiredIn)||void 0===L?void 0:L.message),variant:"outline",className:"mb-5",type:"number",...J&&{required:!0},disabled:!J,min:"1",note:O("form:title-popup-expired-in-info")}),(0,o.jsx)("div",{className:"mb-5",children:(0,o.jsx)(x.Z,{name:"promoPopup.isPopUpNotShow",disabled:!J,control:A,label:O("form:title-popup-checkbox"),toolTipText:O("form:input-tooltip-promo-not-show"),error:O(null==K?void 0:null===(T=K.promoPopup)||void 0===T?void 0:null===(Z=T.isPopUpNotShow)||void 0===Z?void 0:Z.message)})}),(0,o.jsx)(m.Z,{label:O("form:input-label-title"),toolTipText:O("form:input-tooltip-promo-not-show-title"),...z("promoPopup.popUpNotShow.title"),error:O(null==K?void 0:null===(U=K.promoPopup)||void 0===U?void 0:null===(R=U.popUpNotShow)||void 0===R?void 0:null===(C=R.title)||void 0===C?void 0:C.message),variant:"outline",className:"mb-5",...J&&Q&&{required:!0},disabled:!J||!Q}),(0,o.jsx)(m.Z,{label:O("form:title-popup-expired-in"),toolTipText:O("form:input-tooltip-promo-not-show-expired"),...z("promoPopup.popUpNotShow.popUpExpiredIn"),error:O(null==K?void 0:null===(D=K.promoPopup)||void 0===D?void 0:null===(q=D.popUpNotShow)||void 0===q?void 0:null===(_=q.popUpExpiredIn)||void 0===_?void 0:_.message),variant:"outline",className:"mb-5",type:"number",...J&&Q&&{required:!0},disabled:!J||!Q,min:"1",note:O("form:title-popup-expired-in-info")})]})]}),(0,o.jsx)(f.Z,{className:"z-0",children:(0,o.jsxs)(d.Z,{loading:M,disabled:M||!Y,className:"text-sm md:text-base",children:[(0,o.jsx)(n.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),O("form:button-label-save-settings")]})})]})}var N=r(59122),P=r(45957),S=r(55846),k=r(16203),L=!0;function PromotionPopup(){let{t:e}=(0,w.$G)(),{locale:t}=(0,y.useRouter)(),{settings:r,loading:l,error:i}=(0,b.n)({language:t});return l?(0,o.jsx)(S.Z,{text:e("common:text-loading")}):i?(0,o.jsx)(P.Z,{message:i.message}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(N.Z,{pageTitle:"form:text-popup-settings"}),(0,o.jsx)(PromoPopUpSettingsForm,{settings:r})]})}PromotionPopup.authenticate={permissions:k.M$},PromotionPopup.Layout=l.default},3986:function(e,t,r){"use strict";r.d(t,{H:function(){return useConfirmRedirectIfDirty}});var o=r(67294),l=r(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:r="You have unsaved changes - are you sure you wish to leave this page?"}=e,i=(0,l.useRouter)(),n=(0,o.useRef)(t),s=(0,o.useRef)(r);(0,o.useEffect)(()=>{n.current=t},[t]),(0,o.useEffect)(()=>{s.current=r},[r]);let a=(0,o.useCallback)(e=>{if(n.current)return e.preventDefault(),e.returnValue=s.current},[]),d=(0,o.useCallback)(()=>{if(n.current&&!window.confirm(s.current))throw i.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,o.useEffect)(()=>(window.addEventListener("beforeunload",a),i.events.on("routeChangeStart",d),()=>{window.removeEventListener("beforeunload",a),i.events.off("routeChangeStart",d)}),[a,d])}},95389:function(e,t,r){"use strict";r.d(t,{_:function(){return p},b:function(){return H}});var o=r(67294),l=r(19946),i=r(12351),n=r(16723),s=r(23784),a=r(73781);let d=(0,o.createContext)(null);function H(){let[e,t]=(0,o.useState)([]);return[e.length>0?e.join(" "):void 0,(0,o.useMemo)(()=>function(e){let r=(0,a.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),o=r.indexOf(e);return -1!==o&&r.splice(o,1),r}))),l=(0,o.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props}),[r,e.slot,e.name,e.props]);return o.createElement(d.Provider,{value:l},e.children)},[t])]}let p=Object.assign((0,i.yV)(function(e,t){let r=(0,l.M)(),{id:a=`headlessui-label-${r}`,passive:p=!1,...c}=e,m=function u(){let e=(0,o.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),f=(0,s.T)(t);(0,n.e)(()=>m.register(a),[a,m.register]);let x={ref:f,...m.props,id:a};return p&&("onClick"in x&&(delete x.htmlFor,delete x.onClick),"onClick"in c&&delete c.onClick),(0,i.sY)({ourProps:x,theirProps:c,slot:m.slot||{},defaultTag:"label",name:m.name||"Label"})}),{})},77768:function(e,t,r){"use strict";r.d(t,{r:function(){return w}});var o=r(67294),l=r(12351),i=r(19946),n=r(61363),s=r(64103),a=r(95389),d=r(39516),p=r(14157),c=r(23784),m=r(46045),f=r(18689),x=r(73781),v=r(31147),b=r(94192);let h=(0,o.createContext)(null);h.displayName="GroupContext";let g=o.Fragment,w=Object.assign((0,l.yV)(function(e,t){let r=(0,i.M)(),{id:a=`headlessui-switch-${r}`,checked:d,defaultChecked:g=!1,onChange:w,name:y,value:j,form:N,...P}=e,S=(0,o.useContext)(h),k=(0,o.useRef)(null),L=(0,c.T)(k,t,null===S?null:S.setSwitch),[E,Z]=(0,v.q)(d,w,g),T=(0,x.z)(()=>null==Z?void 0:Z(!E)),C=(0,x.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),T()}),R=(0,x.z)(e=>{e.key===n.R.Space?(e.preventDefault(),T()):e.key===n.R.Enter&&(0,f.g)(e.currentTarget)}),U=(0,x.z)(e=>e.preventDefault()),_=(0,o.useMemo)(()=>({checked:E}),[E]),q={id:a,ref:L,role:"switch",type:(0,p.f)(e,k),tabIndex:0,"aria-checked":E,"aria-labelledby":null==S?void 0:S.labelledby,"aria-describedby":null==S?void 0:S.describedby,onClick:C,onKeyUp:R,onKeyPress:U},D=(0,b.G)();return(0,o.useEffect)(()=>{var e;let t=null==(e=k.current)?void 0:e.closest("form");t&&void 0!==g&&D.addEventListener(t,"reset",()=>{Z(g)})},[k,Z]),o.createElement(o.Fragment,null,null!=y&&E&&o.createElement(m._,{features:m.A.Hidden,...(0,l.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:N,checked:E,name:y,value:j})}),(0,l.sY)({ourProps:q,theirProps:P,slot:_,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[r,i]=(0,o.useState)(null),[n,s]=(0,a.b)(),[p,c]=(0,d.f)(),m=(0,o.useMemo)(()=>({switch:r,setSwitch:i,labelledby:n,describedby:p}),[r,i,n,p]);return o.createElement(c,{name:"Switch.Description"},o.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(t=m.switch)?void 0:t.id,onClick(e){r&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),r.click(),r.focus({preventScroll:!0}))}}},o.createElement(h.Provider,{value:m},(0,l.sY)({ourProps:{},theirProps:e,defaultTag:g,name:"Switch.Group"}))))},Label:a._,Description:d.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,2512,9494,5535,8186,1285,1631,5468,9774,2888,179],function(){return e(e.s=19543)}),_N_E=e.O()}]);