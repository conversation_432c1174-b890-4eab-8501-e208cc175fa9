(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8165],{34293:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/authors/[authorSlug]/[action]",function(){return l(55037)}])},97670:function(e,t,l){"use strict";l.r(t);var r=l(85893),i=l(78985),s=l(79362),a=l(8144),n=l(74673),d=l(99494),o=l(5233),u=l(1631),c=l(11163),h=l(48583),x=l(93967),f=l.n(x),p=l(30824),g=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,o.$G)(),[i,a]=(0,h.KO)(s.Hf),{childMenu:n}=t,{width:d}=(0,g.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==n?void 0:n.map(e=>{let{href:t,label:a,icon:n,childMenu:o}=e;return(0,r.jsx)(u.Z,{href:t,label:l(a),icon:n,childMenu:o,miniSidebar:i&&d>=s.h2},a)})})},SideBarGroup=()=>{var e;let{t}=(0,o.$G)(),[l,i]=(0,h.KO)(s.Hf),a=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,n=Object.keys(a),{width:u}=(0,g.Z)();return(0,r.jsx)(r.Fragment,{children:null==n?void 0:n.map((e,i)=>{var n;return(0,r.jsxs)("div",{className:f()("flex flex-col px-5",l&&u>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:f()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&u>=s.h2?"hidden":""),children:t(null===(n=a[e])||void 0===n?void 0:n.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},i)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,c.useRouter)(),[d,o]=(0,h.KO)(s.Hf),[u]=(0,h.KO)(s.GH),[x]=(0,h.KO)(s.W4),{width:m}=(0,g.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(n.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:f()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",m>=s.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-20",d&&m>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:f()("relative flex w-full flex-col justify-start transition-[padding] duration-300",m>=s.h2&&(u||x)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&m>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(a.Z,{})]})]})]})}},55037:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSP:function(){return x},default:function(){return UpdateAuthorPage}});var r=l(85893),i=l(97670),s=l(34926),a=l(45957),n=l(55846),d=l(11163),o=l(5233),u=l(16203),c=l(26654),h=l(93345),x=!0;function UpdateAuthorPage(){let{query:e,locale:t}=(0,d.useRouter)(),{t:l}=(0,o.$G)(),{author:i,loading:u,error:x}=(0,c._H)({slug:e.authorSlug,language:"edit"===e.action.toString()?t:h.Config.defaultLanguage});return u?(0,r.jsx)(n.Z,{text:l("common:text-loading")}):x?(0,r.jsx)(a.Z,{message:x.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex border-b border-dashed border-border-base pb-5 md:pb-7",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:l("form:form-title-update-author")})}),(0,r.jsx)(s.Z,{initialValues:i})]})}UpdateAuthorPage.authenticate={permissions:u.M$},UpdateAuthorPage.Layout=i.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,6994,2512,3261,9494,5535,8186,1285,1631,5468,6491,9774,2888,179],function(){return e(e.s=34293)}),_N_E=e.O()}]);