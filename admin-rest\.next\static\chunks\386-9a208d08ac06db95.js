"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[386],{92072:function(e,t,a){var r=a(85893),o=a(93967),n=a.n(o),s=a(98388);t.Z=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.m6)(n()("rounded bg-light p-5 shadow md:p-8",t)),...a})}},40386:function(e,t,a){a.d(t,{Z:function(){return CreateOrUpdateFaqsForm}});var r=a(85893),o=a(92072);let chatbotAutoSuggestionForFAQs=e=>{let{name:t}=e;return[{id:1,title:"Write a Description for Your Store."},{id:2,title:"Write a description about Your Return and Exchange Policy"},{id:3,title:"Write a description about Shipping Policy Details"},{id:4,title:"Write a description about International Shipping Guide."},{id:5,title:"Write a description about Security and Privacy Measures."},{id:6,title:"Write a description about Instructions for Order Modifications"},{id:7,title:"Write a description about Handle Damaged or Defective Products"},{id:8,title:"Write a description about Craft Customer Support Contact Information"},{id:9,title:"Write a description about Detail Payment Method Information"},{id:10,title:"Write a description about Outline the Order Tracking Process."}]};var n=a(16310),s=a(79362);let i=n.Ry().shape({faq_title:n.Z_().required("form:error-faq-title-required"),faq_description:n.Z_().required("form:error-faq-description-required").max(s.$5,"form:error-description-maximum-title").test({name:"faq_description",skipAbsent:!0,test(e,t){var a,r;return!!((null==e?void 0:null===(r=e.replace(/<(.|\n)*?>/g,""))||void 0===r?void 0:null===(a=r.trim())||void 0===a?void 0:a.length)!==0||(null==e?void 0:e.includes("<img")))||t.createError({message:"form:error-faq-description-required"})}})});var l=a(88762),c=a(60802),d=a(80602),u=a(33e3),m=a(75814),f=a(22220),p=a(60687),g=a(93345),x=a(92001),h=a(90573),b=a(30042),v=a(99930),y=a(16203),q=a(9140),w=a(47533),j=a(5233),N=a(11163),F=a(67294),C=a(87536);function CreateOrUpdateFaqsForm(e){var t,a;let{initialValues:n}=e,s=(0,N.useRouter)(),{t:S}=(0,j.$G)(),{permissions:Z}=(0,y.WA)(),{data:_,isLoading:A,error:k}=(0,v.UE)(),{locale:Q}=s,{settings:{options:M}}=(0,h.n)({language:Q}),{openModal:I}=(0,m.SO)(),{data:E}=(0,b.DZ)({slug:s.query.shop},{enabled:!!s.query.shop}),P=null==E?void 0:E.id,{register:z,handleSubmit:D,control:O,watch:R,setError:L,setValue:T,formState:{errors:W}}=(0,C.cI)({defaultValues:n||{faq_title:"",faq_description:""},resolver:(0,w.X)(i)}),{mutate:G,isLoading:Y}=(0,x.Mf)(),{mutate:U,isLoading:$}=(0,x.Zg)(),B=R("faq_title"),V=(0,F.useMemo)(()=>chatbotAutoSuggestionForFAQs({name:null!=B?B:""}),[B]),J=(0,F.useCallback)(()=>{I("GENERATE_DESCRIPTION",{control:O,name:B,set_value:T,key:"description",suggestion:V})},[B]);s.locale,g.Config.defaultLanguage;let onSubmit=async e=>{let t={language:s.locale,faq_title:e.faq_title,faq_description:e.faq_description};try{n&&n.translated_languages.includes(s.locale)?U({...t,id:n.id,shop_id:n.shop_id}):G({...t,shop_id:P})}catch(t){let e=(0,q.e)(t);Object.keys(null==e?void 0:e.validation).forEach(t=>{L(t.split(".")[1],{type:"manual",message:null==e?void 0:e.validation[t][0]})})}};return(0,r.jsxs)("form",{onSubmit:D(onSubmit),children:[(0,r.jsxs)("div",{className:"my-5 flex flex-wrap sm:my-8",children:[(0,r.jsx)(d.Z,{title:S("form:input-label-description"),details:"".concat(S(n?"form:item-description-edit":"form:item-description-add")," ").concat(S("form:faq-form-info-help-text")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5 "}),(0,r.jsxs)(o.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(u.Z,{label:S("form:input-title"),...z("faq_title"),error:S(null===(t=W.faq_title)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5",required:!0}),(0,r.jsxs)("div",{className:"relative",children:[(null==M?void 0:M.useAi)&&(0,r.jsx)(l.Z,{title:S("form:button-label-description-ai"),onClick:J}),(0,r.jsx)(p.Z,{title:S("form:input-label-description"),required:!0,error:S(null==W?void 0:null===(a=W.faq_description)||void 0===a?void 0:a.message),name:"faq_description",control:O})]})]})]}),(0,r.jsx)(f.Z,{className:"z-0",children:(0,r.jsxs)("div",{className:"text-end",children:[n&&(0,r.jsx)(c.Z,{variant:"outline",onClick:s.back,className:"text-sm me-4 md:text-base",type:"button",children:S("form:button-label-back")}),(0,r.jsx)(c.Z,{loading:Y||$,disabled:Y||$,className:"text-sm md:text-base",children:S(n?"form:button-label-update-faq":"form:button-label-add-faq")})]})})]})}},86779:function(e,t,a){a.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var r=a(85893);let InfoIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,r.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},88762:function(e,t,a){a.d(t,{Z:function(){return OpenAIButton}});var r=a(85893),o=a(93967),n=a.n(o);function OpenAIButton(e){let{className:t,onClick:a,title:o,...s}=e;return(0,r.jsx)("div",{onClick:a,className:n()("absolute right-0 -top-1 z-10 cursor-pointer text-sm font-medium text-accent hover:text-accent-hover",t),...s,children:o})}},80602:function(e,t,a){var r=a(85893);t.Z=e=>{let{title:t,details:a,className:o,...n}=e;return(0,r.jsxs)("div",{className:o,...n,children:[t&&(0,r.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),a&&(0,r.jsx)("p",{className:"text-sm text-body",children:a})]})}},33e3:function(e,t,a){var r=a(85893),o=a(71611),n=a(93967),s=a.n(n),i=a(67294),l=a(98388);let c={small:"text-sm h-10",medium:"h-12",big:"h-14"},d=i.forwardRef((e,t)=>{let{className:a,label:n,note:i,name:d,error:u,children:m,variant:f="normal",dimension:p="medium",shadow:g=!1,type:x="text",inputClassName:h,disabled:b,showLabel:v=!0,required:y,toolTipText:q,labelClassName:w,...j}=e,N=s()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===f,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===f,"border border-border-base focus:border-accent":"outline"===f},{"focus:shadow":g},c[p],h),F="number"===x&&b?"number-disable":"";return(0,r.jsxs)("div",{className:(0,l.m6)(a),children:[v||n?(0,r.jsx)(o.Z,{htmlFor:d,toolTipText:q,label:n,required:y,className:w}):"",(0,r.jsx)("input",{id:d,name:d,type:x,ref:t,className:(0,l.m6)(s()(b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(F," select-none"):"",N)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:b,"aria-invalid":u?"true":"false",...j}),i&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:i}),u&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:u})]})});d.displayName="Input",t.Z=d},23091:function(e,t,a){var r=a(85893),o=a(93967),n=a.n(o),s=a(98388);t.Z=e=>{let{className:t,...a}=e;return(0,r.jsx)("label",{className:(0,s.m6)(n()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...a})}},22220:function(e,t,a){var r=a(85893),o=a(93967),n=a.n(o),s=a(98388);t.Z=e=>{let{children:t,className:a,...o}=e;return(0,r.jsx)("div",{className:(0,s.m6)(n()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",a)),...o,children:t})}},71611:function(e,t,a){var r=a(85893),o=a(86779),n=a(71943),s=a(23091),i=a(98388);t.Z=e=>{let{className:t,required:a,label:l,toolTipText:c,htmlFor:d}=e;return(0,r.jsxs)(s.Z,{className:(0,i.m6)(t),htmlFor:d,children:[l,a?(0,r.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",c?(0,r.jsx)(n.u,{content:c,children:(0,r.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,r.jsx)(o.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,a){a.d(t,{u:function(){return Tooltip}});var r=a(85893),o=a(67294),n=a(93075),s=a(82364),i=a(24750),l=a(93967),c=a.n(l),d=a(67421),u=a(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},f={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:a,gap:l=8,animation:p="zoomIn",placement:g="top",size:x="md",rounded:h="DEFAULT",shadow:b="md",color:v="default",className:y,arrowClassName:q,showArrow:w=!0}=e,[j,N]=(0,o.useState)(!1),F=(0,o.useRef)(null),{t:C}=(0,d.$G)(),{x:S,y:Z,refs:_,strategy:A,context:k}=(0,n.YF)({placement:g,open:j,onOpenChange:N,middleware:[(0,s.x7)({element:F}),(0,s.cv)(l),(0,s.RR)(),(0,s.uY)({padding:8})],whileElementsMounted:i.Me}),{getReferenceProps:Q,getFloatingProps:M}=(0,n.NI)([(0,n.XI)(k),(0,n.KK)(k),(0,n.qs)(k,{role:"tooltip"}),(0,n.bQ)(k)]),{isMounted:I,styles:E}=(0,n.Y_)(k,{duration:{open:150,close:150},...f[p]});return(0,r.jsxs)(r.Fragment,{children:[(0,o.cloneElement)(t,Q({ref:_.setReference,...t.props})),(I||j)&&(0,r.jsx)(n.ll,{children:(0,r.jsxs)("div",{role:"tooltip",ref:_.setFloating,className:(0,u.m6)(c()(m.base,m.size[x],m.rounded[h],m.variant.solid.base,m.variant.solid.color[v],m.shadow[b],y)),style:{position:A,top:null!=Z?Z:0,left:null!=S?S:0,...E},...M(),children:[C("".concat(a)),w&&(0,r.jsx)(n.Y$,{ref:F,context:k,className:c()(m.arrow.color[v],q),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},60687:function(e,t,a){var r=a(85893),o=a(5152),n=a.n(o),s=a(55846),i=a(67294);t.Z=e=>{let{title:t,placeholder:o,control:l,className:c,editorClassName:d,name:u,required:m,disabled:f,error:p,...g}=e,x=(0,i.useMemo)(()=>n()(()=>Promise.all([a.e(5556),a.e(3305),a.e(2937),a.e(939),a.e(6870),a.e(3373)]).then(a.bind(a,97148)),{loadableGenerated:{webpack:()=>[97148]},ssr:!1,loading:()=>(0,r.jsx)("div",{className:"py-8 flex",children:(0,r.jsx)(s.Z,{simple:!0,className:"h-6 w-6 mx-auto"})})}),[]);return(0,r.jsx)(x,{title:t,placeholder:o,control:l,className:c,editorClassName:d,name:u,required:m,disabled:f,error:p,...g})}},92001:function(e,t,a){a.d(t,{Mf:function(){return useCreateFaqsMutation},V7:function(){return useDeleteFaqsMutation},cb:function(){return useFaqQuery},uo:function(){return useFaqsQuery},Zg:function(){return useUpdateFaqsMutation}});var r=a(11163),o=a.n(r),n=a(88767),s=a(22920),i=a(5233),l=a(28597),c=a(97514),d=a(47869),u=a(93345),m=a(55191),f=a(3737);let p={...(0,m.h)(d.P.FAQS),all:function(){let{faq_title:e,shop_id:t,...a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return f.eN.get(d.P.FAQS,{searchJoin:"and",shop_id:t,...a,search:f.eN.formatSearchParams({faq_title:e,shop_id:t})})},get(e){let{id:t,language:a}=e;return f.eN.get("".concat(d.P.FAQS,"/").concat(t),{language:a})},paginated:e=>{let{faq_title:t,shop_id:a,...r}=e;return f.eN.get(d.P.FAQS,{searchJoin:"and",shop_id:a,...r,search:f.eN.formatSearchParams({faq_title:t,shop_id:a})})}},useFaqQuery=e=>{let{id:t,language:a}=e,{data:r,error:o,isLoading:s}=(0,n.useQuery)([d.P.FAQS,{id:t,language:a}],()=>p.get({id:t,language:a}));return{faqs:r,error:o,loading:s}},useFaqsQuery=e=>{var t;let{data:a,error:r,isLoading:o}=(0,n.useQuery)([d.P.FAQS,e],e=>{let{queryKey:t,pageParam:a}=e;return p.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{faqs:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(a),error:r,loading:o}},useCreateFaqsMutation=()=>{let e=(0,n.useQueryClient)(),t=(0,r.useRouter)(),{t:a}=(0,i.$G)();return(0,n.useMutation)(p.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(c.Z.faqs.list):c.Z.faqs.list;await o().push(e,void 0,{locale:u.Config.defaultLanguage}),s.Am.success(a("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(d.P.FAQS)},onError:e=>{var t;s.Am.error(a("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFaqsMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,n.useQueryClient)(),a=(0,r.useRouter)();return(0,n.useMutation)(p.update,{onSuccess:async t=>{let r=a.query.shop?"/".concat(a.query.shop).concat(c.Z.faqs.list):c.Z.faqs.list;await a.push(r,void 0,{locale:u.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(d.P.FAQS)},onError:t=>{var a;s.Am.error(e("common:".concat(null==t?void 0:null===(a=t.response)||void 0===a?void 0:a.data.message)))}})},useDeleteFaqsMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,i.$G)();return(0,n.useMutation)(p.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(d.P.FAQS)},onError:e=>{var a;s.Am.error(t("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}})}},9140:function(e,t,a){a.d(t,{e:function(){return getErrorMessage}});var r=a(11163),o=a.n(r),n=a(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let a of e.graphQLErrors){if(a.extensions&&"validation"===a.extensions.category)return t.message=a.message,t.validation=a.extensions.validation,t;a.extensions&&"authorization"===a.extensions.category&&(n.Z.remove("auth_token"),n.Z.remove("auth_permissions"),o().push("/"))}return t.message=e.message,t}}}]);