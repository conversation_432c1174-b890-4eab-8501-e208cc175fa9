(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4349],{32177:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders/[orderId]",function(){return l(22857)}])},97670:function(e,t,l){"use strict";l.r(t);var s=l(85893),n=l(78985),i=l(79362),r=l(8144),a=l(74673),d=l(99494),o=l(5233),c=l(1631),m=l(11163),x=l(48583),u=l(93967),h=l.n(u),p=l(30824),b=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,o.$G)(),[n,r]=(0,x.KO)(i.Hf),{childMenu:a}=t,{width:d}=(0,b.Z)();return(0,s.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:t,label:r,icon:a,childMenu:o}=e;return(0,s.jsx)(c.Z,{href:t,label:l(r),icon:a,childMenu:o,miniSidebar:n&&d>=i.h2},r)})})},SideBarGroup=()=>{var e;let{t}=(0,o.$G)(),[l,n]=(0,x.KO)(i.Hf),r=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(r),{width:c}=(0,b.Z)();return(0,s.jsx)(s.Fragment,{children:null==a?void 0:a.map((e,n)=>{var a;return(0,s.jsxs)("div",{className:h()("flex flex-col px-5",l&&c>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,s.jsx)("div",{className:h()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&c>=i.h2?"hidden":""),children:t(null===(a=r[e])||void 0===a?void 0:a.label)}),(0,s.jsx)(SidebarItemMap,{menuItems:r[e]})]},n)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,m.useRouter)(),[d,o]=(0,x.KO)(i.Hf),[c]=(0,x.KO)(i.GH),[u]=(0,x.KO)(i.W4),{width:f}=(0,b.Z)();return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,s.jsx)(n.Z,{}),(0,s.jsx)(a.Z,{children:(0,s.jsx)(SideBarGroup,{})}),(0,s.jsxs)("div",{className:"flex flex-1",children:[(0,s.jsx)("aside",{className:h()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",f>=i.h2&&(c||u)?"lg:pt-[8.75rem]":"pt-20",d&&f>=i.h2?"lg:w-24":"lg:w-76"),children:(0,s.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,s.jsx)(p.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,s.jsx)(SideBarGroup,{})})})}),(0,s.jsxs)("main",{className:h()("relative flex w-full flex-col justify-start transition-[padding] duration-300",f>=i.h2&&(c||u)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&f>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,s.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,s.jsx)(r.Z,{})]})]})]})}},22857:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSP:function(){return C},default:function(){return OrderDetailsPage}});var s=l(85893),n=l(92072),i=l(41312),r=l(97670),a=l(85705),d=l(36184),o=l(60802),c=l(45957),m=l(66271),x=l(55846),u=l(28454),h=l(27899),p=l(8657),b=l(51068),f=l(73626),v=l(77556),j=l(99494),g=l(10265),w=l(35841),N=l(95534),y=l(76518),_=l(51640),Z=l(60942),S=l(48583),P=l(5233),k=l(25675),O=l.n(k),I=l(11163),E=l(67294),D=l(87536),F=l(68040),C=!0;function OrderDetailsPage(){var e,t,l,r,k,C,G,L;let{t:K}=(0,P.$G)(),{query:T,locale:H}=(0,I.useRouter)(),{alignLeft:U,alignRight:q,isRTL:z}=(0,y.S)(),{resetCart:A}=(0,b.jD)(),[,B]=(0,S.KO)(p.y9);(0,E.useEffect)(()=>{A(),B()},[A,B]);let{mutate:M,isLoading:R}=(0,f.Oc)(),{order:$,isLoading:V,error:X}=(0,f.OT)({id:T.orderId,language:H}),{refetch:W}=(0,f.qD)({order_id:T.orderId,isRTL:z,language:H},{enabled:!1}),{handleSubmit:J,control:Q,formState:{errors:Y}}=(0,D.cI)({defaultValues:{order_status:null!==(C=null==$?void 0:$.order_status)&&void 0!==C?C:""}}),{price:ee}=(0,Z.ZP)($&&{amount:null==$?void 0:$.amount}),{price:et}=(0,Z.ZP)($&&{amount:null==$?void 0:$.paid_total}),{price:el}=(0,Z.ZP)($&&{amount:null!==(G=null==$?void 0:$.discount)&&void 0!==G?G:0}),{price:es}=(0,Z.ZP)($&&{amount:null==$?void 0:$.delivery_fee}),{price:en}=(0,Z.ZP)($&&{amount:null==$?void 0:$.sales_tax}),{price:ei}=(0,Z.ZP)({amount:null==$?void 0:$.amount}),{price:er}=(0,Z.ZP)({amount:null!==(L=null==$?void 0:$.delivery_fee)&&void 0!==L?L:0}),{price:ea}=(0,Z.ZP)({amount:null==$?void 0:null===(e=$.wallet_point)||void 0===e?void 0:e.amount}),ed=(null==$?void 0:$.payment_status)!==g.bG.SUCCESS?(null==$?void 0:$.paid_total)-(null==$?void 0:null===(t=$.wallet_point)||void 0===t?void 0:t.amount):0,{price:eo}=(0,Z.ZP)({amount:ed});null==$||$.products.reduce(function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,l=arguments.length>1?arguments[1]:void 0;return t+parseInt(null==l?void 0:null===(e=l.pivot)||void 0===e?void 0:e.order_quantity)},0);let ec=(0,F.b)({customer_contact:null==$?void 0:$.customer_contact});if(V)return(0,s.jsx)(x.Z,{text:K("common:text-loading")});if(X)return(0,s.jsx)(c.Z,{message:X.message});async function handleDownloadInvoice(){let{data:e}=await W();if(e){let t=document.createElement("a");t.href=e,t.setAttribute("download","order-invoice"),t.click()}}let em=[{dataIndex:"image",key:"image",width:70,render:e=>{var t;return(0,s.jsx)("div",{className:"relative h-[50px] w-[50px]",children:(0,s.jsx)(O(),{src:null!==(t=null==e?void 0:e.thumbnail)&&void 0!==t?t:j.siteSettings.product.placeholder,alt:"alt text",fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-fill"})})}},{title:K("table:table-item-products"),dataIndex:"name",key:"name",align:U,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{children:e}),(0,s.jsx)("span",{className:"mx-2",children:"x"}),(0,s.jsx)("span",{className:"font-semibold text-heading",children:t.pivot.order_quantity})]})},{title:K("table:table-item-total"),dataIndex:"price",key:"price",align:q,render:function(e,t){let{price:l}=(0,Z.ZP)({amount:parseFloat(t.pivot.subtotal)});return(0,s.jsx)("span",{children:l})}}];return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(n.Z,{className:"relative overflow-hidden",children:[(0,s.jsx)("div",{className:"mb-6 -mt-5 -ml-5 -mr-5 md:-mr-8 md:-ml-8 md:-mt-8",children:(0,s.jsx)(d.Z,{order:$,wrapperClassName:"px-8 py-4"})}),(0,s.jsx)("div",{className:"flex w-full",children:(0,s.jsxs)(o.Z,{onClick:handleDownloadInvoice,className:"mb-5 bg-blue-500 ltr:ml-auto rtl:mr-auto",children:[(0,s.jsx)(i._,{className:"h-4 w-4 me-3"}),K("common:text-download")," ",K("common:text-invoice")]})}),(0,s.jsxs)("div",{className:"flex flex-col items-center lg:flex-row",children:[(0,s.jsxs)("h3",{className:"mb-8 w-full whitespace-nowrap text-center text-2xl font-semibold text-heading lg:mb-0 lg:w-1/3 lg:text-start",children:[K("form:input-label-order-id")," - ",null==$?void 0:$.tracking_number]}),![g.iF.FAILED,g.iF.CANCELLED,g.iF.REFUNDED].includes(null==$?void 0:$.order_status)&&(0,s.jsxs)("form",{onSubmit:J(e=>{let{order_status:t}=e;M({id:null==$?void 0:$.id,order_status:null==t?void 0:t.status})}),className:"flex w-full items-start ms-auto lg:w-2/4",children:[(0,s.jsxs)("div",{className:"z-20 w-full me-5",children:[(0,s.jsx)(u.Z,{name:"order_status",control:Q,getOptionLabel:e=>K(e.name),getOptionValue:e=>e.status,options:_.D.slice(0,6),placeholder:K("form:input-placeholder-order-status")}),(0,s.jsx)(m.Z,{message:K(null==Y?void 0:null===(l=Y.order_status)||void 0===l?void 0:l.message)})]}),(0,s.jsxs)(o.Z,{loading:R,children:[(0,s.jsx)("span",{className:"hidden sm:block",children:K("form:button-label-change-status")}),(0,s.jsx)("span",{className:"block sm:hidden",children:K("form:form:button-label-change")})]})]})]}),(0,s.jsx)("div",{className:"my-5 flex items-center justify-center lg:my-10",children:(0,s.jsx)(a.Z,{orderStatus:null==$?void 0:$.order_status,paymentStatus:null==$?void 0:$.payment_status})}),(0,s.jsxs)("div",{className:"mb-10",children:[$?(0,s.jsx)(h.i,{columns:em,emptyText:()=>(0,s.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,s.jsx)(v.m,{className:"w-52"}),(0,s.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:K("table:empty-table-data")}),(0,s.jsx)("p",{className:"text-[13px]",children:K("table:empty-table-sorry-text")})]}),data:null==$?void 0:$.products,rowKey:"id",scroll:{x:300}}):(0,s.jsx)("span",{children:K("common:no-order-found")}),(null==$?void 0:$.parent_id)?(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2 border-t-4 border-double border-border-200 px-4 py-4 ms-auto sm:w-1/2 md:w-1/3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsx)("span",{children:K("common:order-sub-total")}),(0,s.jsx)("span",{children:ee})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-base font-semibold text-heading",children:[(0,s.jsx)("span",{children:K("common:order-total")}),(0,s.jsx)("span",{children:et})]})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"flex w-full flex-col space-y-2 border-t-4 border-double border-border-200 px-4 py-4 ms-auto sm:w-1/2 md:w-1/3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsx)("span",{children:K("common:order-sub-total")}),(0,s.jsx)("span",{children:ei})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsxs)("span",{children:[" ",K("text-shipping-charge")]}),(0,s.jsx)("span",{children:er})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsxs)("span",{children:[" ",K("text-tax")]}),(0,s.jsx)("span",{children:en})]}),(null==$?void 0:$.discount)>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsx)("span",{children:K("text-discount")}),(0,s.jsx)("span",{children:el})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-base font-semibold text-heading",children:[(0,s.jsxs)("span",{children:[" ",K("text-total")]}),(0,s.jsx)("span",{children:et})]}),(null==$?void 0:null===(r=$.wallet_point)||void 0===r?void 0:r.amount)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-body",children:[(0,s.jsxs)("span",{children:[" ",K("text-paid-from-wallet")]}),(0,s.jsx)("span",{children:ea})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-base font-semibold text-heading",children:[(0,s.jsxs)("span",{children:[" ",K("text-amount-due")]}),(0,s.jsx)("span",{children:eo})]})]})]})})]}),(null==$?void 0:$.note)?(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-12 mb-5 text-xl font-bold text-heading",children:"Purchase Note"}),(0,s.jsx)("div",{className:"mb-12 flex items-start rounded border border-gray-700 bg-gray-100 p-4",children:null==$?void 0:$.note})]}):"",(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between",children:[(0,s.jsxs)("div",{className:"mb-10 w-full sm:mb-0 sm:w-1/2 sm:pe-8",children:[(0,s.jsx)("h3",{className:"mb-3 border-b border-border-200 pb-2 font-semibold text-heading",children:K("text-order-details")}),(0,s.jsxs)("div",{className:"flex flex-col items-start space-y-1 text-sm text-body",children:[(0,s.jsx)("span",{children:(0,N.U)(null==$?void 0:null===(k=$.products)||void 0===k?void 0:k.length,K("text-item"))}),(0,s.jsx)("span",{children:null==$?void 0:$.delivery_time}),(0,s.jsx)("span",{children:"".concat(K("text-payment-method"),":  ").concat(null==$?void 0:$.payment_gateway)})]})]}),(0,s.jsxs)("div",{className:"mb-10 w-full sm:mb-0 sm:w-1/2 sm:pe-8",children:[(0,s.jsx)("h3",{className:"mb-3 border-b border-border-200 pb-2 font-semibold text-heading",children:K("common:billing-address")}),(0,s.jsxs)("div",{className:"flex flex-col items-start space-y-1 text-sm text-body",children:[(0,s.jsx)("span",{children:null==$?void 0:$.customer_name}),(null==$?void 0:$.billing_address)&&(0,s.jsx)("span",{children:(0,w.T)($.billing_address)}),(null==$?void 0:$.customer_contact)&&(0,s.jsx)("span",{children:ec})]})]}),(0,s.jsxs)("div",{className:"w-full sm:w-1/2 sm:ps-8",children:[(0,s.jsx)("h3",{className:"mb-3 border-b border-border-200 pb-2 font-semibold text-heading text-start sm:text-end",children:K("common:shipping-address")}),(0,s.jsxs)("div",{className:"flex flex-col items-start space-y-1 text-sm text-body text-start sm:items-end sm:text-end",children:[(0,s.jsx)("span",{children:null==$?void 0:$.customer_name}),(null==$?void 0:$.shipping_address)&&(0,s.jsx)("span",{children:(0,w.T)($.shipping_address)}),(null==$?void 0:$.customer_contact)&&(0,s.jsx)("span",{children:ec})]})]})]})]})})}OrderDetailsPage.Layout=r.default},95534:function(e,t,l){"use strict";function formatString(e,t){return e&&e>1?"".concat(e," ").concat(t,"s"):"".concat(e," ").concat(t)}l.d(t,{U:function(){return formatString}})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,9709,1247,9494,5535,8186,1285,1631,7556,6779,9774,2888,179],function(){return e(e.s=32177)}),_N_E=e.O()}]);