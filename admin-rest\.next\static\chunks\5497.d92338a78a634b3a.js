"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5497],{65497:function(e,t,a){a.r(t);var n=a(85893),u=a(1587),l=a(60802),c=a(75814),d=a(5233),S=a(60385);t.default=()=>{let{t:e}=(0,d.$G)(),{mutate:t,isLoading:a}=(0,S.mw)(),{data:h}=(0,c.X9)(),{closeModal:f}=(0,c.SO)();return(0,n.jsx)(u.l,{onSubmit:function(){t({id:h}),f()},children:e=>{let{register:t,formState:{errors:u}}=e;return(0,n.jsxs)("div",{className:"m-auto flex w-full max-w-sm flex-col rounded bg-light p-5 sm:w-[24rem]",children:[(0,n.jsx)("h2",{className:"mb-4 text-lg font-semibold text-muted-black",children:"Do you want to approve this ?"}),(0,n.jsx)("span",{className:"text-md font-regular mb-4 text-muted-black",children:"If you approve this request, then the products listed here will be join in the flash sale campaign."}),(0,n.jsx)("div",{children:(0,n.jsx)(l.Z,{type:"submit",loading:a,disabled:a,children:"Approve."})})]})}})}},1587:function(e,t,a){a.d(t,{l:function(){return Form}});var n=a(85893),u=a(87536),l=a(47533),c=a(67294);let Form=e=>{let{onSubmit:t,children:a,options:d,validationSchema:S,serverError:h,resetValues:f,...p}=e,m=(0,u.cI)({...!!S&&{resolver:(0,l.X)(S)},...!!d&&d});return(0,c.useEffect)(()=>{h&&Object.entries(h).forEach(e=>{let[t,a]=e;m.setError(t,{type:"manual",message:a})})},[h,m]),(0,c.useEffect)(()=>{f&&m.reset(f)},[f,m]),(0,n.jsx)("form",{onSubmit:m.handleSubmit(t),noValidate:!0,...p,children:a(m)})}},60385:function(e,t,a){a.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var n=a(11163),u=a.n(n),l=a(88767),c=a(22920),d=a(5233),S=a(28597),h=a(97514),f=a(47869),p=a(93345),m=a(55191),v=a(3737);let F={...(0,m.h)(f.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return v.eN.get(f.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...a,search:v.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:a,shop_id:n}=e;return v.eN.get("".concat(f.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:a,shop_id:n,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:a,...n}=e;return v.eN.get(f.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:a,...n,search:v.eN.formatSearchParams({title:t,shop_id:a})})},approve:e=>v.eN.post(f.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>v.eN.post(f.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...a}=e;return v.eN.get(f.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...a,search:v.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:a,error:n,isLoading:u}=(0,l.useQuery)([f.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:a}=e;return F.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(a),error:n,loading:u}},useRequestedListForFlashSale=e=>{let{id:t,language:a,shop_id:n}=e,{data:u,error:c,isLoading:d}=(0,l.useQuery)([f.P.FLASH_SALE,{id:t,language:a,shop_id:n}],()=>F.get({id:t,language:a,shop_id:n}));return{flashSaleRequest:u,error:c,loading:d}},useRequestedProductsForFlashSale=e=>{var t;let{data:a,error:n,isLoading:u}=(0,l.useQuery)([f.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:a}=e;return F.requestedProducts(Object.assign({},t[1],a))},{keepPreviousData:!0});return{products:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,S.Q)(a),error:n,loading:u}},useCreateFlashSaleRequestMutation=()=>{let e=(0,l.useQueryClient)(),t=(0,n.useRouter)(),{t:a}=(0,d.$G)();return(0,l.useMutation)(F.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(h.Z.vendorRequestForFlashSale.list):h.Z.vendorRequestForFlashSale.list;await u().push(e,void 0,{locale:p.Config.defaultLanguage}),c.Am.success(a("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(f.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;c.Am.error(a("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,d.$G)(),t=(0,l.useQueryClient)(),a=(0,n.useRouter)();return(0,l.useMutation)(F.update,{onSuccess:async t=>{let n=a.query.shop?"/".concat(a.query.shop).concat(h.Z.vendorRequestForFlashSale.list):h.Z.vendorRequestForFlashSale.list;await a.push(n,void 0,{locale:p.Config.defaultLanguage}),c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(f.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var a;c.Am.error(e("common:".concat(null==t?void 0:null===(a=t.response)||void 0===a?void 0:a.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,d.$G)();return(0,l.useMutation)(F.delete,{onSuccess:()=>{c.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(f.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var a;c.Am.error(t("common:".concat(null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,d.$G)(),t=(0,l.useQueryClient)(),a=(0,n.useRouter)();return(0,l.useMutation)(F.approve,{onSuccess:async()=>{let t=a.query.shop?"/".concat(a.query.shop).concat(h.Z.vendorRequestForFlashSale.list):h.Z.vendorRequestForFlashSale.list;await u().push(t,void 0,{locale:p.Config.defaultLanguage}),c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(f.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,d.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(F.disapprove,{onSuccess:()=>{c.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(f.P.FLASH_SALE)}})}},47533:function(e,t,a){a.d(t,{X:function(){return o}});var n=a(87536);let s=(e,t,a)=>{if(e&&"reportValidity"in e){let u=(0,n.U2)(a,t);e.setCustomValidity(u&&u.message||""),e.reportValidity()}},resolvers_o=(e,t)=>{for(let a in t.fields){let n=t.fields[a];n&&n.ref&&"reportValidity"in n.ref?s(n.ref,a,e):n.refs&&n.refs.forEach(t=>s(t,a,e))}},r=(e,t)=>{t.shouldUseNativeValidation&&resolvers_o(e,t);let a={};for(let u in e){let l=(0,n.U2)(t.fields,u),c=Object.assign(e[u]||{},{ref:l&&l.ref});if(i(t.names||Object.keys(e),u)){let e=Object.assign({},(0,n.U2)(a,u));(0,n.t8)(e,"root",c),(0,n.t8)(a,u,e)}else(0,n.t8)(a,u,c)}return a},i=(e,t)=>e.some(e=>e.startsWith(t+"."));function o(e,t,a){return void 0===t&&(t={}),void 0===a&&(a={}),function(u,l,c){try{return Promise.resolve(function(n,d){try{var S=(t.context,Promise.resolve(e["sync"===a.mode?"validateSync":"validate"](u,Object.assign({abortEarly:!1},t,{context:l}))).then(function(e){return c.shouldUseNativeValidation&&resolvers_o({},c),{values:a.raw?u:e,errors:{}}}))}catch(e){return d(e)}return S&&S.then?S.then(void 0,d):S}(0,function(e){var t;if(!e.inner)throw e;return{values:{},errors:r((t=!c.shouldUseNativeValidation&&"all"===c.criteriaMode,(e.inner||[]).reduce(function(e,a){if(e[a.path]||(e[a.path]={message:a.message,type:a.type}),t){var u=e[a.path].types,l=u&&u[a.type];e[a.path]=(0,n.KN)(a.path,t,e,a.type,l?[].concat(l,a.message):a.message)}return e},{})),c)}}))}catch(e){return Promise.reject(e)}}}}}]);