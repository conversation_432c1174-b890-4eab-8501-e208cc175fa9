"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[948],{948:function(e,n,r){r.r(n);var t=r(85893),s=r(71421),u=r(75814),a=r(76240);n.default=()=>{let{mutate:e,isLoading:n}=(0,a.wA)(),{data:r}=(0,u.X9)(),{closeModal:o}=(0,u.SO)();return(0,t.jsx)(s.Z,{onCancel:o,onDelete:function(){e({id:r}),o()},deleteBtnLoading:n})}},76240:function(e,n,r){r.d(n,{wA:function(){return useDeleteOwnerTransferMutation},OM:function(){return useOwnerShipTransferQuery},il:function(){return useOwnerShipTransfersQuery},so:function(){return useUpdateOwnerTransferMutation}});var t=r(11163),s=r(88767),u=r(22920),a=r(5233),o=r(28597),i=r(97514),l=r(47869),c=r(93345),d=r(55191),R=r(3737);let p={...(0,d.h)(l.P.OWNERSHIP_TRANSFER),all:function(){let{transaction_identifier:e,shop_id:n,...r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return R.eN.get(l.P.OWNERSHIP_TRANSFER,{searchJoin:"and",shop_id:n,...r})},get(e){let{transaction_identifier:n,language:r,shop_id:t,request_view_type:s}=e;return R.eN.get("".concat(l.P.OWNERSHIP_TRANSFER,"/").concat(n),{language:r,shop_id:t,transaction_identifier:n,request_view_type:s})},paginated:e=>{let{transaction_identifier:n,shop_id:r,...t}=e;return R.eN.get(l.P.OWNERSHIP_TRANSFER,{searchJoin:"and",shop_id:r,...t,search:R.eN.formatSearchParams({transaction_identifier:n})})},approve:e=>R.eN.post(l.P.OWNERSHIP_TRANSFER,e),disapprove:e=>R.eN.post(l.P.OWNERSHIP_TRANSFER,e)},useOwnerShipTransferQuery=e=>{let{transaction_identifier:n,language:r,shop_id:t,request_view_type:u}=e,{data:a,error:o,isLoading:i,refetch:c}=(0,s.useQuery)([l.P.OWNERSHIP_TRANSFER,{transaction_identifier:n,language:r,shop_id:t}],()=>p.get({transaction_identifier:n,language:r,shop_id:t,request_view_type:u}));return{ownershipTransfer:a,error:o,loading:i,refetch:c}},useOwnerShipTransfersQuery=e=>{var n;let{data:r,error:t,isLoading:u}=(0,s.useQuery)([l.P.OWNERSHIP_TRANSFER,e],e=>{let{queryKey:n,pageParam:r}=e;return p.paginated(Object.assign({},n[1],r))},{keepPreviousData:!0});return{ownershipTransfer:null!==(n=null==r?void 0:r.data)&&void 0!==n?n:[],paginatorInfo:(0,o.Q)(r),error:t,loading:u}},useUpdateOwnerTransferMutation=()=>{let{t:e}=(0,a.$G)(),n=(0,s.useQueryClient)(),r=(0,t.useRouter)();return(0,s.useMutation)(p.update,{onSuccess:async n=>{let t=r.query.shop?"/".concat(r.query.shop).concat(i.Z.ownershipTransferRequest.list):i.Z.ownershipTransferRequest.list;await r.push(t,void 0,{locale:c.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(l.P.OWNERSHIP_TRANSFER)},onError:n=>{var r;u.Am.error(e("common:".concat(null==n?void 0:null===(r=n.response)||void 0===r?void 0:r.data.message)))}})},useDeleteOwnerTransferMutation=()=>{let e=(0,s.useQueryClient)(),{t:n}=(0,a.$G)();return(0,s.useMutation)(p.delete,{onSuccess:()=>{u.Am.success(n("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.OWNERSHIP_TRANSFER)},onError:e=>{var r;u.Am.error(n("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})}}}]);