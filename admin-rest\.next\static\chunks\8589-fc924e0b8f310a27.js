"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8589],{92072:function(e,t,s){var r=s(85893),n=s(93967),a=s.n(n),l=s(98388);t.Z=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.m6)(a()("rounded bg-light p-5 shadow md:p-8",t)),...s})}},35484:function(e,t,s){var r=s(85893),n=s(93967),a=s.n(n),l=s(98388);t.Z=e=>{let{title:t,className:s,...n}=e;return(0,r.jsx)("h2",{className:(0,l.m6)(a()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",s)),...n,children:t})}},37912:function(e,t,s){var r=s(85893),n=s(5114),a=s(80287),l=s(93967),o=s.n(l),i=s(67294),u=s(87536),c=s(5233),d=s(98388);t.Z=e=>{let{className:t,onSearch:s,variant:l="outline",shadow:h=!1,inputClassName:p,placeholderText:m,...v}=e,{register:S,handleSubmit:x,watch:f,reset:A,formState:{errors:g}}=(0,u.cI)({defaultValues:{searchText:""}}),w=f("searchText"),{t:b}=(0,c.$G)();(0,i.useEffect)(()=>{w||s({searchText:""})},[w]);let _=o()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===l,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===l,"border border-border-base focus:border-accent":"outline"===l},{"focus:shadow":h},p);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(o()("relative flex w-full items-center",t)),onSubmit:x(s),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:b("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(a.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...S("searchText"),className:(0,d.m6)(_),placeholder:null!=m?m:b("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...v}),g.searchText&&(0,r.jsx)("p",{children:g.searchText.message}),!!w&&(0,r.jsx)("button",{type:"button",onClick:function(){A(),s({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(n.T,{className:"h-5 w-5"})})]})}},32032:function(e,t,s){var r=s(85893),n=s(804),a=s(77556),l=s(18230),o=s(27899),i=s(78998),u=s(10265),c=s(76518),d=s(27484),h=s.n(d),p=s(84110),m=s.n(p),v=s(29387),S=s.n(v),x=s(70178),f=s.n(x),A=s(5233),g=s(11163),w=s(67294),b=s(16203);h().extend(m()),h().extend(f()),h().extend(S()),t.Z=e=>{let{flashSaleRequests:t,paginatorInfo:s,onPagination:d,onSort:h,onOrder:p}=e,{t:m}=(0,A.$G)();(0,g.useRouter)();let{alignLeft:v,alignRight:S}=(0,c.S)(),[x,f]=(0,w.useState)({sort:null===u.As||void 0===u.As?void 0:u.As.Desc,column:null}),{permissions:_}=(0,b.WA)(),F=(0,b.Ft)(b.M$,_),{query:{shop:R}}=(0,g.useRouter)(),onHeaderClick=e=>({onClick:()=>{h(e=>e===(null===u.As||void 0===u.As?void 0:u.As.Desc)?null===u.As||void 0===u.As?void 0:u.As.Asc:null===u.As||void 0===u.As?void 0:u.As.Desc),p(e),f({sort:(null==x?void 0:x.sort)===(null===u.As||void 0===u.As?void 0:u.As.Desc)?null===u.As||void 0===u.As?void 0:u.As.Asc:null===u.As||void 0===u.As?void 0:u.As.Desc,column:e})}}),E=[{title:(0,r.jsx)(i.Z,{title:m("table:table-item-id"),ascending:x.sort===u.As.Asc&&"id"===x.column,isActive:"id"===x.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:v,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(m("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(i.Z,{title:"Campaign Name",ascending:(null==x?void 0:x.sort)===(null===u.As||void 0===u.As?void 0:u.As.Asc)&&(null==x?void 0:x.column)==="title",isActive:(null==x?void 0:x.column)==="title"}),className:"cursor-pointer",dataIndex:"title",key:"title",align:v,ellipsis:!0,width:200,onHeaderCell:()=>onHeaderClick("title"),render:e=>(0,r.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,r.jsx)(i.Z,{title:m("table:table-item-description"),ascending:(null==x?void 0:x.sort)===(null===u.As||void 0===u.As?void 0:u.As.Asc)&&(null==x?void 0:x.column)==="description",isActive:(null==x?void 0:x.column)==="description"}),className:"cursor-pointer",dataIndex:"note",key:"note",align:v,width:500,ellipsis:!0,onHeaderCell:()=>onHeaderClick("description"),render:e=>(0,r.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:"Requested Status",className:"cursor-pointer",dataIndex:"request_status",key:"request_status",align:v,ellipsis:!0,width:200,render:e=>(0,r.jsx)(r.Fragment,{children:e?(0,r.jsx)("span",{className:"whitespace-nowrap",children:"Accepted"}):(0,r.jsx)("span",{className:"whitespace-nowrap",children:"Pending"})})},{title:m("table:table-item-details"),dataIndex:"id",key:"actions",align:"center",width:150,render:(e,t)=>{let{request_status:s}=t;return(0,r.jsx)(n.Z,{id:e,detailsUrl:R?"/".concat(R,"/flash-sale/vendor-request/").concat(e):"/flash-sale/vendor-request/".concat(e),flashSaleVendorRequestApproveButton:F,isFlashSaleVendorRequestApproved:s})}},{title:m("table:table-item-actions"),dataIndex:"id",key:"actions",align:S,width:150,render:(e,t)=>(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n.Z,{id:e,deleteModalView:"DELETE_FLASH_SALE_REQUEST"})})}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,r.jsx)(o.i,{columns:E,emptyText:()=>(0,r.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,r.jsx)(a.m,{className:"w-52"}),(0,r.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:m("table:empty-table-data")}),(0,r.jsx)("p",{className:"text-[13px]",children:m("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==s?void 0:s.total)&&(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(l.Z,{total:s.total,current:s.currentPage,pageSize:s.perPage,onChange:d})})]})}},14713:function(e,t,s){s.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=s(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},18230:function(e,t,s){s.d(t,{Z:function(){return pagination}});var r=s(85893),n=s(55891),a=s(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};s(5871);var pagination=e=>(0,r.jsx)(n.Z,{nextIcon:(0,r.jsx)(a.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},78998:function(e,t,s){s.d(t,{Z:function(){return title_with_sort}});var r=s(85893),n=s(93967),a=s.n(n);s(67294);let TriangleArrowDown=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,r.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,r.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:t,ascending:s,isActive:n=!0,className:l}=e;return(0,r.jsxs)("span",{className:a()("inline-flex items-center",l),children:[(0,r.jsx)("span",{title:"Sort by ".concat(t),children:t}),s?(0,r.jsx)(TriangleArrowUp,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":n})}):(0,r.jsx)(TriangleArrowDown,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":n})})]})}},60385:function(e,t,s){s.d(t,{mw:function(){return useApproveVendorFlashSaleRequestMutation},x0:function(){return useCreateFlashSaleRequestMutation},Hk:function(){return useDeleteFlashSaleRequestMutation},f0:function(){return useDisApproveVendorFlashSaleRequestMutation},n1:function(){return useRequestedListForFlashSale},K5:function(){return useRequestedListsForFlashSale},j4:function(){return useRequestedProductsForFlashSale},O5:function(){return useUpdateFlashSaleRequestMutation}});var r=s(11163),n=s.n(r),a=s(88767),l=s(22920),o=s(5233),i=s(28597),u=s(97514),c=s(47869),d=s(93345),h=s(55191),p=s(3737);let m={...(0,h.h)(c.P.REQUEST_LISTS_FOR_FLASH_SALE),all:function(){let{title:e,shop_id:t,...s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return p.eN.get(c.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:t,...s,search:p.eN.formatSearchParams({title:e,shop_id:t})})},get(e){let{id:t,language:s,shop_id:r}=e;return p.eN.get("".concat(c.P.REQUEST_LISTS_FOR_FLASH_SALE,"/").concat(t),{language:s,shop_id:r,id:t,with:"flash_sale;products"})},paginated:e=>{let{title:t,shop_id:s,...r}=e;return p.eN.get(c.P.REQUEST_LISTS_FOR_FLASH_SALE,{searchJoin:"and",shop_id:s,...r,search:p.eN.formatSearchParams({title:t,shop_id:s})})},approve:e=>p.eN.post(c.P.APPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),disapprove:e=>p.eN.post(c.P.DISAPPROVE_FLASH_SALE_REQUESTED_PRODUCTS,e),requestedProducts(e){let{name:t,...s}=e;return p.eN.get(c.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,{searchJoin:"and",...s,search:p.eN.formatSearchParams({name:t})})}},useRequestedListsForFlashSale=e=>{var t;let{data:s,error:r,isLoading:n}=(0,a.useQuery)([c.P.REQUEST_LISTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return m.paginated(Object.assign({},t[1],s))},{keepPreviousData:!0});return{flashSaleRequests:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(s),error:r,loading:n}},useRequestedListForFlashSale=e=>{let{id:t,language:s,shop_id:r}=e,{data:n,error:l,isLoading:o}=(0,a.useQuery)([c.P.FLASH_SALE,{id:t,language:s,shop_id:r}],()=>m.get({id:t,language:s,shop_id:r}));return{flashSaleRequest:n,error:l,loading:o}},useRequestedProductsForFlashSale=e=>{var t;let{data:s,error:r,isLoading:n}=(0,a.useQuery)([c.P.REQUESTED_PRODUCTS_FOR_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:s}=e;return m.requestedProducts(Object.assign({},t[1],s))},{keepPreviousData:!0});return{products:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,i.Q)(s),error:r,loading:n}},useCreateFlashSaleRequestMutation=()=>{let e=(0,a.useQueryClient)(),t=(0,r.useRouter)(),{t:s}=(0,o.$G)();return(0,a.useMutation)(m.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(u.Z.vendorRequestForFlashSale.list):u.Z.vendorRequestForFlashSale.list;await n().push(e,void 0,{locale:d.Config.defaultLanguage}),l.Am.success(s("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var t;l.Am.error(s("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)(),s=(0,r.useRouter)();return(0,a.useMutation)(m.update,{onSuccess:async t=>{let r=s.query.shop?"/".concat(s.query.shop).concat(u.Z.vendorRequestForFlashSale.list):u.Z.vendorRequestForFlashSale.list;await s.push(r,void 0,{locale:d.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:t=>{var s;l.Am.error(e("common:".concat(null==t?void 0:null===(s=t.response)||void 0===s?void 0:s.data.message)))}})},useDeleteFlashSaleRequestMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,o.$G)();return(0,a.useMutation)(m.delete,{onSuccess:()=>{l.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.REQUEST_LISTS_FOR_FLASH_SALE)},onError:e=>{var s;l.Am.error(t("common:".concat(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data.message)))}})},useApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)(),s=(0,r.useRouter)();return(0,a.useMutation)(m.approve,{onSuccess:async()=>{let t=s.query.shop?"/".concat(s.query.shop).concat(u.Z.vendorRequestForFlashSale.list):u.Z.vendorRequestForFlashSale.list;await n().push(t,void 0,{locale:d.Config.defaultLanguage}),l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)}})},useDisApproveVendorFlashSaleRequestMutation=()=>{let{t:e}=(0,o.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(m.disapprove,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.FLASH_SALE)}})}}}]);