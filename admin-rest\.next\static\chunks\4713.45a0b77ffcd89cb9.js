(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4713,9930],{81763:function(e,t,s){var n=s(44239),r=s(37005);e.exports=function(e){return"number"==typeof e||r(e)&&"[object Number]"==n(e)}},74713:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return shops}});var n=s(85893),r=s(45957),o=s(55846),i=s(5233),l=s(99930),a=s(25675),u=s.n(a),c=s(8152),d=s(93967),m=s.n(d),h=s(35841),f=s(68040),v=s(8366),g=s(81763),x=s.n(g),p=s(73076),S=s(10718);let ListItem=e=>{let{title:t,info:s}=e;return(0,n.jsxs)(n.Fragment,{children:[x()(s)?(0,n.jsx)("p",{className:"text-sm font-semibold text-muted-black",children:Number(s)}):"",t?(0,n.jsx)("p",{className:"text-xs text-[#666]",children:t}):""]})};var shop_card=e=>{var t,s,r,o,l,a;let{shop:d}=e,{t:g}=(0,i.$G)(),x=(0,f.b)({customer_contact:null==d?void 0:null===(t=d.settings)||void 0===t?void 0:t.contact});return(0,n.jsxs)(c.Z,{href:"/".concat(null==d?void 0:d.slug),className:"overflow-hidden rounded-lg bg-white",children:[(0,n.jsx)("div",{className:m()("relative flex h-22 justify-end overflow-hidden"),children:(0,n.jsx)(u(),{alt:null==d?void 0:d.name,src:"/topographic.svg",width:350,height:88,sizes:"(max-width: 768px) 100vw",className:"h-auto w-auto object-contain"})}),(0,n.jsxs)("div",{className:"relative z-10 -mt-[4.25rem] ml-6 flex flex-wrap items-center gap-3",children:[(0,n.jsx)(p.Z,{is_active:null==d?void 0:d.is_active,name:null==d?void 0:d.name,logo:null==d?void 0:d.logo}),(0,n.jsxs)("div",{className:"relative max-w-[calc(100%-104px)] flex-auto pr-4 pt-2",children:[(null==d?void 0:d.name)?(0,n.jsx)("h3",{className:"text-base font-medium leading-none text-muted-black",children:null==d?void 0:d.name}):"",(0,n.jsxs)("div",{className:"mt-2 flex w-11/12 items-center gap-1 text-xs leading-none",children:[(0,n.jsx)(v.tE,{className:"shrink-0 text-[#666666]"}),(0,n.jsx)("p",{className:"truncate text-base-dark",children:(0,h.T)(null==d?void 0:d.address)?(0,h.T)(null==d?void 0:d.address):"???"})]}),(0,n.jsxs)("div",{className:"mt-2 flex w-11/12 items-center gap-1 text-xs leading-none",children:[(0,n.jsx)(S.y5,{className:"shrink-0 text-[#666666]"}),(0,n.jsx)("p",{className:"truncate text-xs text-base-dark",children:null!=x?x:"???"})]})]})]}),(0,n.jsxs)("ul",{className:"mt-4 grid grid-cols-4 divide-x divide-[#E7E7E7] px-2 pb-7 text-center",children:[(0,n.jsx)("li",{children:(0,n.jsx)(ListItem,{title:g("text-title-commission"),info:null!==(a=null==d?void 0:null===(s=d.balance)||void 0===s?void 0:s.admin_commission_rate)&&void 0!==a?a:0})}),(0,n.jsx)("li",{children:(0,n.jsx)(ListItem,{title:g("text-title-sale"),info:null==d?void 0:null===(r=d.balance)||void 0===r?void 0:r.total_earnings})}),(0,n.jsx)("li",{children:(0,n.jsx)(ListItem,{title:g("text-title-balance"),info:null==d?void 0:null===(o=d.balance)||void 0===o?void 0:o.current_balance})}),(0,n.jsx)("li",{children:(0,n.jsx)(ListItem,{title:g("text-title-withdraw"),info:null==d?void 0:null===(l=d.balance)||void 0===l?void 0:l.withdrawn_amount})})]})]})},P=s(16203),w=s(831),M=s(41609),E=s.n(M),shops=()=>{var e,t;let{t:s}=(0,i.$G)(),{data:a,isLoading:u,error:c}=(0,l.UE)(),{permissions:d}=(0,P.WA)(),m=(0,P.Ft)(P.M$,d);return u?(0,n.jsx)(o.Z,{text:s("common:text-loading")}):c?(0,n.jsx)(r.Z,{message:c.message}):(0,n.jsxs)(n.Fragment,{children:[m?(0,n.jsx)("div",{className:"mb-5 border-b border-dashed border-border-base pb-5 md:mb-8 md:pb-7 ",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:s("common:sidebar-nav-item-my-shops")})}):"",E()(null==a?void 0:a.shops)?"":(0,n.jsx)("div",{className:"grid grid-cols-1 gap-5 md:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4",children:null==a?void 0:null===(e=a.shops)||void 0===e?void 0:e.map((e,t)=>(0,n.jsx)(shop_card,{shop:e},t))}),(null==a?void 0:a.managed_shop)||(null==a?void 0:null===(t=a.shops)||void 0===t?void 0:t.length)?null:(0,n.jsx)(w.Z,{image:"/no-shop-found.svg",text:"text-no-shop-found",className:"mx-auto w-7/12"}),(null==a?void 0:a.managed_shop)?(0,n.jsx)("div",{className:"grid grid-cols-1 gap-5 md:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4",children:(0,n.jsx)(shop_card,{shop:null==a?void 0:a.managed_shop})}):null]})}},8366:function(e,t,s){"use strict";s.d(t,{$t:function(){return MapPin},f_:function(){return MapPinIconWithPlatform},tE:function(){return MapPinIcon}});var n=s(85893);let MapPin=e=>{let{...t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",...t,children:(0,n.jsx)("path",{d:"M256 0C161.896 0 85.333 76.563 85.333 170.667c0 28.25 7.063 56.26 20.49 81.104L246.667 506.5c1.875 3.396 5.448 5.5 9.333 5.5s7.458-2.104 9.333-5.5l140.896-254.813c13.375-24.76 20.438-52.771 20.438-81.021C426.667 76.563 350.104 0 256 0zm0 256c-47.052 0-85.333-38.281-85.333-85.333S208.948 85.334 256 85.334s85.333 38.281 85.333 85.333S303.052 256 256 256z",fill:"currentColor"})})},MapPinIcon=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("path",{opacity:.2,d:"M8 1.5a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M8 4a2.5 2.5 0 100 5 2.5 2.5 0 000-5zm0 4a1.5 1.5 0 110-3 1.5 1.5 0 010 3zm0-7a5.506 5.506 0 00-5.5 5.5c0 1.963.907 4.043 2.625 6.016.772.891 1.64 1.694 2.59 2.393a.5.5 0 00.574 0c.948-.7 1.816-1.502 2.586-2.393C12.591 10.543 13.5 8.463 13.5 6.5A5.506 5.506 0 008 1zm0 12.875c-1.033-.813-4.5-3.797-4.5-7.375a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375z",fill:"currentColor"})]}),MapPinIconWithPlatform=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("path",{opacity:.2,d:"M8 2a5 5 0 00-5 5c0 4.5 5 8 5 8s5-3.5 5-8a5 5 0 00-5-5zm0 7a2 2 0 110-4 2 2 0 010 4z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M12.5 14.5H9.409c.519-.464 1.009-.96 1.466-1.484C12.591 11.043 13.5 8.963 13.5 7a5.5 5.5 0 00-11 0c0 1.963.907 4.043 2.625 6.016.457.525.947 1.02 1.466 1.484H3.5a.5.5 0 000 1h9a.5.5 0 000-1zM3.5 7a4.5 4.5 0 019 0c0 3.577-3.467 6.563-4.5 7.375C6.967 13.562 3.5 10.577 3.5 7zm7 0a2.5 2.5 0 10-5 0 2.5 2.5 0 005 0zm-4 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z",fill:"currentColor"})]})},10718:function(e,t,s){"use strict";s.d(t,{SQ:function(){return PhoneIconNew},y5:function(){return PhoneOutlineIcon}});var n=s(85893);let PhoneOutlineIcon=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("path",{opacity:.2,d:"M13.996 10.88A3.022 3.022 0 0111 13.5 8.5 8.5 0 012.5 5a3.02 3.02 0 012.62-2.996.5.5 0 01.519.3l1.32 2.95a.5.5 0 01-.04.47L5.581 7.312a.496.496 0 00-.033.489c.517 1.058 1.61 2.138 2.672 2.65a.494.494 0 00.489-.037l1.563-1.33a.5.5 0 01.474-.044l2.947 1.32a.5.5 0 01.302.52z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M13.898 9.904l-2.944-1.32-.008-.003a1 1 0 00-.995.122L8.429 10c-.963-.468-1.958-1.456-2.426-2.407L7.3 6.05a1 1 0 00.118-.99v-.007l-1.323-2.95a1 1 0 00-1.038-.594A3.516 3.516 0 002 5c0 4.963 4.038 9 9 9a3.516 3.516 0 003.492-3.057 1 1 0 00-.594-1.04zM11 13a8.009 8.009 0 01-8-8 2.512 2.512 0 012.18-2.5v.007l1.312 2.938L5.2 6.991a1 1 0 00-.098 1.03c.566 1.158 1.733 2.316 2.904 2.882a1 1 0 001.03-.107l1.52-1.296 2.937 1.316h.007A2.513 2.513 0 0111 13z",fill:"currentColor"})]}),PhoneIconNew=e=>(0,n.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,n.jsx)("path",{opacity:.2,d:"M13.996 11.38A3.022 3.022 0 0111 14a8.5 8.5 0 01-8.5-8.5 3.02 3.02 0 012.62-2.996.5.5 0 01.519.3l1.32 2.95a.5.5 0 01-.04.47L5.581 7.812a.496.496 0 00-.033.489c.517 1.058 1.61 2.138 2.672 2.65a.494.494 0 00.489-.037l1.563-1.33a.5.5 0 01.474-.044l2.947 1.32a.5.5 0 01.302.52z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M13.898 10.404l-2.944-1.32-.008-.003a1 1 0 00-.995.122L8.429 10.5c-.963-.468-1.958-1.456-2.426-2.407L7.3 6.55a1 1 0 00.118-.99v-.007l-1.323-2.95a1 1 0 00-1.038-.595A3.516 3.516 0 002 5.5c0 4.963 4.038 9 9 9a3.516 3.516 0 003.492-3.057 1 1 0 00-.594-1.04zM11 13.5a8.009 8.009 0 01-8-8A2.512 2.512 0 015.18 3v.007l1.312 2.938L5.2 7.491a1 1 0 00-.098 1.03c.566 1.158 1.733 2.316 2.904 2.882a1 1 0 001.03-.107L10.556 10l2.937 1.316h.007A2.513 2.513 0 0111 13.5z",fill:"currentColor"})]})},73076:function(e,t,s){"use strict";var n=s(85893),r=s(93967),o=s.n(r),i=s(25675),l=s.n(i),a=s(98388);t.Z=e=>{let{is_active:t,logo:s,name:r,size:i="small",className:u,...c}=e;return(0,n.jsx)("div",{className:(0,a.m6)(o()("shrink-0 rounded-full border-2 bg-[#F2F2F2] drop-shadow-shopLogo",t?"border-accent":"border-[#F75159]","small"===i?"h-[5.75rem] w-[5.75rem]":"h-32 w-32 lg:h-[12.125rem] lg:w-[12.125rem]",u)),...c,children:(0,n.jsxs)("div",{className:o()("relative p-1.5",(null==s?void 0:s.original)?"":"flex h-full"),children:[(null==s?void 0:s.original)?(0,n.jsx)(l(),{alt:r,src:null==s?void 0:s.original,sizes:"(max-width: 768px) 100vw",className:(0,a.m6)(o()("rounded-full object-cover","small"===i?"h-[4.75rem] w-[4.75rem]":"h-28 w-28 lg:h-[11.125rem] lg:w-[11.125rem]")),width:80,height:80}):(0,n.jsx)(l(),{alt:r,src:"/shop-logo-placeholder.svg",sizes:"(max-width: 768px) 100vw",className:o()("m-auto","small"===i?"w-10":"w-14 lg:w-20"),width:80,height:80}),(0,n.jsx)("div",{className:o()("absolute rounded-full border-2 border-white",t?"bg-accent":"bg-[#F75159]","small"===i?"top-2 right-[0.625rem] h-2 w-2":"top-4 right-[4px] h-4 w-4 lg:right-[1.4375rem]")})]})})}},831:function(e,t,s){"use strict";var n=s(85893),r=s(93967),o=s.n(r),i=s(5233),l=s(25675),a=s.n(l),u=s(98388);t.Z=e=>{let{className:t,imageParentClassName:s,text:r,image:l="/no-result.svg"}=e,{t:c}=(0,i.$G)("common");return(0,n.jsxs)("div",{className:(0,u.m6)(o()("flex flex-col items-center",t)),children:[(0,n.jsx)("div",{className:(0,u.m6)(o()("relative flex h-full min-h-[380px] w-full items-center justify-center md:min-h-[450px]",s)),children:(0,n.jsx)(a(),{src:l,alt:c(r||"text-no-result-found"),className:"h-full w-full object-contain",fill:!0,sizes:"(max-width: 768px) 100vw"})}),r&&(0,n.jsx)("h3",{className:"my-7 w-full text-center text-base font-semibold text-heading/80 lg:text-xl",children:c(r)})]})}},44498:function(e,t,s){"use strict";s.d(t,{B:function(){return o}});var n=s(47869),r=s(3737);let o={me:()=>r.eN.get(n.P.ME),login:e=>r.eN.post(n.P.TOKEN,e),logout:()=>r.eN.post(n.P.LOGOUT,{}),register:e=>r.eN.post(n.P.REGISTER,e),update:e=>{let{id:t,input:s}=e;return r.eN.put("".concat(n.P.USERS,"/").concat(t),s)},changePassword:e=>r.eN.post(n.P.CHANGE_PASSWORD,e),forgetPassword:e=>r.eN.post(n.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>r.eN.post(n.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>r.eN.post(n.P.RESET_PASSWORD,e),makeAdmin:e=>r.eN.post(n.P.MAKE_ADMIN,e),block:e=>r.eN.post(n.P.BLOCK_USER,e),unblock:e=>r.eN.post(n.P.UNBLOCK_USER,e),addWalletPoints:e=>r.eN.post(n.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>r.eN.post(n.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...s}=e;return r.eN.get(n.P.USERS,{searchJoin:"and",with:"wallet",...s,search:r.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return r.eN.get(n.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return r.eN.get("".concat(n.P.USERS,"/").concat(t))},resendVerificationEmail:()=>r.eN.post(n.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return r.eN.post(n.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...s}=e;return r.eN.get(n.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...s})},fetchCustomers:e=>{let{...t}=e;return r.eN.get(n.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:s,name:o,...i}=e;return r.eN.get(n.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...i,search:r.eN.formatSearchParams({name:o,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:s,...o}=e;return r.eN.get(n.P.ALL_STAFFS,{searchJoin:"and",...o,search:r.eN.formatSearchParams({name:s,is_active:t})})}}},99930:function(e,t,s){"use strict";s.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var n=s(79362),r=s(97514),o=s(31955),i=s(5233),l=s(11163),a=s(88767),u=s(22920),c=s(47869),d=s(44498),m=s(28597),h=s(87066),f=s(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,l.useRouter)();return(0,a.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===r.Z.verifyLicense&&t.replace(r.Z.dashboard),t.pathname===r.Z.verifyEmail&&((0,f.Fu)(!0),t.replace(r.Z.dashboard))},onError:s=>{if(h.Z.isAxiosError(s)){var n,o;if((null===(n=s.response)||void 0===n?void 0:n.status)===417){t.replace(r.Z.verifyLicense);return}if((null===(o=s.response)||void 0===o?void 0:o.status)===409){(0,f.Fu)(!1),t.replace(r.Z.verifyEmail);return}e.clear(),t.replace(r.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,l.useRouter)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{o.Z.remove(n.E$),e.replace(r.Z.login),u.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{u.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};u.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,i.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{u.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,u.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,i.$G)();(0,a.useQueryClient)();let t=(0,l.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{u.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{u.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{u.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{u.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,i.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{u.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useAdminsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useVendorsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useCustomersQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useMyStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}},useAllStaffsQuery=e=>{var t;let{data:s,isLoading:n,error:r}=(0,a.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:n,error:r}}},35841:function(e,t,s){"use strict";function formatAddress(e){if(!e)return;let t=["street_address","city","state","zip","country"].reduce((t,s)=>({...t,[s]:e[s]}),{}),s=Object.fromEntries(Object.entries(t).filter(e=>{let[t,s]=e;return!!s}));return Object.values(s).join(", ")}s.d(t,{T:function(){return formatAddress}})},68040:function(e,t,s){"use strict";s.d(t,{b:function(){return useFormatPhoneNumber}});var n=s(19709),r=s(67294);let useFormatPhoneNumber=e=>{let{customer_contact:t}=e,s=(0,r.useMemo)(()=>{let e=(0,n.S)("+".concat(t));return null==e?void 0:e.formatInternational()},[t]);return s}}}]);