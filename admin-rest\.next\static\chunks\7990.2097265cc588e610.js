"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7990],{77990:function(e,t,n){n.r(t);var u=n(85893),a=n(1587),l=n(60802),d=n(75814),c=n(5233),m=n(41107);t.default=()=>{let{t:e}=(0,c.$G)(),{mutate:t,isLoading:n}=(0,m.hE)(),{data:f}=(0,d.X9)(),{closeModal:p}=(0,d.SO)();return(0,u.jsx)(a.l,{onSubmit:function(){t({id:f}),p()},children:t=>{let{register:a,formState:{errors:d}}=t;return(0,u.jsxs)("div",{className:"m-auto flex w-full max-w-sm flex-col rounded bg-light p-5 sm:w-[24rem]",children:[(0,u.jsx)("h2",{className:"mb-4 text-lg font-semibold text-muted-black",children:e("form:form-title-do-you-approve")}),(0,u.jsx)("div",{children:(0,u.jsx)(l.Z,{type:"submit",loading:n,disabled:n,children:e("form:button-label-submit")})})]})}})}},1587:function(e,t,n){n.d(t,{l:function(){return Form}});var u=n(85893),a=n(87536),l=n(47533),d=n(67294);let Form=e=>{let{onSubmit:t,children:n,options:c,validationSchema:m,serverError:f,resetValues:p,...v}=e,N=(0,a.cI)({...!!m&&{resolver:(0,l.X)(m)},...!!c&&c});return(0,d.useEffect)(()=>{f&&Object.entries(f).forEach(e=>{let[t,n]=e;N.setError(t,{type:"manual",message:n})})},[f,N]),(0,d.useEffect)(()=>{p&&N.reset(p)},[p,N]),(0,u.jsx)("form",{onSubmit:N.handleSubmit(t),noValidate:!0,...v,children:n(N)})}},41107:function(e,t,n){n.d(t,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var u=n(11163),a=n.n(u),l=n(88767),d=n(22920),c=n(5233),m=n(28597),f=n(97514),p=n(47869),v=n(93345),N=n(55191),A=n(3737);let C={...(0,N.h)(p.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:t,shop_id:n,...u}=e;return A.eN.get(p.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:n,...u,search:A.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>A.eN.post(p.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>A.eN.post(p.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,c.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(C.approve,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(p.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,c.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(C.disapprove,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(p.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:t,language:n}=e,{data:u,error:a,isLoading:d}=(0,l.useQuery)([p.P.TERMS_AND_CONDITIONS,{slug:t,language:n}],()=>C.get({slug:t,language:n}));return{termsAndConditions:u,error:a,loading:d}},useTermsAndConditionsQuery=e=>{var t;let{data:n,error:u,isLoading:a}=(0,l.useQuery)([p.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return C.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{termsAndConditions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(n),error:u,loading:a}},useCreateTermsAndConditionsMutation=()=>{let e=(0,l.useQueryClient)(),t=(0,u.useRouter)(),{t:n}=(0,c.$G)();return(0,l.useMutation)(C.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(f.Z.termsAndCondition.list):f.Z.termsAndCondition.list;await a().push(e,void 0,{locale:v.Config.defaultLanguage}),d.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(p.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;d.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,c.$G)(),t=(0,l.useQueryClient)(),n=(0,u.useRouter)();return(0,l.useMutation)(C.update,{onSuccess:async t=>{let u=n.query.shop?"/".concat(n.query.shop).concat(f.Z.termsAndCondition.list):f.Z.termsAndCondition.list;await n.push(u,void 0,{locale:v.Config.defaultLanguage}),d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(p.P.TERMS_AND_CONDITIONS)},onError:t=>{var n;d.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,c.$G)();return(0,l.useMutation)(C.delete,{onSuccess:()=>{d.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(p.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;d.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},47533:function(e,t,n){n.d(t,{X:function(){return o}});var u=n(87536);let s=(e,t,n)=>{if(e&&"reportValidity"in e){let a=(0,u.U2)(n,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},resolvers_o=(e,t)=>{for(let n in t.fields){let u=t.fields[n];u&&u.ref&&"reportValidity"in u.ref?s(u.ref,n,e):u.refs&&u.refs.forEach(t=>s(t,n,e))}},r=(e,t)=>{t.shouldUseNativeValidation&&resolvers_o(e,t);let n={};for(let a in e){let l=(0,u.U2)(t.fields,a),d=Object.assign(e[a]||{},{ref:l&&l.ref});if(i(t.names||Object.keys(e),a)){let e=Object.assign({},(0,u.U2)(n,a));(0,u.t8)(e,"root",d),(0,u.t8)(n,a,e)}else(0,u.t8)(n,a,d)}return n},i=(e,t)=>e.some(e=>e.startsWith(t+"."));function o(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),function(a,l,d){try{return Promise.resolve(function(u,c){try{var m=(t.context,Promise.resolve(e["sync"===n.mode?"validateSync":"validate"](a,Object.assign({abortEarly:!1},t,{context:l}))).then(function(e){return d.shouldUseNativeValidation&&resolvers_o({},d),{values:n.raw?a:e,errors:{}}}))}catch(e){return c(e)}return m&&m.then?m.then(void 0,c):m}(0,function(e){var t;if(!e.inner)throw e;return{values:{},errors:r((t=!d.shouldUseNativeValidation&&"all"===d.criteriaMode,(e.inner||[]).reduce(function(e,n){if(e[n.path]||(e[n.path]={message:n.message,type:n.type}),t){var a=e[n.path].types,l=a&&a[n.type];e[n.path]=(0,u.KN)(n.path,t,e,n.type,l?[].concat(l,n.message):n.message)}return e},{})),d)}}))}catch(e){return Promise.reject(e)}}}}}]);