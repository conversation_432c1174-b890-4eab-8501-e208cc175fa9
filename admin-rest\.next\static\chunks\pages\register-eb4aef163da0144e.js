(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[495,9930],{54722:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/register",function(){return s(4063)}])},33367:function(e,t,s){"use strict";s.d(t,{b:function(){return Eye}});var r=s(85893);let Eye=e=>(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})},86779:function(e,t,s){"use strict";s.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var r=s(85893);let InfoIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,r.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,r.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,r.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},29160:function(e,t,s){"use strict";s.d(t,{Z:function(){return AuthPageLayout}});var r=s(85893),o=s(51237);function AuthPageLayout(e){let{children:t}=e;return(0,r.jsx)("div",{className:"flex h-screen items-center justify-center bg-light sm:bg-gray-100",children:(0,r.jsxs)("div",{className:"m-auto w-full max-w-[420px] rounded bg-light p-5 sm:p-8 sm:shadow",children:[(0,r.jsx)("div",{className:"mb-2 flex justify-center",children:(0,r.jsx)(o.Z,{})}),t]})})}s(67294)},21587:function(e,t,s){"use strict";var r=s(85893),o=s(93967),n=s.n(o),l=s(5114),i=s(98388);let a={info:"bg-blue-100 text-blue-600",warning:"bg-yellow-100 text-yellow-600",error:"bg-red-100 text-red-500",success:"bg-green-100 text-accent",infoOutline:"border border-blue-200 text-blue-600",warningOutline:"border border-yellow-200 text-yellow-600",errorOutline:"border border-red-200 text-red-600",successOutline:"border border-green-200 text-green-600"};t.Z=e=>{let{message:t="",closeable:s=!1,variant:o="info",className:u,onClose:c,children:d,childClassName:m}=e;return(0,r.jsxs)("div",{className:(0,i.m6)(n()("relative flex items-center justify-between rounded py-4 px-5 shadow-sm",a[o],u)),role:"alert",children:[(0,r.jsxs)("div",{className:(0,i.m6)(n()(m)),children:[(0,r.jsx)("p",{className:"text-sm",children:t}),d]}),s&&(0,r.jsx)("button",{"data-dismiss":"alert","aria-label":"Close",onClick:c,title:"Close alert",className:"absolute top-1/2 -mt-3 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full text-red-500 transition-colors duration-200 -me-0.5 end-2 hover:bg-gray-300 hover:bg-opacity-25 focus:bg-gray-300 focus:bg-opacity-25 focus:outline-none",children:(0,r.jsx)("span",{"aria-hidden":"true",children:(0,r.jsx)(l.T,{className:"h-3 w-3"})})})]})}},33e3:function(e,t,s){"use strict";var r=s(85893),o=s(71611),n=s(93967),l=s.n(n),i=s(67294),a=s(98388);let u={small:"text-sm h-10",medium:"h-12",big:"h-14"},c=i.forwardRef((e,t)=>{let{className:s,label:n,note:i,name:c,error:d,children:m,variant:f="normal",dimension:g="medium",shadow:h=!1,type:p="text",inputClassName:x,disabled:v,showLabel:b=!0,required:S,toolTipText:y,labelClassName:w,...N}=e,E=l()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===f,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===f,"border border-border-base focus:border-accent":"outline"===f},{"focus:shadow":h},u[g],x),P="number"===p&&v?"number-disable":"";return(0,r.jsxs)("div",{className:(0,a.m6)(s),children:[b||n?(0,r.jsx)(o.Z,{htmlFor:c,toolTipText:y,label:n,required:S,className:w}):"",(0,r.jsx)("input",{id:c,name:c,type:p,ref:t,className:(0,a.m6)(l()(v?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(P," select-none"):"",E)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:v,"aria-invalid":d?"true":"false",...N}),i&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:i}),d&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:d})]})});c.displayName="Input",t.Z=c},23091:function(e,t,s){"use strict";var r=s(85893),o=s(93967),n=s.n(o),l=s(98388);t.Z=e=>{let{className:t,...s}=e;return(0,r.jsx)("label",{className:(0,l.m6)(n()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...s})}},51237:function(e,t,s){"use strict";var r=s(85893),o=s(8152),n=s(93967),l=s.n(n),i=s(99494),a=s(48583),u=s(79362),c=s(62964),d=s(25675),m=s.n(d),f=s(11163),g=s(90573);t.Z=e=>{var t,s,n,d,h,p,x,v,b,S,y;let{className:w,...N}=e,{locale:E}=(0,f.useRouter)(),{settings:P}=(0,g.n)({language:E}),[M,j]=(0,a.KO)(u.Hf),{width:A}=(0,c.Z)();return(0,r.jsx)(o.Z,{href:null===i.siteSettings||void 0===i.siteSettings?void 0:null===(t=i.siteSettings.logo)||void 0===t?void 0:t.href,className:l()("inline-flex items-center gap-3",w),children:M&&A>=u.h2?(0,r.jsx)("span",{className:"relative overflow-hidden ",style:{width:i.siteSettings.collapseLogo.width,height:i.siteSettings.collapseLogo.height},children:(0,r.jsx)(m(),{src:null!==(v=null==P?void 0:null===(n=P.options)||void 0===n?void 0:null===(s=n.collapseLogo)||void 0===s?void 0:s.original)&&void 0!==v?v:i.siteSettings.collapseLogo.url,alt:null!==(b=null==P?void 0:null===(d=P.options)||void 0===d?void 0:d.siteTitle)&&void 0!==b?b:i.siteSettings.collapseLogo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})}):(0,r.jsx)("span",{className:"relative overflow-hidden ",style:{width:i.siteSettings.logo.width,height:i.siteSettings.logo.height},children:(0,r.jsx)(m(),{src:null!==(S=null==P?void 0:null===(p=P.options)||void 0===p?void 0:null===(h=p.logo)||void 0===h?void 0:h.original)&&void 0!==S?S:i.siteSettings.logo.url,alt:null!==(y=null==P?void 0:null===(x=P.options)||void 0===x?void 0:x.siteTitle)&&void 0!==y?y:i.siteSettings.logo.alt,fill:!0,sizes:"(max-width: 768px) 100vw",className:"object-contain",loading:"eager"})})})}},33359:function(e,t,s){"use strict";s.d(t,{Z:function(){return d}});var r=s(85893),o=s(33367);let EyeOff=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"})});var n=s(93967),l=s.n(n),i=s(67294),a=s(8152);let u={root:"ltr:pl-4 rtl:pr-4 ltr:pr-12 rtl:pl-12 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",normal:"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent",solid:"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent",outline:"border border-border-base focus:border-accent",shadow:"focus:shadow"},c=i.forwardRef((e,t)=>{let{className:s,inputClassName:n,forgotPassHelpText:c,label:d,name:m,error:f,children:g,variant:h="normal",shadow:p=!1,type:x="text",forgotPageLink:v="",required:b,...S}=e,[y,w]=(0,i.useState)(!1),N=l()(u.root,{[u.normal]:"normal"===h,[u.solid]:"solid"===h,[u.outline]:"outline"===h},!0==p&&u.shadow,n);return(0,r.jsxs)("div",{className:s,children:[(0,r.jsxs)("div",{className:"mb-3 flex items-center justify-between",children:[(0,r.jsxs)("label",{htmlFor:m,className:"text-sm font-semibold leading-none text-body-dark",children:[d,b?(0,r.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):""]}),v&&c&&(0,r.jsx)(a.Z,{href:v,className:"text-xs text-accent transition-colors duration-200 hover:text-accent-hover focus:font-semibold focus:text-accent-700 focus:outline-none",children:c})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:m,name:m,type:y?"text":"password",ref:t,className:N,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",...S}),(0,r.jsx)("label",{htmlFor:m,className:"absolute top-5 -mt-2 text-body end-4",onClick:()=>w(e=>!e),children:y?(0,r.jsx)(EyeOff,{className:"h-5 w-5"}):(0,r.jsx)(o.b,{className:"h-5 w-5"})})]}),f&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:f})]})});c.displayName="PasswordInput";var d=c},71611:function(e,t,s){"use strict";var r=s(85893),o=s(86779),n=s(71943),l=s(23091),i=s(98388);t.Z=e=>{let{className:t,required:s,label:a,toolTipText:u,htmlFor:c}=e;return(0,r.jsxs)(l.Z,{className:(0,i.m6)(t),htmlFor:c,children:[a,s?(0,r.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",u?(0,r.jsx)(n.u,{content:u,children:(0,r.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,r.jsx)(o.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,s){"use strict";s.d(t,{u:function(){return Tooltip}});var r=s(85893),o=s(67294),n=s(93075),l=s(82364),i=s(24750),a=s(93967),u=s.n(a),c=s(67421),d=s(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},f={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:s,gap:a=8,animation:g="zoomIn",placement:h="top",size:p="md",rounded:x="DEFAULT",shadow:v="md",color:b="default",className:S,arrowClassName:y,showArrow:w=!0}=e,[N,E]=(0,o.useState)(!1),P=(0,o.useRef)(null),{t:M}=(0,c.$G)(),{x:j,y:A,refs:_,strategy:R,context:I}=(0,n.YF)({placement:h,open:N,onOpenChange:E,middleware:[(0,l.x7)({element:P}),(0,l.cv)(a),(0,l.RR)(),(0,l.uY)({padding:8})],whileElementsMounted:i.Me}),{getReferenceProps:k,getFloatingProps:Q}=(0,n.NI)([(0,n.XI)(I),(0,n.KK)(I),(0,n.qs)(I,{role:"tooltip"}),(0,n.bQ)(I)]),{isMounted:L,styles:C}=(0,n.Y_)(I,{duration:{open:150,close:150},...f[g]});return(0,r.jsxs)(r.Fragment,{children:[(0,o.cloneElement)(t,k({ref:_.setReference,...t.props})),(L||N)&&(0,r.jsx)(n.ll,{children:(0,r.jsxs)("div",{role:"tooltip",ref:_.setFloating,className:(0,d.m6)(u()(m.base,m.size[p],m.rounded[x],m.variant.solid.base,m.variant.solid.color[b],m.shadow[v],S)),style:{position:R,top:null!=A?A:0,left:null!=j?j:0,...C},...Q(),children:[M("".concat(s)),w&&(0,r.jsx)(n.Y$,{ref:P,context:I,className:u()(m.arrow.color[b],y),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},44498:function(e,t,s){"use strict";s.d(t,{B:function(){return n}});var r=s(47869),o=s(3737);let n={me:()=>o.eN.get(r.P.ME),login:e=>o.eN.post(r.P.TOKEN,e),logout:()=>o.eN.post(r.P.LOGOUT,{}),register:e=>o.eN.post(r.P.REGISTER,e),update:e=>{let{id:t,input:s}=e;return o.eN.put("".concat(r.P.USERS,"/").concat(t),s)},changePassword:e=>o.eN.post(r.P.CHANGE_PASSWORD,e),forgetPassword:e=>o.eN.post(r.P.FORGET_PASSWORD,e),verifyForgetPasswordToken:e=>o.eN.post(r.P.VERIFY_FORGET_PASSWORD_TOKEN,e),resetPassword:e=>o.eN.post(r.P.RESET_PASSWORD,e),makeAdmin:e=>o.eN.post(r.P.MAKE_ADMIN,e),block:e=>o.eN.post(r.P.BLOCK_USER,e),unblock:e=>o.eN.post(r.P.UNBLOCK_USER,e),addWalletPoints:e=>o.eN.post(r.P.ADD_WALLET_POINTS,e),addLicenseKey:e=>o.eN.post(r.P.ADD_LICENSE_KEY_VERIFY,e),fetchUsers:e=>{let{name:t,...s}=e;return o.eN.get(r.P.USERS,{searchJoin:"and",with:"wallet",...s,search:o.eN.formatSearchParams({name:t})})},fetchAdmins:e=>{let{...t}=e;return o.eN.get(r.P.ADMIN_LIST,{searchJoin:"and",with:"wallet;permissions;profile",...t})},fetchUser:e=>{let{id:t}=e;return o.eN.get("".concat(r.P.USERS,"/").concat(t))},resendVerificationEmail:()=>o.eN.post(r.P.SEND_VERIFICATION_EMAIL,{}),updateEmail:e=>{let{email:t}=e;return o.eN.post(r.P.UPDATE_EMAIL,{email:t})},fetchVendors:e=>{let{is_active:t,...s}=e;return o.eN.get(r.P.VENDORS_LIST,{searchJoin:"and",with:"wallet;permissions;profile",is_active:t,...s})},fetchCustomers:e=>{let{...t}=e;return o.eN.get(r.P.CUSTOMERS,{searchJoin:"and",with:"wallet",...t})},getMyStaffs:e=>{let{is_active:t,shop_id:s,name:n,...l}=e;return o.eN.get(r.P.MY_STAFFS,{searchJoin:"and",shop_id:s,...l,search:o.eN.formatSearchParams({name:n,is_active:t})})},getAllStaffs:e=>{let{is_active:t,name:s,...n}=e;return o.eN.get(r.P.ALL_STAFFS,{searchJoin:"and",...n,search:o.eN.formatSearchParams({name:s,is_active:t})})}}},99930:function(e,t,s){"use strict";s.d(t,{$h:function(){return useChangePasswordMutation},AV:function(){return useVerifyForgetPasswordTokenMutation},HK:function(){return useAllStaffsQuery},HV:function(){return useMyStaffsQuery},Jc:function(){return useAdminsQuery},L8:function(){return useLicenseKeyMutation},UE:function(){return useMeQuery},_y:function(){return useLogoutMutation},bT:function(){return useUserQuery},bg:function(){return useVendorsQuery},f0:function(){return useLogin},gL:function(){return useResetPasswordMutation},h3:function(){return useAddWalletPointsMutation},kD:function(){return useUpdateUserMutation},l4:function(){return useRegisterMutation},lJ:function(){return useMakeOrRevokeAdminMutation},of:function(){return useUpdateUserEmailMutation},q3:function(){return useBlockUserMutation},rq:function(){return useCustomersQuery},xE:function(){return useUnblockUserMutation},xY:function(){return useUsersQuery},xy:function(){return useForgetPasswordMutation},y6:function(){return useResendVerificationEmail}});var r=s(79362),o=s(97514),n=s(31955),l=s(5233),i=s(11163),a=s(88767),u=s(22920),c=s(47869),d=s(44498),m=s(28597),f=s(87066),g=s(16203);let useMeQuery=()=>{let e=(0,a.useQueryClient)(),t=(0,i.useRouter)();return(0,a.useQuery)([c.P.ME],d.B.me,{retry:!1,onSuccess:()=>{t.pathname===o.Z.verifyLicense&&t.replace(o.Z.dashboard),t.pathname===o.Z.verifyEmail&&((0,g.Fu)(!0),t.replace(o.Z.dashboard))},onError:s=>{if(f.Z.isAxiosError(s)){var r,n;if((null===(r=s.response)||void 0===r?void 0:r.status)===417){t.replace(o.Z.verifyLicense);return}if((null===(n=s.response)||void 0===n?void 0:n.status)===409){(0,g.Fu)(!1),t.replace(o.Z.verifyEmail);return}e.clear(),t.replace(o.Z.login)}}})};function useLogin(){return(0,a.useMutation)(d.B.login)}let useLogoutMutation=()=>{let e=(0,i.useRouter)(),{t}=(0,l.$G)();return(0,a.useMutation)(d.B.logout,{onSuccess:()=>{n.Z.remove(r.E$),e.replace(o.Z.login),u.Am.success(t("common:successfully-logout"),{toastId:"logoutSuccess"})}})},useRegisterMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,l.$G)();return(0,a.useMutation)(d.B.register,{onSuccess:()=>{u.Am.success(t("common:successfully-register"),{toastId:"successRegister"})},onSettled:()=>{e.invalidateQueries(c.P.REGISTER)}})},useUpdateUserMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.update,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useUpdateUserEmailMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.updateEmail,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onError:e=>{let{response:{data:t}}=null!=e?e:{};u.Am.error(null==t?void 0:t.message)},onSettled:()=>{t.invalidateQueries(c.P.ME),t.invalidateQueries(c.P.USERS)}})},useChangePasswordMutation=()=>(0,a.useMutation)(d.B.changePassword),useForgetPasswordMutation=()=>(0,a.useMutation)(d.B.forgetPassword),useResendVerificationEmail=()=>{let{t:e}=(0,l.$G)("common");return(0,a.useMutation)(d.B.resendVerificationEmail,{onSuccess:()=>{u.Am.success(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL"))},onError:()=>{(0,u.Am)(e("common:PICKBAZAR_MESSAGE.EMAIL_SENT_FAILED"))}})},useLicenseKeyMutation=()=>{let{t:e}=(0,l.$G)();(0,a.useQueryClient)();let t=(0,i.useRouter)();return(0,a.useMutation)(d.B.addLicenseKey,{onSuccess:()=>{u.Am.success(e("common:successfully-updated")),setTimeout(()=>{t.reload()},1e3)},onError:()=>{u.Am.error(e("common:PICKBAZAR_MESSAGE.INVALID_LICENSE_KEY"))}})},useVerifyForgetPasswordTokenMutation=()=>(0,a.useMutation)(d.B.verifyForgetPasswordToken),useResetPasswordMutation=()=>(0,a.useMutation)(d.B.resetPassword),useMakeOrRevokeAdminMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,l.$G)();return(0,a.useMutation)(d.B.makeAdmin,{onSuccess:()=>{u.Am.success(t("common:successfully-updated"))},onSettled:()=>{e.invalidateQueries(c.P.USERS)}})},useBlockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,l.$G)();return(0,a.useMutation)(d.B.block,{onSuccess:()=>{u.Am.success(t("common:successfully-block"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useUnblockUserMutation=()=>{let e=(0,a.useQueryClient)(),{t}=(0,l.$G)();return(0,a.useMutation)(d.B.unblock,{onSuccess:()=>{u.Am.success(t("common:successfully-unblock"))},onSettled:()=>{e.invalidateQueries(c.P.USERS),e.invalidateQueries(c.P.STAFFS),e.invalidateQueries(c.P.ADMIN_LIST),e.invalidateQueries(c.P.CUSTOMERS),e.invalidateQueries(c.P.VENDORS_LIST)}})},useAddWalletPointsMutation=()=>{let{t:e}=(0,l.$G)(),t=(0,a.useQueryClient)();return(0,a.useMutation)(d.B.addWalletPoints,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.USERS)}})},useUserQuery=e=>{let{id:t}=e;return(0,a.useQuery)([c.P.USERS,t],()=>d.B.fetchUser({id:t}),{enabled:!!t})},useUsersQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.USERS,e],()=>d.B.fetchUsers(e),{keepPreviousData:!0});return{users:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}},useAdminsQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.ADMIN_LIST,e],()=>d.B.fetchAdmins(e),{keepPreviousData:!0});return{admins:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}},useVendorsQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.VENDORS_LIST,e],()=>d.B.fetchVendors(e),{keepPreviousData:!0});return{vendors:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}},useCustomersQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.CUSTOMERS,e],()=>d.B.fetchCustomers(e),{keepPreviousData:!0});return{customers:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}},useMyStaffsQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.MY_STAFFS,e],()=>d.B.getMyStaffs(e),{keepPreviousData:!0});return{myStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}},useAllStaffsQuery=e=>{var t;let{data:s,isLoading:r,error:o}=(0,a.useQuery)([c.P.ALL_STAFFS,e],()=>d.B.getAllStaffs(e),{keepPreviousData:!0});return{allStaffs:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:[],paginatorInfo:(0,m.Q)(s),loading:r,error:o}}},4063:function(e,t,s){"use strict";s.r(t),s.d(t,{__N_SSG:function(){return y},default:function(){return RegisterPage}});var r=s(85893),o=s(5233),n=s(21587),l=s(60802),i=s(33e3),a=s(33359),u=s(11163),c=s(67294),d=s(87536),m=s(97514),f=s(47533),g=s(16310),h=s(8152),p=s(16203),x=s(10265),v=s(99930);let b=g.Ry().shape({name:g.Z_().required("form:error-name-required"),email:g.Z_().email("form:error-email-format").required("form:error-email-required"),password:g.Z_().required("form:error-password-required"),permission:g.Z_().default("store_owner").oneOf(["store_owner"])});var registration_form=()=>{var e,t,s;let[g,S]=(0,c.useState)(null),{mutate:y,isLoading:w}=(0,v.l4)(),{register:N,handleSubmit:E,formState:{errors:P},setError:M}=(0,d.cI)({resolver:(0,f.X)(b),defaultValues:{permission:x.y3.StoreOwner}}),j=(0,u.useRouter)(),{t:A}=(0,o.$G)();async function onSubmit(e){let{name:t,email:s,password:r,permission:o}=e;y({name:t,email:s,password:r,permission:o},{onSuccess:e=>{if(null==e?void 0:e.token){if((0,p.Ft)(p.mI,null==e?void 0:e.permissions)){(0,p.j9)(null==e?void 0:e.token,null==e?void 0:e.permissions,null==e?void 0:e.role),j.push(m.Z.dashboard);return}S("form:error-enough-permission")}else S("form:error-credential-wrong")},onError:e=>{var t;Object.keys(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data).forEach(t=>{var s;M(t,{type:"manual",message:null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.data[t]})})}})}return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("form",{onSubmit:E(onSubmit),noValidate:!0,children:[(0,r.jsx)(i.Z,{label:A("form:input-label-name"),...N("name"),variant:"outline",className:"mb-4",error:A(null==P?void 0:null===(e=P.name)||void 0===e?void 0:e.message)}),(0,r.jsx)(i.Z,{label:A("form:input-label-email"),...N("email"),type:"email",variant:"outline",className:"mb-4",error:A(null==P?void 0:null===(t=P.email)||void 0===t?void 0:t.message)}),(0,r.jsx)(a.Z,{label:A("form:input-label-password"),...N("password"),error:A(null==P?void 0:null===(s=P.password)||void 0===s?void 0:s.message),variant:"outline",className:"mb-4"}),(0,r.jsx)(l.Z,{className:"w-full",loading:w,disabled:w,children:A("form:text-register")}),g?(0,r.jsx)(n.Z,{message:A(g),variant:"error",closeable:!0,className:"mt-5",onClose:()=>S(null)}):null]}),(0,r.jsxs)("div",{className:"relative flex flex-col items-center justify-center mt-8 mb-6 text-sm text-heading sm:mt-11 sm:mb-8",children:[(0,r.jsx)("hr",{className:"w-full"}),(0,r.jsx)("span",{className:"start-2/4 -ms-4 absolute -top-2.5 bg-light px-2",children:A("common:text-or")})]}),(0,r.jsxs)("div",{className:"text-sm text-center text-body sm:text-base",children:[A("form:text-already-account")," ",(0,r.jsx)(h.Z,{href:m.Z.login,className:"font-semibold underline transition-colors duration-200 ms-1 text-accent hover:text-accent-hover hover:no-underline focus:text-accent-700 focus:no-underline focus:outline-none",children:A("form:button-label-login")})]})]})},S=s(29160),y=!0;function RegisterPage(){let e=(0,u.useRouter)(),{token:t,permissions:s}=(0,p.WA)();(0,p.$8)({token:t,permissions:s})&&e.replace(m.Z.dashboard);let{t:n}=(0,o.$G)("common");return(0,r.jsxs)(S.Z,{children:[(0,r.jsx)("h3",{className:"mb-6 mt-4 text-center text-base italic text-gray-500",children:n("admin-register-title")}),(0,r.jsx)(registration_form,{})]})}}},function(e){e.O(0,[6342,4750,5921,2964,7536,2216,9494,9774,2888,179],function(){return e(e.s=54722)}),_N_E=e.O()}]);