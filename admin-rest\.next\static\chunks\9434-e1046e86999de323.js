"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9434],{71261:function(e,t,a){a.d(t,{Iy:function(){return EditFillIcon},Iz:function(){return EditGhostIcon},dK:function(){return ComposeEditIcon},dY:function(){return EditIcon}});var l=a(85893);let EditIcon=e=>(0,l.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20.547 20.299",fill:"currentColor",...e,children:(0,l.jsxs)("g",{stroke:"currentColor",strokeWidth:".4",children:[(0,l.jsx)("path",{"data-name":"Path 78",d:"M18.659 12.688a.5.5 0 00-.5.5v4.423a1.5 1.5 0 01-1.494 1.494H2.691A1.5 1.5 0 011.2 17.609V4.629a1.5 1.5 0 011.494-1.494h4.419a.5.5 0 100-1H2.691A2.493 2.493 0 00.2 4.629v12.98A2.493 2.493 0 002.691 20.1h13.976a2.493 2.493 0 002.491-2.491v-4.423a.5.5 0 00-.5-.5zm0 0"}),(0,l.jsx)("path",{"data-name":"Path 79",d:"M18.96.856a2.241 2.241 0 00-3.17 0L6.899 9.739a.5.5 0 00-.128.219l-1.169 4.219a.5.5 0 00.613.613l4.219-1.169a.5.5 0 00.219-.128l8.886-8.887a2.244 2.244 0 000-3.17zm-10.971 9.21l7.273-7.273 2.346 2.346-7.273 7.273zm-.469.94l1.879 1.875-2.592.718zm11.32-7.1l-.528.528-2.346-2.345.528-.528a1.245 1.245 0 011.761 0l.585.584a1.247 1.247 0 010 1.761zm0 0"})]})}),EditFillIcon=e=>(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,l.jsx)("path",{d:"M4.671 7.87l4.546-4.546 1.459 1.459-4.547 4.546h0a2.563 2.563 0 01-1.08.645s0 0 0 0l-1.456.433.434-1.455c.121-.409.343-.78.644-1.081h0zm-1.189 2.57s0 0 0 0h0zm8.112-9.065a1.031 1.031 0 01.729 1.76l-.321.322-1.459-1.459.322-.32a1.03 1.03 0 01.729-.303z",fill:"currentColor",stroke:"currentColor"}),(0,l.jsx)("path",{d:"M3.063 3.063a1.75 1.75 0 00-1.75 1.75v6.125a1.75 1.75 0 001.75 1.75h6.124a1.75 1.75 0 001.75-1.75V7.874a.438.438 0 00-.874 0v3.063a.875.875 0 01-.876.874H3.064a.875.875 0 01-.876-.874V4.811a.875.875 0 01.876-.875h3.062a.437.437 0 100-.874H3.062z",fill:"currentColor"})]}),EditGhostIcon=e=>(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,l.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,l.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h2.793a.992.992 0 00.707-.293l5.23-5.229.217.869-2.3 2.3a.5.5 0 00.707.707l2.5-2.5a.5.5 0 00.132-.475l-.432-1.726L14.207 6a.999.999 0 000-1.414zM3 13v-1.793L4.793 13H3zm3-.207L3.207 10 8.5 4.707 11.293 7.5 6 12.793zm6-6L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]}),ComposeEditIcon=e=>(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,l.jsx)("path",{opacity:.2,d:"M13.854 5.646L12 7.5 8.5 4l1.854-1.854a.5.5 0 01.707 0l2.793 2.792a.5.5 0 010 .708z",fill:"currentColor"}),(0,l.jsx)("path",{d:"M14.207 4.586l-2.793-2.793a1 1 0 00-1.414 0L2.293 9.5a.991.991 0 00-.293.707V13a1 1 0 001 1h10.5a.5.5 0 000-1H7.208l7-7a.999.999 0 000-1.414zM3 10.206l5.5-5.5L11.293 7.5l-5.5 5.5H3v-2.793zm9-3.413L9.208 4l1.5-1.5L13.5 5.293l-1.5 1.5z",fill:"currentColor"})]})},88762:function(e,t,a){a.d(t,{Z:function(){return OpenAIButton}});var l=a(85893),r=a(93967),n=a.n(r);function OpenAIButton(e){let{className:t,onClick:a,title:r,...o}=e;return(0,l.jsx)("div",{onClick:a,className:n()("absolute right-0 -top-1 z-10 cursor-pointer text-sm font-medium text-accent hover:text-accent-hover",t),...o,children:r})}},40796:function(e,t,a){a.d(t,{Z:function(){return CreateOrUpdateTagForm}});var l=a(85893),r=a(33e3),n=a(87536),o=a(60802),i=a(95414),s=a(23091),u=a(92072),c=a(80602),d=a(4232),m=a(47559),f=a(11163),p=a(9140),g=a(66271);let b=[{value:"Accessories",label:"Accessories"},{value:"FruitsVegetable",label:"Fruits and Vegetable"},{value:"MeatFish",label:"Meat and Fish"},{value:"Purse",label:"Purse"},{value:"HandBags",label:"Hand Bags"},{value:"ShoulderBags",label:"Shoulder Bags"},{value:"Wallet",label:"Wallet"},{value:"LaptopBags",label:"Laptop Bags"},{value:"WomenDress",label:"Women Dress"},{value:"OuterWear",label:"Outer Wear"},{value:"Pants",label:"Pants"},{value:"Tops",label:"Tops"},{value:"Shirts",label:"Shirts"},{value:"Skirts",label:"Skirts"},{value:"Face",label:"Face"},{value:"Eyes",label:"Eyes"},{value:"Lips",label:"Lips"},{value:"Snacks",label:"Snacks"},{value:"PetCare",label:"PetCare"},{value:"HomeCleaning",label:"Home Cleaning"},{value:"Dairy",label:"Dairy"},{value:"Cooking",label:"Cooking"},{value:"Breakfast",label:"Breakfast"},{value:"Beverage",label:"Beverage"},{value:"BeautyHealth",label:"Beauty Health"},{value:"ShavingNeeds",label:"Shaving Needs"},{value:"OralCare",label:"Oral Care"},{value:"FacialCare",label:"Facial Care"},{value:"Deodorant",label:"Deodorant"},{value:"BathOil",label:"Bath Oil"},{value:"Chair",label:"Chair"},{value:"Bed",label:"Bed"},{value:"Bookshelf",label:"Bookshelf"},{value:"CenterTable",label:"Center Table"},{value:"DressingTable",label:"Dressing Table"},{value:"ReadingTable",label:"Reading Table"},{value:"Sofa",label:"Sofa"},{value:"RelaxChair",label:"Relax Chair"},{value:"Storage",label:"Storage"},{value:"Tools",label:"Tools"},{value:"Table",label:"Table"}];var v=a(5233),h=a(66272),x=a(28454),y=a(47533),w=a(16310);let j=w.Ry().shape({name:w.Z_().required("form:error-name-required"),type:w.Ry().nullable().required("form:error-type-required")});var C=a(15824),O=a(16720),S=a(88762),T=a(90573),Z=a(67294),N=a(75814),P=a(71261),_=a(93345),k=a(24504),E=a(22220);let TagDetailSuggestions=e=>{let{name:t}=e;return[{id:1,title:"Discover the magic of ".concat(t," as we curate the finest products for you.")},{id:2,title:"Elevate your shopping experience with our carefully selected ".concat(t," collection.")},{id:3,title:"Explore a world of possibilities with our diverse range of ".concat(t," products.")},{id:4,title:"Experience excellence with our handpicked selection of ".concat(t," items.")},{id:5,title:"Find your perfect match among our premium ".concat(t," products.")},{id:6,title:"Simplify your search and find what you need with our intuitive ".concat(t," category.")},{id:7,title:"Embrace style and functionality with our exclusive ".concat(t," product line.")},{id:8,title:"Enhance your lifestyle with our innovative ".concat(t," offerings.")},{id:9,title:"Unlock new dimensions of ".concat(t," with our exceptional product assortment.")},{id:10,title:"Immerse yourself in the world of ".concat(t," and discover unique treasures.")}]};function SelectTypes(e){var t;let{control:a,errors:r}=e,{locale:n}=(0,f.useRouter)(),{t:o}=(0,v.$G)(),{types:i,loading:u}=(0,O.qs)({limit:999,language:n});return(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsx)(s.Z,{children:o("form:input-label-types")}),(0,l.jsx)(x.Z,{name:"type",control:a,getOptionLabel:e=>e.name,getOptionValue:e=>e.slug,options:i,isLoading:u}),(0,l.jsx)(g.Z,{message:o(null===(t=r.type)||void 0===t?void 0:t.message)})]})}let L=b.map(e=>(e.label=(0,l.jsxs)("div",{className:"flex items-center space-s-5",children:[(0,l.jsx)("span",{className:"flex items-center justify-center w-5 h-5",children:(0,m.q)({iconList:d,iconName:e.value,className:"max-h-full max-w-full"})}),(0,l.jsx)("span",{children:e.label})]}),e)),B={image:"",name:"",slug:"",details:"",icon:"",type:""};function CreateOrUpdateTagForm(e){var t,a,d,m;let{initialValues:g}=e,w=(0,f.useRouter)(),{t:O}=(0,v.$G)(),z=(null==w?void 0:null===(t=w.query)||void 0===t?void 0:t.action)==="translate",[D,M]=(0,Z.useState)(!0),A=(null==w?void 0:null===(a=w.query)||void 0===a?void 0:a.action)==="edit"&&(null==w?void 0:w.locale)===_.Config.defaultLanguage,{register:I,handleSubmit:F,control:R,watch:Q,setValue:G,formState:{errors:q}}=(0,n.cI)({defaultValues:g?{...g,icon:(null==g?void 0:g.icon)?b.find(e=>e.value===(null==g?void 0:g.icon)):"",...z&&{type:null}}:B,resolver:(0,y.X)(j)}),{openModal:H}=(0,N.SO)(),{locale:V}=w,{settings:{options:W}}=(0,T.n)({language:V}),$=Q("name"),U=(0,Z.useMemo)(()=>TagDetailSuggestions({name:null!=$?$:""}),[$]),K=(0,Z.useCallback)(()=>{H("GENERATE_DESCRIPTION",{control:R,name:$,set_value:G,key:"details",suggestion:U})},[$]),{mutate:Y,isLoading:J}=(0,C.be)(),{mutate:X,isLoading:ee}=(0,C.go)(),et=(0,k.g)(Q("name")),onSubmit=async e=>{var t,a,l,r,n,o;let i={language:w.locale,name:e.name,slug:e.slug,details:e.details,image:{thumbnail:null==e?void 0:null===(t=e.image)||void 0===t?void 0:t.thumbnail,original:null==e?void 0:null===(a=e.image)||void 0===a?void 0:a.original,id:null==e?void 0:null===(l=e.image)||void 0===l?void 0:l.id},icon:null!==(o=null===(r=e.icon)||void 0===r?void 0:r.value)&&void 0!==o?o:"",type_id:null===(n=e.type)||void 0===n?void 0:n.id};try{g&&g.translated_languages.includes(w.locale)?X({...i,id:g.id}):Y({...i,...(null==g?void 0:g.slug)&&{slug:g.slug}})}catch(e){(0,p.e)(e)}};return(0,l.jsxs)("form",{onSubmit:F(onSubmit),children:[(0,l.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-gray-300 border-dashed sm:my-8",children:[(0,l.jsx)(c.Z,{title:O("form:input-label-image"),details:O("form:tag-image-helper-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,l.jsx)(u.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,l.jsx)(h.Z,{name:"image",control:R,multiple:!1})})]}),(0,l.jsxs)("div",{className:"flex flex-wrap my-5 sm:my-8",children:[(0,l.jsx)(c.Z,{title:O("form:input-label-description"),details:"".concat(O(g?"form:item-description-edit":"form:item-description-add")," ").concat(O("form:tag-description-helper-text")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5 "}),(0,l.jsxs)(u.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,l.jsx)(r.Z,{label:O("form:input-label-name"),...I("name"),error:O(null===(d=q.name)||void 0===d?void 0:d.message),variant:"outline",className:"mb-5"}),A?(0,l.jsxs)("div",{className:"relative mb-5",children:[(0,l.jsx)(r.Z,{label:O("form:input-label-slug"),...I("slug"),error:O(null===(m=q.slug)||void 0===m?void 0:m.message),variant:"outline",disabled:D}),(0,l.jsx)("button",{className:"absolute top-[27px] right-px z-0 flex h-[46px] w-11 items-center justify-center rounded-tr rounded-br border-l border-solid border-border-base bg-white px-2 text-body transition duration-200 hover:text-heading focus:outline-none",type:"button",title:O("common:text-edit"),onClick:()=>M(!1),children:(0,l.jsx)(P.dY,{width:14})})]}):(0,l.jsx)(r.Z,{label:O("form:input-label-slug"),...I("slug"),value:et,variant:"outline",className:"mb-5",disabled:!0}),(0,l.jsxs)("div",{className:"relative",children:[(null==W?void 0:W.useAi)&&(0,l.jsx)(S.Z,{title:O("form:button-label-description-ai"),onClick:K}),(0,l.jsx)(i.Z,{label:O("form:input-label-details"),...I("details"),variant:"outline",className:"mb-5"})]}),(0,l.jsxs)("div",{className:"mb-5",children:[(0,l.jsx)(s.Z,{children:O("form:input-label-select-icon")}),(0,l.jsx)(x.Z,{name:"icon",control:R,options:L,isClearable:!0})]}),(0,l.jsx)(SelectTypes,{control:R,errors:q})]})]}),(0,l.jsx)(E.Z,{className:"z-0",children:(0,l.jsxs)("div",{className:"text-end",children:[g&&(0,l.jsx)(o.Z,{variant:"outline",onClick:w.back,className:"text-sm me-4 md:text-base",type:"button",children:O("form:button-label-back")}),(0,l.jsx)(o.Z,{loading:J||ee,disabled:J||ee,className:"text-sm md:text-base",children:O(g?"form:button-label-update-tag":"form:button-label-add-tag")})]})})]})}},28454:function(e,t,a){var l=a(85893),r=a(79828),n=a(71611),o=a(87536);t.Z=e=>{let{control:t,options:a,name:i,rules:s,getOptionLabel:u,getOptionValue:c,disabled:d,isMulti:m,isClearable:f,isLoading:p,placeholder:g,label:b,required:v,toolTipText:h,error:x,...y}=e;return(0,l.jsxs)(l.Fragment,{children:[b?(0,l.jsx)(n.Z,{htmlFor:i,toolTipText:h,label:b,required:v}):"",(0,l.jsx)(o.Qr,{control:t,name:i,rules:s,...y,render:e=>{let{field:t}=e;return(0,l.jsx)(r.Z,{...t,getOptionLabel:u,getOptionValue:c,placeholder:g,isMulti:m,isClearable:f,isLoading:p,options:a,isDisabled:d})}}),x&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:x})]})}},22220:function(e,t,a){var l=a(85893),r=a(93967),n=a.n(r),o=a(98388);t.Z=e=>{let{children:t,className:a,...r}=e;return(0,l.jsx)("div",{className:(0,o.m6)(n()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",a)),...r,children:t})}},95414:function(e,t,a){var l=a(85893),r=a(71611),n=a(93967),o=a.n(n),i=a(67294),s=a(98388);let u=i.forwardRef((e,t)=>{let{className:a,label:n,toolTipText:i,name:u,error:c,variant:d="normal",shadow:m=!1,inputClassName:f,disabled:p,required:g,...b}=e,v=o()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===d,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===d,"border border-border-base focus:border-accent":"outline"===d},{"focus:shadow":m},f);return(0,l.jsxs)("div",{className:(0,s.m6)(o()(a)),children:[n&&(0,l.jsx)(r.Z,{htmlFor:u,toolTipText:i,label:n,required:g}),(0,l.jsx)("textarea",{id:u,name:u,className:(0,s.m6)(o()(v,p?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:p,...b}),c&&(0,l.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});u.displayName="TextArea",t.Z=u},15824:function(e,t,a){a.d(t,{be:function(){return useCreateTagMutation},BW:function(){return useDeleteTagMutation},wt:function(){return useTagQuery},Qd:function(){return useTagsQuery},go:function(){return useUpdateTagMutation}});var l=a(11163),r=a.n(l),n=a(88767),o=a(22920),i=a(5233),s=a(28597),u=a(47869),c=a(97514),d=a(55191),m=a(3737);let f={...(0,d.h)(u.P.TAGS),paginated:e=>{let{type:t,name:a,...l}=e;return m.eN.get(u.P.TAGS,{searchJoin:"and",...l,search:m.eN.formatSearchParams({type:t,name:a})})}};var p=a(93345);let useCreateTagMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,i.$G)();return(0,n.useMutation)(f.create,{onSuccess:()=>{r().push(c.Z.tag.list,void 0,{locale:p.Config.defaultLanguage}),o.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(u.P.TAGS)}})},useDeleteTagMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,i.$G)();return(0,n.useMutation)(f.delete,{onSuccess:()=>{o.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(u.P.TAGS)}})},useUpdateTagMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useRouter)(),a=(0,n.useQueryClient)();return(0,n.useMutation)(f.update,{onSuccess:async a=>{let l=t.query.shop?"/".concat(t.query.shop).concat(c.Z.tag.list):c.Z.tag.list;await t.push("".concat(l,"/").concat(null==a?void 0:a.slug,"/edit"),void 0,{locale:p.Config.defaultLanguage}),o.Am.success(e("common:successfully-updated"))},onSettled:()=>{a.invalidateQueries(u.P.TAGS)}})},useTagQuery=e=>{let{slug:t,language:a}=e,{data:l,error:r,isLoading:o}=(0,n.useQuery)([u.P.TYPES,{slug:t,language:a}],()=>f.get({slug:t,language:a}));return{tag:l,error:r,loading:o}},useTagsQuery=e=>{var t;let{data:a,error:l,isLoading:r}=(0,n.useQuery)([u.P.TAGS,e],e=>{let{queryKey:t,pageParam:a}=e;return f.paginated(Object.assign({},t[1],a))},{keepPreviousData:!0});return{tags:null!==(t=null==a?void 0:a.data)&&void 0!==t?t:[],paginatorInfo:(0,s.Q)(a),error:l,loading:r}}},9140:function(e,t,a){a.d(t,{e:function(){return getErrorMessage}});var l=a(11163),r=a.n(l),n=a(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let a of e.graphQLErrors){if(a.extensions&&"validation"===a.extensions.category)return t.message=a.message,t.validation=a.extensions.validation,t;a.extensions&&"authorization"===a.extensions.category&&(n.Z.remove("auth_token"),n.Z.remove("auth_permissions"),r().push("/"))}return t.message=e.message,t}},24504:function(e,t,a){a.d(t,{g:function(){return formatSlug}});function formatSlug(e){if(!e)return"";let t=e.replace(/\s+/g,"-").toLowerCase(),a=t.replace(/-+$/,"");return a}},97326:function(e,t,a){a.d(t,{Z:function(){return _assertThisInitialized}});function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},15671:function(e,t,a){a.d(t,{Z:function(){return _classCallCheck}});function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}},43144:function(e,t,a){a.d(t,{Z:function(){return _createClass}});var l=a(83997);function _defineProperties(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(0,l.Z)(r.key),r)}}function _createClass(e,t,a){return t&&_defineProperties(e.prototype,t),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}},73568:function(e,t,a){function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}a.d(t,{Z:function(){return _createSuper}});var l=a(71002),r=a(97326);function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var a,n=_getPrototypeOf(e);if(t){var o=_getPrototypeOf(this).constructor;a=Reflect.construct(n,arguments,o)}else a=n.apply(this,arguments);return function(e,t){if(t&&("object"==(0,l.Z)(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return(0,r.Z)(e)}(this,a)}}},60136:function(e,t,a){a.d(t,{Z:function(){return _inherits}});var l=a(89611);function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,l.Z)(e,t)}},1413:function(e,t,a){a.d(t,{Z:function(){return _objectSpread2}});var l=a(4942);function ownKeys(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,l)}return a}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(a),!0).forEach(function(t){(0,l.Z)(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}}}]);