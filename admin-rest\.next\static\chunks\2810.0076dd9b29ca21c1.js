"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2810],{92810:function(e,t,r){r.r(t),r.d(t,{default:function(){return attribute_import_export}});var n=r(85893),o=r(92072),s=r(41312),u=r(5233),a=r(11163),i=r(91297),c=r(30042),l=r(97507);function ImportAttributes(){let{t:e}=(0,u.$G)(),{query:{shop:t}}=(0,a.useRouter)(),{data:r}=(0,c.DZ)({slug:t}),o=null==r?void 0:r.id,{mutate:s,isLoading:d}=(0,l.JK)(),handleDrop=async e=>{e.length&&s({shop_id:o,csv:e[0]})};return(0,n.jsx)(i.Z,{onDrop:handleDrop,loading:d,title:e("text-import-attributes")})}var d=r(75814),p=r(83454),attribute_import_export=()=>{var e;let{t}=(0,u.$G)(),{data:r}=(0,d.X9)();return(0,n.jsxs)(o.Z,{className:"flex min-h-screen w-screen flex-col md:min-h-0 md:w-auto lg:min-w-[900px]",children:[(0,n.jsx)("div",{className:"mb-5 w-full",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-heading",children:t("common:text-export-import")})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-5 md:grid-cols-3",children:[(0,n.jsx)(ImportAttributes,{}),(0,n.jsxs)("a",{href:"".concat(null==p?void 0:null===(e=p.env)||void 0===e?void 0:"http://localhost:9000/api","/export-attributes/").concat(r),target:"_blank",rel:"noreferrer",className:"flex h-36 cursor-pointer flex-col items-center justify-center rounded border-2 border-dashed border-border-base p-5 focus:border-accent-400 focus:outline-none",children:[(0,n.jsx)(s._,{className:"w-10 text-muted-light"}),(0,n.jsx)("span",{className:"mt-4 text-center text-sm font-semibold text-accent",children:t("common:text-export-attributes")})]})]})]})}},92072:function(e,t,r){var n=r(85893),o=r(93967),s=r.n(o),u=r(98388);t.Z=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,u.m6)(s()("rounded bg-light p-5 shadow md:p-8",t)),...r})}},41312:function(e,t,r){r.d(t,{_:function(){return DownloadIcon}});var n=r(85893);let DownloadIcon=e=>{let{...t}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 548.176 548.176",...t,children:(0,n.jsx)("path",{d:"M524.326 297.352c-15.896-19.89-36.21-32.782-60.959-38.684 7.81-11.8 11.704-24.934 11.704-39.399 0-20.177-7.139-37.401-21.409-51.678-14.273-14.272-31.498-21.411-51.675-21.411-18.083 0-33.879 5.901-47.39 17.703-11.225-27.41-29.171-49.393-53.817-65.95-24.646-16.562-51.818-24.842-81.514-24.842-40.349 0-74.802 14.279-103.353 42.83-28.553 28.544-42.825 62.999-42.825 103.351 0 2.474.191 6.567.571 12.275-22.459 10.469-40.349 26.171-53.676 47.106C6.661 299.594 0 322.43 0 347.179c0 35.214 12.517 65.329 37.544 90.358 25.028 25.037 55.15 37.548 90.362 37.548h310.636c30.259 0 56.096-10.711 77.512-32.12 21.413-21.409 32.121-47.246 32.121-77.516-.003-25.505-7.952-48.201-23.849-68.097zm-161.731 10.992L262.38 408.565c-1.711 1.707-3.901 2.566-6.567 2.566-2.664 0-4.854-.859-6.567-2.566L148.75 308.063c-1.713-1.711-2.568-3.901-2.568-6.567 0-2.474.9-4.616 2.708-6.423 1.812-1.808 3.949-2.711 6.423-2.711h63.954V191.865c0-2.474.905-4.616 2.712-6.427 1.809-1.805 3.949-2.708 6.423-2.708h54.823c2.478 0 4.609.9 6.427 2.708 1.804 1.811 2.707 3.953 2.707 6.427v100.497h63.954c2.665 0 4.855.855 6.563 2.566 1.714 1.711 2.562 3.901 2.562 6.567 0 2.294-.944 4.569-2.843 6.849z",fill:"currentColor"})})}},70665:function(e,t,r){r.d(t,{r:function(){return UploadIcon}});var n=r(85893);let UploadIcon=e=>{let{color:t="currentColor",width:r="41px",height:o="30px",...s}=e;return(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:r,height:o,viewBox:"0 0 40.909 30",...s,children:(0,n.jsx)("g",{transform:"translate(0 -73.091)",children:(0,n.jsx)("path",{"data-name":"Path 2125",d:"M39.129,89.827A8.064,8.064,0,0,0,34.58,86.94,5.446,5.446,0,0,0,30,78.546a5.207,5.207,0,0,0-3.537,1.321,10.921,10.921,0,0,0-10.1-6.776,10.511,10.511,0,0,0-7.713,3.2A10.508,10.508,0,0,0,5.454,84q0,.277.043.916A9.528,9.528,0,0,0,0,93.546a9.193,9.193,0,0,0,2.8,6.743,9.191,9.191,0,0,0,6.744,2.8H32.728a8.172,8.172,0,0,0,6.4-13.264Zm-12.06-.575a.656.656,0,0,1-.479.2H21.818v7.5a.691.691,0,0,1-.681.681H17.045a.691.691,0,0,1-.682-.681v-7.5H11.59a.655.655,0,0,1-.681-.681.8.8,0,0,1,.213-.512L18.6,80.783a.722.722,0,0,1,.98,0l7.5,7.5a.663.663,0,0,1,.191.49A.656.656,0,0,1,27.07,89.252Z",transform:"translate(0)",fill:"currentColor"})})})}},91297:function(e,t,r){r.d(t,{Z:function(){return ImportCsv}});var n=r(85893),o=r(70665),s=r(32512);function ImportCsv(e){let{onDrop:t,loading:r,title:u}=e,{getRootProps:a,getInputProps:i}=(0,s.uI)({accept:".csv",multiple:!1,onDrop:t});return(0,n.jsx)("section",{className:"upload",children:(0,n.jsxs)("div",{...a({className:"border-dashed border-2 border-border-base h-36 rounded flex flex-col justify-center items-center cursor-pointer focus:border-accent-400 focus:outline-none p-5"}),children:[(0,n.jsx)("input",{...i()}),r&&(0,n.jsx)("span",{className:"ms-2 h-[30px] w-[30px] animate-spin rounded-full border-2 border-t-2 border-transparent",style:{borderTopColor:"rgb(var(--color-accent))"}}),!r&&(0,n.jsx)(o.r,{className:"text-muted-light"}),(0,n.jsx)("p",{className:"mt-4 text-center text-sm text-body",children:(0,n.jsx)("span",{className:"font-semibold text-accent",children:u})})]})})}},97507:function(e,t,r){r.d(t,{JK:function(){return useImportAttributesMutation},Lq:function(){return useImportProductsMutation},o$:function(){return useImportVariationOptionsMutation}});var n=r(88767),o=r(47869),s=r(22920),u=r(5233),a=r(3737);let i={importCsv:async(e,t)=>{let r=new FormData;r.append("csv",null==t?void 0:t.csv),r.append("shop_id",null==t?void 0:t.shop_id);let n=await a.eN.post(e,r,{headers:{"Content-Type":"multipart/form-data"}});return n.data}},useImportAttributesMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,u.$G)("common");return(0,n.useMutation)(e=>i.importCsv(o.P.IMPORT_ATTRIBUTES,e),{onSuccess:()=>{s.Am.success(t("common:attribute-imported-successfully"))},onError:e=>{var r;s.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))},onSettled:()=>{e.invalidateQueries(o.P.ATTRIBUTES)}})},useImportProductsMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,u.$G)("common");return(0,n.useMutation)(e=>i.importCsv(o.P.IMPORT_PRODUCTS,e),{onSuccess:()=>{s.Am.success(t("common:product-imported-successfully"))},onError:e=>{var r;s.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))},onSettled:()=>{e.invalidateQueries(o.P.PRODUCTS)}})},useImportVariationOptionsMutation=()=>{let e=(0,n.useQueryClient)(),{t}=(0,u.$G)("common");return(0,n.useMutation)(e=>i.importCsv(o.P.IMPORT_VARIATION_OPTIONS,e),{onSuccess:()=>{s.Am.success(t("common:variation-options-imported-successfully"))},onError:e=>{var r;s.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))},onSettled:()=>{e.invalidateQueries(o.P.PRODUCTS)}})}},30042:function(e,t,r){r.d(t,{bg:function(){return useApproveShopMutation},TC:function(){return useCreateShopMutation},mj:function(){return useDisApproveShopMutation},T3:function(){return useInActiveShopsQuery},DZ:function(){return useShopQuery},uL:function(){return useShopsQuery},_3:function(){return useTransferShopOwnershipMutation},D9:function(){return useUpdateShopMutation}});var n=r(93345),o=r(97514),s=r(47869),u=r(16203),a=r(28597),i=r(5233),c=r(11163),l=r(88767),d=r(22920),p=r(3737),m=r(55191);let h={...(0,m.h)(s.P.SHOPS),get(e){let{slug:t}=e;return p.eN.get("".concat(s.P.SHOPS,"/").concat(t))},paginated:e=>{let{name:t,...r}=e;return p.eN.get(s.P.SHOPS,{searchJoin:"and",...r,search:p.eN.formatSearchParams({name:t})})},newOrInActiveShops:e=>{let{is_active:t,name:r,...n}=e;return p.eN.get(s.P.NEW_OR_INACTIVE_SHOPS,{searchJoin:"and",is_active:t,name:r,...n,search:p.eN.formatSearchParams({is_active:t,name:r})})},approve:e=>p.eN.post(s.P.APPROVE_SHOP,e),disapprove:e=>p.eN.post(s.P.DISAPPROVE_SHOP,e),transferShopOwnership:e=>p.eN.post(s.P.TRANSFER_SHOP_OWNERSHIP,e)},useApproveShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.approve,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useDisApproveShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.disapprove,{onSuccess:()=>{d.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useCreateShopMutation=()=>{let e=(0,l.useQueryClient)(),t=(0,c.useRouter)();return(0,l.useMutation)(h.create,{onSuccess:()=>{let{permissions:e}=(0,u.WA)();if((0,u.Ft)(u.M$,e))return t.push(o.Z.adminMyShops);t.push(o.Z.dashboard)},onSettled:()=>{e.invalidateQueries(s.P.SHOPS)}})},useUpdateShopMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,c.useRouter)(),r=(0,l.useQueryClient)();return(0,l.useMutation)(h.update,{onSuccess:async r=>{await t.push("/".concat(null==r?void 0:r.slug,"/edit"),void 0,{locale:n.Config.defaultLanguage}),d.Am.success(e("common:successfully-updated"))},onSettled:()=>{r.invalidateQueries(s.P.SHOPS)}})},useTransferShopOwnershipMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(h.transferShopOwnership,{onSuccess:t=>{var r;d.Am.success("".concat(e("common:successfully-transferred")).concat(null===(r=t.owner)||void 0===r?void 0:r.name))},onSettled:()=>{t.invalidateQueries(s.P.SHOPS)}})},useShopQuery=(e,t)=>{let{slug:r}=e;return(0,l.useQuery)([s.P.SHOPS,{slug:r}],()=>h.get({slug:r}),t)},useShopsQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,l.useQuery)([s.P.SHOPS,e],e=>{let{queryKey:t,pageParam:r}=e;return h.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0});return{shops:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(r),error:n,loading:o}},useInActiveShopsQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,l.useQuery)([s.P.NEW_OR_INACTIVE_SHOPS,e],e=>{let{queryKey:t,pageParam:r}=e;return h.newOrInActiveShops(Object.assign({},t[1],r))},{keepPreviousData:!0});return{shops:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,a.Q)(r),error:n,loading:o}}}}]);