(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4764],{34980:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/new-shops",function(){return n(32776)}])},32776:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return x},default:function(){return NewShopPage}});var s=n(85893),o=n(92072),a=n(97670),i=n(45957),c=n(55846),u=n(5233),r=n(4852),l=n(67294),m=n(37912),f=n(16203),d=n(30042),h=n(10265),w=n(35484),x=!0;function NewShopPage(){let{t:e}=(0,u.$G)(),[t,n]=(0,l.useState)(""),[a,f]=(0,l.useState)(1),[x,_]=(0,l.useState)("created_at"),[p,N]=(0,l.useState)(h.As.Desc),{shops:S,paginatorInfo:g,loading:j,error:b}=(0,d.T3)({name:t,limit:10,page:a,orderBy:x,sortedBy:p,is_active:!1});return j?(0,s.jsx)(c.Z,{text:e("common:text-loading")}):b?(0,s.jsx)(i.Z,{message:b.message}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o.Z,{className:"mb-8 flex flex-col items-center justify-between md:flex-row",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:w-1/4",children:(0,s.jsx)(w.Z,{title:e("common:sidebar-nav-item-shops")})}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center ms-auto md:w-1/2 md:flex-row",children:(0,s.jsx)(m.Z,{onSearch:function(e){let{searchText:t}=e;n(t)}})})]}),(0,s.jsx)(r.Z,{shops:S,paginatorInfo:g,onPagination:function(e){f(e)},onOrder:_,onSort:N})]})}NewShopPage.authenticate={permissions:f.M$},NewShopPage.Layout=a.default}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,9693,9494,5535,8186,1285,1631,7556,8504,131,9774,2888,179],function(){return e(e.s=34980)}),_N_E=e.O()}]);