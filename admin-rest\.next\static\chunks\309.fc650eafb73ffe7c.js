"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{40309:function(e,n,t){t.r(n);var o=t(85893),u=t(71421),s=t(75814),r=t(9140),a=t(41874);n.default=()=>{let{mutate:e,isLoading:n}=(0,a.vQ)(),{data:t}=(0,s.X9)(),{closeModal:i}=(0,s.SO)();return(0,o.jsx)(u.Z,{onCancel:i,onDelete:function(){try{e({id:t}),i()}catch(e){i(),(0,r.e)(e)}},deleteBtnLoading:n})}},41874:function(e,n,t){t.d(n,{x7:function(){return useCreateRefundPolicyMutation},vQ:function(){return useDeleteRefundPolicyMutation},V4:function(){return useRefundPoliciesQuery},m9:function(){return useRefundPolicyQuery},MG:function(){return useUpdateRefundPolicyMutation}});var o=t(11163),u=t.n(o),s=t(88767),r=t(22920),a=t(5233),i=t(97514),l=t(47869),c=t(28597),d=t(55191),f=t(3737);let v={...(0,d.h)(l.P.REFUND_POLICIES),paginated:e=>{let{target:n,title:t,status:o,...u}=e;return f.eN.get(l.P.REFUND_POLICIES,{searchJoin:"and",...u,search:f.eN.formatSearchParams({title:t,target:n,status:o})})}};var g=t(93345);let useCreateRefundPolicyMutation=()=>{let e=(0,s.useQueryClient)(),{t:n}=(0,a.$G)(),t=(0,o.useRouter)();return(0,s.useMutation)(v.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.refundPolicies.list):i.Z.refundPolicies.list;await u().push(e,void 0,{locale:g.Config.defaultLanguage}),r.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.REFUND_POLICIES)},onError:e=>{var t;r.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useDeleteRefundPolicyMutation=()=>{let e=(0,s.useQueryClient)(),{t:n}=(0,a.$G)();return(0,s.useMutation)(v.delete,{onSuccess:()=>{r.Am.success(n("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.REFUND_POLICIES)},onError:e=>{var t;r.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateRefundPolicyMutation=()=>{let{t:e}=(0,a.$G)(),n=(0,o.useRouter)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(v.update,{onSuccess:async t=>{let o=n.query.shop?"/".concat(n.query.shop).concat(i.Z.refundPolicies.list):i.Z.refundPolicies.list;await n.push("".concat(o,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:g.Config.defaultLanguage}),r.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(l.P.REFUND_POLICIES)},onError:n=>{var t;r.Am.error(e("common:".concat(null==n?void 0:null===(t=n.response)||void 0===t?void 0:t.data.message)))}})},useRefundPolicyQuery=e=>{let{slug:n,language:t}=e,{data:o,error:u,isLoading:r}=(0,s.useQuery)([l.P.REFUND_POLICIES,{slug:n,language:t}],()=>v.get({slug:n,language:t}));return{refundPolicy:o,error:u,loading:r}},useRefundPoliciesQuery=e=>{var n;let{data:t,error:o,isLoading:u}=(0,s.useQuery)([l.P.REFUND_POLICIES,e],e=>{let{queryKey:n,pageParam:t}=e;return v.paginated(Object.assign({},n[1],t))},{keepPreviousData:!0});return{refundPolicies:null!==(n=null==t?void 0:t.data)&&void 0!==n?n:[],paginatorInfo:(0,c.Q)(t),error:o,loading:u}}},9140:function(e,n,t){t.d(n,{e:function(){return getErrorMessage}});var o=t(11163),u=t.n(o),s=t(31955);function getErrorMessage(e){let n={message:"",validation:[]};if(e.graphQLErrors)for(let t of e.graphQLErrors){if(t.extensions&&"validation"===t.extensions.category)return n.message=t.message,n.validation=t.extensions.validation,n;t.extensions&&"authorization"===t.extensions.category&&(s.Z.remove("auth_token"),s.Z.remove("auth_permissions"),u().push("/"))}return n.message=e.message,n}}}]);