"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_shop-single_description-modal_tsx"],{

/***/ "./src/components/shop-single/description-modal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/shop-single/description-modal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n\nvar _s = $RefreshSig$();\n\nconst DescriptionView = ()=>{\n    _s();\n    const { data: { content, title } } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalState)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"m-auto flex w-full max-w-[66.125rem] flex-col rounded-xl bg-white p-4 md:rounded-xl md:p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-4 text-xl font-semibold text-muted-black\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shop-single\\\\description-modal.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto leading-8 text-[#666]\",\n                dangerouslySetInnerHTML: {\n                    __html: content\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shop-single\\\\description-modal.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\admin-rest\\\\src\\\\components\\\\shop-single\\\\description-modal.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DescriptionView, \"duXTYTG5fjXM5BD3ZSLQ61AOJ+w=\", false, function() {\n    return [\n        _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_1__.useModalState\n    ];\n});\n_c = DescriptionView;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DescriptionView);\nvar _c;\n$RefreshReg$(_c, \"DescriptionView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9zaG9wLXNpbmdsZS9kZXNjcmlwdGlvbi1tb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9FO0FBRXBFLE1BQU1DLGtCQUFrQjs7SUFDdEIsTUFBTSxFQUNKQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsS0FBSyxFQUFFLEVBQ3pCLEdBQUdKLGlGQUFhQTtJQUNqQixxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUErQ0Y7Ozs7OzswQkFDN0QsOERBQUNDO2dCQUNDQyxXQUFVO2dCQUNWRSx5QkFBeUI7b0JBQUVDLFFBQVFOO2dCQUFROzs7Ozs7Ozs7Ozs7QUFJbkQ7R0FiTUY7O1FBR0FELDZFQUFhQTs7O0tBSGJDO0FBZU4sK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc2hvcC1zaW5nbGUvZGVzY3JpcHRpb24tbW9kYWwudHN4Pzc4MjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTW9kYWxTdGF0ZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9tb2RhbC9tb2RhbC5jb250ZXh0JztcclxuXHJcbmNvbnN0IERlc2NyaXB0aW9uVmlldyA9ICgpID0+IHtcclxuICBjb25zdCB7XHJcbiAgICBkYXRhOiB7IGNvbnRlbnQsIHRpdGxlIH0sXHJcbiAgfSA9IHVzZU1vZGFsU3RhdGUoKTtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtLWF1dG8gZmxleCB3LWZ1bGwgbWF4LXctWzY2LjEyNXJlbV0gZmxleC1jb2wgcm91bmRlZC14bCBiZy13aGl0ZSBwLTQgbWQ6cm91bmRlZC14bCBtZDpwLThcIj5cclxuICAgICAgPGgyIGNsYXNzTmFtZT1cIm1iLTQgdGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtbXV0ZWQtYmxhY2tcIj57dGl0bGV9PC9oMj5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvIGxlYWRpbmctOCB0ZXh0LVsjNjY2XVwiXHJcbiAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBjb250ZW50IH19XHJcbiAgICAgID48L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBEZXNjcmlwdGlvblZpZXc7XHJcbiJdLCJuYW1lcyI6WyJ1c2VNb2RhbFN0YXRlIiwiRGVzY3JpcHRpb25WaWV3IiwiZGF0YSIsImNvbnRlbnQiLCJ0aXRsZSIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/shop-single/description-modal.tsx\n"));

/***/ })

}]);