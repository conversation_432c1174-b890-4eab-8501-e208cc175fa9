(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8544],{85251:function(){},42189:function(e,t,r){"use strict";let l,a;r.d(t,{E:function(){return ew}});var i=r(67294);/*!
 * OverlayScrollbars
 * Version: 2.10.0
 *
 * Copyright (c) <PERSON> | KingSora.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 */let createCache=(e,t)=>{let r;let{o:l,i:a,u:i}=e,o=l,cacheUpdateContextual=(e,t)=>{let l=o,c=t||(a?!a(l,e):l!==e);return(c||i)&&(o=e,r=l),[o,c,r]};return[t?e=>cacheUpdateContextual(t(o,r),e):cacheUpdateContextual,e=>[o,!!e,r]]},o="undefined"!=typeof window&&"undefined"!=typeof HTMLElement&&!!window.document,c=o?window:{},d=Math.max,u=Math.min,g=Math.round,b=Math.abs,v=Math.sign,y=c.cancelAnimationFrame,f=c.requestAnimationFrame,S=c.setTimeout,E=c.clearTimeout,getApi=e=>void 0!==c[e]?c[e]:void 0,C=getApi("MutationObserver"),A=getApi("IntersectionObserver"),w=getApi("ResizeObserver"),O=getApi("ScrollTimeline"),isUndefined=e=>void 0===e,isNull=e=>null===e,isNumber=e=>"number"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isFunction=e=>"function"==typeof e,isArray=e=>Array.isArray(e),isObject=e=>"object"==typeof e&&!isArray(e)&&!isNull(e),isArrayLike=e=>{let t=!!e&&e.length,r=isNumber(t)&&t>-1&&t%1==0;return(!!isArray(e)||!isFunction(e)&&!!r)&&(!(t>0&&isObject(e))||t-1 in e)},isPlainObject=e=>!!e&&e.constructor===Object,isHTMLElement=e=>e instanceof HTMLElement,isElement=e=>e instanceof Element;function each(e,t){if(isArrayLike(e))for(let r=0;r<e.length&&!1!==t(e[r],r,e);r++);else e&&each(Object.keys(e),r=>t(e[r],r,e));return e}let inArray=(e,t)=>e.indexOf(t)>=0,concat=(e,t)=>e.concat(t),push=(e,t,r)=>(!r&&!isString(t)&&isArrayLike(t)?Array.prototype.push.apply(e,t):e.push(t),e),from=e=>Array.from(e||[]),createOrKeepArray=e=>isArray(e)?e:!isString(e)&&isArrayLike(e)?from(e):[e],isEmptyArray=e=>!!e&&!e.length,deduplicateArray=e=>from(new Set(e)),runEachAndClear=(e,t,r)=>{each(e,e=>!e||e.apply(void 0,t||[])),r||(e.length=0)},D="paddingTop",x="paddingRight",T="paddingLeft",L="paddingBottom",P="marginLeft",H="marginRight",k="marginBottom",M="overflowX",I="overflowY",$="width",R="height",N="visible",z="hidden",F="scroll",capitalizeFirstLetter=e=>{let t=String(e||"");return t?t[0].toUpperCase()+t.slice(1):""},equal=(e,t,r,l)=>{if(e&&t){let a=!0;return each(r,r=>{let i=l?l(e[r]):e[r],o=l?l(t[r]):t[r];i!==o&&(a=!1)}),a}return!1},equalWH=(e,t)=>equal(e,t,["w","h"]),equalXY=(e,t)=>equal(e,t,["x","y"]),equalTRBL=(e,t)=>equal(e,t,["t","r","b","l"]),noop=()=>{},bind=(e,...t)=>e.bind(0,...t),selfClearTimeout=e=>{let t;let r=e?S:f,l=e?E:y;return[a=>{l(t),t=r(()=>a(),isFunction(e)?e():e)},()=>l(t)]},debounce=(e,t)=>{let r,l,a,i;let{_:o,v:c,p:d,S:u}=t||{},g=noop,p=function(t){g(),E(r),i=r=l=void 0,g=noop,e.apply(this,t)},mergeParms=e=>u&&l?u(l,e):e,flush=()=>{g!==noop&&p(mergeParms(a)||a)},h=function(){let e=from(arguments),t=isFunction(o)?o():o,u=isNumber(t)&&t>=0;if(u){let o;let u=isFunction(c)?c():c,b=isNumber(u)&&u>=0,v=t>0?S:f,C=t>0?E:y,A=mergeParms(e),w=A||e,O=p.bind(0,w);g(),d&&!i?(O(),i=!0,o=v(()=>i=void 0,t)):(o=v(O,t),b&&!r&&(r=S(flush,u))),g=()=>C(o),l=a=w}else p(e)};return h.m=flush,h},overlayscrollbars_hasOwnProperty=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),keys=e=>e?Object.keys(e):[],assignDeep=(e,t,r,l,a,i,o)=>{let c=[t,r,l,a,i,o];return("object"!=typeof e||isNull(e))&&!isFunction(e)&&(e={}),each(c,t=>{each(t,(r,l)=>{let a=t[l];if(e===a)return!0;let i=isArray(a);if(a&&isPlainObject(a)){let t=e[l],r=t;i&&!isArray(t)?r=[]:i||isPlainObject(t)||(r={}),e[l]=assignDeep(r,a)}else e[l]=i?a.slice():a})}),e},removeUndefinedProperties=(e,t)=>each(assignDeep({},e),(e,r,l)=>{void 0===e?delete l[r]:t&&e&&isPlainObject(e)&&(l[r]=removeUndefinedProperties(e,t))}),isEmptyObject=e=>!keys(e).length,capNumber=(e,t,r)=>d(e,u(t,r)),getDomTokensArray=e=>deduplicateArray((isArray(e)?e:(e||"").split(" ")).filter(e=>e)),getAttr=(e,t)=>e&&e.getAttribute(t),hasAttr=(e,t)=>e&&e.hasAttribute(t),setAttrs=(e,t,r)=>{each(getDomTokensArray(t),t=>{e&&e.setAttribute(t,String(r||""))})},removeAttrs=(e,t)=>{each(getDomTokensArray(t),t=>e&&e.removeAttribute(t))},domTokenListAttr=(e,t)=>{let r=getDomTokensArray(getAttr(e,t)),l=bind(setAttrs,e,t),domTokenListOperation=(e,t)=>{let l=new Set(r);return each(getDomTokensArray(e),e=>{l[t](e)}),from(l).join(" ")};return{O:e=>l(domTokenListOperation(e,"delete")),$:e=>l(domTokenListOperation(e,"add")),C:e=>{let t=getDomTokensArray(e);return t.reduce((e,t)=>e&&r.includes(t),t.length>0)}}},removeAttrClass=(e,t,r)=>(domTokenListAttr(e,t).O(r),bind(addAttrClass,e,t,r)),addAttrClass=(e,t,r)=>(domTokenListAttr(e,t).$(r),bind(removeAttrClass,e,t,r)),addRemoveAttrClass=(e,t,r,l)=>(l?addAttrClass:removeAttrClass)(e,t,r),hasAttrClass=(e,t,r)=>domTokenListAttr(e,t).C(r),createDomTokenListClass=e=>domTokenListAttr(e,"class"),removeClass=(e,t)=>{createDomTokenListClass(e).O(t)},addClass=(e,t)=>(createDomTokenListClass(e).$(t),bind(removeClass,e,t)),find=(e,t)=>{let r=t?isElement(t)&&t:document;return r?from(r.querySelectorAll(e)):[]},findFirst=(e,t)=>{let r=t?isElement(t)&&t:document;return r&&r.querySelector(e)},is=(e,t)=>isElement(e)&&e.matches(t),isBodyElement=e=>is(e,"body"),contents=e=>e?from(e.childNodes):[],overlayscrollbars_parent=e=>e&&e.parentElement,closest=(e,t)=>isElement(e)&&e.closest(t),getFocusedElement=e=>(e||document).activeElement,liesBetween=(e,t,r)=>{let l=closest(e,t),a=e&&findFirst(r,l),i=closest(a,t)===l;return!!l&&!!a&&(l===e||a===e||i&&closest(closest(e,r),t)!==l)},removeElements=e=>{each(createOrKeepArray(e),e=>{let t=overlayscrollbars_parent(e);e&&t&&t.removeChild(e)})},appendChildren=(e,t)=>bind(removeElements,e&&t&&each(createOrKeepArray(t),t=>{t&&e.appendChild(t)})),createDiv=e=>{let t=document.createElement("div");return setAttrs(t,"class",e),t},createDOM=e=>{let t=createDiv();return t.innerHTML=e.trim(),each(contents(t),e=>removeElements(e))},getCSSVal=(e,t)=>e.getPropertyValue(t)||e[t]||"",validFiniteNumber=e=>{let t=e||0;return isFinite(t)?t:0},parseToZeroOrNumber=e=>validFiniteNumber(parseFloat(e||"")),roundCssNumber=e=>Math.round(1e4*e)/1e4,numberToCssPx=e=>`${roundCssNumber(validFiniteNumber(e))}px`;function setStyles(e,t){e&&t&&each(t,(t,r)=>{try{let l=e.style,a=isNull(t)||isBoolean(t)?"":isNumber(t)?numberToCssPx(t):t;0===r.indexOf("--")?l.setProperty(r,a):l[r]=a}catch(e){}})}function getStyles(e,t,r){let l=isString(t),a=l?"":{};if(e){let i=c.getComputedStyle(e,r)||e.style;a=l?getCSSVal(i,t):from(t).reduce((e,t)=>(e[t]=getCSSVal(i,t),e),a)}return a}let topRightBottomLeft=(e,t,r)=>{let l=t?`${t}-`:"",a=r?`-${r}`:"",i=`${l}top${a}`,o=`${l}right${a}`,c=`${l}bottom${a}`,d=`${l}left${a}`,u=getStyles(e,[i,o,c,d]);return{t:parseToZeroOrNumber(u[i]),r:parseToZeroOrNumber(u[o]),b:parseToZeroOrNumber(u[c]),l:parseToZeroOrNumber(u[d])}},getTrasformTranslateValue=(e,t)=>`translate${isObject(e)?`(${e.x},${e.y})`:`${t?"X":"Y"}(${e})`}`,elementHasDimensions=e=>!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length),V={w:0,h:0},getElmWidthHeightProperty=(e,t)=>t?{w:t[`${e}Width`],h:t[`${e}Height`]}:V,getWindowSize=e=>getElmWidthHeightProperty("inner",e||c),_=bind(getElmWidthHeightProperty,"offset"),B=bind(getElmWidthHeightProperty,"client"),j=bind(getElmWidthHeightProperty,"scroll"),getFractionalSize=e=>{let t=parseFloat(getStyles(e,$))||0,r=parseFloat(getStyles(e,R))||0;return{w:t-g(t),h:r-g(r)}},getBoundingClientRect=e=>e.getBoundingClientRect(),hasDimensions=e=>!!e&&elementHasDimensions(e),domRectHasDimensions=e=>!!(e&&(e[R]||e[$])),domRectAppeared=(e,t)=>{let r=domRectHasDimensions(e),l=domRectHasDimensions(t);return!l&&r},removeEventListener=(e,t,r,l)=>{each(getDomTokensArray(t),t=>{e&&e.removeEventListener(t,r,l)})},addEventListener=(e,t,r,l)=>{var a;let i=null==(a=l&&l.H)||a,o=l&&l.I||!1,c=l&&l.A||!1,d={passive:i,capture:o};return bind(runEachAndClear,getDomTokensArray(t).map(t=>{let l=c?a=>{removeEventListener(e,t,l,o),r&&r(a)}:r;return e&&e.addEventListener(t,l,d),bind(removeEventListener,e,t,l,o)}))},stopPropagation=e=>e.stopPropagation(),preventDefault=e=>e.preventDefault(),stopAndPrevent=e=>stopPropagation(e)||preventDefault(e),scrollElementTo=(e,t)=>{let{x:r,y:l}=isNumber(t)?{x:t,y:t}:t||{};isNumber(r)&&(e.scrollLeft=r),isNumber(l)&&(e.scrollTop=l)},getElementScroll=e=>({x:e.scrollLeft,y:e.scrollTop}),getZeroScrollCoordinates=()=>({D:{x:0,y:0},M:{x:0,y:0}}),sanitizeScrollCoordinates=(e,t)=>{let{D:r,M:l}=e,{w:a,h:i}=t,sanitizeAxis=(e,t,r)=>{let l=v(e)*r,a=v(t)*r;if(l===a){let r=b(e),i=b(t);a=r>i?0:a,l=r<i?0:l}return[(l=l===a?0:l)+0,a+0]},[o,c]=sanitizeAxis(r.x,l.x,a),[d,u]=sanitizeAxis(r.y,l.y,i);return{D:{x:o,y:d},M:{x:c,y:u}}},isDefaultDirectionScrollCoordinates=({D:e,M:t})=>{var r,l,a,i;return{x:(r=e.x,l=t.x,0===r&&r<=l),y:(a=e.y,i=t.y,0===a&&a<=i)}},getScrollCoordinatesPercent=({D:e,M:t},r)=>{let getAxis=(e,t,r)=>capNumber(0,1,(e-r)/(e-t)||0);return{x:getAxis(e.x,t.x,r.x),y:getAxis(e.y,t.y,r.y)}},focusElement=e=>{e&&e.focus&&e.focus({preventScroll:!0})},manageListener=(e,t)=>{each(createOrKeepArray(t),e)},createEventListenerHub=e=>{let t=new Map,removeEvent=(e,r)=>{if(e){let l=t.get(e);manageListener(e=>{l&&l[e?"delete":"clear"](e)},r)}else t.forEach(e=>{e.clear()}),t.clear()},addEvent=(e,r)=>{if(isString(e)){let l=t.get(e)||new Set;return t.set(e,l),manageListener(e=>{isFunction(e)&&l.add(e)},r),bind(removeEvent,e,r)}isBoolean(r)&&r&&removeEvent();let l=keys(e),a=[];return each(l,t=>{let r=e[t];r&&push(a,addEvent(t,r))}),bind(runEachAndClear,a)};return addEvent(e||{}),[addEvent,removeEvent,(e,r)=>{each(from(t.get(e)),e=>{r&&!isEmptyArray(r)?e.apply(0,r):e()})}]},opsStringify=e=>JSON.stringify(e,(e,t)=>{if(isFunction(t))throw 0;return t}),getPropByPath=(e,t)=>e?`${t}`.split(".").reduce((e,t)=>e&&overlayscrollbars_hasOwnProperty(e,t)?e[t]:void 0,e):void 0,U={paddingAbsolute:!1,showNativeOverlaidScrollbars:!1,update:{elementEvents:[["img","load"]],debounce:[0,33],attributes:null,ignoreMutation:null},overflow:{x:"scroll",y:"scroll"},scrollbars:{theme:"os-theme-dark",visibility:"auto",autoHide:"never",autoHideDelay:1300,autoHideSuspend:!1,dragScroll:!0,clickScroll:!1,pointers:["mouse","touch","pen"]}},getOptionsDiff=(e,t)=>{let r={},l=concat(keys(t),keys(e));return each(l,l=>{let a=e[l],i=t[l];if(isObject(a)&&isObject(i))assignDeep(r[l]={},getOptionsDiff(a,i)),isEmptyObject(r[l])&&delete r[l];else if(overlayscrollbars_hasOwnProperty(t,l)&&i!==a){let e=!0;if(isArray(a)||isArray(i))try{opsStringify(a)===opsStringify(i)&&(e=!1)}catch(e){}e&&(r[l]=i)}}),r},createOptionCheck=(e,t,r)=>l=>[getPropByPath(e,l),r||void 0!==getPropByPath(t,l)],W="data-overlayscrollbars",q="os-environment",Z=`${q}-scrollbar-hidden`,K=`${W}-initialize`,Y="noClipping",X=`${W}-body`,G=`${W}-viewport`,J="measuring",Q="scrollbarHidden",ee=`${W}-padding`,et=`${W}-content`,er="os-size-observer",en=`${er}-appear`,el=`${er}-listener`,ea="os-scrollbar",ei=`${ea}-rtl`,es=`${ea}-horizontal`,eo=`${ea}-vertical`,ec=`${ea}-track`,ed=`${ea}-handle`,eu=`${ea}-visible`,ep=`${ea}-cornerless`,em=`${ea}-interaction`,eg=`${ea}-unusable`,eb=`${ea}-auto-hide`,ev=`${eb}-hidden`,ey=`${ea}-wheel`,eh=`${ec}-interactive`,ef=`${ed}-interactive`,getNonce=()=>l,createEnvironment=()=>{let getNativeScrollbarSize=(e,t,r)=>{appendChildren(document.body,e),appendChildren(document.body,e);let l=B(e),a=_(e),i=getFractionalSize(t);return r&&removeElements(e),{x:a.h-l.h+i.h,y:a.w-l.w+i.w}},e=`.${q}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${q} div{width:200%;height:200%;margin:10px 0}.${Z}{scrollbar-width:none!important}.${Z}::-webkit-scrollbar,.${Z}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`,t=createDOM(`<div class="${q}"><div></div><style>${e}</style></div>`),r=t[0],l=r.firstChild,a=r.lastChild,i=getNonce();i&&(a.nonce=i);let[o,,d]=createEventListenerHub(),[u,g]=createCache({o:getNativeScrollbarSize(r,l),i:equalXY},bind(getNativeScrollbarSize,r,l,!0)),[b]=g(),v=(e=>{let t=!1,r=addClass(e,Z);try{t="none"===getStyles(e,"scrollbar-width")||"none"===getStyles(e,"display","::-webkit-scrollbar")}catch(e){}return r(),t})(r),y={x:0===b.x,y:0===b.y},f={elements:{host:null,padding:!v,viewport:e=>v&&isBodyElement(e)&&e,content:!1},scrollbars:{slot:!0},cancel:{nativeScrollbarsOverlaid:!1,body:null}},S=assignDeep({},U),E=bind(assignDeep,{},S),C=bind(assignDeep,{},f),A={T:b,k:y,R:v,V:!!O,L:bind(o,"r"),U:C,P:e=>assignDeep(f,e)&&C(),N:E,q:e=>assignDeep(S,e)&&E(),B:assignDeep({},f),F:assignDeep({},S)};if(removeAttrs(r,"style"),removeElements(r),addEventListener(c,"resize",()=>{d("r",[])}),isFunction(c.matchMedia)&&!v&&(!y.x||!y.y)){let addZoomListener=e=>{let t=c.matchMedia(`(resolution: ${c.devicePixelRatio}dppx)`);addEventListener(t,"change",()=>{e(),addZoomListener(e)},{A:!0})};addZoomListener(()=>{let[e,t]=u();assignDeep(A.T,e),d("r",[t])})}return A},getEnvironment=()=>(a||(a=createEnvironment()),a),resolveInitialization=(e,t)=>isFunction(t)?t.apply(0,e):t,staticInitializationElement=(e,t,r,l)=>{let a=isUndefined(l)?r:l,i=resolveInitialization(e,a);return i||t.apply(0,e)},dynamicInitializationElement=(e,t,r,l)=>{let a=isUndefined(l)?r:l,i=resolveInitialization(e,a);return!!i&&(isHTMLElement(i)?i:t.apply(0,e))},cancelInitialization=(e,t)=>{let{nativeScrollbarsOverlaid:r,body:l}=t||{},{k:a,R:i,U:o}=getEnvironment(),{nativeScrollbarsOverlaid:c,body:d}=o().cancel,u=isUndefined(l)?d:l,g=(a.x||a.y)&&(null!=r?r:c),b=e&&(isNull(u)?!i:u);return!!g||!!b},eS=new WeakMap,addInstance=(e,t)=>{eS.set(e,t)},removeInstance=e=>{eS.delete(e)},getInstance=e=>eS.get(e),createEventContentChange=(e,t,r)=>{let l=!1,a=!!r&&new WeakMap,updateElements=i=>{if(a&&r){let o=r.map(t=>{let[r,l]=t||[],a=l&&r?(i||find)(r,e):[];return[a,l]});each(o,r=>each(r[0],i=>{let o=r[1],c=a.get(i)||[],d=e.contains(i);if(d&&o){let e=addEventListener(i,o,r=>{l?(e(),a.delete(i)):t(r)});a.set(i,push(c,e))}else runEachAndClear(c),a.delete(i)}))}};return updateElements(),[()=>{l=!0},updateElements]},createDOMObserver=(e,t,r,l)=>{let a=!1,{j:i,X:o,Y:c,W:d,J:u,G:g}=l||{},b=debounce(()=>a&&r(!0),{_:33,v:99}),[v,y]=createEventContentChange(e,b,c),f=o||[],S=concat(i||[],f),observerCallback=(a,i)=>{if(!isEmptyArray(i)){let o=u||noop,c=g||noop,b=[],v=[],S=!1,E=!1;if(each(i,r=>{let{attributeName:a,target:i,type:u,oldValue:g,addedNodes:y,removedNodes:C}=r,A="attributes"===u,w=e===i,O=A&&a,D=O&&getAttr(i,a||""),x=isString(D)?D:null,T=O&&g!==x,L=inArray(f,a)&&T;if(t&&("childList"===u||!w)){let t=A&&T,u=t&&d&&is(i,d),v=u?!o(i,a,g,x):!A||t,f=v&&!c(r,!!u,e,l);each(y,e=>push(b,e)),each(C,e=>push(b,e)),E=E||f}!t&&w&&T&&!o(i,a,g,x)&&(push(v,a),S=S||L)}),y(e=>deduplicateArray(b).reduce((t,r)=>(push(t,find(e,r)),is(r,e)?push(t,r):t),[])),t)return!a&&E&&r(!1),[!1];if(!isEmptyArray(v)||S){let e=[deduplicateArray(v),S];return a||r.apply(0,e),e}}},E=new C(bind(observerCallback,!1));return[()=>(E.observe(e,{attributes:!0,attributeOldValue:!0,attributeFilter:S,subtree:t,childList:t,characterData:t}),a=!0,()=>{a&&(v(),E.disconnect(),a=!1)}),()=>{if(a)return b.m(),observerCallback(!0,E.takeRecords())}]},eE={},eC={},addPlugins=e=>{each(e,e=>each(e,(t,r)=>{eE[r]=e[r]}))},registerPluginModuleInstances=(e,t,r)=>keys(e).map(l=>{let{static:a,instance:i}=e[l],[o,c,d]=r||[],u=r?i:a;if(u){let e=r?u(o,c,t):u(t);return(d||eC)[l]=e}}),getStaticPluginModuleInstance=e=>eC[e],getShowNativeOverlaidScrollbars=(e,t)=>{let{k:r}=t,[l,a]=e("showNativeOverlaidScrollbars");return[l&&r.x&&r.y,a]},overflowIsVisible=e=>0===e.indexOf(N),createViewportOverflowState=(e,t)=>{let getAxisOverflowStyle=(e,t,r,l)=>{let a=e===N?z:e.replace(`${N}-`,""),i=overflowIsVisible(e),o=overflowIsVisible(r);if(!t&&!l)return z;if(i&&o)return N;if(i){let e=t?N:z;return t&&l?a:e}let c=o&&l?N:z;return t?a:c},r={x:getAxisOverflowStyle(t.x,e.x,t.y,e.y),y:getAxisOverflowStyle(t.y,e.y,t.x,e.x)};return{K:r,Z:{x:r.x===F,y:r.y===F}}},eA="__osScrollbarsHidingPlugin",createSizeObserver=(e,t,r)=>{let{dt:l}=r||{},a=getStaticPluginModuleInstance("__osSizeObserverPlugin"),[i]=createCache({o:!1,u:!0});return()=>{let r=[],o=createDOM(`<div class="${er}"><div class="${el}"></div></div>`),c=o[0],d=c.firstChild,onSizeChangedCallbackProxy=e=>{let r=e instanceof ResizeObserverEntry,l=!1,a=!1;if(r){let[t,,r]=i(e.contentRect),o=domRectHasDimensions(t);l=!(a=domRectAppeared(t,r))&&!o}else a=!0===e;l||t({ft:!0,dt:a})};if(w){let e=new w(e=>onSizeChangedCallbackProxy(e.pop()));e.observe(d),push(r,()=>{e.disconnect()})}else{if(!a)return noop;let[e,t]=a(d,onSizeChangedCallbackProxy,l);push(r,concat([addClass(c,en),addEventListener(c,"animationstart",e)],t))}return bind(runEachAndClear,push(r,appendChildren(e,c)))}},createTrinsicObserver=(e,t)=>{let r;let isHeightIntrinsic=e=>0===e.h||e.isIntersecting||e.intersectionRatio>0,l=createDiv("os-trinsic-observer"),[a]=createCache({o:!1}),triggerOnTrinsicChangedCallback=(e,r)=>{if(e){let l=a(isHeightIntrinsic(e)),[,i]=l;return i&&!r&&t(l)&&[l]}},intersectionObserverCallback=(e,t)=>triggerOnTrinsicChangedCallback(t.pop(),e);return[()=>{let t=[];if(A)(r=new A(bind(intersectionObserverCallback,!1),{root:e})).observe(l),push(t,()=>{r.disconnect()});else{let onSizeChanged=()=>{let e=_(l);triggerOnTrinsicChangedCallback(e)};push(t,createSizeObserver(l,onSizeChanged)()),onSizeChanged()}return bind(runEachAndClear,push(t,appendChildren(e,l)))},()=>r&&intersectionObserverCallback(!0,r.takeRecords())]},createObserversSetup=(e,t,r,l)=>{let a,i,o,c,d,u;let g=`[${W}]`,b=`[${G}]`,v=["id","class","style","open","wrap","cols","rows"],{vt:y,ht:f,ot:S,gt:E,bt:C,nt:A,wt:O,yt:D,St:x,Ot:T}=e,getDirectionIsRTL=e=>"rtl"===getStyles(e,"direction"),L={$t:!1,ct:getDirectionIsRTL(y)},P=getEnvironment(),H=getStaticPluginModuleInstance(eA),[k]=createCache({i:equalWH,o:{w:0,h:0}},()=>{let l=H&&H.tt(e,t,L,P,r).ut,a=!(O&&A)&&hasAttrClass(f,W,Y),i=!A&&D("arrange"),o=i&&getElementScroll(E),c=o&&T(),d=x(J,a),u=i&&l&&l()[0],g=j(S),b=getFractionalSize(S);return u&&u(),scrollElementTo(E,o),c&&c(),a&&d(),{w:g.w+b.w,h:g.h+b.h}}),M=debounce(l,{_:()=>a,v:()=>i,S(e,t){let[r]=e,[l]=t;return[concat(keys(r),keys(l)).reduce((e,t)=>(e[t]=r[t]||l[t],e),{})]}}),setDirection=e=>{let t=getDirectionIsRTL(y);assignDeep(e,{Ct:u!==t}),assignDeep(L,{ct:t}),u=t},onTrinsicChanged=(e,t)=>{let[r,a]=e,i={xt:a};return assignDeep(L,{$t:r}),t||l(i),i},onSizeChanged=({ft:e,dt:t})=>{let r=e&&!t,a=!r&&P.R?M:l,i={ft:e||t,dt:t};setDirection(i),a(i)},onContentMutation=(e,t)=>{let[,r]=k(),a={Ht:r};setDirection(a);let i=e?l:M;return r&&!t&&i(a),a},onHostMutation=(e,t,r)=>{let l={Et:t};return setDirection(l),t&&!r&&M(l),l},[I,$]=C?createTrinsicObserver(f,onTrinsicChanged):[],R=!A&&createSizeObserver(f,onSizeChanged,{dt:!0}),[N,z]=createDOMObserver(f,!1,onHostMutation,{X:v,j:v}),F=A&&w&&new w(e=>{let t=e[e.length-1].contentRect;onSizeChanged({ft:!0,dt:domRectAppeared(t,d)}),d=t}),V=debounce(()=>{let[,e]=k();l({Ht:e})},{_:222,p:!0});return[()=>{F&&F.observe(f);let e=R&&R(),t=I&&I(),r=N(),l=P.L(e=>{e?M({zt:e}):V()});return()=>{F&&F.disconnect(),e&&e(),t&&t(),c&&c(),r(),l()}},({It:e,At:t,Dt:r})=>{let l={},[d]=e("update.ignoreMutation"),[u,y]=e("update.attributes"),[f,E]=e("update.elementEvents"),[w,O]=e("update.debounce"),D=t||r,ignoreMutationFromOptions=e=>isFunction(d)&&d(e);if(E||y){o&&o(),c&&c();let[e,t]=createDOMObserver(C||S,!0,onContentMutation,{j:concat(v,u||[]),Y:f,W:g,G:(e,t)=>{let{target:r,attributeName:l}=e,a=!t&&!!l&&!A&&liesBetween(r,g,b);return a||!!closest(r,`.${ea}`)||!!ignoreMutationFromOptions(e)}});c=e(),o=t}if(O){if(M.m(),isArray(w)){let e=w[0],t=w[1];a=isNumber(e)&&e,i=isNumber(t)&&t}else a=!!isNumber(w)&&w,i=!1}if(D){let e=z(),t=$&&$(),r=o&&o();e&&assignDeep(l,onHostMutation(e[0],e[1],D)),t&&assignDeep(l,onTrinsicChanged(t[0],D)),r&&assignDeep(l,onContentMutation(r[0],D))}return setDirection(l),l},L]},createScrollbarsSetupElements=(e,t,r,l)=>{let a="--os-scroll-percent",{U:i}=getEnvironment(),{scrollbars:o}=i(),{slot:c}=o,{vt:d,ht:u,ot:g,Mt:b,gt:v,wt:y,nt:f}=t,{scrollbars:S}=b?{}:e,{slot:E}=S||{},C=[],A=[],w=[],D=dynamicInitializationElement([d,u,g],()=>f&&y?d:u,c,E),initScrollTimeline=e=>{if(O){let t=new O({source:v,axis:e});return{kt:e=>{let r=e.Tt.animate({clear:["left"],[a]:[0,1]},{timeline:t});return()=>r.cancel()}}}},x={x:initScrollTimeline("x"),y:initScrollTimeline("y")},getViewportPercent=()=>{let{Rt:e,Vt:t}=r,getAxisValue=(e,t)=>capNumber(0,1,e/(e+t)||0);return{x:getAxisValue(t.x,e.x),y:getAxisValue(t.y,e.y)}},scrollbarStructureAddRemoveClass=(e,t,r)=>{let l=r?addClass:removeClass;each(e,e=>{l(e.Tt,t)})},scrollbarStyle=(e,t)=>{each(e,e=>{let[r,l]=t(e);setStyles(r,l)})},scrollbarsAddRemoveClass=(e,t,r)=>{let l=isBoolean(r),a=!l||r,i=!l||!r;a&&scrollbarStructureAddRemoveClass(A,e,t),i&&scrollbarStructureAddRemoveClass(w,e,t)},generateScrollbarDOM=e=>{let t=e?"x":"y",r=e?es:eo,a=createDiv(`${ea} ${r}`),i=createDiv(ec),o=createDiv(ed),c={Tt:a,Ut:i,Pt:o},d=x[t];return push(e?A:w,c),push(C,[appendChildren(a,i),appendChildren(i,o),bind(removeElements,a),d&&d.kt(c),l(c,scrollbarsAddRemoveClass,e)]),c},T=bind(generateScrollbarDOM,!0),L=bind(generateScrollbarDOM,!1);return T(),L(),[{Nt:()=>{let e=getViewportPercent(),createScrollbarStyleFn=e=>t=>[t.Tt,{"--os-viewport-percent":roundCssNumber(e)+""}];scrollbarStyle(A,createScrollbarStyleFn(e.x)),scrollbarStyle(w,createScrollbarStyleFn(e.y))},qt:()=>{if(!O){let{Lt:e}=r,t=getScrollCoordinatesPercent(e,getElementScroll(v)),createScrollbarStyleFn=e=>t=>[t.Tt,{[a]:roundCssNumber(e)+""}];scrollbarStyle(A,createScrollbarStyleFn(t.x)),scrollbarStyle(w,createScrollbarStyleFn(t.y))}},Bt:()=>{let{Lt:e}=r,t=isDefaultDirectionScrollCoordinates(e),createScrollbarStyleFn=e=>t=>[t.Tt,{"--os-scroll-direction":e?"0":"1"}];scrollbarStyle(A,createScrollbarStyleFn(t.x)),scrollbarStyle(w,createScrollbarStyleFn(t.y))},Ft:()=>{if(f&&!y){let{Rt:e,Lt:t}=r,l=isDefaultDirectionScrollCoordinates(t),a=getScrollCoordinatesPercent(t,getElementScroll(v)),styleScrollbarPosition=t=>{let{Tt:r}=t,i=overlayscrollbars_parent(r)===g&&r,getTranslateValue=(e,t,r)=>{let l=t*e;return numberToCssPx(r?l:-l)};return[i,i&&{transform:getTrasformTranslateValue({x:getTranslateValue(a.x,e.x,l.x),y:getTranslateValue(a.y,e.y,l.y)})}]};scrollbarStyle(A,styleScrollbarPosition),scrollbarStyle(w,styleScrollbarPosition)}},jt:scrollbarsAddRemoveClass,Xt:{Yt:A,Wt:T,Jt:bind(scrollbarStyle,A)},Gt:{Yt:w,Wt:L,Jt:bind(scrollbarStyle,w)}},()=>(appendChildren(D,A[0].Tt),appendChildren(D,w[0].Tt),bind(runEachAndClear,C))]},createScrollbarsSetupEvents=(e,t,r,l)=>(a,i,o)=>{let{ht:c,ot:d,nt:u,gt:v,Kt:y,Ot:f}=t,{Tt:E,Ut:C,Pt:A}=a,[w,O]=selfClearTimeout(333),[D,x]=selfClearTimeout(444),scrollOffsetElementScrollBy=e=>{isFunction(v.scrollBy)&&v.scrollBy({behavior:"smooth",left:e.x,top:e.y})},T=!0;return bind(runEachAndClear,[addEventListener(A,"pointermove pointerleave",l),addEventListener(E,"pointerenter",()=>{i(em,!0)}),addEventListener(E,"pointerleave pointercancel",()=>{i(em,!1)}),!u&&addEventListener(E,"mousedown",()=>{let e=getFocusedElement();(hasAttr(e,G)||hasAttr(e,W)||e===document.body)&&S(bind(focusElement,d),25)}),addEventListener(E,"wheel",e=>{let{deltaX:t,deltaY:r,deltaMode:l}=e;T&&0===l&&overlayscrollbars_parent(E)===c&&scrollOffsetElementScrollBy({x:t,y:r}),T=!1,i(ey,!0),w(()=>{T=!0,i(ey)}),preventDefault(e)},{H:!1,I:!0}),addEventListener(E,"pointerdown",bind(addEventListener,y,"click",stopAndPrevent,{A:!0,I:!0,H:!1}),{I:!0}),(()=>{let t="pointerup pointercancel lostpointercapture",l=`client${o?"X":"Y"}`,a=o?$:R,i=o?"left":"top",c=o?"w":"h",d=o?"x":"y",createRelativeHandleMove=(e,t)=>l=>{let{Rt:a}=r,i=_(C)[c]-_(A)[c],o=t*l/i*a[d];scrollElementTo(v,{[d]:e+o})},u=[];return addEventListener(C,"pointerdown",r=>{let o=closest(r.target,`.${ed}`)===A,S=o?A:C,E=e.scrollbars,w=E[o?"dragScroll":"clickScroll"],{button:O,isPrimary:T,pointerType:L}=r,{pointers:P}=E,H=0===O&&T&&w&&(P||[]).includes(L);if(H){runEachAndClear(u),x();let e=!o&&(r.shiftKey||"instant"===w),E=bind(getBoundingClientRect,A),O=bind(getBoundingClientRect,C),T=g(getBoundingClientRect(v)[a])/_(v)[c]||1,L=createRelativeHandleMove(getElementScroll(v)[d],1/T),P=r[l],H=E(),k=O(),M=H[a],I=(H||E())[i]-(k||O())[i]+M/2,$=P-k[i],R=o?0:$-I,releasePointerCapture=e=>{runEachAndClear(F),S.releasePointerCapture(e.pointerId)},N=o||e,z=f(),F=[addEventListener(y,t,releasePointerCapture),addEventListener(y,"selectstart",e=>preventDefault(e),{H:!1}),addEventListener(C,t,releasePointerCapture),N&&addEventListener(C,"pointermove",e=>L(R+(e[l]-P))),N&&(()=>{let e=getElementScroll(v);z();let t=getElementScroll(v),r={x:t.x-e.x,y:t.y-e.y};(b(r.x)>3||b(r.y)>3)&&(f(),scrollElementTo(v,e),scrollOffsetElementScrollBy(r),D(z))})];if(S.setPointerCapture(r.pointerId),e)L(R);else if(!o){let e=getStaticPluginModuleInstance("__osClickScrollPlugin");if(e){let t=e(L,R,M,e=>{e?z():push(F,z)});push(F,t),push(u,bind(t,!0))}}}})})(),O,x])},createScrollbarsSetup=(e,t,r,l,a,i)=>{let o,c,d,u,g;let b=noop,v=0,isHoverablePointerType=e=>"mouse"===e.pointerType,[y,f]=selfClearTimeout(),[S,E]=selfClearTimeout(100),[C,A]=selfClearTimeout(100),[w,O]=selfClearTimeout(()=>v),[D,x]=createScrollbarsSetupElements(e,a,l,createScrollbarsSetupEvents(t,a,l,e=>isHoverablePointerType(e)&&manageScrollbarsAutoHideInstantInteraction())),{ht:T,Qt:L,wt:P}=a,{jt:H,Nt:k,qt:M,Bt:I,Ft:$}=D,manageScrollbarsAutoHide=(e,t)=>{if(O(),e)H(ev);else{let e=bind(H,ev,!0);v>0&&!t?w(e):e()}},manageScrollbarsAutoHideInstantInteraction=()=>{(d?o:u)||(manageScrollbarsAutoHide(!0),S(()=>{manageScrollbarsAutoHide(!1)}))},manageAutoHideSuspension=e=>{H(eb,e,!0),H(eb,e,!1)},onHostMouseEnter=e=>{isHoverablePointerType(e)&&(o=d,d&&manageScrollbarsAutoHide(!0))},R=[O,E,A,f,()=>b(),addEventListener(T,"pointerover",onHostMouseEnter,{A:!0}),addEventListener(T,"pointerenter",onHostMouseEnter),addEventListener(T,"pointerleave",e=>{isHoverablePointerType(e)&&(o=!1,d&&manageScrollbarsAutoHide(!1))}),addEventListener(T,"pointermove",e=>{isHoverablePointerType(e)&&c&&manageScrollbarsAutoHideInstantInteraction()}),addEventListener(L,"scroll",e=>{y(()=>{M(),manageScrollbarsAutoHideInstantInteraction()}),i(e),$()})];return[()=>bind(runEachAndClear,push(R,x())),({It:e,Dt:t,Zt:a,tn:i})=>{let{nn:o,sn:y,en:f,cn:S}=i||{},{Ct:E,dt:A}=a||{},{ct:w}=r,{k:O}=getEnvironment(),{K:D,rn:x}=l,[T,R]=e("showNativeOverlaidScrollbars"),[z,V]=e("scrollbars.theme"),[_,B]=e("scrollbars.visibility"),[j,U]=e("scrollbars.autoHide"),[W,q]=e("scrollbars.autoHideSuspend"),[Z]=e("scrollbars.autoHideDelay"),[K,Y]=e("scrollbars.dragScroll"),[X,G]=e("scrollbars.clickScroll"),[J,Q]=e("overflow"),ee=x.x||x.y,et=T&&O.x&&O.y,setScrollbarVisibility=(e,t,r)=>{let l=e.includes(F)&&(_===N||"auto"===_&&t===F);return H(eu,l,r),l};if(v=Z,A&&!t&&(W&&ee?(manageAutoHideSuspension(!1),b(),C(()=>{b=addEventListener(L,"scroll",bind(manageAutoHideSuspension,!0),{A:!0})})):manageAutoHideSuspension(!0)),R&&H("os-theme-none",et),V&&(H(g),H(z,!0),g=z),q&&!W&&manageAutoHideSuspension(!0),U&&(c="move"===j,d="leave"===j,manageScrollbarsAutoHide(u="never"===j,!0)),Y&&H(ef,K),G&&H(eh,!!X),f||B||Q){let e=setScrollbarVisibility(J.x,D.x,!0),t=setScrollbarVisibility(J.y,D.y,!1),r=e&&t;H(ep,!r)}(o||y||S||E||t)&&(M(),k(),$(),S&&I(),H(eg,!x.x,!0),H(eg,!x.y,!1),H(ei,w&&!P))},{},D]},createStructureSetupElements=e=>{let t=getEnvironment(),{U:r,R:l}=t,{elements:a}=r(),{padding:i,viewport:o,content:d}=a,u=isHTMLElement(e),g=u?{}:e,{elements:b}=g,{padding:v,viewport:y,content:f}=b||{},S=u?e:g.target,E=isBodyElement(S),C=S.ownerDocument,A=C.documentElement,getDocumentWindow=()=>C.defaultView||c,w=bind(staticInitializationElement,[S]),O=bind(dynamicInitializationElement,[S]),D=bind(createDiv,""),x=bind(w,D,o),T=bind(O,D,d),L=x(y),P=L===S,H=P&&E,k=!P&&T(f),$=!P&&L===k,R=H?A:L,N=H?R:S,z=!P&&O(D,i,v),F=!$&&k,V=[F,R,z,N].map(e=>isHTMLElement(e)&&!overlayscrollbars_parent(e)&&e),elementIsGenerated=e=>e&&inArray(V,e),B=!elementIsGenerated(R)&&(e=>{let t=_(e),r=j(e),l=getStyles(e,M),a=getStyles(e,I);return r.w-t.w>0&&!overflowIsVisible(l)||r.h-t.h>0&&!overflowIsVisible(a)})(R)?R:S,U=H?A:R,q=H?C:R,Z={vt:S,ht:N,ot:R,ln:z,bt:F,gt:U,Qt:q,an:E?A:B,Kt:C,wt:E,Mt:u,nt:P,un:getDocumentWindow,yt:e=>hasAttrClass(R,G,e),St:(e,t)=>addRemoveAttrClass(R,G,e,t),Ot:()=>addRemoveAttrClass(U,G,"scrolling",!0)},{vt:Y,ht:J,ln:er,ot:en,bt:el}=Z,ea=[()=>{removeAttrs(J,[W,K]),removeAttrs(Y,K),E&&removeAttrs(A,[K,W])}],ei=contents([el,en,er,J,Y].find(e=>e&&!elementIsGenerated(e))),es=H?Y:el||en,eo=bind(runEachAndClear,ea);return[Z,()=>{let e=getDocumentWindow(),t=getFocusedElement(),unwrap=e=>{appendChildren(overlayscrollbars_parent(e),contents(e)),removeElements(e)},prepareWrapUnwrapFocus=e=>addEventListener(e,"focusin focusout focus blur",stopAndPrevent,{I:!0,H:!1}),r="tabindex",a=getAttr(en,r),i=prepareWrapUnwrapFocus(t);return setAttrs(J,W,P?"":"host"),setAttrs(er,ee,""),setAttrs(en,G,""),setAttrs(el,et,""),!P&&(setAttrs(en,r,a||"-1"),E&&setAttrs(A,X,"")),appendChildren(es,ei),appendChildren(J,er),appendChildren(er||J,!P&&en),appendChildren(en,el),push(ea,[i,()=>{let e=getFocusedElement(),t=elementIsGenerated(en),l=t&&e===en?Y:e,i=prepareWrapUnwrapFocus(l);removeAttrs(er,ee),removeAttrs(el,et),removeAttrs(en,G),E&&removeAttrs(A,X),a?setAttrs(en,r,a):removeAttrs(en,r),elementIsGenerated(el)&&unwrap(el),t&&unwrap(en),elementIsGenerated(er)&&unwrap(er),focusElement(l),i()}]),l&&!P&&(addAttrClass(en,G,Q),push(ea,bind(removeAttrs,en,G))),focusElement(!P&&E&&t===Y&&e.top===e?en:t),i(),ei=0,eo},eo]},createTrinsicUpdateSegment=({bt:e})=>({Zt:t,_n:r,Dt:l})=>{let{xt:a}=t||{},{$t:i}=r,o=e&&(a||l);o&&setStyles(e,{[R]:i&&"100%"})},createPaddingUpdateSegment=({ht:e,ln:t,ot:r,nt:l},a)=>{let[i,o]=createCache({i:equalTRBL,o:topRightBottomLeft()},bind(topRightBottomLeft,e,"padding",""));return({It:e,Zt:c,_n:d,Dt:u})=>{let[g,b]=o(u),{R:v}=getEnvironment(),{ft:y,Ht:f,Ct:S}=c||{},{ct:E}=d,[C,A]=e("paddingAbsolute"),w=u||f;(y||b||w)&&([g,b]=i(u));let O=!l&&(A||S||b);if(O){let e=!C||!t&&!v,l=g.r+g.l,i=g.t+g.b,o={[H]:e&&!E?-l:0,[k]:e?-i:0,[P]:e&&E?-l:0,top:e?-g.t:0,right:e?E?-g.r:"auto":0,left:e?E?"auto":-g.l:0,[$]:e&&`calc(100% + ${l}px)`},c={[D]:e?g.t:0,[x]:e?g.r:0,[L]:e?g.b:0,[T]:e?g.l:0};setStyles(t||r,o),setStyles(r,c),assignDeep(a,{ln:g,dn:!e,rt:t?c:assignDeep({},o,c)})}return{fn:O}}},createOverflowUpdateSegment=(e,t)=>{let r=getEnvironment(),{ht:l,ln:a,ot:i,nt:o,Qt:u,gt:g,wt:b,St:v,un:y}=e,{R:S}=r,E=b&&o,C=bind(d,0),A={display:()=>!1,direction:e=>"ltr"!==e,flexDirection:e=>e.endsWith("-reverse"),writingMode:e=>"horizontal-tb"!==e},w=keys(A),O={i:equalWH,o:{w:0,h:0}},D={i:equalXY,o:{}},setMeasuringMode=e=>{v(J,!E&&e)},getMeasuredScrollCoordinates=e=>{let t=w.some(t=>{let r=e[t];return r&&A[t](r)});if(!t)return{D:{x:0,y:0},M:{x:1,y:1}};setMeasuringMode(!0);let r=getElementScroll(g),l=v("noContent",!0),a=addEventListener(u,F,e=>{let t=getElementScroll(g);e.isTrusted&&t.x===r.x&&t.y===r.y&&stopPropagation(e)},{I:!0,A:!0});scrollElementTo(g,{x:0,y:0}),l();let i=getElementScroll(g),o=j(g);scrollElementTo(g,{x:o.w,y:o.h});let c=getElementScroll(g);scrollElementTo(g,{x:c.x-i.x<1&&-o.w,y:c.y-i.y<1&&-o.h});let d=getElementScroll(g);return scrollElementTo(g,r),f(()=>a()),{D:i,M:d}},getOverflowAmount=(e,t)=>{let r=c.devicePixelRatio%1!=0?1:0,l={w:C(e.w-t.w),h:C(e.h-t.h)};return{w:l.w>r?l.w:0,h:l.h>r?l.h:0}},[x,T]=createCache(O,bind(getFractionalSize,i)),[L,P]=createCache(O,bind(j,i)),[H,k]=createCache(O),[$]=createCache(D),[R,V]=createCache(O),[_]=createCache(D),[U]=createCache({i:(e,t)=>equal(e,t,w),o:{}},()=>hasDimensions(i)?getStyles(i,w):{}),[q,Z]=createCache({i:(e,t)=>equalXY(e.D,t.D)&&equalXY(e.M,t.M),o:getZeroScrollCoordinates()}),K=getStaticPluginModuleInstance(eA),createViewportOverflowStyleClassName=(e,t)=>{let r=t?M:I;return`${r}${capitalizeFirstLetter(e)}`},setViewportOverflowStyle=e=>{let createAllOverflowStyleClassNames=e=>[N,z,F].map(t=>createViewportOverflowStyleClassName(t,e)),t=createAllOverflowStyleClassNames(!0).concat(createAllOverflowStyleClassNames()).join(" ");v(t),v(keys(e).map(t=>createViewportOverflowStyleClassName(e[t],"x"===t)).join(" "),!0)};return({It:o,Zt:c,_n:d,Dt:u},{fn:g})=>{let{ft:b,Ht:f,Ct:A,dt:w,zt:O}=c||{},D=K&&K.tt(e,t,d,r,o),{it:M,ut:I,_t:N}=D||{},[z,F]=getShowNativeOverlaidScrollbars(o,r),[j,X]=o("overflow"),G=overflowIsVisible(j.x),J=overflowIsVisible(j.y),et=T(u),er=P(u),en=k(u),el=V(u);F&&S&&v(Q,!z);{hasAttrClass(l,W,Y)&&setMeasuringMode(!0);let[e]=I?I():[],[t]=et=x(u),[r]=er=L(u),a=B(i),o=E&&getWindowSize(y()),c={w:C(r.w+t.w),h:C(r.h+t.h)},d={w:C((o?o.w:a.w+C(a.w-r.w))+t.w),h:C((o?o.h:a.h+C(a.h-r.h))+t.h)};e&&e(),el=R(d),en=H(getOverflowAmount(c,d),u)}let[ea,ei]=el,[es,eo]=en,[ec,ed]=er,[eu,ep]=et,[em,eg]=$({x:es.w>0,y:es.h>0}),eb=G&&J&&(em.x||em.y)||G&&em.x&&!em.y||J&&em.y&&!em.x,ev=createViewportOverflowState(em,j),[ey,eh]=_(ev.K),[ef,eS]=U(u),eE=A||w||eS||eg||u,[eC,eA]=eE?q(getMeasuredScrollCoordinates(ef),u):Z();return eh&&setViewportOverflowStyle(ev.K),N&&M&&setStyles(i,N(ev,d,M(ev,ec,eu))),setMeasuringMode(!1),addRemoveAttrClass(l,W,Y,eb),addRemoveAttrClass(a,ee,Y,eb),assignDeep(t,{K:ey,Vt:{x:ea.w,y:ea.h},Rt:{x:es.w,y:es.h},rn:em,Lt:sanitizeScrollCoordinates(eC,es)}),{en:eh,nn:ei,sn:eo,cn:eA||eo,vn:eE}}},createStructureSetup=e=>{let[t,r,l]=createStructureSetupElements(e),a={ln:{t:0,r:0,b:0,l:0},dn:!1,rt:{[H]:0,[k]:0,[P]:0,[D]:0,[x]:0,[L]:0,[T]:0},Vt:{x:0,y:0},Rt:{x:0,y:0},K:{x:z,y:z},rn:{x:!1,y:!1},Lt:getZeroScrollCoordinates()},{vt:i,gt:o,nt:c,Ot:d}=t,{R:u,k:g}=getEnvironment(),b=!u&&(g.x||g.y),v=[createTrinsicUpdateSegment(t),createPaddingUpdateSegment(t,a),createOverflowUpdateSegment(t,a)];return[r,e=>{let t={},r=b&&getElementScroll(o),l=r&&d();return each(v,r=>{assignDeep(t,r(e,t)||{})}),scrollElementTo(o,r),l&&l(),c||scrollElementTo(i,0),t},a,t,l]},createSetups=(e,t,r,l,a)=>{let i=!1,o=createOptionCheck(t,{}),[c,d,u,g,b]=createStructureSetup(e),[v,y,f]=createObserversSetup(g,u,o,e=>{update({},e)}),[S,E,,C]=createScrollbarsSetup(e,t,f,u,g,a),updateHintsAreTruthy=e=>keys(e).some(t=>!!e[t]),update=(e,a)=>{if(r())return!1;let{pn:o,Dt:c,At:u,hn:g}=e,b=o||{},v=!!c||!i,S={It:createOptionCheck(t,b,v),pn:b,Dt:v};if(g)return E(S),!1;let C=a||y(assignDeep({},S,{At:u})),A=d(assignDeep({},S,{_n:f,Zt:C}));E(assignDeep({},S,{Zt:C,tn:A}));let w=updateHintsAreTruthy(C),O=updateHintsAreTruthy(A),D=w||O||!isEmptyObject(b)||v;return i=!0,D&&l(e,{Zt:C,tn:A}),D};return[()=>{let{an:e,gt:t,Ot:r}=g,l=getElementScroll(e),a=[v(),c(),S()],i=r();return scrollElementTo(t,l),i(),bind(runEachAndClear,a)},update,()=>({gn:f,bn:u}),{wn:g,yn:C},b]},OverlayScrollbars=(e,t,r)=>{let{N:l}=getEnvironment(),a=isHTMLElement(e),i=a?e:e.target,o=getInstance(i);if(t&&!o){let o=!1,c=[],d={},validateOptions=e=>{let t=removeUndefinedProperties(e,!0),r=getStaticPluginModuleInstance("__osOptionsValidationPlugin");return r?r(t,!0):t},u=assignDeep({},l(),validateOptions(t)),[g,b,v]=createEventListenerHub(),[y,f,S]=createEventListenerHub(r),triggerEvent=(e,t)=>{S(e,t),v(e,t)},[E,C,A,w,O]=createSetups(e,u,()=>o,({pn:e,Dt:t},{Zt:r,tn:l})=>{let{ft:a,Ct:i,xt:o,Ht:c,Et:d,dt:u}=r,{nn:g,sn:b,en:v,cn:y}=l;triggerEvent("updated",[D,{updateHints:{sizeChanged:!!a,directionChanged:!!i,heightIntrinsicChanged:!!o,overflowEdgeChanged:!!g,overflowAmountChanged:!!b,overflowStyleChanged:!!v,scrollCoordinatesChanged:!!y,contentMutation:!!c,hostMutation:!!d,appear:!!u},changedOptions:e||{},force:!!t}])},e=>triggerEvent("scroll",[D,e])),destroy=e=>{removeInstance(i),runEachAndClear(c),o=!0,triggerEvent("destroyed",[D,e]),b(),f()},D={options(e,t){if(e){let r=t?l():{},a=getOptionsDiff(u,assignDeep(r,validateOptions(e)));isEmptyObject(a)||(assignDeep(u,a),C({pn:a}))}return assignDeep({},u)},on:y,off:(e,t)=>{e&&t&&f(e,t)},state(){let{gn:e,bn:t}=A(),{ct:r}=e,{Vt:l,Rt:a,K:i,rn:c,ln:d,dn:u,Lt:g}=t;return assignDeep({},{overflowEdge:l,overflowAmount:a,overflowStyle:i,hasOverflow:c,scrollCoordinates:{start:g.D,end:g.M},padding:d,paddingAbsolute:u,directionRTL:r,destroyed:o})},elements(){let{vt:e,ht:t,ln:r,ot:l,bt:a,gt:i,Qt:o}=w.wn,{Xt:c,Gt:d}=w.yn,translateScrollbarStructure=e=>{let{Pt:t,Ut:r,Tt:l}=e;return{scrollbar:l,track:r,handle:t}},translateScrollbarsSetupElement=e=>{let{Yt:t,Wt:r}=e,l=translateScrollbarStructure(t[0]);return assignDeep({},l,{clone:()=>{let e=translateScrollbarStructure(r());return C({hn:!0}),e}})};return assignDeep({},{target:e,host:t,padding:r||l,viewport:l,content:a||l,scrollOffsetElement:i,scrollEventElement:o,scrollbarHorizontal:translateScrollbarsSetupElement(c),scrollbarVertical:translateScrollbarsSetupElement(d)})},update:e=>C({Dt:e,At:!0}),destroy:bind(destroy,!1),plugin:e=>d[keys(e)[0]]};return(push(c,[O]),addInstance(i,D),registerPluginModuleInstances(eE,OverlayScrollbars,[D,g,d]),cancelInitialization(w.wn.wt,!a&&e.cancel))?destroy(!0):(push(c,E()),triggerEvent("initialized",[D]),D.update()),D}return o};OverlayScrollbars.plugin=e=>{let t=isArray(e),r=t?e:[e],l=r.map(e=>registerPluginModuleInstances(e,OverlayScrollbars)[0]);return addPlugins(r),t?l:l[0]},OverlayScrollbars.valid=e=>{let t=e&&e.elements,r=isFunction(t)&&t();return isPlainObject(r)&&!!getInstance(r.target)},OverlayScrollbars.env=()=>{let{T:e,k:t,R:r,V:l,B:a,F:i,U:o,P:c,N:d,q:u}=getEnvironment();return assignDeep({},{scrollbarsSize:e,scrollbarsOverlaid:t,scrollbarsHiding:r,scrollTimeline:l,staticDefaultInitialization:a,staticDefaultOptions:i,getDefaultInitialization:o,setDefaultInitialization:c,getDefaultOptions:d,setDefaultOptions:u})},OverlayScrollbars.nonce=e=>{l=e};let overlayscrollbars_react_S=()=>{let e,t;if(typeof window>"u"){let n=()=>{};return[n,n]}let r=window,l="function"==typeof r.requestIdleCallback,a=r.requestAnimationFrame,i=r.cancelAnimationFrame,o=l?r.requestIdleCallback:a,c=l?r.cancelIdleCallback:i,s=()=>{c(e),i(t)};return[(r,i)=>{s(),e=o(l?()=>{s(),t=a(r)}:r,"object"==typeof i?i:{timeout:2233})},s]},overlayscrollbars_react_F=e=>{let{options:t,events:r,defer:l}=e||{},[a,o]=(0,i.useMemo)(overlayscrollbars_react_S,[]),c=(0,i.useRef)(null),d=(0,i.useRef)(l),u=(0,i.useRef)(t),g=(0,i.useRef)(r);return(0,i.useEffect)(()=>{d.current=l},[l]),(0,i.useEffect)(()=>{let{current:e}=c;u.current=t,OverlayScrollbars.valid(e)&&e.options(t||{},!0)},[t]),(0,i.useEffect)(()=>{let{current:e}=c;g.current=r,OverlayScrollbars.valid(e)&&e.on(r||{},!0)},[r]),(0,i.useEffect)(()=>()=>{var e;o(),null==(e=c.current)||e.destroy()},[]),(0,i.useMemo)(()=>[e=>{let t=c.current;if(OverlayScrollbars.valid(t))return;let r=d.current,l=u.current||{},i=g.current||{},m=()=>c.current=OverlayScrollbars(e,l,i);r?a(m,r):m()},()=>c.current],[])},ew=(0,i.forwardRef)((e,t)=>{let{element:r="div",options:l,events:a,defer:o,children:c,...d}=e,u=(0,i.useRef)(null),g=(0,i.useRef)(null),[b,v]=overlayscrollbars_react_F({options:l,events:a,defer:o});return(0,i.useEffect)(()=>{let{current:e}=u,{current:t}=g;if(e)return b("body"===r?{target:e,cancel:{body:null}}:{target:e,elements:{viewport:t,content:t}}),()=>{var e;return null==(e=v())?void 0:e.destroy()}},[b,r]),(0,i.useImperativeHandle)(t,()=>({osInstance:v,getElement:()=>u.current}),[]),i.createElement(r,{"data-overlayscrollbars-initialize":"",ref:u,...d},"body"===r?c:i.createElement("div",{"data-overlayscrollbars-contents":"",ref:g},c))})}}]);