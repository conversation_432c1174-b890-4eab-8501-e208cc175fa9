# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce
DB_USERNAME=ecommerce_owner
DB_PASSWORD=npg_aI0Dn8AMfbWj
DB_DIALECT=postgres

# MinIO Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=ecommerce-uploads
MINIO_REGION=us-east-1

# Application Configuration
NODE_ENV=development
PORT=5000
