"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3001],{35484:function(e,t,n){var o=n(85893),r=n(93967),l=n.n(r),s=n(98388);t.Z=e=>{let{title:t,className:n,...r}=e;return(0,o.jsx)("h2",{className:(0,s.m6)(l()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...r,children:t})}},37912:function(e,t,n){var o=n(85893),r=n(5114),l=n(80287),s=n(93967),a=n.n(s),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:s="outline",shadow:p=!1,inputClassName:v,placeholderText:f,...m}=e,{register:b,handleSubmit:y,watch:g,reset:h,formState:{errors:x}}=(0,i.cI)({defaultValues:{searchText:""}}),P=g("searchText"),{t:A}=(0,c.$G)();(0,u.useEffect)(()=>{P||n({searchText:""})},[P]);let S=a()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===s,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===s,"border border-border-base focus:border-accent":"outline"===s},{"focus:shadow":p},v);return(0,o.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(a()("relative flex w-full items-center",t)),onSubmit:y(n),children:[(0,o.jsx)("label",{htmlFor:"search",className:"sr-only",children:A("form:input-label-search")}),(0,o.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,o.jsx)(l.W,{className:"h-5 w-5"})}),(0,o.jsx)("input",{type:"text",id:"search",...b("searchText"),className:(0,d.m6)(S),placeholder:null!=f?f:A("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...m}),x.searchText&&(0,o.jsx)("p",{children:x.searchText.message}),!!P&&(0,o.jsx)("button",{type:"button",onClick:function(){h(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,o.jsx)(r.T,{className:"h-5 w-5"})})]})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var o=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,o.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,o.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},28884:function(e,t,n){var o=n(85893),r=n(804),l=n(8953),s=n(34927),a=n(18230),u=n(27899),i=n(78998),c=n(97514),d=n(77556),p=n(10265),v=n(76518),f=n(27484),m=n.n(f),b=n(84110),y=n.n(b),g=n(29387),h=n.n(g),x=n(70178),P=n.n(x),A=n(5233),S=n(11163),T=n(67294),N=n(16203);m().extend(y()),m().extend(P()),m().extend(h()),t.Z=e=>{let{termsAndConditions:t,paginatorInfo:n,onPagination:f,onSort:m,onOrder:b,user:y}=e,{t:g}=(0,A.$G)(),h=(0,S.useRouter)(),{query:{shop:x}}=h,{alignLeft:P,alignRight:C}=(0,v.S)(),{permissions:E}=(0,N.WA)(),I=(0,N.Ft)(N.M$,E),[D,O]=(0,T.useState)({sort:null===p.As||void 0===p.As?void 0:p.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{m(e=>e===(null===p.As||void 0===p.As?void 0:p.As.Desc)?null===p.As||void 0===p.As?void 0:p.As.Asc:null===p.As||void 0===p.As?void 0:p.As.Desc),b(e),O({sort:(null==D?void 0:D.sort)===(null===p.As||void 0===p.As?void 0:p.As.Desc)?null===p.As||void 0===p.As?void 0:p.As.Asc:null===p.As||void 0===p.As?void 0:p.As.Desc,column:e})}}),k=[{title:(0,o.jsx)(i.Z,{title:g("table:table-item-id"),ascending:D.sort===p.As.Asc&&"id"===D.column,isActive:"id"===D.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:P,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(g("table:table-item-id"),": ").concat(e)},{title:(0,o.jsx)(i.Z,{title:g("table:table-item-title"),ascending:(null==D?void 0:D.sort)===(null===p.As||void 0===p.As?void 0:p.As.Asc)&&(null==D?void 0:D.column)==="title",isActive:(null==D?void 0:D.column)==="title"}),className:"cursor-pointer",dataIndex:"title",key:"title",align:P,ellipsis:!0,width:200,onHeaderCell:()=>onHeaderClick("title"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(i.Z,{title:g("table:table-item-description"),ascending:(null==D?void 0:D.sort)===(null===p.As||void 0===p.As?void 0:p.As.Asc)&&(null==D?void 0:D.column)==="description",isActive:(null==D?void 0:D.column)==="description"}),className:"cursor-pointer",dataIndex:"description",key:"description",align:P,ellipsis:!0,onHeaderCell:()=>onHeaderClick("description"),render:e=>(0,o.jsx)("span",{dangerouslySetInnerHTML:{__html:(null==e?void 0:e.length)<100?e:(null==e?void 0:e.substring(0,100))+"..."}})},{title:(0,o.jsx)(i.Z,{title:g("table:table-item-type"),ascending:(null==D?void 0:D.sort)===(null===p.As||void 0===p.As?void 0:p.As.Asc)&&(null==D?void 0:D.column)==="type",isActive:(null==D?void 0:D.column)==="type"}),className:"cursor-pointer",dataIndex:"type",key:"type",align:"center",onHeaderCell:()=>onHeaderClick("type"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(i.Z,{title:g("table:table-item-issued-by"),ascending:(null==D?void 0:D.sort)===(null===p.As||void 0===p.As?void 0:p.As.Asc)&&(null==D?void 0:D.column)==="issued_by",isActive:(null==D?void 0:D.column)==="issued_by"}),className:"cursor-pointer",dataIndex:"issued_by",key:"issued_by",align:"center",onHeaderCell:()=>onHeaderClick("issued_by"),render:e=>(0,o.jsx)("span",{className:"whitespace-nowrap",children:e})},{title:(0,o.jsx)(i.Z,{title:g("table:table-item-status"),ascending:D.sort===p.As.Asc&&"is_approved"===D.column,isActive:"is_approved"===D.column}),className:"cursor-pointer",dataIndex:"is_approved",key:"is_approved",align:"center",onHeaderCell:()=>onHeaderClick("is_approved"),render:e=>(0,o.jsx)(l.Z,{textKey:e?"Approved":"Waiting for approval",color:e?"bg-accent/10 text-accent":"bg-red-500/10 text-red-500"})},{title:g("text-approval-action"),dataIndex:"id",key:"actions",align:"center",render:(e,t)=>{let{slug:n,is_approved:l}=t;return(0,o.jsx)(r.Z,{id:e,termApproveButton:I,detailsUrl:x?"/".concat(x,"/terms-and-conditions/").concat(n):"/terms-and-conditions/".concat(n),isTermsApproved:l})}},{title:g("table:table-item-actions"),dataIndex:"slug",key:"actions",align:"right",render:(e,t)=>(0,o.jsx)(s.Z,{slug:e,record:t,deleteModalView:"DELETE_TERMS_AND_CONDITIONS",routes:null===c.Z||void 0===c.Z?void 0:c.Z.termsAndCondition,isShop:!!x,shopSlug:null!=x?x:""})}];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,o.jsx)(u.i,{columns:k,emptyText:()=>(0,o.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,o.jsx)(d.m,{className:"w-52"}),(0,o.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:g("table:empty-table-data")}),(0,o.jsx)("p",{className:"text-[13px]",children:g("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1e3}})}),!!(null==n?void 0:n.total)&&(0,o.jsx)("div",{className:"flex items-center justify-end",children:(0,o.jsx)(a.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:f})})]})}},8953:function(e,t,n){var o=n(85893),r=n(93967),l=n.n(r),s=n(5233),a=n(98388);t.Z=e=>{let{t}=(0,s.$G)(),{className:n,color:r,textColor:u,text:i,textKey:c,animate:d=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("span",{className:(0,a.m6)(l()("inline-block",p.root,{[p.default]:!r,[p.text]:!u,[p.animate]:d},r,u,n)),children:c?t(c):i})})}},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var o=n(85893),r=n(55891),l=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,o.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,o.jsx)(r.Z,{nextIcon:(0,o.jsx)(l.T,{}),prevIcon:(0,o.jsx)(ArrowPrev,{}),...e})},41107:function(e,t,n){n.d(t,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var o=n(11163),r=n.n(o),l=n(88767),s=n(22920),a=n(5233),u=n(28597),i=n(97514),c=n(47869),d=n(93345),p=n(55191),v=n(3737);let f={...(0,p.h)(c.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:t,shop_id:n,...o}=e;return v.eN.get(c.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:n,...o,search:v.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>v.eN.post(c.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>v.eN.post(c.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(f.approve,{onSuccess:()=>{s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,l.useQueryClient)();return(0,l.useMutation)(f.disapprove,{onSuccess:()=>{s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:t,language:n}=e,{data:o,error:r,isLoading:s}=(0,l.useQuery)([c.P.TERMS_AND_CONDITIONS,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{termsAndConditions:o,error:r,loading:s}},useTermsAndConditionsQuery=e=>{var t;let{data:n,error:o,isLoading:r}=(0,l.useQuery)([c.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{termsAndConditions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,u.Q)(n),error:o,loading:r}},useCreateTermsAndConditionsMutation=()=>{let e=(0,l.useQueryClient)(),t=(0,o.useRouter)(),{t:n}=(0,a.$G)();return(0,l.useMutation)(f.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(i.Z.termsAndCondition.list):i.Z.termsAndCondition.list;await r().push(e,void 0,{locale:d.Config.defaultLanguage}),s.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;s.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,l.useQueryClient)(),n=(0,o.useRouter)();return(0,l.useMutation)(f.update,{onSuccess:async t=>{let o=n.query.shop?"/".concat(n.query.shop).concat(i.Z.termsAndCondition.list):i.Z.termsAndCondition.list;await n.push(o,void 0,{locale:d.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:t=>{var n;s.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,l.useQueryClient)(),{t}=(0,a.$G)();return(0,l.useMutation)(f.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;s.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},28368:function(e,t,n){n.d(t,{p:function(){return C}});var o,r,l,s=n(67294),a=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),v=n(16567),f=n(14157),m=n(15466),b=n(73781);let y=null!=(l=s.startTransition)?l:function(e){e()};var g=((o=g||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),h=((r=h||{})[r.ToggleDisclosure=0]="ToggleDisclosure",r[r.CloseDisclosure=1]="CloseDisclosure",r[r.SetButtonId=2]="SetButtonId",r[r.SetPanelId=3]="SetPanelId",r[r.LinkPanel=4]="LinkPanel",r[r.UnlinkPanel=5]="UnlinkPanel",r);let x={0:e=>({...e,disclosureState:(0,a.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},P=(0,s.createContext)(null);function M(e){let t=(0,s.useContext)(P);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}P.displayName="DisclosureContext";let A=(0,s.createContext)(null);A.displayName="DisclosureAPIContext";let S=(0,s.createContext)(null);function Y(e,t){return(0,a.E)(t.type,x,e,t)}S.displayName="DisclosurePanelContext";let T=s.Fragment,N=u.AN.RenderStrategy|u.AN.Static,C=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...o}=e,r=(0,s.useRef)(null),l=(0,i.T)(t,(0,i.h)(e=>{r.current=e},void 0===e.as||e.as===s.Fragment)),c=(0,s.useRef)(null),d=(0,s.useRef)(null),p=(0,s.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:f,buttonId:y},g]=p,h=(0,b.z)(e=>{g({type:1});let t=(0,m.r)(r);if(!t||!y)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(y):t.getElementById(y);null==n||n.focus()}),x=(0,s.useMemo)(()=>({close:h}),[h]),S=(0,s.useMemo)(()=>({open:0===f,close:h}),[f,h]);return s.createElement(P.Provider,{value:p},s.createElement(A.Provider,{value:x},s.createElement(v.up,{value:(0,a.E)(f,{0:v.ZM.Open,1:v.ZM.Closed})},(0,u.sY)({ourProps:{ref:l},theirProps:o,slot:S,defaultTag:T,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-button-${n}`,...r}=e,[l,a]=M("Disclosure.Button"),v=(0,s.useContext)(S),m=null!==v&&v===l.panelId,y=(0,s.useRef)(null),g=(0,i.T)(y,t,m?null:l.buttonRef);(0,s.useEffect)(()=>{if(!m)return a({type:2,buttonId:o}),()=>{a({type:2,buttonId:null})}},[o,a,m]);let h=(0,b.z)(e=>{var t;if(m){if(1===l.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),a({type:0}),null==(t=l.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),a({type:0})}}),x=(0,b.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),P=(0,b.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(m?(a({type:0}),null==(n=l.buttonRef.current)||n.focus()):a({type:0}))}),A=(0,s.useMemo)(()=>({open:0===l.disclosureState}),[l]),T=(0,f.f)(e,y),N=m?{ref:g,type:T,onKeyDown:h,onClick:P}:{ref:g,id:o,type:T,"aria-expanded":0===l.disclosureState,"aria-controls":l.linkedPanel?l.panelId:void 0,onKeyDown:h,onKeyUp:x,onClick:P};return(0,u.sY)({ourProps:N,theirProps:r,slot:A,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-disclosure-panel-${n}`,...r}=e,[l,a]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,s.useContext)(A);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,l.panelRef,e=>{y(()=>a({type:e?4:5}))});(0,s.useEffect)(()=>(a({type:3,panelId:o}),()=>{a({type:3,panelId:null})}),[o,a]);let f=(0,v.oJ)(),m=null!==f?(f&v.ZM.Open)===v.ZM.Open:0===l.disclosureState,b=(0,s.useMemo)(()=>({open:0===l.disclosureState,close:d}),[l,d]);return s.createElement(S.Provider,{value:l.panelId},(0,u.sY)({ourProps:{ref:p,id:o},theirProps:r,slot:b,defaultTag:"div",features:N,visible:m,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){n.d(t,{J:function(){return Z}});var o,r,l=n(67294),s=n(32984),a=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),v=n(16567),f=n(14157),m=n(39650),b=n(15466),y=n(51074),g=n(14007),h=n(46045),x=n(73781),P=n(45662),A=n(3855),S=n(16723),T=n(65958),N=n(2740),C=((o=C||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),E=((r=E||{})[r.TogglePopover=0]="TogglePopover",r[r.ClosePopover=1]="ClosePopover",r[r.SetButton=2]="SetButton",r[r.SetButtonId=3]="SetButtonId",r[r.SetPanel=4]="SetPanel",r[r.SetPanelId=5]="SetPanelId",r);let I={0:e=>{let t={...e,popoverState:(0,s.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},D=(0,l.createContext)(null);function oe(e){let t=(0,l.useContext)(D);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}D.displayName="PopoverContext";let O=(0,l.createContext)(null);function fe(e){let t=(0,l.useContext)(O);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}O.displayName="PopoverAPIContext";let k=(0,l.createContext)(null);function Ee(){return(0,l.useContext)(k)}k.displayName="PopoverGroupContext";let _=(0,l.createContext)(null);function Ne(e,t){return(0,s.E)(t.type,I,e,t)}_.displayName="PopoverPanelContext";let R=a.AN.RenderStrategy|a.AN.Static,j=a.AN.RenderStrategy|a.AN.Static,Z=Object.assign((0,a.yV)(function(e,t){var n;let{__demoMode:o=!1,...r}=e,i=(0,l.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,l.useRef)([]),f=(0,l.useReducer)(Ne,{__demoMode:o,popoverState:o?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,l.createRef)(),afterPanelSentinel:(0,l.createRef)()}),[{popoverState:b,button:h,buttonId:P,panel:S,panelId:C,beforePanelSentinel:E,afterPanelSentinel:I},k]=f,R=(0,y.i)(null!=(n=i.current)?n:h),j=(0,l.useMemo)(()=>{if(!h||!S)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(h))^Number(null==e?void 0:e.contains(S)))return!0;let e=(0,p.GO)(),t=e.indexOf(h),n=(t+e.length-1)%e.length,o=(t+1)%e.length,r=e[n],l=e[o];return!S.contains(r)&&!S.contains(l)},[h,S]),Z=(0,A.E)(P),B=(0,A.E)(C),F=(0,l.useMemo)(()=>({buttonId:Z,panelId:B,close:()=>k({type:1})}),[Z,B,k]),z=Ee(),H=null==z?void 0:z.registerPopover,$=(0,x.z)(()=>{var e;return null!=(e=null==z?void 0:z.isFocusWithinPopoverGroup())?e:(null==R?void 0:R.activeElement)&&((null==h?void 0:h.contains(R.activeElement))||(null==S?void 0:S.contains(R.activeElement)))});(0,l.useEffect)(()=>null==H?void 0:H(F),[H,F]);let[L,G]=(0,N.k)(),Q=(0,T.v)({mainTreeNodeRef:null==z?void 0:z.mainTreeNodeRef,portals:L,defaultContainers:[h,S]});(0,g.O)(null==R?void 0:R.defaultView,"focus",e=>{var t,n,o,r;e.target!==window&&e.target instanceof HTMLElement&&0===b&&($()||h&&S&&(Q.contains(e.target)||null!=(n=null==(t=E.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(r=null==(o=I.current)?void 0:o.contains)&&r.call(o,e.target)||k({type:1})))},!0),(0,m.O)(Q.resolveContainers,(e,t)=>{k({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==h||h.focus())},0===b);let V=(0,x.z)(e=>{k({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:h:h;null==t||t.focus()}),K=(0,l.useMemo)(()=>({close:V,isPortalled:j}),[V,j]),U=(0,l.useMemo)(()=>({open:0===b,close:V}),[b,V]);return l.createElement(_.Provider,{value:null},l.createElement(D.Provider,{value:f},l.createElement(O.Provider,{value:K},l.createElement(v.up,{value:(0,s.E)(b,{0:v.ZM.Open,1:v.ZM.Closed})},l.createElement(G,null,(0,a.sY)({ourProps:{ref:c},theirProps:r,slot:U,defaultTag:"div",name:"Popover"}),l.createElement(Q.MainTreeNode,null))))))}),{Button:(0,a.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-button-${n}`,...r}=e,[v,m]=oe("Popover.Button"),{isPortalled:b}=fe("Popover.Button"),g=(0,l.useRef)(null),A=`headlessui-focus-sentinel-${(0,i.M)()}`,S=Ee(),T=null==S?void 0:S.closeOthers,N=null!==(0,l.useContext)(_);(0,l.useEffect)(()=>{if(!N)return m({type:3,buttonId:o}),()=>{m({type:3,buttonId:null})}},[N,o,m]);let[C]=(0,l.useState)(()=>Symbol()),E=(0,u.T)(g,t,N?null:e=>{if(e)v.buttons.current.push(C);else{let e=v.buttons.current.indexOf(C);-1!==e&&v.buttons.current.splice(e,1)}v.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&m({type:2,button:e})}),I=(0,u.T)(g,t),D=(0,y.i)(g),O=(0,x.z)(e=>{var t,n,o;if(N){if(1===v.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),m({type:1}),null==(o=v.button)||o.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===v.popoverState&&(null==T||T(v.buttonId)),m({type:0});break;case c.R.Escape:if(0!==v.popoverState)return null==T?void 0:T(v.buttonId);if(!g.current||null!=D&&D.activeElement&&!g.current.contains(D.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1})}}),k=(0,x.z)(e=>{N||e.key===c.R.Space&&e.preventDefault()}),R=(0,x.z)(t=>{var n,o;(0,d.P)(t.currentTarget)||e.disabled||(N?(m({type:1}),null==(n=v.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===v.popoverState&&(null==T||T(v.buttonId)),m({type:0}),null==(o=v.button)||o.focus()))}),j=(0,x.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===v.popoverState,B=(0,l.useMemo)(()=>({open:Z}),[Z]),F=(0,f.f)(e,g),z=N?{ref:I,type:F,onKeyDown:O,onClick:R}:{ref:E,id:v.buttonId,type:F,"aria-expanded":0===v.popoverState,"aria-controls":v.panel?v.panelId:void 0,onKeyDown:O,onKeyUp:k,onClick:R,onMouseDown:j},H=(0,P.l)(),$=(0,x.z)(()=>{let e=v.panel;e&&(0,s.E)(H.current,{[P.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[P.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,s.E)(H.current,{[P.N.Forwards]:p.TO.Next,[P.N.Backwards]:p.TO.Previous}),{relativeTo:v.button})});return l.createElement(l.Fragment,null,(0,a.sY)({ourProps:z,theirProps:r,slot:B,defaultTag:"button",name:"Popover.Button"}),Z&&!N&&b&&l.createElement(h._,{id:A,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:$}))}),Overlay:(0,a.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-overlay-${n}`,...r}=e,[{popoverState:s},c]=oe("Popover.Overlay"),p=(0,u.T)(t),f=(0,v.oJ)(),m=null!==f?(f&v.ZM.Open)===v.ZM.Open:0===s,b=(0,x.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),y=(0,l.useMemo)(()=>({open:0===s}),[s]);return(0,a.sY)({ourProps:{ref:p,id:o,"aria-hidden":!0,onClick:b},theirProps:r,slot:y,defaultTag:"div",features:R,visible:m,name:"Popover.Overlay"})}),Panel:(0,a.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-popover-panel-${n}`,focus:r=!1,...d}=e,[f,m]=oe("Popover.Panel"),{close:b,isPortalled:g}=fe("Popover.Panel"),A=`headlessui-focus-sentinel-before-${(0,i.M)()}`,T=`headlessui-focus-sentinel-after-${(0,i.M)()}`,N=(0,l.useRef)(null),C=(0,u.T)(N,t,e=>{m({type:4,panel:e})}),E=(0,y.i)(N);(0,S.e)(()=>(m({type:5,panelId:o}),()=>{m({type:5,panelId:null})}),[o,m]);let I=(0,v.oJ)(),D=null!==I?(I&v.ZM.Open)===v.ZM.Open:0===f.popoverState,O=(0,x.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==f.popoverState||!N.current||null!=E&&E.activeElement&&!N.current.contains(E.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1}),null==(t=f.button)||t.focus()}});(0,l.useEffect)(()=>{var t;e.static||1===f.popoverState&&(null==(t=e.unmount)||t)&&m({type:4,panel:null})},[f.popoverState,e.unmount,e.static,m]),(0,l.useEffect)(()=>{if(f.__demoMode||!r||0!==f.popoverState||!N.current)return;let e=null==E?void 0:E.activeElement;N.current.contains(e)||(0,p.jA)(N.current,p.TO.First)},[f.__demoMode,r,N,f.popoverState]);let k=(0,l.useMemo)(()=>({open:0===f.popoverState,close:b}),[f,b]),R={ref:C,id:o,onKeyDown:O,onBlur:r&&0===f.popoverState?e=>{var t,n,o,r,l;let s=e.relatedTarget;s&&N.current&&(null!=(t=N.current)&&t.contains(s)||(m({type:1}),(null!=(o=null==(n=f.beforePanelSentinel.current)?void 0:n.contains)&&o.call(n,s)||null!=(l=null==(r=f.afterPanelSentinel.current)?void 0:r.contains)&&l.call(r,s))&&s.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,P.l)(),B=(0,x.z)(()=>{let e=N.current;e&&(0,s.E)(Z.current,{[P.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=f.afterPanelSentinel.current)||t.focus())},[P.N.Backwards]:()=>{var e;null==(e=f.button)||e.focus({preventScroll:!0})}})}),F=(0,x.z)(()=>{let e=N.current;e&&(0,s.E)(Z.current,{[P.N.Forwards]:()=>{var e;if(!f.button)return;let t=(0,p.GO)(),n=t.indexOf(f.button),o=t.slice(0,n+1),r=[...t.slice(n+1),...o];for(let t of r.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=f.panel)&&e.contains(t)){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}(0,p.jA)(r,p.TO.First,{sorted:!1})},[P.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=f.button)||t.focus())}})});return l.createElement(_.Provider,{value:o},D&&g&&l.createElement(h._,{id:A,ref:f.beforePanelSentinel,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:B}),(0,a.sY)({ourProps:R,theirProps:d,slot:k,defaultTag:"div",features:j,visible:D,name:"Popover.Panel"}),D&&g&&l.createElement(h._,{id:T,ref:f.afterPanelSentinel,features:h.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}))}),Group:(0,a.yV)(function(e,t){let n=(0,l.useRef)(null),o=(0,u.T)(n,t),[r,s]=(0,l.useState)([]),i=(0,T.H)(),c=(0,x.z)(e=>{s(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,x.z)(e=>(s(t=>[...t,e]),()=>c(e))),p=(0,x.z)(()=>{var e;let t=(0,b.r)(n);if(!t)return!1;let o=t.activeElement;return!!(null!=(e=n.current)&&e.contains(o))||r.some(e=>{var n,r;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(o))||(null==(r=t.getElementById(e.panelId.current))?void 0:r.contains(o))})}),v=(0,x.z)(e=>{for(let t of r)t.buttonId.current!==e&&t.close()}),f=(0,l.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:v,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,v,i.mainTreeNodeRef]),m=(0,l.useMemo)(()=>({}),[]);return l.createElement(k.Provider,{value:f},(0,a.sY)({ourProps:{ref:o},theirProps:e,slot:m,defaultTag:"div",name:"Popover.Group"}),l.createElement(i.MainTreeNode,null))})})}}]);