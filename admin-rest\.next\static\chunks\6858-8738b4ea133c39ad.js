"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6858],{92072:function(e,t,n){var o=n(85893),s=n(93967),r=n.n(s),i=n(98388);t.Z=e=>{let{className:t,...n}=e;return(0,o.jsx)("div",{className:(0,i.m6)(r()("rounded bg-light p-5 shadow md:p-8",t)),...n})}},86779:function(e,t,n){n.d(t,{A:function(){return InfoIconNew},s:function(){return InfoIcon}});var o=n(85893);let InfoIcon=e=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23.625 23.625",...e,width:"1em",height:"1em",children:(0,o.jsx)("g",{children:(0,o.jsx)("path",{fill:"currentColor",d:"M11.812,0C5.289,0,0,5.289,0,11.812s5.289,11.813,11.812,11.813s11.813-5.29,11.813-11.813     S18.335,0,11.812,0z M14.271,18.307c-0.608,0.24-1.092,0.422-1.455,0.548c-0.362,0.126-0.783,0.189-1.262,0.189     c-0.736,0-1.309-0.18-1.717-0.539s-0.611-0.814-0.611-1.367c0-0.215,0.015-0.435,0.045-0.659c0.031-0.224,0.08-0.476,0.147-0.759     l0.761-2.688c0.067-0.258,0.125-0.503,0.171-0.731c0.046-0.23,0.068-0.441,0.068-0.633c0-0.342-0.071-0.582-0.212-0.717     c-0.143-0.135-0.412-0.201-0.813-0.201c-0.196,0-0.398,0.029-0.605,0.09c-0.205,0.063-0.383,0.12-0.529,0.176l0.201-0.828     c0.498-0.203,0.975-0.377,1.43-0.521c0.455-0.146,0.885-0.218,1.29-0.218c0.731,0,1.295,0.178,1.692,0.53     c0.395,0.353,0.594,0.812,0.594,1.376c0,0.117-0.014,0.323-0.041,0.617c-0.027,0.295-0.078,0.564-0.152,0.811l-0.757,2.68     c-0.062,0.215-0.117,0.461-0.167,0.736c-0.049,0.275-0.073,0.485-0.073,0.626c0,0.356,0.079,0.599,0.239,0.728     c0.158,0.129,0.435,0.194,0.827,0.194c0.185,0,0.392-0.033,0.626-0.097c0.232-0.064,0.4-0.121,0.506-0.17L14.271,18.307z     M14.137,7.429c-0.353,0.328-0.778,0.492-1.275,0.492c-0.496,0-0.924-0.164-1.28-0.492c-0.354-0.328-0.533-0.727-0.533-1.193     c0-0.465,0.18-0.865,0.533-1.196c0.356-0.332,0.784-0.497,1.28-0.497c0.497,0,0.923,0.165,1.275,0.497     c0.353,0.331,0.53,0.731,0.53,1.196C14.667,6.703,14.49,7.101,14.137,7.429z"})})}),InfoIconNew=e=>(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,o.jsx)("rect",{opacity:.1,width:48,height:48,rx:12,fill:"currentColor"}),(0,o.jsx)("path",{opacity:.2,d:"M34.082 24a10.08 10.08 0 11-20.16 0 10.08 10.08 0 0120.16 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M25.846 29.538a.923.923 0 01-.923.924 1.846 1.846 0 01-1.846-1.847V24a.923.923 0 010-1.846A1.846 1.846 0 0124.923 24v4.615a.923.923 0 01.923.924zM36 24a12 12 0 11-12-12 12.013 12.013 0 0112 12zm-1.846 0A10.154 10.154 0 1024 34.154 10.165 10.165 0 0034.154 24zm-10.616-3.692a1.384 1.384 0 100-2.769 1.384 1.384 0 000 2.769z",fill:"currentColor"})]})},88762:function(e,t,n){n.d(t,{Z:function(){return OpenAIButton}});var o=n(85893),s=n(93967),r=n.n(s);function OpenAIButton(e){let{className:t,onClick:n,title:s,...i}=e;return(0,o.jsx)("div",{onClick:n,className:r()("absolute right-0 -top-1 z-10 cursor-pointer text-sm font-medium text-accent hover:text-accent-hover",t),...i,children:s})}},66858:function(e,t,n){n.d(t,{Z:function(){return CreateOrUpdateTermsAndConditionsForm}});var o=n(85893),s=n(33e3),r=n(87536),i=n(60802),a=n(80602),l=n(92072),d=n(11163),u=n(5233),c=n(47533),m=n(9140),p=n(93345),f=n(16203),x=n(90573),g=n(67294),h=n(75814),b=n(88762);let chatbotAutoSuggestionForTermsAndConditions=e=>{let{name:t}=e;return[{id:1,title:"Write a Terms & Conditions Description."},{id:2,title:"Write a description about user agreement policies."},{id:3,title:"Write a description about Privacy Policy Content."},{id:4,title:"Write a description about Account Termination Terms."},{id:5,title:"Write a description about Intellectual Property Rights."},{id:6,title:"Write a description about online Payment Terms & Conditions."},{id:7,title:"Write a description about User Responsibilities."},{id:8,title:"Write a description about Data Protection Measures."},{id:9,title:"Write a description about Contact Information for legal advices."},{id:10,title:"Write a description about Dispute Resolution Procedures."}]};var v=n(16310),N=n(79362);let y=v.Ry().shape({title:v.Z_().required("form:error-terms-title-required"),description:v.Z_().required("form:error-term-description-required").max(N.$5,"form:error-description-maximum-title").test({name:"description",skipAbsent:!0,test(e,t){var n,o;return!!((null==e?void 0:null===(o=e.replace(/<(.|\n)*?>/g,""))||void 0===o?void 0:null===(n=o.trim())||void 0===n?void 0:n.length)!==0||(null==e?void 0:e.includes("<img")))||t.createError({message:"form:error-term-description-required"})}})});var C=n(99930),A=n(30042),w=n(41107),T=n(22220),I=n(60687);function CreateOrUpdateTermsAndConditionsForm(e){var t,n;let{initialValues:v}=e,N=(0,d.useRouter)(),{t:j}=(0,u.$G)(),{permissions:S}=(0,f.WA)(),{data:_,isLoading:D,error:M}=(0,C.UE)(),{locale:E}=N,{settings:{options:O}}=(0,x.n)({language:E}),{openModal:Z}=(0,h.SO)(),{data:k}=(0,A.DZ)({slug:N.query.shop},{enabled:!!N.query.shop}),R=null==k?void 0:k.id,{register:P,handleSubmit:Q,control:z,watch:q,setError:F,setValue:L,formState:{errors:W}}=(0,r.cI)({defaultValues:v,resolver:(0,c.X)(y)}),{mutate:G,isLoading:$}=(0,w.NO)(),{mutate:U,isLoading:Y}=(0,w.cb)(),B=q("title"),V=(0,g.useMemo)(()=>chatbotAutoSuggestionForTermsAndConditions({name:null!=B?B:""}),[B]),K=(0,g.useCallback)(()=>{Z("GENERATE_DESCRIPTION",{control:z,name:B,set_value:L,key:"description",suggestion:V})},[B]);N.locale,p.Config.defaultLanguage;let onSubmit=async e=>{let t={language:N.locale,title:e.title,description:e.description};try{v&&v.translated_languages.includes(N.locale)?U({...t,id:v.id,shop_id:v.shop_id}):G({...t,...(null==v?void 0:v.slug)&&{slug:v.slug},shop_id:R||(null==v?void 0:v.shop_id)})}catch(t){let e=(0,m.e)(t);Object.keys(null==e?void 0:e.validation).forEach(t=>{F(t.split(".")[1],{type:"manual",message:null==e?void 0:e.validation[t][0]})})}};return(0,o.jsxs)("form",{onSubmit:Q(onSubmit),children:[(0,o.jsxs)("div",{className:"my-5 flex flex-wrap sm:my-8",children:[(0,o.jsx)(a.Z,{title:j("form:input-label-description"),details:"".concat(j(v?"form:item-description-edit":"form:item-description-add")," ").concat(j("form:terms-conditions-form-info-help-text")),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5 "}),(0,o.jsxs)(l.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,o.jsx)(s.Z,{label:j("form:input-title"),...P("title"),error:j(null===(t=W.title)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5",required:!0}),(0,o.jsxs)("div",{className:"relative",children:[(null==O?void 0:O.useAi)&&(0,o.jsx)(b.Z,{title:j("form:button-label-description-ai"),onClick:K}),(0,o.jsx)(I.Z,{title:j("form:input-label-description"),required:!0,error:j(null==W?void 0:null===(n=W.description)||void 0===n?void 0:n.message),name:"description",control:z})]})]})]}),(0,o.jsx)(T.Z,{className:"z-10",children:(0,o.jsxs)("div",{className:"text-end",children:[v&&(0,o.jsx)(i.Z,{variant:"outline",onClick:N.back,className:"text-sm me-4 md:text-base",type:"button",children:j("form:button-label-back")}),(0,o.jsx)(i.Z,{loading:$||Y,disabled:$||Y,className:"text-sm md:text-base",children:j(v?"button-label-update-terms-conditions":"button-label-add-terms-conditions")})]})})]})}},80602:function(e,t,n){var o=n(85893);t.Z=e=>{let{title:t,details:n,className:s,...r}=e;return(0,o.jsxs)("div",{className:s,...r,children:[t&&(0,o.jsx)("h4",{className:"text-base font-semibold text-body-dark mb-2",children:t}),n&&(0,o.jsx)("p",{className:"text-sm text-body",children:n})]})}},33e3:function(e,t,n){var o=n(85893),s=n(71611),r=n(93967),i=n.n(r),a=n(67294),l=n(98388);let d={small:"text-sm h-10",medium:"h-12",big:"h-14"},u=a.forwardRef((e,t)=>{let{className:n,label:r,note:a,name:u,error:c,children:m,variant:p="normal",dimension:f="medium",shadow:x=!1,type:g="text",inputClassName:h,disabled:b,showLabel:v=!0,required:N,toolTipText:y,labelClassName:C,...A}=e,w=i()("px-4 h-12 flex items-center w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===p,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===p,"border border-border-base focus:border-accent":"outline"===p},{"focus:shadow":x},d[f],h),T="number"===g&&b?"number-disable":"";return(0,o.jsxs)("div",{className:(0,l.m6)(n),children:[v||r?(0,o.jsx)(s.Z,{htmlFor:u,toolTipText:y,label:r,required:N,className:C}):"",(0,o.jsx)("input",{id:u,name:u,type:g,ref:t,className:(0,l.m6)(i()(b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] ".concat(T," select-none"):"",w)),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:b,"aria-invalid":c?"true":"false",...A}),a&&(0,o.jsx)("p",{className:"mt-2 text-xs text-body",children:a}),c&&(0,o.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:c})]})});u.displayName="Input",t.Z=u},23091:function(e,t,n){var o=n(85893),s=n(93967),r=n.n(s),i=n(98388);t.Z=e=>{let{className:t,...n}=e;return(0,o.jsx)("label",{className:(0,i.m6)(r()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...n})}},22220:function(e,t,n){var o=n(85893),s=n(93967),r=n.n(s),i=n(98388);t.Z=e=>{let{children:t,className:n,...s}=e;return(0,o.jsx)("div",{className:(0,i.m6)(r()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",n)),...s,children:t})}},71611:function(e,t,n){var o=n(85893),s=n(86779),r=n(71943),i=n(23091),a=n(98388);t.Z=e=>{let{className:t,required:n,label:l,toolTipText:d,htmlFor:u}=e;return(0,o.jsxs)(i.Z,{className:(0,a.m6)(t),htmlFor:u,children:[l,n?(0,o.jsx)("span",{className:"ml-0.5 text-red-500",children:"*"}):"",d?(0,o.jsx)(r.u,{content:d,children:(0,o.jsx)("span",{className:"ltr:ml-1 rtl:mr-1 text-base-dark/40 shrink-0",children:(0,o.jsx)(s.s,{className:"w-3.5 h-3.5"})})}):""]})}},71943:function(e,t,n){n.d(t,{u:function(){return Tooltip}});var o=n(85893),s=n(67294),r=n(93075),i=n(82364),a=n(24750),l=n(93967),d=n.n(l),u=n(67421),c=n(98388);let m={base:"text-center z-40 max-w-sm",shadow:{sm:"drop-shadow-md",md:"drop-shadow-lg",lg:"drop-shadow-xl",xl:"drop-shadow-2xl"},size:{sm:"px-2.5 py-1 text-xs",md:"px-3 py-2 text-sm leading-[1.7]",lg:"px-3.5 py-2 text-base",xl:"px-4 py-2.5 text-base"},rounded:{none:"rounded-none",sm:"rounded-md",DEFAULT:"rounded-md",lg:"rounded-lg",pill:"rounded-full"},arrow:{color:{default:"fill-muted-black",primary:"fill-accent",danger:"fill-red-500",info:"fill-blue-500",success:"fill-green-500",warning:"fill-orange-500"}},variant:{solid:{base:"",color:{default:"text-white bg-muted-black",primary:"text-white bg-accent",danger:"text-white bg-red-500",info:"text-white bg-blue-500",success:"text-white bg-green-500",warning:"text-white bg-orange-500"}}}},p={fadeIn:{initial:{opacity:0},close:{opacity:0}},zoomIn:{initial:{opacity:0,transform:"scale(0.96)"},close:{opacity:0,transform:"scale(0.96)"}},slideIn:{initial:{opacity:0,transform:"translateY(4px)"},close:{opacity:0,transform:"translateY(4px)"}}};function Tooltip(e){let{children:t,content:n,gap:l=8,animation:f="zoomIn",placement:x="top",size:g="md",rounded:h="DEFAULT",shadow:b="md",color:v="default",className:N,arrowClassName:y,showArrow:C=!0}=e,[A,w]=(0,s.useState)(!1),T=(0,s.useRef)(null),{t:I}=(0,u.$G)(),{x:j,y:S,refs:_,strategy:D,context:M}=(0,r.YF)({placement:x,open:A,onOpenChange:w,middleware:[(0,i.x7)({element:T}),(0,i.cv)(l),(0,i.RR)(),(0,i.uY)({padding:8})],whileElementsMounted:a.Me}),{getReferenceProps:E,getFloatingProps:O}=(0,r.NI)([(0,r.XI)(M),(0,r.KK)(M),(0,r.qs)(M,{role:"tooltip"}),(0,r.bQ)(M)]),{isMounted:Z,styles:k}=(0,r.Y_)(M,{duration:{open:150,close:150},...p[f]});return(0,o.jsxs)(o.Fragment,{children:[(0,s.cloneElement)(t,E({ref:_.setReference,...t.props})),(Z||A)&&(0,o.jsx)(r.ll,{children:(0,o.jsxs)("div",{role:"tooltip",ref:_.setFloating,className:(0,c.m6)(d()(m.base,m.size[g],m.rounded[h],m.variant.solid.base,m.variant.solid.color[v],m.shadow[b],N)),style:{position:D,top:null!=S?S:0,left:null!=j?j:0,...k},...O(),children:[I("".concat(n)),C&&(0,o.jsx)(r.Y$,{ref:T,context:M,className:d()(m.arrow.color[v],y),style:{strokeDasharray:"0,14, 5"}})]})})]})}Tooltip.displayName="Tooltip"},60687:function(e,t,n){var o=n(85893),s=n(5152),r=n.n(s),i=n(55846),a=n(67294);t.Z=e=>{let{title:t,placeholder:s,control:l,className:d,editorClassName:u,name:c,required:m,disabled:p,error:f,...x}=e,g=(0,a.useMemo)(()=>r()(()=>Promise.all([n.e(5556),n.e(3305),n.e(2937),n.e(939),n.e(6870),n.e(3373)]).then(n.bind(n,97148)),{loadableGenerated:{webpack:()=>[97148]},ssr:!1,loading:()=>(0,o.jsx)("div",{className:"py-8 flex",children:(0,o.jsx)(i.Z,{simple:!0,className:"h-6 w-6 mx-auto"})})}),[]);return(0,o.jsx)(g,{title:t,placeholder:s,control:l,className:d,editorClassName:u,name:c,required:m,disabled:p,error:f,...x})}},41107:function(e,t,n){n.d(t,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var o=n(11163),s=n.n(o),r=n(88767),i=n(22920),a=n(5233),l=n(28597),d=n(97514),u=n(47869),c=n(93345),m=n(55191),p=n(3737);let f={...(0,m.h)(u.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:t,shop_id:n,...o}=e;return p.eN.get(u.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:n,...o,search:p.eN.formatSearchParams({title:t,shop_id:n})})},approve:e=>p.eN.post(u.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>p.eN.post(u.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,r.useQueryClient)();return(0,r.useMutation)(f.approve,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(u.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,r.useQueryClient)();return(0,r.useMutation)(f.disapprove,{onSuccess:()=>{i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(u.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:t,language:n}=e,{data:o,error:s,isLoading:i}=(0,r.useQuery)([u.P.TERMS_AND_CONDITIONS,{slug:t,language:n}],()=>f.get({slug:t,language:n}));return{termsAndConditions:o,error:s,loading:i}},useTermsAndConditionsQuery=e=>{var t;let{data:n,error:o,isLoading:s}=(0,r.useQuery)([u.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return f.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{termsAndConditions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(n),error:o,loading:s}},useCreateTermsAndConditionsMutation=()=>{let e=(0,r.useQueryClient)(),t=(0,o.useRouter)(),{t:n}=(0,a.$G)();return(0,r.useMutation)(f.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.termsAndCondition.list):d.Z.termsAndCondition.list;await s().push(e,void 0,{locale:c.Config.defaultLanguage}),i.Am.success(n("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(u.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;i.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,r.useQueryClient)(),n=(0,o.useRouter)();return(0,r.useMutation)(f.update,{onSuccess:async t=>{let o=n.query.shop?"/".concat(n.query.shop).concat(d.Z.termsAndCondition.list):d.Z.termsAndCondition.list;await n.push(o,void 0,{locale:c.Config.defaultLanguage}),i.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(u.P.TERMS_AND_CONDITIONS)},onError:t=>{var n;i.Am.error(e("common:".concat(null==t?void 0:null===(n=t.response)||void 0===n?void 0:n.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,r.useQueryClient)(),{t}=(0,a.$G)();return(0,r.useMutation)(f.delete,{onSuccess:()=>{i.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(u.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;i.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})}},9140:function(e,t,n){n.d(t,{e:function(){return getErrorMessage}});var o=n(11163),s=n.n(o),r=n(31955);function getErrorMessage(e){let t={message:"",validation:[]};if(e.graphQLErrors)for(let n of e.graphQLErrors){if(n.extensions&&"validation"===n.extensions.category)return t.message=n.message,t.validation=n.extensions.validation,t;n.extensions&&"authorization"===n.extensions.category&&(r.Z.remove("auth_token"),r.Z.remove("auth_permissions"),s().push("/"))}return t.message=e.message,t}}}]);