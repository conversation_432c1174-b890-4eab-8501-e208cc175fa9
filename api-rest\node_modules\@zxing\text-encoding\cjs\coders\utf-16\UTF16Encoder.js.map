{"version": 3, "file": "UTF16Encoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/utf-16/UTF16Encoder.ts"], "names": [], "mappings": ";;AACA,oDAAmD;AACnD,0DAA2D;AAC3D,sDAAmD;AACnD,iEAAiE;AAEjE;;;;;GAKG;AACH;IAIE,sBAAoB,QAAiB,EAAE,OAA4B;QAA/C,aAAQ,GAAR,QAAQ,CAAS;QACnC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,8BAAO,GAAP,UAAQ,MAAc,EAAE,UAAkB;QACxC,sDAAsD;QACtD,IAAI,UAAU,KAAK,2BAAa;YAC9B,OAAO,mBAAQ,CAAC;QAElB,gEAAgE;QAChE,4DAA4D;QAC5D,qCAAqC;QACrC,IAAI,mBAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC;YACrC,OAAO,8CAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3D,0DAA0D;QAC1D,kDAAkD;QAClD,IAAM,IAAI,GAAG,8CAAsB,CACjC,CAAC,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1D,6DAA6D;QAC7D,kDAAkD;QAClD,IAAM,KAAK,GAAG,8CAAsB,CAClC,CAAC,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5D,uDAAuD;QACvD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IACH,mBAAC;AAAD,CAAC,AArCD,IAqCC;AArCY,oCAAY"}