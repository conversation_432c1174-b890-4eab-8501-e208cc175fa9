"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6410],{92072:function(e,t,n){var s=n(85893),r=n(93967),a=n.n(r),l=n(98388);t.Z=e=>{let{className:t,...n}=e;return(0,s.jsx)("div",{className:(0,l.m6)(a()("rounded bg-light p-5 shadow md:p-8",t)),...n})}},35484:function(e,t,n){var s=n(85893),r=n(93967),a=n.n(r),l=n(98388);t.Z=e=>{let{title:t,className:n,...r}=e;return(0,s.jsx)("h2",{className:(0,l.m6)(a()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...r,children:t})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var s=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,s.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,s.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,s.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},96839:function(e,t,n){n.d(t,{R:function(){return DislikeIcon}});var s=n(85893);let DislikeIcon=e=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17.067 16",...e,children:[(0,s.jsx)("path",{id:"Path_22674","data-name":"Path 22674",d:"M19.244,1h1.778a1.166,1.166,0,0,1,1.244,1.065v7a1.166,1.166,0,0,1-1.244,1.065H19.244A1.166,1.166,0,0,1,18,9.06v-7A1.166,1.166,0,0,1,19.244,1Z",transform:"translate(-18 0.188)",fill:"currentColor"}),(0,s.jsx)("path",{id:"Path_22675","data-name":"Path 22675",d:"M8.093.75a2.84,2.84,0,0,1,2.8,2.361l.8,4.622a2.843,2.843,0,0,1-2.8,3.328H5.533a6.867,6.867,0,0,1,.533,2.844A2.611,2.611,0,0,1,3.755,16.75c-.711,0-1.067-.356-1.067-2.133,0-1.69-1.636-3.049-2.689-3.75V2.058A16.575,16.575,0,0,1,6.955.75Z",transform:"translate(5.333 -0.75)",fill:"currentColor"})]})},38184:function(e,t,n){n.d(t,{l:function(){return LikeIcon}});var s=n(85893);let LikeIcon=e=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17.067 16",...e,children:(0,s.jsxs)("g",{id:"like_1_","data-name":"like (1)",transform:"translate(0 -0.75)",fill:"currentColor",children:[(0,s.jsx)("path",{id:"Path_22672","data-name":"Path 22672",d:"M1.244,17.063H3.022a1.164,1.164,0,0,0,1.244-1.057V9.057A1.164,1.164,0,0,0,3.022,8H1.244A1.164,1.164,0,0,0,0,9.057v6.948A1.164,1.164,0,0,0,1.244,17.063Z",transform:"translate(0 -1.25)"}),(0,s.jsx)("path",{id:"Path_22673","data-name":"Path 22673",d:"M11.255.75c-.711,0-1.067.356-1.067,2.133,0,1.69-1.636,3.049-2.689,3.75v8.809a16.575,16.575,0,0,0,6.955,1.308h1.138a2.84,2.84,0,0,0,2.8-2.361l.8-4.622a2.843,2.843,0,0,0-2.8-3.328H13.033a6.867,6.867,0,0,0,.533-2.844A2.611,2.611,0,0,0,11.255.75Z",transform:"translate(-2.167 0)"})]})})},20854:function(e,t,n){n.d(t,{Z:function(){return question_list}});var s=n(85893),r=n(18230),a=n(27899),l=n(804),i=n(27484),o=n.n(i),c=n(10265),d=n(25675),u=n.n(d),x=n(5233),m=n(76518),h=n(67294),w=n(84110),g=n.n(w),p=n(70178),v=n.n(p),f=n(29387),b=n.n(f),j=n(77556),N=n(99494),k=n(11163),A=n(78998),question_card=e=>{let{record:t,id:n}=e,{question:r,answer:a}=t;return(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text mb-2 text-sm font-semibold text-heading",children:[(0,s.jsx)("span",{className:"me-1 inline-block uppercase",children:"Q:"}),r," "]}),a?(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("span",{className:"me-1 inline-block font-semibold uppercase text-heading",children:"A:"}),a]}):(0,s.jsx)(l.Z,{id:n,showReplyQuestion:!0})]})},y=n(38184),_=n(96839),Q=n(41664),S=n.n(Q),question_list=e=>{let{questions:t,paginatorInfo:n,onPagination:i,onSort:d,onOrder:w}=e,{t:p}=(0,x.$G)(),{alignLeft:f}=(0,m.S)(),Q=(0,k.useRouter)(),{query:{shop:P}}=Q,[C,I]=(0,h.useState)({sort:c.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{d(e=>e===c.As.Desc?c.As.Asc:c.As.Desc),w(e),I({sort:C.sort===c.As.Desc?c.As.Asc:c.As.Desc,column:e})}}),M=[{title:(0,s.jsx)(A.Z,{title:p("table:table-item-id"),ascending:C.sort===c.As.Asc&&"id"===C.column,isActive:"id"===C.column}),className:"cursor-pointer",dataIndex:"id",key:"id",align:f,width:120,onHeaderCell:()=>onHeaderClick("id"),render:e=>"#".concat(p("table:table-item-id"),": ").concat(e)},{title:p("table:table-item-image"),dataIndex:"product",key:"product-image",align:f,width:250,render:e=>{var t,n;return(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative aspect-square h-14 w-14 shrink-0 overflow-hidden rounded border border-border-200/80 bg-gray-100 me-2.5",children:(0,s.jsx)(u(),{src:null!==(n=null==e?void 0:null===(t=e.image)||void 0===t?void 0:t.thumbnail)&&void 0!==n?n:N.siteSettings.product.placeholder,alt:null==e?void 0:e.name,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw"})}),(0,s.jsx)(S(),{href:"".concat("http://localhost:3003","/products/").concat(null==e?void 0:e.slug),children:(0,s.jsx)("span",{className:"truncate whitespace-nowrap font-medium",children:null==e?void 0:e.name})})]})}},{title:p("table:table-item-question-answer"),className:"cursor-pointer",key:"question",align:f,width:350,render:(e,t)=>(0,s.jsx)(question_card,{record:e,id:t})},{title:p("table:table-item-customer"),dataIndex:"user",key:"user",align:f,width:150,render:e=>(0,s.jsx)("span",{children:(null==e?void 0:e.name)?null==e?void 0:e.name:p("common:text-guest")})},{title:p("table:table-item-feedbacks"),key:"feedbacks",align:f,width:150,render:e=>(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"flex items-center text-xs tracking-wider text-gray-400 transition",children:[(0,s.jsx)(y.l,{className:"h-4 w-4 me-1.5"}),null==e?void 0:e.positive_feedbacks_count]}),(0,s.jsxs)("span",{className:"flex items-center text-xs tracking-wider text-gray-400 transition",children:[(0,s.jsx)(_.R,{className:"h-4 w-4 me-1.5"}),null==e?void 0:e.negative_feedbacks_count]})]})},{title:(0,s.jsx)(A.Z,{title:p("table:table-item-date"),ascending:C.sort===c.As.Asc&&"created_at"===C.column,isActive:"created_at"===C.column}),className:"cursor-pointer",dataIndex:"created_at",key:"created_at",align:f,width:150,onHeaderCell:()=>onHeaderClick("created_at"),render:e=>(o().extend(g()),o().extend(v()),o().extend(b()),(0,s.jsx)("span",{className:"whitespace-nowrap",children:o().utc(e).tz(o().tz.guess()).fromNow()}))},{title:p("table:table-item-actions"),dataIndex:"id",key:"actions",align:"right",width:120,render:function(e,t){let{query:{shop:n}}=(0,k.useRouter)();return(0,s.jsx)(l.Z,{id:t,editModalView:"REPLY_QUESTION",deleteModalView:!n&&"DELETE_QUESTION"})}}];return P&&(M=null==M?void 0:M.filter(e=>(null==e?void 0:e.key)!=="actions")),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,s.jsx)(a.i,{columns:M,emptyText:()=>(0,s.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,s.jsx)(j.m,{className:"w-52"}),(0,s.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:p("table:empty-table-data")}),(0,s.jsx)("p",{className:"text-[13px]",children:p("table:empty-table-sorry-text")})]}),data:t,rowKey:"id",scroll:{x:1200}})}),!!(null==n?void 0:n.total)&&(0,s.jsx)("div",{className:"flex items-center justify-end",children:(0,s.jsx)(r.Z,{total:n.total,current:n.currentPage,pageSize:n.perPage,onChange:i})})]})}},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var s=n(85893),r=n(55891),a=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,s.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,s.jsx)(r.Z,{nextIcon:(0,s.jsx)(a.T,{}),prevIcon:(0,s.jsx)(ArrowPrev,{}),...e})},78998:function(e,t,n){n.d(t,{Z:function(){return title_with_sort}});var s=n(85893),r=n(93967),a=n.n(r);n(67294);let TriangleArrowDown=e=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.958",...e,children:(0,s.jsx)("path",{d:"M117.979 28.017h-112c-5.3 0-8 6.4-4.2 10.2l56 56c2.3 2.3 6.1 2.3 8.401 0l56-56c3.799-3.8 1.099-10.2-4.201-10.2z",fill:"currentColor"})}),TriangleArrowUp=e=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 123.959 123.959",...e,children:(0,s.jsx)("path",{d:"M66.18 29.742c-2.301-2.3-6.101-2.3-8.401 0l-56 56c-3.8 3.801-1.1 10.2 4.2 10.2h112c5.3 0 8-6.399 4.2-10.2l-55.999-56z",fill:"currentColor"})});var title_with_sort=e=>{let{title:t,ascending:n,isActive:r=!0,className:l}=e;return(0,s.jsxs)("span",{className:a()("inline-flex items-center",l),children:[(0,s.jsx)("span",{title:"Sort by ".concat(t),children:t}),n?(0,s.jsx)(TriangleArrowUp,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":r})}):(0,s.jsx)(TriangleArrowDown,{width:"9",className:a()("flex-shrink-0 text-gray-300 ms-1.5",{"!text-heading":r})})]})}},52541:function(e,t,n){n.d(t,{VR:function(){return useDeleteQuestionMutation},Pb:function(){return useQuestionsQuery},bD:function(){return useReplyQuestionMutation}});var s=n(88767),r=n(28597),a=n(47869),l=n(22920),i=n(5233),o=n(55191),c=n(3737);let d={...(0,o.h)(a.P.QUESTIONS),get(e){let{id:t}=e;return c.eN.get("".concat(a.P.QUESTIONS,"/").concat(t))},paginated:e=>{let{type:t,shop_id:n,...s}=e;return c.eN.get(a.P.QUESTIONS,{searchJoin:"and",with:"product;user",...s,search:c.eN.formatSearchParams({type:t,shop_id:n})})}},useQuestionsQuery=e=>{var t;let{data:n,error:l,isLoading:i}=(0,s.useQuery)([a.P.QUESTIONS,e],e=>{let{queryKey:t,pageParam:n}=e;return d.paginated(Object.assign({},t[1],n))},{keepPreviousData:!0});return{questions:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,r.Q)(n),error:l,loading:i}},useReplyQuestionMutation=()=>{let{t:e}=(0,i.$G)(),t=(0,s.useQueryClient)();return(0,s.useMutation)(d.update,{onSuccess:()=>{l.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(a.P.QUESTIONS)}})},useDeleteQuestionMutation=()=>{let e=(0,s.useQueryClient)(),{t}=(0,i.$G)();return(0,s.useMutation)(d.delete,{onSuccess:()=>{l.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(a.P.QUESTIONS)}})}}}]);