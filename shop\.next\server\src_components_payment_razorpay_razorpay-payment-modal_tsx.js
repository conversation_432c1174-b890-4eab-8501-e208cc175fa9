"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_payment_razorpay_razorpay-payment-modal_tsx";
exports.ids = ["src_components_payment_razorpay_razorpay-payment-modal_tsx"];
exports.modules = {

/***/ "./src/components/payment/razorpay/razorpay-payment-modal.tsx":
/*!********************************************************************!*\
  !*** ./src/components/payment/razorpay/razorpay-payment-modal.tsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/use-razorpay */ \"./src/lib/use-razorpay.ts\");\n/* harmony import */ var _lib_format_address__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/format-address */ \"./src/lib/format-address.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\n/* harmony import */ var _framework_order__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/framework/order */ \"./src/framework/rest/order.ts\");\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_framework_settings__WEBPACK_IMPORTED_MODULE_6__, _framework_order__WEBPACK_IMPORTED_MODULE_7__]);\n([_framework_settings__WEBPACK_IMPORTED_MODULE_6__, _framework_order__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst RazorpayPaymentModal = ({ trackingNumber, paymentIntentInfo, paymentGateway })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_5__.useModalAction)();\n    const { loadRazorpayScript, checkScriptLoaded } = (0,_lib_use_razorpay__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { settings, isLoading: isSettingsLoading } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const { order, isLoading, refetch } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrder)({\n        tracking_number: trackingNumber\n    });\n    const { createOrderPayment } = (0,_framework_order__WEBPACK_IMPORTED_MODULE_7__.useOrderPayment)();\n    // @ts-ignore\n    const { customer_name, customer_contact, customer, billing_address } = order ?? {};\n    const paymentHandle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!checkScriptLoaded()) {\n            await loadRazorpayScript();\n        }\n        const options = {\n            key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\n            amount: paymentIntentInfo?.amount,\n            currency: paymentIntentInfo?.currency,\n            name: customer_name,\n            description: `${t(\"text-order\")}#${trackingNumber}`,\n            image: settings?.logo?.original,\n            order_id: paymentIntentInfo?.payment_id,\n            handler: async ()=>{\n                closeModal();\n                createOrderPayment({\n                    tracking_number: trackingNumber,\n                    payment_gateway: \"razorpay\"\n                });\n            },\n            prefill: {\n                ...customer_name && {\n                    name: customer_name\n                },\n                ...customer_contact && {\n                    contact: `+${customer_contact}`\n                },\n                ...customer?.email && {\n                    email: customer?.email\n                }\n            },\n            notes: {\n                address: (0,_lib_format_address__WEBPACK_IMPORTED_MODULE_3__.formatAddress)(billing_address)\n            },\n            modal: {\n                ondismiss: async ()=>{\n                    closeModal();\n                    await refetch();\n                }\n            }\n        };\n        const razorpay = window.Razorpay(options);\n        return razorpay.open();\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isSettingsLoading) {\n            (async ()=>{\n                await paymentHandle();\n            })();\n        }\n    }, [\n        isLoading,\n        isSettingsLoading\n    ]);\n    if (isLoading || isSettingsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            showText: false\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\payment\\\\razorpay\\\\razorpay-payment-modal.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, undefined);\n    }\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RazorpayPaymentModal);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/payment/razorpay/razorpay-payment-modal.tsx\n");

/***/ }),

/***/ "./src/lib/format-address.ts":
/*!***********************************!*\
  !*** ./src/lib/format-address.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAddress: () => (/* binding */ formatAddress)\n/* harmony export */ });\nfunction removeFalsy(obj) {\n    return Object.fromEntries(Object.entries(obj).filter(([_, v])=>Boolean(v)));\n}\nfunction formatAddress(address) {\n    if (!address) return;\n    const temp = [\n        \"street_address\",\n        \"state\",\n        \"city\",\n        \"zip\",\n        \"country\"\n    ].reduce((acc, k)=>({\n            ...acc,\n            [k]: address[k]\n        }), {});\n    const formattedAddress = removeFalsy(temp);\n    return Object.values(formattedAddress).join(\", \");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFDQSxTQUFTQSxZQUFZQyxHQUFRO0lBQzNCLE9BQU9DLE9BQU9DLFdBQVcsQ0FBQ0QsT0FBT0UsT0FBTyxDQUFDSCxLQUFLSSxNQUFNLENBQUMsQ0FBQyxDQUFDQyxHQUFHQyxFQUFFLEdBQUtDLFFBQVFEO0FBQzNFO0FBRU8sU0FBU0UsY0FBY0MsT0FBb0I7SUFDaEQsSUFBSSxDQUFDQSxTQUFTO0lBQ2QsTUFBTUMsT0FBTztRQUFDO1FBQWtCO1FBQVM7UUFBUTtRQUFPO0tBQVUsQ0FBQ0MsTUFBTSxDQUN2RSxDQUFDQyxLQUFLQyxJQUFPO1lBQUUsR0FBR0QsR0FBRztZQUFFLENBQUNDLEVBQUUsRUFBRSxPQUFnQixDQUFDQSxFQUFFO1FBQUMsSUFDaEQsQ0FBQztJQUVILE1BQU1DLG1CQUFtQmYsWUFBWVc7SUFDckMsT0FBT1QsT0FBT2MsTUFBTSxDQUFDRCxrQkFBa0JFLElBQUksQ0FBQztBQUM5QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvbGliL2Zvcm1hdC1hZGRyZXNzLnRzP2NiMjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVXNlckFkZHJlc3MgfSBmcm9tICdAL3R5cGVzJztcclxuZnVuY3Rpb24gcmVtb3ZlRmFsc3kob2JqOiBhbnkpIHtcclxuICByZXR1cm4gT2JqZWN0LmZyb21FbnRyaWVzKE9iamVjdC5lbnRyaWVzKG9iaikuZmlsdGVyKChbXywgdl0pID0+IEJvb2xlYW4odikpKTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEFkZHJlc3MoYWRkcmVzczogVXNlckFkZHJlc3MpIHtcclxuICBpZiAoIWFkZHJlc3MpIHJldHVybjtcclxuICBjb25zdCB0ZW1wID0gWydzdHJlZXRfYWRkcmVzcycsICdzdGF0ZScsICdjaXR5JywgJ3ppcCcsICdjb3VudHJ5J10ucmVkdWNlKFxyXG4gICAgKGFjYywgaykgPT4gKHsgLi4uYWNjLCBba106IChhZGRyZXNzIGFzIGFueSlba10gfSksXHJcbiAgICB7fVxyXG4gICk7XHJcbiAgY29uc3QgZm9ybWF0dGVkQWRkcmVzcyA9IHJlbW92ZUZhbHN5KHRlbXApO1xyXG4gIHJldHVybiBPYmplY3QudmFsdWVzKGZvcm1hdHRlZEFkZHJlc3MpLmpvaW4oJywgJyk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInJlbW92ZUZhbHN5Iiwib2JqIiwiT2JqZWN0IiwiZnJvbUVudHJpZXMiLCJlbnRyaWVzIiwiZmlsdGVyIiwiXyIsInYiLCJCb29sZWFuIiwiZm9ybWF0QWRkcmVzcyIsImFkZHJlc3MiLCJ0ZW1wIiwicmVkdWNlIiwiYWNjIiwiayIsImZvcm1hdHRlZEFkZHJlc3MiLCJ2YWx1ZXMiLCJqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/lib/format-address.ts\n");

/***/ }),

/***/ "./src/lib/use-razorpay.ts":
/*!*********************************!*\
  !*** ./src/lib/use-razorpay.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useRazorpay = ()=>{\n    /* Constants */ const RAZORPAY_SCRIPT = \"https://checkout.razorpay.com/v1/checkout.js\";\n    const isClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>\"undefined\" !== \"undefined\", []);\n    const checkScriptLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient || !(\"Razorpay\" in window)) return false;\n        return true;\n    }, []);\n    const loadRazorpayScript = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isClient) return; // Don't execute this function if it's rendering on server side\n        return new Promise((resolve, reject)=>{\n            const scriptTag = document.createElement(\"script\");\n            scriptTag.src = RAZORPAY_SCRIPT;\n            scriptTag.onload = (ev)=>resolve(ev);\n            scriptTag.onerror = (err)=>reject(err);\n            document.body.appendChild(scriptTag);\n        });\n    }, []);\n    return {\n        checkScriptLoaded,\n        loadRazorpayScript\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRazorpay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-razorpay.ts\n");

/***/ })

};
;