{"version": 3, "file": "ISO2022JPEncoder.js", "sourceRoot": "", "sources": ["../../../../src/coders/iso-2022-jp/ISO2022JPEncoder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAG7E,IAAK,MAIJ;AAJD,WAAK,MAAM;IACT,qCAAK,CAAA;IACL,qCAAK,CAAA;IACL,yCAAO,CAAA;AACT,CAAC,EAJI,MAAM,KAAN,MAAM,QAIV;AAED;;;;GAIG;AACH,MAAM,OAAO,gBAAgB;IAM3B,YAAY,OAA4B;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,8DAA8D;QAC9D,6DAA6D;QAC7D,UAAU;QACV,qBAAqB,CAAC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,MAAc,EAAE,UAAkB;QACxC,4DAA4D;QAC5D,wDAAwD;QACxD,6DAA6D;QAC7D,kBAAkB;QAClB,IAAI,UAAU,KAAK,aAAa;YAC9B,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC;YACpC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3B;QAED,4DAA4D;QAC5D,mCAAmC;QACnC,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK;YACvE,OAAO,QAAQ,CAAC;QAElB,8DAA8D;QAC9D,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK;YACxC,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK,CAAC;YACtC,CAAC,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;gBAC7C,UAAU,KAAK,MAAM,CAAC,EAAE;YAC1B,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;SAC7B;QAED,gEAAgE;QAChE,6DAA6D;QAC7D,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK;YACvC,gBAAgB,CAAC,UAAU,CAAC;YAC5B,OAAO,UAAU,CAAC;QAEpB,gEAAgE;QAChE,8DAA8D;QAC9D,iCAAiC;QACjC,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK;YACvC,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBAC5B,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,CAAC;gBAC/C,CAAC,UAAU,IAAI,MAAM,IAAI,UAAU,IAAI,MAAM,CAAC,CAAC,EAAE;YAEnD,yDAAyD;YACzD,6BAA6B;YAC7B,IAAI,gBAAgB,CAAC,UAAU,CAAC;gBAC9B,OAAO,UAAU,CAAC;YAEpB,gDAAgD;YAChD,IAAI,UAAU,KAAK,MAAM;gBACvB,OAAO,IAAI,CAAC;YAEd,gDAAgD;YAChD,IAAI,UAAU,KAAK,MAAM;gBACvB,OAAO,IAAI,CAAC;SACf;QAED,2DAA2D;QAC3D,gEAAgE;QAChE,6DAA6D;QAC7D,kBAAkB;QAClB,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAC9B,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC;YACpC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3B;QAED,+DAA+D;QAC/D,gEAAgE;QAChE,6DAA6D;QAC7D,kBAAkB;QAClB,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,CAAC;YAClD,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,KAAK,EAAE;YACvC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC;YACpC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3B;QAED,gDAAgD;QAChD,IAAI,UAAU,KAAK,MAAM;YACvB,UAAU,GAAG,MAAM,CAAC;QAEtB,8DAA8D;QAC9D,WAAW;QACX,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAa,CAAC,CAAC;QAE1E,wDAAwD;QACxD,IAAI,OAAO,KAAK,IAAI;YAClB,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;QAElC,gEAAgE;QAChE,6DAA6D;QAC7D,yCAAyC;QACzC,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,OAAO,EAAE;YAC3C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC;YACtC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3B;QAED,mDAAmD;QACnD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAE7C,wCAAwC;QACxC,MAAM,KAAK,GAAG,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;QAElC,wDAAwD;QACxD,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvB,CAAC;CACF"}