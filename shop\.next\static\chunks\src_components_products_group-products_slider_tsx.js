"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_products_group-products_slider_tsx"],{

/***/ "./src/components/icons/check-icon.tsx":
/*!*********************************************!*\
  !*** ./src/components/icons/check-icon.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CheckIcon = (param)=>{\n    let { width = 24, height = 24, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: width,\n        height: height,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M20 6L9 17L4 12\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\check-icon.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CheckIcon;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CheckIcon);\nvar _c;\n$RefreshReg$(_c, \"CheckIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jaGVjay1pY29uLnRzeCIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsWUFBK0M7UUFBQyxFQUNwREMsUUFBUSxFQUFFLEVBQ1ZDLFNBQVMsRUFBRSxFQUNYLEdBQUdDLE9BQ0o7SUFDQyxxQkFDRSw4REFBQ0M7UUFDQ0gsT0FBT0E7UUFDUEMsUUFBUUE7UUFDUkcsU0FBUTtRQUNSQyxNQUFLO1FBQ0xDLFFBQU87UUFDTixHQUFHSixLQUFLO2tCQUVULDRFQUFDSztZQUNDQyxHQUFFO1lBQ0ZDLGFBQVk7WUFDWkMsZUFBYztZQUNkQyxnQkFBZTs7Ozs7Ozs7Ozs7QUFJdkI7S0F0Qk1aO0FBd0JOLCtEQUFlQSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2NoZWNrLWljb24udHN4P2Q2NGQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQ2hlY2tJY29uOiBSZWFjdC5GQzxSZWFjdC5TVkdBdHRyaWJ1dGVzPHt9Pj4gPSAoe1xyXG4gIHdpZHRoID0gMjQsXHJcbiAgaGVpZ2h0ID0gMjQsXHJcbiAgLi4ucHJvcHNcclxufSkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8c3ZnXHJcbiAgICAgIHdpZHRoPXt3aWR0aH1cclxuICAgICAgaGVpZ2h0PXtoZWlnaHR9XHJcbiAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxyXG4gICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgID5cclxuICAgICAgPHBhdGhcclxuICAgICAgICBkPVwiTTIwIDZMOSAxN0w0IDEyXCJcclxuICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxyXG4gICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgIC8+XHJcbiAgICA8L3N2Zz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ2hlY2tJY29uO1xyXG4iXSwibmFtZXMiOlsiQ2hlY2tJY29uIiwid2lkdGgiLCJoZWlnaHQiLCJwcm9wcyIsInN2ZyIsInZpZXdCb3giLCJmaWxsIiwic3Ryb2tlIiwicGF0aCIsImQiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/icons/check-icon.tsx\n"));

/***/ }),

/***/ "./src/components/icons/index.ts":
/*!***************************************!*\
  !*** ./src/components/icons/index.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowNext: function() { return /* reexport safe */ _arrow_next__WEBPACK_IMPORTED_MODULE_1__.ArrowNextIcon; },\n/* harmony export */   ArrowPrev: function() { return /* reexport safe */ _arrow_prev__WEBPACK_IMPORTED_MODULE_2__.ArrowPrevIcon; },\n/* harmony export */   Check: function() { return /* reexport safe */ _check_icon__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _check_icon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-icon */ \"./src/components/icons/check-icon.tsx\");\n/* harmony import */ var _arrow_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./arrow-next */ \"./src/components/icons/arrow-next.tsx\");\n/* harmony import */ var _arrow_prev__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./arrow-prev */ \"./src/components/icons/arrow-prev.tsx\");\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0Q7QUFDVTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2ljb25zL2luZGV4LnRzPzdmNjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVjayB9IGZyb20gXCIuL2NoZWNrLWljb25cIjtcclxuZXhwb3J0IHsgQXJyb3dOZXh0SWNvbiBhcyBBcnJvd05leHQgfSBmcm9tIFwiLi9hcnJvdy1uZXh0XCI7XHJcbmV4cG9ydCB7IEFycm93UHJldkljb24gYXMgQXJyb3dQcmV2IH0gZnJvbSBcIi4vYXJyb3ctcHJldlwiO1xyXG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNoZWNrIiwiQXJyb3dOZXh0SWNvbiIsIkFycm93TmV4dCIsIkFycm93UHJldkljb24iLCJBcnJvd1ByZXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/index.ts\n"));

/***/ }),

/***/ "./src/components/products/group-products/slider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/products/group-products/slider.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/slider */ \"./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/icons */ \"./src/components/icons/index.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var _components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/products/cards/card */ \"./src/components/products/cards/card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst offerSliderBreakpoints = {\n    320: {\n        slidesPerView: 1,\n        spaceBetween: 0\n    },\n    580: {\n        slidesPerView: 2,\n        spaceBetween: 16\n    },\n    1024: {\n        slidesPerView: 3,\n        spaceBetween: 16\n    },\n    1920: {\n        slidesPerView: 5,\n        spaceBetween: 24\n    }\n};\nconst ProductsSlider = (param)=>{\n    let { products } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Swiper, {\n                id: \"handPicked_products\",\n                breakpoints: offerSliderBreakpoints,\n                modules: [\n                    _components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.Navigation\n                ],\n                navigation: {\n                    nextEl: \".next\",\n                    prevEl: \".prev\"\n                },\n                children: products === null || products === void 0 ? void 0 : products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_1__.SwiperSlide, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_cards_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            product: product\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, undefined)\n                    }, product === null || product === void 0 ? void 0 : product.id, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prev absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-left-4 rtl:-right-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-left-5 rtl:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-previous\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowPrev, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"next absolute top-2/4 z-10 -mt-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border border-border-200 border-opacity-70 bg-light text-heading shadow-xl transition-all duration-200 hover:border-accent hover:bg-accent hover:text-light ltr:-right-4 rtl:-left-4 md:-mt-5 md:h-9 md:w-9 ltr:md:-right-5\",\n                role: \"button\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: t(\"common:text-next\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowNext, {\n                        width: 18,\n                        height: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\products\\\\group-products\\\\slider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsSlider, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = ProductsSlider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsSlider);\nvar _c;\n$RefreshReg$(_c, \"ProductsSlider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/products/group-products/slider.tsx\n"));

/***/ })

}]);