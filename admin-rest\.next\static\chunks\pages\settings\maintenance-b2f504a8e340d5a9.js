(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3070],{51085:function(e,t,n){"use strict";n.d(t,{Z:function(){return isToday}});var r=n(3151),l=n(13882);function isToday(e){return(0,l.Z)(1,arguments),(0,r.default)(e,Date.now())}},22547:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/maintenance",function(){return n(81698)}])},62003:function(e,t,n){"use strict";n.d(t,{s:function(){return ChevronLeft}});var r=n(85893);let ChevronLeft=e=>(0,r.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},12032:function(e,t,n){"use strict";n.d(t,{N:function(){return SaveIcon}});var r=n(85893);let SaveIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,r.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},97670:function(e,t,n){"use strict";n.r(t);var r=n(85893),l=n(78985),i=n(79362),a=n(8144),s=n(74673),o=n(99494),d=n(5233),c=n(1631),m=n(11163),f=n(48583),p=n(93967),b=n.n(p),x=n(30824),v=n(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:n}=(0,d.$G)(),[l,a]=(0,f.KO)(i.Hf),{childMenu:s}=t,{width:o}=(0,v.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==s?void 0:s.map(e=>{let{href:t,label:a,icon:s,childMenu:d}=e;return(0,r.jsx)(c.Z,{href:t,label:n(a),icon:s,childMenu:d,miniSidebar:l&&o>=i.h2},a)})})},SideBarGroup=()=>{var e;let{t}=(0,d.$G)(),[n,l]=(0,f.KO)(i.Hf),a=null===o.siteSettings||void 0===o.siteSettings?void 0:null===(e=o.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,s=Object.keys(a),{width:c}=(0,v.Z)();return(0,r.jsx)(r.Fragment,{children:null==s?void 0:s.map((e,l)=>{var s;return(0,r.jsxs)("div",{className:b()("flex flex-col px-5",n&&c>=i.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:b()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",n&&c>=i.h2?"hidden":""),children:t(null===(s=a[e])||void 0===s?void 0:s.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:a[e]})]},l)})})};t.default=e=>{let{children:t}=e,{locale:n}=(0,m.useRouter)(),[o,d]=(0,f.KO)(i.Hf),[c]=(0,f.KO)(i.GH),[p]=(0,f.KO)(i.W4),{width:h}=(0,v.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===n||"he"===n?"rtl":"ltr",children:[(0,r.jsx)(l.Z,{}),(0,r.jsx)(s.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:b()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",h>=i.h2&&(c||p)?"lg:pt-[8.75rem]":"pt-20",o&&h>=i.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:b()("relative flex w-full flex-col justify-start transition-[padding] duration-300",h>=i.h2&&(c||p)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",o&&h>=i.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(a.Z,{})]})]})]})}},59122:function(e,t,n){"use strict";n.d(t,{Z:function(){return SettingsPageHeader}});var r=n(85893),l=n(11163),i=n(99494),a=n(8152),s=n(93967),o=n.n(s),d=n(5233),c=n(67294),m=n(86998),f=n(62003);function SettingsPageHeader(e){var t,n,s,p;let{pageTitle:b}=e,{t:x}=(0,d.$G)(),v=(0,l.useRouter)(),{sliderEl:h,sliderPrevBtn:g,sliderNextBtn:w,scrollToTheRight:j,scrollToTheLeft:y}=function(){let e=(0,c.useRef)(null),t=(0,c.useRef)(null),n=(0,c.useRef)(null);return(0,c.useEffect)(()=>{let r=e.current,l=t.current,i=n.current,a=r.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=r.offsetWidth;r.scrollWidth>e?(null==i||i.classList.remove("opacity-0","invisible"),a&&(null==r||r.classList.add("!-mb-[43px]"))):(null==i||i.classList.add("opacity-0","invisible"),a&&(null==r||r.classList.remove("!-mb-[43px]"))),null==l||l.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==r?void 0:r.scrollLeft,t=null==r?void 0:r.offsetWidth;(null==r?void 0:r.scrollWidth)-e==t?(null==i||i.classList.add("opacity-0","invisible"),null==l||l.classList.remove("opacity-0","invisible")):null==i||i.classList.remove("opacity-0","invisible"),0===e?(null==l||l.classList.add("opacity-0","invisible"),null==i||i.classList.remove("opacity-0","invisible")):null==l||l.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),r.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),r.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:n,scrollToTheRight:function(){let n=e.current.offsetWidth;e.current.scrollLeft+=n/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,n.current.classList.remove("opacity-0","invisible")}}}(),N=null===i.siteSettings||void 0===i.siteSettings?void 0:null===(p=i.siteSettings.sidebarLinks)||void 0===p?void 0:null===(s=p.admin)||void 0===s?void 0:null===(n=s.settings)||void 0===n?void 0:null===(t=n.childMenu[0])||void 0===t?void 0:t.childMenu,T=v.asPath.split("#")[0].split("?")[0];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:x(b)})}),(0,r.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,r.jsx)("button",{title:"Prev",ref:g,onClick:()=>y(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(f.s,{className:"h-[18px] w-[18px]"})}),(0,r.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,r.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:h,children:null==N?void 0:N.map((e,t)=>(0,r.jsx)(a.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:o()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",T===e.href?"text-heading before:w-full":null),children:x(e.label)},t))})}),(0,r.jsx)("button",{title:"Next",ref:w,onClick:()=>j(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(m._,{className:"h-[18px] w-[18px]"})})]})]})}},46531:function(e,t,n){"use strict";var r=n(85893),l=n(66271),i=n(71611),a=n(93967),s=n.n(a),o=n(42298),d=n(9198),c=n.n(d);n(35890);var m=n(87536),f=n(98388);t.Z=e=>{let{control:t,minDate:n,startDate:a,locale:d,disabled:p,placeholder:b="Start Date",todayButton:x="Today",name:v,label:h,toolTipText:g,required:w,error:j,dateFormat:y,className:N,maxDate:T,endDate:Z,showTimeSelect:S,timeFormat:D,timeIntervals:C,timeCaption:k,filterTime:L,...E}=e;return(0,r.jsxs)(r.Fragment,{children:[h?(0,r.jsx)(i.Z,{htmlFor:v,toolTipText:g,label:h,required:w}):"",(0,r.jsx)(m.Qr,{control:t,name:v,render:e=>{let{field:t}=e;return(0,r.jsx)(c(),{...t,minDate:n,selected:(null==t?void 0:t.value)?new Date(null==t?void 0:t.value):"",startDate:new Date(a),locale:d,todayButton:x,placeholderText:b,disabled:p,className:N,maxDate:T,endDate:Z,...S&&{showTimeSelect:S,timeFormat:D,timeIntervals:C,timeCaption:k,filterTime:L},customInput:(0,r.jsx)("div",{className:(0,f.m6)(s()("border border-border-base px-4 h-12 flex items-center w-full rounded transition duration-300 ease-in-out text-heading text-sm cursor-pointer",p?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] select-none":"")),children:(null==t?void 0:t.value)?(0,o.default)(new Date(null==t?void 0:t.value),"MMMM, dd yyyy pp"):null==t?void 0:t.value})})},...E}),(0,r.jsx)(l.Z,{message:j})]})}},22220:function(e,t,n){"use strict";var r=n(85893),l=n(93967),i=n.n(l),a=n(98388);t.Z=e=>{let{children:t,className:n,...l}=e;return(0,r.jsx)("div",{className:(0,a.m6)(i()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",n)),...l,children:t})}},77180:function(e,t,n){"use strict";var r=n(85893),l=n(66271),i=n(71611),a=n(77768),s=n(93967),o=n.n(s),d=n(5233),c=n(87536),m=n(98388);t.Z=e=>{let{control:t,label:n,name:s,error:f,disabled:p,required:b,toolTipText:x,className:v,labelClassName:h,...g}=e,{t:w}=(0,d.$G)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:(0,m.m6)(o()("flex items-center gap-x-4",v)),children:[(0,r.jsx)(c.Qr,{name:s,control:t,...g,render:e=>{let{field:{onChange:t,value:l}}=e;return(0,r.jsxs)(a.r,{checked:l,onChange:t,disabled:p,className:"".concat(l?"bg-accent":"bg-gray-300"," relative inline-flex h-6 w-11 items-center rounded-full focus:outline-none ").concat(p?"cursor-not-allowed bg-[#EEF1F4]":""),dir:"ltr",id:s,children:[(0,r.jsxs)("span",{className:"sr-only",children:["Enable ",n]}),(0,r.jsx)("span",{className:"".concat(l?"translate-x-6":"translate-x-1"," inline-block h-4 w-4 transform rounded-full bg-light transition-transform")})]})}}),n?(0,r.jsx)(i.Z,{htmlFor:s,className:o()("mb-0",h),toolTipText:x,label:n,required:b}):""]}),f?(0,r.jsx)(l.Z,{message:f}):""]})}},95414:function(e,t,n){"use strict";var r=n(85893),l=n(71611),i=n(93967),a=n.n(i),s=n(67294),o=n(98388);let d=s.forwardRef((e,t)=>{let{className:n,label:i,toolTipText:s,name:d,error:c,variant:m="normal",shadow:f=!1,inputClassName:p,disabled:b,required:x,...v}=e,h=a()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===m,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===m,"border border-border-base focus:border-accent":"outline"===m},{"focus:shadow":f},p);return(0,r.jsxs)("div",{className:(0,o.m6)(a()(n)),children:[i&&(0,r.jsx)(l.Z,{htmlFor:d,toolTipText:s,label:i,required:x}),(0,r.jsx)("textarea",{id:d,name:d,className:(0,o.m6)(a()(h,b?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:b,...v}),c&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:c})]})});d.displayName="TextArea",t.Z=d},81698:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return R},default:function(){return SeoSettings}});var r=n(85893),l=n(97670),i=n(92072),a=n(12032),s=n(60802),o=n(71611),d=n(67294),c=n(98388);let m=d.forwardRef((e,t)=>{let{className:n,label:l,note:i,name:a,error:s,disabled:d,showLabel:m=!0,required:f,toolTipText:p,...b}=e;return(0,r.jsxs)("div",{className:(0,c.m6)(n),children:[m?(0,r.jsx)(o.Z,{htmlFor:a,toolTipText:p,label:l,required:f}):"",(0,r.jsx)("input",{id:a,name:a,type:"color",ref:t,className:d?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] select-none":"",autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:d,"aria-invalid":s?"true":"false",...b}),i&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:i}),s&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:s})]})});m.displayName="Color";var f=n(46531),p=n(80602),b=n(66272),x=n(33e3);let v=d.forwardRef((e,t)=>{let{className:n,label:l,note:i,name:a,error:s,disabled:d,showLabel:m=!0,required:f,toolTipText:p,...b}=e;return(0,r.jsxs)("div",{className:(0,c.m6)(n),children:[m?(0,r.jsx)(o.Z,{htmlFor:a,toolTipText:p,label:l,required:f}):"",(0,r.jsx)("input",{id:a,name:a,type:"range",ref:t,className:d?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4] select-none":"",autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",disabled:d,"aria-invalid":s?"true":"false",...b}),i&&(0,r.jsx)("p",{className:"mt-2 text-xs text-body",children:i}),s&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 text-start",children:s})]})});v.displayName="Range";var h=n(22220),g=n(77180),w=n(95414),j=n(90573),y=n(3986),N=n(47533),T=n(77349),Z=n(58545),S=n(3151),D=n(51085),C=n(5233),k=n(11163),L=n(87536),E=n(16310);let q=new Date,M=E.Ry().shape({maintenance:E.Ry().when("isUnderMaintenance",{is:e=>e,then:()=>E.Ry().shape({title:E.Z_().required("Title is required"),buttonTitleOne:E.Z_().required("Button title one is required"),buttonTitleTwo:E.Z_().required("Button title two is required"),newsLetterTitle:E.Z_().required("News letter title is required"),aboutUsTitle:E.Z_().required("About us title is required"),contactUsTitle:E.Z_().required("Contact us title is required"),aboutUsDescription:E.Z_().required("About us description is required"),newsLetterDescription:E.Z_().required("News letter description is required"),description:E.Z_().required("Description is required"),start:E.hT().min(q.toDateString(),"Maintenance start date  field must be later than ".concat(q.toDateString())).required("Start date is required"),until:E.hT().required("Until date is required").min(E.iH("start"),"Until date must be greater than or equal to start date")})})});function MaintenanceSettingsForm(e){var t,n,l,o,c,E,q,F,_,O,P,R,A,B,U,G,z,I,V,W,$,K;let{settings:Y}=e,{t:X}=(0,C.$G)(),{locale:Q}=(0,k.useRouter)(),{mutate:J,isLoading:ee}=(0,j.B)(),{language:et,options:en}=null!=Y?Y:{},{register:er,handleSubmit:el,control:ei,reset:ea,watch:es,formState:{errors:eo,dirtyFields:ed}}=(0,L.cI)({shouldUnregister:!0,resolver:(0,N.X)(M),defaultValues:{...en}});async function onSubmit(e){J({language:Q,options:{...en,...e}}),ea(e,{keepValues:!0})}let ec=Object.keys(ed).length>0;(0,y.H)({isDirty:ec});let eu=es("maintenance.start"),em=es("maintenance.until"),ef=es("maintenance.isOverlayColor"),ep=es("isUnderMaintenance"),eb=new Date,ex=(0,r.jsxs)("span",{children:[X("form:maintenance-cover-image-help-text")," ",(0,r.jsx)("br",{}),X("form:cover-image-dimension-help-text")," \xa0",(0,r.jsxs)("span",{className:"font-bold",children:["1170 x 435",X("common:text-px")]})]}),ev=(0,d.useMemo)(()=>(0,S.default)(new Date(em),new Date(eu)),[em,eu]);return(0,r.jsxs)("form",{onSubmit:el(onSubmit),children:[(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:X("form:form-title-information"),details:X("form:site-maintenance-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsx)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:(0,r.jsx)("div",{className:"my-5",children:(0,r.jsx)(g.Z,{name:"isUnderMaintenance",label:X("form:input-label-enable-maintenance-mode"),toolTipText:X("form:input-tooltip-enable-maintenance-mode"),control:ei})})})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:X("form:input-label-maintenance-cover-image"),details:ex,className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsx)(i.Z,{className:"w-full logo-field-area sm:w-8/12 md:w-2/3",children:(0,r.jsx)(b.Z,{name:"maintenance.image",control:ei,multiple:!1,disabled:!ep})})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:X("form:form-title-maintenance-information"),details:X("form:site-maintenance-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(x.Z,{label:X("form:input-label-title"),toolTipText:X("form:input-tooltip-maintenance-title"),...er("maintenance.title"),error:X(null===(n=eo.maintenance)||void 0===n?void 0:null===(t=n.title)||void 0===t?void 0:t.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)(w.Z,{label:X("form:input-label-description"),toolTipText:X("form:input-tooltip-maintenance-description"),...er("maintenance.description"),error:X(null===(o=eo.maintenance)||void 0===o?void 0:null===(l=o.description)||void 0===l?void 0:l.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)("div",{className:"mb-5",children:(0,r.jsx)(f.Z,{control:ei,name:"maintenance.start",minDate:eb,startDate:new Date(eu),locale:Q,placeholder:"Start Date",disabled:!ep,label:X("form:maintenance-start-time"),toolTipText:X("form:input-tooltip-maintenance-start-time"),...ep&&{required:!0},error:X(null===(E=eo.maintenance)||void 0===E?void 0:null===(c=E.start)||void 0===c?void 0:c.message),showTimeSelect:!0,timeFormat:"h:mm aa",timeIntervals:15,timeCaption:"time",dateFormat:"MMMM d, yyyy h:mm aa",filterTime:e=>{if((0,D.Z)(new Date(eu))){let t=new Date(eu).getTime()>e.getTime();return!t}return!0}})}),(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(f.Z,{control:ei,name:"maintenance.until",disabled:!eu||!ep,minDate:(0,T.default)(new Date(eu),0),placeholder:"End Date",locale:Q,...ep&&{required:!0},toolTipText:X("form:input-tooltip-maintenance-end-time"),label:X("form:maintenance-end-date"),error:X(null===(F=eo.maintenance)||void 0===F?void 0:null===(q=F.until)||void 0===q?void 0:q.message),showTimeSelect:!0,timeFormat:"h:mm aa",timeIntervals:15,timeCaption:"time",dateFormat:"MMMM d, yyyy h:mm aa",filterTime:e=>{if(ev){let t=(0,Z.default)(new Date(eu),15).getTime()>e.getTime();return!t}return!0}})})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:"Maintenance mode extra settings",details:"Add maintenance mode extra settings here.",className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)("div",{className:"mb-5",children:(0,r.jsx)(g.Z,{name:"maintenance.isOverlayColor",control:ei,disabled:!ep,label:"Overlay color enable?",toolTipText:X("form:input-tooltip-maintenance-overlay-color")})}),ef?(0,r.jsx)("div",{className:"mb-5",children:(0,r.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,r.jsx)(m,{...er("maintenance.overlayColor"),disabled:!ep,label:"Overlay Color"}),(0,r.jsx)(v,{min:"0",max:"1",step:"0.1",...er("maintenance.overlayColorRange"),disabled:!ep,label:"Alpha"})]})}):"",(0,r.jsx)(x.Z,{label:"Button Title One",toolTipText:X("form:input-tooltip-maintenance-button-one"),...er("maintenance.buttonTitleOne"),error:X(null===(O=eo.maintenance)||void 0===O?void 0:null===(_=O.buttonTitleOne)||void 0===_?void 0:_.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)(x.Z,{label:"Button Title Two",toolTipText:X("form:input-tooltip-maintenance-button-two"),...er("maintenance.buttonTitleTwo"),error:X(null===(R=eo.maintenance)||void 0===R?void 0:null===(P=R.buttonTitleTwo)||void 0===P?void 0:P.message),variant:"outline",...ep&&{required:!0},disabled:!ep})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:"News letter settings",details:"Add news letter settings here.",className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(x.Z,{label:"News letter title.",toolTipText:X("form:input-tooltip-maintenance-newsletter-title"),...er("maintenance.newsLetterTitle"),error:X(null===(B=eo.maintenance)||void 0===B?void 0:null===(A=B.newsLetterTitle)||void 0===A?void 0:A.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)(w.Z,{label:X("form:input-label-description"),toolTipText:X("form:input-tooltip-maintenance-newsletter-description"),...er("maintenance.newsLetterDescription"),error:X(null===(G=eo.maintenance)||void 0===G?void 0:null===(U=G.newsLetterDescription)||void 0===U?void 0:U.message),variant:"outline",...ep&&{required:!0},disabled:!ep})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:my-8",children:[(0,r.jsx)(p.Z,{title:"Side bar drawer content",details:"Add side bar content here.",className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pe-4 md:w-1/3 md:pe-5"}),(0,r.jsxs)(i.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(x.Z,{label:"About us heading.",toolTipText:X("form:input-tooltip-maintenance-drawer-title"),...er("maintenance.aboutUsTitle"),error:X(null===(I=eo.maintenance)||void 0===I?void 0:null===(z=I.aboutUsTitle)||void 0===z?void 0:z.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)(w.Z,{label:X("form:input-label-description"),toolTipText:X("form:input-tooltip-maintenance-drawer-description"),...er("maintenance.aboutUsDescription"),error:X(null===(W=eo.maintenance)||void 0===W?void 0:null===(V=W.aboutUsDescription)||void 0===V?void 0:V.message),variant:"outline",className:"mb-5",...ep&&{required:!0},disabled:!ep}),(0,r.jsx)(x.Z,{label:"Contact us heading.",toolTipText:X("form:input-tooltip-maintenance-contact-us"),...er("maintenance.contactUsTitle"),error:X(null===(K=eo.maintenance)||void 0===K?void 0:null===($=K.contactUsTitle)||void 0===$?void 0:$.message),variant:"outline",...ep&&{required:!0},disabled:!ep})]})]}),(0,r.jsx)(h.Z,{className:"z-0",children:(0,r.jsxs)(s.Z,{loading:ee,disabled:ee||!ec,className:"text-sm md:text-base",children:[(0,r.jsx)(a.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),X("form:button-label-save-settings")]})})]})}var F=n(59122),_=n(45957),O=n(55846),P=n(16203),R=!0;function SeoSettings(){let{t:e}=(0,C.$G)(),{locale:t}=(0,k.useRouter)(),{settings:n,loading:l,error:i}=(0,j.n)({language:t});return l?(0,r.jsx)(O.Z,{text:e("common:text-loading")}):i?(0,r.jsx)(_.Z,{message:i.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(F.Z,{pageTitle:"form:form-title-maintenance-settings"}),(0,r.jsx)(MaintenanceSettingsForm,{settings:n})]})}SeoSettings.authenticate={permissions:P.M$},SeoSettings.Layout=l.default},3986:function(e,t,n){"use strict";n.d(t,{H:function(){return useConfirmRedirectIfDirty}});var r=n(67294),l=n(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:n="You have unsaved changes - are you sure you wish to leave this page?"}=e,i=(0,l.useRouter)(),a=(0,r.useRef)(t),s=(0,r.useRef)(n);(0,r.useEffect)(()=>{a.current=t},[t]),(0,r.useEffect)(()=>{s.current=n},[n]);let o=(0,r.useCallback)(e=>{if(a.current)return e.preventDefault(),e.returnValue=s.current},[]),d=(0,r.useCallback)(()=>{if(a.current&&!window.confirm(s.current))throw i.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,r.useEffect)(()=>(window.addEventListener("beforeunload",o),i.events.on("routeChangeStart",d),()=>{window.removeEventListener("beforeunload",o),i.events.off("routeChangeStart",d)}),[o,d])}},95389:function(e,t,n){"use strict";n.d(t,{_:function(){return c},b:function(){return H}});var r=n(67294),l=n(19946),i=n(12351),a=n(16723),s=n(23784),o=n(73781);let d=(0,r.createContext)(null);function H(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=(0,o.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),l=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return r.createElement(d.Provider,{value:l},e.children)},[t])]}let c=Object.assign((0,i.yV)(function(e,t){let n=(0,l.M)(),{id:o=`headlessui-label-${n}`,passive:c=!1,...m}=e,f=function u(){let e=(0,r.useContext)(d);if(null===e){let e=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return e}(),p=(0,s.T)(t);(0,a.e)(()=>f.register(o),[o,f.register]);let b={ref:p,...f.props,id:o};return c&&("onClick"in b&&(delete b.htmlFor,delete b.onClick),"onClick"in m&&delete m.onClick),(0,i.sY)({ourProps:b,theirProps:m,slot:f.slot||{},defaultTag:"label",name:f.name||"Label"})}),{})},77768:function(e,t,n){"use strict";n.d(t,{r:function(){return w}});var r=n(67294),l=n(12351),i=n(19946),a=n(61363),s=n(64103),o=n(95389),d=n(39516),c=n(14157),m=n(23784),f=n(46045),p=n(18689),b=n(73781),x=n(31147),v=n(94192);let h=(0,r.createContext)(null);h.displayName="GroupContext";let g=r.Fragment,w=Object.assign((0,l.yV)(function(e,t){let n=(0,i.M)(),{id:o=`headlessui-switch-${n}`,checked:d,defaultChecked:g=!1,onChange:w,name:j,value:y,form:N,...T}=e,Z=(0,r.useContext)(h),S=(0,r.useRef)(null),D=(0,m.T)(S,t,null===Z?null:Z.setSwitch),[C,k]=(0,x.q)(d,w,g),L=(0,b.z)(()=>null==k?void 0:k(!C)),E=(0,b.z)(e=>{if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),L()}),q=(0,b.z)(e=>{e.key===a.R.Space?(e.preventDefault(),L()):e.key===a.R.Enter&&(0,p.g)(e.currentTarget)}),M=(0,b.z)(e=>e.preventDefault()),F=(0,r.useMemo)(()=>({checked:C}),[C]),_={id:o,ref:D,role:"switch",type:(0,c.f)(e,S),tabIndex:0,"aria-checked":C,"aria-labelledby":null==Z?void 0:Z.labelledby,"aria-describedby":null==Z?void 0:Z.describedby,onClick:E,onKeyUp:q,onKeyPress:M},O=(0,v.G)();return(0,r.useEffect)(()=>{var e;let t=null==(e=S.current)?void 0:e.closest("form");t&&void 0!==g&&O.addEventListener(t,"reset",()=>{k(g)})},[S,k]),r.createElement(r.Fragment,null,null!=j&&C&&r.createElement(f._,{features:f.A.Hidden,...(0,l.oA)({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:N,checked:C,name:j,value:y})}),(0,l.sY)({ourProps:_,theirProps:T,slot:F,defaultTag:"button",name:"Switch"}))}),{Group:function(e){var t;let[n,i]=(0,r.useState)(null),[a,s]=(0,o.b)(),[c,m]=(0,d.f)(),f=(0,r.useMemo)(()=>({switch:n,setSwitch:i,labelledby:a,describedby:c}),[n,i,a,c]);return r.createElement(m,{name:"Switch.Description"},r.createElement(s,{name:"Switch.Label",props:{htmlFor:null==(t=f.switch)?void 0:t.id,onClick(e){n&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},r.createElement(h.Provider,{value:f},(0,l.sY)({ourProps:{},theirProps:e,defaultTag:g,name:"Switch.Group"}))))},Label:o._,Description:d.d})}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,2512,3261,9494,5535,8186,1285,1631,5468,9774,2888,179],function(){return e(e.s=22547)}),_N_E=e.O()}]);