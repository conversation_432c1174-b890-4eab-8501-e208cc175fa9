"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_cart_cart-counter-button_tsx"],{

/***/ "./src/components/cart/cart-counter-button.tsx":
/*!*****************************************************!*\
  !*** ./src/components/cart/cart-counter-button.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_icons_cart_check_bag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/icons/cart-check-bag */ \"./src/components/icons/cart-check-bag.tsx\");\n/* harmony import */ var _lib_format_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/format-string */ \"./src/lib/format-string.tsx\");\n/* harmony import */ var _lib_use_price__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/use-price */ \"./src/lib/use-price.tsx\");\n/* harmony import */ var _store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/drawer-atom */ \"./src/store/drawer-atom.ts\");\n/* harmony import */ var _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/quick-cart/cart.context */ \"./src/store/quick-cart/cart.context.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"./node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CartCounterButton = ()=>{\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { totalUniqueItems, total } = (0,_store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    const [_, setDisplayCart] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_store_drawer_atom__WEBPACK_IMPORTED_MODULE_4__.drawerAtom);\n    const { price: totalPrice } = (0,_lib_use_price__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        amount: total\n    });\n    function handleCartSidebar() {\n        setDisplayCart({\n            display: true,\n            view: \"cart\"\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"product-cart fixed top-1/2 z-40 -mt-12 hidden flex-col items-center justify-center rounded bg-accent p-3 pt-3.5 text-sm font-semibold text-light shadow-900 transition-colors duration-200 hover:bg-accent-hover focus:outline-0 ltr:right-0 ltr:rounded-tr-none ltr:rounded-br-none rtl:left-0 rtl:rounded-tl-none rtl:rounded-bl-none lg:flex\",\n        onClick: handleCartSidebar,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex pb-0.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_cart_check_bag__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        className: \"shrink-0\",\n                        width: 14,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"flex ltr:ml-2 rtl:mr-2\",\n                        children: (0,_lib_format_string__WEBPACK_IMPORTED_MODULE_2__.formatString)(totalUniqueItems, t(\"common:text-item\"))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"w-full px-2 py-2 mt-3 rounded bg-light text-accent\",\n                children: totalPrice\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\cart\\\\cart-counter-button.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CartCounterButton, \"uxwqxQcqk2RuarPy+16hhR7o5DE=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _store_quick_cart_cart_context__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom,\n        _lib_use_price__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = CartCounterButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartCounterButton);\nvar _c;\n$RefreshReg$(_c, \"CartCounterButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/cart/cart-counter-button.tsx\n"));

/***/ }),

/***/ "./src/components/icons/cart-check-bag.tsx":
/*!*************************************************!*\
  !*** ./src/components/icons/cart-check-bag.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CartCheckBag = (param)=>{\n    let { width, height, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: width,\n        height: height,\n        className: className,\n        viewBox: \"0 0 12.686 16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            transform: \"translate(-27.023 -2)\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    transform: \"translate(27.023 5.156)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M65.7,111.043l-.714-9A1.125,1.125,0,0,0,63.871,101H62.459V103.1a.469.469,0,1,1-.937,0V101H57.211V103.1a.469.469,0,1,1-.937,0V101H54.862a1.125,1.125,0,0,0-1.117,1.033l-.715,9.006a2.605,2.605,0,0,0,2.6,2.8H63.1a2.605,2.605,0,0,0,2.6-2.806Zm-4.224-4.585-2.424,2.424a.468.468,0,0,1-.663,0l-1.136-1.136a.469.469,0,0,1,.663-.663l.8.8,2.092-2.092a.469.469,0,1,1,.663.663Z\",\n                            transform: \"translate(-53.023 -101.005)\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 5\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    transform: \"translate(30.274 2)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M160.132,0a3.1,3.1,0,0,0-3.093,3.093v.063h.937V3.093a2.155,2.155,0,1,1,4.311,0v.063h.937V3.093A3.1,3.1,0,0,0,160.132,0Z\",\n                            transform: \"translate(-157.039)\",\n                            fill: \"currentColor\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 6\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n            lineNumber: 17,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\cart-check-bag.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n};\n_c = CartCheckBag;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CartCheckBag);\nvar _c;\n$RefreshReg$(_c, \"CartCheckBag\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/icons/cart-check-bag.tsx\n"));

/***/ }),

/***/ "./src/lib/format-string.tsx":
/*!***********************************!*\
  !*** ./src/lib/format-string.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatString: function() { return /* binding */ formatString; }\n/* harmony export */ });\nfunction formatString(count, string) {\n    if (!count) return \"\".concat(count, \" \").concat(string);\n    return count > 1 ? \"\".concat(count, \" \").concat(string, \"s\") : \"\".concat(count, \" \").concat(string);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2Zvcm1hdC1zdHJpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxhQUFhQyxLQUFnQyxFQUFFQyxNQUFjO0lBQzNFLElBQUksQ0FBQ0QsT0FBTyxPQUFPLEdBQVlDLE9BQVRELE9BQU0sS0FBVSxPQUFQQztJQUMvQixPQUFPRCxRQUFRLElBQUksR0FBWUMsT0FBVEQsT0FBTSxLQUFVLE9BQVBDLFFBQU8sT0FBSyxHQUFZQSxPQUFURCxPQUFNLEtBQVUsT0FBUEM7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9mb3JtYXQtc3RyaW5nLnRzeD8zZTllIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRTdHJpbmcoY291bnQ6IG51bWJlciB8IG51bGwgfCB1bmRlZmluZWQsIHN0cmluZzogc3RyaW5nKSB7XHJcbiAgaWYgKCFjb3VudCkgcmV0dXJuIGAke2NvdW50fSAke3N0cmluZ31gO1xyXG4gIHJldHVybiBjb3VudCA+IDEgPyBgJHtjb3VudH0gJHtzdHJpbmd9c2AgOiBgJHtjb3VudH0gJHtzdHJpbmd9YDtcclxufVxyXG4iXSwibmFtZXMiOlsiZm9ybWF0U3RyaW5nIiwiY291bnQiLCJzdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/format-string.tsx\n"));

/***/ }),

/***/ "./src/lib/use-price.tsx":
/*!*******************************!*\
  !*** ./src/lib/use-price.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ usePrice; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatVariantPrice: function() { return /* binding */ formatVariantPrice; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _framework_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/framework/settings */ \"./src/framework/rest/settings.ts\");\nvar _s = $RefreshSig$();\n\n\n\nfunction formatPrice(param) {\n    let { amount, currencyCode, locale, fractions } = param;\n    const formatCurrency = new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        maximumFractionDigits: fractions\n    });\n    return formatCurrency.format(amount);\n}\nfunction formatVariantPrice(param) {\n    let { amount, baseAmount, currencyCode, locale, fractions = 2 } = param;\n    const hasDiscount = baseAmount > amount;\n    const formatDiscount = new Intl.NumberFormat(locale, {\n        style: \"percent\"\n    });\n    const discount = hasDiscount ? formatDiscount.format((baseAmount - amount) / baseAmount) : null;\n    const price = formatPrice({\n        amount,\n        currencyCode,\n        locale,\n        fractions\n    });\n    const basePrice = hasDiscount ? formatPrice({\n        amount: baseAmount,\n        currencyCode,\n        locale,\n        fractions\n    }) : null;\n    return {\n        price,\n        basePrice,\n        discount\n    };\n}\nfunction usePrice(data) {\n    _s();\n    const { settings } = (0,_framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    const currency = settings === null || settings === void 0 ? void 0 : settings.currency;\n    const currencyOptions = settings === null || settings === void 0 ? void 0 : settings.currencyOptions;\n    const { amount, baseAmount, currencyCode, currencyOptionsFormat } = {\n        ...data,\n        currencyCode: currency !== null && currency !== void 0 ? currency : \"USD\",\n        currencyOptionsFormat: currencyOptions !== null && currencyOptions !== void 0 ? currencyOptions : {\n            formation: \"en-US\",\n            fractions: 2\n        }\n    };\n    const { formation = \"en-US\", fractions = 2 } = currencyOptionsFormat;\n    const { locale } = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (typeof amount !== \"number\" || !currencyCode) return \"\";\n        const fractionalDigit = fractions ? fractions : 2;\n        let currentLocale = formation ? formation : \"en\";\n        // if (process.env.NEXT_PUBLIC_ENABLE_MULTI_LANG) {\n        //   currentLocale = locale ? locale : 'en';\n        // }\n        return baseAmount ? formatVariantPrice({\n            amount,\n            baseAmount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        }) : formatPrice({\n            amount,\n            currencyCode,\n            locale: currentLocale,\n            fractions: fractionalDigit\n        });\n    }, [\n        amount,\n        baseAmount,\n        currencyCode,\n        locale\n    ]);\n    return typeof value === \"string\" ? {\n        price: value,\n        basePrice: null,\n        discount: null\n    } : value;\n}\n_s(usePrice, \"Bur4/Czn9qVPnH4TQg+8FWM+KEI=\", false, function() {\n    return [\n        _framework_settings__WEBPACK_IMPORTED_MODULE_2__.useSettings,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-price.tsx\n"));

/***/ })

}]);