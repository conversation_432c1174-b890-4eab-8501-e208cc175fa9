"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[685],{35484:function(e,t,n){var r=n(85893),l=n(93967),o=n.n(l),a=n(98388);t.Z=e=>{let{title:t,className:n,...l}=e;return(0,r.jsx)("h2",{className:(0,a.m6)(o()("before:content-'' relative text-lg font-semibold text-heading before:absolute before:-top-0.5 before:h-8 before:rounded-tr-md before:rounded-br-md before:bg-accent ltr:before:-left-8 rtl:before:-right-8 md:before:w-1",n)),...l,children:t})}},37912:function(e,t,n){var r=n(85893),l=n(5114),o=n(80287),a=n(93967),s=n.n(a),u=n(67294),i=n(87536),c=n(5233),d=n(98388);t.Z=e=>{let{className:t,onSearch:n,variant:a="outline",shadow:p=!1,inputClassName:f,placeholderText:v,...m}=e,{register:x,handleSubmit:b,watch:h,reset:y,formState:{errors:g}}=(0,i.cI)({defaultValues:{searchText:""}}),P=h("searchText"),{t:S}=(0,c.$G)();(0,u.useEffect)(()=>{P||n({searchText:""})},[P]);let E=s()("ps-10 pe-4 h-12 flex items-center w-full rounded-md appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===a,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===a,"border border-border-base focus:border-accent":"outline"===a},{"focus:shadow":p},f);return(0,r.jsxs)("form",{noValidate:!0,role:"search",className:(0,d.m6)(s()("relative flex w-full items-center",t)),onSubmit:b(n),children:[(0,r.jsx)("label",{htmlFor:"search",className:"sr-only",children:S("form:input-label-search")}),(0,r.jsx)("button",{className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none start-1 focus:outline-none active:outline-none",children:(0,r.jsx)(o.W,{className:"h-5 w-5"})}),(0,r.jsx)("input",{type:"text",id:"search",...x("searchText"),className:(0,d.m6)(E),placeholder:null!=v?v:S("form:input-placeholder-search"),"aria-label":"Search",autoComplete:"off",...m}),g.searchText&&(0,r.jsx)("p",{children:g.searchText.message}),!!P&&(0,r.jsx)("button",{type:"button",onClick:function(){y(),n({searchText:""})},className:"absolute top-1/2 -translate-y-1/2 p-2 text-body outline-none end-1 focus:outline-none active:outline-none",children:(0,r.jsx)(l.T,{className:"h-5 w-5"})})]})}},85634:function(e,t,n){n.d(t,{K:function(){return ArrowDown}});var r=n(85893);n(67294);let ArrowDown=e=>{let{color:t="currentColor",width:n="12px",height:l="12px",...o}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:n,height:l,viewBox:"0 0 11.996 12",...o,children:(0,r.jsx)("path",{"data-name":"Path 2462",d:"M18.276,12.1,12.7,6.524a.424.424,0,0,0-.6,0L6.524,12.1a.424.424,0,0,0,0,.6.424.424,0,0,0,.6,0l4.854-4.854V17.977a.423.423,0,1,0,.847,0V7.846L17.677,12.7a.424.424,0,0,0,.6,0A.434.434,0,0,0,18.276,12.1Z",transform:"translate(18.396 18.4) rotate(180)",fill:t})})}},14713:function(e,t,n){n.d(t,{P:function(){return ArrowNextNew},T:function(){return ArrowNext}});var r=n(85893);let ArrowNext=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z",fill:"currentColor",stroke:"currentColor"})})},ArrowNextNew=e=>(0,r.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:(0,r.jsx)("path",{d:"M19.599 9.085l-5 5.833a1.665 1.665 0 01-2.35.18 1.667 1.667 0 01-.181-2.35l2.642-3.081H1.667a1.667 1.667 0 010-3.334H14.71l-2.642-3.082a1.667 1.667 0 012.53-2.17l5 5.834a1.667 1.667 0 010 2.17z",fill:"currentColor"})})},46626:function(e,t,n){n.d(t,{a:function(){return ArrowUp}});var r=n(85893);n(67294);let ArrowUp=e=>{let{color:t="currentColor",width:n="12px",height:l="12px",...o}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:n,height:l,viewBox:"0 0 11.996 12",...o,children:(0,r.jsx)("path",{"data-name":"Path 2462",d:"M18.276,12.1,12.7,6.524a.424.424,0,0,0-.6,0L6.524,12.1a.424.424,0,0,0,0,.6.424.424,0,0,0,.6,0l4.854-4.854V17.977a.423.423,0,1,0,.847,0V7.846L17.677,12.7a.424.424,0,0,0,.6,0A.434.434,0,0,0,18.276,12.1Z",transform:"translate(-6.4 -6.4)",fill:t})})}},65366:function(e,t,n){var r=n(85893),l=n(18230),o=n(25675),a=n.n(o),s=n(27899),u=n(99494),i=n(8953),c=n(11163),d=n(5233),p=n(77556),f=n(10265),v=n(76518),m=n(67294),x=n(78998),b=n(97514),h=n(34927);t.Z=e=>{var t;let{products:n,paginatorInfo:o,onPagination:y,onSort:g,onOrder:P}=e,S=(0,c.useRouter)(),{t:E}=(0,d.$G)(),{query:{shop:N}}=S,{alignLeft:I}=(0,v.S)(),[T,j]=(0,m.useState)({sort:f.As.Desc,column:null}),onHeaderClick=e=>({onClick:()=>{g(e=>e===f.As.Desc?f.As.Asc:f.As.Desc),P(e),j({sort:T.sort===f.As.Desc?f.As.Asc:f.As.Desc,column:e})}}),k=[{title:E("table:table-item-id"),dataIndex:"id",key:"id",align:I,width:130,render:e=>"#".concat(E("table:table-item-id"),": ").concat(e)},{title:(0,r.jsx)(x.Z,{title:E("table:table-item-title"),ascending:T.sort===f.As.Asc&&"name"===T.column,isActive:"name"===T.column}),className:"cursor-pointer",dataIndex:"name",key:"name",align:I,width:280,ellipsis:!0,onHeaderCell:()=>onHeaderClick("name"),render:(e,t)=>{var n;let{image:l,type:o}=t;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative aspect-square h-10 w-10 shrink-0 overflow-hidden rounded border border-border-200/80 bg-gray-100 me-2.5",children:(0,r.jsx)(a(),{src:null!==(n=null==l?void 0:l.thumbnail)&&void 0!==n?n:u.siteSettings.product.placeholder,alt:e,fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:e}),(0,r.jsx)("span",{className:"truncate whitespace-nowrap pt-1 pb-0.5 text-[13px] text-body/80",children:null==o?void 0:o.name})]})]})}},{title:E("table:table-item-sku"),dataIndex:"sku",key:"sku",width:200,align:I,ellipsis:!0,render:e=>(0,r.jsx)("span",{className:"truncate whitespace-nowrap",children:e})},{title:(0,r.jsx)(x.Z,{title:E("table:table-item-quantity"),ascending:T.sort===f.As.Asc&&"quantity"===T.column,isActive:"quantity"===T.column}),className:"cursor-pointer",dataIndex:"quantity",key:"quantity",align:"center",width:150,onHeaderCell:()=>onHeaderClick("quantity"),render:e=>e<10?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"flex justify-start ".concat(e>0&&e<10?"flex-col items-baseline space-y-3 3xl:flex-row 3xl:space-x-2 3xl:space-y-0 rtl:3xl:space-x-reverse":"items-center space-x-2 rtl:space-x-reverse"),children:[e<1?(0,r.jsx)(i.Z,{text:E("common:text-out-of-stock"),color:"bg-status-failed/10 text-status-failed",className:"capitalize"}):(0,r.jsx)(i.Z,{text:E("common:text-low-quantity"),color:"bg-status-failed/10 text-status-failed",animate:!0,className:"capitalize"}),(0,r.jsx)("span",{children:e})]})}):(0,r.jsx)("span",{children:e})},{title:(0,r.jsx)(x.Z,{title:E("table:table-item-sold-quantity"),ascending:T.sort===f.As.Asc&&"sold_quantity"===T.column,isActive:"sold_quantity"===T.column}),className:"cursor-pointer",dataIndex:"sold_quantity",key:"sold_quantity",width:120,align:"center",onHeaderCell:()=>onHeaderClick("sold_quantity"),render:e=>(0,r.jsx)("span",{className:"truncate whitespace-nowrap",children:e})},{title:E("table:table-item-actions"),dataIndex:"slug",key:"actions",align:"right",width:120,render:(e,t)=>(0,r.jsx)(h.Z,{slug:e,record:t,deleteModalView:"DELETE_PRODUCT",routes:null===b.Z||void 0===b.Z?void 0:b.Z.inventory,isShop:!!N,shopSlug:null!=N?N:""})}];return(null==S?void 0:null===(t=S.query)||void 0===t?void 0:t.shop)&&(k=null==k?void 0:k.filter(e=>(null==e?void 0:e.key)!=="shop")),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6 overflow-hidden rounded shadow",children:(0,r.jsx)(s.i,{columns:k,emptyText:()=>(0,r.jsxs)("div",{className:"flex flex-col items-center py-7",children:[(0,r.jsx)(p.m,{className:"w-52"}),(0,r.jsx)("div",{className:"mb-1 pt-6 text-base font-semibold text-heading",children:E("table:empty-table-data")}),(0,r.jsx)("p",{className:"text-[13px]",children:E("table:empty-table-sorry-text")})]}),data:n,rowKey:"id",scroll:{x:900}})}),!!(null==o?void 0:o.total)&&(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(l.Z,{total:o.total,current:o.currentPage,pageSize:o.perPage,onChange:y,showLessItems:!0})})]})}},8953:function(e,t,n){var r=n(85893),l=n(93967),o=n.n(l),a=n(5233),s=n(98388);t.Z=e=>{let{t}=(0,a.$G)(),{className:n,color:l,textColor:u,text:i,textKey:c,animate:d=!1}=e,p={root:"px-3 py-1.5 rounded text-xs whitespace-nowrap relative font-medium",animate:"animate-pulse",default:"bg-accent",text:"text-light"};return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("span",{className:(0,s.m6)(o()("inline-block",p.root,{[p.default]:!l,[p.text]:!u,[p.animate]:d},l,u,n)),children:c?t(c):i})})}},23091:function(e,t,n){var r=n(85893),l=n(93967),o=n.n(l),a=n(98388);t.Z=e=>{let{className:t,...n}=e;return(0,r.jsx)("label",{className:(0,a.m6)(o()("flex text-body-dark font-semibold text-sm leading-none mb-3",t)),...n})}},18230:function(e,t,n){n.d(t,{Z:function(){return pagination}});var r=n(85893),l=n(55891),o=n(14713);let ArrowPrev=e=>{let{...t}=e;return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",width:"20",...t,children:(0,r.jsx)("path",{d:"M217.9 256L345 129c9.4-9.4 9.4-24.6 0-33.9-9.4-9.4-24.6-9.3-34 0L167 239c-9.1 9.1-9.3 23.7-.7 33.1L310.9 417c4.7 4.7 10.9 7 17 7s12.3-2.3 17-7c9.4-9.4 9.4-24.6 0-33.9L217.9 256z",fill:"currentColor",stroke:"currentColor"})})};n(5871);var pagination=e=>(0,r.jsx)(l.Z,{nextIcon:(0,r.jsx)(o.T,{}),prevIcon:(0,r.jsx)(ArrowPrev,{}),...e})},28368:function(e,t,n){n.d(t,{p:function(){return T}});var r,l,o,a=n(67294),s=n(32984),u=n(12351),i=n(23784),c=n(19946),d=n(61363),p=n(64103),f=n(16567),v=n(14157),m=n(15466),x=n(73781);let b=null!=(o=a.startTransition)?o:function(e){e()};var h=((r=h||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),y=((l=y||{})[l.ToggleDisclosure=0]="ToggleDisclosure",l[l.CloseDisclosure=1]="CloseDisclosure",l[l.SetButtonId=2]="SetButtonId",l[l.SetPanelId=3]="SetPanelId",l[l.LinkPanel=4]="LinkPanel",l[l.UnlinkPanel=5]="UnlinkPanel",l);let g={0:e=>({...e,disclosureState:(0,s.E)(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},P=(0,a.createContext)(null);function M(e){let t=(0,a.useContext)(P);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,M),t}return t}P.displayName="DisclosureContext";let S=(0,a.createContext)(null);S.displayName="DisclosureAPIContext";let E=(0,a.createContext)(null);function Y(e,t){return(0,s.E)(t.type,g,e,t)}E.displayName="DisclosurePanelContext";let N=a.Fragment,I=u.AN.RenderStrategy|u.AN.Static,T=Object.assign((0,u.yV)(function(e,t){let{defaultOpen:n=!1,...r}=e,l=(0,a.useRef)(null),o=(0,i.T)(t,(0,i.h)(e=>{l.current=e},void 0===e.as||e.as===a.Fragment)),c=(0,a.useRef)(null),d=(0,a.useRef)(null),p=(0,a.useReducer)(Y,{disclosureState:n?0:1,linkedPanel:!1,buttonRef:d,panelRef:c,buttonId:null,panelId:null}),[{disclosureState:v,buttonId:b},h]=p,y=(0,x.z)(e=>{h({type:1});let t=(0,m.r)(l);if(!t||!b)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(b):t.getElementById(b);null==n||n.focus()}),g=(0,a.useMemo)(()=>({close:y}),[y]),E=(0,a.useMemo)(()=>({open:0===v,close:y}),[v,y]);return a.createElement(P.Provider,{value:p},a.createElement(S.Provider,{value:g},a.createElement(f.up,{value:(0,s.E)(v,{0:f.ZM.Open,1:f.ZM.Closed})},(0,u.sY)({ourProps:{ref:o},theirProps:r,slot:E,defaultTag:N,name:"Disclosure"}))))}),{Button:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-button-${n}`,...l}=e,[o,s]=M("Disclosure.Button"),f=(0,a.useContext)(E),m=null!==f&&f===o.panelId,b=(0,a.useRef)(null),h=(0,i.T)(b,t,m?null:o.buttonRef);(0,a.useEffect)(()=>{if(!m)return s({type:2,buttonId:r}),()=>{s({type:2,buttonId:null})}},[r,s,m]);let y=(0,x.z)(e=>{var t;if(m){if(1===o.disclosureState)return;switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0}),null==(t=o.buttonRef.current)||t.focus()}}else switch(e.key){case d.R.Space:case d.R.Enter:e.preventDefault(),e.stopPropagation(),s({type:0})}}),g=(0,x.z)(e=>{e.key===d.R.Space&&e.preventDefault()}),P=(0,x.z)(t=>{var n;(0,p.P)(t.currentTarget)||e.disabled||(m?(s({type:0}),null==(n=o.buttonRef.current)||n.focus()):s({type:0}))}),S=(0,a.useMemo)(()=>({open:0===o.disclosureState}),[o]),N=(0,v.f)(e,b),I=m?{ref:h,type:N,onKeyDown:y,onClick:P}:{ref:h,id:r,type:N,"aria-expanded":0===o.disclosureState,"aria-controls":o.linkedPanel?o.panelId:void 0,onKeyDown:y,onKeyUp:g,onClick:P};return(0,u.sY)({ourProps:I,theirProps:l,slot:S,defaultTag:"button",name:"Disclosure.Button"})}),Panel:(0,u.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-disclosure-panel-${n}`,...l}=e,[o,s]=M("Disclosure.Panel"),{close:d}=function w(e){let t=(0,a.useContext)(S);if(null===t){let t=Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return t}("Disclosure.Panel"),p=(0,i.T)(t,o.panelRef,e=>{b(()=>s({type:e?4:5}))});(0,a.useEffect)(()=>(s({type:3,panelId:r}),()=>{s({type:3,panelId:null})}),[r,s]);let v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===o.disclosureState,x=(0,a.useMemo)(()=>({open:0===o.disclosureState,close:d}),[o,d]);return a.createElement(E.Provider,{value:o.panelId},(0,u.sY)({ourProps:{ref:p,id:r},theirProps:l,slot:x,defaultTag:"div",features:I,visible:m,name:"Disclosure.Panel"}))})})},86215:function(e,t,n){n.d(t,{J:function(){return Z}});var r,l,o=n(67294),a=n(32984),s=n(12351),u=n(23784),i=n(19946),c=n(61363),d=n(64103),p=n(84575),f=n(16567),v=n(14157),m=n(39650),x=n(15466),b=n(51074),h=n(14007),y=n(46045),g=n(73781),P=n(45662),S=n(3855),E=n(16723),N=n(65958),I=n(2740),T=((r=T||{})[r.Open=0]="Open",r[r.Closed=1]="Closed",r),j=((l=j||{})[l.TogglePopover=0]="TogglePopover",l[l.ClosePopover=1]="ClosePopover",l[l.SetButton=2]="SetButton",l[l.SetButtonId=3]="SetButtonId",l[l.SetPanel=4]="SetPanel",l[l.SetPanelId=5]="SetPanelId",l);let k={0:e=>{let t={...e,popoverState:(0,a.E)(e.popoverState,{0:1,1:0})};return 0===t.popoverState&&(t.__demoMode=!1),t},1:e=>1===e.popoverState?e:{...e,popoverState:1},2:(e,t)=>e.button===t.button?e:{...e,button:t.button},3:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},4:(e,t)=>e.panel===t.panel?e:{...e,panel:t.panel},5:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},C=(0,o.createContext)(null);function oe(e){let t=(0,o.useContext)(C);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return t}C.displayName="PopoverContext";let A=(0,o.createContext)(null);function fe(e){let t=(0,o.useContext)(A);if(null===t){let t=Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fe),t}return t}A.displayName="PopoverAPIContext";let D=(0,o.createContext)(null);function Ee(){return(0,o.useContext)(D)}D.displayName="PopoverGroupContext";let R=(0,o.createContext)(null);function Ne(e,t){return(0,a.E)(t.type,k,e,t)}R.displayName="PopoverPanelContext";let O=s.AN.RenderStrategy|s.AN.Static,B=s.AN.RenderStrategy|s.AN.Static,Z=Object.assign((0,s.yV)(function(e,t){var n;let{__demoMode:r=!1,...l}=e,i=(0,o.useRef)(null),c=(0,u.T)(t,(0,u.h)(e=>{i.current=e})),d=(0,o.useRef)([]),v=(0,o.useReducer)(Ne,{__demoMode:r,popoverState:r?0:1,buttons:d,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,o.createRef)(),afterPanelSentinel:(0,o.createRef)()}),[{popoverState:x,button:y,buttonId:P,panel:E,panelId:T,beforePanelSentinel:j,afterPanelSentinel:k},D]=v,O=(0,b.i)(null!=(n=i.current)?n:y),B=(0,o.useMemo)(()=>{if(!y||!E)return!1;for(let e of document.querySelectorAll("body > *"))if(Number(null==e?void 0:e.contains(y))^Number(null==e?void 0:e.contains(E)))return!0;let e=(0,p.GO)(),t=e.indexOf(y),n=(t+e.length-1)%e.length,r=(t+1)%e.length,l=e[n],o=e[r];return!E.contains(l)&&!E.contains(o)},[y,E]),Z=(0,S.E)(P),z=(0,S.E)(T),F=(0,o.useMemo)(()=>({buttonId:Z,panelId:z,close:()=>D({type:1})}),[Z,z,D]),_=Ee(),L=null==_?void 0:_.registerPopover,q=(0,g.z)(()=>{var e;return null!=(e=null==_?void 0:_.isFocusWithinPopoverGroup())?e:(null==O?void 0:O.activeElement)&&((null==y?void 0:y.contains(O.activeElement))||(null==E?void 0:E.contains(O.activeElement)))});(0,o.useEffect)(()=>null==L?void 0:L(F),[L,F]);let[V,H]=(0,I.k)(),$=(0,N.v)({mainTreeNodeRef:null==_?void 0:_.mainTreeNodeRef,portals:V,defaultContainers:[y,E]});(0,h.O)(null==O?void 0:O.defaultView,"focus",e=>{var t,n,r,l;e.target!==window&&e.target instanceof HTMLElement&&0===x&&(q()||y&&E&&($.contains(e.target)||null!=(n=null==(t=j.current)?void 0:t.contains)&&n.call(t,e.target)||null!=(l=null==(r=k.current)?void 0:r.contains)&&l.call(r,e.target)||D({type:1})))},!0),(0,m.O)($.resolveContainers,(e,t)=>{D({type:1}),(0,p.sP)(t,p.tJ.Loose)||(e.preventDefault(),null==y||y.focus())},0===x);let G=(0,g.z)(e=>{D({type:1});let t=e?e instanceof HTMLElement?e:"current"in e&&e.current instanceof HTMLElement?e.current:y:y;null==t||t.focus()}),K=(0,o.useMemo)(()=>({close:G,isPortalled:B}),[G,B]),U=(0,o.useMemo)(()=>({open:0===x,close:G}),[x,G]);return o.createElement(R.Provider,{value:null},o.createElement(C.Provider,{value:v},o.createElement(A.Provider,{value:K},o.createElement(f.up,{value:(0,a.E)(x,{0:f.ZM.Open,1:f.ZM.Closed})},o.createElement(H,null,(0,s.sY)({ourProps:{ref:c},theirProps:l,slot:U,defaultTag:"div",name:"Popover"}),o.createElement($.MainTreeNode,null))))))}),{Button:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-button-${n}`,...l}=e,[f,m]=oe("Popover.Button"),{isPortalled:x}=fe("Popover.Button"),h=(0,o.useRef)(null),S=`headlessui-focus-sentinel-${(0,i.M)()}`,E=Ee(),N=null==E?void 0:E.closeOthers,I=null!==(0,o.useContext)(R);(0,o.useEffect)(()=>{if(!I)return m({type:3,buttonId:r}),()=>{m({type:3,buttonId:null})}},[I,r,m]);let[T]=(0,o.useState)(()=>Symbol()),j=(0,u.T)(h,t,I?null:e=>{if(e)f.buttons.current.push(T);else{let e=f.buttons.current.indexOf(T);-1!==e&&f.buttons.current.splice(e,1)}f.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&m({type:2,button:e})}),k=(0,u.T)(h,t),C=(0,b.i)(h),A=(0,g.z)(e=>{var t,n,r;if(I){if(1===f.popoverState)return;switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),null==(n=(t=e.target).click)||n.call(t),m({type:1}),null==(r=f.button)||r.focus()}}else switch(e.key){case c.R.Space:case c.R.Enter:e.preventDefault(),e.stopPropagation(),1===f.popoverState&&(null==N||N(f.buttonId)),m({type:0});break;case c.R.Escape:if(0!==f.popoverState)return null==N?void 0:N(f.buttonId);if(!h.current||null!=C&&C.activeElement&&!h.current.contains(C.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1})}}),D=(0,g.z)(e=>{I||e.key===c.R.Space&&e.preventDefault()}),O=(0,g.z)(t=>{var n,r;(0,d.P)(t.currentTarget)||e.disabled||(I?(m({type:1}),null==(n=f.button)||n.focus()):(t.preventDefault(),t.stopPropagation(),1===f.popoverState&&(null==N||N(f.buttonId)),m({type:0}),null==(r=f.button)||r.focus()))}),B=(0,g.z)(e=>{e.preventDefault(),e.stopPropagation()}),Z=0===f.popoverState,z=(0,o.useMemo)(()=>({open:Z}),[Z]),F=(0,v.f)(e,h),_=I?{ref:k,type:F,onKeyDown:A,onClick:O}:{ref:j,id:f.buttonId,type:F,"aria-expanded":0===f.popoverState,"aria-controls":f.panel?f.panelId:void 0,onKeyDown:A,onKeyUp:D,onClick:O,onMouseDown:B},L=(0,P.l)(),q=(0,g.z)(()=>{let e=f.panel;e&&(0,a.E)(L.current,{[P.N.Forwards]:()=>(0,p.jA)(e,p.TO.First),[P.N.Backwards]:()=>(0,p.jA)(e,p.TO.Last)})===p.fE.Error&&(0,p.jA)((0,p.GO)().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),(0,a.E)(L.current,{[P.N.Forwards]:p.TO.Next,[P.N.Backwards]:p.TO.Previous}),{relativeTo:f.button})});return o.createElement(o.Fragment,null,(0,s.sY)({ourProps:_,theirProps:l,slot:z,defaultTag:"button",name:"Popover.Button"}),Z&&!I&&x&&o.createElement(y._,{id:S,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:q}))}),Overlay:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-overlay-${n}`,...l}=e,[{popoverState:a},c]=oe("Popover.Overlay"),p=(0,u.T)(t),v=(0,f.oJ)(),m=null!==v?(v&f.ZM.Open)===f.ZM.Open:0===a,x=(0,g.z)(e=>{if((0,d.P)(e.currentTarget))return e.preventDefault();c({type:1})}),b=(0,o.useMemo)(()=>({open:0===a}),[a]);return(0,s.sY)({ourProps:{ref:p,id:r,"aria-hidden":!0,onClick:x},theirProps:l,slot:b,defaultTag:"div",features:O,visible:m,name:"Popover.Overlay"})}),Panel:(0,s.yV)(function(e,t){let n=(0,i.M)(),{id:r=`headlessui-popover-panel-${n}`,focus:l=!1,...d}=e,[v,m]=oe("Popover.Panel"),{close:x,isPortalled:h}=fe("Popover.Panel"),S=`headlessui-focus-sentinel-before-${(0,i.M)()}`,N=`headlessui-focus-sentinel-after-${(0,i.M)()}`,I=(0,o.useRef)(null),T=(0,u.T)(I,t,e=>{m({type:4,panel:e})}),j=(0,b.i)(I);(0,E.e)(()=>(m({type:5,panelId:r}),()=>{m({type:5,panelId:null})}),[r,m]);let k=(0,f.oJ)(),C=null!==k?(k&f.ZM.Open)===f.ZM.Open:0===v.popoverState,A=(0,g.z)(e=>{var t;if(e.key===c.R.Escape){if(0!==v.popoverState||!I.current||null!=j&&j.activeElement&&!I.current.contains(j.activeElement))return;e.preventDefault(),e.stopPropagation(),m({type:1}),null==(t=v.button)||t.focus()}});(0,o.useEffect)(()=>{var t;e.static||1===v.popoverState&&(null==(t=e.unmount)||t)&&m({type:4,panel:null})},[v.popoverState,e.unmount,e.static,m]),(0,o.useEffect)(()=>{if(v.__demoMode||!l||0!==v.popoverState||!I.current)return;let e=null==j?void 0:j.activeElement;I.current.contains(e)||(0,p.jA)(I.current,p.TO.First)},[v.__demoMode,l,I,v.popoverState]);let D=(0,o.useMemo)(()=>({open:0===v.popoverState,close:x}),[v,x]),O={ref:T,id:r,onKeyDown:A,onBlur:l&&0===v.popoverState?e=>{var t,n,r,l,o;let a=e.relatedTarget;a&&I.current&&(null!=(t=I.current)&&t.contains(a)||(m({type:1}),(null!=(r=null==(n=v.beforePanelSentinel.current)?void 0:n.contains)&&r.call(n,a)||null!=(o=null==(l=v.afterPanelSentinel.current)?void 0:l.contains)&&o.call(l,a))&&a.focus({preventScroll:!0})))}:void 0,tabIndex:-1},Z=(0,P.l)(),z=(0,g.z)(()=>{let e=I.current;e&&(0,a.E)(Z.current,{[P.N.Forwards]:()=>{var t;(0,p.jA)(e,p.TO.First)===p.fE.Error&&(null==(t=v.afterPanelSentinel.current)||t.focus())},[P.N.Backwards]:()=>{var e;null==(e=v.button)||e.focus({preventScroll:!0})}})}),F=(0,g.z)(()=>{let e=I.current;e&&(0,a.E)(Z.current,{[P.N.Forwards]:()=>{var e;if(!v.button)return;let t=(0,p.GO)(),n=t.indexOf(v.button),r=t.slice(0,n+1),l=[...t.slice(n+1),...r];for(let t of l.slice())if("true"===t.dataset.headlessuiFocusGuard||null!=(e=v.panel)&&e.contains(t)){let e=l.indexOf(t);-1!==e&&l.splice(e,1)}(0,p.jA)(l,p.TO.First,{sorted:!1})},[P.N.Backwards]:()=>{var t;(0,p.jA)(e,p.TO.Previous)===p.fE.Error&&(null==(t=v.button)||t.focus())}})});return o.createElement(R.Provider,{value:r},C&&h&&o.createElement(y._,{id:S,ref:v.beforePanelSentinel,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:z}),(0,s.sY)({ourProps:O,theirProps:d,slot:D,defaultTag:"div",features:B,visible:C,name:"Popover.Panel"}),C&&h&&o.createElement(y._,{id:N,ref:v.afterPanelSentinel,features:y.A.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F}))}),Group:(0,s.yV)(function(e,t){let n=(0,o.useRef)(null),r=(0,u.T)(n,t),[l,a]=(0,o.useState)([]),i=(0,N.H)(),c=(0,g.z)(e=>{a(t=>{let n=t.indexOf(e);if(-1!==n){let e=t.slice();return e.splice(n,1),e}return t})}),d=(0,g.z)(e=>(a(t=>[...t,e]),()=>c(e))),p=(0,g.z)(()=>{var e;let t=(0,x.r)(n);if(!t)return!1;let r=t.activeElement;return!!(null!=(e=n.current)&&e.contains(r))||l.some(e=>{var n,l;return(null==(n=t.getElementById(e.buttonId.current))?void 0:n.contains(r))||(null==(l=t.getElementById(e.panelId.current))?void 0:l.contains(r))})}),f=(0,g.z)(e=>{for(let t of l)t.buttonId.current!==e&&t.close()}),v=(0,o.useMemo)(()=>({registerPopover:d,unregisterPopover:c,isFocusWithinPopoverGroup:p,closeOthers:f,mainTreeNodeRef:i.mainTreeNodeRef}),[d,c,p,f,i.mainTreeNodeRef]),m=(0,o.useMemo)(()=>({}),[]);return o.createElement(D.Provider,{value:v},(0,s.sY)({ourProps:{ref:r},theirProps:e,slot:m,defaultTag:"div",name:"Popover.Group"}),o.createElement(i.MainTreeNode,null))})})}}]);