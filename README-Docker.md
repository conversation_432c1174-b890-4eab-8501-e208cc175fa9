# E-commerce Platform - Docker Setup

This project contains a complete e-commerce platform with three main services:
- **API REST**: NestJS backend API
- **Admin Panel**: Next.js admin dashboard
- **Shop Frontend**: Next.js customer-facing shop

## Architecture

The platform uses:
- **Traefik**: Reverse proxy and load balancer
- **PostgreSQL**: Primary database
- **MinIO**: S3-compatible object storage
- **Docker Compose**: Container orchestration

## Quick Start

### Prerequisites
- Docker Desktop installed and running
- At least 4GB RAM available
- Ports 80, 443, 5432, 8080, 9000, 9001 available

### 1. Check Docker Installation
```powershell
# Check if Docker is ready
.\check-docker.ps1
```

### 2. Start All Services

**Windows (PowerShell):**
```powershell
.\docker-init.ps1
```

**Windows (Command Prompt):**
```cmd
docker-init.bat
```

**Linux/Mac:**
```bash
chmod +x docker-init.sh
./docker-init.sh
```

### 3. Access the Platform
After startup (takes ~2-3 minutes), access:

- **Shop Frontend**: http://shop.localhost
- **Admin Panel**: http://admin.localhost  
- **API Documentation**: http://api.localhost/docs
- **MinIO Console**: http://minio-console.localhost
- **Traefik Dashboard**: http://traefik.localhost:8080

## Manual Setup

If you prefer manual setup:

```bash
# Create directories
mkdir -p letsencrypt

# Start services
docker-compose up --build -d

# Wait for PostgreSQL to be ready
sleep 30

# Setup database
docker-compose exec api-rest npm run db:migrate
docker-compose exec api-rest node simple-seed.js
```

## Development Mode

For development with hot reload:

```bash
# Use development override
docker-compose -f docker-compose.yml -f docker-compose.override.yml up --build
```

## Default Credentials

- **Admin Panel**: <EMAIL> / password
- **MinIO**: minioadmin / minioadmin123

## Services Overview

### API REST (Port 5000)
- NestJS backend
- PostgreSQL database
- Swagger documentation at `/docs`
- Health check at `/api/health`

### Admin Panel (Port 3002)
- Next.js admin dashboard
- Product management
- Order management
- User management

### Shop Frontend (Port 3003)
- Next.js customer interface
- Product catalog
- Shopping cart
- Checkout process

### Supporting Services

#### Traefik
- Reverse proxy on ports 80/443
- Dashboard on port 8080
- Automatic HTTPS with Let's Encrypt

#### PostgreSQL
- Database on port 5432
- Persistent data storage

#### MinIO
- Object storage on port 9000
- Console on port 9001
- S3-compatible API

## Useful Commands

```bash
# View logs
docker-compose logs -f [service-name]

# Restart a service
docker-compose restart [service-name]

# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v

# Rebuild a specific service
docker-compose build [service-name]

# Execute commands in containers
docker-compose exec api-rest npm run db:migrate
docker-compose exec postgres psql -U ecommerce_owner -d ecommerce
```

## Troubleshooting

### Services not accessible
1. Check if all containers are running: `docker-compose ps`
2. Check logs: `docker-compose logs -f`
3. Verify DNS resolution: Add entries to `/etc/hosts` if needed:
   ```
   127.0.0.1 shop.localhost
   127.0.0.1 admin.localhost
   127.0.0.1 api.localhost
   127.0.0.1 minio.localhost
   127.0.0.1 minio-console.localhost
   127.0.0.1 traefik.localhost
   ```

### Database connection issues
1. Wait for PostgreSQL to fully start (30+ seconds)
2. Check database logs: `docker-compose logs postgres`
3. Verify connection: `docker-compose exec postgres pg_isready`

### Build failures
1. Clear Docker cache: `docker system prune -a`
2. Rebuild without cache: `docker-compose build --no-cache`

## Production Deployment

For production:
1. Update environment variables in `docker-compose.yml`
2. Configure proper SSL certificates
3. Set up proper backup strategies
4. Configure monitoring and logging
5. Use external database for better performance

## File Structure

```
├── api-rest/              # NestJS API service
├── admin-rest/            # Admin dashboard
├── shop/                  # Shop frontend
├── traefik/               # Traefik configuration
├── docker-compose.yml     # Main compose file
├── docker-compose.override.yml  # Development overrides
├── docker-init.sh         # Initialization script
└── README-Docker.md       # This file
```
