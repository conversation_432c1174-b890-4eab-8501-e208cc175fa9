(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9495,2007,2036],{38419:function(e,t,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/settings/seo",function(){return l(91872)}])},62003:function(e,t,l){"use strict";l.d(t,{s:function(){return ChevronLeft}});var r=l(85893);let ChevronLeft=e=>(0,r.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})},12032:function(e,t,l){"use strict";l.d(t,{N:function(){return SaveIcon}});var r=l(85893);let SaveIcon=e=>(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",...e,children:(0,r.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})})},97670:function(e,t,l){"use strict";l.r(t);var r=l(85893),i=l(78985),s=l(79362),n=l(8144),o=l(74673),a=l(99494),u=l(5233),d=l(1631),c=l(11163),m=l(48583),f=l(93967),p=l.n(f),x=l(30824),b=l(62964);let SidebarItemMap=e=>{let{menuItems:t}=e,{t:l}=(0,u.$G)(),[i,n]=(0,m.KO)(s.Hf),{childMenu:o}=t,{width:a}=(0,b.Z)();return(0,r.jsx)("div",{className:"space-y-2",children:null==o?void 0:o.map(e=>{let{href:t,label:n,icon:o,childMenu:u}=e;return(0,r.jsx)(d.Z,{href:t,label:l(n),icon:o,childMenu:u,miniSidebar:i&&a>=s.h2},n)})})},SideBarGroup=()=>{var e;let{t}=(0,u.$G)(),[l,i]=(0,m.KO)(s.Hf),n=null===a.siteSettings||void 0===a.siteSettings?void 0:null===(e=a.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,o=Object.keys(n),{width:d}=(0,b.Z)();return(0,r.jsx)(r.Fragment,{children:null==o?void 0:o.map((e,i)=>{var o;return(0,r.jsxs)("div",{className:p()("flex flex-col px-5",l&&d>=s.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,r.jsx)("div",{className:p()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",l&&d>=s.h2?"hidden":""),children:t(null===(o=n[e])||void 0===o?void 0:o.label)}),(0,r.jsx)(SidebarItemMap,{menuItems:n[e]})]},i)})})};t.default=e=>{let{children:t}=e,{locale:l}=(0,c.useRouter)(),[a,u]=(0,m.KO)(s.Hf),[d]=(0,m.KO)(s.GH),[f]=(0,m.KO)(s.W4),{width:v}=(0,b.Z)();return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===l||"he"===l?"rtl":"ltr",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)(o.Z,{children:(0,r.jsx)(SideBarGroup,{})}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)("aside",{className:p()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",v>=s.h2&&(d||f)?"lg:pt-[8.75rem]":"pt-20",a&&v>=s.h2?"lg:w-24":"lg:w-76"),children:(0,r.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,r.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,r.jsx)(SideBarGroup,{})})})}),(0,r.jsxs)("main",{className:p()("relative flex w-full flex-col justify-start transition-[padding] duration-300",v>=s.h2&&(d||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",a&&v>=s.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,r.jsx)("div",{className:"h-full p-5 md:p-8",children:t}),(0,r.jsx)(n.Z,{})]})]})]})}},59122:function(e,t,l){"use strict";l.d(t,{Z:function(){return SettingsPageHeader}});var r=l(85893),i=l(11163),s=l(99494),n=l(8152),o=l(93967),a=l.n(o),u=l(5233),d=l(67294),c=l(86998),m=l(62003);function SettingsPageHeader(e){var t,l,o,f;let{pageTitle:p}=e,{t:x}=(0,u.$G)(),b=(0,i.useRouter)(),{sliderEl:v,sliderPrevBtn:h,sliderNextBtn:g,scrollToTheRight:j,scrollToTheLeft:w}=function(){let e=(0,d.useRef)(null),t=(0,d.useRef)(null),l=(0,d.useRef)(null);return(0,d.useEffect)(()=>{let r=e.current,i=t.current,s=l.current,n=r.classList.contains("formPageHeaderSliderElJS");function initNextPrevBtnVisibility(){let e=r.offsetWidth;r.scrollWidth>e?(null==s||s.classList.remove("opacity-0","invisible"),n&&(null==r||r.classList.add("!-mb-[43px]"))):(null==s||s.classList.add("opacity-0","invisible"),n&&(null==r||r.classList.remove("!-mb-[43px]"))),null==i||i.classList.add("opacity-0","invisible")}function visibleNextAndPrevBtnOnScroll(){let e=null==r?void 0:r.scrollLeft,t=null==r?void 0:r.offsetWidth;(null==r?void 0:r.scrollWidth)-e==t?(null==s||s.classList.add("opacity-0","invisible"),null==i||i.classList.remove("opacity-0","invisible")):null==s||s.classList.remove("opacity-0","invisible"),0===e?(null==i||i.classList.add("opacity-0","invisible"),null==s||s.classList.remove("opacity-0","invisible")):null==i||i.classList.remove("opacity-0","invisible")}return initNextPrevBtnVisibility(),window.addEventListener("resize",initNextPrevBtnVisibility),r.addEventListener("scroll",visibleNextAndPrevBtnOnScroll),()=>{window.removeEventListener("resize",initNextPrevBtnVisibility),r.removeEventListener("scroll",visibleNextAndPrevBtnOnScroll)}},[]),{sliderEl:e,sliderPrevBtn:t,sliderNextBtn:l,scrollToTheRight:function(){let l=e.current.offsetWidth;e.current.scrollLeft+=l/2,t.current.classList.remove("opacity-0","invisible")},scrollToTheLeft:function(){let t=e.current.offsetWidth;e.current.scrollLeft-=t/2,l.current.classList.remove("opacity-0","invisible")}}}(),y=null===s.siteSettings||void 0===s.siteSettings?void 0:null===(f=s.siteSettings.sidebarLinks)||void 0===f?void 0:null===(o=f.admin)||void 0===o?void 0:null===(l=o.settings)||void 0===l?void 0:null===(t=l.childMenu[0])||void 0===t?void 0:t.childMenu,N=b.asPath.split("#")[0].split("?")[0];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex pt-1 pb-5 sm:pb-8",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-heading",children:x(p)})}),(0,r.jsxs)("div",{className:"relative mb-9 flex items-center overflow-hidden border-b border-border-base/90 lg:mb-12",children:[(0,r.jsx)("button",{title:"Prev",ref:h,onClick:()=>w(),className:"absolute -top-1 z-10 h-[calc(100%-4px)] w-8 bg-gradient-to-r from-gray-100 via-gray-100 to-transparent px-0 text-gray-500 start-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(m.s,{className:"h-[18px] w-[18px]"})}),(0,r.jsx)("div",{className:"flex items-start overflow-hidden",children:(0,r.jsx)("div",{className:"custom-scrollbar-none flex w-full items-center gap-6 overflow-x-auto scroll-smooth text-[15px] md:gap-7 lg:gap-10",ref:v,children:null==y?void 0:y.map((e,t)=>(0,r.jsx)(n.Z,{href:{pathname:null==e?void 0:e.href,query:{parents:"Settings"}},as:null==e?void 0:e.href,className:a()("relative shrink-0 pb-3 font-medium text-body before:absolute before:bottom-0 before:h-px before:bg-accent before:content-[''] hover:text-heading",N===e.href?"text-heading before:w-full":null),children:x(e.label)},t))})}),(0,r.jsx)("button",{title:"Next",ref:g,onClick:()=>j(),className:"absolute -top-1 z-10 flex h-[calc(100%-4px)] w-8 items-center justify-center bg-gradient-to-l from-gray-100 via-gray-100 to-transparent text-gray-500 end-0 hover:text-black 3xl:hidden",children:(0,r.jsx)(c._,{className:"h-[18px] w-[18px]"})})]})]})}},22220:function(e,t,l){"use strict";var r=l(85893),i=l(93967),s=l.n(i),n=l(98388);t.Z=e=>{let{children:t,className:l,...i}=e;return(0,r.jsx)("div",{className:(0,n.m6)(s()("sticky bottom-0 -mx-5 bg-gray-100/10 py-3 px-5 backdrop-blur text-end md:py-5 lg:-mx-8 lg:px-8",l)),...i,children:t})}},95414:function(e,t,l){"use strict";var r=l(85893),i=l(71611),s=l(93967),n=l.n(s),o=l(67294),a=l(98388);let u=o.forwardRef((e,t)=>{let{className:l,label:s,toolTipText:o,name:u,error:d,variant:c="normal",shadow:m=!1,inputClassName:f,disabled:p,required:x,...b}=e,v=n()("align-middle py-3 px-4 w-full rounded appearance-none transition duration-300 ease-in-out text-heading text-sm focus:outline-none focus:ring-0",{"bg-gray-100 border border-border-base focus:shadow focus:bg-light focus:border-accent":"normal"===c,"bg-gray-100 border border-border-100 focus:bg-light focus:border-accent":"solid"===c,"border border-border-base focus:border-accent":"outline"===c},{"focus:shadow":m},f);return(0,r.jsxs)("div",{className:(0,a.m6)(n()(l)),children:[s&&(0,r.jsx)(i.Z,{htmlFor:u,toolTipText:o,label:s,required:x}),(0,r.jsx)("textarea",{id:u,name:u,className:(0,a.m6)(n()(v,p?"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]":"")),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:"false",rows:4,ref:t,disabled:p,...b}),d&&(0,r.jsx)("p",{className:"my-2 text-xs text-red-500 ltr:text-left rtl:text-right",children:d})]})});u.displayName="TextArea",t.Z=u},91872:function(e,t,l){"use strict";l.r(t),l.d(t,{__N_SSG:function(){return T},default:function(){return SeoSettings}});var r=l(85893),i=l(97670),s=l(92072),n=l(60802),o=l(80602),a=l(66272),u=l(33e3),d=l(95414),c=l(90573),m=l(47533),f=l(5233),p=l(11163),x=l(67294),b=l(87536),v=l(16310);let h=v.Ry().shape({});var g=l(12032),j=l(22220),w=l(3986);function SeoSettingsForm(e){let{settings:t}=e,{t:l}=(0,f.$G)(),{locale:i}=(0,p.useRouter)(),[v,y]=(0,x.useState)(!1),{mutate:N,isLoading:S}=(0,c.B)(),{language:L,options:T}=null!=t?t:{},{register:k,handleSubmit:Z,control:C,reset:E,formState:{isDirty:_}}=(0,b.cI)({shouldUnregister:!0,resolver:(0,m.X)(h),defaultValues:{...T}});async function onSubmit(e){var t;N({language:i,options:{...e,...T,seo:{...null==e?void 0:e.seo,ogImage:null==e?void 0:null===(t=e.seo)||void 0===t?void 0:t.ogImage}}}),E(e,{keepValues:!0})}return(0,w.H)({isDirty:_}),(0,r.jsxs)("form",{onSubmit:Z(onSubmit),children:[(0,r.jsxs)("div",{className:"flex flex-wrap pb-8 my-5 border-b border-dashed border-border-base sm:mt-8 sm:mb-3",children:[(0,r.jsx)(o.Z,{title:l("text-seo"),details:l("form:tax-form-seo-info-help-text"),className:"w-full px-0 pb-5 sm:w-4/12 sm:py-8 sm:pr-4 md:w-1/3 md:pr-5"}),(0,r.jsxs)(s.Z,{className:"w-full sm:w-8/12 md:w-2/3",children:[(0,r.jsx)(u.Z,{label:l("form:input-label-meta-title"),toolTipText:l("form:input-tooltip-meta-title"),...k("seo.metaTitle"),variant:"outline",className:"mb-5"}),(0,r.jsx)(d.Z,{label:l("form:input-label-meta-description"),toolTipText:l("form:input-tooltip-meta-description"),...k("seo.metaDescription"),variant:"outline",className:"mb-5"}),(0,r.jsx)(u.Z,{label:l("form:input-label-meta-tags"),toolTipText:l("form:input-tooltip-meta-tags"),...k("seo.metaTags"),variant:"outline",className:"mb-5"}),(0,r.jsx)(u.Z,{label:l("form:input-label-canonical-url"),toolTipText:l("form:input-tooltip-canonical-url"),...k("seo.canonicalUrl"),variant:"outline",className:"mb-5"}),(0,r.jsx)(u.Z,{label:l("form:input-label-og-title"),toolTipText:l("form:input-tooltip-og-title"),...k("seo.ogTitle"),variant:"outline",className:"mb-5"}),(0,r.jsx)(d.Z,{label:l("form:input-label-og-description"),toolTipText:l("form:input-tooltip-og-description"),...k("seo.ogDescription"),variant:"outline",className:"mb-5"}),(0,r.jsx)("div",{className:"mb-5",children:(0,r.jsx)(a.Z,{label:l("form:input-label-og-image"),toolTipText:l("form:input-tooltip-og-image"),name:"seo.ogImage",control:C,multiple:!1})}),(0,r.jsx)(u.Z,{label:l("form:input-label-twitter-handle"),toolTipText:l("form:input-tooltip-twitter-handle"),...k("seo.twitterHandle"),variant:"outline",className:"mb-5",placeholder:"your twitter username (exp: @username)"}),(0,r.jsx)(u.Z,{label:l("form:input-label-twitter-card-type"),toolTipText:l("form:input-tooltip-twitter-card-type"),...k("seo.twitterCardType"),variant:"outline",className:"mb-5",placeholder:"one of summary, summary_large_image, app, or player"})]})]}),(0,r.jsx)(j.Z,{className:"z-0",children:(0,r.jsxs)(n.Z,{loading:S,disabled:S||!_,className:"text-sm md:text-base",children:[(0,r.jsx)(g.N,{className:"relative w-6 h-6 top-px shrink-0 ltr:mr-2 rtl:pl-2"}),l("form:button-label-save-settings")]})})]})}var y=l(59122),N=l(45957),S=l(55846),L=l(16203),T=!0;function SeoSettings(){let{t:e}=(0,f.$G)(),{locale:t}=(0,p.useRouter)(),{settings:l,loading:i,error:s}=(0,c.n)({language:t});return i?(0,r.jsx)(S.Z,{text:e("common:text-loading")}):s?(0,r.jsx)(N.Z,{message:s.message}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.Z,{pageTitle:"form:form-title-seo-settings"}),(0,r.jsx)(SeoSettingsForm,{settings:l})]})}SeoSettings.authenticate={permissions:L.M$},SeoSettings.Layout=i.default},3986:function(e,t,l){"use strict";l.d(t,{H:function(){return useConfirmRedirectIfDirty}});var r=l(67294),i=l(11163);function useConfirmRedirectIfDirty(e){let{isDirty:t,message:l="You have unsaved changes - are you sure you wish to leave this page?"}=e,s=(0,i.useRouter)(),n=(0,r.useRef)(t),o=(0,r.useRef)(l);(0,r.useEffect)(()=>{n.current=t},[t]),(0,r.useEffect)(()=>{o.current=l},[l]);let a=(0,r.useCallback)(e=>{if(n.current)return e.preventDefault(),e.returnValue=o.current},[]),u=(0,r.useCallback)(()=>{if(n.current&&!window.confirm(o.current))throw s.events.emit("routeChangeError"),"routeChange aborted."},[]);(0,r.useEffect)(()=>(window.addEventListener("beforeunload",a),s.events.on("routeChangeStart",u),()=>{window.removeEventListener("beforeunload",a),s.events.off("routeChangeStart",u)}),[a,u])}}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,2216,2512,9494,5535,8186,1285,1631,5468,9774,2888,179],function(){return e(e.s=38419)}),_N_E=e.O()}]);