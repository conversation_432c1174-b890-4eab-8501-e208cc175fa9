(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9965],{1200:function(e,l,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shop-transfer/[transaction_identifier]",function(){return r(7341)}])},97670:function(e,l,r){"use strict";r.r(l);var t=r(85893),i=r(78985),n=r(79362),s=r(8144),a=r(74673),d=r(99494),o=r(5233),u=r(1631),c=r(11163),h=r(48583),f=r(93967),p=r.n(f),x=r(30824),g=r(62964);let SidebarItemMap=e=>{let{menuItems:l}=e,{t:r}=(0,o.$G)(),[i,s]=(0,h.KO)(n.Hf),{childMenu:a}=l,{width:d}=(0,g.Z)();return(0,t.jsx)("div",{className:"space-y-2",children:null==a?void 0:a.map(e=>{let{href:l,label:s,icon:a,childMenu:o}=e;return(0,t.jsx)(u.Z,{href:l,label:r(s),icon:a,childMenu:o,miniSidebar:i&&d>=n.h2},s)})})},SideBarGroup=()=>{var e;let{t:l}=(0,o.$G)(),[r,i]=(0,h.KO)(n.Hf),s=null===d.siteSettings||void 0===d.siteSettings?void 0:null===(e=d.siteSettings.sidebarLinks)||void 0===e?void 0:e.admin,a=Object.keys(s),{width:u}=(0,g.Z)();return(0,t.jsx)(t.Fragment,{children:null==a?void 0:a.map((e,i)=>{var a;return(0,t.jsxs)("div",{className:p()("flex flex-col px-5",r&&u>=n.h2?"border-b border-dashed border-gray-200 py-5":"pt-6 pb-3"),children:[(0,t.jsx)("div",{className:p()("px-3 pb-5 text-xs font-semibold uppercase tracking-[0.05em] text-body/60",r&&u>=n.h2?"hidden":""),children:l(null===(a=s[e])||void 0===a?void 0:a.label)}),(0,t.jsx)(SidebarItemMap,{menuItems:s[e]})]},i)})})};l.default=e=>{let{children:l}=e,{locale:r}=(0,c.useRouter)(),[d,o]=(0,h.KO)(n.Hf),[u]=(0,h.KO)(n.GH),[f]=(0,h.KO)(n.W4),{width:m}=(0,g.Z)();return(0,t.jsxs)("div",{className:"flex min-h-screen flex-col bg-gray-100 transition-colors duration-150",dir:"ar"===r||"he"===r?"rtl":"ltr",children:[(0,t.jsx)(i.Z,{}),(0,t.jsx)(a.Z,{children:(0,t.jsx)(SideBarGroup,{})}),(0,t.jsxs)("div",{className:"flex flex-1",children:[(0,t.jsx)("aside",{className:p()("fixed bottom-0 z-10 hidden h-full w-72 bg-white shadow transition-[width] duration-300 ltr:left-0 ltr:right-auto rtl:right-0 rtl:left-auto lg:block",m>=n.h2&&(u||f)?"lg:pt-[8.75rem]":"pt-20",d&&m>=n.h2?"lg:w-24":"lg:w-76"),children:(0,t.jsx)("div",{className:"sidebar-scrollbar h-full w-full overflow-x-hidden",children:(0,t.jsx)(x.Z,{className:"h-full w-full",options:{scrollbars:{autoHide:"never"}},children:(0,t.jsx)(SideBarGroup,{})})})}),(0,t.jsxs)("main",{className:p()("relative flex w-full flex-col justify-start transition-[padding] duration-300",m>=n.h2&&(u||f)?"lg:pt-[8.75rem]":"pt-[72px] lg:pt-20",d&&m>=n.h2?"ltr:lg:pl-24 rtl:lg:pr-24":"ltr:xl:pl-76 rtl:xl:pr-76 ltr:lg:pl-72 rtl:lg:pr-72 rtl:lg:pl-0"),children:[(0,t.jsx)("div",{className:"h-full p-5 md:p-8",children:l}),(0,t.jsx)(s.Z,{})]})]})]})}},7341:function(e,l,r){"use strict";r.r(l),r.d(l,{__N_SSP:function(){return w}});var t=r(85893),i=r(97670),n=r(24724),s=r(45957),a=r(55846),d=r(76240),o=r(99930),u=r(16203),c=r(27484),h=r.n(c),f=r(84110),p=r.n(f),x=r(29387),g=r.n(x),m=r(70178),v=r.n(m),b=r(5233),j=r(11163);h().extend(p()),h().extend(v()),h().extend(g());let OwnershipTransferSinglePage=()=>{let{query:e,locale:l}=(0,j.useRouter)(),{t:r}=(0,b.$G)(),{ownershipTransfer:i,loading:u,error:c,refetch:h}=(0,d.OM)({transaction_identifier:null==e?void 0:e.transaction_identifier,language:l,request_view_type:"detail"}),{data:f,isLoading:p,error:x}=(0,o.UE)();return u||p?(0,t.jsx)(a.Z,{text:r("common:text-loading")}):c||x?(0,t.jsx)(s.Z,{message:(null==c?void 0:c.message)||(null==x?void 0:x.message)}):(0,t.jsx)(n.P,{data:i,userId:null==f?void 0:f.id})};OwnershipTransferSinglePage.authenticate={permissions:u.M$},OwnershipTransferSinglePage.Layout=i.default;var w=!0;l.default=OwnershipTransferSinglePage}},function(e){e.O(0,[6342,4750,5921,1609,2964,8544,2023,6117,7536,5601,6994,2787,9494,5535,8186,1285,1631,8504,919,4724,9774,2888,179],function(){return e(e.s=1200)}),_N_E=e.O()}]);