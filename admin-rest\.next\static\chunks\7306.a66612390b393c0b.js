(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7306,2036],{44174:function(e){e.exports=function(e,t,r,n){for(var o=-1,s=null==e?0:e.length;++o<s;){var a=e[o];t(n,a,r(a),e)}return n}},81119:function(e,t,r){var n=r(89881);e.exports=function(e,t,r,o){return n(e,function(e,n,s){t(o,e,r(e),s)}),o}},89465:function(e,t,r){var n=r(38777);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},89881:function(e,t,r){var n=r(47816),o=r(99291)(n);e.exports=o},28483:function(e,t,r){var n=r(25063)();e.exports=n},47816:function(e,t,r){var n=r(28483),o=r(3674);e.exports=function(e,t){return e&&n(e,t,o)}},13:function(e){e.exports=function(e,t){return null!=e&&t in Object(e)}},2958:function(e,t,r){var n=r(46384),o=r(90939);e.exports=function(e,t,r,s){var a=r.length,i=a,c=!s;if(null==e)return!i;for(e=Object(e);a--;){var u=r[a];if(c&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++a<i;){var l=(u=r[a])[0],d=e[l],f=u[1];if(c&&u[2]){if(void 0===d&&!(l in e))return!1}else{var m=new n;if(s)var h=s(d,f,l,e,t,m);if(!(void 0===h?o(f,d,3,s,m):h))return!1}}return!0}},67206:function(e,t,r){var n=r(91573),o=r(16432),s=r(6557),a=r(1469),i=r(39601);e.exports=function(e){return"function"==typeof e?e:null==e?s:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):i(e)}},91573:function(e,t,r){var n=r(2958),o=r(1499),s=r(42634);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?s(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},16432:function(e,t,r){var n=r(90939),o=r(27361),s=r(79095),a=r(15403),i=r(89162),c=r(42634),u=r(40327);e.exports=function(e,t){return a(e)&&i(t)?c(u(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?s(r,e):n(t,a,3)}}},40371:function(e){e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},79152:function(e,t,r){var n=r(97786);e.exports=function(e){return function(t){return n(t,e)}}},55189:function(e,t,r){var n=r(44174),o=r(81119),s=r(67206),a=r(1469);e.exports=function(e,t){return function(r,i){var c=a(r)?n:o,u=t?t():{};return c(r,e,s(i,2),u)}}},99291:function(e,t,r){var n=r(98612);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var s=r.length,a=t?s:-1,i=Object(r);(t?a--:++a<s)&&!1!==o(i[a],a,i););return r}}},25063:function(e){e.exports=function(e){return function(t,r,n){for(var o=-1,s=Object(t),a=n(t),i=a.length;i--;){var c=a[e?i:++o];if(!1===r(s[c],c,s))break}return t}}},38777:function(e,t,r){var n=r(10852),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},1499:function(e,t,r){var n=r(89162),o=r(3674);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var s=t[r],a=e[s];t[r]=[s,a,n(a)]}return t}},222:function(e,t,r){var n=r(71811),o=r(35694),s=r(1469),a=r(65776),i=r(41780),c=r(40327);e.exports=function(e,t,r){t=n(t,e);for(var u=-1,l=t.length,d=!1;++u<l;){var f=c(t[u]);if(!(d=null!=e&&r(e,f)))break;e=e[f]}return d||++u!=l?d:!!(l=null==e?0:e.length)&&i(l)&&a(f,l)&&(s(e)||o(e))}},89162:function(e,t,r){var n=r(13218);e.exports=function(e){return e==e&&!n(e)}},42634:function(e){e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},27361:function(e,t,r){var n=r(97786);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},7739:function(e,t,r){var n=r(89465),o=r(55189),s=Object.prototype.hasOwnProperty,a=o(function(e,t,r){s.call(e,r)?e[r].push(t):n(e,r,[t])});e.exports=a},79095:function(e,t,r){var n=r(13),o=r(222);e.exports=function(e,t){return null!=e&&o(e,t,n)}},18446:function(e,t,r){var n=r(90939);e.exports=function(e,t){return n(e,t)}},39601:function(e,t,r){var n=r(40371),o=r(79152),s=r(15403),a=r(40327);e.exports=function(e){return s(e)?n(a(e)):o(e)}},97092:function(e,t,r){"use strict";r.d(t,{o:function(){return AddToCart}});var n=r(85893),o=r(96475),s=r(85031),cart=e=>(0,n.jsx)("svg",{...e,viewBox:"0 0 14.4 12",children:(0,n.jsx)("g",{transform:"translate(-288 -413.89)",children:(0,n.jsx)("path",{fill:"currentColor",d:"M298.7,418.289l-2.906-4.148a.835.835,0,0,0-.528-.251.607.607,0,0,0-.529.251l-2.905,4.148h-3.17a.609.609,0,0,0-.661.625v.191l1.651,5.84a1.336,1.336,0,0,0,1.255.945h8.588a1.261,1.261,0,0,0,1.254-.945l1.651-5.84v-.191a.609.609,0,0,0-.661-.625Zm-5.419,0,1.984-2.767,1.98,2.767Zm1.984,5.024a1.258,1.258,0,1,1,1.319-1.258,1.3,1.3,0,0,1-1.319,1.258Zm0,0"})})}),a=r(5233),i=r(93967),c=r.n(i),add_to_cart_btn=e=>{let{variant:t,onClick:r,disabled:o}=e,{t:i}=(0,a.$G)("common");switch(t){case"neon":return(0,n.jsxs)("button",{onClick:r,disabled:o,className:"group flex h-7 w-full items-center justify-between rounded bg-gray-100 text-xs text-body-dark transition-colors hover:border-accent hover:bg-accent hover:text-light focus:border-accent focus:bg-accent focus:text-light focus:outline-none md:h-9 md:text-sm",children:[(0,n.jsx)("span",{className:"flex-1",children:i("text-add")}),(0,n.jsx)("span",{className:"rounded-te rounded-be grid h-7 w-7 place-items-center bg-gray-200 transition-colors duration-200 group-hover:bg-accent-600 group-focus:bg-accent-600 md:h-9 md:w-9",children:(0,n.jsx)(s.p,{className:"h-4 w-4 stroke-2 group-hover:text-light"})})]});case"argon":return(0,n.jsx)("button",{onClick:r,disabled:o,className:"flex h-7 w-7 items-center justify-center rounded border border-border-200 bg-light text-sm text-heading transition-colors hover:border-accent hover:bg-accent hover:text-light focus:border-accent focus:bg-accent focus:text-light focus:outline-none md:h-9 md:w-9",children:(0,n.jsx)(s.p,{className:"h-5 w-5 stroke-2"})});case"oganesson":return(0,n.jsxs)("button",{onClick:r,disabled:o,className:"shadow-500 flex h-8 w-8 items-center justify-center rounded-full bg-accent text-sm text-light transition-colors hover:border-accent hover:bg-accent hover:text-light focus:border-accent focus:bg-accent focus:text-light focus:outline-none md:h-10 md:w-10",children:[(0,n.jsx)("span",{className:"sr-only",children:i("text-plus")}),(0,n.jsx)(s.p,{className:"h-5 w-5 stroke-2 md:h-6 md:w-6"})]});case"single":return(0,n.jsxs)("button",{onClick:r,disabled:o,className:"order-5 flex items-center justify-center rounded-full border-2 border-border-100 bg-light py-2 px-3 text-sm font-semibold text-accent transition-colors duration-300 hover:border-accent hover:bg-accent hover:text-light focus:border-accent focus:bg-accent focus:text-light focus:outline-none sm:order-4 sm:justify-start sm:px-5",children:[(0,n.jsx)(cart,{className:"me-2.5 h-4 w-4"}),(0,n.jsx)("span",{children:i("text-cart")})]});case"big":return(0,n.jsx)("button",{onClick:r,disabled:o,className:c()("flex w-full items-center justify-center rounded bg-accent py-4 px-5 text-sm font-light text-light transition-colors duration-300 hover:bg-accent-hover focus:bg-accent-hover focus:outline-none lg:text-base",{"cursor-not-allowed border border-border-400 !bg-gray-300 !text-body hover:!bg-gray-300":o}),children:(0,n.jsx)("span",{children:i("text-add-cart")})});default:return(0,n.jsxs)("button",{onClick:r,disabled:o,title:o?"Out Of Stock":"",className:"flex h-7 w-7 items-center justify-center rounded border border-border-200 bg-light text-sm text-accent transition-colors hover:border-accent hover:bg-accent hover:text-light focus:border-accent focus:bg-accent focus:text-light focus:outline-none md:h-9 md:w-9",children:[(0,n.jsx)("span",{className:"sr-only",children:i("text-plus")}),(0,n.jsx)(s.p,{className:"h-5 w-5 stroke-2"})]})}};let cartAnimation=e=>{let t=function(e,t){for(;e&&e!==document;e=e.parentNode)if(e.matches(t))return e;return null}(e.target,".product-card");if(!t)return;let r=document.getElementsByClassName("product-cart")[0],n=t.querySelector(".product-image"),o=t.getBoundingClientRect().left,s=t.getBoundingClientRect().top,a=r.getBoundingClientRect().left,i=r.getBoundingClientRect().top,c=n.cloneNode(!0);c.style="z-index: 11111; width: 100px;opacity:1; position:fixed; top:"+s+"px;left:"+o+"px;transition: left 1s, top 1s, width 1s, opacity 1s cubic-bezier(1, 1, 1, 1);border-radius: 50px; overflow: hidden; box-shadow: 0 21px 36px rgba(0,0,0,0.1)";var u=document.body.appendChild(c);setTimeout(function(){c.style.left=a+"px",c.style.top=i+"px",c.style.width="40px",c.style.opacity="0"},200),setTimeout(function(){u.parentNode.removeChild(u)},1e3)};var u=r(51068),l=r(41609),d=r.n(l);let AddToCart=e=>{let{data:t,variant:r="helium",counterVariant:s,counterClass:a,variation:i,disabled:c}=e,{addItemToCart:l,removeItemFromCart:f,isInStock:m,getItemFromCart:h,isInCart:p}=(0,u.jD)(),v=function(e,t){let{id:r,name:n,slug:o,image:s,price:a,sale_price:i,quantity:c,unit:u,is_digital:l}=e;return d()(t)?{id:r,name:n,slug:o,unit:u,is_digital:l,image:null==s?void 0:s.thumbnail,stock:c,price:i||a}:{id:"".concat(r,".").concat(t.id),productId:r,name:"".concat(n," - ").concat(t.title),slug:o,unit:u,is_digital:l,stock:t.quantity,price:t.sale_price?t.sale_price:t.price,image:null==s?void 0:s.thumbnail,variationId:t.id}}(t,i),handleAddClick=e=>{e.stopPropagation(),l(v,1),p(v.id)||cartAnimation(e)},x=p(null==v?void 0:v.id)&&!m(v.id);return p(null==v?void 0:v.id)?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.Z,{value:h(v.id).quantity,onDecrement:e=>{e.stopPropagation(),f(v.id)},onIncrement:handleAddClick,variant:s||r,className:a,disabled:x})}):(0,n.jsx)(add_to_cart_btn,{disabled:c||x,variant:r,onClick:handleAddClick})}},85031:function(e,t,r){"use strict";r.d(t,{p:function(){return PlusIcon}});var n=r(85893);let PlusIcon=e=>(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})},18380:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return variation}});var n=r(85893),o=r(67294),s=r(7739),a=r.n(s),i=r(41609),c=r.n(i),u=r(93967),l=r.n(u),ui_attribute=e=>{let{value:t,active:r,className:o,color:s,...a}=e,i=l()({"px-4 py-3 text-sm border rounded text-heading bg-gray-50 border-border-200":"color"!==o,"!text-light !bg-accent !border-accent":r&&"color"!==o,"h-11 w-11 p-0.5 flex items-center justify-center border-2 rounded-full border-transparent":"color"===o,"!border-accent":r&&"color"===o},"cursor-pointer");return(0,n.jsx)("div",{className:i,...a,children:"color"===o?(0,n.jsx)("span",{className:"w-full h-full rounded-full border border-border-200",style:{backgroundColor:s}}):t})},d=r(30824);let f={},m=o.createContext(f);m.displayName="AttributesContext";let AttributesProvider=e=>{let[t,r]=o.useState(f),s=o.useMemo(()=>({attributes:t,setAttributes:r}),[t]);return(0,n.jsx)(m.Provider,{value:s,...e})},useAttributes=()=>{let e=o.useContext(m);if(void 0===e)throw Error("useAttributes must be used within a SettingsProvider");return e};var variation_groups=e=>{let{variations:t}=e,{attributes:r,setAttributes:o}=useAttributes(),replaceHyphens=e=>e.replace(/-/g," ");return(0,n.jsx)(n.Fragment,{children:Object.keys(t).map((e,s)=>(0,n.jsxs)("div",{className:"flex items-center border-b  border-border-200 border-opacity-70 py-4 first:pt-0 last:border-b-0 last:pb-0",children:[(0,n.jsxs)("span",{className:"me-4 inline-block min-w-[60px] whitespace-nowrap text-sm font-semibold capitalize leading-none text-heading",children:[replaceHyphens(e),":"]}),(0,n.jsx)("div",{className:"-mb-5 w-full overflow-hidden",children:(0,n.jsx)(d.Z,{className:"w-full pb-5",options:{scrollbars:{autoHide:"never"}},children:(0,n.jsx)("div",{className:"space-s-4 flex w-full",children:t[e].map(t=>(0,n.jsx)(ui_attribute,{className:e,color:t.meta?t.meta:null==t?void 0:t.value,active:r[e]===t.value,value:t.value,onClick:()=>o(r=>({...r,[e]:t.value}))},t.id))})})})]},s))})},h=r(60942);function VariationPrice(e){let{selectedVariation:t,minPrice:r,maxPrice:o}=e,{price:s,basePrice:a}=(0,h.ZP)(t&&{amount:(null==t?void 0:t.sale_price)?Number(null==t?void 0:t.sale_price):Number(null==t?void 0:t.price),baseAmount:Number(null==t?void 0:t.price)}),{price:i}=(0,h.ZP)({amount:r}),{price:u}=(0,h.ZP)({amount:o});return(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)("ins",{className:"text-2xl font-semibold text-accent no-underline",children:c()(t)?"".concat(i," - ").concat(u):"".concat(s)}),a&&(0,n.jsx)("del",{className:"text-sm font-normal text-muted ms-2 md:text-base",children:a})]})}var p=r(18446),v=r.n(p),x=r(97092),g=r(93242),b=r(11163),y=r(55846);let Variation=e=>{var t;let{product:r}=e,{attributes:s}=useAttributes(),i=(0,o.useMemo)(()=>{var e;return(e=null==r?void 0:r.variations)?a()(e,"attribute.slug"):{}},[null==r?void 0:r.variations]),u=!!c()(i)||!c()(s)&&Object.keys(i).every(e=>s.hasOwnProperty(e)),l={};return u&&(l=null==r?void 0:null===(t=r.variation_options)||void 0===t?void 0:t.find(e=>v()(e.options.map(e=>e.value).sort(),Object.values(s).sort()))),(0,n.jsxs)("div",{className:"w-[95vw] max-w-lg rounded-md bg-white p-8",children:[(0,n.jsx)("h3",{className:"mb-2 text-center text-2xl font-semibold text-heading",children:null==r?void 0:r.name}),(0,n.jsx)("div",{className:"mb-8 flex items-center justify-center",children:(0,n.jsx)(VariationPrice,{selectedVariation:l,minPrice:null==r?void 0:r.min_price,maxPrice:null==r?void 0:r.max_price})}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsx)(variation_groups,{variations:i})}),(0,n.jsx)(x.o,{data:r,variant:"big",variation:l,disabled:(null==l?void 0:l.is_disable)||!u})]})};var variation=e=>{let{productSlug:t}=e,{locale:r}=(0,b.useRouter)(),{product:o,isLoading:s}=(0,g.FA)({slug:t,language:r});return s||!o?(0,n.jsx)("div",{className:"flex h-48 w-48 items-center justify-center rounded-md bg-white",children:(0,n.jsx)(y.Z,{})}):(0,n.jsx)(AttributesProvider,{children:(0,n.jsx)(Variation,{product:o})})}},96475:function(e,t,r){"use strict";r.d(t,{Z:function(){return counter}});var n=r(85893),o=r(93967),s=r.n(o),a=r(85031);let MinusIcon=e=>(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",...e,children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20 12H4"})});var i=r(5233);let c={helium:"w-7 h-18 sm:w-20 sm:h-7 md:h-9 md:w-24 bg-accent flex-col-reverse sm:flex-row absolute sm:static bottom-3 end-3 sm:bottom-0 sm:end-0 text-light rounded",neon:"w-full h-7 md:h-9 bg-accent text-light rounded",argon:"w-7 h-18 sm:w-20 sm:h-7 md:h-9 md:w-24 bg-accent flex-col-reverse sm:flex-row text-light rounded",oganesson:"w-20 h-8 md:w-24 md:h-10 bg-accent text-light rounded-full shadow-500",single:"order-5 sm:order-4 w-9 sm:w-24 h-24 sm:h-10 bg-accent text-light rounded-full flex-col-reverse sm:flex-row absolute sm:relative bottom-0 sm:bottom-auto end-0 sm:end-auto",details:"order-5 sm:order-4 w-full sm:w-24 h-10 bg-accent text-light rounded-full",pillVertical:"flex-col-reverse items-center w-8 h-24 bg-gray-100 text-heading rounded-full",big:"w-full h-14 rounded text-light bg-accent inline-flex justify-between"};var counter=e=>{let{value:t,variant:r="helium",onDecrement:o,onIncrement:u,className:l,disabled:d}=e,{t:f}=(0,i.$G)("common");return(0,n.jsxs)("div",{className:s()("flex overflow-hidden",c[r],l),children:[(0,n.jsxs)("button",{onClick:o,className:s()("cursor-pointer p-2 transition-colors duration-200 hover:bg-accent-hover focus:outline-none",{"px-3 py-3 sm:px-2":"single"===r,"px-5":"big"===r,"hover:!bg-gray-100":"pillVertical"===r}),children:[(0,n.jsx)("span",{className:"sr-only",children:f("text-minus")}),(0,n.jsx)(MinusIcon,{className:"h-3 w-3 stroke-2"})]}),(0,n.jsx)("div",{className:s()("flex flex-1 items-center justify-center text-sm font-semibold","pillVertical"===r&&"text-heading"),children:t}),(0,n.jsxs)("button",{onClick:u,disabled:d,className:s()("cursor-pointer p-2 transition-colors duration-200 hover:bg-accent-hover focus:outline-none",{"px-3 py-3 sm:px-2":"single"===r,"px-5":"big"===r,"hover:!bg-gray-100":"pillVertical"===r}),title:d?f("text-out-of-stock"):"",children:[(0,n.jsx)("span",{className:"sr-only",children:f("text-plus")}),(0,n.jsx)(a.p,{className:"md:h-4.5 md:w-4.5 h-3.5 w-3.5 stroke-2"})]})]})}},30824:function(e,t,r){"use strict";var n=r(85893),o=r(93967),s=r.n(o),a=r(42189);r(85251),t.Z=e=>{let{options:t,children:r,style:o,className:i,...c}=e;return(0,n.jsx)(a.E,{options:{scrollbars:{autoHide:"scroll"},...t},className:s()("os-theme-thin-dark",i),style:o,...c,children:r})}},13202:function(e,t,r){"use strict";r.d(t,{M:function(){return a}});var n=r(47869),o=r(55191),s=r(3737);let a={...(0,o.h)(n.P.PRODUCTS),get(e){let{slug:t,language:r}=e;return s.eN.get("".concat(n.P.PRODUCTS,"/").concat(t),{language:r,with:"type;shop;categories;tags;variations.attribute.values;variation_options;variation_options.digital_file;author;manufacturer;digital_file"})},paginated:e=>{let{type:t,name:r,categories:o,shop_id:a,product_type:i,status:c,...u}=e;return s.eN.get(n.P.PRODUCTS,{searchJoin:"and",with:"shop;type;categories",shop_id:a,...u,search:s.eN.formatSearchParams({type:t,name:r,categories:o,shop_id:a,product_type:i,status:c})})},popular(e){let{shop_id:t,...r}=e;return s.eN.get(n.P.POPULAR_PRODUCTS,{searchJoin:"and",with:"type;shop",...r,search:s.eN.formatSearchParams({shop_id:t})})},lowStock(e){let{shop_id:t,...r}=e;return s.eN.get(n.P.LOW_STOCK_PRODUCTS_ANALYTICS,{searchJoin:"and",with:"type;shop",...r,search:s.eN.formatSearchParams({shop_id:t})})},generateDescription:e=>s.eN.post(n.P.GENERATE_DESCRIPTION,e),newOrInActiveProducts:e=>{let{user_id:t,shop_id:r,status:o,name:a,...i}=e;return s.eN.get(n.P.NEW_OR_INACTIVE_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:o,name:a,...i,search:s.eN.formatSearchParams({status:o,name:a})})},lowOrOutOfStockProducts:e=>{let{user_id:t,shop_id:r,status:o,categories:a,name:i,type:c,...u}=e;return s.eN.get(n.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,{searchJoin:"and",user_id:t,shop_id:r,status:o,name:i,...u,search:s.eN.formatSearchParams({status:o,name:i,categories:a,type:c})})},productByCategory(e){let{limit:t,language:r}=e;return s.eN.get(n.P.CATEGORY_WISE_PRODUCTS,{limit:t,language:r})},mostSoldProductByCategory(e){let{shop_id:t,...r}=e;return s.eN.get(n.P.CATEGORY_WISE_PRODUCTS_SALE,{searchJoin:"and",...r,search:s.eN.formatSearchParams({shop_id:t})})},getProductsByFlashSale:e=>{let{user_id:t,shop_id:r,slug:o,name:a,...i}=e;return s.eN.get(n.P.PRODUCTS_BY_FLASH_SALE,{searchJoin:"and",user_id:t,shop_id:r,slug:o,name:a,...i,search:s.eN.formatSearchParams({name:a})})},topRated(e){let{shop_id:t,...r}=e;return s.eN.get(n.P.TOP_RATED_PRODUCTS,{searchJoin:"and",...r,search:s.eN.formatSearchParams({shop_id:t})})}}},93242:function(e,t,r){"use strict";r.d(t,{FA:function(){return useProductQuery},Uc:function(){return useProductsByFlashSaleQuery},YC:function(){return useInActiveProductsQuery},bJ:function(){return useGenerateDescriptionMutation},eH:function(){return useProductStockQuery},kN:function(){return useProductsQuery},qX:function(){return useCreateProductMutation},wE:function(){return useUpdateProductMutation},xq:function(){return useDeleteProductMutation}});var n=r(11163),o=r.n(n),s=r(22920),a=r(5233),i=r(88767),c=r(47869),u=r(13202),l=r(28597),d=r(97514),f=r(93345);let useCreateProductMutation=()=>{let e=(0,i.useQueryClient)(),t=(0,n.useRouter)(),{t:r}=(0,a.$G)();return(0,i.useMutation)(u.M.create,{onSuccess:async()=>{let e=t.query.shop?"/".concat(t.query.shop).concat(d.Z.product.list):d.Z.product.list;await o().push(e,void 0,{locale:f.Config.defaultLanguage}),s.Am.success(r("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{let{data:t,status:n}=null==e?void 0:e.response;if(422===n){let e=Object.values(t).flat();s.Am.error(e[0])}else{var o;s.Am.error(r("common:".concat(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.data.message)))}}})},useUpdateProductMutation=()=>{let{t:e}=(0,a.$G)(),t=(0,i.useQueryClient)(),r=(0,n.useRouter)();return(0,i.useMutation)(u.M.update,{onSuccess:async t=>{let n=r.query.shop?"/".concat(r.query.shop).concat(d.Z.product.list):d.Z.product.list;await r.push("".concat(n,"/").concat(null==t?void 0:t.slug,"/edit"),void 0,{locale:f.Config.defaultLanguage}),s.Am.success(e("common:successfully-updated"))},onSettled:()=>{t.invalidateQueries(c.P.PRODUCTS)},onError:t=>{var r;s.Am.error(e("common:".concat(null==t?void 0:null===(r=t.response)||void 0===r?void 0:r.data.message)))}})},useDeleteProductMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)();return(0,i.useMutation)(u.M.delete,{onSuccess:()=>{s.Am.success(t("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(c.P.PRODUCTS)},onError:e=>{var r;s.Am.error(t("common:".concat(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)))}})},useProductQuery=e=>{let{slug:t,language:r}=e,{data:n,error:o,isLoading:s}=(0,i.useQuery)([c.P.PRODUCTS,{slug:t,language:r}],()=>u.M.get({slug:t,language:r}));return{product:n,error:o,isLoading:s}},useProductsQuery=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{data:n,error:o,isLoading:s}=(0,i.useQuery)([c.P.PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return u.M.paginated(Object.assign({},t[1],r))},{keepPreviousData:!0,...r});return{products:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(n),error:o,loading:s}},useGenerateDescriptionMutation=()=>{let e=(0,i.useQueryClient)(),{t}=(0,a.$G)("common");return(0,i.useMutation)(u.M.generateDescription,{onSuccess:()=>{s.Am.success(t("Generated..."))},onSettled:t=>{e.refetchQueries(c.P.GENERATE_DESCRIPTION)}})},useInActiveProductsQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,i.useQuery)([c.P.NEW_OR_INACTIVE_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return u.M.newOrInActiveProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:n,loading:o}},useProductStockQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,i.useQuery)([c.P.LOW_OR_OUT_OF_STOCK_PRODUCTS,e],e=>{let{queryKey:t,pageParam:r}=e;return u.M.lowOrOutOfStockProducts(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:n,loading:o}},useProductsByFlashSaleQuery=e=>{var t;let{data:r,error:n,isLoading:o}=(0,i.useQuery)([c.P.PRODUCTS_BY_FLASH_SALE,e],e=>{let{queryKey:t,pageParam:r}=e;return u.M.getProductsByFlashSale(Object.assign({},t[1],r))},{keepPreviousData:!0});return{products:null!==(t=null==r?void 0:r.data)&&void 0!==t?t:[],paginatorInfo:(0,l.Q)(r),error:n,loading:o}}},60942:function(e,t,r){"use strict";r.d(t,{T4:function(){return formatPrice},ZP:function(){return usePrice}});var n=r(67294),o=r(99494),s=r(73263);function formatPrice(e){let{amount:t,currencyCode:r,locale:n,fractions:o=2}=e,s=new Intl.NumberFormat(n,{style:"currency",currency:r,maximumFractionDigits:o>20||o<0||!o?2:o});return s.format(t)}function usePrice(e){let{currency:t,currencyOptions:r}=(0,s.rV)(),{formation:a,fractions:i}=r,{amount:c,baseAmount:u,currencyCode:l=t}=null!=e?e:{},d=null!=a?a:o.siteSettings.defaultLanguage,f=(0,n.useMemo)(()=>"number"==typeof c&&l?u?function(e){let{amount:t,baseAmount:r,currencyCode:n,locale:o,fractions:s=2}=e,a=r<t,i=new Intl.NumberFormat(o,{style:"percent"}),c=a?i.format((t-r)/t):null,u=formatPrice({amount:t,currencyCode:n,locale:o,fractions:s}),l=a?formatPrice({amount:r,currencyCode:n,locale:o,fractions:s}):null;return{price:u,basePrice:l,discount:c}}({amount:c,baseAmount:u,currencyCode:l,locale:d,fractions:i}):formatPrice({amount:c,currencyCode:l,locale:d,fractions:i}):"",[c,u,l]);return"string"==typeof f?{price:f,basePrice:null,discount:null}:f}}}]);