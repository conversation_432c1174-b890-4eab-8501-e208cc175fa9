"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5655],{59272:function(e,n,t){t.d(n,{O:function(){return CheckMarkGhost},c:function(){return CheckMarkCircle}});var o=t(85893);t(67294);let CheckMarkCircle=e=>{let{...n}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 330 330",fill:"currentColor",...n,children:[(0,o.jsx)("path",{d:"M165 0C74.019 0 0 74.019 0 165s74.019 165 165 165 165-74.019 165-165S255.981 0 165 0zm0 300c-74.44 0-135-60.561-135-135S90.56 30 165 30s135 60.561 135 135-60.561 135-135 135z"}),(0,o.jsx)("path",{d:"M226.872 106.664l-84.854 84.853-38.89-38.891c-5.857-5.857-15.355-5.858-21.213-.001-5.858 5.858-5.858 15.355 0 21.213l49.496 49.498a15 15 0 0010.606 4.394h.001c3.978 0 7.793-1.581 10.606-4.393l95.461-95.459c5.858-5.858 5.858-15.355 0-21.213-5.858-5.858-15.355-5.859-21.213-.001z"})]})},CheckMarkGhost=e=>{let{...n}=e;return(0,o.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",...n,children:[(0,o.jsx)("path",{opacity:.2,d:"M17.5 10a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M13.567 7.683a.626.626 0 010 .884l-4.375 4.375a.626.626 0 01-.884 0l-1.875-1.875a.625.625 0 11.884-.884l1.433 1.433 3.933-3.933a.625.625 0 01.884 0zM18.125 10A8.125 8.125 0 1110 1.875 8.133 8.133 0 0118.125 10zm-1.25 0A6.875 6.875 0 1010 16.875 6.883 6.883 0 0016.875 10z",fill:"currentColor"})]})}},75655:function(e,n,t){t.r(n);var o=t(85893),s=t(71421),r=t(59272),u=t(75814),i=t(41107);n.default=()=>{let{mutate:e,isLoading:n}=(0,i._k)(),{data:t}=(0,u.X9)(),{closeModal:a}=(0,u.SO)();async function handleDelete(){e({id:t},{onSettled:()=>{a()}})}return(0,o.jsx)(s.Z,{onCancel:a,onDelete:handleDelete,deleteBtnLoading:n,deleteBtnText:"text-shop-approve-button",icon:(0,o.jsx)(r.c,{className:"m-auto mt-4 h-10 w-10 text-accent"}),deleteBtnClassName:"!bg-accent focus:outline-none hover:!bg-accent-hover focus:!bg-accent-hover",cancelBtnClassName:"!bg-red-600 focus:outline-none hover:!bg-red-700 focus:!bg-red-700",title:"text-shop-approve-description",description:""})}},41107:function(e,n,t){t.d(n,{hE:function(){return useApproveTermAndConditionMutation},NO:function(){return useCreateTermsAndConditionsMutation},Du:function(){return useDeleteTermsAndConditionsMutation},_k:function(){return useDisApproveTermAndConditionMutation},nF:function(){return useTermsAndConditionQuery},Ze:function(){return useTermsAndConditionsQuery},cb:function(){return useUpdateTermsAndConditionsMutation}});var o=t(11163),s=t.n(o),r=t(88767),u=t(22920),i=t(5233),a=t(28597),c=t(97514),l=t(47869),d=t(93345),m=t(55191),p=t(3737);let C={...(0,m.h)(l.P.TERMS_AND_CONDITIONS),paginated:e=>{let{title:n,shop_id:t,...o}=e;return p.eN.get(l.P.TERMS_AND_CONDITIONS,{searchJoin:"and",shop_id:t,...o,search:p.eN.formatSearchParams({title:n,shop_id:t})})},approve:e=>p.eN.post(l.P.APPROVE_TERMS_AND_CONDITIONS,e),disapprove:e=>p.eN.post(l.P.DISAPPROVE_TERMS_AND_CONDITIONS,e)},useApproveTermAndConditionMutation=()=>{let{t:e}=(0,i.$G)(),n=(0,r.useQueryClient)();return(0,r.useMutation)(C.approve,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(l.P.TERMS_AND_CONDITIONS)}})},useDisApproveTermAndConditionMutation=()=>{let{t:e}=(0,i.$G)(),n=(0,r.useQueryClient)();return(0,r.useMutation)(C.disapprove,{onSuccess:()=>{u.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(l.P.TERMS_AND_CONDITIONS)}})},useTermsAndConditionQuery=e=>{let{slug:n,language:t}=e,{data:o,error:s,isLoading:u}=(0,r.useQuery)([l.P.TERMS_AND_CONDITIONS,{slug:n,language:t}],()=>C.get({slug:n,language:t}));return{termsAndConditions:o,error:s,loading:u}},useTermsAndConditionsQuery=e=>{var n;let{data:t,error:o,isLoading:s}=(0,r.useQuery)([l.P.TERMS_AND_CONDITIONS,e],e=>{let{queryKey:n,pageParam:t}=e;return C.paginated(Object.assign({},n[1],t))},{keepPreviousData:!0});return{termsAndConditions:null!==(n=null==t?void 0:t.data)&&void 0!==n?n:[],paginatorInfo:(0,a.Q)(t),error:o,loading:s}},useCreateTermsAndConditionsMutation=()=>{let e=(0,r.useQueryClient)(),n=(0,o.useRouter)(),{t}=(0,i.$G)();return(0,r.useMutation)(C.create,{onSuccess:async()=>{let e=n.query.shop?"/".concat(n.query.shop).concat(c.Z.termsAndCondition.list):c.Z.termsAndCondition.list;await s().push(e,void 0,{locale:d.Config.defaultLanguage}),u.Am.success(t("common:successfully-created"))},onSettled:()=>{e.invalidateQueries(l.P.TERMS_AND_CONDITIONS)},onError:e=>{var n;u.Am.error(t("common:".concat(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.data.message)))}})},useUpdateTermsAndConditionsMutation=()=>{let{t:e}=(0,i.$G)(),n=(0,r.useQueryClient)(),t=(0,o.useRouter)();return(0,r.useMutation)(C.update,{onSuccess:async n=>{let o=t.query.shop?"/".concat(t.query.shop).concat(c.Z.termsAndCondition.list):c.Z.termsAndCondition.list;await t.push(o,void 0,{locale:d.Config.defaultLanguage}),u.Am.success(e("common:successfully-updated"))},onSettled:()=>{n.invalidateQueries(l.P.TERMS_AND_CONDITIONS)},onError:n=>{var t;u.Am.error(e("common:".concat(null==n?void 0:null===(t=n.response)||void 0===t?void 0:t.data.message)))}})},useDeleteTermsAndConditionsMutation=()=>{let e=(0,r.useQueryClient)(),{t:n}=(0,i.$G)();return(0,r.useMutation)(C.delete,{onSuccess:()=>{u.Am.success(n("common:successfully-deleted"))},onSettled:()=>{e.invalidateQueries(l.P.TERMS_AND_CONDITIONS)},onError:e=>{var t;u.Am.error(n("common:".concat(null==e?void 0:null===(t=e.response)||void 0===t?void 0:t.data.message)))}})}}}]);