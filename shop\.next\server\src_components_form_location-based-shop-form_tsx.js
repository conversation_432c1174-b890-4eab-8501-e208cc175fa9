"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_form_location-based-shop-form_tsx";
exports.ids = ["src_components_form_location-based-shop-form_tsx"];
exports.modules = {

/***/ "./src/components/form/google-places-autocomplete.tsx":
/*!************************************************************!*\
  !*** ./src/components/form/google-places-autocomplete.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GooglePlacesAutocomplete)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-google-maps/api */ \"@react-google-maps/api\");\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/loaders/spinner/spinner */ \"./src/components/ui/loaders/spinner/spinner.tsx\");\n/* harmony import */ var _lib_use_location__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/use-location */ \"./src/lib/use-location.tsx\");\n/* harmony import */ var _icons_current_location__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../icons/current-location */ \"./src/components/icons/current-location.tsx\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! jotai */ \"jotai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_use_location__WEBPACK_IMPORTED_MODULE_5__, jotai__WEBPACK_IMPORTED_MODULE_7__]);\n([_lib_use_location__WEBPACK_IMPORTED_MODULE_5__, jotai__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction GooglePlacesAutocomplete({ register, onChange, onChangeCurrentLocation, data, disabled = false }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onLoad, onUnmount, onPlaceChanged, getCurrentLocation, isLoaded, loadError] = (0,_lib_use_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        onChange,\n        onChangeCurrentLocation,\n        setInputValue\n    });\n    const [location] = (0,jotai__WEBPACK_IMPORTED_MODULE_7__.useAtom)(_lib_use_location__WEBPACK_IMPORTED_MODULE_5__.locationAtom);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getLocation = data?.formattedAddress;\n        setInputValue(getLocation);\n    }, [\n        data\n    ]);\n    if (loadError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: t(\"common:text-map-cant-load\")\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    }\n    return isLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_2__.Autocomplete, {\n                onLoad: onLoad,\n                onPlaceChanged: onPlaceChanged,\n                onUnmount: onUnmount,\n                fields: [\n                    \"address_components\",\n                    \"geometry.location\",\n                    \"formatted_address\"\n                ],\n                types: [\n                    \"address\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    ...register(\"location\"),\n                    placeholder: t(\"common:placeholder-search-location\"),\n                    value: inputValue,\n                    onChange: (e)=>setInputValue(e.target.value),\n                    className: `line-clamp-1 flex h-12 w-full appearance-none items-center rounded border border-border-base p-4 pr-9 text-sm font-medium text-heading transition duration-300 ease-in-out invalid:border-red-500 focus:border-accent focus:outline-0 focus:ring-0 ${disabled ? \"cursor-not-allowed border-[#D4D8DD] bg-[#EEF1F4]\" : \"\"}`,\n                    disabled: disabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 flex h-12 w-12 items-center justify-center text-accent\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icons_current_location__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 cursor-pointer hover:text-accent\",\n                    onClick: ()=>{\n                        getCurrentLocation();\n                        setInputValue(location?.formattedAddress);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loaders_spinner_spinner__WEBPACK_IMPORTED_MODULE_4__.SpinnerLoader, {}, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\google-places-autocomplete.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/form/google-places-autocomplete.tsx\n");

/***/ }),

/***/ "./src/components/form/location-based-shop-form.tsx":
/*!**********************************************************!*\
  !*** ./src/components/form/location-based-shop-form.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocationBasedShopForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hook-form */ \"react-hook-form\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"classnames\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jotai */ \"jotai\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/forms/form */ \"./src/components/ui/forms/form.tsx\");\n/* harmony import */ var _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/form/google-places-autocomplete */ \"./src/components/form/google-places-autocomplete.tsx\");\n/* harmony import */ var _config_routes__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/routes */ \"./src/config/routes.ts\");\n/* harmony import */ var _components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/modal/modal.context */ \"./src/components/ui/modal/modal.context.tsx\");\n/* harmony import */ var _components_icons_arrow_right__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/icons/arrow-right */ \"./src/components/icons/arrow-right.tsx\");\n/* harmony import */ var _lib_use_location__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/use-location */ \"./src/lib/use-location.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, jotai__WEBPACK_IMPORTED_MODULE_5__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__, _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__, _lib_use_location__WEBPACK_IMPORTED_MODULE_13__]);\n([react_hook_form__WEBPACK_IMPORTED_MODULE_1__, jotai__WEBPACK_IMPORTED_MODULE_5__, _components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__, _components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__, _lib_use_location__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LocationBasedShopForm({ className, closeLocation }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { closeModal } = (0,_components_ui_modal_modal_context__WEBPACK_IMPORTED_MODULE_11__.useModalAction)();\n    const [location, setLocation] = (0,jotai__WEBPACK_IMPORTED_MODULE_5__.useAtom)(_lib_use_location__WEBPACK_IMPORTED_MODULE_13__.locationAtom);\n    const onSubmit = (values)=>{\n        router.push(_config_routes__WEBPACK_IMPORTED_MODULE_10__.Routes?.nearByShop({\n            lat: values?.location?.lat?.toString(),\n            lng: values?.location?.lng?.toString()\n        }));\n        setLocation(values?.location);\n        closeModal();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const storedLocation = localStorage.getItem(\"currentLocation\");\n        if (storedLocation) {\n            const parsedLocation = JSON.parse(storedLocation);\n            setLocation(parsedLocation);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (location) {\n            const stringifiedLocation = JSON.stringify(location);\n            localStorage.setItem(\"currentLocation\", stringifiedLocation);\n        }\n    }, [\n        location\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"w-full border border-border-200 bg-light p-5 shadow-[-8px_8px_16px_rgba(0,0,0,0.18)] md:min-h-0 md:w-[650px] md:rounded-xl xl:w-[1076px]\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_forms_form__WEBPACK_IMPORTED_MODULE_8__.Form, {\n            onSubmit: onSubmit,\n            className: \"flex h-full gap-2.5\",\n            children: ({ register, control, watch })=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_1__.Controller, {\n                                control: control,\n                                name: \"location\",\n                                render: ({ field: { onChange, value } })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_form_google_places_autocomplete__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        register: register,\n                                        onChange: onChange,\n                                        onChangeCurrentLocation: onChange,\n                                        data: value\n                                    }, void 0, false, void 0, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-12 w-12 !px-0\",\n                            disabled: !watch(\"location\"),\n                            onClick: closeLocation,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_arrow_right__WEBPACK_IMPORTED_MODULE_12__.ArrowRight, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true);\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\form\\\\location-based-shop-form.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/form/location-based-shop-form.tsx\n");

/***/ }),

/***/ "./src/components/icons/arrow-right.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/arrow-right.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRight: () => (/* binding */ ArrowRight)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ArrowRight = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.8,\n        stroke: \"currentColor\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\arrow-right.tsx\",\n            lineNumber: 3,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\arrow-right.tsx\",\n        lineNumber: 2,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9hcnJvdy1yaWdodC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLE1BQU1BLGFBQWdELENBQUNDLHNCQUM1RCw4REFBQ0M7UUFBSUMsT0FBTTtRQUE2QkMsTUFBSztRQUFPQyxTQUFRO1FBQVlDLGFBQWE7UUFBS0MsUUFBTztRQUFnQixHQUFHTixLQUFLO2tCQUN2SCw0RUFBQ087WUFBS0MsZUFBYztZQUFRQyxnQkFBZTtZQUFRQyxHQUFFOzs7Ozs7Ozs7O2tCQUV2RCIsInNvdXJjZXMiOlsid2VicGFjazovL0BwaWNrLWJhemFyL3Nob3AvLi9zcmMvY29tcG9uZW50cy9pY29ucy9hcnJvdy1yaWdodC50c3g/YmY0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQXJyb3dSaWdodDogUmVhY3QuRkM8UmVhY3QuU1ZHQXR0cmlidXRlczx7fT4+ID0gKHByb3BzKSA9PiAoXHJcbiAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlV2lkdGg9ezEuOH0gc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgey4uLnByb3BzfT5cclxuICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBkPVwiTTEzLjUgNC41TDIxIDEybTAgMGwtNy41IDcuNU0yMSAxMkgzXCIgLz5cclxuICA8L3N2Zz5cclxuKTtcclxuIl0sIm5hbWVzIjpbIkFycm93UmlnaHQiLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/icons/arrow-right.tsx\n");

/***/ }),

/***/ "./src/components/icons/current-location.tsx":
/*!***************************************************!*\
  !*** ./src/components/icons/current-location.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CurrentLocation({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: 16,\n        height: 16,\n        viewBox: \"0 0 24 24\",\n        strokeWidth: \"2\",\n        stroke: \"currentColor\",\n        fill: \"none\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                stroke: \"none\",\n                d: \"M0 0h24v24H0z\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0 -16 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2l0 2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 20l0 2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 12l2 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2 12l2 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\GithubProjects\\\\LOLgorithm\\\\logorithm-e-site\\\\shop\\\\src\\\\components\\\\icons\\\\current-location.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CurrentLocation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9pY29ucy9jdXJyZW50LWxvY2F0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFFekIsU0FBU0MsZ0JBQWdCLEVBQUUsR0FBR0MsT0FBTztJQUNuQyxxQkFDRSw4REFBQ0M7UUFDQ0MsT0FBTTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUTtRQUNSQyxhQUFZO1FBQ1pDLFFBQU87UUFDUEMsTUFBSztRQUNMQyxlQUFjO1FBQ2RDLGdCQUFlO1FBQ2QsR0FBR1YsS0FBSzs7MEJBQ1QsOERBQUNXO2dCQUFLSixRQUFPO2dCQUFPSyxHQUFFO2dCQUFnQkosTUFBSzs7Ozs7OzBCQUMzQyw4REFBQ0c7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7OztBQUdkO0FBRUEsaUVBQWViLGVBQWVBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AcGljay1iYXphci9zaG9wLy4vc3JjL2NvbXBvbmVudHMvaWNvbnMvY3VycmVudC1sb2NhdGlvbi50c3g/MjlhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXHJcblxyXG5mdW5jdGlvbiBDdXJyZW50TG9jYXRpb24oeyAuLi5wcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxzdmdcclxuICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXHJcbiAgICAgIHdpZHRoPXsxNn1cclxuICAgICAgaGVpZ2h0PXsxNn1cclxuICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgIHN0cm9rZVdpZHRoPVwiMlwiXHJcbiAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgIGZpbGw9XCJub25lXCJcclxuICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgIHsuLi5wcm9wc30+XHJcbiAgICAgIDxwYXRoIHN0cm9rZT1cIm5vbmVcIiBkPVwiTTAgMGgyNHYyNEgwelwiIGZpbGw9XCJub25lXCI+PC9wYXRoPlxyXG4gICAgICA8cGF0aCBkPVwiTTEyIDEybS0zIDBhMyAzIDAgMSAwIDYgMGEzIDMgMCAxIDAgLTYgMFwiPjwvcGF0aD5cclxuICAgICAgPHBhdGggZD1cIk0xMiAxMm0tOCAwYTggOCAwIDEgMCAxNiAwYTggOCAwIDEgMCAtMTYgMFwiPjwvcGF0aD5cclxuICAgICAgPHBhdGggZD1cIk0xMiAybDAgMlwiPjwvcGF0aD5cclxuICAgICAgPHBhdGggZD1cIk0xMiAyMGwwIDJcIj48L3BhdGg+XHJcbiAgICAgIDxwYXRoIGQ9XCJNMjAgMTJsMiAwXCI+PC9wYXRoPlxyXG4gICAgICA8cGF0aCBkPVwiTTIgMTJsMiAwXCI+PC9wYXRoPlxyXG4gICAgPC9zdmc+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDdXJyZW50TG9jYXRpb24iXSwibmFtZXMiOlsiUmVhY3QiLCJDdXJyZW50TG9jYXRpb24iLCJwcm9wcyIsInN2ZyIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJmaWxsIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwicGF0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/icons/current-location.tsx\n");

/***/ }),

/***/ "./src/lib/use-location.tsx":
/*!**********************************!*\
  !*** ./src/lib/use-location.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLocation),\n/* harmony export */   fullAddressAtom: () => (/* binding */ fullAddressAtom),\n/* harmony export */   locationAtom: () => (/* binding */ locationAtom)\n/* harmony export */ });\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-google-maps/api */ \"@react-google-maps/api\");\n/* harmony import */ var _react_google_maps_api__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jotai */ \"jotai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__, jotai__WEBPACK_IMPORTED_MODULE_3__]);\n([react_i18next__WEBPACK_IMPORTED_MODULE_2__, jotai__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst locationAtom = (0,jotai__WEBPACK_IMPORTED_MODULE_3__.atom)(null);\nconst libraries = [\n    \"places\"\n];\nconst fullAddressAtom = (0,jotai__WEBPACK_IMPORTED_MODULE_3__.atom)((get)=>{\n    const location = get(locationAtom);\n    return location ? `${location.street_address}, ${location.city}, ${location.state}, ${location.zip}, ${location.country}` : \"\";\n});\nfunction getLocation(placeOrResult) {\n    // Declare the location variable with the Location interface\n    const location = {\n        lat: placeOrResult?.geometry?.location.lat(),\n        lng: placeOrResult?.geometry?.location.lng(),\n        formattedAddress: placeOrResult.formatted_address\n    };\n    // Define an object that maps component types to location properties\n    const componentMap = {\n        postal_code: \"zip\",\n        postal_code_suffix: \"zip\",\n        state_name: \"street_address\",\n        route: \"street_address\",\n        sublocality_level_1: \"street_address\",\n        locality: \"city\",\n        administrative_area_level_1: \"state\",\n        country: \"country\"\n    };\n    for (const component of placeOrResult?.address_components){\n        const [componentType] = component.types;\n        const { long_name, short_name } = component;\n        // Check if the component type is in the map\n        if (componentMap[componentType]) {\n            // Assign the component value to the location property\n            location[componentMap[componentType]] ??= long_name;\n            // If the component type is postal_code_suffix, append it to the zip\n            componentType === \"postal_code_suffix\" ? location[\"zip\"] = `${location?.zip}-${long_name}` : null;\n            // If the component type is administrative_area_level_1, use the short name\n            componentType === \"administrative_area_level_1\" ? location[\"state\"] = short_name : null;\n        }\n    }\n    // Return the location object\n    return location;\n}\nfunction useLocation({ onChange, onChangeCurrentLocation, setInputValue }) {\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [autocomplete, setAutocomplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isLoaded, loadError } = (0,_react_google_maps_api__WEBPACK_IMPORTED_MODULE_0__.useJsApiLoader)({\n        id: \"google_map_autocomplete\",\n        googleMapsApiKey: \"\",\n        libraries\n    });\n    const onLoad = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((autocompleteInstance)=>{\n        setAutocomplete(autocompleteInstance);\n    }, []);\n    const onUnmount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setAutocomplete(true);\n    }, []);\n    const onPlaceChanged = ()=>{\n        const place = autocomplete?.getPlace();\n        if (!place?.geometry?.location) {\n            return;\n        }\n        const location = getLocation(place);\n        if (onChange) {\n            onChange(location);\n        }\n        if (setInputValue) {\n            setInputValue(place?.formatted_address);\n        }\n    };\n    const getCurrentLocation = ()=>{\n        if (navigator?.geolocation) {\n            navigator?.geolocation.getCurrentPosition(async (position)=>{\n                const { latitude, longitude } = position.coords;\n                const geocoder = new google.maps.Geocoder();\n                const latlng = {\n                    lat: latitude,\n                    lng: longitude\n                };\n                geocoder.geocode({\n                    location: latlng\n                }, (results, status)=>{\n                    if (status === \"OK\" && results?.[0]) {\n                        const location = getLocation(results?.[0]);\n                        onChangeCurrentLocation?.(location);\n                    }\n                });\n            }, (error)=>{\n                console.error(\"Error getting current location:\", error);\n            });\n        } else {\n            console.error(\"Geolocation is not supported by this browser.\");\n        }\n    };\n    return [\n        onLoad,\n        onUnmount,\n        onPlaceChanged,\n        getCurrentLocation,\n        isLoaded,\n        loadError && t(loadError)\n    ];\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/use-location.tsx\n");

/***/ })

};
;